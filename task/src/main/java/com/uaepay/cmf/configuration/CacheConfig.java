package com.uaepay.cmf.configuration;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.redis.starter.custom.AbstractRedisCustomizer;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * redis缓存过期时间设置
 * </p>
 */
@Configuration
public class CacheConfig extends AbstractRedisCustomizer implements BasicConstant {

    private static final String CACHE_CMF_SYS_CONFIGURATION = "com.uaepay.cmf.sysconfig";

    /**
     * 缓存过期时间
     */
    public static final Duration CACHE_EXPIRE_SECONDS = Duration.ofSeconds(1800);
    public static final Duration CACHE_EXPIRE_ONE_HOUR_SECONDS = Duration.ofSeconds(3600);


    protected RedisCacheConfiguration defaultCacheConfiguration() {
        return buildCacheConfiguration(CACHE_EXPIRE_ONE_HOUR_SECONDS);
    }

    @Override
    public Map<String, RedisCacheConfiguration> customize() {
        Map<String, RedisCacheConfiguration> result = new HashMap<>();
        result.put(CACHE_CMF_SYS_CONFIGURATION, buildCacheConfiguration(CACHE_EXPIRE_SECONDS));
        result.put(CACHE_NAMESPACE_CHANNEL_CONFIG, defaultCacheConfiguration());

        return result;
    }
}
