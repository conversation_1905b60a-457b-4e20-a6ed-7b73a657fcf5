package com.uaepay.cmf;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * -javaagent:D:\3rdParty\skywalking\6.3.0\agent\skywalking-agent.jar -Dskywalking.agent.service_name=gp002_cmf
 *
 * <AUTHOR>
 * @version 1.0: CsaApplication, v 0.1 2019-05-17 11:00 Hewj Exp $
 */
@EnableAsync
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@ImportResource("classpath:META-INF/spring/applicationContext.xml")
public class CmfTaskApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(CmfTaskApplication.class);
        application.run(args);
    }

}
