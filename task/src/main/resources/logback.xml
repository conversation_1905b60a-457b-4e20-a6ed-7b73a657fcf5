<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds" debug="false">
    <property name="LOGGER_PREFIX" value="com.uaepay.cmf"/>
    <property name="LOG_FILE_PATH" value="/opt/logs/cmf"/>
    <property name="LOG_FILE_MAX_SIZE" value="1024MB"/>
    <property name="LOG_FILE_MAX_HISTORY" value="0"/>

    <include resource="uaepay-logback-defaults.xml"/>
    <include resource="uaepay-logback-facade.xml"/>
    <include resource="uaepay-logback-daemon.xml"/>
    <include resource="uaepay-logback-service.xml"/>
    <include resource="uaepay-logback-integration.xml"/>
    <include resource="uaepay-logback-dal.xml"/>

    <logger name="SERVICE-COST-LOGGER" additivity="false">
        <level value="info"/>
        <appender-ref ref="FACADE-APPENDER"/>
        <appender-ref ref="ERROR-APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="quartz" additivity="false">-->
        <level value="info"/>
        <appender-ref ref="DAEMON-APPENDER"/>
        <appender-ref ref="ERROR-APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.ibatis" additivity="false">
        <level value="info"/>
        <appender-ref ref="DAL-APPENDER"/>
        <appender-ref ref="ERROR-APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="java.sql" additivity="false">
        <level value="info"/>
        <appender-ref ref="DAL-APPENDER"/>
        <appender-ref ref="ERROR-APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>


    <!-- 定时器日志 -->
    <!--	<logger name="org.apache.velocity" additivity="false">-->
    <!--		<level value="INFO" />-->
    <!--		<appender-ref ref="CONSOLE" />-->
    <!--		<appender-ref ref="VELOCITY-APPENDER" />-->
    <!--		<appender-ref ref="ERROR-APPENDER" />-->
    <!--	</logger>-->

</configuration>