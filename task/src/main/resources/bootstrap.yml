spring:
  application:
    name: gp002_cmf

  cloud:
    config:
      uri: ${CONFIG_ADDRESS:http://config.test2pay.com}
      fail-fast: true
      retry:
        initial-interval: 2000
        max-interval: 10000
        multiplier: 2
        max-attempts: 10
      username: ${CONFIG_USER:admin}
      password: ${CONFIG_PASSWORD:admin}

#dubbo:
#  protocol:
#    port: 20899
#  consumer:
#    retries: 0
#  provider:
#    retries: 0
#  scan:
#    base-packages: com.uaepay.cmf.ext.service
#
#
#server:
#  port: 8111

#debug: true

dubbo:
  registry:
    register: false