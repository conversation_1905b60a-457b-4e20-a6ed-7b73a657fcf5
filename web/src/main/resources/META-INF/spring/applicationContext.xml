<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-2.5.xsd">

    <!-- auto register Processor -->
    <context:annotation-config/>
    <context:component-scan base-package="com.uaepay.cmf"/>


    <!-- MQ -->
    <!--	<import resource="classpath:META-INF/spring/uaepay-mq-jms-client.xml"/>-->

    <!-- 数据库访问 -->
    <import resource="classpath:META-INF/spring/persistence-cmf.xml"/>
    <import resource="classpath:META-INF/spring/data-source.xml"/>
    <import resource="classpath:META-INF/spring/dal-dao-cmf.xml"/>

    <!-- 公共服务 -->
    <import resource="classpath:META-INF/spring/common-core-schedule.xml"/>


    <!-- 核心服務服务 -->
    <import resource="classpath:META-INF/spring/cmf-domainservice-main.xml"/>

    <!-- 定时任务 -->
    <import resource="classpath:META-INF/spring/cmf-ext-service.xml"/>
    <import resource="classpath:META-INF/spring/cmf-ext-validator.xml"/>

    <!-- 外联服务 -->
    <import resource="classpath:META-INF/spring/cmf-ext-integration.xml"/>

</beans>
