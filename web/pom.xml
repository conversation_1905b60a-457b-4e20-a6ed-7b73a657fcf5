<?xml version="1.0" encoding="UTF-8"?>
<project
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <parent>
        <groupId>com.uaepay.fund.cmf</groupId>
        <artifactId>cmf-parent</artifactId>
        <version>1.1.19-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cmf-web</artifactId>

    <properties>
        <maven.install.skip>false</maven.install.skip>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!-- 项目内依赖 -->
        <dependency>
            <groupId>com.uaepay.fund.cmf</groupId>
            <artifactId>cmf-ext-service</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>activemq-core</artifactId>
                    <groupId>org.apache.activemq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.uaepay.fund.cmf</groupId>
            <artifactId>cmf-ext-task</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uaepay.starter</groupId>
            <artifactId>monitor-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.fund.pfs</groupId>
            <artifactId>pfs-service-payment</artifactId>
            <version>1.0.1</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-test</artifactId>-->
<!--            <scope>test</scope>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.junit.jupiter</groupId>-->
<!--            <artifactId>junit-jupiter-api</artifactId>-->
<!--            <scope>test</scope>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.junit.jupiter</groupId>-->
<!--            <artifactId>junit-jupiter-engine</artifactId>-->
<!--            <scope>test</scope>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.uaepay.unittest</groupId>-->
<!--            <artifactId>facade-mocker</artifactId>-->
<!--            <scope>test</scope>-->
<!--        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
