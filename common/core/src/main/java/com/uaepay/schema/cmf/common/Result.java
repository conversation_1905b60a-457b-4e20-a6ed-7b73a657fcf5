package com.uaepay.schema.cmf.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Objects;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/7/1
 */
@Data
public class Result<T> {

    private String code;
    private String message;
    private T data;


    public static <T> Result<T> ofSuccess(){
        Result result = new Result();
        result.setCode(Codes.SUCCESS.code);
        return result;
    }

    public static <T> Result<T> ofNothing(){
        Result result = new Result();
        result.setCode(Codes.NOTHING.code);
        return result;
    }

    public static <T> Result<T> ofFail(){
        Result result = new Result();
        result.setCode(Codes.FAILURE.code);
        return result;
    }

    public boolean isSuccess(){
        return Objects.equals(this.getCode(),Codes.SUCCESS.code) || Objects.equals(this.getCode(),Codes.NOTHING.code);
    }


    @Getter
    @AllArgsConstructor
    public enum Codes{
        SUCCESS("00"),
        NOTHING("01"),
        FAILURE("99"),
        ;

        private String code;
    }
}
