package com.uaepay.schema.cmf.enums;

import lombok.Getter;

/**
 * <p>订单类型</p>
 *
 * <AUTHOR> won
 * @version $Id: BizType.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum BizType {
    // 业务类型
    FUNDIN("I", "入款"),

    REFUND("B", "退款"),

    FUNDOUT("O", "出款");

    /**
     * 代码
     */
    private final String code;
    /**
     * 信息
     */
    private final String message;

    /**
     * 构造
     *
     * @param code
     * @param message
     */
    BizType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     *
     * @param code
     * @return
     */
    public static BizType getByCode(String code) {

        for (BizType type : BizType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

    public boolean isFundInRefund() {
        return BizType.FUNDIN == this || BizType.REFUND == this;
    }

    public boolean isFundOutRefund() {
        return BizType.FUNDOUT == this || BizType.REFUND == this;
    }

}
