package com.uaepay.schema.cmf.enums;

import lombok.Getter;

/**
 * <p>枚举:是-否</p>
 * <AUTHOR> won
 * @version $Id: YesNo.java, v 0.1 2010-12-22 下午02:50:57 sean won Exp $
 */
@Getter
public enum YesNo {
    YES("Y", "是"),

    NO("N", "否");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    YesNo(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据代码获取
     * @param code
     * @return
     */
    public static YesNo getByCode(String code) {

        for (YesNo type : YesNo.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
