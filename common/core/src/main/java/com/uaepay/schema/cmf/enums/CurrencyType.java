package com.uaepay.schema.cmf.enums;

import lombok.Getter;

/**
 * <p>货币类型</p>
 * <AUTHOR> won
 * @version $Id: CurrencyType.java, v 0.1 2010-12-22 下午02:56:35 sean won Exp $
 */
@Getter
public enum CurrencyType {
    /**
     * 阿联酋货币
     */
    AED("AED", "迪拉姆"),
    /**
     * 中国货币
     */
    CNY("CNY", "人民币"),
    /**
     * 欧盟货币
     */
    EUR("EUR", "欧元"),
    /**
     * 印度货币
     */
    INR("INR", "印元"),
    /**
     * 巴基斯坦货币
     */
    PKR("PKR", "巴元"),
    /**
     * 美国货币
     */
    USD("USD", "美元");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    CurrencyType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据代码获取
     * @param code
     * @return
     */
    public static CurrencyType getByCode(String code) {

        for (CurrencyType type : CurrencyType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
