package com.uaepay.cmf.common.domain.fundout;

import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.schema.cmf.enums.CardType;
import com.uaepay.schema.cmf.enums.CompanyOrPersonal;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 出款同一request
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: FundOutRequest.java, v 0.1 2012-3-30 下午02:26:59 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class FundoutRequest extends ChannelFundRequest {

    private static final long serialVersionUID = 708846509083955934L;
    /**
     * 证件类型
     */
    protected String identityType;
    /**
     * 证件号
     */
    protected String identityNo;
    /**
     * 是否同行
     */
    protected Boolean innerBank;
    /**
     * 银行编码
     */
    protected String bankCode;
    /**
     * 银行名称
     */
    protected String bankName;
    /**
     * 分行
     */
    protected String bankBranch;
    /**
     * 分行编码
     */
    protected String bankBranchCode;
    /**
     * 省份信息
     */
    protected String bankProvince;
    /**
     * 城市信息
     */
    protected String bankCity;
    /**
     * 邮政编码
     */
    protected String areaCode;
    /**
     * 对公/对私
     */
    protected CompanyOrPersonal accountType;
    /**
     * 账户名
     */
    protected String accountName;
    /**
     * 账户卡号
     */
    protected String accountNo;
    /**
     * 收款iban
     */
    protected String ibanNo;
    /**
     * 收款行swiftCode
     */
    protected String swiftCode;
    /**
     * 受益人地址
     */
    private String beneficiaryAddress;
    /**
     * 中间行
     */
    private String intermediaryBank;
    /**
     * 卡类型
     */
    protected CardType cardType;
    /**
     * 协议号
     */
    protected String agreementNo;
    /**
     * 手机号
     */
    protected String mobilePhone;
    /**
     * 出款原因
     */
    protected String purpose;
    /**
     * 产品编码
     */
    protected String productCode;
    /**
     * 支付编码
     */
    protected String paymentCode;

    /**
     * payout ac
     */
    protected String payoutAccount;

    protected String cardNo;

}
