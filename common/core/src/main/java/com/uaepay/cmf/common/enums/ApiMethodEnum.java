package com.uaepay.cmf.common.enums;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ApiMethodEnum.java v1.0  2020-09-09 18:32
 */
public enum ApiMethodEnum {
    // 接口方法
    WS("ws", "Web Service"),
    DUBBO("dubbo", "dubbo");

    private String code;

    private String description;

    ApiMethodEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static ApiMethodEnum getByCode(String code) {
        for (ApiMethodEnum apiMethod : values()) {
            if (apiMethod.getCode().equals(code)) {
                return apiMethod;
            }
        }
        return null;
    }
}
