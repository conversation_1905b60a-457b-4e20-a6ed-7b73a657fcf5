package com.uaepay.cmf.common.domain;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 渠道结果对象
 * </p>
 * 针对不同业务结果扩展此基类
 * 
 * <AUTHOR> won
 * @version $Id: ChannelFundResult.java, v 0.1 2011-10-2 上午09:21:28 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChannelResult extends BaseResult {
    private static final long serialVersionUID = 9159442540047469222L;

    /** API类型 */
    protected FundChannelApiType apiType;
    /** 渠道统一结果代码，进过改造的渠道不填写此值，由CMF完成映射 */
    protected String resultCode;
    /** 提交机构订单号 */
    protected String instOrderNo;
    /** 机构返回流水号 */
    protected String instReturnOrderNo;
    /** 渠道API结果码 ， 为资金结构实际返回码 */
    protected String apiResultCode;
    /** 渠道API结果子码 */
    protected String apiResultSubCode;
    /** 描述信息 */
    protected String apiResultMessage;
    /** 描述信息 */
    protected String apiResultSubMessage;
    /** 扩展信息 */
    protected String extension;

    /**
     * 默认构造
     */
    public ChannelResult() {

    }

    /**
     * 构造结果及信息构造
     * @param success
     */
    public ChannelResult(boolean success) {
        this.success = success;
    }

    /**
     * 根据结果代码构造
     * @param success
     * @param apiResultCode
     */
    public ChannelResult(boolean success, String apiResultCode) {
        this.success = success;
        this.apiResultCode = apiResultCode;
    }

    /**
     * 根据结果代码构造
     * @param success
     * @param apiResultCode
     * @param apiResultMessage
     */
    public ChannelResult(boolean success, String apiResultCode, String apiResultMessage) {
        this.success = success;
        this.apiResultCode = apiResultCode;
        this.apiResultMessage = apiResultMessage;
    }

    /**
     * 根据结果代码构造
     * @param success
     * @param apiResultCode
     * @param apiResultMessage
     * @param apiType
     */
    public ChannelResult(boolean success, String apiResultCode, String apiResultMessage,
                         FundChannelApiType apiType) {
        this.success = success;
        this.apiResultCode = apiResultCode;
        this.apiResultMessage = apiResultMessage;
        this.apiType = apiType;
    }

}
