package com.uaepay.cmf.common.domain;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 渠道通用结果
 * </p>
 * 用于适应渠道结果的多样性
 * 
 * <AUTHOR>
 * @version $Id: ChannelCommonResult.java, v 0.1 2012-8-15 下午7:34:56 fuyangbiao Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChannelCommonResult implements Serializable {
    private static final long serialVersionUID = -1137050045481432344L;

    /** 结果对象CLASS全路径名称 */
    private String resultClass;
    /** 结果JASON串 */
    private String resultJason;

}
