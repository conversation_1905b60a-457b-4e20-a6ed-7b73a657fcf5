package com.uaepay.cmf.common.domain.fundin.ebank;

import java.util.Date;
import java.util.List;

import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.AccessChannel;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * EBank渠道支付请求类
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: EBankChannelFundRequest.java, v 0.1 2011-10-2 上午09:05:37 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class EBankChannelFundRequest extends ChannelFundRequest {
    private static final long serialVersionUID = 3566102968597941518L;
    /**
     * 会员ID
     */
    private String memberId;
    /**
     * 用户ID
     */
    private String userId;
    private String userIp;
    private String userDomain;
    private PayMode payMode;
    private AccessChannel accessChannel;
    /**
     * 订单日期
     */
    private Date orderDate;
    /**
     * 分期付款
     */
    private Installment installmentInfo;
    /**
     * 商户名称
     */
    private String merchantName;
    /**
     * 商户商品信息
     */
    private List<MerchantOrder> merchantOrders;

}
