package com.uaepay.cmf.common.domain.base;

import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.InstOrderStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 机构订单结果
 * </p>
 *
 * <AUTHOR>
 * @version $Id: InstOrderResult.java, v 0.1 2014-7-30 下午12:36:42 Administrator Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class InstOrderResult implements Serializable {

    private static final long serialVersionUID = -6479710883141365601L;
    protected ReturnInfo returnInfo;

    protected ReturnInfo instReturnInfo;

    protected Money realAmount;

    protected BizType orderType;

    protected String instOrderNo;

    protected String instReturnOrderNo;
    /**
     * 结算时间
     */
    protected Date gmtSettle;
    /**
     * 产品码
     */
    protected String productCode;
    /**
     * 支付码
     */
    protected String paymentCode;
    /**
     * 订单状态
     */
    protected InstOrderStatus instOrderStatus;
    /**
     * 处理时间
     */
    protected Date gmtProcess;
    /**
     * 扩展参数
     */
    protected Map<String, String> extension;
}
