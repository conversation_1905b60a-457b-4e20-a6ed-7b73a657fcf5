package com.uaepay.cmf.common.domain.fundin.ebank;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.enums.CallBackType;

/**
 *
 * <p>验签请求对象</p>
 * <AUTHOR>
 * @version $Id: EBankChannelVerifyRequest.java, v 0.1 2012-8-3 上午10:39:32 liumaoli Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class EBankChannelVerifyRequest extends ChannelFundRequest {

    private static final long   serialVersionUID = 2973414315388583858L;
    /**
     * 回调类型
     */
    private CallBackType        callBackType;
    /**
     * 页面地址
     */
    private String              pageUrl;
    /**
     * 后台地址
     */
    private String              serverUrl;
    /**
     * 请求头map
     */
    @ToString.Exclude
    private Map<String, List<String>> headerMap;
    /**
     * 结果数据
     */
    private Map<String, String> responseData     = new ConcurrentHashMap<>();

    private String              responseQueryString;

}
