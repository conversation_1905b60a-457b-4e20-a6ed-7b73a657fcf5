package com.uaepay.cmf.common.domain;

import com.uaepay.common.util.money.Money;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 渠道控制类结果
 * </p>
 *
 * <AUTHOR>
 * @version ChannelControlResult.java 1.0 Created@2017-02-12 11:06 $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChannelControlResult extends ChannelResult {
    private static final long serialVersionUID = -3571826435976259106L;

    /** 实际金额 */
    private Money realAmount;
    /** 机构URL地址 */
    private String instUrl;
    /** 渠道编码 */
    private String fundChannelCode;

}
