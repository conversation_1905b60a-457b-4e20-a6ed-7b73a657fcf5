package com.uaepay.cmf.common.domain.file;

import com.uaepay.cmf.common.domain.ChannelFundRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ChannelFileRequest.java v1.0  1/4/21 2:02 PM
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChannelFileRequest extends ChannelFundRequest {
    private static final long serialVersionUID = 2868437874923443965L;

    /**
     * 文件日期-yyyyMMdd格式
     */
    private String fileDate;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件名
     */
    private String fileNamePattern;

    protected String payoutAccount;

}
