package com.uaepay.cmf.common.domain.refund;

import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.common.util.money.Money;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <p>
 * 退款请求
 * </p>
 *
 * <AUTHOR>
 * @version $Id: RefundRequest.java, v 0.1 2012-8-10 上午9:51:31 liumaoli Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class RefundRequest extends ChannelFundRequest {
    private static final long serialVersionUID = 8372695725367193904L;

    /**
     * 原始订单金额
     */
    private Money originalOrderAmount;
    /**
     * 原始银行返回流水号
     */
    private String orignalInstSeqNo;
    /**
     * 原始机构订单号
     */
    private String orignalInstOrderNo;
    /**
     * 原始订单提交时间
     */
    private Date orignalOrderSubmitTime;
    /**
     * 原始订单结算时间
     */
    private Date orignalOrderSettleTime;
    /**
     * 退款类型: true-撤销，false-退款
     */
    private boolean cancel;

}
