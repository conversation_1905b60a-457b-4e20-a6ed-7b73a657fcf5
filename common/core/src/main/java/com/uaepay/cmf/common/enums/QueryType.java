package com.uaepay.cmf.common.enums;

import lombok.Getter;

/**
 * 查询类型
 *
 * <AUTHOR> won
 */
@Getter
public enum QueryType {

    // 查询类型
    BATCH_ID("batchId", "批次号查询"),

    ORDER_RANGE("orderRange", "订单号区间查询"),

    TIME_RANGE("timeRange", "时间区间查询"),
    ;

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述信息
     */
    private final String message;

    QueryType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     *
     * @param code
     * @return
     */
    public static QueryType getByCode(String code) {

        for (QueryType type : QueryType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
