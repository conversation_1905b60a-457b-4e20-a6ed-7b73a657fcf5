package com.uaepay.cmf.common.enums;

import org.apache.commons.lang3.StringUtils;
import lombok.Getter;

/**
 * <p>api使用参数</p>
 *
 * <AUTHOR>
 * @version $Id: ApiParamScene.java, v 0.1 2012-8-8 上午10:44:51 liumaoli Exp $
 */
@Getter
public enum ApiParamScene {
    //
    REQUEST_CHANNEL("request", "请求渠道"),

    CHANNEL_RETURN("response", "渠道返回");

    /**
     * 代码
     */
    private final String code;
    /**
     * 信息
     */
    private final String message;

    /**
     * 构造
     *
     * @param code
     * @param message
     */
    ApiParamScene(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据代码获取
     *
     * @param code
     * @return
     */
    public static ApiParamScene getByCode(String code) {
        for (ApiParamScene type : ApiParamScene.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

    public static boolean isChannelRequest(String code) {
        return getByCode(code) == REQUEST_CHANNEL;
    }

}
