package com.uaepay.cmf.common.enums;

import lombok.Getter;

/**
 * <p>
 * 监控日志状态
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: MonitorLogStatus.java, v 0.1 2011-7-20 上午10:48:32 sean won Exp $
 */
@Getter
public enum MonitorLogStatus {

    //监控日志
    AWAITING("A", "待发送"),

    IN_PROCESS("I", "正在发送"),

    NOT_SEND("N", "本批次不发送"),

    SUCCESSFUL("S", "完成");

    /**
     * 代码
     */
    private final String code;
    /**
     * 信息
     */
    private final String message;

    /**
     * 构造
     *
     * @param code
     * @param message
     */
    MonitorLogStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     *
     * @param code
     * @return
     */
    public static MonitorLogStatus getByCode(String code) {

        for (MonitorLogStatus type : MonitorLogStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }
}
