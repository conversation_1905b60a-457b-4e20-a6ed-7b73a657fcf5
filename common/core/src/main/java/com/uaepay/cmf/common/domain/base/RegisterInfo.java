package com.uaepay.cmf.common.domain.base;

import com.uaepay.schema.cmf.enums.CardType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 
 * <p>
 * 报备信息
 * </p>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class RegisterInfo implements Serializable {

    private static final long serialVersionUID = -961252575683018753L;
    /**
     * 渠道提供者code
     */
    protected String fundProviderCode;
    /**
     * 报备类型
     */
    private String registerType;
    /**
     * 批次ID
     */
    private String batchId;
}
