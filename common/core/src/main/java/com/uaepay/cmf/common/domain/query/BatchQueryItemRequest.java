package com.uaepay.cmf.common.domain.query;

import java.util.List;

import com.uaepay.cmf.common.domain.ChannelFundRequest;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 批量逐笔查询请求.区别于BatchQueryRequest
 * </p>
 *
 * <AUTHOR>
 * @version BatchQueryItemRequest.java 1.0 @2015/4/20 17:46 $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class BatchQueryItemRequest extends ChannelFundRequest {

    private static final long serialVersionUID = -4021846433695985257L;
    private List<QueryRequest> queryRequestList;

    /**
     * 批次id
     */
    private Long archiveBatchId;
    /**
     * 查询请求号
     */
    private String querySeqNo;

}
