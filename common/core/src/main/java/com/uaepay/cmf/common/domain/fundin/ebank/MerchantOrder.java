package com.uaepay.cmf.common.domain.fundin.ebank;

import java.math.BigDecimal;
import java.util.Map;

import com.uaepay.common.util.money.Money;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * <p>
 * 商品订单详细信息
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: MerchantOrder.java, v 0.1 2012-2-9 上午10:46:56 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class MerchantOrder {
    /**
     * 商品名称
     */
    protected String commodityName;
    /**
     * 商品单价
     */
    protected Money unitPrice;
    /**
     * 数量
     */
    protected BigDecimal quantity;
    /**
     * 总金额
     */
    protected Money totalAmount;
    /**
     * 订单描述
     */
    protected String orderDesc;
    /**
     * 商户号
     */
    protected String merchantId;
    /**
     * 商户订单号
     */
    protected String merchantOrderNo;
    /**
     * 扩展参数
     */
    protected Map<String, String> extension;

}
