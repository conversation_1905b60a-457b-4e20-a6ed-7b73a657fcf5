package com.uaepay.cmf.common.domain.query;

import java.util.Date;

import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.common.enums.QueryType;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * <p>
 * 批量查询请求参数
 * </p>
 * 
 * <AUTHOR>
 * @version $Id: BulkQueryRequest.java, v 0.1 2012-8-10 上午9:38:06 liumaoli Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class BatchQueryRequest extends ChannelRequest {

    private static final long serialVersionUID = 3071311284031277063L;
    /** 查询类型 */
    private QueryType queryType;
    /** 订单开始时间 */
    private Date startOrderTime;
    /** 订单结束时间 */
    private Date endOrderTime;
    /** 开始机构订单号 */
    private String startOrderNo;
    /** 终止机构订单号 */
    private String endOrderNo;
    /** 查询时间 */
    private Date queryTime;
    /** 批次号 */
    private String batchId;

}
