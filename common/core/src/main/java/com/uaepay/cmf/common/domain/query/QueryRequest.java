package com.uaepay.cmf.common.domain.query;

import java.util.Date;

import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.common.util.money.Money;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * <p>
 * 查询请求参数
 * </p>
 * 
 * <AUTHOR>
 * @version $Id: QueryRequest.java, v 0.1 2012-8-10 上午9:24:57 liumaoli Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class QueryRequest extends ChannelRequest {

    private static final long serialVersionUID = -9170420746508623845L;

    /** 金额 */
    private Money amount;
    /** 机构订单提交时间 */
    private Date instOrderSubmitTime;
    /** 查询序列号(一些特殊银行需要使用) */
    private String querySeqNo;
    /** 查询时间(当前时间) */
    private Date queryTime;
    /** 原始机构订单号(退款时使用) */
    private String originalInstOrderNo;
    /** 原始订单银行返回订单号(退款时使用) */
    private String originalInstSeqNo;
    /** 原始机构订单提交时间(退款时使用) */
    private Date originalInstOrderSubmitTime;
    /** 原始机构订单结算时间(退款时使用) */
    private Date originalInstOrderSettleTime;
    /** 原始订单金额(退款时使用) */
    private Money originalInstOrderAmount;

}
