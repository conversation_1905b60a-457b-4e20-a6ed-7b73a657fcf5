package com.uaepay.cmf.common.domain.base;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 *
 * <p>返回信息</p>
 * <AUTHOR>
 * @version $Id: ReturnInfo.java, v 0.1 2014-7-30 下午3:10:07 Administrator Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ReturnInfo implements Serializable {

    private static final long serialVersionUID = -8880631880943811889L;

    protected String returnMsg;

    protected String returnCode;

}
