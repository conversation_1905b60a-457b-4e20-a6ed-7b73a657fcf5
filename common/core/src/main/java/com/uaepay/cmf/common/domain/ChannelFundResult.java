package com.uaepay.cmf.common.domain;

import java.util.Date;

import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.common.util.money.Money;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 渠道支付结果对象
 * </p>
 * 
 * <AUTHOR> won
 * @version $Id: ChannelFundResult.java, v 0.1 2011-10-3 上午10:17:40 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChannelFundResult extends ChannelResult {
    private static final long serialVersionUID = -5232985693057220878L;

    /** 实际金额 */
    private Money realAmount;
    /** 机构清算时间 */
    private Date instSettleTime;
    /** 机构处理时间 */
    private Date processTime;
    /** 机构URL地址 */
    private String instUrl;
    /** 渠道编码 */
    private String fundChannelCode;

    /**
     * 默认构造
     */
    public ChannelFundResult() {}

    /**
     * 构造
     * 
     * @param success
     * @param apiResultCode
     */
    public ChannelFundResult(boolean success, String apiResultCode) {
        super(success, apiResultCode);
    }

    /**
     * 根据详细信息构造
     * 
     * @param success
     * @param apiResultCode
     * @param apiResultMessage
     */
    public ChannelFundResult(boolean success, String apiResultCode, String apiResultMessage) {
        super(success, apiResultCode, apiResultMessage);
    }

    /**
     * 根据详细信息构造
     * 
     * @param success
     * @param apiResultCode
     * @param apiResultMessage
     * @param apiType
     */
    public ChannelFundResult(boolean success, String apiResultCode, String apiResultMessage, FundChannelApiType apiType) {
        super(success, apiResultCode, apiResultMessage, apiType);
    }

}
