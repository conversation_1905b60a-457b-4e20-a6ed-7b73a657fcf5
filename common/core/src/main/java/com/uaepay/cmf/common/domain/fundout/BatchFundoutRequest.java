package com.uaepay.cmf.common.domain.fundout;

import java.util.List;

import com.uaepay.cmf.common.domain.ChannelFundRequest;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 批量出款请求
 * </p>
 *
 * <AUTHOR>
 * @version $Id: BatchFundoutRequest.java, v 0.1 2015-4-21 下午1:01 Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class BatchFundoutRequest extends ChannelFundRequest {

    private static final long serialVersionUID = -3820269774715902681L;
    /**
     * 是否同行
     */
    private Boolean innerBank;

    /**
     * 批次id
     */
    private Long archiveBatchId;

    /**
     * 总笔数
     */
    private Integer totalCount;
    /**
     * 出款请求列表
     */
    private List<FundoutRequest> requests;

}
