package com.uaepay.cmf.common.enums;

import com.uaepay.schema.cmf.enums.BizType;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <p>Interface Type: SP (Single Payment), BP (Batch Payment), SQ (Single Query), BQ (Batch Query), SR (Single Refund), BR (Batch Refund)</p>
 *
 * <AUTHOR> won
 * @version $Id: FundChannelApiType.java, v 0.1 2011-2-22 下午04:56:13 sean won Exp $
 */
@Getter
public enum FundChannelApiType {
    // Interface Type
    SINGLE_PAY("SP", "Single Payment"),
    BATCH_PAY("BP", "Batch Payment"),
    BATCH_FILE_PAY("BFP", "Batch File Payment"),
    MANUAL_FILE_PAY("MFP", "Manual File Payment"),
    SINGLE_QUERY("SQ", "Single Query"),
    BATCH_QUERY("BQ", "Batch Query"),
    QUERY_BALANCE("QB", "Query Account Balance"),
    SINGLE_REFUND("SR", "Single Refund"),
    BATCH_REFUND("BR", "Batch Refund"),
    BATCH_FILE_REFUND("BFR", "Batch File Refund"),
    MANUAL_REFUND("MR", "Manual Refund"),
    DEBIT("DB", "Debit"),
    PRE_DEBIT("PDB", "Pre-Debit"),
    DEBIT_ADVANCE("DBA", "Debit Advance"),
    DEBIT_ADVANCE_QUERY("DBAQ", "Debit Advance Query"),
    ADVANCE_3DS2("AD", "Advance 3DS2.0"),
    SIGN("SG", "Online Banking B2C Redirect"),
    VERIFY_SIGN("VS", "Online Banking Signature Verification"),
    SINGLE_REFUND_QUERY("SRQ", "Single Refund Query"),
    BATCH_REFUND_QUERY("BRQ", "Batch Refund Query"),
    AUTH("AT", "Authorization"),
    AUTH_VERIFY("AV", "Authorization Verification"),
    AUTH_ADVANCE("AUTHA", "Authorization Advance"),
    AUTH_ADVANCE_QUERY("AUTHAQ", "Authorization Advance Query"),
    DOWNLOAD_STATEMENT("DS", "Download Statement"),
    FILE_MIGRATE("MF", "File Migration"),
    FILE_IMPORT("FI", "File Import"),
    NOTIFY("NT", "Notification"),
    FUND_TRANSFER("FT", "Fund Transfer/Allocation"),
    VOID_TRANSACTION("VT", "Void Transaction"),
    PREAUTH_UPDATE("PAU", "Pre-authorization Update"),
    PREAUTH_COMPLETE("PAC", "Pre-authorization Complete"),
    PREAUTH_VOID("PAV", "Pre-authorization Void"),
    REVERSAL("REVERSAL", "Reversal"),
    MERCHANT_REGISTER("MCR", "Merchant Registration"),
    MERCHANT_REGISTER_QUERY("MRQ", "Merchant Registration Query"),
    MERCHANT_REGISTER_VERIFY("MRV", "Merchant Registration Verification"),
    CONTROL_VOID_TRANSACTION("CV", "Control Type Transaction Void"),
    VALIDATE_PARAMETER("VP", "Parameter Validation"),
    BATCH_FILE_MERCHANT_REGISTER("BFMR", "Batch Merchant Registration - File Mode"),
    VERIFY_MERCHANT_REPORT_PARAM("VMRP", "Verify Merchant Report Parameters"),
    REPORT_RETURN_FILE_ANALYSIS("RRFA", "Report Return File Analysis"),
    IBAN_DETAIL_QUERY("IBD", "IBAN Detail Query"),
    RETRIEVE_CARD_METADATA("RCM", "Retrieve Card Metadata"),
    ;


    /**
     * Code
     */
    private final String code;
    /**
     * Message
     */
    private final String message;




    /**
     * Whether the interface is file mode
     *
     * @param type
     * @return
     */
    public static boolean isFile(FundChannelApiType type) {
        return BATCH_FILE_PAY.equals(type) || MANUAL_FILE_PAY.equals(type)
                || BATCH_FILE_REFUND.equals(type);
    }

    /**
     * Whether the interface is batch
     *
     * @param type
     * @return
     */
    public static boolean isBatch(FundChannelApiType type) {
        return BATCH_PAY.equals(type)
                || BATCH_QUERY.equals(type)
                || BATCH_REFUND.equals(type) || BATCH_FILE_PAY.equals(type) || BATCH_FILE_REFUND.equals(type)
                || MANUAL_FILE_PAY.equals(type);
    }

    public static boolean isBatch(String typeStr) {
        return isBatch(getByCode(typeStr));
    }

    public static boolean isControl(FundChannelApiType apiType) {
        return apiType == ADVANCE_3DS2 ||
                apiType == CONTROL_VOID_TRANSACTION ||
                apiType == VOID_TRANSACTION ||
                apiType == AUTH ||
                apiType == AUTH_VERIFY ||
                apiType == MERCHANT_REGISTER ||
                apiType == MERCHANT_REGISTER_QUERY;
    }

    /**
     * Whether the interface is delay batch
     *
     * @param apiType
     * @return
     */
    public static boolean isDelayBatch(FundChannelApiType apiType) {
        return BATCH_FILE_PAY.equals(apiType);
    }


    public static boolean isFund(FundChannelApiType type) {
        return isFundin(type) || isFundout(type) || isRefund(type);
    }

    /**
     * Whether the interface is single operation
     *
     * @param type
     * @return
     */
    public static boolean isSingle(FundChannelApiType type) {
        return SINGLE_PAY.equals(type)
                || SINGLE_QUERY.equals(type)
                || SINGLE_REFUND.equals(type)
                || DEBIT.equals(type)
                || SIGN.equals(type)
                || MANUAL_REFUND.equals(type);
    }

    public static boolean isMatchBizType(FundChannelApiType type, BizType bizType) {
        if (bizType.equals(BizType.FUNDIN)) {
            return isFundin(type);
        }
        if (bizType.equals(BizType.FUNDOUT)) {
            return isFundout(type);
        }
        if (bizType.equals(BizType.REFUND)) {
            return isRefund(type);
        }
        return false;
    }

    /**
     * Whether the interface is fund in operation
     *
     * @param type
     * @return
     */
    public static boolean isFundin(FundChannelApiType type) {
        return SIGN.equals(type)
                || VERIFY_SIGN.equals(type)
                || DEBIT.equals(type);
    }

    /**
     * Whether the interface is fund out operation
     *
     * @param type
     * @return
     */
    public static boolean isFundout(FundChannelApiType type) {
        return SINGLE_PAY.equals(type)
                || BATCH_PAY.equals(type)
                || BATCH_FILE_PAY.equals(type)
                || MANUAL_FILE_PAY.equals(type)
                || SINGLE_QUERY.equals(type);
    }

    public static List<FundChannelApiType> getFundoutTypes() {
        return Arrays.asList(SINGLE_PAY, BATCH_PAY, BATCH_FILE_PAY, MANUAL_FILE_PAY);
    }

    /**
     * Whether the interface is automatic fund out (API mode); for automatic fund out requests, CMF must return PE channel number, etc.
     *
     * @param type
     * @return
     */
    public static boolean isAutoFundout(FundChannelApiType type) {
        return SINGLE_PAY.equals(type)
                || BATCH_PAY.equals(type);
    }

    /**
     * Whether the interface is refund operation
     *
     * @param type
     * @return
     */
    public static boolean isRefund(FundChannelApiType type) {
        return BATCH_FILE_REFUND.equals(type)
                || MANUAL_REFUND.equals(type)
                || BATCH_REFUND.equals(type)
                || SINGLE_REFUND.equals(type);
    }

    /**
     * Constructor
     *
     * @param code
     * @param message
     */
    FundChannelApiType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * Get by code
     *
     * @param code
     * @return
     */
    public static FundChannelApiType getByCode(String code) {

        for (FundChannelApiType type : FundChannelApiType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

    public static boolean isInstNoRequired(FundChannelApiType apiType) {
        return isRefund(apiType) || isFundout(apiType) || SIGN.equals(apiType) || DEBIT.equals(apiType);
    }

    public static boolean isAsync(String apiType) {
        FundChannelApiType apiTypeEnum = getByCode(apiType);
        return (apiTypeEnum == VERIFY_SIGN || apiTypeEnum == SINGLE_REFUND_QUERY || apiTypeEnum == SINGLE_QUERY || apiTypeEnum == BATCH_QUERY || apiTypeEnum == BATCH_PAY || apiTypeEnum == BATCH_FILE_PAY
                || apiTypeEnum == MANUAL_FILE_PAY || apiTypeEnum == VOID_TRANSACTION || apiTypeEnum == ADVANCE_3DS2);
    }

}
