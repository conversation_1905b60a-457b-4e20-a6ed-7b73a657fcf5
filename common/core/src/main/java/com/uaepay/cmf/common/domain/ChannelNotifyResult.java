package com.uaepay.cmf.common.domain;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.schema.cmf.enums.InstOrderStatus;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * <p>
 * CMF处理完'渠道通知'返回给渠道的结果对象
 * </p>
 * 
 * <AUTHOR> won
 * @version $Id: ChannelNotifyResult.java, v 0.1 2012-2-7 下午09:12:10 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChannelNotifyResult extends BaseResult {

    private static final long serialVersionUID = -4284695310562215966L;
    /** 结果代码 可以定义0为成功，其他失败 */
    private String returnCode;
    /** 结果 */
    private InstOrderStatus instOrderStatus;
    /** 处理结果 */
    private String processStatus;
    /** 扩展信息 */
    private String extension;

}
