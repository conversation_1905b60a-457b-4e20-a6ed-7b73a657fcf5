package com.uaepay.cmf.common.domain.register;

import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.domain.base.RegisterInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <p>
 * 批量报备请求
 * </p>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class BatchRegisterRequest extends ChannelFundRequest {

    private static final long serialVersionUID = -3820269774715902681L;

    /**
     * 报备批次列表
     */
    private List<RegisterInfo> batchInfo;

}
