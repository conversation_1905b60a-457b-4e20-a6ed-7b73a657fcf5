package com.uaepay.cmf.common.domain;

import com.uaepay.cmf.common.enums.ApiMethodEnum;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * 渠道请求基类
 * </p>
 * 各类渠道请求此基类进行扩展
 *
 * <AUTHOR> won
 * @version $Id: ChannelNotifyRequest.java, v 0.1 2011-10-2 上午09:15:07 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChannelRequest implements Serializable {
    private static final long serialVersionUID = -2526536023145997502L;

    /**
     * 资金渠道代码
     */
    protected String fundChannelCode;
    /**
     * fundProviderCode
     */
    protected String fundProviderCode;
    /**
     * API类型
     */
    protected FundChannelApiType apiType;
    /**
     * 接口方法
     */
    protected ApiMethodEnum apiMethod;
    /**
     * API地址
     */
    protected String apiUrl;
    /**
     * 收单机构编码
     */
    protected String instCode;
    /**
     * 机构订单号
     */
    protected String instOrderNo;
    /**
     * 商户号
     */
    protected String merchantId;
    /**
     * 机构回调地址, 后台回调
     */
    protected String callbackServerUrl;
    /**
     * 机构回调地址, 页面跳转
     */
    protected String callbackPageUrl;
    /**
     * 扩展信息
     */
    protected Map<String, String> extension = new ConcurrentHashMap<>();
    /**
     * 备注
     */
    protected String memo;

    /**
     * 默认构造
     */
    public ChannelRequest() {
    }

    /**
     * 根据API类型构造
     *
     * @param fundChannelCode
     * @param apiType
     */
    public ChannelRequest(String fundChannelCode, FundChannelApiType apiType) {
        this.fundChannelCode = fundChannelCode;
        this.apiType = apiType;
    }
}
