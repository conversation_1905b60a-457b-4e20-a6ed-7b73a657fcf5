package com.uaepay.cmf.common.monitor;

import java.util.Date;

import com.uaepay.cmf.common.enums.MonitorItem;
import com.uaepay.cmf.common.enums.MonitorLogStatus;
import com.uaepay.common.lang.SystemUtil;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * <p>
 * 监控日志
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: MonitorLog.java, v 0.1 2011-7-19 下午01:28:52 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class MonitorLog {
    /**
     * 主键
     */
    private Long logId;
    /**
     * IP地址
     */
    private String ipAddress;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 监控项
     */
    private MonitorItem monitorItem;
    /**
     * 描述
     */
    private String eventMessage;
    /**
     * 异常
     */
    private Throwable exception;
    /**
     * 异常日志
     */
    private String exceptionLog;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 最后更新时间
     */
    private Date gmtModified;
    /**
     * 状态
     */
    private MonitorLogStatus status;
    /**
     * 备注
     */
    private String memo;

    public MonitorLog(MonitorItem monitorItem) {
        this(null, monitorItem, null, null);
    }

    public MonitorLog(MonitorItem monitorItem, Throwable exception) {
        this(null, monitorItem, null, exception);
    }

    public MonitorLog(String orderNo, MonitorItem monitorItem, String message) {
        this(orderNo, monitorItem, message, null);
    }

    public MonitorLog(String orderNo, MonitorItem monitorItem, String message, Throwable exception) {
        super();
        this.orderNo = orderNo;
        this.monitorItem = monitorItem;
        this.eventMessage = message;
        this.ipAddress = SystemUtil.getHostInfo().getAddress();
        this.gmtCreate = new Date();
        /**
         * 默认为待发送
         */
        this.status = MonitorLogStatus.AWAITING;
        setException(exception);
    }

    public void setException(Throwable exception) {
        this.exception = exception;
        // 分析出异常日志.
        setExceptionLog(printExceptionTrace());
    }

    private String printExceptionTrace() {
        StringBuilder trace = new StringBuilder();
        if (null == exception) {
            return null;
        }
        trace.append("异常日志：" + exception.getMessage());
        if (exception.getStackTrace() != null) {
            int depth = 20;
            for (StackTraceElement element : exception.getStackTrace()) {
                if (depth-- > 0) {
                    trace.append(element.getClassName() + "." + element.getMethodName() + "(), line number:"
                        + element.getLineNumber() + "];\r\n");
                } else {
                    break;
                }
            }
        }

        return trace.toString();
    }
}
