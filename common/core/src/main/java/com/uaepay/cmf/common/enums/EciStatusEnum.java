package com.uaepay.cmf.common.enums;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date EciStatusEnum.java v1.0  11/22/20 4:19 PM
 */
public enum EciStatusEnum {

    // 05,02 - 3ds验证成功，银行赔付
    // 06,01 - 3ds降级，商户赔付
    // 07,00 - 3ds验证失败
    VISA_SUCCESS("05", "S", "VISA认证成功"),
    MASTERCARD_SUCCESS("02", "S", "MASTERCARD认证成功"),
    VISA_DOWNGRADE("06", "D", "VISA认证降级"),
    MASTERCARD_DOWNGRADE("01", "D", "MASTERCARD认证降级"),
    VISA_FAIL("07", "F", "VISA认证失败"),
    MASTERCARD_FAIL("00", "F", "MASTERCARD认证失败"),
    NO_SHOW("xx", "N", "未展示3ds"),

    // 3ds2.0
    THREE_3DS2_VISA_SUCCESS("", "S", "VISA认证成功"),
    THREE_3DS2_MASTERCARD_SUCCESS("", "S", "MASTERCARD认证成功"),
    THREE_3DS2_VISA_DOWNGRADE("", "D", "VISA认证降级"),
    THREE_3DS2_MASTERCARD_DOWNGRADE("", "D", "MASTERCARD认证降级"),
    ;

    private String eci;

    private String status;

    private String description;

    EciStatusEnum(String eci, String status, String description) {
        this.eci = eci;
        this.status = status;
        this.description = description;
    }

    public String getEci() {
        return eci;
    }

    public String getStatus() {
        return status;
    }

    public String getDescription() {
        return description;
    }

    public static EciStatusEnum getByCode(String code) {
        for (EciStatusEnum status : values()) {
            if (status.getEci().equals(code)) {
                return status;
            }
        }
        return null;
    }
}


