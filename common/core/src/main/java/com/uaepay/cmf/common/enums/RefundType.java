package com.uaepay.cmf.common.enums;

import lombok.Getter;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date RefundType.java v1.0  2020-06-23 15:06
 */
@Getter
public enum RefundType {
    //  退款类型
    REFUND("refund", "退款"),
    CANCEL("cancel", "撤销");

    /** 代码 */
    private final String code;

    /** 描述信息 */
    private final String message;

    RefundType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static RefundType getByCode(String code) {
        for (RefundType type : RefundType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        // 默认为退款
        return RefundType.REFUND;
    }

}
