package com.uaepay.cmf.common.domain.fundin.ebank;

import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.ResponseType;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 *
 * <p>
 * 验签返回对象
 * </p>
 * 
 * <AUTHOR>
 * @version $Id: EBankChannelVerifyResult.java, v 0.1 2012-8-3 上午10:39:48 liumaoli Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class EBankChannelVerifyResult extends ChannelFundResult {

    private static final long serialVersionUID = 5141347839629073979L;

    /** 页面跳转类型 */
    private ResponseType responseType;

    /**
     * 响应银行信息
     */
    private Map<String, String> headerMap;

    /** 返回银行信息 */
    private String returnToInstData;
    /**
     * 结果数据
     */
    private String responseData;
    /**
     * 验签是否成功
     */
    private boolean isSuccess;
    /**
     * 机构支付域名
     */
    private String instRetPayDomain;
    /**
     * 机构返回付款ip
     */
    private String instRetPayIP;
}
