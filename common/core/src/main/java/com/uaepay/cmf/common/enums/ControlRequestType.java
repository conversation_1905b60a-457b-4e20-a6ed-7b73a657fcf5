package com.uaepay.cmf.common.enums;

import lombok.Getter;

/**
 * 控制类请求类型
 *
 * <AUTHOR> won
 */
@Getter
public enum ControlRequestType {

    // Control transaction requests
    ADVANCE("A", "Advance"),
    ADVANCE_QUERY("AQ", "Advance Query"),

    // Pre-authorization update
    PREAUTH_UPDATE("PAU", "Pre-authorization Update"),

    // Pre-authorization complete
    PREAUTH_COMPLETE("PAC", "Pre-authorization Complete"),
    // Pre-authorization void
    PREAUTH_VOID("PAV", "Pre-authorization Void"),

    AUTH("AUTH", "Authorization"),

    AUTHENTICATE_ADVANCE("AUTHA", "Authentication Advance"),

    NOTIFY("NT", "Notification"),

    QUERY_BALANCE("QB", "Query Balance"),

    DOWNLOAD_STATEMENT("DS", "Download Statement"),

    VOID_TRANSACTION("VT", "Void Transaction"),

    REVERSAL("RV", "Reversal"),

    FILE_MIGRATE("FM", "File Migration"),

    FILE_IMPORT("FI", "File Import"),

    CONTROL_VOID_TRANSACTION("CV", "Control Void Transaction"),

    VALIDATE_PARAMETER("VP", "Validate Parameter"),

    PRE_AUTH_QUERY("PAQ", "Pre-authorization Query"),

    MERCHANT_REGISTER("MCR", "Merchant Registration"),

    IBAN_DETAIL_QUERY("IBD", "IBAN Detail Query"),

    RETRIEVE_CARD_METADATA("RCM", "Retrieve Card Metadata"),
    ;

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述信息
     */
    private final String message;

    ControlRequestType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     *
     * @param code
     * @return
     */
    public static ControlRequestType getByCode(String code) {

        for (ControlRequestType type : ControlRequestType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

    public boolean isPreAuth(){
        return PREAUTH_UPDATE.code.equals(code) || PREAUTH_COMPLETE.code.equals(code);
    }

    public boolean updateOriginal(){
        return PREAUTH_UPDATE.code.equals(code) || PREAUTH_COMPLETE.code.equals(code);
    }

}
