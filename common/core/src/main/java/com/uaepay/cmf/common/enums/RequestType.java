package com.uaepay.cmf.common.enums;

import lombok.Getter;

/**
 * <p>请求类型</p>
 *
 * <AUTHOR> won
 * @version $Id: RequestType.java, v 0.1 2010-12-18 下午01:05:34 sean won Exp $
 */
@Getter
public enum RequestType {
    //
    FUND_IN("FUND_IN", "入款"),

    REFUND("REFUND", "退款"),

    FUND_OUT("FUND_OUT", "出款");

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述信息
     */
    private final String message;

    RequestType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     *
     * @param code
     * @return
     */
    public static RequestType getByCode(String code) {

        for (RequestType type : RequestType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
