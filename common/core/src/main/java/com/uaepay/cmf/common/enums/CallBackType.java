package com.uaepay.cmf.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>回调类型</p>
 *
 * <AUTHOR> won
 * @version $Id: NotifyType.java, v 0.1 2012-2-3 上午10:55:55 Yun=sean wonzhi<PERSON>ang Exp $
 */
@Getter
public enum CallBackType {

    /**
     * 页面方式回调通知
     */
    PAGE("page", "页面方式回调通知"),

    /**
     * 服务器对服务器方式通知
     */
    SERVER("server", "服务器对服务器方式通知");

    private final String code;

    private final String desc;

    CallBackType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CallBackType getByCode(String code) {

        for (CallBackType item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}
