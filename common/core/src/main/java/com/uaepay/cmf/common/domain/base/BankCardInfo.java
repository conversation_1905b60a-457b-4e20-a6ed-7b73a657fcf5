package com.uaepay.cmf.common.domain.base;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.uaepay.schema.cmf.enums.CardType;

/**
 * 
 * <p>
 * 银行卡信息
 * </p>
 * 
 * <AUTHOR>
 * @version $Id: BankCardInfo.java, v 0.1 2012-9-17 上午10:07:03 liumaoli Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class BankCardInfo implements Serializable {

    private static final long serialVersionUID = -961252575683018753L;
    /** 卡类型 */
    private CardType cardType;
    /** 卡号 */
    private String cardNo;
    /** 名称 */
    private String name;
    /** 有效期 */
    private String expiredDate;
}
