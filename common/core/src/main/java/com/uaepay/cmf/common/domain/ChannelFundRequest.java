package com.uaepay.cmf.common.domain;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.common.util.money.Money;

/**
 * <p>
 * 渠道支付类请求基类
 * </p>
 * 根据各个业务大类继承此基类实现进行扩展
 * 
 * <AUTHOR> won
 * @version $Id: ChannelFundRequest.java, v 0.1 2011-10-2 上午09:05:37 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChannelFundRequest extends ChannelRequest {
    private static final long serialVersionUID = 3566102968597941518L;

    /** 目标机构 */
    protected String targetInstCode;
    /** 金额 */
    protected Money amount;
    /** 机构订单提交时间 */
    private Date instOrderSubmitTime;

    /**
     * 默认构造
     */
    public ChannelFundRequest() {}

    /**
     * 根据API类型构造
     * 
     * @param fundChannelCode
     * @param apiType
     */
    public ChannelFundRequest(String fundChannelCode, FundChannelApiType apiType) {
        super(fundChannelCode, apiType);
    }

}
