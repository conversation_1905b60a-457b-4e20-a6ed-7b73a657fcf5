package com.uaepay.cmf.common.domain.fundin.ebank;


import com.uaepay.common.util.money.Money;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * <p>
 * 分期付款信息
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: Installment.java, v 0.1 2012-2-9 上午10:51:25 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class Installment {
    /**
     * 总金额
     */
    protected Money totalAmount;
    /**
     * 手续费
     */
    protected Money fee;
    /**
     * 首期金额
     */
    protected Money firstPaymentAmount;
    /**
     * 末期金额
     */
    protected Money lastPaymentAmount;
    /**
     * 中间每期金额
     */
    protected Money dividedAmount;
    /**
     * 期数
     */
    protected Integer numberOfPeriod;

}
