package com.uaepay.cmf.common.domain;

import java.util.List;

import com.uaepay.cmf.common.enums.FundChannelApiType;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 渠道资金批量结果对象
 * </p>
 * 
 * <AUTHOR>
 * @version $Id: ChannelFundBatchResult.java, v 0.1 2012-8-10 上午10:50:27 fuyangbiao Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChannelFundBatchResult extends ChannelFundResult {
    private static final long serialVersionUID = 8981110366122003531L;

    /** 批次号 */
    private Long archiveBatchId;

    /** 明细结果 */
    private List<ChannelFundResult> fundResultList;

    public ChannelFundBatchResult() {}

    public ChannelFundBatchResult(boolean status, String apiResultCode, String resultMessage, FundChannelApiType apiType) {
        setSuccess(status);
        this.setApiResultCode(apiResultCode);
        this.setApiResultMessage(resultMessage);
        setApiType(apiType);
    }

    public ChannelFundBatchResult(boolean success, String resultCode, Long archiveBatchId) {
        super(success, resultCode);
        this.archiveBatchId = archiveBatchId;
    }

}
