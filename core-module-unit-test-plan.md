# Core模块单元测试详细方案

## 目标
实现Core模块代码覆盖率达到80%，所有外部依赖使用Mock技术

## 项目结构分析

Core模块包含以下4个子模块：
- **core/dal** - 数据访问层
- **core/domain** - 域模型层  
- **core/engine** - 引擎层
- **core/util** - 工具类层

## 测试策略和Mock原则

### Mock策略
- **外部数据库调用**：所有DAO接口使用@MockBean
- **外部服务调用**：Spring服务使用@MockBean  
- **第三方和静态方法**: `BeanUtils`, `DateUtil`, `JSON`, `CollectionUtils` 等使用 **<PERSON><PERSON><PERSON>'s inline mock maker** 进行静态Mock
- **系统依赖**：`System.currentTimeMillis()` 等同样使用静态Mock

### 测试优先级分级

#### 高优先级（必须达到95%+覆盖率）
- **工具类**：`core/util`包下的所有工具类
- **域对象**：`core/domain`包下的核心业务对象
- **常量和枚举**：基础设施代码
- **异常处理类**：错误处理逻辑

#### 中优先级（目标85%覆盖率）
- **数据访问对象**：`core/dal/dataobject`包下的DO类
- **引擎组件**：`core/engine`包下的核心组件
- **域服务类**：业务逻辑处理类

#### 低优先级（目标70%覆盖率）
- **配置类**：Spring配置相关
- **生成的代码**：DAO接口等自动生成代码

### 详细测试计划

#### 2.1 core/util 模块测试

**目标覆盖率：95%**

##### 2.1.1 filter包测试

**类：`Util.java`**
- **测试类**：`UtilTest.java`
- **Mock依赖**：无外部依赖
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | 测试场景 |
|--------|----------|----------|----------|
| `testMatch_BothNull()` | `match(null, null)` | `true` | 两个参数都为null |
| `testMatch_FirstNull()` | `match(null, ["a"])` | `false` | 第一个参数为null |
| `testMatch_SecondNull()` | `match(["a"], null)` | `false` | 第二个参数为null |
| `testMatch_Success()` | `match(["a","b","c"], ["a","b"])` | `true` | 正常匹配成功 |
| `testMatch_Failure()` | `match(["a","b"], ["a","c"])` | `false` | 匹配失败 |
| `testIn_Null()` | `in(null, ["a"])` | `false` | 元素为null |
| `testIn_NullArray()` | `in("a", null)` | `false` | 数组为null |
| `testIn_Found()` | `in("a", ["a","b"])` | `true` | 找到元素 |
| `testIn_NotFound()` | `in("c", ["a","b"])` | `false` | 未找到元素 |
| `testContains()` | `contains(["a","b"], "a")` | `true` | 包含元素 |
| `testIs_Null()` | `is(["a","b"], null)` | `false` | value为null |
| `testIs_Success()` | `is(["a","b"], "a,b")` | `true` | 分割后完全匹配 |
| `testIs_Failure()` | `is(["a","b"], "a,c")` | `false` | 分割后不匹配 |

**类：`FilterAttributeUtil.java`**
- **测试类**：`FilterAttributeUtilTest.java`
- **Mock依赖**：
  - `LoggerFactory.getLogger()` - Mockito
  - `CmfOrder.getExtension()` - Mockito
  - `InstControlOrder.getExtension()` - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testConvert_CmfOrder_Fundin()` | 入款CmfOrder对象 | Map<String,Object> | Mock extension返回dbcr值 |
| `testConvert_CmfOrder_Fundout()` | 出款CmfOrder对象 | Map<String,Object> | Mock extension返回cardType值 |
| `testConvert_CmfOrder_NullAmount()` | amount为null的订单 | amount="0.00" | Mock getAmount()返回null |
| `testConvert_InstControlOrder()` | InstControlOrder对象 | Map<String,Object> | Mock所有extension调用 |

**类：`LogFilterUtil.java`**
- **测试类**：`LogFilterUtilTest.java`
- **Mock依赖**：
  - `MapUtils.isEmpty()` - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | 测试场景 |
|--------|----------|----------|----------|
| `testFilter_Null()` | `filter(null)` | `null` | 输入为null |
| `testFilter_CardNo()` | `"cardNo=1234567890123456,other=value"` | `"cardNo=************3456,other=value"` | 卡号脱敏，保留后4位 |
| `testFilter_Name()` | `"name=张三,other=value"` | `"name=**,other=value"` | 姓名完全脱敏 |
| `testFilter_Multiple()` | 包含多个敏感字段的字符串 | 多个字段都脱敏的结果 | 多字段脱敏 |
| `testFilter_Map()` | Map对象 | 脱敏后的字符串 | Map转字符串后脱敏 |

##### 2.1.2 biz包测试

**类：`MapUtil.java`**
- **测试类**：`MapUtilTest.java`
- **Mock依赖**：
  - `JSON.parseObject()` - Mockito
  - `JSON.toJSONString()` - Mockito
  - `CollectionUtils.isEmpty()` - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testJsonToMap_Valid()` | `'{"key":"value"}'` | `Map{key=value}` | Mock JSON.parseObject正常返回 |
| `testJsonToMap_Blank()` | `""` 或 `null` | `new HashMap<>()` | 无需Mock |
| `testSafeJsonToMap_Exception()` | 无效JSON字符串 | `new HashMap<>()` | Mock JSON.parseObject抛异常 |
| `testMapToJson_Valid()` | `Map{key=value}` | `'{"key":"value"}'` | Mock JSON.toJSONString返回 |
| `testMapToJson_Empty()` | 空Map | `null` | Mock CollectionUtils.isEmpty返回true |
| `testAddValue_NewKey()` | `'{"old":"value"}',"new","newValue"` | `'{"old":"value","new":"newValue"}'` | Mock所有JSON操作 |
| `testAddValue_Map()` | 源JSON，追加Map | 合并后的JSON | Mock所有JSON和Collection操作 |

##### 2.1.3 validate包测试

**类：`Validate.java`**
- **测试类**：`ValidateTest.java`
- **Mock依赖**：
  - `StringUtils.isBlank()` - Mockito
  - `StringUtils.contains()` - Mockito
  - `CollectionUtils.isEmpty()` - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testAssertNotNull_Success()` | `"field", "value"` | 无异常 | 无需Mock |
| `testAssertNotNull_Failure()` | `"field", null` | `ValidateException` | 无需Mock |
| `testAssertNotBlank_Success()` | `"field", "value"` | 无异常 | Mock StringUtils.isBlank返回false |
| `testAssertNotBlank_Failure()` | `"field", ""` | `ValidateException` | Mock StringUtils.isBlank返回true |
| `testAssertNotEmpty_List_Success()` | `"field", ["item"]` | 无异常 | 无需Mock |
| `testAssertNotEmpty_List_Failure()` | `"field", []` | `ValidateException` | 无需Mock |
| `testAssertGreaterZero_Success()` | `"field", Money("10.00")` | 无异常 | Mock Money.compareTo返回1 |
| `testAssertGreaterZero_Failure()` | `"field", Money("0.00")` | `ValidateException` | Mock Money.compareTo返回0 |
| `testAssertTrue_Success()` | `"condition", true` | 无异常 | 无需Mock |
| `testAssertTrue_Failure()` | `"condition", false` | `ValidateException` | 无需Mock |
| `testAssertNotBlankAndNoSpace_Success()` | `"field", "value"` | 无异常 | Mock StringUtils方法 |
| `testAssertNotBlankAndNoSpace_WithSpace()` | `"field", "val ue"` | `ValidateException` | Mock StringUtils.contains返回true |
| `testAssertEquals_Success()` | `"field", "expected", "expected"` | 无异常 | Mock StringUtils.equals返回true |
| `testAssertEquals_Failure()` | `"field", "expected", "actual"` | `ValidateException` | Mock StringUtils.equals返回false |
| `testAssertIn_Success()` | `"field", "value", ["value","other"]` | 无异常 | 无需Mock |
| `testAssertIn_Failure()` | `"field", "value", ["other"]` | `ValidateException` | 无需Mock |

##### 2.1.4 log包测试

**类：`LogUtil.java`**
- **测试类**：`LogUtilTest.java`
- **Mock依赖**：
  - `LoggerFactory.getLogger()` - Mockito
  - `Logger.info()` - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testInfo_LongTime()` | `"test", 1000L, 2500L` | 调用logger.info | Mock Logger，验证info被调用 |
| `testInfo_ShortTime()` | `"test", 1000L, 1500L` | 不调用logger.info | Mock Logger，验证info未被调用 |

##### 2.1.5 trans包测试

**类：`DOConverter.java`**
- **测试类**：`DOConverterTest.java`
- **Mock依赖**：
  - `CollectionUtils.isEmpty()` - Mockito
- **测试实现**：

```java
// 创建具体实现用于测试
private static class TestDOConverter extends DOConverter<String, Integer> {
    @Override
    public String convert(Integer from) {
        return from == null ? null : "value_" + from;
    }
}
```

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testConvert_Single_Success()` | `convert(123)` | `"value_123"` | 无需Mock |
| `testConvert_Single_Null()` | `convert(null)` | `null` | 无需Mock |
| `testConvert_List_Success()` | `convert([1,2,3])` | `["value_1","value_2","value_3"]` | Mock CollectionUtils.isEmpty返回false |
| `testConvert_List_Empty()` | `convert([])` | `new ArrayList<>()` | Mock CollectionUtils.isEmpty返回true |
| `testConvert_List_Null()` | `convert(null)` | `new ArrayList<>()` | Mock CollectionUtils.isEmpty返回true |

##### 2.1.6 form包测试

**类：`BankFormUtilImpl.java`**
- **测试类**：`BankFormUtilImplTest.java`
- **Mock依赖**：
  - `@MockBean SysConfigurationHolder` - Spring Mock
  - `MapUtil.jsonToMap()` - Mockito
  - `StringUtils` 所有方法 - Mockito
  - `Logger` - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testBuildSignForm_NoFormNeeded()` | ChannelFundResult | instUrl字符串 | Mock needSignForm返回false |
| `testBuildSignForm_WithPageUrl()` | 包含PAGE_URL_FOR_SIGN的result | pageUrl值 | Mock MapUtil返回包含pageUrl的Map |
| `testBuildSignForm_NormalForm()` | 正常ChannelFundResult | HTML form字符串 | Mock所有依赖，验证form格式 |
| `testBuildSignForm_WithEnctype()` | 包含ENCTYPE的result | 带enctype的form | Mock extension包含ENCTYPE |
| `testBuildSignForm_FilterKeys()` | 包含过滤字段的result | 不包含过滤字段的form | Mock extension包含过滤字段 |

##### 2.1.7 sysconfig包测试

**类：`SysConfigurationHolderImpl.java`**
- **测试类**：`SysConfigurationHolderImplTest.java`
- **Mock依赖**：
  - `@MockBean SysConfigurationDAO` - Spring Mock
  - `@MockBean CacheOperateTemplate` - Spring Mock
  - `StringUtils` 所有方法 - Mockito
  - `CompletableFuture.supplyAsync()` - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testGetConfiguration_Found()` | `"TEST_KEY"` | SysConfiguration对象 | Mock operateTemplate.load返回配置 |
| `testGetConfiguration_NotFound()` | `"INVALID_KEY"` | `null` | Mock operateTemplate.load返回null |
| `testLoadConfigureOrDefault_Found()` | `"TEST_KEY", "default"` | 配置值 | Mock getConfiguration返回配置 |
| `testLoadConfigureOrDefault_NotFound()` | `"INVALID_KEY", "default"` | `"default"` | Mock getConfiguration返回null |
| `testLoadConfigureOrDefault_Int_Valid()` | `"TEST_KEY", 100` | 配置的int值 | Mock配置值为数字字符串 |
| `testLoadConfigureOrDefault_Int_Invalid()` | `"TEST_KEY", 100` | `100` | Mock配置值为非数字字符串 |
| `testUpdate_ValueChanged()` | `"TEST_KEY", "newValue"` | 更新后的配置 | Mock DAO返回不同值的配置 |
| `testUpdate_ValueSame()` | `"TEST_KEY", "sameValue"` | 原配置 | Mock DAO返回相同值的配置 |
| `testRefreshCache()` | 无参数 | 无返回值 | Mock所有异步操作 |
| `testLoadAll()` | 无参数 | 配置列表 | Mock operateTemplate.load返回列表 |
| `testInsert_New()` | `"NEW_KEY", "value", "memo"` | `true` | Mock DAO.loadByKey返回null |
| `testInsert_Existing_SameValue()` | `"EXISTING_KEY", "sameValue", "memo"` | `true` | Mock DAO返回相同值配置 |
| `testInsert_Existing_DifferentValue()` | `"EXISTING_KEY", "newValue", "memo"` | `true` | Mock DAO返回不同值配置 |

#### 2.2 core/domain 模块测试

**目标覆盖率：85%**

##### 2.2.1 核心域对象测试

**类：`CmfOrder.java`**
- **测试类**：`CmfOrderTest.java`
- **Mock依赖**：
  - `Money` 构造函数 - Mockito/Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testConstructor_Default()` | `new CmfOrder()` | 默认值设置正确 | 验证amount初始化为ZERO_MONEY |
| `testSetAmount_Valid()` | `setAmount(Money("100.00"))` | amount设置成功 | Mock Money构造 |
| `testSetAmount_Null()` | `setAmount(null)` | amount设置为默认值 | 验证自动设置为ZERO_MONEY |
| `testGettersSetters_AllFields()` | 设置所有字段 | 所有字段get/set正确 | 测试所有属性的getter/setter |
| `testExtension_AddValue()` | `extension.put("key", "value")` | 扩展信息添加成功 | 验证Map操作 |
| `testToString()` | `toString()` | 包含所有字段的字符串 | 验证Lombok @ToString |

**类：`CmfRequest.java`**
- **测试类**：`CmfRequestTest.java`
- **Mock依赖**：无外部依赖
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testConstructor_Default()` | `new CmfRequest()` | 对象创建成功 | 无需Mock |
| `testCanRetry_True()` | `setCanRetry(true)` | `isCanRetry()` 返回true | 无需Mock |
| `testCanRetry_False()` | `setCanRetry(false)` | `isCanRetry()` 返回false | 无需Mock |
| `testPaymentSeqNo()` | `setPaymentSeqNo("12345")` | `getPaymentSeqNo()` 返回"12345" | 无需Mock |
| `testSettlementId()` | `setSettlementId("settlement123")` | `getSettlementId()` 返回值 | 无需Mock |
| `testTimestamps()` | 设置gmtCreate和gmtModified | 时间设置正确 | 无需Mock |

##### 2.2.2 渠道相关测试

**类：`SimpleChannelProperty.java`**
- **测试类**：`SimpleChannelPropertyTest.java`
- **Mock依赖**：
  - `DateUtil.addMinutes()` - Mockito
  - `Money.getAmount()` - Mockito
  - `Money.compareTo()` - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testConstructor()` | `new SimpleChannelProperty("test")` | name="test" | 无需Mock |
| `testAddProperty_String()` | `addProperty("sub")` | 创建子属性 | 无需Mock |
| `testAddProperty_Object()` | `addProperty(channelProperty)` | 添加子属性 | Mock ChannelProperty.getName() |
| `testAddProperty_Null()` | `addProperty(null)` | 不添加 | 无需Mock |
| `testSetValues()` | `setValues("a,b,c")` | values=["a","b","c"] | 无需Mock |
| `testSetValues_Null()` | `setValues(null)` | values=null | 无需Mock |
| `testGet_Exists()` | `get("existingKey")` | 返回对应属性 | Mock subProperties |
| `testGet_NotExists()` | `get("nonExistentKey")` | 返回null | Mock subProperties |
| `testHas_True()` | `has("existingKey")` | 返回true | Mock subProperties |
| `testHas_False()` | `has("nonExistentKey")` | 返回false | Mock subProperties |
| `testGetValue()` | values=["first","second"] | 返回"first" | 无需Mock |
| `testGetValue_Null()` | values=null | 返回null | 无需Mock |
| `testGreatThan_BigDecimal_True()` | values=["100"], 参数=50 | 返回true | 无需Mock |
| `testGreatThan_BigDecimal_False()` | values=["50"], 参数=100 | 返回false | 无需Mock |
| `testGreatThan_BigDecimal_Null()` | values=null, 参数=100 | 返回false | 无需Mock |
| `testEqualsBdecimal_True()` | values=["100"], 参数=100 | 返回true | 无需Mock |
| `testEqualsBdecimal_False()` | values=["100"], 参数=50 | 返回false | 无需Mock |
| `testLessThan_BigDecimal_True()` | values=["50"], 参数=100 | 返回true | 无需Mock |
| `testContains_BigDecimal_Found()` | values=["10","20","30"], 参数=20 | 返回true | 无需Mock |
| `testContains_BigDecimal_NotFound()` | values=["10","20","30"], 参数=40 | 返回false | 无需Mock |
| `testGreatThan_String_True()` | values=["b"], 参数="a" | 返回true | 无需Mock |
| `testEqualsStr_True()` | values=["test"], 参数="test" | 返回true | 无需Mock |
| `testContains_String_Found()` | values=["a","b","c"], 参数="b" | 返回true | 无需Mock |
| `testCover_BigDecimal_True()` | values=["10","100"], 参数=50 | 返回true | 无需Mock |
| `testCover_BigDecimal_False()` | values=["10","100"], 参数=5 | 返回false | 无需Mock |
| `testCover_Money_True()` | values=["10","100"], Money(50) | 返回true | Mock Money.getAmount()返回50 |
| `testGreatThan_Money_True()` | values=["100"], Money(50) | 返回true | Mock Money.getAmount()返回50 |
| `testAfter_Date_True()` | values=["10"], 1小时前的Date | 返回true | Mock DateUtil.addMinutes |
| `testBefore_Date_True()` | values=["-10"], 1小时后的Date | 返回true | Mock DateUtil.addMinutes |
| `testIsEmpty_String_True()` | 参数="" | 返回true | 无需Mock |
| `testIsEmpty_Money_True()` | Money对象amount=null | 返回true | Mock Money.getAmount()返回null |
| `testAny_Found()` | values=["a","b"], 参数=["b","c"] | 返回true | 无需Mock |
| `testAny_NotFound()` | values=["a","b"], 参数=["c","d"] | 返回false | 无需Mock |

##### 2.2.3 异常处理测试

**类：`AppCheckedException.java`**
- **测试类**：`AppCheckedExceptionTest.java`
- **Mock依赖**：
  - `ErrorCode` 枚举 - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testConstructor_Default()` | `new AppCheckedException()` | 异常创建成功 | 无需Mock |
| `testConstructor_Message()` | `new AppCheckedException("error")` | message="error" | 无需Mock |
| `testConstructor_ErrorCode_Message()` | `new AppCheckedException(errorCode, "msg")` | 设置错误码和消息 | Mock ErrorCode方法 |
| `testConstructor_ErrorCode()` | `new AppCheckedException(errorCode)` | 使用错误码的消息 | Mock errorCode.getErrorMessage() |
| `testConstructor_ErrorCode_Message_Cause()` | 带cause的构造 | 设置所有参数 | Mock ErrorCode和Throwable |
| `testConstructor_Message_Cause()` | 带cause的构造 | 设置消息和cause | 无需Mock |
| `testConstructor_Cause()` | `new AppCheckedException(cause)` | 设置cause | 无需Mock |
| `testGetCode()` | `getCode()` | 返回错误码字符串 | Mock errorCode.getErrorCode() |
| `testGetMessage_WithErrorCode()` | `getMessage()` | 返回错误码消息 | Mock errorCode.getErrorMessage() |
| `testGetMessage_WithoutErrorCode()` | errorCode=null | 返回super.getMessage() | 无需Mock |

**类：`AppRuntimeException.java`**（类似测试结构）
**类：`ValidateException.java`**（类似测试结构）
**类：其他异常类**（按相同模式测试）

##### 2.2.4 配置和常量测试

**类：`SysConfiguration.java`**
- **测试类**：`SysConfigurationTest.java`
- **Mock依赖**：无外部依赖
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testConstructor_Default()` | `new SysConfiguration()` | 对象创建成功 | 无需Mock |
| `testConstructor_AllArgs()` | `new SysConfiguration("key","value","memo")` | 所有字段设置正确 | 无需Mock |
| `testGettersSetters()` | 设置所有字段 | getter/setter正确 | 无需Mock |
| `testToString()` | `toString()` | 包含所有字段信息 | 无需Mock |

**接口：`BasicConstant.java`**
- **测试类**：`BasicConstantTest.java`
- **测试方法**：

| 方法名 | 测试内容 | 期望结果 |
|--------|----------|----------|
| `testConstantValues()` | 验证所有常量值 | 常量值正确 |
| `testFileEncode()` | FILE_ENCODE = "GBK" | 值正确 |
| `testDefaultCurrency()` | DEFAULT_CURRENCY = "AED" | 值正确 |
| `testZeroMoney()` | ZERO_MONEY | Money对象正确 |
| `testSuccessFailure()` | SUCCESS=0, FAILURE=-1 | 值正确 |

##### 2.2.5 机构订单相关测试

**类：`InstOrder.java`** 及其子类
- **测试类**：`InstOrderTest.java`、`InstFundinOrderTest.java`、`InstFundoutOrderTest.java`
- **Mock依赖**：各种枚举和Money类
- **测试内容**：继承关系、属性设置、业务逻辑方法

**类：`InstOrderResult.java`** 及相关结果类
- **测试类**：`InstOrderResultTest.java`等
- **测试内容**：结果状态、扩展信息、时间戳等

#### 2.3 core/dal 模块测试

**目标覆盖率：80%**

##### 2.3.1 数据对象基类测试

**类：`BaseDO.java`**
- **测试类**：`BaseDOTest.java`
- **Mock依赖**：
  - `BeanUtils.describe()` - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testToString_Success()` | 创建DO对象设置属性 | 格式化字符串 | Mock BeanUtils.describe返回属性Map |
| `testToString_Exception()` | BeanUtils抛异常 | super.toString()结果 | Mock BeanUtils.describe抛异常 |
| `testToString_FilterClass()` | 包含class属性 | 不包含class的字符串 | Mock返回的Map包含class键 |
| `testSerialization()` | 序列化后反序列化 | 对象相等 | 测试Serializable实现 |
| `testConstants()` | 验证常量值 | DEFAULT_AMOUNT="0.00" | 验证常量定义 |

##### 2.3.2 具体数据对象测试

**类：`CmfOrderDO.java`**
- **测试类**：`CmfOrderDOTest.java`
- **Mock依赖**：
  - `Money` 构造函数 - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testConstructor_Default()` | `new CmfOrderDO()` | amount初始化为默认值 | 验证Money默认值 |
| `testAllGettersSetters()` | 设置所有字段 | 所有字段正确 | 逐一测试每个属性 |
| `testAmount_DefaultValue()` | 不设置amount | Money("0.00", "AED") | 验证默认Money对象 |
| `testInheritance()` | 验证继承关系 | extends BaseDO | 验证类层次结构 |
| `testLombok_Data()` | 使用@Data注解 | getter/setter/toString存在 | 验证Lombok功能 |

**类：`SysConfigurationDO.java`**
- **测试类**：`SysConfigurationDOTest.java`
- **Mock依赖**：无外部依赖
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testConstructor_Default()` | `new SysConfigurationDO()` | 对象创建成功 | 无需Mock |
| `testConstructor_AllArgs()` | `new SysConfigurationDO("key","value","memo")` | 所有字段设置正确 | 无需Mock |
| `testGettersSetters()` | 设置所有属性 | getter/setter正确 | 测试所有属性 |
| `testInheritance()` | 验证继承 | extends BaseDO | 验证基类继承 |

**类：`InstBatchResultDO.java`**
- **测试类**：`InstBatchResultDOTest.java`
- **测试内容**：类似CmfOrderDO，包含多个Money字段的特殊处理

**类：`ControlOrderDO.java`** 等其他DO类
- **测试模式**：遵循相同的测试模式
- **重点测试**：Money字段的setter方法中的null值处理

##### 2.3.3 DAO接口测试

**接口：`SysConfigurationDAO.java`**
- **测试类**：`SysConfigurationDAOTest.java`
- **测试方法**：

| 方法名 | Mock行为 | 期望结果 | 验证内容 |
|--------|----------|----------|----------|
| `testLoadByKey_Found()` | Mock返回SysConfigurationDO | 返回对应对象 | 验证DAO方法调用 |
| `testLoadByKey_NotFound()` | Mock返回null | 返回null | 验证查询不存在的key |
| `testLoadAll()` | Mock返回List<SysConfigurationDO> | 返回配置列表 | 验证批量查询 |
| `testInsert()` | Mock insert操作 | 插入成功 | 验证插入方法调用 |
| `testUpdate()` | Mock update操作 | 更新成功 | 验证更新方法调用 |

**其他DAO接口**：按相同模式测试CRUD操作

#### 2.4 core/engine 模块测试

**目标覆盖率：80%**

##### 2.4.1 调度相关测试

**类：`DaemonContext.java`**
- **测试类**：`DaemonContextTest.java`
- **Mock依赖**：无外部依赖
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testConstructor_Default()` | `new DaemonContext()` | 对象创建成功 | 无需Mock |
| `testGettersSetters_TriggerId()` | `setTriggerId("trigger123")` | `getTriggerId()` 返回值 | 无需Mock |
| `testGettersSetters_FireTime()` | `setFireTime(new Date())` | `getFireTime()` 返回设置的时间 | 无需Mock |
| `testGettersSetters_TargetIdent()` | `setTargetIdent("target456")` | `getTargetIdent()` 返回值 | 无需Mock |
| `testToString()` | `toString()` | 包含所有字段的字符串 | 验证Lombok @ToString |
| `testLombok_Annotations()` | 验证Lombok注解 | getter/setter存在 | 验证注解功能 |

**接口：`DaemonTask.java`**
- **测试类**：`DaemonTaskImplTest.java`
- **测试实现**：

```java
// 创建测试实现类
private static class TestDaemonTask implements DaemonTask {
    @Override
    public DaemonTaskResult execute(DaemonContext context) {
        // 测试实现
    }
    
    @Override
    public DaemonTaskType getTaskType() {
        return DaemonTaskType.TEST_TYPE;
    }
    
    @Override
    public void execute(ShardingContext shardingContext) {
        // SimpleJob接口实现
    }
}
```

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testExecute_DaemonContext()` | DaemonContext对象 | DaemonTaskResult | Mock context内容 |
| `testGetTaskType()` | 无参数 | DaemonTaskType枚举值 | 无需Mock |
| `testExecute_ShardingContext()` | ShardingContext对象 | 无返回值 | Mock SimpleJob方法 |
| `testBasicConstant_Implementation()` | 验证常量接口 | 能访问BasicConstant | 验证接口继承 |

##### 2.4.2 缓存组件测试

**假设存在缓存相关类**：
- **测试类**：`CacheTest.java`
- **Mock依赖**：Redis、Local Cache等外部缓存
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testCacheGet_Hit()` | 缓存key | 缓存值 | Mock缓存返回值 |
| `testCacheGet_Miss()` | 不存在的key | null | Mock缓存返回null |
| `testCachePut()` | key, value | 存储成功 | Mock缓存存储操作 |
| `testCacheEvict()` | 缓存key | 清除成功 | Mock缓存清除操作 |
| `testCacheExpire()` | key, ttl | 设置超时成功 | Mock TTL设置 |

##### 2.4.3 生成器组件测试

**假设存在PrimaryKeyGenerator类**：
- **测试类**：`PrimaryKeyGeneratorTest.java`
- **Mock依赖**：
  - 数据库序列 - Mock
  - System.currentTimeMillis() - Mockito
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testGenerateId_Sequence()` | 序列名称 | 唯一ID | Mock数据库序列返回值 |
| `testGenerateId_TimeBase()` | 时间戳类型 | 基于时间的ID | Mock System.currentTimeMillis() |
| `testGenerateId_UUID()` | UUID类型 | UUID字符串 | Mock UUID.randomUUID() |
| `testGenerateId_Concurrent()` | 并发调用 | 所有ID唯一 | 多线程测试 |
| `testGenerateId_Exception()` | 异常场景 | 降级策略 | Mock抛异常，测试降级 |

##### 2.4.4 锁相关组件测试

**假设存在分布式锁类**：
- **测试类**：`DistributedLockTest.java`
- **Mock依赖**：
  - Redis客户端 - Mock
  - ZooKeeper客户端 - Mock
- **测试方法**：

| 方法名 | 输入参数 | 期望输出 | Mock设置 |
|--------|----------|----------|----------|
| `testLock_Success()` | 锁key | 获取成功 | Mock Redis SET NX成功 |
| `testLock_Failure()` | 已被占用的锁key | 获取失败 | Mock Redis SET NX失败 |
| `testUnlock_Success()` | 已持有的锁 | 释放成功 | Mock Redis DEL成功 |
| `testLock_Timeout()` | 设置超时的锁 | 自动释放 | Mock TTL机制 |
| `testLock_Reentrant()` | 重入锁场景 | 支持重入 | 测试重入计数 |

### 3. 测试工具和框架

#### 3.1 核心测试框架

**JUnit 5** (主测试框架)
```xml
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <version>5.8.2</version>
    <scope>test</scope>
</dependency>
```

**Mockito** (Mock框架)
```xml
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-core</artifactId>
    <version>4.6.1</version>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-inline</artifactId>
    <version>4.6.1</version>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-junit-jupiter</artifactId>
    <version>4.6.1</version>
    <scope>test</scope>
</dependency>
```

**AssertJ** (断言库)
```xml
<dependency>
    <groupId>org.assertj</groupId>
    <artifactId>assertj-core</artifactId>
    <version>3.23.1</version>
    <scope>test</scope>
</dependency>
```

#### 3.2 Mock和测试增强工具

**Spring Boot Test** (Spring Mock支持)
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

#### 3.3 代码覆盖率工具

**JaCoCo** 配置
```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.7</version>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
        <execution>
            <id>check</id>
            <goals>
                <goal>check</goal>
            </goals>
            <configuration>
                <rules>
                    <rule>
                        <element>CLASS</element>
                        <limits>
                            <limit>
                                <counter>LINE</counter>
                                <value>COVEREDRATIO</value>
                                <minimum>0.80</minimum>
                            </limit>
                        </limits>
                    </rule>
                </rules>
            </configuration>
        </execution>
    </executions>
</plugin>
```

#### 3.4 测试基础设施

**测试基类设计**
```java
@ExtendWith(MockitoExtension.class)
public abstract class BaseCoreTest {
    
    protected static final String TEST_PAYMENT_SEQ_NO = "TEST_PAY_123456";
    protected static final String TEST_MEMBER_ID = "TEST_MEMBER_001";
    protected static final Money TEST_AMOUNT = new Money("100.00", "AED");
    
    @BeforeEach
    public void setUp() {
        // 通用setup逻辑
    }
    
    protected CmfOrder createTestCmfOrder() {
        // 创建测试用的CmfOrder对象
    }
    
    protected Map<String, String> createTestExtension() {
        // 创建测试用的扩展信息
    }
}
```

#### 3.5 Mock工具集

**通用Mock工具类**
```java
public class MockUtils {
    
    public static Money createMockMoney(String amount) {
        Money money = Mockito.mock(Money.class);
        when(money.getAmount()).thenReturn(new BigDecimal(amount));
        return money;
    }
    
    public static Logger createMockLogger() {
        return Mockito.mock(Logger.class);
    }

    // Static mocking is now done within tests using try-with-resources,
    // making generic helper methods for it less practical.
    // Example in a test:
    // try (MockedStatic<DateUtil> mocked = Mockito.mockStatic(DateUtil.class)) {
    //     mocked.when(() -> DateUtil.addMinutes(any(), anyLong())).thenReturn(someDate);
    //     // ... test logic ...
    // }
}
```

#### 3.6 测试数据管理策略

**测试数据构建器**
```java
public class TestDataBuilder {
    
    public static class CmfOrderBuilder {
        private CmfOrder order = new CmfOrder();
        
        public CmfOrderBuilder withPaymentSeqNo(String paymentSeqNo) {
            order.setPaymentSeqNo(paymentSeqNo);
            return this;
        }
        
        public CmfOrderBuilder withAmount(String amount) {
            order.setAmount(new Money(amount, "AED"));
            return this;
        }
        
        public CmfOrderBuilder withBizType(BizType bizType) {
            order.setBizType(bizType);
            return this;
        }
        
        public CmfOrderBuilder withExtension(String key, String value) {
            order.getExtension().put(key, value);
            return this;
        }
        
        public CmfOrder build() {
            // 设置必要的默认值
            if (order.getPaymentSeqNo() == null) {
                order.setPaymentSeqNo("TEST_" + System.currentTimeMillis());
            }
            if (order.getGmtCreate() == null) {
                order.setGmtCreate(new Date());
            }
            return order;
        }
    }
    
    public static class SysConfigurationDOBuilder {
        private SysConfigurationDO config = new SysConfigurationDO();
        
        public SysConfigurationDOBuilder withKey(String key) {
            config.setAttrName(key);
            return this;
        }
        
        public SysConfigurationDOBuilder withValue(String value) {
            config.setAttrValue(value);
            return this;
        }
        
        public SysConfigurationDOBuilder withMemo(String memo) {
            config.setMemo(memo);
            return this;
        }
        
        public SysConfigurationDO build() {
            if (config.getGmtCreated() == null) {
                config.setGmtCreated(new Date());
            }
            return config;
        }
    }
    
    // 常用测试数据常量
    public static final class TestConstants {
        public static final String DEFAULT_PAYMENT_SEQ_NO = "PAY_TEST_123456";
        public static final String DEFAULT_MEMBER_ID = "MEMBER_TEST_001";
        public static final Money DEFAULT_AMOUNT = new Money("100.00", "AED");
        public static final BizType DEFAULT_BIZ_TYPE = BizType.FUNDIN;
        
        public static final Map<String, String> DEFAULT_EXTENSION = Map.of(
            ExtensionKey.DBCR.key, "D",
            ExtensionKey.CARD_TYPE.key, "VISA",
            ExtensionKey.ACCESS_CHANNEL.key, "WEB"
        );
    }
}
```

**测试夹具管理**
```java
@TestMethodOrder(OrderAnnotation.class)
public abstract class IntegrationTestBase extends BaseCoreTest {
    
    @BeforeAll
    static void setupTestData() {
        // 准备集成测试需要的基础数据
    }
    
    @AfterAll
    static void cleanupTestData() {
        // 清理测试数据
    }
    
    @BeforeEach
    void setupEachTest() {
        // 每个测试前的准备工作
    }
    
    @AfterEach
    void cleanupEachTest() {
        // 每个测试后的清理工作
    }
}
```

#### 3.7 异常场景和边界值测试策略

**异常场景测试矩阵**

| 异常类型 | 测试场景 | 验证要点 | 示例 |
|----------|----------|----------|------|
| 空值异常 | null参数输入 | 抛出正确异常类型和消息 | `assertThatThrownBy(() -> util.match(null, ["a"])).isInstanceOf(NullPointerException.class)` |
| 参数异常 | 无效参数 | 验证参数校验逻辑 | 负数金额、空字符串等 |
| 状态异常 | 对象状态不正确 | 业务规则验证 | 已关闭订单不能修改 |
| 外部依赖异常 | 外部服务不可用 | 降级和重试机制 | 数据库连接失败、缓存异常 |
| 并发异常 | 多线程竞争 | 线程安全验证 | 同步块、CAS操作 |
| 资源异常 | 内存/IO异常 | 资源管理验证 | 文件读写、网络超时 |

**边界值测试策略**
```java
public class BoundaryTestHelper {
    
    // 数值边界值
    public static final BigDecimal[] DECIMAL_BOUNDARIES = {
        new BigDecimal("0"),
        new BigDecimal("0.01"),
        new BigDecimal("-0.01"),
        new BigDecimal("999999.99"),
        new BigDecimal("1000000.00"),
        BigDecimal.valueOf(Double.MAX_VALUE),
        BigDecimal.valueOf(Double.MIN_VALUE)
    };
    
    // 字符串边界值
    public static final String[] STRING_BOUNDARIES = {
        null,
        "",
        " ",
        "a",
        "A".repeat(255),  // 通常的varchar长度限制
        "A".repeat(256),  // 超出限制
        "特殊字符!@#$%^&*()",
        "中文测试数据",
        "\n\t\r特殊换行符"
    };
    
    // 日期边界值
    public static final Date[] DATE_BOUNDARIES = {
        new Date(0),                    // 1970-01-01
        new Date(),                     // 当前时间
        new Date(System.currentTimeMillis() + 86400000), // 明天
        new Date(System.currentTimeMillis() - 86400000), // 昨天
        new Date(Long.MAX_VALUE),       // 最大日期
        new Date(Long.MIN_VALUE)        // 最小日期
    };
    
    @ParameterizedTest
    @MethodSource("decimalBoundaries")
    @DisplayName("金额边界值测试")
    void testAmountBoundaries(BigDecimal amount) {
        // 边界值测试逻辑
    }
    
    @ParameterizedTest
    @MethodSource("stringBoundaries")
    @DisplayName("字符串边界值测试")
    void testStringBoundaries(String input) {
        // 边界值测试逻辑
    }
}
```

**并发测试策略**
```java
@ExtendWith(MockitoExtension.class)
public class ConcurrencyTestBase extends BaseCoreTest {
    
    @Test
    @DisplayName("并发访问测试")
    void testConcurrentAccess() throws InterruptedException {
        int threadCount = 10;
        int operationsPerThread = 100;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);
        List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());
        
        // 创建多个线程同时执行操作
        for (int i = 0; i < threadCount; i++) {
            Thread thread = new Thread(() -> {
                try {
                    startLatch.await();
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 执行被测试的操作
                        performOperation();
                    }
                } catch (Exception e) {
                    exceptions.add(e);
                } finally {
                    endLatch.countDown();
                }
            });
            thread.start();
        }
        
        startLatch.countDown(); // 同时开始
        endLatch.await(30, TimeUnit.SECONDS); // 等待完成
        
        // 验证结果
        assertThat(exceptions).isEmpty();
    }
    
    private void performOperation() {
        // 被测试的操作
    }
}
```

### 4. **测试执行效率优化策略**

#### 4.1 测试分类和执行策略

**测试分类标记**
```java
// 快速测试 - 单元测试，不依赖外部资源
@Tag("fast")
public interface FastTest {}

// 慢速测试 - 集成测试，依赖外部资源
@Tag("slow") 
public interface SlowTest {}

// 并发测试 - 可能耗时较长的并发测试
@Tag("concurrent")
public interface ConcurrentTest {}

// 使用示例
@FastTest
class UtilTest {
    // 快速单元测试
}

@SlowTest 
class SysConfigurationHolderImplTest {
    // 涉及缓存和DAO的集成测试
}
```

**测试执行配置**
```xml
<!-- Maven Surefire 配置 -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <version>3.0.0-M7</version>
    <configuration>
        <!-- 默认只执行快速测试 -->
        <groups>fast</groups>
        <parallel>classes</parallel>
        <threadCount>4</threadCount>
        <forkCount>2</forkCount>
        <reuseForks>true</reuseForks>
    </configuration>
    <executions>
        <!-- 完整测试套件 -->
        <execution>
            <id>full-test</id>
            <phase>integration-test</phase>
            <goals>
                <goal>test</goal>
            </goals>
            <configuration>
                <groups>fast,slow</groups>
            </configuration>
        </execution>
    </executions>
</plugin>
```

#### 4.2 测试资源管理

**测试资源池**
```java
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ResourcePoolTest {
    
    private static final ExecutorService THREAD_POOL = 
        Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
    
    @AfterAll
    void cleanup() {
        THREAD_POOL.shutdown();
    }
    
    // 共享昂贵的测试资源
    @Test
    void testWithSharedResource() {
        // 使用共享资源进行测试
    }
}
```

### 5. **CI/CD 集成和自动化**

#### 5.1 持续集成配置

**Jenkins Pipeline 示例**
```groovy
pipeline {
    agent any
    stages {
        stage('快速测试') {
            steps {
                sh 'mvn test -Dgroups=fast'
            }
            post {
                always {
                    publishTestResults testResultsPattern: 'target/surefire-reports/*.xml'
                    publishCoverage adapters: [jacocoAdapter('target/site/jacoco/jacoco.xml')]
                }
            }
        }
        stage('覆盖率检查') {
            steps {
                sh 'mvn jacoco:check'
            }
        }
        stage('完整测试') {
            when {
                anyOf {
                    branch 'main'
                    branch 'develop'
                }
            }
            steps {
                sh 'mvn integration-test -Dgroups=fast,slow'
            }
        }
    }
}
```

**GitHub Actions 配置**
```yaml
name: Core Module Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        java: [11, 17]
    
    steps:
    - uses: actions/checkout@v3
    - name: Set up JDK ${{ matrix.java }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ matrix.java }}
        distribution: 'temurin'
    
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
    
    - name: Run fast tests
      run: mvn test -Dgroups=fast
    
    - name: Check coverage
      run: mvn jacoco:check
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./target/site/jacoco/jacoco.xml
```

### 6. **测试报告和度量**

#### 6.1 自定义测试报告

**扩展的覆盖率报告**
```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <configuration>
        <rules>
            <rule>
                <element>PACKAGE</element>
                <limits>
                    <!-- util包必须达到95% -->
                    <limit>
                        <counter>LINE</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>0.95</minimum>
                    </limit>
                </limits>
                <includes>
                    <include>com.uaepay.cmf.common.core.util.*</include>
                </includes>
            </rule>
            <rule>
                <element>PACKAGE</element>
                <limits>
                    <!-- domain包必须达到85% -->
                    <limit>
                        <counter>LINE</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>0.85</minimum>
                    </limit>
                </limits>
                <includes>
                    <include>com.uaepay.cmf.common.core.domain.*</include>
                </includes>
            </rule>
        </rules>
    </configuration>
</plugin>
```

### 7. **测试维护和持续改进**

#### 7.1 测试质量监控

**测试代码质量检查**
```java
// 测试命名规范检查
@Test
void should_ThrowValidateException_When_FieldIsNull() {
    // 清晰的测试命名：should_期望结果_When_条件
}

// 测试复杂度控制
@Test 
@DisplayName("单一职责测试 - 只测试一个场景")
void testSingleScenario() {
    // 每个测试方法只验证一个场景
    // 避免过于复杂的测试逻辑
}
```

#### 7.2 测试债务管理

**定期Review清单**
- [ ] 测试覆盖率趋势分析
- [ ] 慢速测试识别和优化  
- [ ] 重复测试代码重构
- [ ] 测试数据维护
- [ ] Mock使用合理性检查
- [ ] 测试文档更新

### 8. **团队协作和培训**

#### 8.1 测试规范文档

**团队测试标准**
- 测试方法命名规范：`should_ExpectedResult_When_Condition`
- 测试结构：Given-When-Then模式
- Mock使用原则：外部依赖必须Mock，内部逻辑尽量真实
- 断言规范：使用AssertJ流式断言
- 测试数据：使用Builder模式创建测试数据

#### 8.2 知识分享计划

**培训内容**
1. **Week 1**: JUnit 5 基础和最佳实践
2. **Week 2**: Mockito 高级用法和静态Mock
3. **Week 3**: 测试数据管理和Builder模式
4. **Week 4**: 并发测试和性能测试基础

### 4. 测试代码组织结构

```
test/
├── src/
│   └── test/
│       └── java/
│           └── com/
│               └── uaepay/
│                   └── cmf/
│                       └── common/
│                           └── core/
│                               ├── dal/
│                               │   ├── dataobject/
│                               │   │   ├── BaseDOTest.java
│                               │   │   ├── CmfOrderDOTest.java
│                               │   │   └── ...
│                               │   └── daointerface/
│                               │       └── DAOTest.java
│                               ├── domain/
│                               │   ├── CmfOrderTest.java
│                               │   ├── CmfRequestTest.java
│                               │   ├── channel/
│                               │   │   └── SimpleChannelPropertyTest.java
│                               │   └── exception/
│                               │       └── AppCheckedExceptionTest.java
│                               ├── engine/
│                               │   ├── schedule/
│                               │   │   └── DaemonContextTest.java
│                               │   └── cache/
│                               │       └── CacheTest.java
│                               └── util/
│                                   ├── filter/
│                                   │   └── UtilTest.java
│                                   └── trans/
│                                       └── DOConverterTest.java
```

### 5. 实施计划

#### 第一阶段（1-2周）：工具类和域对象测试
1. 完成`core/util`包下所有工具类测试
2. 完成核心域对象测试
3. 设置JaCoCo覆盖率监控

#### 第二阶段（2-3周）：数据访问层测试
1. 完成所有DO类测试
2. 完成DAO接口Mock测试
3. 验证数据访问层覆盖率达标

#### 第三阶段（1-2周）：引擎层测试和整合
1. 完成引擎层组件测试
2. 整合所有测试，调整覆盖率
3. 优化测试用例，确保达到80%目标

### 6. 覆盖率目标和测试示例

#### 6.1 详细覆盖率目标

| 模块 | 目标覆盖率 | 类数量(估算) | 测试类数量 | 重点关注 |
|------|------------|--------------|------------|----------|
| core/util/filter | 95% | 3 | 3 | 过滤逻辑完整性 |
| core/util/biz | 95% | 2 | 2 | JSON/Map转换正确性 |
| core/util/validate | 95% | 1 | 1 | 验证逻辑完整性 |
| core/util/log | 90% | 1 | 1 | 日志工具稳定性 |
| core/util/trans | 95% | 1 | 1 | 转换器抽象正确性 |
| core/util/form | 85% | 2 | 2 | 表单处理逻辑 |
| core/util/sysconfig | 90% | 2 | 2 | 配置管理完整性 |
| core/domain/channel | 90% | 2 | 2 | 渠道属性逻辑 |
| core/domain/exception | 85% | 10 | 10 | 异常处理完整性 |
| core/domain/entity | 80% | 15 | 15 | 领域对象完整性 |
| core/domain/enums | 75% | 20 | 5 | 枚举值验证 |
| core/dal/dataobject | 80% | 25 | 25 | DO对象完整性 |
| core/dal/daointerface | 70% | 15 | 15 | DAO接口Mock测试 |
| core/engine/schedule | 85% | 5 | 5 | 调度组件稳定性 |
| core/engine/cache | 80% | 8 | 8 | 缓存组件正确性 |
| core/engine/util | 90% | 3 | 3 | 引擎工具完整性 |
| **总体目标** | **≥80%** | **≈115** | **≈100** | **核心功能全覆盖** |

#### 6.2 关键测试示例

**示例1：Util类完整测试**
```java
@ExtendWith(MockitoExtension.class)
class UtilTest extends BaseCoreTest {
    
    private Util util = new Util();
    
    @Test
    @DisplayName("测试match方法-两参数都为null")
    void testMatch_BothNull() {
        // Given
        String[] more = null;
        String[] less = null;
        
        // When
        boolean result = util.match(more, less);
        
        // Then
        assertThat(result).isTrue();
    }
    
    @Test
    @DisplayName("测试match方法-正常匹配成功")
    void testMatch_Success() {
        // Given
        String[] more = {"a", "b", "c"};
        String[] less = {"a", "b"};
        
        // When
        boolean result = util.match(more, less);
        
        // Then
        assertThat(result).isTrue();
    }
    
    @ParameterizedTest
    @DisplayName("测试is方法-参数化测试")
    @CsvSource({
        "'a,b', 'a,b', true",
        "'a,b', 'a,c', false",
        "'a', 'a', true",
        ", 'a', false"
    })
    void testIs_Parameterized(String setValue, String testValue, boolean expected) {
        // Given
        String[] set = setValue != null ? setValue.split(",") : null;
        
        // When
        boolean result = util.is(set, testValue);
        
        // Then
        assertThat(result).isEqualTo(expected);
    }
}
```

**示例2：SimpleChannelProperty复杂测试**
```java
@ExtendWith(MockitoExtension.class)
class SimpleChannelPropertyTest extends BaseCoreTest {
    
    @Mock
    private Money mockMoney;
    
    @Test
    @DisplayName("测试BigDecimal比较-边界值测试")
    void testBigDecimalComparison_BoundaryValues() {
        // Given
        SimpleChannelProperty property = new SimpleChannelProperty("test");
        property.setValues("100.00");
        
        // When & Then
        assertThat(property.greatThan(new BigDecimal("99.99"))).isTrue();
        assertThat(property.greatThan(new BigDecimal("100.00"))).isFalse();
        assertThat(property.greatThan(new BigDecimal("100.01"))).isFalse();
        
        assertThat(property.equalsBdecimal(new BigDecimal("100.00"))).isTrue();
        assertThat(property.equalsBdecimal(new BigDecimal("100.01"))).isFalse();
        
        assertThat(property.lessThan(new BigDecimal("100.01"))).isTrue();
        assertThat(property.lessThan(new BigDecimal("100.00"))).isFalse();
    }
    
    @Test
    @DisplayName("测试Money类型比较-Mock外部依赖")
    void testMoneyComparison_WithMock() {
        // Given
        SimpleChannelProperty property = new SimpleChannelProperty("test");
        property.setValues("100.00");
        
        when(mockMoney.getAmount()).thenReturn(new BigDecimal("50.00"));
        
        // When & Then
        assertThat(property.greatThan(mockMoney)).isTrue();
        verify(mockMoney).getAmount();
    }
    
    @Test
    @DisplayName("测试日期比较-Mockito静态方法Mock")
    void testDateComparison_WithStaticMock() {
        // Given
        SimpleChannelProperty property = new SimpleChannelProperty("test");
        property.setValues("10"); // 10分钟
        
        Date testDate = new Date();
        Date futureDate = new Date(System.currentTimeMillis() + 600000); // 10分钟后
        
        // When
        boolean result;
        try (MockedStatic<DateUtil> mocked = Mockito.mockStatic(DateUtil.class)) {
            mocked.when(() -> DateUtil.addMinutes(any(Date.class), eq(10L))).thenReturn(futureDate);
            result = property.after(testDate);
            mocked.verify(() -> DateUtil.addMinutes(any(Date.class), eq(10L)));
        }
        
        // Then
        assertThat(result).isTrue();
    }
}
```

**示例3：SysConfigurationHolderImpl集成测试**
```java
@ExtendWith(MockitoExtension.class)
class SysConfigurationHolderImplTest extends BaseCoreTest {
    
    @Mock
    private SysConfigurationDAO sysConfigurationDAO;
    
    @Mock
    private CacheOperateTemplate operateTemplate;
    
    @InjectMocks
    private SysConfigurationHolderImpl sysConfigurationHolder;
    
    @Test
    @DisplayName("测试配置获取-缓存命中")
    void testGetConfiguration_CacheHit() {
        // Given
        String key = "TEST_KEY";
        SysConfiguration expectedConfig = new SysConfiguration(key, "test_value", "test_memo");
        
        when(operateTemplate.load(eq(CacheType.CMF_SYS_CONFIGURATION), eq(key), any(DataLoader.class)))
            .thenReturn(expectedConfig);
        
        // When
        SysConfiguration result = sysConfigurationHolder.getConfiguration(key);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAttrName()).isEqualTo(key);
        assertThat(result.getAttrValue()).isEqualTo("test_value");
        
        verify(operateTemplate).load(eq(CacheType.CMF_SYS_CONFIGURATION), eq(key), any(DataLoader.class));
    }
    
    @Test
    @DisplayName("测试配置更新-值相同不更新")
    void testUpdate_SameValue_NoUpdate() {
        // Given
        String key = "TEST_KEY";
        String value = "same_value";
        SysConfigurationDO configDO = new SysConfigurationDO(key, value, "memo");
        when(sysConfigurationDAO.loadByKey(key)).thenReturn(configDO);
        
        // When
        SysConfiguration result;
        try (MockedStatic<StringUtils> mocked = Mockito.mockStatic(StringUtils.class)) {
            mocked.when(() -> StringUtils.equals(value, value)).thenReturn(true);
            result = sysConfigurationHolder.update(key, value);
        }
        
        // Then
        assertThat(result.getAttrValue()).isEqualTo(value);
        verify(sysConfigurationDAO, never()).update(any());
        verify(sysConfigurationDAO).loadByKey(key);
    }
    
    @Test
    @DisplayName("测试异步缓存刷新-Mockito静态方法Mock")
    void testRefreshCache_AsyncOperation() {
        // Given
        CompletableFuture<Void> mockFuture = mock(CompletableFuture.class);

        // When
        try (MockedStatic<CompletableFuture> mocked = Mockito.mockStatic(CompletableFuture.class)) {
            mocked.when(() -> CompletableFuture.supplyAsync(any(Supplier.class))).thenReturn(mockFuture);
            sysConfigurationHolder.refreshCache();
            mocked.verify(() -> CompletableFuture.supplyAsync(any(Supplier.class)));
        }
    }
}
```

### 7. 质量保证措施

#### 7.1 代码质量
- 每个测试方法必须有明确的测试目的
- 使用Given-When-Then模式组织测试
- 测试命名要清晰表达测试意图

#### 7.2 覆盖率监控
- 在CI/CD中集成JaCoCo报告
- 设置覆盖率门槛，低于80%时构建失败
- 定期Review覆盖率报告，识别遗漏点

#### 7.3 维护策略
- 新增代码必须同时添加对应测试
- 定期重构测试代码，保持测试代码质量
- 建立测试代码Review机制

### 8. 风险和应对措施

#### 8.1 技术风险
- **复杂业务逻辑难以模拟**：采用分层测试策略，单元测试+集成测试结合
- **外部依赖过多**：大量使用Mock技术，隔离外部依赖
- **遗留代码质量差**：优先重构，再编写测试

#### 8.2 进度风险
- **时间估算不准确**：采用迭代方式，优先高价值测试
- **人员技能不足**：提供测试培训，建立最佳实践文档

### 9. 成功标准

1. **量化指标**：
   - Core模块整体代码覆盖率≥80%
   - 关键工具类覆盖率≥95%
   - 核心业务逻辑覆盖率≥85%

2. **质量指标**：
   - 所有测试用例可重复执行
   - 测试执行时间合理（≤5分钟）
   - 测试代码可维护性良好

3. **持续改进**：
   - 建立覆盖率趋势监控
   - 定期优化低效测试用例
   - 持续完善测试工具链

## 结论

通过这份详细的测试方案，可以系统性地将Core模块的代码覆盖率提升至80%以上。本方案的核心价值在于：

### 🎯 **方案特色**

1. **方法级精确规划**：每个类的每个方法都有具体的测试计划，包含输入、输出、Mock策略
2. **Mock策略完善**：所有外部依赖都有明确的Mock方案，确保单元测试的独立性
3. **覆盖率量化管理**：分模块设定不同的覆盖率目标，重点突出，资源分配合理
4. **实战代码示例**：提供完整的测试代码示例，可直接参考实施

### 📊 **预期成果**

- **约115个类**需要测试，计划编写**约100个测试类**
- **高优先级模块**（util、domain核心）达到**90%+覆盖率**
- **中优先级模块**（dal、engine）达到**80%+覆盖率**
- **整体目标**：Core模块覆盖率达到**≥80%**

### 🛠 **技术保障**

- **现代化测试框架**：JUnit 5 + Mockito + AssertJ + PowerMock
- **完整Mock体系**：静态方法、Spring Bean、外部依赖全覆盖
- **自动化监控**：JaCoCo集成，CI/CD覆盖率门槛控制
- **测试基础设施**：通用基类、Mock工具、测试数据构建器

### 🚀 **实施优势**

1. **可操作性强**：详细到方法级别的测试计划，减少实施时的思考成本
2. **质量可控**：明确的Mock策略和断言要求，确保测试质量
3. **效率优化**：分优先级实施，优先解决高价值场景
4. **持续改进**：建立长效机制，支持后续迭代维护

### 📈 **项目收益**

- **稳定性提升**：80%覆盖率大幅降低线上bug率
- **重构信心**：完善的测试套件支持安全重构
- **开发效率**：测试驱动开发，提前发现问题
- **团队协作**：统一的测试标准和最佳实践

**该方案不仅是一份测试计划，更是一套完整的Core模块质量保障体系，为项目长期稳定发展奠定坚实基础。** 