# Core模块Mock配置问题技术解决方案

## 🎯 **核心策略：真实对象 + 简化Mock**

### **解决方案1：Money类 - 使用真实对象**

**问题**：`com.uaepay.common.util.money.Money` 无法被Mock

**解决方案**：使用真实的Money对象而不是Mock
```java
// ❌ 错误的Mock方式
@Mock 
private Money mockMoney;

// ✅ 正确的真实对象方式
private Money testMoney = new Money("100.00", "AED");
```

**代码示例**：
```java
@Test
void testAssertGreaterZero_Success() {
    // Given - 使用真实Money对象
    Money realMoney = new Money("100.00", "AED");
    
    // When & Then - 不需要Mock，直接测试
    assertThatCode(() -> Validate.assertGreaterZero(realMoney, "TEST"))
        .doesNotThrowAnyException();
}
```

---

### **解决方案2：CacheOperateTemplate - 接口Mock**

**问题**：`CacheOperateTemplate` 具体类无法Mock

**解决方案**：Mock接口或使用测试专用实现
```java
// ❌ 错误的具体类Mock
@Mock
private CacheOperateTemplate<String, SysConfiguration> cacheTemplate;

// ✅ 正确的接口Mock或测试实现
@Mock  
private CacheOperateInterface<String, SysConfiguration> cacheInterface;

// 或者使用测试专用实现
private TestCacheOperateTemplate testCacheTemplate = new TestCacheOperateTemplate();
```

---

### **解决方案3：静态方法Mock优化**

**Java 17兼容的静态Mock**：
```java
@Test
void testStaticMock() {
    // ✅ 正确的静态Mock方式
    try (MockedStatic<StringUtils> mockedStatic = Mockito.mockStatic(StringUtils.class)) {
        // 配置Mock行为
        mockedStatic.when(() -> StringUtils.isBlank("test")).thenReturn(false);
        
        // 执行测试
        boolean result = StringUtils.isBlank("test");
        
        // 验证结果
        assertThat(result).isFalse();
    } // 自动释放资源
}
```

---

## 🚀 **实施计划**

### **Phase 1：修复Money类测试（ValidateTest）**
1. 替换所有Money Mock为真实对象
2. 重新设计测试用例，使用真实Money进行边界值测试
3. 预计恢复29个测试用例

### **Phase 2：修复CacheOperateTemplate测试**
1. 创建TestCacheOperateTemplate测试实现
2. 或者重构为接口依赖，Mock接口而非具体类
3. 预计恢复19个测试用例

### **Phase 3：优化静态Mock配置**
1. 统一使用try-with-resources模式
2. 升级到兼容Java 17的Mock配置
3. 验证所有静态Mock正常工作

---

## 📊 **预期效果**

| 修复项 | 当前状态 | 预期效果 | 覆盖率提升 |
|--------|----------|----------|------------|
| ValidateTest | 29个错误 | 29个通过 | +25% |
| SysConfigurationHolderImplTest | 19个错误 | 19个通过 | +20% |  
| 静态Mock | 部分失败 | 全部通过 | +10% |
| **总计** | **34%覆盖率** | **89%覆盖率** | **+55%** |

---

## 🎯 **技术结论**

**主要问题**：Java 17 + Mockito inline的兼容性问题
**解决原则**：
1. **真实对象优于Mock**：对于简单数据类使用真实对象
2. **接口Mock优于具体类Mock**：避免复杂的字节码修改
3. **资源管理**：严格使用try-with-resources管理静态Mock

**最终目标**：在不降低测试质量的前提下，达到**89%+代码覆盖率** 