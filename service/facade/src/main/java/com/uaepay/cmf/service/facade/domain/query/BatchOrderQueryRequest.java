package com.uaepay.cmf.service.facade.domain.query;

import com.uaepay.basis.beacon.service.facade.domain.request.PageRequest;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BatchOrderQueryRequest.java v1.0
 */
public class BatchOrderQueryRequest extends PageRequest {
    private static final long serialVersionUID = 6147368197121775705L;

    private String channelCode;

    private String status;

    private Date gmtStart;

    private Date gmtEnd;

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getGmtStart() {
        return gmtStart;
    }

    public void setGmtStart(Date gmtStart) {
        this.gmtStart = gmtStart;
    }

    public Date getGmtEnd() {
        return gmtEnd;
    }

    public void setGmtEnd(Date gmtEnd) {
        this.gmtEnd = gmtEnd;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
