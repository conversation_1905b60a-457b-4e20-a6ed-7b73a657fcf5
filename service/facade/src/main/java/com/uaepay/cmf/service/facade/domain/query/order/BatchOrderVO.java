package com.uaepay.cmf.service.facade.domain.query.order;

import com.uaepay.common.util.money.Money;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BatchOrderVO.java v1.0
 */
public class BatchOrderVO implements Serializable {
    private static final long serialVersionUID = -6553950500688856707L;

    /**
     * 归档批次ID
     */
    private long archiveBatchId;
    /**
     * 机构批次号
     */
    private String instBatchNo;
    /**
     * 总金额
     */
    private Money amount;
    /**
     * 总笔数
     */
    private Integer totalCount;
    /**
     * 状态
     */
    private String status;
    /**
     * 归档时间
     */
    private Date gmtArchive;
    /**
     * 渠道编号
     */
    private String channelCode;
    /**
     * 接口编码
     */
    private String apiCode;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 文件标签
     */
    private String fileTag;
    /**
     * 文件扩展名
     */
    private String fileExt;
    /**
     * 扩展参数
     */
    private String extension;

    public long getArchiveBatchId() {
        return archiveBatchId;
    }

    public void setArchiveBatchId(long archiveBatchId) {
        this.archiveBatchId = archiveBatchId;
    }

    public String getInstBatchNo() {
        return instBatchNo;
    }

    public void setInstBatchNo(String instBatchNo) {
        this.instBatchNo = instBatchNo;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getGmtArchive() {
        return gmtArchive;
    }

    public void setGmtArchive(Date gmtArchive) {
        this.gmtArchive = gmtArchive;
    }

    public String getApiCode() {
        return apiCode;
    }

    public void setApiCode(String apiCode) {
        this.apiCode = apiCode;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileTag() {
        return fileTag;
    }

    public void setFileTag(String fileTag) {
        this.fileTag = fileTag;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getFileExt() {
        return fileExt;
    }

    public void setFileExt(String fileExt) {
        this.fileExt = fileExt;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
