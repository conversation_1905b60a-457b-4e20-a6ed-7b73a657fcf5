package com.uaepay.cmf.service.facade.api;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.service.facade.domain.fundin.RefundQueryRequest;
import com.uaepay.cmf.service.facade.domain.refund.RetryRefundRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.RefundQueryResult;

/**
 * <p>
 * 充退验证接口
 * </p>
 * 充退之前PE调用CMF验证该笔定单是否可以充退等
 *
 * <AUTHOR> won
 * @version $Id: RefundFacade.java, v 0.1 2010-12-24 上午09:30:49 sean won Exp $
 */
public interface RefundFacade {

    /**
     * 查询退款周期
     *
     * @return
     */
    RefundQueryResult queryDuration(RefundQueryRequest request);


    CommonResponse retryReFund(RetryRefundRequest request);

}
