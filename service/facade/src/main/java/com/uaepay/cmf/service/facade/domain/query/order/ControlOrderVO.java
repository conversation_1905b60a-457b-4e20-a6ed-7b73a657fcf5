package com.uaepay.cmf.service.facade.domain.query.order;

import com.uaepay.common.util.money.Money;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ControlOrderVO.java v1.0
 */
public class ControlOrderVO implements Serializable {
    private static final long serialVersionUID = 2915791767760526833L;

    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 请求号
     */
    private String requestNo;
    /**
     * 请求类型
     */
    private String requestType;
    /**
     * 目标机构代码
     */
    protected String instCode;
    /**
     * 支付模式
     */
    private String payMode;
    /**
     * 渠道编号
     */
    private String fundChannelCode;
    /**
     * 接口类型
     */
    private String apiType;
    /**
     * 提交机构订单号
     */
    protected String instOrderNo;
    /**
     * 产品编码
     */
    protected String productCode;
    /**
     * 金额
     */
    protected Money amount;
    /**
     * 状态
     */
    protected String status;
    /**
     * 通知前端结果状态
     */
    private String notifyStatus;
    /**
     * 请求时间
     */
    private Date gmtCreate;
    /**
     * 扩展参数
     */
    private Map<String, String> extMap;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getFundChannelCode() {
        return fundChannelCode;
    }

    public void setFundChannelCode(String fundChannelCode) {
        this.fundChannelCode = fundChannelCode;
    }

    public String getApiType() {
        return apiType;
    }

    public void setApiType(String apiType) {
        this.apiType = apiType;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getNotifyStatus() {
        return notifyStatus;
    }

    public void setNotifyStatus(String notifyStatus) {
        this.notifyStatus = notifyStatus;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Map<String, String> getExtMap() {
        return extMap;
    }

    public void setExtMap(Map<String, String> extMap) {
        this.extMap = extMap;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
