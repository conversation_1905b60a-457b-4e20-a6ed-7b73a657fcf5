package com.uaepay.cmf.service.facade.result;

import com.uaepay.common.util.money.Money;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 资金源支付结果
 * </p>
 *
 * <AUTHOR>
 */
public class CmfResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 支付流水号
     */
    private String paymentSeqNo;

    /**
     * 清算请求号
     */
    private String requestNo;

    /**
     * CMF返回码
     */
    private String returnCode;
    /**
     * CMF返回信息
     */
    private String returnMessage;

    /**
     * 实际使用资金渠道
     */
    private String fundsChannelCode;

    /**
     * 渠道订单号
     */
    private String channelPayNo;

    /**
     * 提交机构订单号
     */
    private String instPayNo;

    /**
     * 机构支付完成时间
     */
    private Date instPayTime;

    /**
     * 实付金额
     */
    private Money amount;

    /**
     * 统一返回码,资金源（外部资金源、储值）处理结果的统一代码
     */
    private String unityResultCode;

    /**
     * 统一返回信息
     */
    private String unityResultMessage;

    /**
     * 扩展字段 json格式map
     */
    private String extension;

    public String getPaymentSeqNo() {
        return paymentSeqNo;
    }

    public void setPaymentSeqNo(String paymentSeqNo) {
        this.paymentSeqNo = paymentSeqNo;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public String getFundsChannelCode() {
        return fundsChannelCode;
    }

    public void setFundsChannelCode(String fundsChannelCode) {
        this.fundsChannelCode = fundsChannelCode;
    }

    public String getChannelPayNo() {
        return channelPayNo;
    }

    public void setChannelPayNo(String channelPayNo) {
        this.channelPayNo = channelPayNo;
    }

    public String getInstPayNo() {
        return instPayNo;
    }

    public void setInstPayNo(String instPayNo) {
        this.instPayNo = instPayNo;
    }

    public Date getInstPayTime() {
        return instPayTime;
    }

    public void setInstPayTime(Date instPayTime) {
        this.instPayTime = instPayTime;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public String getUnityResultCode() {
        return unityResultCode;
    }

    public void setUnityResultCode(String unityResultCode) {
        this.unityResultCode = unityResultCode;
    }

    public String getUnityResultMessage() {
        return unityResultMessage;
    }

    public void setUnityResultMessage(String unityResultMessage) {
        this.unityResultMessage = unityResultMessage;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
