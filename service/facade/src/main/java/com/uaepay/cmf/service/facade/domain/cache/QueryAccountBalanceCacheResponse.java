package com.uaepay.cmf.service.facade.domain.cache;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.common.util.money.Money;

/**
 * <AUTHOR>
 * @date 02/09/2024 15:04
 */
public class QueryAccountBalanceCacheResponse extends CommonResponse {

    private static final long serialVersionUID = 3429530028007856395L;

    private Money balance;

    private Money threshold;

    public Money getBalance() {
        return balance;
    }

    public void setBalance(Money balance) {
        this.balance = balance;
    }

    public Money getThreshold() {
        return threshold;
    }

    public void setThreshold(Money threshold) {
        this.threshold = threshold;
    }
}
