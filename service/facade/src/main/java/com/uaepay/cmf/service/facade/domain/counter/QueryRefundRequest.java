package com.uaepay.cmf.service.facade.domain.counter;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;

/**
 * <AUTHOR>
 * @date 23/08/2024 13:19
 */
public class QueryRefundRequest extends AbstractRequest {

    private static final long serialVersionUID = -7997466367118125158L;

    private String instOrderNo;
    
    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }
}
