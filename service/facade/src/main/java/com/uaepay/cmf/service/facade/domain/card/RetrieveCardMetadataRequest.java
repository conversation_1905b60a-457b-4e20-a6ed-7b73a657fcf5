package com.uaepay.cmf.service.facade.domain.card;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;

import javax.validation.constraints.NotBlank;

public class RetrieveCardMetadataRequest extends AbstractRequest{

    private static final long serialVersionUID = 7018287094956239494L;

    @NotBlank(message = "Request no should not be null!")
    private String requestNo;

    @NotBlank(message = "Card no should not be null!")
    private String cardNo;

    private String bankCode;

    private String cardType;

    private String countryCode;

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
}
