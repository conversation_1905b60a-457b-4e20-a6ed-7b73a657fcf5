package com.uaepay.cmf.service.facade.domain.counter.channelcode;

import java.io.Serializable;
import java.util.Date;

public class ChannelCodeMappingPageResponse implements Serializable {
    private static final long serialVersionUID = -1467978627658660275L;
    /**
     * Rule ID
     */
    private Long id;

    /**
     * Original channel code
     */
    private String oldChannelCode;

    /**
     * New channel code
     */
    private String newChannelCode;

    /**
     * Match expression
     */
    private String matchExpression;

    /**
     * Priority
     */
    private Integer priority;

    /**
     * Status (Y-enabled, N-disabled)
     */
    private String status;

    /**
     * Memo
     */
    private String memo;

    /**
     * Rule name
     */
    private String ruleName;

    /**
     * Create time
     */
    private Date gmtCreate;

    /**
     * Modify time
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOldChannelCode() {
        return oldChannelCode;
    }

    public void setOldChannelCode(String oldChannelCode) {
        this.oldChannelCode = oldChannelCode;
    }

    public String getNewChannelCode() {
        return newChannelCode;
    }

    public void setNewChannelCode(String newChannelCode) {
        this.newChannelCode = newChannelCode;
    }

    public String getMatchExpression() {
        return matchExpression;
    }

    public void setMatchExpression(String matchExpression) {
        this.matchExpression = matchExpression;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}