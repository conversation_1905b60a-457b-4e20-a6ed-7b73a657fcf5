package com.uaepay.cmf.service.facade.domain;

/**
 * 返回控制类指令结果
 *
 * <AUTHOR> won
 */
public enum CmfCommonResultCode {
    //
    SUCCESS("0000", "处理成功"),
    /**
     * 已提交到机构，等待结果返回
     */
    REQUEST_SUCCESS("0001", "提交CMF成功"),
    /**
     * 可能出现提交异常，结果待确认 返回调用方 机构支付已提交
     */
    IN_PROCESS("0002", "处理中"),
    /**
     * 机构支付明确失败
     */
    FAILED("0003", "处理失败"),
    CHANNEL_NOT_ACCESS("E001", "无可用渠道"),
    RepeatRequestError("E002", "重复请求"),
    BALANCE_IS_NOT_ENOUGH("E003", "余额不足"),
    BUSINESS_CHECK_EXCEPTION("E004", "业务校验异常"),

    INST_ORDER_IS_NOT_EXISTS("E010", "机构订单不存在"),
    CMF_ORDER_IS_NOT_EXISTS("E011", "机构订单不存在"),
    INST_ORDER_RESULT_IS_NOT_EXISTS("E012", "机构订单结果不存在"),

    VALIDARE_ERROR("E998", "校验不通过"),

    UNKNOW_EXCEPTION("E999", "未知异常");

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述信息
     */
    private final String message;

    CmfCommonResultCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 通过代码获取
     *
     * @param code
     * @return
     */
    public static CmfCommonResultCode getByCode(String code) {

        for (CmfCommonResultCode type : CmfCommonResultCode.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }
}
