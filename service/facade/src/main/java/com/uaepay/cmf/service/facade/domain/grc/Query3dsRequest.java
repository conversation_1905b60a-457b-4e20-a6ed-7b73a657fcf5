package com.uaepay.cmf.service.facade.domain.grc;


import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date Query3dsRequest.java v1.0  2020-10-16 12:00
 */
public class Query3dsRequest implements Serializable {
    private static final long serialVersionUID = 1065808885369460415L;

    /**
     * 产品订单号
     */
    private String productOrderNo;

    /**
     * 支付订单号
     */
    private String paymentOrderNo;

    /**
     * 机构订单号
     */
    private String instOrderNo;

    public String getProductOrderNo() {
        return productOrderNo;
    }

    public void setProductOrderNo(String productOrderNo) {
        this.productOrderNo = productOrderNo;
    }

    public String getPaymentOrderNo() {
        return paymentOrderNo;
    }

    public void setPaymentOrderNo(String paymentOrderNo) {
        this.paymentOrderNo = paymentOrderNo;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
