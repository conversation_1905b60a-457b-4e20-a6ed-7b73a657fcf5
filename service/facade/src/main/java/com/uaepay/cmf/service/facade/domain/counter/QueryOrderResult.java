package com.uaepay.cmf.service.facade.domain.counter;

import com.uaepay.schema.cmf.enums.InstOrderStatus;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * counter
 * </p>
 *
 * <AUTHOR>
 * @version $Id: OrderResult.java, v 0.1 2012-10-25 下午3:09:55 liumaoli Exp $
 */
public class QueryOrderResult implements Serializable {

    private static final long serialVersionUID = 1692677048534113520L;
    /**
     * 查询状态
     */
    private InstOrderStatus status;

    /**
     * 备注
     */
    private String messag;

    public QueryOrderResult() {

    }

    public QueryOrderResult(InstOrderStatus status, String message) {
        this.status = status;
        this.messag = message;
    }

    public InstOrderStatus getStatus() {
        return status;
    }

    public void setStatus(InstOrderStatus status) {
        this.status = status;
    }

    public String getMessag() {
        return messag;
    }

    public void setMessag(String messag) {
        this.messag = messag;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
