package com.uaepay.cmf.service.facade.domain.control;

import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.Kvp;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>控制类请求</p>
 *
 * <AUTHOR>
 * @version $Id: CmfControlRequest.java, v 0.1 2012-8-17 下午3:47:57 fuyangbiao Exp $
 */
public class CmfControlRequest implements Serializable {
    private static final long serialVersionUID = -4184461781833269836L;

    /**
     * 请求号，非空
     */
    private String requestNo;
    /**
     * 请求类型，非空
     */
    private ControlRequestType requestType;
    /**
     * 原请求号，可空
     */
    private String preRequestNo;
    /**
     * 原请求结算ID,可空
     */
    private String preSettlementId;
    /**
     * 目标机构代码，非空
     */
    private String instCode;
    /**
     * 支付模式，可空
     */
    private PayMode payMode;
    /**
     * 产品编码，可空
     */
    private String productCode;
    /**
     * 金额 ，可空
     */
    private Money amount;
    /**
     * 扩展信息，可空
     */
    private Extension extension;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public ControlRequestType getRequestType() {
        return requestType;
    }

    public void setRequestType(ControlRequestType requestType) {
        this.requestType = requestType;
    }

    public String getPreRequestNo() {
        return preRequestNo;
    }

    public void setPreRequestNo(String preRequestNo) {
        this.preRequestNo = preRequestNo;
    }

    public String getPreSettlementId() {
        return preSettlementId;
    }

    public void setPreSettlementId(String preSettlementId) {
        this.preSettlementId = preSettlementId;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public PayMode getPayMode() {
        return payMode;
    }

    public void setPayMode(PayMode payMode) {
        this.payMode = payMode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public Extension getExtension() {
        return extension;
    }

    public void setExtension(Extension extension) {
        this.extension = extension;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE)
                + ",extension:" + toStringExtension();
    }

    protected String toStringExtension() {
        if (extension == null || extension.getEntryList() == null) {
            return null;
        }
        StringBuilder builder = new StringBuilder();
        for (Kvp kvp : extension.getEntryList()) {
            builder.append(kvp.getKey()).append("=").append(kvp.getValue()).append(",");
        }
        return builder.toString();
    }
}
