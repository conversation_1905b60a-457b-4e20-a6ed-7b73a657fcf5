package com.uaepay.cmf.service.facade.result;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BindCardRequest.java v1.0  2020-03-27 20:23
 */
public class BindCardRequest implements Serializable {

    private static final long serialVersionUID = 3458231097605462211L;

    /**
     * clientId
     */
    private String clientId;
    /**
     * 会员卡id
     */
    private Long cardId;
    /**
     * 目标机构
     */
    private String instCode;

    /**
     * 发卡行
     */
    private String issueBank;

    /**
     * 发卡行名称
     */
    private String issueBankName;

    /**
     * 渠道编号
     */
    private String channelCode;

    /**
     * 机构订单号
     */
    private String instOrderNo;

    /**
     * 支付订单号
     */
    private String paymentOrderNo;

    /**
     * 会员id
     */
    private String memberId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 持卡人
     */
    private String cardHolder;

    /**
     * 卡品牌
     */
    private String cardBrand;

    /**
     * 机构Token
     */
    private String instTokenId;

    /**
     * eci状态
     */
    private String eciStatus;

    /**
     * 返回iban
     */
    private String iban;

    /**
     * 账号
     */
    private String cardAccountNo;

    /**
     * 卡类型- DC/CC
     */
    private String cardType;

    /**
     * 卡分类- TOKEN/COMMON
     */
    private String cardCategory;

    /**
     * 有效期
     */
    private String expiredDate;

    /**
     * 扩展参数
     */
    private String extension;

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getIssueBankName() {
        return issueBankName;
    }

    public void setIssueBankName(String issueBankName) {
        this.issueBankName = issueBankName;
    }

    public String getIssueBank() {
        return issueBank;
    }

    public void setIssueBank(String issueBank) {
        this.issueBank = issueBank;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public String getPaymentOrderNo() {
        return paymentOrderNo;
    }

    public void setPaymentOrderNo(String paymentOrderNo) {
        this.paymentOrderNo = paymentOrderNo;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getInstTokenId() {
        return instTokenId;
    }

    public void setInstTokenId(String instTokenId) {
        this.instTokenId = instTokenId;
    }

    public String getCardHolder() {
        return cardHolder;
    }

    public void setCardHolder(String cardHolder) {
        this.cardHolder = cardHolder;
    }

    public String getCardBrand() {
        return cardBrand;
    }

    public void setCardBrand(String cardBrand) {
        this.cardBrand = cardBrand;
    }

    public String getEciStatus() {
        return eciStatus;
    }

    public void setEciStatus(String eciStatus) {
        this.eciStatus = eciStatus;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getCardAccountNo() {
        return cardAccountNo;
    }

    public void setCardAccountNo(String cardAccountNo) {
        this.cardAccountNo = cardAccountNo;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardCategory() {
        return cardCategory;
    }

    public void setCardCategory(String cardCategory) {
        this.cardCategory = cardCategory;
    }

    public String getExpiredDate() {
        return expiredDate;
    }

    public void setExpiredDate(String expiredDate) {
        this.expiredDate = expiredDate;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    @Override
    public String toString() {
        ReflectionToStringBuilder.setDefaultStyle(ToStringStyle.SHORT_PREFIX_STYLE);
        return ReflectionToStringBuilder.toStringExclude(this, new String[]{"cardNo", "cardHolder", "iban", "expiredDate"});
    }

}
