package com.uaepay.cmf.service.facade.domain.auth;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import com.uaepay.payment.common.v2.enums.PayMode;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CmfAuthRequest.java v1.0
 */
public class CmfAuthRequest extends AbstractRequest {
    private static final long serialVersionUID = 8031458940173917697L;

    @NotEmpty(message = "Request no should not be null!")
    private String requestNo;
    /**
     * 目标机构代码，若cardToken中已传入则可空
     */
    private String instCode;
    /**
     * 卡tokenId
     */
    @NotEmpty(message = "Card token id should not be null!")
    private String cardTokenId;
    /**
     * 支付模式，不可空
     */
    @NotNull(message = "Pay mode should not be null!")
    private PayMode payMode;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getCardTokenId() {
        return cardTokenId;
    }

    public void setCardTokenId(String cardTokenId) {
        this.cardTokenId = cardTokenId;
    }

    public PayMode getPayMode() {
        return payMode;
    }

    public void setPayMode(PayMode payMode) {
        this.payMode = payMode;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
