package com.uaepay.cmf.service.facade.domain.counter;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date 23/08/2024 13:11
 */
public class QueryRefundResponse extends CommonResponse {
    private static final long serialVersionUID = 6241492623914165135L;

    InstOrderVO instOrder;

    List<InstOrderVO> refundList;

    public InstOrderVO getInstOrder() {
        return instOrder;
    }

    public void setInstOrder(InstOrderVO instOrder) {
        this.instOrder = instOrder;
    }

    public List<InstOrderVO> getRefundList() {
        return refundList;
    }

    public void setRefundList(List<InstOrderVO> refundList) {
        this.refundList = refundList;
    }
}
