package com.uaepay.cmf.service.facade.result;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.Kvp;
import com.uaepay.common.util.money.Money;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <p>
 * CMF处理结果
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: DepositResult.java, v 0.1 2010-12-18 下午12:40:33 sean won Exp $
 */
public class CmfFundResult extends CommonResponse {
    private static final long serialVersionUID = -4869821182798291416L;

    /**
     * 结果代码
     */
    private CmfFundResultCode resultCode;
    /**
     * 扩展信息
     */
    private Extension extension;
    /**
     * 资金源id
     */
    private String fundsChannel;
    /**
     * 渠道支付编号
     */
    private String channelPayNo;
    /**
     * cmf机构订单号
     */
    private String instOrderNo;
    /**
     * 渠道支付时间
     */
    private Date instPayTime;
    /**
     * 实际支付金额
     */
    protected Money amount;

    protected boolean success = false;
    protected String errorCode;
    protected String resultMessage;

    /**
     * 默认构造
     */
    public CmfFundResult() {
    }

    /**
     * 根据结果代码构造
     *
     * @param resultCode
     */
    public CmfFundResult(CmfFundResultCode resultCode) {
        this.setResultCode(resultCode);
    }

    public CmfFundResultCode getResultCode() {
        return resultCode;
    }

    public void setResultCode(CmfFundResultCode resultCode) {
        this.success = resultCode == CmfFundResultCode.SUCCESS;
        this.resultCode = resultCode;
    }

    public Extension getExtension() {
        return extension;
    }

    public void setExtension(Extension extension) {
        this.extension = extension;
    }

    public String getFundsChannel() {
        return fundsChannel;
    }

    public void setFundsChannel(String fundsChannel) {
        this.fundsChannel = fundsChannel;
    }

    public String getChannelPayNo() {
        return channelPayNo;
    }

    public void setChannelPayNo(String channelPayNo) {
        this.channelPayNo = channelPayNo;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public Date getInstPayTime() {
        return instPayTime;
    }

    public void setInstPayTime(Date instPayTime) {
        this.instPayTime = instPayTime;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getResultMessage() {
        return resultMessage;
    }

    public void setResultMessage(String resultMessage) {
        this.resultMessage = resultMessage;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE) + ",extension:"
                + toStringExtension();
    }

    private String toStringExtension() {
        if (extension == null || extension.getEntryList() == null) {
            return null;
        }
        StringBuilder builder = new StringBuilder();
        for (Kvp kvp : extension.getEntryList()) {
            builder.append(kvp.getKey()).append("=").append("x*").append(StringUtils.isEmpty(kvp.getValue())?0:kvp.getValue().length()).append(",");
        }
        return builder.toString();
    }
}
