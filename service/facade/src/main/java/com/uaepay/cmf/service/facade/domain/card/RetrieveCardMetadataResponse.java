package com.uaepay.cmf.service.facade.domain.card;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;

/**
 * <AUTHOR>
 * @date 19/02/2025 16:17
 */
public class RetrieveCardMetadataResponse extends CommonResponse {

    private static final long serialVersionUID = -1857634119991421668L;

    private String bin;
    private String scheme;
    private String schemeLocal;
    private String cardType;
    private String cardCategory;
    private String currency;
    private String issuer;
    private String issuerCountry;
    private String issuerCountryName;
    private String productId;
    private String productType;
    private String domesticNonMoneyTransfer;
    private String crossBorderNonMoneyTransfer;
    private String domesticGambling;
    private String crossBorderGambling;
    private String domesticMoneyTransfer;
    private String crossBorderMoneyTransfer;

    public String getBin() {
        return bin;
    }

    public void setBin(String bin) {
        this.bin = bin;
    }

    public String getScheme() {
        return scheme;
    }

    public void setScheme(String scheme) {
        this.scheme = scheme;
    }

    public String getSchemeLocal() {
        return schemeLocal;
    }

    public void setSchemeLocal(String schemeLocal) {
        this.schemeLocal = schemeLocal;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardCategory() {
        return cardCategory;
    }

    public void setCardCategory(String cardCategory) {
        this.cardCategory = cardCategory;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getIssuerCountry() {
        return issuerCountry;
    }

    public void setIssuerCountry(String issuerCountry) {
        this.issuerCountry = issuerCountry;
    }

    public String getIssuerCountryName() {
        return issuerCountryName;
    }

    public void setIssuerCountryName(String issuerCountryName) {
        this.issuerCountryName = issuerCountryName;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getDomesticNonMoneyTransfer() {
        return domesticNonMoneyTransfer;
    }

    public void setDomesticNonMoneyTransfer(String domesticNonMoneyTransfer) {
        this.domesticNonMoneyTransfer = domesticNonMoneyTransfer;
    }

    public String getCrossBorderNonMoneyTransfer() {
        return crossBorderNonMoneyTransfer;
    }

    public void setCrossBorderNonMoneyTransfer(String crossBorderNonMoneyTransfer) {
        this.crossBorderNonMoneyTransfer = crossBorderNonMoneyTransfer;
    }

    public String getDomesticGambling() {
        return domesticGambling;
    }

    public void setDomesticGambling(String domesticGambling) {
        this.domesticGambling = domesticGambling;
    }

    public String getCrossBorderGambling() {
        return crossBorderGambling;
    }

    public void setCrossBorderGambling(String crossBorderGambling) {
        this.crossBorderGambling = crossBorderGambling;
    }

    public String getDomesticMoneyTransfer() {
        return domesticMoneyTransfer;
    }

    public void setDomesticMoneyTransfer(String domesticMoneyTransfer) {
        this.domesticMoneyTransfer = domesticMoneyTransfer;
    }

    public String getCrossBorderMoneyTransfer() {
        return crossBorderMoneyTransfer;
    }

    public void setCrossBorderMoneyTransfer(String crossBorderMoneyTransfer) {
        this.crossBorderMoneyTransfer = crossBorderMoneyTransfer;
    }
}
