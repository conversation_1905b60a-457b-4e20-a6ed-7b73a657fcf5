package com.uaepay.cmf.service.facade.result;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.time.Duration;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date RefundQueryResult.java v1.0  2019-12-12 20:15
 */
public class RefundQueryResult extends CommonResponse {
    private static final long serialVersionUID = -877960730038872864L;

    private Duration refundDuration;

    public Duration getRefundDuration() {
        return refundDuration;
    }

    public void setRefundDuration(Duration refundDuration) {
        this.refundDuration = refundDuration;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
