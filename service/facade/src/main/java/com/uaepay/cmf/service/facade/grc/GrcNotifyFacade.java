package com.uaepay.cmf.service.facade.grc;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import com.uaepay.cmf.service.facade.domain.grc.Query3dsRequest;
import com.uaepay.cmf.service.facade.domain.grc.Query3dsResponse;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date GrcNotifyFacade.java v1.0  2020-10-16 11:57
 */
public interface GrcNotifyFacade {

    /**
     * 通知3ds结果
     * @param notify3dsResult
     * @return
     */
    CommonResponse notify3dsResult(Notify3dsResult notify3dsResult);

    /**
     * 查询3ds结果
     * @param request
     * @return
     */
    Query3dsResponse query3dsResult(Query3dsRequest request);

}
