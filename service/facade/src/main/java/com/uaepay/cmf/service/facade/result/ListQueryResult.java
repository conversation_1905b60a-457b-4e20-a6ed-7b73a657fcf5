package com.uaepay.cmf.service.facade.result;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ListQueryResult.java v1.0
 */
public class ListQueryResult<T> extends CommonResponse {
    private static final long serialVersionUID = 239529115121726190L;

    private List<T> list;

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
