package com.uaepay.cmf.service.facade.domain.verify;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import com.uaepay.cmf.service.facade.domain.util.LogUtil;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 验证签名
 * </p>
 *
 * <AUTHOR>
 * @date VerifySignRequest.java v1.0 2019-11-14 16:48
 */
public class VerifySignRequest extends AbstractRequest {

    private static final long serialVersionUID = 579962000099400590L;
    /**
     * 渠道编号
     */
    @NotEmpty(message = "Channel code should not be empty!")
    private String channelCode;

    /**
     * 接口类型
     */
    @NotEmpty(message = "Api type should not be empty!")
    @Pattern(regexp = "^VS|AV|MRV$", message = "apiType should be VS/AV/MRV type")
    private String apiType;

    /**
     * 请求号
     */
    private String requestNo;

    /**
     * 机构订单号
     */
    private String instOrderNo;

    /**
     * 回调类型
     */
    @NotEmpty(message = "Call back type should not be empty!")
    @Pattern(regexp = "^page|server$", message = "callbackType should be one of page/server!")
    private String callbackType;

    /**
     * 请求头
     */
    private Map<String, List<String>> headerMap;

    /**
     * 验证参数
     */
    private Map<String, String> verifyParam;

    /**
     * 是否为异步参数，true-异步，false-同步
     */
    private boolean async;

    /**
     * 验证参数字符串
     */
    private String verifyParamStr;

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getApiType() {
        return apiType;
    }

    public void setApiType(String apiType) {
        this.apiType = apiType;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public String getCallbackType() {
        return callbackType;
    }

    public void setCallbackType(String callbackType) {
        this.callbackType = callbackType;
    }

    public Map<String, List<String>> getHeaderMap() {
        return headerMap;
    }

    public void setHeaderMap(Map<String, List<String>> headerMap) {
        this.headerMap = headerMap;
    }

    public Map<String, String> getVerifyParam() {
        return verifyParam;
    }

    public void setVerifyParam(Map<String, String> verifyParam) {
        this.verifyParam = verifyParam;
    }

    public boolean isAsync() {
        return async;
    }

    public void setAsync(boolean async) {
        this.async = async;
    }

    public String getVerifyParamStr() {
        return verifyParamStr;
    }

    public void setVerifyParamStr(String verifyParamStr) {
        this.verifyParamStr = verifyParamStr;
    }

    @Override
    public String toString() {
        ReflectionToStringBuilder.setDefaultStyle(ToStringStyle.SHORT_PREFIX_STYLE);
        return ReflectionToStringBuilder.toStringExclude(this, new String[]{"verifyParam"})
                +",dataMap=[" + LogUtil.toShortString(verifyParam) + "]";
    }
}
