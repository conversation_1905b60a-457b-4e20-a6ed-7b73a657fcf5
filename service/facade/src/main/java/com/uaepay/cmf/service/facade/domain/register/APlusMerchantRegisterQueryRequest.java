package com.uaepay.cmf.service.facade.domain.register;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version: APlusMerchantRegisterQueryRequest.class v1.0
 */
public class APlusMerchantRegisterQueryRequest extends AbstractRequest {
    private static final long serialVersionUID = 3507527869269316969L;

    /**
     * 原请求号
     */
    private String preRequestNo;

    public String getPreRequestNo() {
        return preRequestNo;
    }

    public void setPreRequestNo(String preRequestNo) {
        this.preRequestNo = preRequestNo;
    }
}
