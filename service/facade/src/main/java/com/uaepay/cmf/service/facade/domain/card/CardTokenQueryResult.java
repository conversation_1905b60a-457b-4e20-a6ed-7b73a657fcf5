package com.uaepay.cmf.service.facade.domain.card;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenQueryResult.java v1.0  2020-03-28 14:57
 */
public class CardTokenQueryResult extends CommonResponse {
    private static final long serialVersionUID = 4975957710638493876L;

    private String cardTokenId;
    /**
     * 会员卡id
     */
    private Long cardId;

    /**
     * 目标机构
     */
    private String instCode;

    /**
     * 渠道编号
     */
    private String channelCode;

    /**
     * 机构订单号
     */
    private String instOrderNo;

    /**
     * 支付订单号
     */
    private String paymentOrderNo;

    /**
     * 会员id
     */
    private String memberId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 持卡人
     */
    private String cardHolder;

    /**
     * 卡品牌
     */
    private String cardBrand;

    /**
     * 机构Token
     */
    private String instTokenId;

    /**
     * 卡类型- DC/CC
     */
    private String cardType;

    private String cardExpired;

    /**
     * 发卡行
     */
    private String issueBank;
    /**
     * 发卡行名称
     */
    private String issueBankName;

    /**
     * 扩展参数
     */
    private String extension;

    public String getIssueBank() {
        return issueBank;
    }

    public void setIssueBank(String issueBank) {
        this.issueBank = issueBank;
    }

    public String getIssueBankName() {
        return issueBankName;
    }

    public void setIssueBankName(String issueBankName) {
        this.issueBankName = issueBankName;
    }

    public String getCardTokenId() {
        return cardTokenId;
    }

    public void setCardTokenId(String cardTokenId) {
        this.cardTokenId = cardTokenId;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public String getPaymentOrderNo() {
        return paymentOrderNo;
    }

    public void setPaymentOrderNo(String paymentOrderNo) {
        this.paymentOrderNo = paymentOrderNo;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCardHolder() {
        return cardHolder;
    }

    public void setCardHolder(String cardHolder) {
        this.cardHolder = cardHolder;
    }

    public String getCardBrand() {
        return cardBrand;
    }

    public void setCardBrand(String cardBrand) {
        this.cardBrand = cardBrand;
    }

    public String getInstTokenId() {
        return instTokenId;
    }

    public void setInstTokenId(String instTokenId) {
        this.instTokenId = instTokenId;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardExpired() {
        return cardExpired;
    }

    public void setCardExpired(String cardExpired) {
        this.cardExpired = cardExpired;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public static CardTokenQueryResult buildFail(String message){
        CardTokenQueryResult result = new CardTokenQueryResult();
        result.setApplyStatus(ApplyStatusEnum.FAIL);
        result.setMessage(message);
        return result;
    }

    public static CardTokenQueryResult buildSuccess(){
        CardTokenQueryResult result = new CardTokenQueryResult();
        result.setApplyStatus(ApplyStatusEnum.SUCCESS);
        return result;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
