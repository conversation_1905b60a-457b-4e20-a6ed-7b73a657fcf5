package com.uaepay.cmf.service.facade.api;

import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.common.domain.OperationEnvironment;

/**
 * CMF 出款,入款,冲退门面请求
 *
 * <AUTHOR> won
 * @version $Id: FundRequestFacade.java, v 0.1 2011-3-21 下午04:43:09 sean won Exp $
 */
public interface FundRequestFacade {
    /**
     * 入款,出款申请
     *
     * @param request
     * @param environment
     * @return
     */
    CmfFundResult apply(CmfRequest request, OperationEnvironment environment);

    /**
     * 退款申请
     *
     * @param request     必须明确原入款PE订单号,充退金额，如果没有指定充退金额以原入款金额作为充退金额
     * @param environment
     * @return
     */
    CmfFundResult refund(CmfRequest request, OperationEnvironment environment);
}
