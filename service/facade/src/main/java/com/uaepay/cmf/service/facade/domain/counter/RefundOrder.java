package com.uaepay.cmf.service.facade.domain.counter;

import com.uaepay.cmf.service.facade.domain.query.SimpleOrder;
import com.uaepay.common.util.money.Money;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date RefundOrder.java v1.0  11/22/20 5:32 PM
 */
public class RefundOrder extends SimpleOrder {
    private static final long serialVersionUID = -7538271304346524252L;

    /**
     * 退款类型， B-退款，C-撤销
     */
    private String refundType;
    /**
     * 原机构订单号
     */
    private String originalOrderNo;
    /**
     * 原交易金额
     */
    private Money originalAmount;
    /**
     * 原交易时间
     */
    private Date gmtOriginal;

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public Money getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(Money originalAmount) {
        this.originalAmount = originalAmount;
    }

    public Date getGmtOriginal() {
        return gmtOriginal;
    }

    public void setGmtOriginal(Date gmtOriginal) {
        this.gmtOriginal = gmtOriginal;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
