package com.uaepay.cmf.service.facade.domain.fundout;

import com.uaepay.schema.cmf.enums.InstOrderStatus;

import java.io.Serializable;

public class FundOutCancelRequest implements Serializable {

    private static final long serialVersionUID = 2560891300879798843L;

    /**
     * 机构订单号
     */
    private String instOrderNo;
    /**
     * 订单状态
     */
    protected InstOrderStatus instOrderStatus;
    /**
     * 机构序列号
     */
    protected String instSeqNo;

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public InstOrderStatus getInstOrderStatus() {
        return instOrderStatus;
    }

    public void setInstOrderStatus(InstOrderStatus instOrderStatus) {
        this.instOrderStatus = instOrderStatus;
    }

    public String getInstSeqNo() {
        return instSeqNo;
    }

    public void setInstSeqNo(String instSeqNo) {
        this.instSeqNo = instSeqNo;
    }

    @Override
    public String toString() {
        return "FundOutCancelRequest{" +
                "instOrderNo='" + instOrderNo + '\'' +
                ", instOrderStatus=" + instOrderStatus +
                ", instSeqNo='" + instSeqNo + '\'' +
                '}';
    }
}
