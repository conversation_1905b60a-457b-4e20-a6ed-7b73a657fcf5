package com.uaepay.cmf.service.facade.domain.control.pos;

public enum PosReversalResStatus {
    IN_PROCESSING("I", "processing"),
    SUCCESS("S", "success"),
    FAILED("F", "failed"),
    UNABLE("U", "unable"),
    REPEATED("R", "repeated");
    private final String code;
    private final String message;

    PosReversalResStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据code获取ENUM
     *
     * @param code
     * @return
     */
    public static PosReversalResStatus getByCode(String code) {
        for (PosReversalResStatus resultCode : PosReversalResStatus.values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return null;
    }
}
