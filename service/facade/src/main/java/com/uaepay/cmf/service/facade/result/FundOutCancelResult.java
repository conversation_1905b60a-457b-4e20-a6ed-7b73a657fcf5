package com.uaepay.cmf.service.facade.result;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;

public class FundOutCancelResult extends CommonResponse {

    private static final long serialVersionUID = -4517535977971036340L;

    private String returnCode;

    private String returnMsg;

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMsg() {
        return returnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    @Override
    public String toString() {
        return "FundOutCancelResult{" +
                "returnCode='" + returnCode + '\'' +
                ", returnMsg='" + returnMsg + '\'' +
                '}';
    }
}
