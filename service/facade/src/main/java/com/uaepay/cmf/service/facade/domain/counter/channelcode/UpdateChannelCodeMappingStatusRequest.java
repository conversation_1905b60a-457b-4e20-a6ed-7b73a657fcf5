package com.uaepay.cmf.service.facade.domain.counter.channelcode;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;


public class UpdateChannelCodeMappingStatusRequest extends AbstractRequest {
    private static final long serialVersionUID = 4947319883763470860L;
    /**
     * Rule ID
     */
    private Long id;

    /**
     * New status (Y-enabled, N-disabled)
     */
    private String status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}