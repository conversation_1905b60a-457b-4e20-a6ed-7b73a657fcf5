package com.uaepay.cmf.service.facade.api;

import com.uaepay.cmf.common.domain.ChannelCommonResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelNotifyResult;

/**
 * <p>
 * 渠道通知CMF--CMF接收通知处理接口
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: ChannelNotifyFacade.java, v 0.1 2012-2-7 下午09:13:28 sean won Exp $
 */
public interface ChannelReceiveFacade {
    /**
     * 支付结果通知
     *
     * @param result
     * @return
     */
    ChannelNotifyResult fundNotify(ChannelFundResult result);

    /**
     * 一般处理结果通知
     *
     * @param result
     * @return
     */
    ChannelNotifyResult notify(ChannelCommonResult result);

}
