package com.uaepay.cmf.service.facade.domain.control;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CmfFileResponse.java v1.0  1/7/21 4:42 PM
 */
public class CmfFileResponse extends CommonResponse {
    private static final long serialVersionUID = -4303145921648376278L;

    /**
     * 清算请求号
     */
    private String requestNo;
    /**
     * 文件列表
     */
    private List<String> fileList;
    /**
     * 扩展字段 json格式map
     */
    private Map<String, String> extension;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public List<String> getFileList() {
        return fileList;
    }

    public void setFileList(List<String> fileList) {
        this.fileList = fileList;
    }

    public Map<String, String> getExtension() {
        return extension;
    }

    public void setExtension(Map<String, String> extension) {
        this.extension = extension;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
