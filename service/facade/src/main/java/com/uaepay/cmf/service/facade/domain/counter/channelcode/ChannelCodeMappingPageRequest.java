package com.uaepay.cmf.service.facade.domain.counter.channelcode;

import com.uaepay.biz.common.util.QueryBase;

/**
 * Channel code mapping rule query request
 */
public class ChannelCodeMappingPageRequest extends QueryBase {
    private static final long serialVersionUID = 3320535699380163006L;
    /**
     * Original channel code
     */
    private String oldChannelCode;

    /**
     * New channel code
     */
    private String newChannelCode;

    /**
     * Rule status (Y-enabled, N-disabled)
     */
    private String status;

    public String getOldChannelCode() {
        return oldChannelCode;
    }

    public void setOldChannelCode(String oldChannelCode) {
        this.oldChannelCode = oldChannelCode;
    }

    public String getNewChannelCode() {
        return newChannelCode;
    }

    public void setNewChannelCode(String newChannelCode) {
        this.newChannelCode = newChannelCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}