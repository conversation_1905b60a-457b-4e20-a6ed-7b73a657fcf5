package com.uaepay.cmf.service.facade.domain.control.config;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.payment.common.v2.enums.PayMode;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.validation.constraints.NotEmpty;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ChannelConfigQueryRequest.java v1.0
 */
public class ChannelConfigQueryRequest extends AbstractRequest {
    private static final long serialVersionUID = 3110042821535339833L;

    /**
     * 请求号
     */
    @NotEmpty(message = "Request no cannot be empty")
    private String requestNo;
    /**
     * 目标机构代码
     */
    @NotEmpty(message = "Inst code cannot be empty")
    private String instCode;
    /**
     * 支付模式，可空
     */
    private PayMode payMode = PayMode.TOKENPAY;


    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public PayMode getPayMode() {
        return payMode;
    }

    public void setPayMode(PayMode payMode) {
        this.payMode = payMode;
    }


    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.NO_FIELD_NAMES_STYLE);
    }
}