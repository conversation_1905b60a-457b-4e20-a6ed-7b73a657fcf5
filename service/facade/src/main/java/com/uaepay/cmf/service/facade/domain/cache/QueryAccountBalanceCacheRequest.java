package com.uaepay.cmf.service.facade.domain.cache;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 02/09/2024 15:00
 */
public class QueryAccountBalanceCacheRequest extends AbstractRequest {

    private static final long serialVersionUID = -4108881422794624741L;

    @NotBlank(message = "accountNo is not null")
    private String accountNo;

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }
}
