package com.uaepay.cmf.service.facade.domain.util;

import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date LogUtil.java v1.0
 */
public class LogUtil {

    private LogUtil(){

    }

    public static String toShortString(Map<String,String> extMap){
        if(extMap==null || extMap.size()==0){
            return "";
        }
        StringBuilder accum = new StringBuilder();
        for(Map.Entry<String,String> entry:extMap.entrySet()){
            accum.append(entry.getKey()).append("=").append("x*").append(StringUtils.length(entry.getValue())).append(",");
        }
        return accum.toString();
    }
}
