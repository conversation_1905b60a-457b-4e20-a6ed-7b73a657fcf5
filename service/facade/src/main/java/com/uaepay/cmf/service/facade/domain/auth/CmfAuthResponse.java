package com.uaepay.cmf.service.facade.domain.auth;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.service.facade.domain.util.LogUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CmfAuthResponse.java v1.0
 */
public class CmfAuthResponse extends CommonResponse {
    private static final long serialVersionUID = -7511444131604942570L;

    private String fundChannelCode;

    private String instOrderNo;

    private Map<String, String> dataMap;

    public String getFundChannelCode() {
        return fundChannelCode;
    }

    public void setFundChannelCode(String fundChannelCode) {
        this.fundChannelCode = fundChannelCode;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public Map<String, String> getDataMap() {
        return dataMap;
    }

    public void setDataMap(Map<String, String> dataMap) {
        this.dataMap = dataMap;
    }

    @Override
    public String toString() {
        ReflectionToStringBuilder.setDefaultStyle(ToStringStyle.SHORT_PREFIX_STYLE);
        return ReflectionToStringBuilder.toStringExclude(this, new String[]{"dataMap"})
                +",dataMap=[" + LogUtil.toShortString(dataMap) + "]";
    }

}
