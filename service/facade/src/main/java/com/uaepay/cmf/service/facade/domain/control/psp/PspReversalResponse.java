package com.uaepay.cmf.service.facade.domain.control.psp;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date PspReversalResponse.java v1.0
 */
public class PspReversalResponse extends CommonResponse {
    private static final long serialVersionUID = -3744515966151640715L;

    /**
     * 资金源id
     */
    private String fundsChannel;
    /**
     * cmf机构订单号
     */
    private String instOrderNo;
    /**
     * 状态
     */
    private String status;

    public String getFundsChannel() {
        return fundsChannel;
    }

    public void setFundsChannel(String fundsChannel) {
        this.fundsChannel = fundsChannel;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
