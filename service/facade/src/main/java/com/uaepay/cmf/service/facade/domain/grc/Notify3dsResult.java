package com.uaepay.cmf.service.facade.domain.grc;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <p>3ds结果通知</p>
 *
 * <AUTHOR>
 * @date ThreeDSNotify.java v1.0  2020-10-16 10:51
 */
public class Notify3dsResult implements Serializable {
    private static final long serialVersionUID = -834521531739856539L;
    /**
     * 结果id
     */
    private Long resultId;

    /**
     * 交易订单号
     */
    private String tradeOrderNo;

    /**
     * 支付订单号
     */
    private String paymentOrderNo;

    /**
     * 渠道编号
     */
    @NotNull(message = "Channel code should not be null!")
    private String channelCode;

    /**
     * 渠道订单号
     */
    @NotNull(message = "Inst order no should not be null!")
    private String instOrderNo;

    /**
     * Cmf请求时间
     */
    private Date gmtCmfRequest;

    /**
     * 请求bank时间
     */
    private Date gmtBankRequest;

    /**
     * bank响应时间
     */
    @NotNull(message = "Bank Response time should not be null!")
    private Date gmtBankResponse;

    /**
     * 持卡人姓名
     */
    private String cardHolder;

    /**
     * 卡号 - 加密
     */
    private String cardNo;

    /**
     * 国家编码 - 加密
     */
    private String countryCode;

    /**
     * 卡号，掩码
     */
    private String cardNoMask;

    /**
     * 发卡行
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 卡类型
     */
    private String cardType;

    /**
     * 卡品牌
     */
    private String cardBrand;

    /**
     * 卡有效期
     */
    private String cardExpire;

    /**
     * eci结果
     */
    @NotNull(message = "Eci should not be null!")
    private String eci;

    /**
     * 认证结果
     */
    private String identityResult;

    /**
     * 认证结果描述
     */
    private String identityResultDesc;

    /**
     * 扩展参数
     */
    private Map<String, String> extMap;

    public Long getResultId() {
        return resultId;
    }

    public void setResultId(Long resultId) {
        this.resultId = resultId;
    }

    public String getTradeOrderNo() {
        return tradeOrderNo;
    }

    public void setTradeOrderNo(String tradeOrderNo) {
        this.tradeOrderNo = tradeOrderNo;
    }

    public String getPaymentOrderNo() {
        return paymentOrderNo;
    }

    public void setPaymentOrderNo(String paymentOrderNo) {
        this.paymentOrderNo = paymentOrderNo;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public Date getGmtCmfRequest() {
        return gmtCmfRequest;
    }

    public void setGmtCmfRequest(Date gmtCmfRequest) {
        this.gmtCmfRequest = gmtCmfRequest;
    }

    public Date getGmtBankRequest() {
        return gmtBankRequest;
    }

    public void setGmtBankRequest(Date gmtBankRequest) {
        this.gmtBankRequest = gmtBankRequest;
    }

    public Date getGmtBankResponse() {
        return gmtBankResponse;
    }

    public void setGmtBankResponse(Date gmtBankResponse) {
        this.gmtBankResponse = gmtBankResponse;
    }

    public String getCardHolder() {
        return cardHolder;
    }

    public void setCardHolder(String cardHolder) {
        this.cardHolder = cardHolder;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCardNoMask() {
        return cardNoMask;
    }

    public void setCardNoMask(String cardNoMask) {
        this.cardNoMask = cardNoMask;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardBrand() {
        return cardBrand;
    }

    public void setCardBrand(String cardBrand) {
        this.cardBrand = cardBrand;
    }

    public String getCardExpire() {
        return cardExpire;
    }

    public void setCardExpire(String cardExpire) {
        this.cardExpire = cardExpire;
    }

    public String getEci() {
        return eci;
    }

    public void setEci(String eci) {
        this.eci = eci;
    }

    public String getIdentityResult() {
        return identityResult;
    }

    public void setIdentityResult(String identityResult) {
        this.identityResult = identityResult;
    }

    public String getIdentityResultDesc() {
        return identityResultDesc;
    }

    public void setIdentityResultDesc(String identityResultDesc) {
        this.identityResultDesc = identityResultDesc;
    }

    public Map<String, String> getExtMap() {
        return extMap;
    }

    public void setExtMap(Map<String, String> extMap) {
        this.extMap = extMap;
    }

    @Override
    public String toString() {
        ReflectionToStringBuilder.setDefaultStyle(ToStringStyle.SHORT_PREFIX_STYLE);
        return ReflectionToStringBuilder.toStringExclude(this, new String[]{"cardNo", "cardHolder", "cardExpire", "iban"});
    }

}
