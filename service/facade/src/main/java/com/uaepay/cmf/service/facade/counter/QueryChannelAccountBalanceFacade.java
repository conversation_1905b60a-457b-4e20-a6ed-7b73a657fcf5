package com.uaepay.cmf.service.facade.counter;

import com.uaepay.cmf.service.facade.domain.counter.QueryAccountBalanceRequest;
import com.uaepay.cmf.service.facade.domain.counter.QueryAccountBalanceResponse;

public interface QueryChannelAccountBalanceFacade {

    /**
     *
     * @param request
     * @return
     */
    QueryAccountBalanceResponse queryAccountBalance(QueryAccountBalanceRequest request);
}
