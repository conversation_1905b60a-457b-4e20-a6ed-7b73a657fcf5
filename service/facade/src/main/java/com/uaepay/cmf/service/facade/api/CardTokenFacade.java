package com.uaepay.cmf.service.facade.api;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateRequest;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateResult;
import com.uaepay.cmf.service.facade.domain.card.CardTokenQueryResult;
import com.uaepay.cmf.service.facade.domain.card.CardTokenUpdateRequest;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenFacade.java v1.0  2020-03-27 21:17
 */
public interface CardTokenFacade {

    /**
     * 创建cardToken
     *
     * @param request
     * @return
     */
    CardTokenCreateResult create(CardTokenCreateRequest request);

    /**
     * 更新cardToken
     *
     * @param request
     * @return
     */
    CommonResponse update(CardTokenUpdateRequest request);


    /**
     * 查询卡token
     *
     * @param cardTokenId
     * @return
     */
    CardTokenQueryResult query(String cardTokenId);


}
