package com.uaepay.cmf.service.facade.api;

import com.uaepay.basis.beacon.service.facade.domain.response.PageResponse;
import com.uaepay.cmf.service.facade.domain.query.*;
import com.uaepay.cmf.service.facade.domain.query.order.BatchOrderVO;
import com.uaepay.cmf.service.facade.domain.query.order.ControlOrderVO;
import com.uaepay.cmf.service.facade.result.PkQueryResult;

/**
 * <p>
 * 订单查询
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: OrderQueryFacade.java, v 0.1 2011-4-22 下午04:51:44 sean won Exp $
 */
public interface OrderQueryFacade {

    /**
     * 分页查询订单
     * @param request
     * @return
     */
    PageResponse<SimpleOrder> queryOrdersByPage(OrderPageQueryRequest request);

    /**
     * 根据订单号查询订单
     * @param request
     * @return
     */
    PkQueryResult<SimpleOrder> queryByOrderNo(OrderNoQueryRequest request);

    /**
     * 请求号查询控制订单
     * @param request
     * @return
     */
    PkQueryResult<ControlOrderVO> queryControlOrder(PkQueryRequest<String> request);

    /**
     *
      * @param request
     * @return
     */
    PageResponse<SimpleOrder> queryByInstOrderNoList(InstOrderQueryRequest request);

    /**
     * 分页查询批量订单
     * @param request
     * @return
     */
    PageResponse<BatchOrderVO> queryBatchOrder(BatchOrderQueryRequest request);
}
