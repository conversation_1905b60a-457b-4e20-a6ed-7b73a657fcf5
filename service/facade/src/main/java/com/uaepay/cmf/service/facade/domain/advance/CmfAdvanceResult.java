package com.uaepay.cmf.service.facade.domain.advance;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import com.uaepay.common.util.money.Money;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CmfAdvanceResult.java v1.0
 */
public class CmfAdvanceResult extends CommonResponse {
    private static final long serialVersionUID = 433807617171210545L;

    /**
     * 渠道编码
     */
    private String fundChannelCode;
    /**
     * 机构订单号
     */
    private String instOrderNo;
    /**
     * 金额
     */
    private Money amount;
    /**
     * 结果码
     */
    private CmfFundResultCode resultCode;
    /**
     * 扩展参数
     */
    private Map<String, String> extension;

    public String getFundChannelCode() {
        return fundChannelCode;
    }

    public void setFundChannelCode(String fundChannelCode) {
        this.fundChannelCode = fundChannelCode;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public CmfFundResultCode getResultCode() {
        return resultCode;
    }

    public void setResultCode(CmfFundResultCode resultCode) {
        this.resultCode = resultCode;
    }

    public Map<String, String> getExtension() {
        return extension;
    }

    public void setExtension(Map<String, String> extension) {
        this.extension = extension;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
