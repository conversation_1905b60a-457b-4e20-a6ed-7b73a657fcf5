package com.uaepay.cmf.service.facade.api;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.common.domain.base.ReturnInfo;
import com.uaepay.cmf.service.facade.domain.fundout.FundOutCancelRequest;
import com.uaepay.cmf.service.facade.domain.fundschannel.OrderResult;
import com.uaepay.cmf.service.facade.result.FundOutCancelResult;

public interface ResultNotifyFacade {

    /**
     * 消息通知
     * @param result
     * @return
     */
    ReturnInfo notify(OrderResult result);

    FundOutCancelResult fundOutCancelNotice(FundOutCancelRequest request);

}
