package com.uaepay.cmf.service.facade.domain.register;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version: APlusMerchantRegisterQueryResponse.class v1.0
 */
public class APlusMerchantRegisterQueryResponse extends CommonResponse {
    private static final long serialVersionUID = -643902889278167010L;

    private String merchantId;

    private String storeId;

    /**
     * PENDING: indicates that the MPP has not returned the registration result.
     * CANCELLED: indicates that the registration is canceled by the MPP.
     * APPROVED: indicates that the registration is approved by the MPP.
     * REJECTED: indicates that the registration is rejected by the MPP.
     */
    private String registerStatus;

    private String registerMessage;

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getRegisterStatus() {
        return registerStatus;
    }

    public void setRegisterStatus(String registerStatus) {
        this.registerStatus = registerStatus;
    }

    public String getRegisterMessage() {
        return registerMessage;
    }

    public void setRegisterMessage(String registerMessage) {
        this.registerMessage = registerMessage;
    }
}
