package com.uaepay.cmf.service.facade.domain.fundout;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;

/**
 * <AUTHOR>
 * @date 25/12/2023 14:46
 */
public class QueryFundoutBeneficiaryInfoResponse extends CommonResponse {
    private static final long serialVersionUID = -1481976821437113654L;

    private String memberId;

    private String bankCode;

    /**
     * CC-Credit Card
     * DC-Debit Card
     */
    private String cardType;

    private String targetInst;

    /**
     * encrypted string
     */
    private String iban;

    /**
     * encrypted string
     */
    private String accountName;

    /**
     * encrypted string
     */
    private String beneficiaryAddress;


    /**
     * 目前默认个人提现类型 220401
     */
    private String bizProductCode;


    /**
     * default balance - 14
     */
    private String payChannel;

    /**
     * default anonymous
     */
    private String payeeId;

    /**
     * company-B
     * personal-C
     */
    private String companyOrPersonal;

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getTargetInst() {
        return targetInst;
    }

    public void setTargetInst(String targetInst) {
        this.targetInst = targetInst;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBeneficiaryAddress() {
        return beneficiaryAddress;
    }

    public void setBeneficiaryAddress(String beneficiaryAddress) {
        this.beneficiaryAddress = beneficiaryAddress;
    }

    public String getBizProductCode() {
        return bizProductCode;
    }

    public void setBizProductCode(String bizProductCode) {
        this.bizProductCode = bizProductCode;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getPayeeId() {
        return payeeId;
    }

    public void setPayeeId(String payeeId) {
        this.payeeId = payeeId;
    }

    public String getCompanyOrPersonal() {
        return companyOrPersonal;
    }

    public void setCompanyOrPersonal(String companyOrPersonal) {
        this.companyOrPersonal = companyOrPersonal;
    }
}
