package com.uaepay.cmf.service.facade.domain.card;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.validation.constraints.NotNull;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenUpdateRequest.java v1.0  2020-03-28 14:54
 */
public class CardTokenUpdateRequest extends CardTokenCreateRequest{
    private static final long serialVersionUID = -4529645784951214091L;

    @NotNull(message = "Card token id should not be null!")
    private String cardTokenId;

    public String getCardTokenId() {
        return cardTokenId;
    }

    public void setCardTokenId(String cardTokenId) {
        this.cardTokenId = cardTokenId;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
