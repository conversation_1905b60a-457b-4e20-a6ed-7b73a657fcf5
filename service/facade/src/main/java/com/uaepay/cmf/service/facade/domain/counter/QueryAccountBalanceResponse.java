package com.uaepay.cmf.service.facade.domain.counter;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.common.util.money.Money;

/**
 * <AUTHOR>
 * @date 27/08/2024 15:15
 */
public class QueryAccountBalanceResponse extends CommonResponse {

    private static final long serialVersionUID = 3535497087889300582L;

    private Money accountBalance;

    public Money getAccountBalance() {
        return accountBalance;
    }

    public void setAccountBalance(Money accountBalance) {
        this.accountBalance = accountBalance;
    }
}
