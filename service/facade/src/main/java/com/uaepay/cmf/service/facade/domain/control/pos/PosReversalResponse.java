package com.uaepay.cmf.service.facade.domain.control.pos;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date PspReversalResponse.java v1.0
 */
public class PosReversalResponse extends CommonResponse {
    private static final long serialVersionUID = -3744515966151640715L;

    /**
     * fund channel code
     */
    private String fundsChannel;
    /**
     * control order No
     */
    private String instOrderNo;
    /**
     * status
     * <p>
     * I-processing-Retryable（creates a control order and initiates a reversal to ni）
     * S-success-final (control order is existed and successful)
     * F-failed-final (control order is existed and failed)
     * U-unable-Retryable (control order is awaiting or preInstOrder status is S or F,communicationStatus is awaiting)
     * R-repeated-Retryable (can't get redis lock)
     */
    private PosReversalResStatus status;

    public String getFundsChannel() {
        return fundsChannel;
    }

    public void setFundsChannel(String fundsChannel) {
        this.fundsChannel = fundsChannel;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public PosReversalResStatus getStatus() {
        return status;
    }

    public void setStatus(PosReversalResStatus status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
