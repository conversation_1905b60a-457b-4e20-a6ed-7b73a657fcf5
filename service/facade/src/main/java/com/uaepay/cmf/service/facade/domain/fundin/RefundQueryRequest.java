package com.uaepay.cmf.service.facade.domain.fundin;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 充退查询请求
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: RefundQueryRequest.java, v 0.1 2010-12-24 上午10:42:56 sean won Exp $
 */
public class RefundQueryRequest implements Serializable {

    private static final long serialVersionUID = 4635182542840427890L;

    /**
     * 原入款订单号。非空
     */
    private String orgiFundinOrderNo;
    /**
     * 原入款结算ID
     */
    private String orgiSettlementId;
    /**
     * 充退金额。非空
     */
    private BigDecimal amount;
    /**
     * 00为全部退款，01为部分退款，如果为部分退款但退款金额和支付金额一致，则不会进行退款。非空
     */
    private String refundType;

    public String getOrgiFundinOrderNo() {
        return orgiFundinOrderNo;
    }

    public void setOrgiFundinOrderNo(String orgiFundinOrderNo) {
        this.orgiFundinOrderNo = orgiFundinOrderNo;
    }

    public String getOrgiSettlementId() {
        return orgiSettlementId;
    }

    public void setOrgiSettlementId(String orgiSettlementId) {
        this.orgiSettlementId = orgiSettlementId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
