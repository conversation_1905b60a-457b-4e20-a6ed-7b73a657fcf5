package com.uaepay.cmf.service.facade.api;

import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignResult;
import com.uaepay.cmf.service.facade.result.CmfFundResult;

/**
 * <p>验证签名接口</p>
 *
 * <AUTHOR>
 * @date VerifyFacade.java v1.0  2019-11-14 16:38
 */
public interface VerifySignFacade {


    /**
     * 验证签名方法
     *
     * 此方法不要删，信贷的手机分期业务会调用这个接口
     *
     * @param request
     * @return
     */
    CmfFundResult verifySign(VerifySignRequest request);

    /**
     * 验证接口-可能是支付验签，也可能是签约验签
     * @param request
     * @return
     */
    VerifySignResult verify(VerifySignRequest request);

}
