package com.uaepay.cmf.service.facade.counter;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.domain.response.ObjectQueryResponse;
import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.service.facade.domain.counter.*;
import com.uaepay.cmf.service.facade.domain.fundout.FundOutRetryRequest;
import com.uaepay.cmf.service.facade.domain.fundout.UpdateBookingTimeRequest;

import java.util.List;

/**
 * <p>
 * CMF机构订单处理服务.
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: BankFileProcessServiceImpl.java, v 0.1 2009-10-27 上午10:56:35 sean won Exp $
 */
public interface InstOrderProcessFacade {

    /**
     * 查询订单
     * @param instOrderNo
     * @return
     */
    InstOrderVO getInstOrder(String instOrderNo);


    /**
     * 更具支付流水号获取机构订单
     * @param paymentSeqNo 支付流水号
     * @return 机构订单
     */
    ObjectQueryResponse<InstOrderVO> getInstOrderByPaymentSeqNo(String paymentSeqNo);

    /**
     * 查询出款记录.
     * <p>
     * USED
     *
     * @param query
     * @return FixMe:wait abolish
     */
    @Deprecated
    List<InstOrderVO> getInstOrders(InstOrderQuery query);

    /**
     * 重新通知PE
     * USED
     *
     * @param cmfSeqNo
     * @return
     */
    BaseResult notifyPE(String cmfSeqNo);

    /**
     * 查询并修改订单结果
     * <p>
     * USED
     *
     * @param instOrderNo
     * @return
     */
    QueryOrderResult queryInstOrderResult(String instOrderNo);

    /**
     * 更新预计提交时间
     * @param request
     * @return
     */
    CommonResponse updateGmtBooking(UpdateBookingTimeRequest request);

    /**
     * 出款重试
     * @param request
     * @return
     */
    CommonResponse retry(FundOutRetryRequest request);

    QueryRefundResponse queryRefundListByInstOrderNo(QueryRefundRequest request);

}
