package com.uaepay.cmf.service.facade.domain.query;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.validation.constraints.NotNull;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date PkQueryRequest.java v1.0
 */
public class PkQueryRequest<T> extends AbstractRequest {

    private static final long serialVersionUID = 3282079812723221443L;
    @NotNull(message = "Pk should not be null!")
    private T pk;

    public T getPk() {
        return pk;
    }

    public void setPk(T pk) {
        this.pk = pk;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}

