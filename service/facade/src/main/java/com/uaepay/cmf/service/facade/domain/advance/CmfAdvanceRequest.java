package com.uaepay.cmf.service.facade.domain.advance;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotEmpty;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CmfAdvanceRequest.java v1.0
 */
public class CmfAdvanceRequest extends AbstractRequest {
    private static final long serialVersionUID = -7507069198488941873L;

    @NotEmpty(message = "Inst order token should not be empty!")
    private String instOrderToken;

    /**
     * 若存在instOrderNo，则优先取instOrderNo
     */
    private String instOrderNo;

    public String getInstOrderToken() {
        return instOrderToken;
    }

    public void setInstOrderToken(String instOrderToken) {
        this.instOrderToken = instOrderToken;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
