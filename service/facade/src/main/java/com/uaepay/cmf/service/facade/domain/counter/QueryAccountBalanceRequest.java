package com.uaepay.cmf.service.facade.domain.counter;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import com.uaepay.common.util.money.Money;

/**
 * <AUTHOR>
 * @date 27/08/2024 15:15
 */
public class QueryAccountBalanceRequest extends AbstractRequest {

    private static final long serialVersionUID = 6164090248354615337L;

    private String instCode;

    private String channelCode;

    private String accountNo;

    private String accountType;

    private Money threshold;

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public Money getThreshold() {
        return threshold;
    }

    public void setThreshold(Money threshold) {
        this.threshold = threshold;
    }
}
