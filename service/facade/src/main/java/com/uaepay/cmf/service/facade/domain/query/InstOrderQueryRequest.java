package com.uaepay.cmf.service.facade.domain.query;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date InstOrderQueryRequest.java v1.0
 */
public class InstOrderQueryRequest extends AbstractRequest {
    private static final long serialVersionUID = -347322505024279769L;

    @NotNull(message = "Payment Order No should not be null!")
    @Size(max=1000, message="Inst Order No List size exceed 1000!")
    private List<String> instOrderNoList;

    public List<String> getInstOrderNoList() {
        return instOrderNoList;
    }

    public void setInstOrderNoList(List<String> instOrderNoList) {
        this.instOrderNoList = instOrderNoList;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
