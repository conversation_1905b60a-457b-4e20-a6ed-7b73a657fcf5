package com.uaepay.cmf.service.facade.domain.grc;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date Query3dsResponse.java v1.0  2020-10-16 12:03
 */
public class Query3dsResponse extends CommonResponse {
    private static final long serialVersionUID = -7463713885430382532L;

    private Notify3dsResult result;

    public Notify3dsResult getResult() {
        return result;
    }

    public void setResult(Notify3dsResult result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
