package com.uaepay.cmf.service.facade.domain.fundout;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date UpdateBookingTimeRequest.java v1.0
 */
public class UpdateBookingTimeRequest extends AbstractRequest {
    private static final long serialVersionUID = 5909439235022855852L;

    /**
     * 机构订单号
     */
    @NotEmpty(message = "Inst order no should not be empty!")
    private String instOrderNo;

    /**
     * 预计提交时间不能为空
     */
    @NotNull(message = "Booking submit time should not be null!")
    private Date gmtBookingSubmit;

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public Date getGmtBookingSubmit() {
        return gmtBookingSubmit;
    }

    public void setGmtBookingSubmit(Date gmtBookingSubmit) {
        this.gmtBookingSubmit = gmtBookingSubmit;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
