package com.uaepay.cmf.service.facade.domain.fundin;

import com.uaepay.common.util.money.Money;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SubscriptionChannelQueryRequest implements Serializable {

    private static final long serialVersionUID = 2689721356487957100L;

    /**
     * 金额
     */
    private Money amount;

    /**
     * 目标机构
     */
    private String instCode;

    /**
     * 支付方式
     */
    private String payMode;

    /**
     * memberId
     */
    private String memberId;

    /**
     * 卡组织 VISA MASTERCARD
     */
    private String cardBrand;

    /**
     * 业务产品码
     */
    private String bizProductCode;

    /**
     * 卡类型， DC/CC/GC
     */
    private String cardType;

    /**
     * 对公对私
     */
    private String companyOrPersonal;

    /**
     * 商户号
     */
    private String merchantId;

    /**
     * 已签约渠道所属机构
     */
    private List<String> signChannels;

    /**
     * 扩展参数
     */
    private final Map<String, String> extension = new HashMap<>();

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getCompanyOrPersonal() {
        return companyOrPersonal;
    }

    public void setCompanyOrPersonal(String companyOrPersonal) {
        this.companyOrPersonal = companyOrPersonal;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getCardBrand() {
        return cardBrand;
    }

    public void setCardBrand(String cardBrand) {
        this.cardBrand = cardBrand;
    }

    public String getBizProductCode() {
        return bizProductCode;
    }

    public void setBizProductCode(String bizProductCode) {
        this.bizProductCode = bizProductCode;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public List<String> getSignChannels() {
        return signChannels;
    }

    public void setSignChannels(List<String> signChannels) {
        this.signChannels = signChannels;
    }

    public Map<String, String> getExtension() {
        return extension;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
