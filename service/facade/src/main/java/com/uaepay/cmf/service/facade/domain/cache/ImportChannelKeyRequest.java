package com.uaepay.cmf.service.facade.domain.cache;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 08/03/2024 09:30
 */

public class ImportChannelKeyRequest extends AbstractRequest {

    private static final long serialVersionUID = -1203754844130459976L;

    @NotBlank
    private String keyName;

    @NotBlank
    private String keyValue;

    private String memo;

    public String getKeyName() {
        return keyName;
    }

    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }

    public String getKeyValue() {
        return keyValue;
    }

    public void setKeyValue(String keyValue) {
        this.keyValue = keyValue;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
