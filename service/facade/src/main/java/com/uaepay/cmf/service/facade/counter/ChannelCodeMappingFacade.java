package com.uaepay.cmf.service.facade.counter;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;

import com.uaepay.basis.beacon.service.facade.domain.response.PageResponse;
import com.uaepay.cmf.service.facade.domain.counter.channelcode.*;

/**
 * Channel code mapping management facade
 */
public interface ChannelCodeMappingFacade {

    /**
     * Page query channel code mapping rules
     *
     * @param request query parameters
     * @return paged mapping rules
     */
    PageResponse<ChannelCodeMappingPageResponse> pageQuery(ChannelCodeMappingPageRequest request);

    /**
     * Create new mapping rule
     *
     * @param request mapping rule details
     * @return operation result
     */
    CommonResponse create(CreateChannelCodeMappingRequest request);

    /**
     * Update existing mapping rule
     *
     * @param request mapping rule details
     * @return operation result
     */
    CommonResponse update(UpdateChannelCodeMappingRequest request);

    /**
     * Update mapping rule status
     *
     * @param request status update details
     * @return operation result
     */
    CommonResponse updateStatus(UpdateChannelCodeMappingStatusRequest request);
} 