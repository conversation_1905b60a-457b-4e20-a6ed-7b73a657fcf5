package com.uaepay.cmf.service.facade.domain.control;

import com.uaepay.cmf.service.facade.domain.CmfCommonResultCode;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.Kvp;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 控制类指令返回结果
 *
 * <AUTHOR> won
 */
public class CmfControlResult implements Serializable {
    private static final long serialVersionUID = 4622849336530381868L;

    /**
     * 请求号
     */
    private String requestNo;
    /**
     * 结果编码
     */
    private CmfCommonResultCode resultCode;
    /**
     * 资金源id
     */
    private String fundsChannel;
    /**
     * cmf机构订单号
     */
    private String instOrderNo;
    /**
     * 扩展信息
     */
    private Extension extension;
    /**
     * 机构统一返回码
     */
    private String instResultCode;
    /**
     * 返回信息
     */
    private String returnMessage;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public CmfCommonResultCode getResultCode() {
        return resultCode;
    }

    public void setResultCode(CmfCommonResultCode resultCode) {
        this.resultCode = resultCode;
    }

    public String getFundsChannel() {
        return fundsChannel;
    }

    public void setFundsChannel(String fundsChannel) {
        this.fundsChannel = fundsChannel;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public Extension getExtension() {
        return extension;
    }

    public void setExtension(Extension extension) {
        this.extension = extension;
    }

    public String getInstResultCode() {
        return instResultCode;
    }

    public void setInstResultCode(String instResultCode) {
        this.instResultCode = instResultCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE) + "扩展信息"
                + toStringExtension();
    }

    protected String toStringExtension() {
        if (extension == null || extension.getEntryList() == null) {
            return null;
        }
        StringBuilder builder = new StringBuilder();
        for (Kvp kvp : extension.getEntryList()) {
            builder.append(kvp.getKey()).append("=").append(kvp.getValue()).append(",");
        }
        return builder.toString();
    }

}
