package com.uaepay.cmf.service.facade.enums;

/**
 * <p>归档状态--枚举</p>
 * <p>
 * CMF归档文件的状态.
 *
 * <AUTHOR> won
 * @version $Id: ArchiveStatusEnum.java, v 0.1 2010-12-27 下午12:50:45 sean won Exp $
 */
public enum ArchiveStatusEnum {
    //
    AWAIT("A", "等待归档"),
    GENERATED("G", "已生成"),
    IN_PROCESS("I", "发送中"),
    // 归档数据已经提交==数据已经下载
    SUBMITED("S", "已提交"),
    RETURNED("R", "已返回"),
    FAIL("F", "提交失败");

    /**
     * 代码
     */
    private final String code;
    /**
     * 信息
     */
    private final String message;

    ArchiveStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据code获取ENUM
     *
     * @param code
     * @return
     */
    public static ArchiveStatusEnum getByCode(String code) {
        for (ArchiveStatusEnum resultCode : ArchiveStatusEnum.values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return null;
    }
}
