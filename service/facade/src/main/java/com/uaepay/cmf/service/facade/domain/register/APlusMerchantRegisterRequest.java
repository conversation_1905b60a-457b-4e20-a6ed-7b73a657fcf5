package com.uaepay.cmf.service.facade.domain.register;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * <p>A+商户注册请求</p>
 *
 * <AUTHOR>
 * @version: APlusMerchantRegisterRequest.class v1.0
 */
public class APlusMerchantRegisterRequest extends AbstractRequest {

    private static final long serialVersionUID = 2023502264248844187L;
    /**
     * 请求号，唯一
     */
    @NotEmpty(message = "Request no can't be empty!")
    private String requestNo;

    /**
     * 商户id
     */
    @NotEmpty(message = "Merchant id can't be empty!")
    private String merchantId;

    /**
     * 商户名
     */
    @NotEmpty(message = "Merchant name can't be empty!")
    private String merchantName;

    /**
     * 商户mcc
     */
    @Size(min = 4, max = 4, message = "Mcc illegal, length must be 4!")
    @NotEmpty(message = "Merchant mcc can't be empty!")
    private String merchantMcc;

    /**
     * 企业注册号
     */
    @NotEmpty(message = "Registration no can't be empty!")
    private String registrationNo;

    /**
     * 商户地址
     */
    @NotEmpty(message = "Merchant address can't be empty!")
    private String merchantAddress;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 门店名
     */
    private String storeName;

    /**
     * 门店mcc
     */
    private String storeMcc;

    /**
     * 门店地址
     */
    private String storeAddress;

    /**
     * 线上商户注册域名
     */
    private String websiteUrl;

    public String getWebsiteUrl() {
        return websiteUrl;
    }

    public void setWebsiteUrl(String websiteUrl) {
        this.websiteUrl = websiteUrl;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantMcc() {
        return merchantMcc;
    }

    public void setMerchantMcc(String merchantMcc) {
        this.merchantMcc = merchantMcc;
    }

    public String getRegistrationNo() {
        return registrationNo;
    }

    public void setRegistrationNo(String registrationNo) {
        this.registrationNo = registrationNo;
    }

    public String getMerchantAddress() {
        return merchantAddress;
    }

    public void setMerchantAddress(String merchantAddress) {
        this.merchantAddress = merchantAddress;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getStoreMcc() {
        return storeMcc;
    }

    public void setStoreMcc(String storeMcc) {
        this.storeMcc = storeMcc;
    }

    public String getStoreAddress() {
        return storeAddress;
    }

    public void setStoreAddress(String storeAddress) {
        this.storeAddress = storeAddress;
    }
}
