package com.uaepay.cmf.service.facade.domain.query;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date OrderNoQueryRequest.java v1.0
 */
public class OrderNoQueryRequest implements Serializable {
    private static final long serialVersionUID = 2031576863300724157L;

    @NotNull(message = "Payment Order No should not be null!")
    private String paymentOrderNo;

    @NotNull(message = "Gmt Start should not be null!")
    private Date gmtStart;

    @NotNull(message = "Gmt End should not be null!")
    private Date gmtEnd;

    public String getPaymentOrderNo() {
        return paymentOrderNo;
    }

    public void setPaymentOrderNo(String paymentOrderNo) {
        this.paymentOrderNo = paymentOrderNo;
    }

    public Date getGmtStart() {
        return gmtStart;
    }

    public void setGmtStart(Date gmtStart) {
        this.gmtStart = gmtStart;
    }

    public Date getGmtEnd() {
        return gmtEnd;
    }

    public void setGmtEnd(Date gmtEnd) {
        this.gmtEnd = gmtEnd;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
