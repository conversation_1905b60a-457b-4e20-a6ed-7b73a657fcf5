package com.uaepay.cmf.service.facade.domain.query;

import com.uaepay.basis.beacon.service.facade.domain.request.PageRequest;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date OrderQueryRequest.java v1.0  2020-06-21 17:11
 */
public class OrderPageQueryRequest extends PageRequest {
    private static final long serialVersionUID = 635196140388869632L;

    private String fundChannelCode;

    private String instCode;

    private String status;

    @NotNull(message = "Gmt Start should not be null!")
    private Date gmtStart;

    @NotNull(message = "Gmt End should not be null!")
    private Date gmtEnd;

    public String getFundChannelCode() {
        return fundChannelCode;
    }

    public void setFundChannelCode(String fundChannelCode) {
        this.fundChannelCode = fundChannelCode;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getGmtStart() {
        return gmtStart;
    }

    public void setGmtStart(Date gmtStart) {
        this.gmtStart = gmtStart;
    }

    public Date getGmtEnd() {
        return gmtEnd;
    }

    public void setGmtEnd(Date gmtEnd) {
        this.gmtEnd = gmtEnd;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
