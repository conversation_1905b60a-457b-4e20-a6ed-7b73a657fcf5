package com.uaepay.cmf.service.facade.domain.card;

import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>sessionId/cardId二选一</p>
 *
 * <AUTHOR>
 * @date CardTokenCreateRequest.java v1.0  2020-03-27 21:21
 */
public class CardTokenCreateRequest implements Serializable {

    private static final long serialVersionUID = 2664121519358404049L;

    /**
     * clientId
     */
    private String clientId;

    /**
     * 支付的session
     */
    private String sessionId;

    /**
     * 卡id
     */
    private Long cardId;

    /**
     * 受益人id
     */
    private Long beneficiaryId;

    /**
     * 会员id
     */
    private String memberId;

    /**
     * 目标机构
     */
    private String instCode;

    /**
     * 借记贷记，DC-借记，CC-贷记，GC-可借可贷
     */
    @Pattern(regexp = "^DC|CC|GC$", message = "dbcr should be one of DC/CC/GC!")
    private String dbcr;

    /**
     * 对公对私,A-全部, B-对公，C-对私
     */
    @Pattern(regexp = "^A|B|C$", message = "companyOrPersonal should be one of A/B/C!")
    private String companyOrPersonal;

    /**
     * 卡号
     */
    @Size(max = 32, message = "The cardNo's max length is 32!")
    private String cardNo;

    @Size(max = 3, message = "The countryCode's max length is 3!")
    private String countryCode;
    /**
     * 持卡人
     */
    private String cardHolder;

    /**
     * 卡有效期
     */
    private String cardExpired;

    /**
     * 有效期-月份
     */
    @Size(max = 2, message = "The expiredMonth's max length is 2!")
    private String expiredMonth;
    /**
     * 有效期-年份
     */
    @Size(max = 2, message = "The expiredYear's max length is 2!")
    private String expiredYear;


    @Pattern(regexp = "^Y|N$", message = "is3DS should be one of Y/N!")
    private String is3DS;

    /**
     * ip地址
     */
    @Size(max = 40, message = "The ipAddress's max length is 40!")
    private String ipAddress;

    /**
     * iban信息
     */
    private String iban;

    /**
     * 账号
     */
    private String cardAccountNo;

    /**
     * 结果url
     */
    @Size(max = 255, message = "The resultUrl's max length is 255!")
    private String resultUrl;

    @Pattern(regexp = "^Y|N$", message = "needCsc should be one of Y/N!")
    private String needCsc;

    @Pattern(regexp = "^DC|CC|PB|OT$", message = "cardType should be one of DC/CC/PB/OT!")
    private String cardType;

    private String cardBrand;

    /**
     * 卡安全码 - 此字段不会落地，只储存于缓存之中
     */
    private String csc;

    private String extension;

    /**
     * AANI
     */

    @Size(max = 10, message = "The cardTokenType max length is 10!")
    private String cardTokenType;

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public Long getBeneficiaryId() {
        return beneficiaryId;
    }

    public void setBeneficiaryId(Long beneficiaryId) {
        this.beneficiaryId = beneficiaryId;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getDbcr() {
        return dbcr;
    }

    public void setDbcr(String dbcr) {
        this.dbcr = dbcr;
    }

    public String getCompanyOrPersonal() {
        return companyOrPersonal;
    }

    public void setCompanyOrPersonal(String companyOrPersonal) {
        this.companyOrPersonal = companyOrPersonal;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCardHolder() {
        return cardHolder;
    }

    public void setCardHolder(String cardHolder) {
        this.cardHolder = cardHolder;
    }

    public String getCardExpired() {
        return cardExpired;
    }

    public void setCardExpired(String cardExpired) {
        this.cardExpired = cardExpired;
    }

    public String getExpiredMonth() {
        return expiredMonth;
    }

    public void setExpiredMonth(String expiredMonth) {
        this.expiredMonth = expiredMonth;
    }

    public String getExpiredYear() {
        return expiredYear;
    }

    public void setExpiredYear(String expiredYear) {
        this.expiredYear = expiredYear;
    }

    public String getIs3DS() {
        return is3DS;
    }

    public void setIs3DS(String is3DS) {
        this.is3DS = is3DS;
    }

    public String getCardAccountNo() {
        return cardAccountNo;
    }

    public void setCardAccountNo(String cardAccountNo) {
        this.cardAccountNo = cardAccountNo;
    }

    public String getResultUrl() {
        return resultUrl;
    }

    public void setResultUrl(String resultUrl) {
        this.resultUrl = resultUrl;
    }

    public String getNeedCsc() {
        return needCsc;
    }

    public void setNeedCsc(String needCsc) {
        this.needCsc = needCsc;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardBrand() {
        return cardBrand;
    }

    public void setCardBrand(String cardBrand) {
        this.cardBrand = cardBrand;
    }

    public String getCsc() {
        return csc;
    }

    public void setCsc(String csc) {
        this.csc = csc;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public String getCardTokenType() {
        return cardTokenType;
    }

    public void setCardTokenType(String cardTokenType) {
        this.cardTokenType = cardTokenType;
    }

    @Override
    public String toString() {
        return "CardTokenCreateRequest{" +
                "sessionId='" + sessionId + '\'' +
                ", cardId=" + cardId +
                ", memberId='" + memberId + '\'' +
                ", instCode='" + instCode + '\'' +
                ", dbcr='" + dbcr + '\'' +
                ", companyOrPersonal='" + companyOrPersonal + '\'' +
                ", is3DS='" + is3DS + '\'' +
                ", cardNo='" + StringUtils.isNotEmpty(cardNo) + '\'' +
                ", cardExpired='" + StringUtils.isNotEmpty(cardExpired) + '\'' +
                ", expiredYear='" + StringUtils.isNotEmpty(expiredYear) + '\'' +
                ", expiredMonth='" + StringUtils.isNotEmpty(expiredMonth) + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", cardType='" + cardType + '\'' +
                ", cardBrand='" + cardBrand + '\'' +
                ", iban='" + iban + '\'' +
                ", resultUrl='" + resultUrl + '\'' +
                ", needCsc='" + needCsc + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", iban='" + iban + '\'' +
                ", extension='" + extension + '\'' +
                ", cardTokenType='" + cardTokenType + '\'' +
                '}';
    }
}
