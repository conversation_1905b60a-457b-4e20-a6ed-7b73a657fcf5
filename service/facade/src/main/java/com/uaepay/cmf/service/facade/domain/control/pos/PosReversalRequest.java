package com.uaepay.cmf.service.facade.domain.control.pos;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date PspReversalRequest.java v1.0
 */
public class PosReversalRequest extends AbstractRequest {
    private static final long serialVersionUID = 2780886501676970372L;

    /**
     * request no is PAYMENT_SEQ_NO
     */
    @NotEmpty(message = "Request no should not be empty!")
    private String requestNo;

    /**
     * requestType use REVERSAL
     */
    @NotNull(message="Request type should not be null!")
    private ControlRequestType requestType;

    /**
     * instCode
     */
    @NotNull(message="Inst code should not be null!")
    private String instCode;
    /**
     * 支付模式，非空
     */
    @NotNull(message="Pay mode should not be null!")
    private PayMode payMode;
    /**
     * 金额 ，非空
     */
    @NotNull(message="Amount should not be null!")
    private Money amount;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public ControlRequestType getRequestType() {
        return requestType;
    }

    public void setRequestType(ControlRequestType requestType) {
        this.requestType = requestType;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public PayMode getPayMode() {
        return payMode;
    }

    public void setPayMode(PayMode payMode) {
        this.payMode = payMode;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
