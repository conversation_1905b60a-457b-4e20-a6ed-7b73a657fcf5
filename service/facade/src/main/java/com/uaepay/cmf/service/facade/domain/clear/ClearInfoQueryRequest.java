package com.uaepay.cmf.service.facade.domain.clear;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ClearInfoQueryRequest.java v1.0
 */
public class ClearInfoQueryRequest extends AbstractRequest {
    private static final long serialVersionUID = -4660730297168356585L;

    /**
     * 清算网络-swift
     */
    private String clearNet;

    public String getClearNet() {
        return clearNet;
    }

    public void setClearNet(String clearNet) {
        this.clearNet = clearNet;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
