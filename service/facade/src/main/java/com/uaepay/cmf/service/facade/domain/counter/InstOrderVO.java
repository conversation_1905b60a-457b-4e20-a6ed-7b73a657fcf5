package com.uaepay.cmf.service.facade.domain.counter;

import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * CMF机构订单--VO
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: InstOrderVO.java, v 0.1 2010-12-2 下午01:57:22 sean won Exp $
 */
public class InstOrderVO implements Serializable {

    private static final long serialVersionUID = 2952172544399996890L;
    /**
     * 提现流水号(机构订单号)
     */
    private String instOrderNo;
    /**
     * 业务类型
     */
    private String bizType;
    /**
     * 目标银行代码(代收银行)
     */
    private String instCode;
    /**
     * 目标银行名称(代收银行)
     */
    private String instName;
    /**
     * 收款银行代码
     */
    private String fundInstCode;
    /**
     * 收款银行名称
     */
    private String fundInstName;
    /**
     * 收款支银行代码
     */
    private String fundInstBranchCode;
    /**
     * 收款支银行名称
     */
    private String fundInstBranchName;
    /**
     * 帐号
     */
    private String accountNo;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * iban号
     */
    private String ibanNo;
    /**
     * 收款人姓名
     */
    private String accountName;
    /**
     * 出款原因
     */
    private String purpose;
    /**
     * 归档批次号
     */
    private Long archiveBatchNo;
    /**
     * 归档状态；参见InstOrderArchiveStatus.java，针对批量的才有值。
     */
    private String archiveStatus;
    /**
     * 通行证(付款方)
     */
    private String memberId;
    /**
     * 金额
     */
    private Money amount;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 文件生成时间
     */
    private Date gmtBookingSubmit;
    /**
     * 订单状态 (I: 处理中， S:成功， F:失败）
     */
    private String instStatus;
    /**
     * 支付码
     */
    private String paymentCode;
    /**
     * 产品码
     */
    private String productCode;
    /**
     * 是否支持手工更改状态，1. 直连出款的不允许操作, 2.手工出款的若复核通过后不允许操作
     */
    private boolean canManualChange;
    /**
     * 渠道编号
     */
    private String fundChannelCode;
    /**
     * 渠道API
     */
    private String fundChannelApi;
    /**
     * 主键
     */
    private Long instOrderId;
    /**
     * 订单处理情况说明
     */
    private String memo;
    /**
     * 归档模板
     */
    private Long archiveTemplateId;
    /**
     * 是否复核通过
     */
    private YesNo checkFlag;
    /**
     * 网关订单号
     */
    private String gateOrderNo;
    /**
     * PE流水号
     */
    private String paymentSeqNo;
    private String extension;

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getInstName() {
        return instName;
    }

    public void setInstName(String instName) {
        this.instName = instName;
    }

    public String getFundInstCode() {
        return fundInstCode;
    }

    public void setFundInstCode(String fundInstCode) {
        this.fundInstCode = fundInstCode;
    }

    public String getFundInstName() {
        return fundInstName;
    }

    public void setFundInstName(String fundInstName) {
        this.fundInstName = fundInstName;
    }

    public String getFundInstBranchCode() {
        return fundInstBranchCode;
    }

    public void setFundInstBranchCode(String fundInstBranchCode) {
        this.fundInstBranchCode = fundInstBranchCode;
    }

    public String getFundInstBranchName() {
        return fundInstBranchName;
    }

    public void setFundInstBranchName(String fundInstBranchName) {
        this.fundInstBranchName = fundInstBranchName;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getIbanNo() {
        return ibanNo;
    }

    public void setIbanNo(String ibanNo) {
        this.ibanNo = ibanNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Long getArchiveBatchNo() {
        return archiveBatchNo;
    }

    public void setArchiveBatchNo(Long archiveBatchNo) {
        this.archiveBatchNo = archiveBatchNo;
    }

    public String getArchiveStatus() {
        return archiveStatus;
    }

    public void setArchiveStatus(String archiveStatus) {
        this.archiveStatus = archiveStatus;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtBookingSubmit() {
        return gmtBookingSubmit;
    }

    public void setGmtBookingSubmit(Date gmtBookingSubmit) {
        this.gmtBookingSubmit = gmtBookingSubmit;
    }

    public String getInstStatus() {
        return instStatus;
    }

    public void setInstStatus(String instStatus) {
        this.instStatus = instStatus;
    }

    public String getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(String paymentCode) {
        this.paymentCode = paymentCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public boolean isCanManualChange() {
        return canManualChange;
    }

    public void setCanManualChange(boolean canManualChange) {
        this.canManualChange = canManualChange;
    }

    public String getFundChannelCode() {
        return fundChannelCode;
    }

    public void setFundChannelCode(String fundChannelCode) {
        this.fundChannelCode = fundChannelCode;
    }

    public String getFundChannelApi() {
        return fundChannelApi;
    }

    public void setFundChannelApi(String fundChannelApi) {
        this.fundChannelApi = fundChannelApi;
    }

    public Long getInstOrderId() {
        return instOrderId;
    }

    public void setInstOrderId(Long instOrderId) {
        this.instOrderId = instOrderId;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Long getArchiveTemplateId() {
        return archiveTemplateId;
    }

    public void setArchiveTemplateId(Long archiveTemplateId) {
        this.archiveTemplateId = archiveTemplateId;
    }

    public YesNo getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(YesNo checkFlag) {
        this.checkFlag = checkFlag;
    }

    public String getGateOrderNo() {
        return gateOrderNo;
    }

    public void setGateOrderNo(String gateOrderNo) {
        this.gateOrderNo = gateOrderNo;
    }

    public String getPaymentSeqNo() {
        return paymentSeqNo;
    }

    public void setPaymentSeqNo(String paymentSeqNo) {
        this.paymentSeqNo = paymentSeqNo;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
