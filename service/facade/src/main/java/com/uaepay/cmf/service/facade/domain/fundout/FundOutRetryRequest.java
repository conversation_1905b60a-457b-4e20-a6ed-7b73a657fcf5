package com.uaepay.cmf.service.facade.domain.fundout;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date FundOutRetryRequest.java v1.0
 */
public class FundOutRetryRequest extends AbstractRequest {
    private static final long serialVersionUID = -7836944567915971081L;
    /**
     * 批次订单号
     */
    @NotNull(message = "Archive batch id should not be null!")
    private Long archiveBatchId;
    /**
     * 重试时间
     */
    @NotNull(message = "Retry time should not be null!")
    private Date gmtRetry;

    public Long getArchiveBatchId() {
        return archiveBatchId;
    }

    public void setArchiveBatchId(Long archiveBatchId) {
        this.archiveBatchId = archiveBatchId;
    }

    public Date getGmtRetry() {
        return gmtRetry;
    }

    public void setGmtRetry(Date gmtRetry) {
        this.gmtRetry = gmtRetry;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
