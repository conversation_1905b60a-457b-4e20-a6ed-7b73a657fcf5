package com.uaepay.cmf.service.facade.domain;

import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.Kvp;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * CMF门面请求参数
 *
 * <AUTHOR> won
 * @version $Id: CmfRequest.java, v 0.1 2011-3-21 下午04:47:20 sean won Exp $
 */
public class CmfRequest implements Serializable {
    private static final long serialVersionUID = 92352396940413179L;

    /**
     * 请求批次号，批量提交时使用。可空
     */
    private String requestBatchNo;
    /**
     * 支付流水号。非空
     */
    protected String paymentSeqNo;
    /**
     * 清结算ID
     */
    protected String settlementId;
    /**
     * 产品码。非空
     */
    protected String productCode;
    /**
     * 支付编码。非空
     */
    protected String paymentCode;
    /**
     * 支付方式
     **/
    protected PayMode payMode;
    /**
     * 业务类型
     **/
    protected BizType bizType;
    /**
     * 机构编码 目标机构
     */
    private String instCode;
    /**
     * 会员ID。非空
     */
    protected String memberId;
    /**
     * 金额。非空
     */
    protected Money amount;
    /**
     * 业务发起时间
     */
    private Date bizTime;
    /**
     * 资金渠道，兼容用
     */
    private String fundsChannel;
    /**
     * 操作员
     */
    protected String operator;
    /**
     * 扩展信息
     */
    protected Extension extension = new Extension();
    /**
     * 备注
     */
    protected String memo;

    public String getRequestBatchNo() {
        return requestBatchNo;
    }

    public void setRequestBatchNo(String requestBatchNo) {
        this.requestBatchNo = requestBatchNo;
    }

    public String getPaymentSeqNo() {
        return paymentSeqNo;
    }

    public void setPaymentSeqNo(String paymentSeqNo) {
        this.paymentSeqNo = paymentSeqNo;
    }

    public String getSettlementId() {
        return settlementId;
    }

    public void setSettlementId(String settlementId) {
        this.settlementId = settlementId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(String paymentCode) {
        this.paymentCode = paymentCode;
    }

    public PayMode getPayMode() {
        return payMode;
    }

    public void setPayMode(PayMode payMode) {
        this.payMode = payMode;
    }

    public BizType getBizType() {
        return bizType;
    }

    public void setBizType(BizType bizType) {
        this.bizType = bizType;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public Date getBizTime() {
        return bizTime;
    }

    public void setBizTime(Date bizTime) {
        this.bizTime = bizTime;
    }

    public String getFundsChannel() {
        return fundsChannel;
    }

    public void setFundsChannel(String fundsChannel) {
        this.fundsChannel = fundsChannel;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Extension getExtension() {
        return extension;
    }

    public void setExtension(Extension extension) {
        this.extension = extension;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE) + ",extension:"
                + toStringExtension();
    }

    private String toStringExtension() {
        if (extension == null || extension.getEntryList() == null) {
            return null;
        }
        StringBuilder builder = new StringBuilder();
        for (Kvp kvp : extension.getEntryList()) {
            builder.append(kvp.getKey()).append("=").append(kvp.getValue()).append(",");
        }
        return builder.toString();
    }
}
