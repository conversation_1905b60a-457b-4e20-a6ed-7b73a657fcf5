package com.uaepay.cmf.service.facade.domain.cache;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 08/03/2024 09:35
 */
public class QueryChannelKeyRequest extends AbstractRequest {


    private static final long serialVersionUID = 6632736358170053185L;

    @NotBlank
    private String key;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
