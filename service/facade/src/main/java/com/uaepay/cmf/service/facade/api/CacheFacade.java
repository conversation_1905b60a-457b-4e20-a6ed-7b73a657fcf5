package com.uaepay.cmf.service.facade.api;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.service.facade.domain.cache.*;
import com.uaepay.cmf.service.facade.domain.query.PkQueryRequest;
import com.uaepay.cmf.service.facade.result.PkQueryResult;

/**
 * <p>缓存刷新门面</p>
 *
 * <AUTHOR> won
 * @version $Id: CacheFacade.java, v 0.1 2011-5-25 下午05:17:37 sean won Exp $
 */
public interface CacheFacade {

    /**
     * 获取csc
     *
     * @param cardTokenId
     * @return
     */
    String getCsc(String cardTokenId);

    PkQueryResult<String> query3ds2Form(PkQueryRequest<String> request);


    CommonResponse importChannelKey(ImportChannelKeyRequest request);

    QueryChannelKeyResponse queryChannelKey(QueryChannelKeyRequest request);

    QueryAccountBalanceCacheResponse queryBalanceCache(QueryAccountBalanceCacheRequest request);

}
