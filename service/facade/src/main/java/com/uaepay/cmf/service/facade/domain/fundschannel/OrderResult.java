package com.uaepay.cmf.service.facade.domain.fundschannel;

import com.uaepay.cmf.common.domain.base.InstOrderResult;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 订单结果
 *
 * <AUTHOR>
 * @version 2014-7-15 下午3:59:53
 */
public class OrderResult implements Serializable {

    private static final long serialVersionUID = 5119361907320123458L;
    /**
     * 机构订单结果
     */
    protected InstOrderResult orderResult;

    public InstOrderResult getOrderResult() {
        return orderResult;
    }

    public void setOrderResult(InstOrderResult orderResult) {
        this.orderResult = orderResult;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
