package com.uaepay.cmf.service.facade.api;

import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceRequest;
import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceResult;
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthRequest;
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthResponse;
import com.uaepay.cmf.service.facade.domain.card.RetrieveCardMetadataRequest;
import com.uaepay.cmf.service.facade.domain.card.RetrieveCardMetadataResponse;
import com.uaepay.cmf.service.facade.domain.clear.ClearInfo;
import com.uaepay.cmf.service.facade.domain.clear.ClearInfoQueryRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.cmf.service.facade.domain.control.CmfFileRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfFileResponse;
import com.uaepay.cmf.service.facade.domain.control.config.ChannelConfigQueryRequest;
import com.uaepay.cmf.service.facade.domain.control.config.ChannelConfigQueryResponse;
import com.uaepay.cmf.service.facade.domain.control.pos.PosReversalResponse;
import com.uaepay.cmf.service.facade.domain.control.psp.PspReversalRequest;
import com.uaepay.cmf.service.facade.domain.control.psp.PspReversalResponse;
import com.uaepay.cmf.service.facade.result.ListQueryResult;
import com.uaepay.common.domain.OperationEnvironment;

/**
 * 控制类门面请求参数
 *
 * <AUTHOR> won
 */
public interface ControlRequestFacade {

    /**
     * 控制请求接口
     *
     * @param request
     * @param environment
     * @return
     */
    CmfControlResult control(CmfControlRequest request, OperationEnvironment environment);

    /**
     * 推进3ds2.0
     * @param request
     * @return
     */
    CmfAdvanceResult advance(CmfAdvanceRequest request);

    /**
     * 验卡
     *
     * @param request
     * @return
     */
    CmfAuthResponse auth(CmfAuthRequest request);

    /**
     * 文件处理
     *
     * @param request
     * @return
     */
    CmfFileResponse processFile(CmfFileRequest request);

    /**
     * 查询清算网络信息
     * @param request
     * @return
     */
    ListQueryResult<ClearInfo> queryClearInfo(ClearInfoQueryRequest request);

    /**
     * psp渠道的撤销
     * @param request
     * @return
     */
    PspReversalResponse pspReversal(PspReversalRequest request);

    /**
     * NI-pos reversal
     * @param request
     * @return
     */
    PosReversalResponse posReversal(PspReversalRequest request);

    /**
     * 查询渠道配置信息
     * @param request
     * @return
     */
    ChannelConfigQueryResponse queryChannelConfig(ChannelConfigQueryRequest request);

    RetrieveCardMetadataResponse retrieveCardMetadata(RetrieveCardMetadataRequest request);

}
