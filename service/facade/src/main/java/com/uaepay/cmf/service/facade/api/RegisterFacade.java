package com.uaepay.cmf.service.facade.api;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterQueryRequest;
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterQueryResponse;
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterRequest;

/**
 * <p>注册类接口</p>
 *
 * <AUTHOR>
 * @version: RegisterFacade.class v1.0
 */
public interface RegisterFacade {

    /**
     * a+注册商户
     * @param request
     * @return
     */
    CommonResponse registerAPlusMerchant(APlusMerchantRegisterRequest request);

    /**
     * a+查询注册商户
     * @param request
     * @return
     */
    APlusMerchantRegisterQueryResponse queryRegisterResponse(APlusMerchantRegisterQueryRequest request);

}
