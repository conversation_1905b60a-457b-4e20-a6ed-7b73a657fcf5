package com.uaepay.cmf.service.facade.domain.fundout;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 25/12/2023 14:42
 */

public class QueryFundoutBeneficiaryInfoRequest extends AbstractRequest {

    private static final long serialVersionUID = -2438898020706809817L;

    private String memberId;

    private String instOrderNo;

    /**
     * accountId or other id which can use to query beneficiaryInfo
     */
    private String beneficiaryInfoId;

    private Map<String, String> extension;

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public String getBeneficiaryInfoId() {
        return beneficiaryInfoId;
    }

    public void setBeneficiaryInfoId(String beneficiaryInfoId) {
        this.beneficiaryInfoId = beneficiaryInfoId;
    }

    @Override
    public Map<String, String> getExtension() {
        return extension;
    }

    @Override
    public void setExtension(Map<String, String> extension) {
        this.extension = extension;
    }
}
