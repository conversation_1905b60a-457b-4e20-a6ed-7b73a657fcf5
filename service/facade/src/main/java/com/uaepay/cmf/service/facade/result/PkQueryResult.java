package com.uaepay.cmf.service.facade.result;


import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date PkQueryResult.java v1.0
 */
public class PkQueryResult<T> extends CommonResponse {

    private static final long serialVersionUID = 42453846506646157L;

    public PkQueryResult() {
        this(ApplyStatusEnum.FAIL, null, null);
    }

    public PkQueryResult(ApplyStatusEnum applyStatus, T item, String returnMessage) {
        this.applyStatus = applyStatus;
        this.item = item;
        this.message = returnMessage;
    }

    public static <V> PkQueryResult<V> success(V item,
                                               String returnMessage) {
        return new PkQueryResult<V>(ApplyStatusEnum.SUCCESS, item, returnMessage);
    }

    public static <V> PkQueryResult<V> fail(String returnMessage) {
        return new PkQueryResult<V>(ApplyStatusEnum.FAIL, null, returnMessage);
    }

    private T item;

    public T getItem() {
        return item;
    }

    public void setItem(T item) {
        this.item = item;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
