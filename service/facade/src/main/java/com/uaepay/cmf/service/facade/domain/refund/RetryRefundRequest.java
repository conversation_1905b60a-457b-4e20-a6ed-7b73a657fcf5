package com.uaepay.cmf.service.facade.domain.refund;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 11/12/2023 14:51
 */

public class RetryRefundRequest extends AbstractRequest implements Serializable {

    private static final long serialVersionUID = -8416377534008130985L;

    @NotBlank
    private String instOrderNo;
    @NotNull
    private Boolean needNewInstOrderNo;

    @NotBlank
    private String operator;

    public String getInstOrderNo() {
        return instOrderNo;
    }

    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo;
    }

    public Boolean getNeedNewInstOrderNo() {
        return needNewInstOrderNo;
    }

    public void setNeedNewInstOrderNo(Boolean needNewInstOrderNo) {
        this.needNewInstOrderNo = needNewInstOrderNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
