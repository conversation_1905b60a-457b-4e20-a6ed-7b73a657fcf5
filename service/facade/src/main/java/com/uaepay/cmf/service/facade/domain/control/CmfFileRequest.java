package com.uaepay.cmf.service.facade.domain.control;

import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import com.uaepay.cmf.common.enums.ControlRequestType;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <p>cmf对账单请求</p>
 *
 * <AUTHOR>
 * @date CmfFileRequest.java v1.0  1/7/21 4:33 PM
 */
public class CmfFileRequest extends AbstractRequest {
    private static final long serialVersionUID = -8604158676042213066L;

    /**
     * 请求号，非空
     */
    @NotEmpty(message = "Request no should not be empty!")
    private String requestNo;

    /**
     * 请求类型
     */
    @NotNull(message="Request type should not be null!")
    private ControlRequestType requestType;

    /**
     * 渠道编号，非空
     */
    @NotEmpty(message = "Channel code should not be empty!")
    private String channelCode;
    /**
     * 文件日期 yyyyMMdd，非空
     */
    private String fileDate;

    /**
     * 文件类型，eod,ack,statement
     */
    private String fileType;

    /**
     * 文件名
     */
    private String fileNamePattern;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getFileDate() {
        return fileDate;
    }

    public void setFileDate(String fileDate) {
        this.fileDate = fileDate;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public ControlRequestType getRequestType() {
        return requestType;
    }

    public void setRequestType(ControlRequestType requestType) {
        this.requestType = requestType;
    }

    public String getFileNamePattern() {
        return fileNamePattern;
    }

    public void setFileNamePattern(String fileNamePattern) {
        this.fileNamePattern = fileNamePattern;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
