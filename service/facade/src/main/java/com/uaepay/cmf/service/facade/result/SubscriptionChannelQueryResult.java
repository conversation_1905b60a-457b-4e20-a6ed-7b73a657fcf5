package com.uaepay.cmf.service.facade.result;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

public class SubscriptionChannelQueryResult implements Serializable {

    private static final long serialVersionUID = 2133997728399140852L;

    private Boolean available;

    public SubscriptionChannelQueryResult(Boolean available) {
        this.available = available;
    }

    public Boolean getAvailable() {
        return available;
    }

    public void setAvailable(Boolean available) {
        this.available = available;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
