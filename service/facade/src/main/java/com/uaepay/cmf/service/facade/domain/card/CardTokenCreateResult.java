package com.uaepay.cmf.service.facade.domain.card;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenCreateResult.java v1.0  2020-03-27 21:24
 */
public class CardTokenCreateResult extends CommonResponse {
    private static final long serialVersionUID = -7983587367231753131L;

    private String cardTokenId;

    public String getCardTokenId() {
        return cardTokenId;
    }

    public void setCardTokenId(String cardTokenId) {
        this.cardTokenId = cardTokenId;
    }

    public static CardTokenCreateResult buildSuccess(String cardTokenId) {
        CardTokenCreateResult result  = new CardTokenCreateResult();
        result.setApplyStatus(ApplyStatusEnum.SUCCESS);
        result.setCardTokenId(cardTokenId);
        return result;
    }

    public static CardTokenCreateResult buildFail(String message) {
        CardTokenCreateResult result  = new CardTokenCreateResult();
        result.setApplyStatus(ApplyStatusEnum.FAIL);
        result.setMessage(message);
        return result;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
