package com.uaepay.cmf.service.facade.domain.clear;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ClearInfo.java v1.0
 */
public class ClearInfo implements Serializable {
    private static final long serialVersionUID = -1564373597916482455L;

    /**
     * 清算网络-swift
     */
    private String clearNet;
    /**
     * 国家编码-AE
     */
    private String countryCode;
    /**
     * 币种-[USD,AED...]
     */
    private List<String> currencyList;

    public ClearInfo(String clearNet, String countryCode, List<String> currencyList) {
        this.clearNet = clearNet;
        this.countryCode = countryCode;
        this.currencyList = currencyList;
    }

    public String getClearNet() {
        return clearNet;
    }

    public void setClearNet(String clearNet) {
        this.clearNet = clearNet;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public List<String> getCurrencyList() {
        return currencyList;
    }

    public void setCurrencyList(List<String> currencyList) {
        this.currencyList = currencyList;
    }

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
