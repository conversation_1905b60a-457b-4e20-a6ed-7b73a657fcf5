<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cmf-core-parent</artifactId>
        <groupId>com.uaepay.fund.cmf</groupId>
        <version>1.1.19-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cmf-core-dal</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.uaepay.starter</groupId>
            <artifactId>mysql-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uaepay.common.basic</groupId>
            <artifactId>basic-lang</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uaepay.basis.beacon</groupId>
            <artifactId>beacon-service-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uaepay.basic.cobarclient</groupId>
            <artifactId>cobarclient</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <!-- try: mvn mybatis-generator:generate -->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.7</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>false</overwrite>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>5.1.6</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>
