package com.uaepay.cmf.common.core.dal.ibatis;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.uaepay.cmf.common.core.query.InstBatchOrderPageQuery;
import org.mybatis.spring.support.SqlSessionDaoSupport;

import com.uaepay.biz.common.util.PageList;
import com.uaepay.cmf.common.core.dal.daointerface.InstBatchOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.InstBatchOrderDO;
import com.uaepay.common.lang.Paginator;
import com.uaepay.common.util.money.Money;

/**
 * An ibatis based implementation of dao interface
 * <tt>com.uaepay.cmf.common.core.dal.daointerface.InstBatchOrderDAO</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_inst_batch_order.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR> won
 */
public class IbatisInstBatchOrderDAO extends SqlSessionDaoSupport implements InstBatchOrderDAO {
    /**
     * Insert one <tt>InstBatchOrderDO</tt> object to DB table <tt>TT_INST_BATCH_ORDER</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_INST_BATCH_ORDER(ARCHIVE_BATCH_ID,ORDER_TYPE,TOTAL_AMOUNT,TOTAL_COUNT,CURRENCY,FUND_CHANNEL_CODE,ARCHIVE_TEMPLATE_ID,FUND_CHANNEL_API,PAY_MODE,STATUS,IS_LOCKED,FILE_PATH,OPERATOR,GMT_ARCHIVE,GMT_CREATE,GMT_MODIFIED,MEMO,CHECK_FLAG) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp, ?, ?)</tt>
     *
     * @param instBatchOrder
     * @return Long @
     */
    @Override
    public Long insert(InstBatchOrderDO instBatchOrder) {
        if (instBatchOrder == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-INST-BATCH-ORDER-INSERT", instBatchOrder);

        return instBatchOrder.getArchiveBatchId();
    }

    /**
     * Query DB table <tt>TT_INST_BATCH_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ARCHIVE_BATCH_ID, ORDER_TYPE, TOTAL_AMOUNT, TOTAL_COUNT, CURRENCY, FUND_CHANNEL_CODE, ARCHIVE_TEMPLATE_ID, FUND_CHANNEL_API, PAY_MODE, STATUS, IS_LOCKED, FILE_PATH, OPERATOR, GMT_ARCHIVE, GMT_CREATE, GMT_MODIFIED, MEMO, CHECK_FLAG from TT_INST_BATCH_ORDER where (ARCHIVE_BATCH_ID = ?)</tt>
     *
     * @param archiveBatchId
     * @return InstBatchOrderDO @
     */
    @Override
    public InstBatchOrderDO loadById(Long archiveBatchId) {
        return (InstBatchOrderDO)getSqlSession().selectOne("MS-INST-BATCH-ORDER-LOAD-BY-ID", archiveBatchId);
    }

    /**
     * Query DB table <tt>TT_INST_BATCH_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ARCHIVE_BATCH_ID, ORDER_TYPE, TOTAL_AMOUNT, TOTAL_COUNT, CURRENCY, FUND_CHANNEL_CODE, ARCHIVE_TEMPLATE_ID, FUND_CHANNEL_API, PAY_MODE, STATUS, IS_LOCKED, FILE_PATH, OPERATOR, GMT_ARCHIVE, GMT_CREATE, GMT_MODIFIED, MEMO, CHECK_FLAG from TT_INST_BATCH_ORDER where (ARCHIVE_BATCH_ID = ?) for update</tt>
     *
     * @param archiveBatchId
     * @return InstBatchOrderDO @
     */
    @Override
    public InstBatchOrderDO lockedById(Long archiveBatchId) {
        return (InstBatchOrderDO)getSqlSession().selectOne("MS-INST-BATCH-ORDER-LOCKED-BY-ID", archiveBatchId);
    }

    /**
     * Update DB table <tt>TT_INST_BATCH_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_BATCH_ORDER set gmt_modified=systimestamp, status=? where (ARCHIVE_BATCH_ID = ?)</tt>
     *
     * @param status
     * @param archiveBatchId
     * @return int @
     */
    @Override
    public int updateStatusById(String status, Long archiveBatchId) {
        Map param = new HashMap();

        param.put("status", status);
        param.put("archiveBatchId", archiveBatchId);

        return getSqlSession().update("MS-INST-BATCH-ORDER-UPDATE-STATUS-BY-ID", param);
    }

    /**
     *
     * @param status
     * @param preStatus
     * @param archiveBatchId
     * @return @
     */
    @Override
    public int updateStatusByIdAndPreStatus(String status, String preStatus, Long archiveBatchId) {
        Map param = new HashMap();

        param.put("status", status);
        param.put("preStatus", preStatus);
        param.put("archiveBatchId", archiveBatchId);

        return getSqlSession().update("MS-INST-BATCH-ORDER-UPDATE-STATUS-BY-ID-AND-PRE-STATUS", param);
    }

    @Override
    public int updateStatusAndArchiveTimeByIdAndPreStatus(String status, Date gmtArchive, String preStatus, Long archiveBatchId) {
        Map param = new HashMap();

        param.put("status", status);
        param.put("gmtArchive", gmtArchive);
        param.put("preStatus", preStatus);
        param.put("archiveBatchId", archiveBatchId);
        return getSqlSession().update("MS-INST-BATCH-ORDER-UPDATE-STATUS-TIME-BY-ID-AND-PRE", param);
    }

    /**
     * Update DB table <tt>TT_INST_BATCH_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_BATCH_ORDER set gmt_modified=systimestamp, TOTAL_AMOUNT=?, TOTAL_COUNT=? where (ARCHIVE_BATCH_ID = ?)</tt>
     *
     * @param totalAmount
     * @param totalCount
     * @param archiveBatchId
     * @return int @
     */
    @Override
    public int updateAmountAndCount(Money totalAmount, Integer totalCount, Long archiveBatchId) {
        Map param = new HashMap();

        param.put("totalAmount", totalAmount);
        param.put("totalCount", totalCount);
        param.put("archiveBatchId", archiveBatchId);

        return getSqlSession().update("MS-INST-BATCH-ORDER-UPDATE-AMOUNT-AND-COUNT", param);
    }

    /**
     * Update DB table <tt>TT_INST_BATCH_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_BATCH_ORDER set gmt_modified=systimestamp</tt>
     *
     * @param archiveBatchId
     * @param checkFlag
     * @return int @
     */
    @Override
    public int passStatus(Long archiveBatchId, String checkFlag) {
        Map param = new HashMap();

        param.put("archiveBatchId", archiveBatchId);
        param.put("checkFlag", checkFlag);

        return getSqlSession().update("MS-INST-BATCH-ORDER-PASS-STATUS", param);
    }

    /**
     *
     * @param status
     * @param isLocked
     * @param pageSize
     * @param pageNum
     * @return @
     */
    @Override
    public PageList queryForBatchFundout(String status, String isLocked, String apiCode, String operator, int pageSize,
        int pageNum) {
        Map param = new HashMap();

        param.put("status", status);
        param.put("isLocked", isLocked);
        param.put("apiCode", apiCode);
        param.put("operator", operator);
        param.put("pageSize", pageSize);
        param.put("pageNum", pageNum);

        Paginator paginator = new Paginator();
        paginator.setItemsPerPage(pageSize);
        paginator.setPage(pageNum);

        paginator.setItems(
                getSqlSession().selectOne("MS-INST-BATCH-ORDER-QUERY-FOR-BDC-FUNDOUT-FOR-PAGING", param));

        PageList pageList = new PageList();
        pageList.setPaginator(paginator);

        if (paginator.getBeginIndex() <= paginator.getItems()) {
            param.put("startRow", paginator.getBeginIndex());
            param.put("endRow", paginator.getEndIndex());
            pageList.addAll(getSqlSession().selectList("MS-INST-BATCH-ORDER-QUERY-FOR-BDC-FUNDOUT", param));
        }

        return pageList;
    }

    @Override
    public int updateIsLockedByOriStatus(List<Long> archiveBatchIdList, String code, String oriCode) {
        Map param = new HashMap();
        param.put("archiveBatchIdList", archiveBatchIdList);
        param.put("code", code);
        param.put("oriCode", oriCode);
        return getSqlSession().update("MS-INST-BATCH-ORDER-UPDATE-IS-LOCKED", param);
    }

    @Override
    public List<Long> loadBatchOrder4Query(String lockStatus, String bizType, int rownum) {
        Map param = new HashMap();
        param.put("lockStatus", lockStatus);
        param.put("bizType", bizType);
        param.put("rownum", rownum);
        return getSqlSession().selectList("MS-INST-BATCH-ORDER-LOAD-4-QUERY", param);
    }

    @Override
    public int updateRetryInfoById(Integer retryTimes, Date gmtNextRetry, Long orderId) {
        Map param = new HashMap();

        param.put("retryTimes", retryTimes);
        param.put("archiveBatchId", orderId);
        param.put("gmtNextRetry", gmtNextRetry);

        return getSqlSession().update("MS-INST-BATCH-ORDER-UPDATE-RETRY-INFO-BY-ID", param);
    }

    @Override
    public PageList loadBatchList(InstBatchOrderPageQuery query) {
        Map param = new HashMap();

        param.put("gmtStart", query.getGmtStart());
        param.put("gmtEnd", query.getGmtEnd());
        param.put("status", query.getStatus());
        param.put("channelCode", query.getChannelCode());
        param.put("pageSize", query.getPageSize());

        Paginator paginator = new Paginator();
        paginator.setItemsPerPage(query.getPageSize());
        paginator.setPage(query.getPageNum());
        paginator.setItems(
                getSqlSession().selectOne("MS-INST-BATCH-ORDER-QUERY-FOR-PAGING", param));
        PageList pageList = new PageList();
        pageList.setPaginator(paginator);

        if (paginator.getBeginIndex() <= paginator.getItems()) {
            param.put("startRow", paginator.getBeginIndex());
            param.put("endRow", paginator.getEndIndex());
            pageList.addAll(getSqlSession().selectList("MS-INST-BATCH-ORDER-QUERY", param));
        }

        return pageList;
    }

    @Override
    public int updateExtensionById(String extension, long archiveBatchId) {
        Map param = new HashMap();

        param.put("extension", extension);
        param.put("archiveBatchId", archiveBatchId);

        return getSqlSession().update("MS-INST-BATCH-ORDER-UPDATE-EXTENSION-BY-ID", param);
    }
}
