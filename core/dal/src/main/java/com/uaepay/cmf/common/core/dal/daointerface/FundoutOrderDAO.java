package com.uaepay.cmf.common.core.dal.daointerface;

import com.uaepay.biz.common.util.QueryBase;
import com.uaepay.cmf.common.core.dal.dataobject.FundoutOrderDO;

import java.util.Map;

/**
 * A dao interface provides methods to access database table <tt>TT_FUNDOUT_ORDER</tt>.
 * <p>
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVER<PERSON>ITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_fundout_order.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public interface FundoutOrderDAO {
    /**
     * Insert one <tt>FundoutOrderDO</tt> object to DB table <tt>TT_FUNDOUT_ORDER</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_FUNDOUT_ORDER(INST_ORDER_ID,BANK_CODE,BANK_NAME,BANK_BRANCH,BANK_BRANCH_CODE,BANK_PROVINCE,BANK_CITY,AREA_CODE,ACCOUNT_TYPE,ACCOUNT_NAME,ACCOUNT_NO,CARD_TYPE,AGREEMENT_NO,PURPOSE,PT_ID,GMT_MODIFIED,GMT_CREATE) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp)</tt>
     *
     * @param fundoutOrder
     * @return long @
     */
    long insert(FundoutOrderDO fundoutOrder);

    /**
     * Delete records from DB table <tt>TT_FUNDOUT_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>delete from TT_FUNDOUT_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return int @
     */
    int delete(long instOrderId);

    /**
     * Query DB table <tt>TT_FUNDOUT_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, BANK_CODE, BANK_NAME, BANK_BRANCH, BANK_BRANCH_CODE, BANK_PROVINCE, BANK_CITY, AREA_CODE, ACCOUNT_TYPE, ACCOUNT_NAME, ACCOUNT_NO, CARD_TYPE, AGREEMENT_NO, PURPOSE, PT_ID, GMT_MODIFIED, GMT_CREATE from TT_FUNDOUT_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return FundoutOrderDO @
     */
    FundoutOrderDO loadById(long instOrderId);

    /**
     * Query DB table <tt>TT_FUNDOUT_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, BANK_CODE, BANK_NAME, BANK_BRANCH, BANK_BRANCH_CODE, BANK_PROVINCE, BANK_CITY, AREA_CODE, ACCOUNT_TYPE, ACCOUNT_NAME, ACCOUNT_NO, CARD_TYPE, AGREEMENT_NO, PURPOSE, PT_ID, GMT_MODIFIED, GMT_CREATE from TT_FUNDOUT_ORDER where (INST_ORDER_ID = ?) for update</tt>
     *
     * @param instOrderId
     * @return FundoutOrderDO @
     */
    FundoutOrderDO lockedById(long instOrderId);

}