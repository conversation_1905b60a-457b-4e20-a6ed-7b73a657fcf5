package com.uaepay.cmf.common.core.dal.ibatis;

import com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO;
import com.uaepay.cmf.common.core.dal.dataobject.ChannelCodeMappingDO;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.support.SqlSessionDaoSupport;

import java.util.List;

public class IbatisChannelCodeMappingDAO extends SqlSessionDaoSupport implements ChannelCodeMappingDAO {

    @Override
    public List<ChannelCodeMappingDO> selectByOldChannelCode(String oldChannelCode) {
        return getSqlSession().selectList(
                "com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.selectByOldChannelCode",
                oldChannelCode
        );
    }

    @Override
    public void insert(ChannelCodeMappingDO mapping) {
        getSqlSession().insert(
                "com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.insert",
                mapping
        );
    }

    @Override
    public int updateById(ChannelCodeMappingDO mapping) {
        return getSqlSession().update(
                "com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.updateById",
                mapping
        );
    }

    @Override
    public int updateStatus(Long id, String status) {
        return getSqlSession().update(
                "com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.updateStatus",
                new java.util.HashMap<String, Object>() {{
                    put("id", id);
                    put("status", status);
                }}
        );
    }

    @Override
    public List<ChannelCodeMappingDO> pageQuery(String oldChannelCode, String newChannelCode,
                                                String status, Integer offset, Integer limit) {
        return getSqlSession().selectList(
                "com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.pageQuery",
                new java.util.HashMap<String, Object>() {{
                    put("oldChannelCode", oldChannelCode);
                    put("newChannelCode", newChannelCode);
                    put("status", status);
                    put("offset", offset);
                    put("limit", limit);
                }}
        );
    }

    @Override
    public int pageCount(String oldChannelCode, String newChannelCode, String status) {
        return getSqlSession().selectOne(
                "com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.pageCount",
                new java.util.HashMap<String, Object>() {{
                    put("oldChannelCode", oldChannelCode);
                    put("newChannelCode", newChannelCode);
                    put("status", status);
                }}
        );
    }

    @Override
    public int deleteByOldChannelCode(String oldChannelCode) {
        if (StringUtils.isBlank(oldChannelCode)) {
            return 0;
        }

        return getSqlSession().update(
                "com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.deleteByOldChannelCode",
                new java.util.HashMap<String, Object>() {{
                    put("oldChannelCode", oldChannelCode);
                }}
        );
    }

    @Override
    public List<ChannelCodeMappingDO> selectAllValidRules() {
        return getSqlSession().selectList(
                "com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.selectAllValidRules"
        );
    }
}