package com.uaepay.cmf.common.core.dal.dataobject;

import lombok.Data;

import java.util.Date;

/**
 * A data object class directly models database table <tt>TT_CMF_REQUEST</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_cmf_request.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
@Data
public class CmfRequestDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>PAYMENT_SEQ_NO</tt>.
     */
    private String paymentSeqNo;

    /**
     * This property corresponds to db column <tt>CAN_RETRY</tt>.
     */
    private String canRetry;

    /**
     * This property corresponds to db column <tt>GMT_CREATE</tt>.
     */
    private Date gmtCreate;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

    /**
     * This property corresponds to db column <tt>SETTLEMENT_ID</tt>.
     */
    private String settlementId;

}
