package com.uaepay.cmf.common.core.dal.daointerface;

import com.uaepay.cmf.common.core.dal.dataobject.InstBatchResultDO;

/**
 * A dao interface provides methods to access database table <tt>TT_INST_BATCH_RESULT</tt>.
 * <p>
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_inst_batch_result.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR> won
 */
public interface InstBatchResultDAO {
    /**
     * Insert one <tt>InstBatchResultDO</tt> object to DB table <tt>TT_INST_BATCH_RESULT</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_INST_BATCH_RESULT(BATCH_RESULT_ID,ARCHIVE_BATCH_ID,BIZ_TYPE,BATCH_TYPE,FUND_CHANNEL_API,FUND_CHANNEL_CODE,TOTAL_COUNT,TOTAL_AMOUNT,SUCCESS_COUNT,SUCCESS_AMOUNT,FAILED_COUNT,FAILED_AMOUNT,DIFFERENT_COUNT,DIFFERENT_AMOUNT,LESS_COUNT,MORE_COUNT,STATUS,GMT_RETURN,GMT_CREATE,GMT_MODIFIED,MEMO,FILE_PATH,IMPORT_OPERATER,CHECK_OPERATER,IMPORT_TYPE) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp, ?, ?, ?, ?, ?)</tt>
     *
     * @param instBatchResult
     * @return Long @
     */
    Long insert(InstBatchResultDO instBatchResult);

    /**
     * Query DB table <tt>TT_INST_BATCH_RESULT</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select BATCH_RESULT_ID, ARCHIVE_BATCH_ID, BIZ_TYPE, BATCH_TYPE, FUND_CHANNEL_API, FUND_CHANNEL_CODE, TOTAL_COUNT, TOTAL_AMOUNT, SUCCESS_COUNT, SUCCESS_AMOUNT, FAILED_COUNT, FAILED_AMOUNT, DIFFERENT_COUNT, DIFFERENT_AMOUNT, LESS_COUNT, MORE_COUNT, STATUS, GMT_RETURN, GMT_CREATE, GMT_MODIFIED, MEMO, FILE_PATH, IMPORT_OPERATER, CHECK_OPERATER, IMPORT_TYPE from TT_INST_BATCH_RESULT where (BATCH_RESULT_ID = ?)</tt>
     *
     * @param batchResultId
     * @return InstBatchResultDO @
     */
    InstBatchResultDO loadById(Long batchResultId);

}
