package com.uaepay.cmf.common.core.dal.ibatis;

import java.util.List;

import org.mybatis.spring.support.SqlSessionDaoSupport;

import com.uaepay.cmf.common.core.dal.daointerface.PaymentNotifyLogDAO;
import com.uaepay.cmf.common.core.dal.dataobject.PaymentNotifyLogDO;

/**
 * An ibatis based implementation of dao interface <tt>com.uaepay.cmf.common.core.dal.daointerface.PaymentNotifyLogDAO</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)
 * code generation utility specially developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
 * be OVERWRITTEN by someone else. To modify the file, you should go to 
 * directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and 
 * find the corresponding configuration file (<tt>tables/tt_payment_notify_log.xml</tt>). 
 * Modify the configuration file according to your needs, then run <tt>iwallet-dalgen</tt> 
 * to generate this file.
 *
 * <AUTHOR> won
 */
public class IbatisPaymentNotifyLogDAO extends SqlSessionDaoSupport implements
                                                                     PaymentNotifyLogDAO {
    /**
     *  Insert one <tt>PaymentNotifyLogDO</tt> object to DB table <tt>TT_PAYMENT_NOTIFY_LOG</tt>, return primary key
     *
     *  <p>
     *  The sql statement for this operation is <br>
     *  <tt>insert into TT_PAYMENT_NOTIFY_LOG(NOTIFY_LOG_ID,CHANNEL_SEQ_NO,NOTIFY_RESULT,GMT_CREATE,MEMO) values (?, ?, ?, systimestamp, ?)</tt>
     *
     *	@param paymentNotifyLog
     *	@return long
     *	@
     */
    @Override
    public long insert(PaymentNotifyLogDO paymentNotifyLog) {
        if (paymentNotifyLog == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-PAYMENT-NOTIFY-LOG-INSERT", paymentNotifyLog);

        return paymentNotifyLog.getNotifyLogId();
    }

    /**
     *  Query DB table <tt>TT_PAYMENT_NOTIFY_LOG</tt> for records.
     *
     *  <p>
     *  The sql statement for this operation is <br>
     *  <tt>select NOTIFY_LOG_ID, CHANNEL_SEQ_NO, NOTIFY_RESULT, GMT_CREATE, MEMO from TT_PAYMENT_NOTIFY_LOG where (NOTIFY_LOG_ID = ?)</tt>
     *
     *	@param notifyLogId
     *	@return PaymentNotifyLogDO
     *	@
     */
    @Override
    public PaymentNotifyLogDO loadById(long notifyLogId) {
        return (PaymentNotifyLogDO) getSqlSession().selectOne(
            "MS-PAYMENT-NOTIFY-LOG-LOAD-BY-ID", notifyLogId);
    }

    /**
     *  Query DB table <tt>TT_PAYMENT_NOTIFY_LOG</tt> for records.
     *
     *  <p>
     *  The sql statement for this operation is <br>
     *  <tt>select NOTIFY_LOG_ID, CHANNEL_SEQ_NO, NOTIFY_RESULT, GMT_CREATE, MEMO from TT_PAYMENT_NOTIFY_LOG where (NOTIFY_LOG_ID = ?) for update</tt>
     *
     *	@param notifyLogId
     *	@return PaymentNotifyLogDO
     *	@
     */
    @Override
    public PaymentNotifyLogDO lockedById(long notifyLogId) {
        return (PaymentNotifyLogDO) getSqlSession().selectOne(
            "MS-PAYMENT-NOTIFY-LOG-LOCKED-BY-ID", notifyLogId);
    }

    /**
     *  Query DB table <tt>TT_PAYMENT_NOTIFY_LOG</tt> for records.
     *
     *  <p>
     *  The sql statement for this operation is <br>
     *  <tt>select NOTIFY_LOG_ID, CHANNEL_SEQ_NO, NOTIFY_RESULT, GMT_CREATE, MEMO from TT_PAYMENT_NOTIFY_LOG order by NOTIFY_LOG_ID ASC</tt>
     *
     *	@return List<PaymentNotifyLogDO>
     *	@
     */
    @Override
    public List<PaymentNotifyLogDO> loadAll() {
        return getSqlSession().selectList("MS-PAYMENT-NOTIFY-LOG-LOAD-ALL", null);
    }

}
