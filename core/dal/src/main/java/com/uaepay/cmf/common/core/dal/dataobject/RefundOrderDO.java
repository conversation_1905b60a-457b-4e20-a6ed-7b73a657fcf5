package com.uaepay.cmf.common.core.dal.dataobject;

import java.util.Date;

import com.uaepay.common.util.money.Money;
import lombok.Data;

/**
 * A data object class directly models database table <tt>TT_REFUND_ORDER</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_refund_order.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
@Data
public class RefundOrderDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>INST_ORDER_ID</tt>.
     */
    private Long instOrderId;

    /**
     * This property corresponds to db column <tt>FUNDIN_ORDER_NO</tt>.
     */
    private String fundinOrderNo;

    /**
     * This property corresponds to db column <tt>FUNDIN_REAL_AMOUNT</tt>.
     */
    private Money fundinRealAmount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);

    /**
     * This property corresponds to db column <tt>FUNDIN_DATE</tt>.
     */
    private String fundinDate;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

    /**
     * Setter method for property <tt>fundinRealAmount</tt>.
     * 
     * @param fundinRealAmount
     *            value to be assigned to property fundinRealAmount
     */
    public void setFundinRealAmount(Money fundinRealAmount) {
        if (fundinRealAmount == null) {
            this.fundinRealAmount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);
        } else {
            this.fundinRealAmount = fundinRealAmount;
        }
    }

}
