package com.uaepay.cmf.common.core.dal.daointerface;

import java.util.List;

import com.uaepay.cmf.common.core.dal.dataobject.InstOrderResultDO;

/**
 * A dao interface provides methods to access database table <tt>TT_INST_ORDER_RESULT</tt>.
 * <p>
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_inst_order_result.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public interface InstOrderResultDAO {
    /**
     * Insert one <tt>InstOrderResultDO</tt> object to DB table <tt>TT_INST_ORDER_RESULT</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_INST_ORDER_RESULT(RESULT_ID,BATCH_RESULT_ID,INST_ORDER_ID,INST_SEQ_NO,ORDER_TYPE,REAL_AMOUNT,BATCH_TYPE,INST_ORDER_NO,REAL_CURRENCY,ACCOUNT_NAME,ACCOUNT_NO,CARD_TYPE,ORGI_INST_ORDER_NO,COMPARE_STATUS,OPERATE_STATUS,FUND_CHANNEL_CODE,INST_STATUS,GLIDE_STATUS,INST_RESULT_CODE,GMT_MODIFIED,GMT_CREATE,MEMO,ARCHIVE_BATCH_ID,DIFF_MSG,FUNDIN_ORGI_INST_ORDER_NO,SYNC_CHANNEL_STATUS,NOTIFY_BANKORDER_STATUS,EXTENSION,API_RESULT_CODE,API_RESULT_SUB_CODE,API_TYPE,RISK_FLAG) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</tt>
     *
     * @param instOrderResult
     * @return Long @
     */
    Long insert(InstOrderResultDO instOrderResult);

    /**
     * Query DB table <tt>TT_INST_ORDER_RESULT</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select RESULT_ID, BATCH_RESULT_ID, INST_ORDER_ID, INST_SEQ_NO, ORDER_TYPE, REAL_AMOUNT, BATCH_TYPE, INST_ORDER_NO, REAL_CURRENCY, ACCOUNT_NAME, ACCOUNT_NO, CARD_TYPE, ORGI_INST_ORDER_NO, COMPARE_STATUS, OPERATE_STATUS, FUND_CHANNEL_CODE, INST_STATUS, GLIDE_STATUS, INST_RESULT_CODE, GMT_MODIFIED, GMT_CREATE, MEMO, ARCHIVE_BATCH_ID, DIFF_MSG, FUNDIN_ORGI_INST_ORDER_NO, SYNC_CHANNEL_STATUS, NOTIFY_BANKORDER_STATUS, EXTENSION, API_RESULT_CODE, API_RESULT_SUB_CODE, API_TYPE, RISK_FLAG from TT_INST_ORDER_RESULT where (RESULT_ID = ?)</tt>
     *
     * @param resultId
     * @return InstOrderResultDO @
     */
    InstOrderResultDO loadById(Long resultId);

    /**
     * Query DB table <tt>TT_INST_ORDER_RESULT</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select RESULT_ID, BATCH_RESULT_ID, INST_ORDER_ID, INST_SEQ_NO, ORDER_TYPE, REAL_AMOUNT, BATCH_TYPE, INST_ORDER_NO, REAL_CURRENCY, ACCOUNT_NAME, ACCOUNT_NO, CARD_TYPE, ORGI_INST_ORDER_NO, COMPARE_STATUS, OPERATE_STATUS, FUND_CHANNEL_CODE, INST_STATUS, GLIDE_STATUS, INST_RESULT_CODE, GMT_MODIFIED, GMT_CREATE, MEMO, ARCHIVE_BATCH_ID, DIFF_MSG, FUNDIN_ORGI_INST_ORDER_NO, SYNC_CHANNEL_STATUS, NOTIFY_BANKORDER_STATUS, EXTENSION, API_RESULT_CODE, API_RESULT_SUB_CODE, API_TYPE, RISK_FLAG from TT_INST_ORDER_RESULT where (RESULT_ID = ?) for update</tt>
     *
     * @param resultId
     * @return InstOrderResultDO @
     */
    InstOrderResultDO lockedById(Long resultId);

    /**
     * Query DB table <tt>TT_INST_ORDER_RESULT</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select RESULT_ID, BATCH_RESULT_ID, INST_ORDER_ID, INST_SEQ_NO, ORDER_TYPE, REAL_AMOUNT, BATCH_TYPE, INST_ORDER_NO, REAL_CURRENCY, ACCOUNT_NAME, ACCOUNT_NO, CARD_TYPE, ORGI_INST_ORDER_NO, COMPARE_STATUS, OPERATE_STATUS, FUND_CHANNEL_CODE, INST_STATUS, GLIDE_STATUS, INST_RESULT_CODE, GMT_MODIFIED, GMT_CREATE, MEMO, ARCHIVE_BATCH_ID, DIFF_MSG, FUNDIN_ORGI_INST_ORDER_NO, SYNC_CHANNEL_STATUS, NOTIFY_BANKORDER_STATUS, EXTENSION, API_RESULT_CODE, API_RESULT_SUB_CODE, API_TYPE, RISK_FLAG from TT_INST_ORDER_RESULT where ((INST_ORDER_ID = ?) AND (INST_STATUS != 'Q')) order by RESULT_ID DESC</tt>
     *
     * @param instOrderId
     * @return List<InstOrderResultDO> @
     */
    List<InstOrderResultDO> loadByOrder(Long instOrderId);

    /**
     * Update DB table <tt>TT_INST_ORDER_RESULT</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER_RESULT set gmt_modified=systimestamp, RISK_FLAG=? where (RESULT_ID = ?)</tt>
     *
     * @param riskFlag
     * @param resultId
     * @return int @
     */
    int updateRiskFlagById(String riskFlag, Long resultId);

    /**
     * Update DB table <tt>TT_INST_ORDER_RESULT</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER_RESULT set gmt_modified=systimestamp, OPERATE_STATUS=? where (RESULT_ID = ?)</tt>
     *
     * @param operateStatus
     * @param resultId
     * @return int @
     */
    int updateOperateStatusById(String operateStatus, Long resultId);

    /**
     * Update DB table <tt>TT_INST_ORDER_RESULT</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER_RESULT set gmt_modified=systimestamp</tt>
     *
     * @param to
     * @param ids
     * @param from
     * @return int @
     */
    int updateOperateStatusByResultIds(String to, List ids, String from);

    /**
     * Query DB table <tt>TT_INST_ORDER_RESULT</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select RESULT_ID, BATCH_RESULT_ID, INST_ORDER_ID, INST_SEQ_NO, ORDER_TYPE, REAL_AMOUNT, BATCH_TYPE, INST_ORDER_NO, REAL_CURRENCY, ACCOUNT_NAME, ACCOUNT_NO, CARD_TYPE, ORGI_INST_ORDER_NO, COMPARE_STATUS, OPERATE_STATUS, FUND_CHANNEL_CODE, INST_STATUS, GLIDE_STATUS, INST_RESULT_CODE, GMT_MODIFIED, GMT_CREATE, MEMO, ARCHIVE_BATCH_ID, DIFF_MSG, FUNDIN_ORGI_INST_ORDER_NO, SYNC_CHANNEL_STATUS, NOTIFY_BANKORDER_STATUS, EXTENSION, API_RESULT_CODE, API_RESULT_SUB_CODE, API_TYPE, RISK_FLAG from TT_INST_ORDER_RESULT where (INST_ORDER_ID = ?) order by INST_ORDER_ID DESC</tt>
     *
     * @param instOrderId
     * @return List<InstOrderResultDO> @
     */
    List<InstOrderResultDO> listByInstOrderId(Long instOrderId);


    List<InstOrderResultDO> loadByInstOrderIdAndResult(Long instOrderId, String apiType, String apiResultCode, String apiResultSubCode);

    int updateResultById(InstOrderResultDO resultDO, Long resultId);
}