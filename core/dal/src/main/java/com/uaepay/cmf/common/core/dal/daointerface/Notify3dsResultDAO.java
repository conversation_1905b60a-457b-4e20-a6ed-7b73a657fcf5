package com.uaepay.cmf.common.core.dal.daointerface;

import com.uaepay.cmf.common.core.dal.dataobject.Notify3dsResultDO;

import java.util.Map;

public interface Notify3dsResultDAO {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_notify_3ds_result
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    long insert(Notify3dsResultDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_notify_3ds_result
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    Notify3dsResultDO selectByPrimaryKey(Long resultId);

    Notify3dsResultDO selectByCondition(Map<String, String> paramMap);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_notify_3ds_result
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    int updateByPrimaryKey(Notify3dsResultDO record);
}