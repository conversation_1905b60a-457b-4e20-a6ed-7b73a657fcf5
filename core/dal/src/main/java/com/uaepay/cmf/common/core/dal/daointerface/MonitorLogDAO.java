package com.uaepay.cmf.common.core.dal.daointerface;

import java.util.List;

import com.uaepay.cmf.common.core.dal.dataobject.MonitorLogDO;

/**
 * A dao interface provides methods to access database table <tt>TL_MONITOR_LOG</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tl_monitor_log.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public interface MonitorLogDAO {
    /**
     * Insert one <tt>MonitorLogDO</tt> object to DB table <tt>TL_MONITOR_LOG</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TL_MONITOR_LOG(LOG_ID,IP_ADDRESS,ORDER_NO,EVENT_MESSAGE,ACTION_ID,ALERT_LEVEL,LOG_STATUS,EXCEPTION_LOG,MEMO,GMT_CREATE,GMT_MODIFIED) values (?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp)</tt>
     *
     * @param monitorLog
     * @return Long @
     */
    Long insert(MonitorLogDO monitorLog);

    /**
     * Update DB table <tt>TL_MONITOR_LOG</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TL_MONITOR_LOG set GMT_MODIFIED=sysdate, LOG_STATUS='newStatus', MEMO='memo' where ((LOG_STATUS = 'oldStatus') AND (LOG_ID = 'logIdlist'))</tt>
     *
     * @param logIdlist
     * @param newStatus
     * @param oldStatus
     * @param memo
     * @return int @
     */
    int updateStatusWithPreStatus(List logIdlist, String newStatus, String oldStatus, String memo);

    /**
     * Query DB table <tt>TL_MONITOR_LOG</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select LOG_ID, IP_ADDRESS, ORDER_NO, EVENT_MESSAGE, ACTION_ID, ALERT_LEVEL, LOG_STATUS, EXCEPTION_LOG, MEMO, GMT_CREATE, GMT_MODIFIED from TL_MONITOR_LOG where (LOG_ID = ?)</tt>
     *
     * @param logId
     * @return MonitorLogDO @
     */
    MonitorLogDO loadByLogId(Long logId);

    /**
     * Query DB table <tt>TL_MONITOR_LOG</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select LOG_ID, IP_ADDRESS, ORDER_NO, EVENT_MESSAGE, ACTION_ID, ALERT_LEVEL, LOG_STATUS, EXCEPTION_LOG, MEMO, GMT_CREATE, GMT_MODIFIED from TL_MONITOR_LOG where ((GMT_CREATE >= (sysdate - (3 / 24))) AND (LOG_STATUS = ?) AND (rownum >= ?))</tt>
     *
     * @param logStatus
     * @param rownum
     * @return List<MonitorLogDO> @
     */
    List<MonitorLogDO> loadByStatus(String logStatus, int rownum);

}