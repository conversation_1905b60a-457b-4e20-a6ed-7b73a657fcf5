package com.uaepay.cmf.common.core.dal.ibatis;

import org.mybatis.spring.support.SqlSessionDaoSupport;

import com.uaepay.cmf.common.core.dal.daointerface.FundinOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.FundinOrderDO;

/**
 * An ibatis based implementation of dao interface <tt>com.uaepay.cmf.common.core.dal.daointerface.FundinOrderDAO</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_fundin_order.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public class IbatisFundinOrderDAO extends SqlSessionDaoSupport implements FundinOrderDAO {
    /**
     * Insert one <tt>FundinOrderDO</tt> object to DB table <tt>TT_FUNDIN_ORDER</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_FUNDIN_ORDER(INST_ORDER_ID,CARD_TYPE,PAYER_INST_CODE,CONTRACT_NO,GMT_MODIFIED,GMT_CREATED) values (?, ?, ?, ?, systimestamp, systimestamp)</tt>
     *
     * @param fundinOrder
     * @return long @
     */
    @Override
    public long insert(FundinOrderDO fundinOrder) {
        if (fundinOrder == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-FUNDIN-ORDER-INSERT", fundinOrder);

        return fundinOrder.getInstOrderId();
    }

    /**
     * Query DB table <tt>TT_FUNDIN_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, CARD_TYPE, PAYER_INST_CODE, CONTRACT_NO, GMT_MODIFIED, GMT_CREATED from TT_FUNDIN_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return FundinOrderDO @
     */
    @Override
    public FundinOrderDO loadById(long instOrderId) {
        return (FundinOrderDO)getSqlSession().selectOne("MS-FUNDIN-ORDER-LOAD-BY-ID", instOrderId);
    }

    /**
     * Delete records from DB table <tt>TT_FUNDIN_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>delete from TT_FUNDIN_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return int @
     */
    @Override
    public int delete(long instOrderId) {
        return getSqlSession().delete("MS-FUNDIN-ORDER-DELETE", instOrderId);
    }

    /**
     * Query DB table <tt>TT_FUNDIN_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, CARD_TYPE, PAYER_INST_CODE, CONTRACT_NO, GMT_MODIFIED, GMT_CREATED from TT_FUNDIN_ORDER where (INST_ORDER_ID = ?) for update</tt>
     *
     * @param instOrderId
     * @return FundinOrderDO @
     */
    @Override
    public FundinOrderDO lockedById(long instOrderId) {
        return (FundinOrderDO)getSqlSession().selectOne("MS-FUNDIN-ORDER-LOCKED-BY-ID", instOrderId);
    }

}