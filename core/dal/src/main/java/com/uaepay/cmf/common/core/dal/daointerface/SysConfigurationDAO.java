package com.uaepay.cmf.common.core.dal.daointerface;

import java.util.List;

import com.uaepay.cmf.common.core.dal.dataobject.SysConfigurationDO;

/**
 * A dao interface provides methods to access database table <tt>TM_SYS_CONFIGURATION</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tm_sys_configuration.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR> won
 */
public interface SysConfigurationDAO {
    /**
     * Insert one <tt>SysConfigurationDO</tt> object to DB table <tt>TM_SYS_CONFIGURATION</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TM_SYS_CONFIGURATION(ATTR_NAME,ATTR_VALUE,MEMO,GMT_CREATED,GMT_MODIFIED) values (?, ?, ?, systimestamp, systimestamp)</tt>
     *
     * @param sysConfiguration
     * @return String @
     */
    String insert(SysConfigurationDO sysConfiguration);

    /**
     * 更新值
     * 
     * @param sysConfigurationDO
     * @return
     */
    int update(SysConfigurationDO sysConfigurationDO);

    /**
     * Query DB table <tt>TM_SYS_CONFIGURATION</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ATTR_NAME, ATTR_VALUE, MEMO, GMT_CREATED, GMT_MODIFIED from TM_SYS_CONFIGURATION where (ATTR_NAME = ?)</tt>
     *
     * @param attrName
     * @return SysConfigurationDO @
     */
    SysConfigurationDO loadByKey(String attrName);

    /**
     * Query DB table <tt>TM_SYS_CONFIGURATION</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ATTR_NAME, ATTR_VALUE, MEMO, GMT_CREATED, GMT_MODIFIED from TM_SYS_CONFIGURATION order by ATTR_NAME DESC</tt>
     *
     * @return List<SysConfigurationDO> @
     */
    List<SysConfigurationDO> loadAll();

}
