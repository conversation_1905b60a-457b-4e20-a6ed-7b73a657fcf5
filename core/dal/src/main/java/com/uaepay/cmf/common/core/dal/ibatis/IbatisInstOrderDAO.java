package com.uaepay.cmf.common.core.dal.ibatis;

import com.uaepay.basis.beacon.service.facade.domain.request.PageRequest;
import com.uaepay.biz.common.util.PageList;
import com.uaepay.cmf.common.core.dal.daointerface.InstOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.InstOrderDO;
import com.uaepay.cmf.common.core.dal.dataobject.UniqueOrderDO;
import com.uaepay.common.lang.Paginator;
import com.uaepay.common.util.money.Money;
import org.apache.commons.lang.StringUtils;
import org.mybatis.spring.support.SqlSessionDaoSupport;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * An ibatis based implementation of dao interface <tt>com.uaepay.cmf.common.core.dal.daointerface.InstOrderDAO</tt>.
 * <p>
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_inst_order.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public class IbatisInstOrderDAO extends SqlSessionDaoSupport implements InstOrderDAO {

    private static final String INST_ORDER_ID = "instOrderId";
    private static final String STATUS = "status";

    /**
     * Insert one <tt>InstOrderDO</tt> object to DB table <tt>TT_INST_ORDER</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_INST_ORDER(INST_ORDER_ID,INST_CODE,INST_ORDER_NO,ORDER_TYPE,PAY_MODE,CURRENCY,SUBMIT_PRIORITY,GMT_BOOKING_SUBMIT,ARCHIVE_TEMPLATE_ID,PRODUCT_CODE,PAYMENT_CODE,AMOUNT,STATUS,COMMUNICATE_TYPE,FUND_CHANNEL_CODE,FUND_CHANNEL_API,COMMUNICATE_STATUS,ARCHIVE_BATCH_ID,FLAG,RISK_STATUS,GMT_SUBMIT,GMT_CREATE,GMT_MODIFIED,MEMO,GMT_NEXT_RETRY,RETRY_TIMES,ROUTE_VERSION,IS_SPLIT,CMF_SEQ_NO,EXTENSION,EXPECT_TIME) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp, ?, systimestamp, 0, ?, ?, ?, ?, ?, ?)</tt>
     *
     * @param instOrder
     * @return Long @
     */
    @Override
    public Long insert(InstOrderDO instOrder) {
        if (instOrder == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-INST-ORDER-INSERT", instOrder);

        return instOrder.getInstOrderId();
    }

    /**
     * Delete records from DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>delete from TT_INST_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return int @
     */
    @Override
    public int delete(Long instOrderId) {
        return getSqlSession().delete("MS-INST-ORDER-DELETE", instOrderId);
    }

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, INST_CODE, INST_ORDER_NO, ORDER_TYPE, PAY_MODE, CURRENCY, SUBMIT_PRIORITY, GMT_BOOKING_SUBMIT, ARCHIVE_TEMPLATE_ID, PRODUCT_CODE, PAYMENT_CODE, AMOUNT, STATUS, COMMUNICATE_TYPE, FUND_CHANNEL_CODE, FUND_CHANNEL_API, COMMUNICATE_STATUS, ARCHIVE_BATCH_ID, FLAG, RISK_STATUS, GMT_SUBMIT, GMT_CREATE, GMT_MODIFIED, MEMO, GMT_NEXT_RETRY, RETRY_TIMES, ROUTE_VERSION,  IS_SPLIT, CMF_SEQ_NO, EXTENSION, EXPECT_TIME from TT_INST_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return InstOrderDO @
     */
    @Override
    public InstOrderDO loadById(Long instOrderId) {
        return (InstOrderDO) getSqlSession().selectOne("MS-INST-ORDER-LOAD-BY-ID", instOrderId);
    }

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, INST_CODE, INST_ORDER_NO, ORDER_TYPE, PAY_MODE, CURRENCY, SUBMIT_PRIORITY, GMT_BOOKING_SUBMIT, ARCHIVE_TEMPLATE_ID, PRODUCT_CODE, PAYMENT_CODE, AMOUNT, STATUS, COMMUNICATE_TYPE, FUND_CHANNEL_CODE, FUND_CHANNEL_API, COMMUNICATE_STATUS, ARCHIVE_BATCH_ID, FLAG, RISK_STATUS, GMT_SUBMIT, GMT_CREATE, GMT_MODIFIED, MEMO, GMT_NEXT_RETRY, RETRY_TIMES, ROUTE_VERSION,  IS_SPLIT, CMF_SEQ_NO, EXTENSION, EXPECT_TIME from TT_INST_ORDER where (INST_ORDER_ID = ?) for update</tt>
     *
     * @param instOrderId
     * @return InstOrderDO @
     */
    @Override
    public InstOrderDO lockedById(Long instOrderId) {
        return (InstOrderDO) getSqlSession().selectOne("MS-INST-ORDER-LOCKED-BY-ID", instOrderId);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, STATUS=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @param targetStatus
     * @param preStatus
     * @return int @
     */
    @Override
    public int updateStatusById(Long instOrderId, String targetStatus, String preStatus) {
        Map param = new HashMap();

        param.put("targetStatus", targetStatus);
        param.put("preStatus", preStatus);
        param.put(INST_ORDER_ID, instOrderId);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-STATUS-BY-ID", param);
    }

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, INST_CODE, INST_ORDER_NO, ORDER_TYPE, PAY_MODE, CURRENCY, SUBMIT_PRIORITY, GMT_BOOKING_SUBMIT, ARCHIVE_TEMPLATE_ID, PRODUCT_CODE, PAYMENT_CODE, AMOUNT, STATUS, COMMUNICATE_TYPE, FUND_CHANNEL_CODE, FUND_CHANNEL_API, COMMUNICATE_STATUS, ARCHIVE_BATCH_ID, FLAG, RISK_STATUS, GMT_SUBMIT, GMT_CREATE, GMT_MODIFIED, MEMO, GMT_NEXT_RETRY, RETRY_TIMES, ROUTE_VERSION,  IS_SPLIT, CMF_SEQ_NO, EXTENSION, EXPECT_TIME from TT_INST_ORDER where (CMF_SEQ_NO = ?)</tt>
     *
     * @param cmfSeqNo
     * @return List<InstOrderDO> @
     */
    @Override
    public List<InstOrderDO> loadByCmfSeq(String cmfSeqNo) {
        return getSqlSession().selectList("MS-INST-ORDER-LOAD-BY-CMF-SEQ", cmfSeqNo);
    }

    @Override
    public List<InstOrderDO> lockedByCmfSeq(String cmfSeqNo) {
        return getSqlSession().selectList("MS-INST-ORDER-LOCKED-BY-CMF-SEQ", cmfSeqNo);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, COMMUNICATE_STATUS=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param communicateStatus
     * @param instOrderId
     * @return int @
     */
    @Override
    public int updateCommunicateStatusById(String communicateStatus, Long instOrderId) {
        Map param = new HashMap();

        param.put("communicateStatus", communicateStatus);
        param.put(INST_ORDER_ID, instOrderId);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-COMMUNICATE-STATUS-BY-ID", param);
    }

    @Override
    public int updateCommunicateStatusBatchByIds(List<Long> instOrderIds, String nowStatus, String preStatus) {
        Map param = new HashMap();

        param.put("instOrderIds", instOrderIds);
        param.put("nowStatus", nowStatus);
        param.put("preStatus", preStatus);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-COMMUNICATE-STATUS-BATCH-BY-IDS", param);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, COMMUNICATE_STATUS=? where ((INST_ORDER_ID = ?) AND (COMMUNICATE_STATUS = ?))</tt>
     *
     * @param communicateStatus
     * @param instOrderId
     * @param communicateStatus2
     * @return int @
     */
    @Override
    public int updateCommunicateStatusWithPreStatus(String communicateStatus, Long instOrderId,
                                                    String communicateStatus2) {
        Map param = new HashMap();

        param.put("communicateStatus", communicateStatus);
        param.put(INST_ORDER_ID, instOrderId);
        param.put("communicateStatus2", communicateStatus2);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-COMMUNICATE-STATUS-WITH-PRE-STATUS", param);
    }

    @Override
    public int updateRetryDataWithPreStatus(String communicateStatus, Long instOrderId, String communicateStatus2, String instOrderNo, String extension) {
        Map param = new HashMap();

        param.put("communicateStatus", communicateStatus);
        param.put(INST_ORDER_ID, instOrderId);
        param.put("communicateStatus2", communicateStatus2);
        param.put("instOrderNo", instOrderNo);
        param.put("extension", extension);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-RETRY-DATA-WITH-PRE-STATUS", param);
    }

    @Override
    public int updateCommStatusAndBookingByArchiveId(String commStatus, Date gmtBooking, Long archiveId, String preCommStatus) {
        Map param = new HashMap();

        param.put("commStatus", commStatus);
        param.put("archiveId", archiveId);
        param.put("gmtBooking", gmtBooking);
        param.put("preCommStatus", preCommStatus);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-COMM-STATUS-AND-BOOKING-BY-ARCHIVE-ID", param);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, FUND_CHANNEL_CODE=?, FUND_CHANNEL_API=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param fundChannelCode
     * @param channelApi
     * @param instOrderId
     * @return int @
     */
    @Override
    public int updateChannelInfoById(String fundChannelCode, String channelApi, Long instOrderId) {
        Map param = new HashMap();

        param.put("fundChannelCode", fundChannelCode);
        param.put("fundChannelApi", channelApi);
        param.put(INST_ORDER_ID, instOrderId);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-CHANNEL-INFO-BY-ID", param);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, memo=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param memo
     * @param instOrderId
     * @return int @
     */
    @Override
    public int updateMemoById(String memo, Long instOrderId) {
        Map param = new HashMap();

        param.put("memo", memo);
        param.put(INST_ORDER_ID, instOrderId);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-MEMO-BY-ID", param);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, extension=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param extension
     * @param instOrderId
     * @return int @
     */
    @Override
    public int updateExtensionById(String extension, Long instOrderId) {
        Map param = new HashMap();

        param.put("extension", extension);
        param.put(INST_ORDER_ID, instOrderId);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-EXTENSION-BY-ID", param);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, GMT_NEXT_RETRY='gmtNextRetry', RETRY_TIMES=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param retryTimes
     * @param instOrderId
     * @param gmtNextRetry
     * @return int @
     */
    @Override
    public int updateRetryInfoById(Integer retryTimes, Date gmtNextRetry, Long instOrderId) {
        Map param = new HashMap();

        param.put("retryTimes", retryTimes);
        param.put(INST_ORDER_ID, instOrderId);
        param.put("gmtNextRetry", gmtNextRetry);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-RETRY-INFO-BY-ID", param);
    }

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select sum(amount) sumAmount from TT_INST_ORDER where ((COMMUNICATE_STATUS = ?) AND (STATUS = 'I') AND (COMMUNICATE_TYPE = 'S') AND (GMT_BOOKING_SUBMIT > (sysdate - 1)) AND (ORDER_TYPE = ?))</tt>
     *
     * @param communicateStatusList
     * @param orderType
     * @param channelCodeList
     * @return String @
     */
    @Override
    public Money sumAmountForQueryResult(List<String> communicateStatusList, String orderType, List channelCodeList,
                                         Date startDate, Date endDate) {
        Map param = new HashMap();

        param.put("communicateStatusList", communicateStatusList);
        param.put("orderType", orderType);
        param.put("channelCodeList", channelCodeList);
        param.put("startDate", startDate);
        param.put("endDate", endDate);

        Double sumAmount = getSqlSession().selectOne("MS-INST-ORDER-SUM-AMOUNT-FOR-QUERY-RESULT", param);
        return sumAmount == null ? new Money("0", "AED") : new Money(sumAmount.toString(), "AED");
    }

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, INST_CODE, INST_ORDER_NO, ORDER_TYPE, PAY_MODE, CURRENCY, SUBMIT_PRIORITY, GMT_BOOKING_SUBMIT, ARCHIVE_TEMPLATE_ID, PRODUCT_CODE, PAYMENT_CODE, AMOUNT, STATUS, COMMUNICATE_TYPE, FUND_CHANNEL_CODE, FUND_CHANNEL_API, COMMUNICATE_STATUS, ARCHIVE_BATCH_ID, FLAG, RISK_STATUS, GMT_SUBMIT, GMT_CREATE, GMT_MODIFIED, MEMO, GMT_NEXT_RETRY, RETRY_TIMES, ROUTE_VERSION,  IS_SPLIT, CMF_SEQ_NO, EXTENSION, EXPECT_TIME from TT_INST_ORDER where (ARCHIVE_BATCH_ID = ?) order by INST_ORDER_ID ASC</tt>
     *
     * @param archiveBatchId
     * @return List<InstOrderDO> @
     */
    @Override
    public List<InstOrderDO> loadByArchiveBatchId(Long archiveBatchId) {

        return getSqlSession().selectList("MS-INST-ORDER-LOAD-BY-ARCHIVE-BATCH-ID", archiveBatchId);

    }

    @Override
    public List<InstOrderDO> loadByBatchIdStatus(Long archiveBatchId, Date archiveDate, String communicateStatus,
                                                 String instOrderStatus) {
        Map param = new HashMap();
        param.put("archiveBatchId", archiveBatchId);
        param.put("communicateStatus", communicateStatus);
        param.put("instOrderStatus", instOrderStatus);
        param.put("archiveDate", archiveDate);

        return getSqlSession().selectList("MS-INST-ORDER-LOAD-BY-BATCH-ID-STATUS", param);
    }

    @Override
    public int countArchivePages(Long archiveTemplateId, Long hours, String apiCode,
                                 String communicateType, Date bookingTime, int rownum) {

        Map param = new HashMap();
        param.put("archiveTemplateId", archiveTemplateId);
        param.put("hours", hours);
        param.put("apiCode", apiCode);
        param.put("communicateType", communicateType);
        param.put("bookingTime", bookingTime);
        Paginator paginator = new Paginator();
        paginator.setItemsPerPage(rownum);
        paginator
                .setItems(((Integer) getSqlSession().selectOne("MS-INST-ORDER-QUERY-INST-ORDER-4-ARCHIVE-PAGE-SIZE", param))
                        .intValue());
        return paginator.getPages();
    }

    @Override
    public List<Long> queryInstOrderList4ArchivePage(Long archiveTemplateId, Long hours,
                                                     String apiCode, String communicateType, Date bookingTime, int rownum) {

        Map param = new HashMap();
        param.put("archiveTemplateId", archiveTemplateId);
        param.put("hours", hours);
        param.put("apiCode", apiCode);
        param.put("communicateType", communicateType);
        param.put("bookingTime", bookingTime);
        param.put("rownum", rownum);

        return getSqlSession().selectList("MS-INST-ORDER-QUERY-INST-ORDER-4-ARCHIVE-PAGEING", param);
    }

    @Override
    public int updateTempBatchByInstOrderId(Long tempBatchId, List<Long> instOrderIdList) {
        Map param = new HashMap();
        param.put("tempBatchId", tempBatchId);
        param.put("instOrderIdList", instOrderIdList);
        return getSqlSession().update("MS-INST-ORDER-UPDATE-TEMP-BY-INST-ORDER-ID", param);
    }

    @Override
    public List<InstOrderDO> loadInstOrderIdListByIds(List<Long> instOrderIdList) {
        Map param = new HashMap();
        param.put("instOrderIdList", instOrderIdList);
        return getSqlSession().selectList("MS-INST-ORDER-QUERY-INST-ORDER-LIST-BY-ID", param);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp</tt>
     *
     * @param tempBatchId
     * @param defId
     * @return int @
     */
    @Override
    public int updateBatchId2Default(Long tempBatchId, Long defId) {
        Map param = new HashMap();

        param.put("tempBatchId", tempBatchId);
        param.put("defId", defId);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-BATCH-ID-2-DEFAULT", param);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp where (1 != 1)</tt>
     *
     * @param tempBatchId
     * @param archiveBatchId
     * @param instOrderIdList
     * @return int @
     */
    @Override
    public int updateBatchIdListByTempBatchId(Long tempBatchId, Long archiveBatchId, List instOrderIdList) {
        Map param = new HashMap();

        param.put("tempBatchId", tempBatchId);
        param.put("archiveBatchId", archiveBatchId);
        param.put("instOrderIdList", instOrderIdList);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-BATCH-ID-LIST-BY-TEMP-BATCH-ID", param);
    }

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select count(1) COUNT_NUM from TT_INST_ORDER where ((ARCHIVE_BATCH_ID = ?) AND (STATUS != 'F'))</tt>
     *
     * @param archiveBatchId
     * @return long @
     */
    @Override
    public long countNotFailureByArchiveBatchId(Long archiveBatchId) {

        Long retObj =
                (Long) getSqlSession().selectOne("MS-INST-ORDER-COUNT-NOT-FAILURE-BY-ARCHIVE-BATCH-ID", archiveBatchId);

        if (retObj == null) {
            return 0;
        } else {
            return retObj.longValue();
        }

    }

    @Override
    public PageList queryInstOrders(PageRequest request) {
        Map param = new HashMap();

        param.put("query", request);
        param.put("pageSize", request.getPageSize());
        param.put("pageNum", request.getPageNum());

        Paginator paginator = new Paginator();
        paginator.setItemsPerPage(request.getPageSize());
        paginator.setPage(request.getPageNum());

        paginator.setItems((Integer) getSqlSession().selectOne("MS-INST-ORDER-PAGE-QUERY-COUNT-FOR-PAGING", param));

        PageList pageList = new PageList();
        pageList.setPaginator(paginator);

        if (paginator.getBeginIndex() <= paginator.getItems()) {
            param.put("startRow", paginator.getBeginIndex());
            param.put("endRow", paginator.getEndIndex());
            pageList.addAll(getSqlSession().selectList("MS-INST-ORDER-PAGE-QUERY", param));
        }

        return pageList;
    }


    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp</tt>
     *
     * @param instOrderId
     * @param flag
     * @param preFlag
     * @return int @
     */
    @Override
    public int updateFlagWithOrderIdAndPreFlag(Long instOrderId, String flag, String preFlag) {
        Map param = new HashMap();

        param.put(INST_ORDER_ID, instOrderId);
        param.put("flag", flag);
        param.put("preFlag", preFlag);

        return getSqlSession().update("MS-INST-ORDER-UPDATE-FLAG-WITH-ORDER-ID-AND-PRE-FLAG", param);
    }

    @Override
    public int updateAdvanceStatusWithPreStatus(String advanceStatus, Long instOrderId, String preStatus) {
        Map<String, String> param = new HashMap<>(5);
        param.put("advanceStatus", advanceStatus);
        param.put(INST_ORDER_ID, instOrderId + "");
        param.put("preStatus", preStatus);
        return getSqlSession().update("MS-INST-ORDER-UPDATE-IS-ADVANCE-WITH-INST_ORDER_ID", param);
    }

    @Override
    public int updateFoOrderRetryWithOrderId(String newInstOrderNo, String memo, String instCode, String status,
                                             String communicateStatus, long instOrderId, String oldInstOrderNo) {
        Map param = new HashMap();

        param.put(INST_ORDER_ID, instOrderId);
        param.put("newInstOrderNo", newInstOrderNo);
        param.put("memo", memo);
        param.put(STATUS, status);
        param.put("communicateStatus", communicateStatus);
        param.put("instCode", instCode);
        param.put("oldInstOrderNo", oldInstOrderNo);
        return getSqlSession().update("MS-INST-UPDATE-FO-ORDER-RETRY-ORDER-NO", param);
    }

    @Override
    public List<InstOrderDO> getRefundOrderByFundInOrder(String fundInOrderNo, List<String> orderStatusList) {
        Map param = new HashMap();
        param.put("fundInOrderNo", fundInOrderNo);
        param.put("orderStatusList", orderStatusList);

        return getSqlSession().selectList("MS-INST-ORDER-QUERY-REFUND-BY-FUNDIN-ORDER", param);
    }

    @Override
    public Long insertUnique(UniqueOrderDO uniqueOrderDO) {

        getSqlSession().insert("MS-UNIQUE-ORDER-INSERT", uniqueOrderDO);

        return uniqueOrderDO.getInstOrderId();
    }

    @Override
    public UniqueOrderDO loadUniqueOrderByNo(String instOrderNo) {
        return (UniqueOrderDO) getSqlSession().selectOne("MS-UNIQUE-ORDER-LOAD-BY-NO", instOrderNo);
    }

    @Override
    public List<Long> loadSingleOrder4Query(Date startTime, Date endTime, int maxSize, String orderType,
                                            String fixedTableSuffix) {
        Map param = new HashMap();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        param.put("maxSize", maxSize);
        param.put("orderType", orderType);
        param.put("fixedTableSuffix", fixedTableSuffix);
        return getSqlSession().selectList("MS-LOAD-SINGLE-ORDER-4-QUERY", param);
    }

    @Override
    public List<Long> loadSingleOrder4Send(List<String> channelCodeList, List<String> ignoreChannelList,
                                           String orderType, Date startTime, Date endTime, String fixedTableSuffix, int rownum) {
        Map param = new HashMap();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        param.put("rownum", rownum);
        param.put("orderType", orderType);
        param.put("channelCodeList", channelCodeList);
        param.put("fixedTableSuffix", fixedTableSuffix);
        param.put("ignoreChannelList", ignoreChannelList);
        return getSqlSession().selectList("MS-LOAD-SINGLE-ORDER-4-SEND", param);
    }

    @Override
    public Money getArchiveBatchAmt(Long archiveBatchId) {
        Double archiveAmount = getSqlSession().selectOne("MS-INST-ORDER-GET-ARCHIVE-BATCH-AMT", archiveBatchId);
        return archiveAmount == null ? new Money(BigDecimal.ZERO, "AED") : new Money(archiveAmount.toString(), "AED");
    }

    @Override
    public int getArchiveBatchCnt(Long archiveBatchId) {
        return (Integer) getSqlSession().selectOne("MS-INST-ORDER-GET-ARCHIVE-BATCH-CNT", archiveBatchId);
    }

    @Override
    public List<Long> loadInstOrderIdListByBatchId(Long archiveBatchId) {
        return getSqlSession().selectList("MS-LOAD-ORDER-IDS-BY-ARCHIVE-ID", archiveBatchId);
    }

    @Override
    public List<String> queryFOFinishedDateList(String channelCode) {
        return getSqlSession().selectList("MS-LOAD-FO-FINISHED-DATE-LIST", channelCode);
    }

    @Override
    public List<Long> loadInstOrderIdListByBatchIdAndCurrency(Long archiveBatchId, String currency) {
        Map param = new HashMap();
        param.put("archiveBatchId", archiveBatchId);
        param.put("currency", currency);
        return getSqlSession().selectList("MS-LOAD-ORDER-IDS-BY-BATCH-ID-AND-CURRENCY", param);
    }

    @Override
    public List<String> loadOrderCurrencyListByBatchId(Long archiveBatchId) {
        return getSqlSession().selectList("MS-LOAD-CURRENCY-LIST-BY-BATCH-ID", archiveBatchId);
    }

    @Override
    public int updateBookingSubmitById(Date bookingSubmit, Long instOrderId) {
        Map param = new HashMap();
        param.put("bookingSubmit", bookingSubmit);
        param.put("instOrderId", instOrderId);
        return getSqlSession().update("MS-INST-UPDATE-BOOKING-SUBMIT-BY-ID", param);
    }

    @Override
    public int updateAmountAndExtension(long instOrderId, Money amount, String extension) {
        Map param = new HashMap();
        if(StringUtils.isNotEmpty(extension)){
            param.put("extension",extension);
        }
        param.put("amount", amount.getAmount());
        param.put("instOrderId", instOrderId);
        return getSqlSession().update("MS-INST-UPDATE-AMOUNT-BY-ID", param);
    }

    @Override
    public int updateUniqueOrder(UniqueOrderDO uniqueOrderDO) {
        return getSqlSession().update("MS-UNIQUE-UPDATE-INST-ORDER-NO-BY-ID", uniqueOrderDO);
    }
}
