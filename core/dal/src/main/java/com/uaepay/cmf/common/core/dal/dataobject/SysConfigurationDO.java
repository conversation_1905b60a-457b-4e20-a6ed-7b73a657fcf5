package com.uaepay.cmf.common.core.dal.dataobject;

import lombok.Data;

import java.util.Date;

/**
 * A data object class directly models database table <tt>TM_SYS_CONFIGURATION</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tm_sys_configuration.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR> won
 */
@Data
public class SysConfigurationDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>ATTR_NAME</tt>.
     */
    private String attrName;

    /**
     * This property corresponds to db column <tt>ATTR_VALUE</tt>.
     */
    private String attrValue;

    /**
     * This property corresponds to db column <tt>MEMO</tt>.
     */
    private String memo;

    /**
     * This property corresponds to db column <tt>GMT_CREATED</tt>.
     */
    private Date gmtCreated;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

    public SysConfigurationDO() {}

    public SysConfigurationDO(String attrName, String attrValue, String memo) {
        this.attrName = attrName;
        this.attrValue = attrValue;
        this.memo = memo;
    }
}
