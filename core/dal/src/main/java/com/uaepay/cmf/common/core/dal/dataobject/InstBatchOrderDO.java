package com.uaepay.cmf.common.core.dal.dataobject;

import java.util.Date;

import com.uaepay.common.util.money.Money;
import lombok.Data;

/**
 * A data object class directly models database table <tt>TT_INST_BATCH_ORDER</tt>.
 * <p>
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_inst_batch_order.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR> won
 */
@Data
public class InstBatchOrderDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>ARCHIVE_BATCH_ID</tt>.
     */
    private Long archiveBatchId;

    /**
     * This property corresponds to db column <tt>ARCHIVE_TEMPLATE_ID</tt>.
     */
    private Long archiveTemplateId;

    /**
     * This property corresponds to db column <tt>ORDER_TYPE</tt>.
     */
    private String orderType;

    /**
     * This property corresponds to db column <tt>FUND_CHANNEL_CODE</tt>.
     */
    private String fundChannelCode;

    /**
     * This property corresponds to db column <tt>FUND_CHANNEL_API</tt>.
     */
    private String fundChannelApi;

    /**
     * This property corresponds to db column <tt>PAY_MODE</tt>.
     */
    private String payMode;

    /**
     * This property corresponds to db column <tt>TOTAL_AMOUNT</tt>.
     */
    private Money totalAmount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);

    /**
     * This property corresponds to db column <tt>TOTAL_COUNT</tt>.
     */
    private Integer totalCount;

    /**
     * This property corresponds to db column <tt>STATUS</tt>.
     */
    private String status;

    /**
     * This property corresponds to db column <tt>IS_LOCKED</tt>.
     */
    private String isLocked;

    /**
     * This property corresponds to db column <tt>OPERATOR</tt>.
     */
    private String operator;

    /**
     * This property corresponds to db column <tt>GMT_ARCHIVE</tt>.
     */
    private Date gmtArchive;

    /**
     * This property corresponds to db column <tt>GMT_CREATE</tt>.
     */
    private Date gmtCreate;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

    /**
     * This property corresponds to db column <tt>MEMO</tt>.
     */
    private String memo;

    /**
     * This property corresponds to db column <tt>CHECK_FLAG</tt>.
     */
    private String checkFlag;

    private String batchOrderNo;

    private Integer queryTimes;

    private Date gmtNextRetry;

    /**
     * This property corresponds to db column <tt>EXTENSION</tt>.
     */
    private String extension;

    /**
     * Setter method for property <tt>totalAmount</tt>.
     *
     * @param totalAmount value to be assigned to property totalAmount
     */
    public void setTotalAmount(Money totalAmount) {
        if (totalAmount == null) {
            this.totalAmount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);
        } else {
            this.totalAmount = totalAmount;
        }
    }
}
