package com.uaepay.cmf.common.core.dal.dataobject;

import java.io.Serializable;
import java.util.Iterator;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;

/**
 * Base class for all data objects.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file. Modify the configuration file according to your needs, then run <tt>iwallet-dalgen</tt> to
 * generate this file.
 *
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class BaseDO implements Serializable {
    private static final long serialVersionUID = 741231858441822688L;
    static String DEFAULT_AMOUNT = "0.00";
    static String DEFAULT_CURRENCY = "AED";

    /**
     * @return
     *
     * @see Object#toString()
     */
    @Override
    public String toString() {
        try {
            Map props = BeanUtils.describe(this);
            Iterator iProps = props.keySet().iterator();
            StringBuilder retBuffer = new StringBuilder();

            while (iProps.hasNext()) {
                String key = (String)iProps.next();

                // skip false property "class"
                if ("class".equals(key)) {
                    continue;
                }

                retBuffer.append(key).append("=[").append(props.get(key)).append("]");

                if (iProps.hasNext()) {
                    retBuffer.append(", ");
                }
            }

            return retBuffer.toString();
        } catch (Exception e) {
            return super.toString();
        }
    }
}
