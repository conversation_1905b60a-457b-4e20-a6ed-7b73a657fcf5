package com.uaepay.cmf.common.core.dal.configuration;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.uaepay.basis.sequenceutil.IDGen;
import com.uaepay.basis.sequenceutil.segment.SegmentIDGenImpl;
import com.uaepay.basis.sequenceutil.segment.dao.IDAllocDao;
import com.uaepay.basis.sequenceutil.segment.dao.impl.IDAllocDaoImpl;

/**
 * <p>
 * id生成器配置.
 * </p>
 *
 * <AUTHOR>
 * @version $Id: SeqConstant.java, v 0.1 2019-7-24 下午01:15:21
 */
@Configuration
public class IdGenConfiguration {

    @Bean
    public IDAllocDao idAllocDao(@Qualifier("cmfDataSource") DataSource dataSource) {
        return new IDAllocDaoImpl(dataSource);
    }

    @Bean
    public IDGen idGen(IDAllocDao idAllocDao) {
        SegmentIDGenImpl result = new SegmentIDGenImpl();
        result.setDao(idAllocDao);
        return result;
    }

}
