package com.uaepay.cmf.common.core.dal.daointerface;

import com.uaepay.biz.common.util.PageList;
import com.uaepay.biz.common.util.QueryBase;
import com.uaepay.cmf.common.core.dal.dataobject.RefundOrderDO;

/**
 * A dao interface provides methods to access database table <tt>TT_REFUND_ORDER</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_refund_order.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public interface RefundOrderDAO {
    /**
     * Insert one <tt>RefundOrderDO</tt> object to DB table <tt>TT_REFUND_ORDER</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_REFUND_ORDER(INST_ORDER_ID,FUNDIN_ORDER_NO,FUNDIN_REAL_AMOUNT,FUNDIN_DATE,GMT_MODIFIED) values (?, ?, ?, ?, systimestamp)</tt>
     *
     * @param refundOrder
     * @return long @
     */
    long insert(RefundOrderDO refundOrder);

    /**
     * Delete records from DB table <tt>TT_REFUND_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>delete from TT_REFUND_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return int @
     */
    int delete(long instOrderId);

    /**
     * Query DB table <tt>TT_REFUND_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, FUNDIN_ORDER_NO, FUNDIN_REAL_AMOUNT, FUNDIN_DATE, GMT_MODIFIED from TT_REFUND_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return RefundOrderDO @
     */
    RefundOrderDO loadById(long instOrderId);

    /**
     * Query DB table <tt>TT_REFUND_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, FUNDIN_ORDER_NO, FUNDIN_REAL_AMOUNT, FUNDIN_DATE, GMT_MODIFIED from TT_REFUND_ORDER where (INST_ORDER_ID = ?) for update</tt>
     *
     * @param instOrderId
     * @return RefundOrderDO @
     */
    RefundOrderDO lockedById(long instOrderId);

}