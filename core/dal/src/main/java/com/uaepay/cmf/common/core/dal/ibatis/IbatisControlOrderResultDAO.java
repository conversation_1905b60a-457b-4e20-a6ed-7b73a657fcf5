package com.uaepay.cmf.common.core.dal.ibatis;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.mybatis.spring.support.SqlSessionDaoSupport;

import com.uaepay.cmf.common.core.dal.daointerface.ControlOrderResultDAO;
import com.uaepay.cmf.common.core.dal.dataobject.ControlOrderResultDO;

/**
 * An ibatis based implementation of dao interface
 * <tt>com.uaepay.cmf.common.core.dal.daointerface.ControlOrderResultDAO</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_control_order_result.xml</tt>). Modify the configuration file according to your
 * needs, then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public class IbatisControlOrderResultDAO extends SqlSessionDaoSupport implements ControlOrderResultDAO {

    /**
     * Query DB table <tt>TT_CONTROL_ORDER_RESULT</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select RESULT_ID, CONTROL_ORDER_ID, AMOUNT, INST_ORDER_NO, INST_RESULT_CODE, API_TYPE, STATUS, API_RESULT_CODE, API_SUB_RESULT_CODE, RESULT_MESSAGE, MEMO, EXTENSION, GMT_CREATE, GMT_MODIFIED from TT_CONTROL_ORDER_RESULT where (CONTROL_ORDER_ID = ?)</tt>
     *
     * @param controlOrderId
     * @return ControlOrderResultDO @
     */
    @Override
    public List<ControlOrderResultDO> loadWithControlOrderId(long controlOrderId) {
        return getSqlSession().selectList("MS-CONTROL-ORDER-RESULT-LOAD-WITH-CONTROL-ORDER-ID", controlOrderId);

    }

    /**
     * Query DB table <tt>TT_CONTROL_ORDER_RESULT</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select RESULT_ID, CONTROL_ORDER_ID, AMOUNT, INST_ORDER_NO, INST_RESULT_CODE, API_TYPE, STATUS, API_RESULT_CODE, API_SUB_RESULT_CODE, RESULT_MESSAGE, MEMO, EXTENSION, GMT_CREATE, GMT_MODIFIED from TT_CONTROL_ORDER_RESULT where (INST_ORDER_NO = ?)</tt>
     *
     * @param instOrderNo
     * @return ControlOrderResultDO @
     */
    @Override
    public ControlOrderResultDO loadWithInstOrderNo(String instOrderNo) {

        return (ControlOrderResultDO)getSqlSession().selectOne("MS-CONTROL-ORDER-RESULT-LOAD-WITH-INST-ORDER-NO",
            instOrderNo);

    }

    /**
     * Insert one <tt>ControlOrderResultDO</tt> object to DB table <tt>TT_CONTROL_ORDER_RESULT</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_CONTROL_ORDER_RESULT(RESULT_ID,CONTROL_ORDER_ID,AMOUNT,INST_ORDER_NO,INST_RESULT_CODE,API_TYPE,STATUS,API_RESULT_CODE,API_SUB_RESULT_CODE,RESULT_MESSAGE,MEMO,EXTENSION,GMT_CREATE,GMT_MODIFIED) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp)</tt>
     *
     * @param controlOrderResult
     * @return long @
     */
    @Override
    public long insert(ControlOrderResultDO controlOrderResult) {
        if (controlOrderResult == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-CONTROL-ORDER-RESULT-INSERT", controlOrderResult);

        return controlOrderResult.getResultId();
    }

}