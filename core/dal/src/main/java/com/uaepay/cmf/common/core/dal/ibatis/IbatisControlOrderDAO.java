package com.uaepay.cmf.common.core.dal.ibatis;

import com.uaepay.cmf.common.core.dal.daointerface.ControlOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.ControlOrderDO;
import org.mybatis.spring.support.SqlSessionDaoSupport;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * An ibatis based implementation of dao interface <tt>com.uaepay.cmf.common.core.dal.daointerface.ControlOrderDAO</tt>.
 * <p>
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_control_order.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public class IbatisControlOrderDAO extends SqlSessionDaoSupport implements ControlOrderDAO {

    /**
     * Query DB table <tt>TT_CONTROL_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ORDER_ID, FUND_CHANNEL_CODE, PRODUCT_CODE, INST_CODE, PRE_REQUEST_NO, PAY_MODE, REQUEST_NO, REQUEST_TYPE, API_TYPE, INST_ORDER_NO, AMOUNT, STATUS, RETRY_STATUS, GMT_CREATE, GMT_MODIFIED, EXTENSION, NOTIFY_STATUS, SOURCE_CODE, COMMUNICATE_STATUS, FLAG, MEMO from TT_CONTROL_ORDER where (REQUEST_NO = ?)</tt>
     *
     * @param requestNo
     * @return ControlOrderDO @
     */
    @Override
    public ControlOrderDO loadWithRequestNo(String requestNo) {

        return (ControlOrderDO) getSqlSession().selectOne("MS-CONTROL-ORDER-LOAD-WITH-REQUEST-NO", requestNo);

    }

    @Override
    public ControlOrderDO loadWithRequestNoAndApiType(String requestNo, String apiType) {

        Map param = new HashMap();

        param.put("requestNo", requestNo);
        param.put("apiType", apiType);

        return (ControlOrderDO) getSqlSession().selectOne("MS-CONTROL-ORDER-LOAD-WITH-REQUEST-NO-API-TYPE", param);
    }


    /**
     * Query DB table <tt>TT_CONTROL_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ORDER_ID, FUND_CHANNEL_CODE, PRODUCT_CODE, INST_CODE, PRE_REQUEST_NO, PAY_MODE, REQUEST_NO, REQUEST_TYPE, API_TYPE, INST_ORDER_NO, AMOUNT, STATUS, RETRY_STATUS, GMT_CREATE, GMT_MODIFIED, EXTENSION, NOTIFY_STATUS, SOURCE_CODE, COMMUNICATE_STATUS, FLAG, MEMO from TT_CONTROL_ORDER where (INST_ORDER_NO = ?)</tt>
     *
     * @param instOrderNo
     * @return ControlOrderDO @
     */
    @Override
    public ControlOrderDO loadWithInstOrderNo(String instOrderNo) {

        return (ControlOrderDO) getSqlSession().selectOne("MS-CONTROL-ORDER-LOAD-WITH-INST-ORDER-NO", instOrderNo);

    }

    /**
     * Query DB table <tt>TT_CONTROL_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ORDER_ID, FUND_CHANNEL_CODE, PRODUCT_CODE, INST_CODE, PRE_REQUEST_NO, PAY_MODE, REQUEST_NO, REQUEST_TYPE, API_TYPE, INST_ORDER_NO, AMOUNT, STATUS, RETRY_STATUS, GMT_CREATE, GMT_MODIFIED, EXTENSION, NOTIFY_STATUS, SOURCE_CODE, COMMUNICATE_STATUS, FLAG, MEMO from TT_CONTROL_ORDER where (ORDER_ID = ?)</tt>
     *
     * @param orderId
     * @return ControlOrderDO @
     */
    @Override
    public ControlOrderDO loadWithOrderId(long orderId) {

        return (ControlOrderDO) getSqlSession().selectOne("MS-CONTROL-ORDER-LOAD-WITH-ORDER-ID", orderId);

    }

    /**
     * Insert one <tt>ControlOrderDO</tt> object to DB table <tt>TT_CONTROL_ORDER</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_CONTROL_ORDER(ORDER_ID,FUND_CHANNEL_CODE,PRODUCT_CODE,INST_CODE,PRE_REQUEST_NO,PAY_MODE,REQUEST_NO,REQUEST_TYPE,API_TYPE,INST_ORDER_NO,AMOUNT,STATUS,RETRY_STATUS,NOTIFY_STATUS,SOURCE_CODE,COMMUNICATE_STATUS,FLAG,GMT_CREATE,GMT_MODIFIED,EXTENSION,MEMO) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp, ?, ?)</tt>
     *
     * @param controlOrder
     * @return long @
     */
    @Override
    public long insert(ControlOrderDO controlOrder) {
        if (controlOrder == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-CONTROL-ORDER-INSERT", controlOrder);

        return controlOrder.getOrderId();
    }

    /**
     * Update DB table <tt>TT_CONTROL_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_CONTROL_ORDER set gmt_modified=systimestamp, STATUS=? where (ORDER_ID = ?)</tt>
     *
     * @param orderId
     * @param targetStatus
     * @param preStatus
     * @return int @
     */
    @Override
    public int updateStatusById(long orderId, String targetStatus, String preStatus) {
        Map param = new HashMap();

        param.put("orderId", orderId);
        param.put("targetStatus", targetStatus);
        param.put("preStatus", preStatus);

        return getSqlSession().update("MS-CONTROL-ORDER-UPDATE-STATUS-BY-ID", param);
    }

    @Override
    public int updateCommunicateStatusByIdAndPreStatus(long orderId, String communicateStatus, String preStatus) {
        Map param = new HashMap();

        param.put("communicateStatus", communicateStatus);
        param.put("orderId", orderId);
        param.put("preStatus", preStatus);
        return getSqlSession().update("MS-CONTROL-ORDER-UPDATE-COMMUNICATE-STATUS-BY-ID-PRE-STATUS", param);
    }

    /**
     * @param requestNo
     * @return @
     */
    @Override
    public int updateExtensionByRequestNo(String extension, String requestNo) {
        Map param = new HashMap();
        param.put("extension", extension);
        param.put("requestNo", requestNo);

        return getSqlSession().update("MS-CONTROL-ORDER-UPDATE-EXTENSION-BY-REQUEST-NO", param);
    }

    @Override
    public List<Long> loadControlOrder4Query(String apiType, Date startTime, Date endTime, int maxSize) {
        Map param = new HashMap();
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        param.put("apiType", apiType);
        param.put("maxSize", maxSize);
        return getSqlSession().selectList("MS-CONTROL-ORDER-LOAD-4-QUERY", param);
    }

    @Override
    public int updateRetryInfoById(int retryTimes, Date retryDateTime, long orderId) {
        Map param = new HashMap();
        param.put("retryTimes", retryTimes);
        param.put("gmtNextRetry", retryDateTime);
        param.put("orderId", orderId);
        return getSqlSession().update("MS-CONTROL-ORDER-UPDATE-RETRY-TIME-BY-ID", param);
    }

    @Override
    public int updateFlagWithOrderIdAndPreFlag(Long orderId, String flag, String preFlag) {
        Map param = new HashMap();
        param.put("orderId", orderId);
        param.put("flag", flag);
        param.put("preFlag", preFlag);
        return getSqlSession().update("MS-CONTROL-ORDER-UPDATE-FLAG-WITH-ID-AND-PRE-FLAG", param);
    }
}