package com.uaepay.cmf.common.core.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date InstBatchOrderPageQuery.java v1.0
 */
@Data
public class InstBatchOrderPageQuery implements Serializable {
    private static final long serialVersionUID = 4318045481854045762L;

    private String channelCode;

    private String status;

    private Date gmtStart;

    private Date gmtEnd;

    private Integer pageNum;

    private Integer pageSize;
}
