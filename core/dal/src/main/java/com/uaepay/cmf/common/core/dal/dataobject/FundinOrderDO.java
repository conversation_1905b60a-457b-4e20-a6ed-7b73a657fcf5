package com.uaepay.cmf.common.core.dal.dataobject;

import lombok.Data;

import java.util.Date;

/**
 * A data object class directly models database table <tt>TT_FUNDIN_ORDER</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_fundin_order.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
@Data
public class FundinOrderDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>INST_ORDER_ID</tt>.
     */
    private Long instOrderId;

    /**
     * This property corresponds to db column <tt>CARD_TYPE</tt>.
     */
    private String cardType;

    /**
     * This property corresponds to db column <tt>PAYER_INST_CODE</tt>.
     */
    private String payerInstCode;

    /**
     * This property corresponds to db column <tt>CONTRACT_NO</tt>.
     */
    private String contractNo;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

    /**
     * This property corresponds to db column <tt>GMT_CREATED</tt>.
     */
    private Date gmtCreated;

}
