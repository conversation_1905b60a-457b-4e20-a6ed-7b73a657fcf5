package com.uaepay.cmf.common.core.dal.dataobject;

import lombok.Data;

import java.util.Date;

/**
 * A data object class directly models database table <tt>TT_FUNDOUT_ORDER</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_fundout_order.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
@Data
public class FundoutOrderDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>INST_ORDER_ID</tt>.
     */
    private Long instOrderId;

    /**
     * This property corresponds to db column <tt>BANK_CODE</tt>.
     */
    private String bankCode;

    /**
     * This property corresponds to db column <tt>BANK_NAME</tt>.
     */
    private String bankName;

    /**
     * This property corresponds to db column <tt>BANK_BRANCH</tt>.
     */
    private String bankBranch;

    /**
     * This property corresponds to db column <tt>BANK_BRANCH_CODE</tt>.
     */
    private String bankBranchCode;

    /**
     * This property corresponds to db column <tt>BANK_PROVINCE</tt>.
     */
    private String bankProvince;

    /**
     * This property corresponds to db column <tt>BANK_CITY</tt>.
     */
    private String bankCity;

    /**
     * This property corresponds to db column <tt>AREA_CODE</tt>.
     */
    private String areaCode;

    /**
     * This property corresponds to db column <tt>ACCOUNT_TYPE</tt>.
     */
    private String accountType;

    /**
     * This property corresponds to db column <tt>ACCOUNT_NAME</tt>.
     */
    private String accountName;

    /**
     * This property corresponds to db column <tt>ACCOUNT_NO</tt>.
     */
    private String accountNo;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * iban帐号
     */
    private String ibanNo;

    /**
     * 银行交换码
     */
    private String swiftCode;

    /**
     * 受益人地址
     */
    private String beneficiaryAddress;

    /**
     * 中间行
     */
    private String intermediaryBank;

    /**
     * This property corresponds to db column <tt>CARD_TYPE</tt>.
     */
    private String cardType;

    /**
     * This property corresponds to db column <tt>AGREEMENT_NO</tt>.
     */
    private String agreementNo;

    /**
     * This property corresponds to db column <tt>PURPOSE</tt>.
     */
    private String purpose;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

    /**
     * This property corresponds to db column <tt>GMT_CREATE</tt>.
     */
    private Date gmtCreate;

    /**
     * This property corresponds to db column <tt>PT_ID</tt>.
     */
    private String ptId;

}
