package com.uaepay.cmf.common.core.dal.daointerface;

import com.uaepay.cmf.common.core.dal.dataobject.ChannelCodeMappingDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ChannelCodeMappingDAO {
    
    /**
     * Query enabled mapping rules by old channel code
     * 
     * @param oldChannelCode original channel code
     * @return mapping rule list, sorted by priority desc
     */
    List<ChannelCodeMappingDO> selectByOldChannelCode(@Param("oldChannelCode") String oldChannelCode);

    /**
     * Insert new rule
     */
    void insert(ChannelCodeMappingDO mapping);

    /**
     * Update rule
     */
    int updateById(ChannelCodeMappingDO mapping);

    /**
     * Update rule status
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * Page query channel code mapping rules
     *
     * @param oldChannelCode original channel code (optional)
     * @param newChannelCode new channel code (optional)
     * @param status rule status (optional)
     * @param offset start position
     * @param limit page size
     * @return mapping rule list
     */
    List<ChannelCodeMappingDO> pageQuery(@Param("oldChannelCode") String oldChannelCode,
                                        @Param("newChannelCode") String newChannelCode,
                                        @Param("status") String status,
                                        @Param("offset") Integer offset,
                                        @Param("limit") Integer limit);

    /**
     * Count total records for page query
     *
     * @param oldChannelCode original channel code (optional)
     * @param newChannelCode new channel code (optional)
     * @param status rule status (optional)
     * @return total count
     */
    int pageCount(@Param("oldChannelCode") String oldChannelCode,
                 @Param("newChannelCode") String newChannelCode,
                 @Param("status") String status);

    int deleteByOldChannelCode(@Param("oldChannelCode") String oldChannelCode);


    List<ChannelCodeMappingDO> selectAllValidRules();
} 