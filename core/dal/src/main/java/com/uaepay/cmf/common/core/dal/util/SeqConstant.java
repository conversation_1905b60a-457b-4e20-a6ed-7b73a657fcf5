package com.uaepay.cmf.common.core.dal.util;

/**
 * <p>
 * Sequence常量.
 * </p>
 *
 * <AUTHOR> yun
 * @version $Id: SeqConstant.java, v 0.1 2019-7-24 下午01:15:21
 */
public class SeqConstant {
    public static final String SEQ_TM_FUND_CHANNEL_EXT = "SEQ_TM_FUND_CHANNEL_EXT";

    // MS-FUND-CHANNEL-PROPERTY-INSERT
    public static final String SEQ_CMF_CHANNELS = "SEQ_CMF_CHANNELS";

    // MS-FUND-CHANNEL-API-PARAM-INSERT
    public static final String SEQ_PARAM_ID = "SEQ_PARAM_ID";

    // MS-SCHEDULING-BALANCING-LOG-INSERT
    public static final String SEQ_SCHEDULING_BALANCING_LOG = "SEQ_SCHEDULING_BALANCING_LOG";

    // MS-API-NO-MODE-INSERT
    public static final String SEQ_TM_API_NO_MODE = "SEQ_TM_API_NO_MODE";

    // MS-INST-ARCHIVE-TEMPLATE-INSERT
    public static final String SEQ_INST_ARCHIVE_TEMPLATE = "SEQ_INST_ARCHIVE_TEMPLATE";

    // MS-SCHEDULING-TASK-BALANCING-INSERT
    public static final String SEQ_SCHEDULING_TASK_BALANCING = "SEQ_SCHEDULING_TASK_BALANCING";

    // MS-API-RESULT-CODE-INSERT
    public static final String SEQ_API_RESULT_CODE = "SEQ_API_RESULT_CODE";

    // MS-FC-TARGET-INST-RELATION-INSERT
    public static final String SEQ_TR_FC_TARGET_INST_RELATION = "SEQ_TR_FC_TARGET_INST_RELATION";

    // MS-CHANNEL-TRANS-INFO-INSERT
    public static final String SEQ_TM_CHANNEL_TRANS_INFO = "SEQ_TM_CHANNEL_TRANS_INFO";

    // MS-SCHEDULING-TASK-REDUCE-INSERT
    public static final String SEQ_SCHEDULING_TASK_REDUCE = "SEQ_SCHEDULING_TASK_REDUCE";

    // MS-MONITOR-LOG-INSERT
    public static final String SEQ_MONITOR_LOG = "SEQ_MONITOR_LOG";

    // MS-ROUTE-RULE-FACTOR-INSERT
    public static final String SEQ_TM_ROUTE_RULE_FACTOR = "SEQ_TM_ROUTE_RULE_FACTOR";

    // MS-FUND-CHANNEL-MAINTAIN-INSERT
    public static final String SEQ_TM_FUND_CHANNEL_MAINTAIN = "SEQ_TM_FUND_CHANNEL_MAINTAIN";

}
