package com.uaepay.cmf.common.core.dal.ibatis;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.mybatis.spring.support.SqlSessionDaoSupport;

import com.uaepay.cmf.common.core.dal.daointerface.InstOrderResultDAO;
import com.uaepay.cmf.common.core.dal.dataobject.InstOrderResultDO;

/**
 * An ibatis based implementation of dao interface
 * <tt>com.uaepay.cmf.common.core.dal.daointerface.InstOrderResultDAO</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_inst_order_result.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public class IbatisInstOrderResultDAO extends SqlSessionDaoSupport implements InstOrderResultDAO {

    /**
     * Insert one <tt>InstOrderResultDO</tt> object to DB table <tt>TT_INST_ORDER_RESULT</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_INST_ORDER_RESULT(RESULT_ID,BATCH_RESULT_ID,INST_ORDER_ID,INST_SEQ_NO,ORDER_TYPE,REAL_AMOUNT,BATCH_TYPE,INST_ORDER_NO,REAL_CURRENCY,ACCOUNT_NAME,ACCOUNT_NO,CARD_TYPE,ORGI_INST_ORDER_NO,COMPARE_STATUS,OPERATE_STATUS,FUND_CHANNEL_CODE,INST_STATUS,GLIDE_STATUS,INST_RESULT_CODE,GMT_MODIFIED,GMT_CREATE,MEMO,ARCHIVE_BATCH_ID,DIFF_MSG,FUNDIN_ORGI_INST_ORDER_NO,SYNC_CHANNEL_STATUS,NOTIFY_BANKORDER_STATUS,EXTENSION,API_RESULT_CODE,API_RESULT_SUB_CODE,API_TYPE,RISK_FLAG) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</tt>
     *
     * @param instOrderResult
     * @return Long @
     */
    @Override
    public Long insert(InstOrderResultDO instOrderResult) {
        if (instOrderResult == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-INST-ORDER-RESULT-INSERT", instOrderResult);

        return instOrderResult.getResultId();
    }

    /**
     * Query DB table <tt>TT_INST_ORDER_RESULT</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select RESULT_ID, BATCH_RESULT_ID, INST_ORDER_ID, INST_SEQ_NO, ORDER_TYPE, REAL_AMOUNT, BATCH_TYPE, INST_ORDER_NO, REAL_CURRENCY, ACCOUNT_NAME, ACCOUNT_NO, CARD_TYPE, ORGI_INST_ORDER_NO, COMPARE_STATUS, OPERATE_STATUS, FUND_CHANNEL_CODE, INST_STATUS, GLIDE_STATUS, INST_RESULT_CODE, GMT_MODIFIED, GMT_CREATE, MEMO, ARCHIVE_BATCH_ID, DIFF_MSG, FUNDIN_ORGI_INST_ORDER_NO, SYNC_CHANNEL_STATUS, NOTIFY_BANKORDER_STATUS, EXTENSION, API_RESULT_CODE, API_RESULT_SUB_CODE, API_TYPE, RISK_FLAG from TT_INST_ORDER_RESULT where (RESULT_ID = ?)</tt>
     *
     * @param resultId
     * @return InstOrderResultDO @
     */
    @Override
    public InstOrderResultDO loadById(Long resultId) {
        return (InstOrderResultDO)getSqlSession().selectOne("MS-INST-ORDER-RESULT-LOAD-BY-ID", resultId);
    }

    /**
     * Query DB table <tt>TT_INST_ORDER_RESULT</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select RESULT_ID, BATCH_RESULT_ID, INST_ORDER_ID, INST_SEQ_NO, ORDER_TYPE, REAL_AMOUNT, BATCH_TYPE, INST_ORDER_NO, REAL_CURRENCY, ACCOUNT_NAME, ACCOUNT_NO, CARD_TYPE, ORGI_INST_ORDER_NO, COMPARE_STATUS, OPERATE_STATUS, FUND_CHANNEL_CODE, INST_STATUS, GLIDE_STATUS, INST_RESULT_CODE, GMT_MODIFIED, GMT_CREATE, MEMO, ARCHIVE_BATCH_ID, DIFF_MSG, FUNDIN_ORGI_INST_ORDER_NO, SYNC_CHANNEL_STATUS, NOTIFY_BANKORDER_STATUS, EXTENSION, API_RESULT_CODE, API_RESULT_SUB_CODE, API_TYPE, RISK_FLAG from TT_INST_ORDER_RESULT where (RESULT_ID = ?) for update</tt>
     *
     * @param resultId
     * @return InstOrderResultDO @
     */
    @Override
    public InstOrderResultDO lockedById(Long resultId) {
        return (InstOrderResultDO)getSqlSession().selectOne("MS-INST-ORDER-RESULT-LOCKED-BY-ID", resultId);
    }

    /**
     * Query DB table <tt>TT_INST_ORDER_RESULT</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select RESULT_ID, BATCH_RESULT_ID, INST_ORDER_ID, INST_SEQ_NO, ORDER_TYPE, REAL_AMOUNT, BATCH_TYPE, INST_ORDER_NO, REAL_CURRENCY, ACCOUNT_NAME, ACCOUNT_NO, CARD_TYPE, ORGI_INST_ORDER_NO, COMPARE_STATUS, OPERATE_STATUS, FUND_CHANNEL_CODE, INST_STATUS, GLIDE_STATUS, INST_RESULT_CODE, GMT_MODIFIED, GMT_CREATE, MEMO, ARCHIVE_BATCH_ID, DIFF_MSG, FUNDIN_ORGI_INST_ORDER_NO, SYNC_CHANNEL_STATUS, NOTIFY_BANKORDER_STATUS, EXTENSION, API_RESULT_CODE, API_RESULT_SUB_CODE, API_TYPE, RISK_FLAG from TT_INST_ORDER_RESULT where ((INST_ORDER_ID = ?) AND (INST_STATUS != 'Q')) order by RESULT_ID DESC</tt>
     *
     * @param instOrderId
     * @return List<InstOrderResultDO> @
     */
    @Override
    public List<InstOrderResultDO> loadByOrder(Long instOrderId) {
        return getSqlSession().selectList("MS-INST-ORDER-RESULT-LOAD-BY-ORDER", instOrderId);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER_RESULT</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER_RESULT set gmt_modified=systimestamp, RISK_FLAG=? where (RESULT_ID = ?)</tt>
     *
     * @param riskFlag
     * @param resultId
     * @return int @
     */
    @Override
    public int updateRiskFlagById(String riskFlag, Long resultId) {
        Map param = new HashMap();

        param.put("riskFlag", riskFlag);
        param.put("resultId", resultId);

        return getSqlSession().update("MS-INST-ORDER-RESULT-UPDATE-RISK-FLAG-BY-ID", param);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER_RESULT</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER_RESULT set gmt_modified=systimestamp, OPERATE_STATUS=? where (RESULT_ID = ?)</tt>
     *
     * @param operateStatus
     * @param resultId
     * @return int @
     */
    @Override
    public int updateOperateStatusById(String operateStatus, Long resultId) {
        Map param = new HashMap();

        param.put("operateStatus", operateStatus);
        param.put("resultId", resultId);

        return getSqlSession().update("MS-INST-ORDER-RESULT-UPDATE-OPERATE-STATUS-BY-ID", param);
    }

    /**
     * Update DB table <tt>TT_INST_ORDER_RESULT</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER_RESULT set gmt_modified=systimestamp</tt>
     *
     * @param to
     * @param ids
     * @param from
     * @return int @
     */
    @Override
    public int updateOperateStatusByResultIds(String to, List ids, String from) {
        Map param = new HashMap();

        param.put("to", to);
        param.put("ids", ids);
        param.put("from", from);

        return getSqlSession().update("MS-INST-ORDER-RESULT-UPDATE-OPERATE-STATUS-BY-RESULT-IDS", param);
    }

    /**
     * Query DB table <tt>TT_INST_ORDER_RESULT</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select RESULT_ID, BATCH_RESULT_ID, INST_ORDER_ID, INST_SEQ_NO, ORDER_TYPE, REAL_AMOUNT, BATCH_TYPE, INST_ORDER_NO, REAL_CURRENCY, ACCOUNT_NAME, ACCOUNT_NO, CARD_TYPE, ORGI_INST_ORDER_NO, COMPARE_STATUS, OPERATE_STATUS, FUND_CHANNEL_CODE, INST_STATUS, GLIDE_STATUS, INST_RESULT_CODE, GMT_MODIFIED, GMT_CREATE, MEMO, ARCHIVE_BATCH_ID, DIFF_MSG, FUNDIN_ORGI_INST_ORDER_NO, SYNC_CHANNEL_STATUS, NOTIFY_BANKORDER_STATUS, EXTENSION, API_RESULT_CODE, API_RESULT_SUB_CODE, API_TYPE, RISK_FLAG from TT_INST_ORDER_RESULT where (INST_ORDER_ID = ?) order by INST_ORDER_ID DESC</tt>
     *
     * @param instOrderId
     * @return List<InstOrderResultDO> @
     */
    @Override
    public List<InstOrderResultDO> listByInstOrderId(Long instOrderId) {
        return getSqlSession().selectList("MS-INST-ORDER-RESULT-LIST-BY-INST-ORDER-ID", instOrderId);
    }

    @Override
    public List<InstOrderResultDO> loadByInstOrderIdAndResult(Long instOrderId, String apiType, String apiResultCode, String apiResultSubCode) {
        Map param = new HashMap(6);

        param.put("instOrderId", instOrderId);
        param.put("apiType", apiType);
        param.put("apiResultCode", apiResultCode);
        param.put("apiResultSubCode", apiResultSubCode);
        return getSqlSession().selectList("MS-INST-ORDER-RESULT-LIST-BY-INST-ORDER-ID-AND-RESULT", param);
    }

    @Override
    public int updateResultById(InstOrderResultDO result, Long resultId) {
        Map param = new HashMap();

        param.put("result", result);
        param.put("resultId", resultId);
        return getSqlSession().update("MS-INST-ORDER-RESULT-UPDATE-BY-RESULT-ID", param);
    }
}