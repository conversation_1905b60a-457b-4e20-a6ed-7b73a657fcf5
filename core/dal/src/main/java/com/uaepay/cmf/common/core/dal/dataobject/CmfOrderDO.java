package com.uaepay.cmf.common.core.dal.dataobject;

import java.util.Date;

import com.uaepay.common.util.money.Money;
import lombok.Data;

/**
 * A data object class directly models database table <tt>TT_CMF_ORDER</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 *
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_cmf_order.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
@Data
public class CmfOrderDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>CMF_SEQ_NO</tt>.
     */
    private String cmfSeqNo;

    /**
     * This property corresponds to db column <tt>PAYMENT_SEQ_NO</tt>.
     */
    private String paymentSeqNo;

    /**
     * This property corresponds to db column <tt>REQUEST_BATCH_NO</tt>.
     */
    private String requestBatchNo;

    /**
     * This property corresponds to db column <tt>ORDER_TYPE</tt>.
     */
    private String orderType;

    /**
     * This property corresponds to db column <tt>PRODUCT_CODE</tt>.
     */
    private String productCode;

    /**
     * This property corresponds to db column <tt>PAYMENT_CODE</tt>.
     */
    private String paymentCode;

    /**
     * This property corresponds to db column <tt>MEMBER_ID</tt>.
     */
    private String memberId;

    /**
     * This property corresponds to db column <tt>AMOUNT</tt>.
     */
    private Money amount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);

    /**
     * This property corresponds to db column <tt>INST_CODE</tt>.
     */
    private String instCode;

    /**
     * This property corresponds to db column <tt>PAYMENT_NOTIFY_STATUS</tt>.
     */
    private String paymentNotifyStatus;

    /**
     * This property corresponds to db column <tt>STATUS</tt>.
     */
    private String status;

    /**
     * This property corresponds to db column <tt>OPERATOR</tt>.
     */
    private String operator;

    /**
     * This property corresponds to db column <tt>GMT_CREATE</tt>.
     */
    private Date gmtCreate;

    /**
     * This property corresponds to db column <tt>BIZ_DATE</tt>.
     */
    private String bizDate;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

    /**
     * This property corresponds to db column <tt>MEMO</tt>.
     */
    private String memo;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * This property corresponds to db column <tt>CONFIRM_STATUS</tt>.
     */
    private String confirmStatus;

    /**
     * This property corresponds to db column <tt>PAY_MODE</tt>.
     */
    private String payMode;

    /**
     * This property corresponds to db column <tt>ORGI_PAYMENT_SEQ_NO</tt>.
     */
    private String orgiPaymentSeqNo;

    /**
     * This property corresponds to db column <tt>EXTENSION</tt>.
     */
    private String extension;

    /**
     * This property corresponds to db column <tt>SETTLEMENT_ID</tt>.
     */
    private String settlementId;

    /**
     * This property corresponds to db column <tt>ORGI_SETTLEMENT_ID</tt>.
     */
    private String orgiSettlementId;

    /**
     * This property corresponds to db column <tt>PAYMENT_VOUCHER_NO</tt>.
     */
    private String paymentVoucherNo;

}
