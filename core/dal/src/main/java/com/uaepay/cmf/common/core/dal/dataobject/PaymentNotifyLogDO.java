package com.uaepay.cmf.common.core.dal.dataobject;

import lombok.Data;

import java.util.Date;

/**
 * A data object class directly models database table <tt>TT_PAYMENT_NOTIFY_LOG</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_payment_notify_log.xml</tt>). Modify the configuration file according to your
 * needs, then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR> won
 */
@Data
public class PaymentNotifyLogDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>NOTIFY_LOG_ID</tt>.
     */
    private Long notifyLogId;

    /**
     * This property corresponds to db column <tt>CHANNEL_SEQ_NO</tt>.
     */
    private String channelSeqNo;

    /**
     * This property corresponds to db column <tt>NOTIFY_RESULT</tt>.
     */
    private String notifyResult;

    /**
     * This property corresponds to db column <tt>GMT_CREATE</tt>.
     */
    private Date gmtCreate;

    /**
     * This property corresponds to db column <tt>MEMO</tt>.
     */
    private String memo;
}
