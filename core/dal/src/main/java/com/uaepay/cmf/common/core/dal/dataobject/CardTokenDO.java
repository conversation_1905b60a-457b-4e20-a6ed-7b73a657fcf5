package com.uaepay.cmf.common.core.dal.dataobject;

import lombok.Data;

import java.util.Date;

@Data
public class CardTokenDO extends BaseDO {
    private static final long serialVersionUID = -1770354067501352154L;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.card_token_id
     *
     * @mbg.generated Sat Mar 28 15:09:02 GST 2020
     */
    private String cardTokenId;

    private Long instOrderId;

    private String tokenType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.member_id
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String memberId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.card_id
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private Long cardId;

    private Long beneficiaryId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.session_id
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String sessionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.inst_code
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String instCode;


    private String issueBank;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.dbcr
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String dbcr;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.company_or_personal
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String companyOrPersonal;

    /**
     * is3DS
     */
    private String is3DS;

    /**
     * 首次绑定
     */
    private String firstBind;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.extension
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String extension;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.card_no
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String cardNo;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.card_holder
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String cardHolder;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.inst_token_id
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String instTokenId;

    private String cardExpired;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.card_type
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String cardType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.card_brand
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String cardBrand;

    /**
     * 是否需要csc
     */
    private String needCsc;

    /**
     * ipAddress
     */
    private String ipAddress;

    /**
     * iban
     */
    private String iban;

    private String cardAccountNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.result_url
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private String resultUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.gmt_create
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private Date gmtCreate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_card_token.gmt_modified
     *
     * @mbg.generated Sun Mar 29 19:33:29 GST 2020
     */
    private Date gmtModified;

}