package com.uaepay.cmf.common.core.dal.daointerface;

import com.uaepay.cmf.common.core.dal.dataobject.CardTokenDO;

public interface CardTokenDAO {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_card_token
     *
     * @mbg.generated Sat Mar 28 15:09:02 GST 2020
     */
    String insert(CardTokenDO record);


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_card_token
     *
     * @mbg.generated Sat Mar 28 15:09:02 GST 2020
     */
    CardTokenDO selectByPrimaryKey(String cardTokenId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_card_token
     *
     * @mbg.generated Sat Mar 28 15:09:02 GST 2020
     */
    int updateByPrimaryKey(CardTokenDO record);


    int updateSelective(CardTokenDO record);

    CardTokenDO selectByInstOrderId(Long instOrderId);

    int updateInstInfo(Long instOrderId, String instTokenId, String cardTokenId);
}