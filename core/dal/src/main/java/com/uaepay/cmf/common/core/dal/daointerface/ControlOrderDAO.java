package com.uaepay.cmf.common.core.dal.daointerface;

import com.uaepay.cmf.common.core.dal.dataobject.ControlOrderDO;

import java.util.Date;
import java.util.List;

/**
 * A dao interface provides methods to access database table <tt>TT_CONTROL_ORDER</tt>.
 * <p>
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_control_order.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public interface ControlOrderDAO {

    /**
     * Query DB table <tt>TT_CONTROL_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ORDER_ID, FUND_CHANNEL_CODE, PRODUCT_CODE, INST_CODE, PRE_REQUEST_NO, PAY_MODE, REQUEST_NO, REQUEST_TYPE, API_TYPE, INST_ORDER_NO, AMOUNT, STATUS, RETRY_STATUS, GMT_CREATE, GMT_MODIFIED, EXTENSION, NOTIFY_STATUS, SOURCE_CODE, COMMUNICATE_STATUS, FLAG, MEMO from TT_CONTROL_ORDER where (REQUEST_NO = ?)</tt>
     *
     * @param requestNo
     * @return ControlOrderDO @
     */
    ControlOrderDO loadWithRequestNo(String requestNo);


    /**
     * 根据请求号和 api类型查询
     * @param requestNo 请求号
     * @param apiType apiType
     * @return 控制订单
     */
    ControlOrderDO loadWithRequestNoAndApiType(String requestNo,String apiType);

    /**
     * Query DB table <tt>TT_CONTROL_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ORDER_ID, FUND_CHANNEL_CODE, PRODUCT_CODE, INST_CODE, PRE_REQUEST_NO, PAY_MODE, REQUEST_NO, REQUEST_TYPE, API_TYPE, INST_ORDER_NO, AMOUNT, STATUS, RETRY_STATUS, GMT_CREATE, GMT_MODIFIED, EXTENSION, NOTIFY_STATUS, SOURCE_CODE, COMMUNICATE_STATUS, FLAG, MEMO from TT_CONTROL_ORDER where (INST_ORDER_NO = ?)</tt>
     *
     * @param instOrderNo
     * @return ControlOrderDO @
     */
    ControlOrderDO loadWithInstOrderNo(String instOrderNo);

    /**
     * Query DB table <tt>TT_CONTROL_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ORDER_ID, FUND_CHANNEL_CODE, PRODUCT_CODE, INST_CODE, PRE_REQUEST_NO, PAY_MODE, REQUEST_NO, REQUEST_TYPE, API_TYPE, INST_ORDER_NO, AMOUNT, STATUS, RETRY_STATUS, GMT_CREATE, GMT_MODIFIED, EXTENSION, NOTIFY_STATUS, SOURCE_CODE, COMMUNICATE_STATUS, FLAG, MEMO from TT_CONTROL_ORDER where (ORDER_ID = ?)</tt>
     *
     * @param orderId
     * @return ControlOrderDO @
     */
    ControlOrderDO loadWithOrderId(long orderId);

    /**
     * Insert one <tt>ControlOrderDO</tt> object to DB table <tt>TT_CONTROL_ORDER</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_CONTROL_ORDER(ORDER_ID,FUND_CHANNEL_CODE,PRODUCT_CODE,INST_CODE,PRE_REQUEST_NO,PAY_MODE,REQUEST_NO,REQUEST_TYPE,API_TYPE,INST_ORDER_NO,AMOUNT,STATUS,RETRY_STATUS,NOTIFY_STATUS,SOURCE_CODE,COMMUNICATE_STATUS,FLAG,GMT_CREATE,GMT_MODIFIED,EXTENSION,MEMO) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp, ?, ?)</tt>
     *
     * @param controlOrder
     * @return long @
     */
    long insert(ControlOrderDO controlOrder);

    /**
     * Update DB table <tt>TT_CONTROL_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_CONTROL_ORDER set gmt_modified=systimestamp, STATUS=? where (ORDER_ID = ?)</tt>
     *
     * @param orderId
     * @param targetStatus
     * @param preStatus
     * @return int @
     */
    int updateStatusById(long orderId, String targetStatus, String preStatus);

    /**
     * @param extension
     * @param requestNo
     * @return @
     */
    int updateExtensionByRequestNo(String extension, String requestNo);

    int updateCommunicateStatusByIdAndPreStatus(long orderId, String communicateStatus, String preStatus);

    List<Long> loadControlOrder4Query(String code, Date startDate, Date endDate, int batchSize);

    int updateRetryInfoById(int retryTimes, Date retryDateTime1, long orderId);

    int updateFlagWithOrderIdAndPreFlag(Long orderId, String flag, String preFlag);
}