package com.uaepay.cmf.common.core.dal.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;

import org.apache.commons.lang3.StringUtils;
import com.uaepay.common.util.DateUtil;

/**
 * <AUTHOR>
 * @version $Id: DbRouteUtil, v 0.1 2016/11/1 下午5:07 Hewj Exp $
 */
public class DbRouter {

    /**
     * 序列号长度
     */
    private static final int SEQ_NO_RIGHT_LEN = 9;

    /**
     * 所有分表后缀
     */
    public static final String ALL_TABLE_SUFFIX = "_00,_01,_02,_03,_04,_05,_06,_07,_08,_09";

    private static final String[] TABLE_SUFFIX_ARR =
        new String[] {"", "_00", "_01", "_02", "_03", "_04", "_05", "_06", "_07", "_08", "_09"};
    private static final String EMPTY_STRING = "";
    private static final String CMF_DB = "cmfDb";
    private static final String NEW_CMF_DB = "csCmfDb";
    private static final String HISTORY_CMF_DB = "cmfDbHis";
    private static final String NEW_HISTORY_CMF_DB = "csCmfDbHis";

    /**
     * 走分表策略的商户号
     **/
    @Value("${sharding.mode.partnerId:someone}")
    private String shardingModePartnerId = "";

    /**
     * 归档月份
     */
    @Value("${db.archive.months:2}")
    private int archiveMonths;

    private static final int PRODUCT = 1;
    private static final int HISTORY = 0;
    private static final int UNKNOWN = -1;

    /**
     * 符合新订单号规则的订单号计算分表序号
     *
     * @param voucherNo
     * @return 返回 "",_00~_09的格式
     */
    public static String calTabSuffix(String voucherNo) {
        String suffix = calSeqSuffix(voucherNo);
        if (StringUtils.isNotEmpty(suffix)) {
            suffix = "_0" + suffix;
        }
        return suffix;
    }

    public static String defaultSuffix() {
        return EMPTY_STRING;
    }

    /**
     * 符合新订单号规则的订单号计算sequence分表序号
     *
     * @param voucherNo
     * @return 返回 "",0~9的格式
     */
    private static String calSeqSuffix(String voucherNo) {
        String suffix = "";
        if (isNewOrder(voucherNo)) {
            suffix = voucherNo.substring(voucherNo.length() - 1, voucherNo.length());
        }
        return suffix;
    }

    public static String routePaymentSeqNo(String paymentSeqNo) {
        if (isNewOrder(paymentSeqNo)) {
            String suffixNo = paymentSeqNo.substring(paymentSeqNo.length() - 1);
            if (StringUtils.isNumeric(suffixNo)) {
                int index = Integer.parseInt(suffixNo);
                return TABLE_SUFFIX_ARR[index + 1];
            }
        }
        return "";
    }

    /**
     * 新订单返回10张分表后缀
     *
     * @param voucherNo
     * @return
     */
    public static String calTabSuffix4All(String voucherNo) {
        String suffix = "";
        if (isNewOrder(voucherNo)) {
            suffix = ALL_TABLE_SUFFIX;
        }
        return suffix;
    }

    /**
     * 一般生成规则：YYYYMMDD(8)+序列(9)+分表序号(1) = 18位 CLEARING_SESSION_ID YYYYMMDD(8)+0000+序列(9)+分表序号(1) = 22位
     * BIZ_PAYMENT_SEQ_NO YYYYMMDD(8)+数字类型(2)+序列(9)+分表序号(1) = 20位 PAYMENT_SEQ_NO YYYYMMDD(8)+字符类型(2)+序列(9)+分表序号(1) = 20位
     *
     * @return
     */
    public static boolean isNewOrder(String voucherNo) {
        if (voucherNo != null && isStartWithDate(voucherNo)) {// 日期开头
            // 支付流水号
            if (StringUtils.isNumeric(voucherNo)) {// 全数字
                return voucherNo.length() == 18;
            }
            return voucherNo.length() == 20 && StringUtils.isNumeric(voucherNo.substring(10, 20));
        }
        return false;
    }

    /**
     * 判断前8位是否日期(不严谨)
     *
     * @param str
     * @return
     */
    private static boolean isStartWithDate(String str) {
        boolean result = false;
        try {
            if (str != null && str.length() >= 8) {
                int year = Integer.parseInt(str.substring(0, 4));
                int month = Integer.parseInt(str.substring(4, 6));
                int day = Integer.parseInt(str.substring(6, 8));
                if (year >= 1900 && year <= 2099 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                    result = true;
                }
            }
        } catch (Exception e) {
            // do nothing
        }
        return result;
    }

    /**
     * 是否分库分表模式 根据扩展参数
     *
     * @param partnerId
     *            根据商户号判断是否走新库(灰度上线时使用,标识代码中赋予)
     * @param relateBizPaymentSeqNo
     *            相关订单好决定需要走分表
     * @return
     */
    public boolean isShardingMode(String partnerId, String relateBizPaymentSeqNo) {
        return isNewOrder(relateBizPaymentSeqNo) && (shardingModePartnerId.contains("defaults")
            || (StringUtils.isNotBlank(partnerId) && shardingModePartnerId.contains(partnerId)));
    }

    public static String routeDb(String fixedTableSuffix) {
        return StringUtils.isEmpty(fixedTableSuffix) ? CMF_DB : NEW_CMF_DB;

    }

    public static String covert4Pay(String voucherNo) {
        boolean isRst = isNewOrder(voucherNo);
        return isRst ? NEW_CMF_DB : CMF_DB;
    }

    public String covert4PaySeqNoShards(String paymentSeqNo) {
        int isArchived = isArchived(paymentSeqNo);
        if (isArchived == HISTORY) {
            return HISTORY_CMF_DB;
        } else {
            return NEW_CMF_DB + "," + CMF_DB;
        }
    }

    public String covert4PaySeqNo(String paymentSeqNo) {
        return EMPTY_STRING;
    }

    public static String newDb() {
        return NEW_CMF_DB;
    }

    public static String oldDb() {
        return CMF_DB;
    }

    /**
     * 根据订单号路由 包括历史库
     *
     * @param voucherNo
     * @return
     */
    public String covert4PayHis(String voucherNo) {
        boolean isRst = isNewOrder(voucherNo);
        int isArchived = isArchived(voucherNo);
        if (isArchived == HISTORY) {
            return HISTORY_CMF_DB;
        } else {
            return isRst ? NEW_CMF_DB : CMF_DB;
        }
    }

    /**
     * 老payment生成的 201704053062340505 新payment生成的 201704060000001431
     *
     * @param voucherNo
     * @return
     */
    public static boolean isNewOrderRequestNo(String voucherNo) {
        boolean isRst = isNewOrder(voucherNo);
        if (isRst) {
            long date = Long.parseLong(voucherNo.substring(0, 8));
            String flag = voucherNo.substring(8, 10);
            if (!"00".equals(flag) && date <= 20170406L) {// 特殊处理历史数据
                isRst = false;
            }
        }
        return isRst;
    }

    public static String covert4RequestNo(String voucherNo) {
        boolean isRst = isNewOrderRequestNo(voucherNo);
        return isRst ? NEW_CMF_DB : CMF_DB;
    }

    public static String calTabSuffix4RequestNo(String voucherNo) {
        boolean isRst = isNewOrderRequestNo(voucherNo);
        if (isRst) {
            return calTabSuffix(voucherNo);
        } else {
            return "";
        }
    }

    public static String appendOrderNoSuffix(String orderNo, String origNo) {
        if (StringUtils.isEmpty(origNo)) {
            return orderNo;
        }
        return orderNo + origNo.substring(origNo.length() - 1);
    }

    /**
     * YYYYMMDD(8) + 业务码(0,2,4)+序列(9)+分表序号(1)
     * <p>
     * 一般生成规则：YYYYMMDD(8)+序列(9)+分表序号(1) = 18位 CLEARING_SESSION_ID YYYYMMDD(8)+0000+序列(9)+分表序号(1) = 22位
     * BIZ_PAYMENT_SEQ_NO YYYYMMDD(8)+数字类型(2)+序列(9)+分表序号(1) = 20位 PAYMENT_SEQ_NO YYYYMMDD(8)+字符类型(2)+序列(9)+分表序号(1) = 20位
     *
     * @param sequence
     * @return
     */
    private static String genSeqNo4Sharding(long sequence, String bizCode, String suffix) {
        String date = DateUtil.getTodayString();
        String tmpId = StringUtils.rightPad(String.valueOf(sequence), SEQ_NO_RIGHT_LEN, "0");
        String id = StringUtils.substring(tmpId, tmpId.length() - SEQ_NO_RIGHT_LEN);// 超过9位裁剪
        if (bizCode == null) {
            bizCode = "";
        }
        if (suffix == null) {
            suffix = "";
        }
        return date + bizCode + id + suffix;
    }

    /**
     * 根据依赖字段 生成带分表后缀seq
     *
     * @param sequence
     * @param dependVoucherNo
     *            分表依赖字段
     * @return
     */
    public static String genSeq4Sharding(long sequence, String dependVoucherNo) {
        return genSeq4Sharding(sequence, "", dependVoucherNo);
    }

    public static String gen4Sharding(String orderNo, String preNo) {
        if (StringUtils.isEmpty(preNo) || !isNewOrder(preNo)) {
            return orderNo;
        }
        return orderNo + preNo.substring(preNo.length() - 1);
    }

    public static long genSeq4ShardingLong(long sequence, String dependVoucherNo) {
        return Long.parseLong(genSeq4Sharding(sequence, "", dependVoucherNo));
    }

    /**
     * 根据依赖字段 生成带分表后缀seq
     *
     * @param sequence
     *            sequence
     * @param bizCode
     *            业务编码
     * @param dependVoucherNo
     *            依赖字段
     * @return
     */
    public static String genSeq4Sharding(long sequence, String bizCode, String dependVoucherNo) {
        return genSeqNo4Sharding(sequence, bizCode, calSeqSuffix(dependVoucherNo));
    }

    public static String getOne(List<String> stringList) {
        return stringList.get(0);
    }

    /**
     * 判断是否归档
     *
     * @return
     */
    public int isArchived(String voucherNo) {
        int rst = UNKNOWN;
        if (null == voucherNo) {
            return UNKNOWN;
        }
        try {
            Date vnDate = null;
            if (isStartWithDate(voucherNo)) {
                SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
                vnDate = sf.parse(voucherNo.substring(0, 8));
            } else {
                // voucher生成的凭证号
                String timeStamp = voucherNo.substring(2, 15);
                vnDate = new Date(Long.parseLong(timeStamp));
            }
            Date hisDate = DateUtils.addMonths(DateUtils.truncate(new Date(), Calendar.MONTH), -archiveMonths);
            rst = vnDate.compareTo(hisDate) >= 0 ? PRODUCT : HISTORY;
        } catch (Exception e) {
            // pass
        }
        return rst;
    }

    public static String[] getTableSuffixArr() {
        return TABLE_SUFFIX_ARR;
    }
}
