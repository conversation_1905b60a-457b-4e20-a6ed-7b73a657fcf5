package com.uaepay.cmf.common.core.dal.dataobject;

import lombok.Data;

import java.util.Date;

/**
 * A data object class directly models database table <tt>TL_MONITOR_LOG</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tl_monitor_log.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
@Data
public class MonitorLogDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>LOG_ID</tt>.
     */
    private Long logId;

    /**
     * This property corresponds to db column <tt>IP_ADDRESS</tt>.
     */
    private String ipAddress;

    /**
     * This property corresponds to db column <tt>ORDER_NO</tt>.
     */
    private String orderNo;

    /**
     * This property corresponds to db column <tt>EVENT_MESSAGE</tt>.
     */
    private String eventMessage;

    /**
     * This property corresponds to db column <tt>ACTION_ID</tt>.
     */
    private String actionId;

    /**
     * This property corresponds to db column <tt>ALERT_LEVEL</tt>.
     */
    private String alertLevel;

    /**
     * This property corresponds to db column <tt>LOG_STATUS</tt>.
     */
    private String logStatus;

    /**
     * This property corresponds to db column <tt>EXCEPTION_LOG</tt>.
     */
    private String exceptionLog;

    /**
     * This property corresponds to db column <tt>MEMO</tt>.
     */
    private String memo;

    /**
     * This property corresponds to db column <tt>GMT_CREATE</tt>.
     */
    private Date gmtCreate;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

}
