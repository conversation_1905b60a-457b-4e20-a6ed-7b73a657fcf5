package com.uaepay.cmf.common.core.dal.dataobject;

import java.util.Date;

import com.uaepay.common.util.money.Money;
import lombok.Data;

/**
 * A data object class directly models database table <tt>TT_INST_ORDER_RESULT</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_inst_order_result.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
@Data
public class InstOrderResultDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>RESULT_ID</tt>.
     */
    private Long resultId;

    /**
     * This property corresponds to db column <tt>INST_ORDER_ID</tt>.
     */
    private Long instOrderId;

    /**
     * This property corresponds to db column <tt>INST_SEQ_NO</tt>.
     */
    private String instSeqNo;

    /**
     * This property corresponds to db column <tt>ORDER_TYPE</tt>.
     */
    private String orderType;

    /**
     * This property corresponds to db column <tt>REAL_AMOUNT</tt>.
     */
    private Money realAmount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);

    /**
     * This property corresponds to db column <tt>ORGI_INST_ORDER_NO</tt>.
     */
    private String orgiInstOrderNo;

    /**
     * This property corresponds to db column <tt>INST_STATUS</tt>.
     */
    private String instStatus;

    /**
     * This property corresponds to db column <tt>INST_RESULT_CODE</tt>.
     */
    private String instResultCode;

    /**
     * This property corresponds to db column <tt>RETURN_TIMES</tt>.
     */
    private Integer returnTimes;

    /**
     * This property corresponds to db column <tt>GMT_CREATE</tt>.
     */
    private Date gmtCreate;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

    /**
     * This property corresponds to db column <tt>MEMO</tt>.
     */
    private String memo;

    /**
     * This property corresponds to db column <tt>FUND_CHANNEL_CODE</tt>.
     */
    private String fundChannelCode;

    /**
     * This property corresponds to db column <tt>INST_ORDER_NO</tt>.
     */
    private String instOrderNo;

    /**
     * This property corresponds to db column <tt>OPERATE_STATUS</tt>.
     */
    private String operateStatus;

    /**
     * This property corresponds to db column <tt>EXTENSION</tt>.
     */
    private String extension;

    /**
     * This property corresponds to db column <tt>API_RESULT_CODE</tt>.
     */
    private String apiResultCode;

    /**
     * This property corresponds to db column <tt>API_RESULT_SUB_CODE</tt>.
     */
    private String apiResultSubCode;

    /**
     * This property corresponds to db column <tt>API_TYPE</tt>.
     */
    private String apiType;

    /**
     * This property corresponds to db column <tt>RISK_FLAG</tt>.
     */
    private String riskFlag;

}
