package com.uaepay.cmf.common.core.dal.dataobject;

import java.util.Date;

public class Notify3dsResultDO extends BaseDO{
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.result_id
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private Long resultId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.card_token_id
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private Long cardTokenId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.inst_order_no
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private String instOrderNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.channel_code
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private String channelCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.trade_order_no
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private String tradeOrderNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.payment_order_no
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private String paymentOrderNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.eci
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private String eci;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.identity_result
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private String identityResult;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.identity_result_desc
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private String identityResultDesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.extension
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private String extension;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.gmt_cmf_request
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private Date gmtCmfRequest;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.gmt_bank_request
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private Date gmtBankRequest;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tb_notify_3ds_result.gmt_bank_response
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    private Date gmtBankResponse;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.result_id
     *
     * @return the value of tb_notify_3ds_result.result_id
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public Long getResultId() {
        return resultId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.result_id
     *
     * @param resultId the value for tb_notify_3ds_result.result_id
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setResultId(Long resultId) {
        this.resultId = resultId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.card_token_id
     *
     * @return the value of tb_notify_3ds_result.card_token_id
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public Long getCardTokenId() {
        return cardTokenId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.card_token_id
     *
     * @param cardTokenId the value for tb_notify_3ds_result.card_token_id
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setCardTokenId(Long cardTokenId) {
        this.cardTokenId = cardTokenId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.inst_order_no
     *
     * @return the value of tb_notify_3ds_result.inst_order_no
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public String getInstOrderNo() {
        return instOrderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.inst_order_no
     *
     * @param instOrderNo the value for tb_notify_3ds_result.inst_order_no
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setInstOrderNo(String instOrderNo) {
        this.instOrderNo = instOrderNo == null ? null : instOrderNo.trim();
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.trade_order_no
     *
     * @return the value of tb_notify_3ds_result.trade_order_no
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public String getTradeOrderNo() {
        return tradeOrderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.trade_order_no
     *
     * @param tradeOrderNo the value for tb_notify_3ds_result.trade_order_no
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setTradeOrderNo(String tradeOrderNo) {
        this.tradeOrderNo = tradeOrderNo == null ? null : tradeOrderNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.payment_order_no
     *
     * @return the value of tb_notify_3ds_result.payment_order_no
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public String getPaymentOrderNo() {
        return paymentOrderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.payment_order_no
     *
     * @param paymentOrderNo the value for tb_notify_3ds_result.payment_order_no
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setPaymentOrderNo(String paymentOrderNo) {
        this.paymentOrderNo = paymentOrderNo == null ? null : paymentOrderNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.eci
     *
     * @return the value of tb_notify_3ds_result.eci
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public String getEci() {
        return eci;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.eci
     *
     * @param eci the value for tb_notify_3ds_result.eci
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setEci(String eci) {
        this.eci = eci == null ? null : eci.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.identity_result
     *
     * @return the value of tb_notify_3ds_result.identity_result
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public String getIdentityResult() {
        return identityResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.identity_result
     *
     * @param identityResult the value for tb_notify_3ds_result.identity_result
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setIdentityResult(String identityResult) {
        this.identityResult = identityResult == null ? null : identityResult.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.identity_result_desc
     *
     * @return the value of tb_notify_3ds_result.identity_result_desc
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public String getIdentityResultDesc() {
        return identityResultDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.identity_result_desc
     *
     * @param identityResultDesc the value for tb_notify_3ds_result.identity_result_desc
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setIdentityResultDesc(String identityResultDesc) {
        this.identityResultDesc = identityResultDesc == null ? null : identityResultDesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.extension
     *
     * @return the value of tb_notify_3ds_result.extension
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public String getExtension() {
        return extension;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.extension
     *
     * @param extension the value for tb_notify_3ds_result.extension
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setExtension(String extension) {
        this.extension = extension == null ? null : extension.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.gmt_cmf_request
     *
     * @return the value of tb_notify_3ds_result.gmt_cmf_request
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public Date getGmtCmfRequest() {
        return gmtCmfRequest;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.gmt_cmf_request
     *
     * @param gmtCmfRequest the value for tb_notify_3ds_result.gmt_cmf_request
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setGmtCmfRequest(Date gmtCmfRequest) {
        this.gmtCmfRequest = gmtCmfRequest;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.gmt_bank_request
     *
     * @return the value of tb_notify_3ds_result.gmt_bank_request
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public Date getGmtBankRequest() {
        return gmtBankRequest;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.gmt_bank_request
     *
     * @param gmtBankRequest the value for tb_notify_3ds_result.gmt_bank_request
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setGmtBankRequest(Date gmtBankRequest) {
        this.gmtBankRequest = gmtBankRequest;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tb_notify_3ds_result.gmt_bank_response
     *
     * @return the value of tb_notify_3ds_result.gmt_bank_response
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public Date getGmtBankResponse() {
        return gmtBankResponse;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tb_notify_3ds_result.gmt_bank_response
     *
     * @param gmtBankResponse the value for tb_notify_3ds_result.gmt_bank_response
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    public void setGmtBankResponse(Date gmtBankResponse) {
        this.gmtBankResponse = gmtBankResponse;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_notify_3ds_result
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Notify3dsResultDO other = (Notify3dsResultDO) that;
        return (this.getResultId() == null ? other.getResultId() == null : this.getResultId().equals(other.getResultId()))
            && (this.getCardTokenId() == null ? other.getCardTokenId() == null : this.getCardTokenId().equals(other.getCardTokenId()))
            && (this.getInstOrderNo() == null ? other.getInstOrderNo() == null : this.getInstOrderNo().equals(other.getInstOrderNo()))
            && (this.getTradeOrderNo() == null ? other.getTradeOrderNo() == null : this.getTradeOrderNo().equals(other.getTradeOrderNo()))
            && (this.getPaymentOrderNo() == null ? other.getPaymentOrderNo() == null : this.getPaymentOrderNo().equals(other.getPaymentOrderNo()))
            && (this.getEci() == null ? other.getEci() == null : this.getEci().equals(other.getEci()))
            && (this.getIdentityResult() == null ? other.getIdentityResult() == null : this.getIdentityResult().equals(other.getIdentityResult()))
            && (this.getIdentityResultDesc() == null ? other.getIdentityResultDesc() == null : this.getIdentityResultDesc().equals(other.getIdentityResultDesc()))
            && (this.getExtension() == null ? other.getExtension() == null : this.getExtension().equals(other.getExtension()))
            && (this.getGmtCmfRequest() == null ? other.getGmtCmfRequest() == null : this.getGmtCmfRequest().equals(other.getGmtCmfRequest()))
            && (this.getGmtBankRequest() == null ? other.getGmtBankRequest() == null : this.getGmtBankRequest().equals(other.getGmtBankRequest()))
            && (this.getGmtBankResponse() == null ? other.getGmtBankResponse() == null : this.getGmtBankResponse().equals(other.getGmtBankResponse()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_notify_3ds_result
     *
     * @mbg.generated Fri Oct 16 22:59:02 GST 2020
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getResultId() == null) ? 0 : getResultId().hashCode());
        result = prime * result + ((getCardTokenId() == null) ? 0 : getCardTokenId().hashCode());
        result = prime * result + ((getInstOrderNo() == null) ? 0 : getInstOrderNo().hashCode());
        result = prime * result + ((getTradeOrderNo() == null) ? 0 : getTradeOrderNo().hashCode());
        result = prime * result + ((getPaymentOrderNo() == null) ? 0 : getPaymentOrderNo().hashCode());
        result = prime * result + ((getEci() == null) ? 0 : getEci().hashCode());
        result = prime * result + ((getIdentityResult() == null) ? 0 : getIdentityResult().hashCode());
        result = prime * result + ((getIdentityResultDesc() == null) ? 0 : getIdentityResultDesc().hashCode());
        result = prime * result + ((getExtension() == null) ? 0 : getExtension().hashCode());
        result = prime * result + ((getGmtCmfRequest() == null) ? 0 : getGmtCmfRequest().hashCode());
        result = prime * result + ((getGmtBankRequest() == null) ? 0 : getGmtBankRequest().hashCode());
        result = prime * result + ((getGmtBankResponse() == null) ? 0 : getGmtBankResponse().hashCode());
        return result;
    }
}