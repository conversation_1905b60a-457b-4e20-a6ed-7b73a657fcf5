package com.uaepay.cmf.common.core.dal.daointerface;

import com.uaepay.basis.beacon.service.facade.domain.request.PageRequest;
import com.uaepay.biz.common.util.PageList;
import com.uaepay.cmf.common.core.dal.dataobject.InstOrderDO;
import com.uaepay.cmf.common.core.dal.dataobject.UniqueOrderDO;
import com.uaepay.common.util.money.Money;

import java.util.Date;
import java.util.List;

/**
 * A dao interface provides methods to access database table <tt>TT_INST_ORDER</tt>.
 * <p>
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_inst_order.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public interface InstOrderDAO {
    /**
     * Insert one <tt>InstOrderDO</tt> object to DB table <tt>TT_INST_ORDER</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_INST_ORDER(INST_ORDER_ID,INST_CODE,INST_ORDER_NO,ORDER_TYPE,PAY_MODE,CURRENCY,SUBMIT_PRIORITY,GMT_BOOKING_SUBMIT,ARCHIVE_TEMPLATE_ID,PRODUCT_CODE,PAYMENT_CODE,AMOUNT,STATUS,COMMUNICATE_TYPE,FUND_CHANNEL_CODE,FUND_CHANNEL_API,COMMUNICATE_STATUS,ARCHIVE_BATCH_ID,FLAG,RISK_STATUS,GMT_SUBMIT,GMT_CREATE,GMT_MODIFIED,MEMO,GMT_NEXT_RETRY,RETRY_TIMES,ROUTE_VERSION,IS_SPLIT,CMF_SEQ_NO,EXTENSION,EXPECT_TIME) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp, ?, systimestamp, 0, ?, ?, ?, ?, ?, ?)</tt>
     *
     * @param instOrder
     * @return Long @
     */
    Long insert(InstOrderDO instOrder);

    /**
     * Delete records from DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>delete from TT_INST_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return int @
     */
    int delete(Long instOrderId);

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, INST_CODE, INST_ORDER_NO, ORDER_TYPE, PAY_MODE, CURRENCY, SUBMIT_PRIORITY, GMT_BOOKING_SUBMIT, ARCHIVE_TEMPLATE_ID, PRODUCT_CODE, PAYMENT_CODE, AMOUNT, STATUS, COMMUNICATE_TYPE, FUND_CHANNEL_CODE, FUND_CHANNEL_API, COMMUNICATE_STATUS, ARCHIVE_BATCH_ID, FLAG, RISK_STATUS, GMT_SUBMIT, GMT_CREATE, GMT_MODIFIED, MEMO, GMT_NEXT_RETRY, RETRY_TIMES, ROUTE_VERSION, IS_SPLIT, CMF_SEQ_NO, EXTENSION, EXPECT_TIME from TT_INST_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return InstOrderDO @
     */
    InstOrderDO loadById(Long instOrderId);

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, INST_CODE, INST_ORDER_NO, ORDER_TYPE, PAY_MODE, CURRENCY, SUBMIT_PRIORITY, GMT_BOOKING_SUBMIT, ARCHIVE_TEMPLATE_ID, PRODUCT_CODE, PAYMENT_CODE, AMOUNT, STATUS, COMMUNICATE_TYPE, FUND_CHANNEL_CODE, FUND_CHANNEL_API, COMMUNICATE_STATUS, ARCHIVE_BATCH_ID, FLAG, RISK_STATUS, GMT_SUBMIT, GMT_CREATE, GMT_MODIFIED, MEMO, GMT_NEXT_RETRY, RETRY_TIMES, ROUTE_VERSION, IS_SPLIT, CMF_SEQ_NO, EXTENSION, EXPECT_TIME from TT_INST_ORDER where (INST_ORDER_ID = ?) for update</tt>
     *
     * @param instOrderId
     * @return InstOrderDO @
     */
    InstOrderDO lockedById(Long instOrderId);

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, STATUS=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param status
     * @param instOrderId
     * @return int @
     */
    public int updateStatusById(Long instOrderId, String targetStatus, String preStatus);

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, INST_CODE, INST_ORDER_NO, ORDER_TYPE, PAY_MODE, CURRENCY, SUBMIT_PRIORITY, GMT_BOOKING_SUBMIT, ARCHIVE_TEMPLATE_ID, PRODUCT_CODE, PAYMENT_CODE, AMOUNT, STATUS, COMMUNICATE_TYPE, FUND_CHANNEL_CODE, FUND_CHANNEL_API, COMMUNICATE_STATUS, ARCHIVE_BATCH_ID, FLAG, RISK_STATUS, GMT_SUBMIT, GMT_CREATE, GMT_MODIFIED, MEMO, GMT_NEXT_RETRY, RETRY_TIMES, ROUTE_VERSION, IS_SPLIT, CMF_SEQ_NO, EXTENSION, EXPECT_TIME from TT_INST_ORDER where (CMF_SEQ_NO = ?)</tt>
     *
     * @param cmfSeqNo
     * @return List<InstOrderDO> @
     */
    List<InstOrderDO> loadByCmfSeq(String cmfSeqNo);

    List<InstOrderDO> lockedByCmfSeq(String cmfSeqNo);

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, COMMUNICATE_STATUS=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param communicateStatus
     * @param instOrderId
     * @return int @
     */
    int updateCommunicateStatusById(String communicateStatus, Long instOrderId);

    /**
     * 修改批量交互状态
     *
     * @param instOrderIds
     * @param newStatus
     * @param oldStatus
     * @return @
     */
    int updateCommunicateStatusBatchByIds(List<Long> instOrderIds, String newStatus, String oldStatus);

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, COMMUNICATE_STATUS=? where ((INST_ORDER_ID = ?) AND (COMMUNICATE_STATUS = ?))</tt>
     *
     * @param communicateStatus
     * @param instOrderId
     * @param communicateStatus2
     * @return int @
     */
    int updateCommunicateStatusWithPreStatus(String communicateStatus, Long instOrderId, String communicateStatus2);

    /**
     * 更新发送时间
     * @param commStatus
     * @param gmtBooking
     * @param archiveId
     * @param preCommStatus
     * @return
     */
    int updateCommStatusAndBookingByArchiveId(String commStatus, Date gmtBooking, Long archiveId, String preCommStatus);

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, FUND_CHANNEL_CODE=?, FUND_CHANNEL_API=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param fundChannelCode
     * @param channelApi
     * @param instOrderId
     * @return int @
     */
    int updateChannelInfoById(String fundChannelCode, String channelApi, Long instOrderId);

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, memo=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param memo
     * @param instOrderId
     * @return int @
     */
    int updateMemoById(String memo, Long instOrderId);

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, extension=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param extension
     * @param instOrderId
     * @return int @
     */
    int updateExtensionById(String extension, Long instOrderId);

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp, GMT_NEXT_RETRY='gmtNextRetry', RETRY_TIMES=? where (INST_ORDER_ID = ?)</tt>
     *
     * @param retryTimes
     * @param instOrderId
     * @param gmtNextRetry
     * @return int @
     */
    int updateRetryInfoById(Integer retryTimes, Date gmtNextRetry, Long instOrderId);

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select sum(amount) sumAmount from TT_INST_ORDER where ((COMMUNICATE_STATUS = ?) AND (STATUS = 'I') AND (COMMUNICATE_TYPE = 'S') AND (GMT_BOOKING_SUBMIT > (sysdate - 1)) AND (ORDER_TYPE = ?))</tt>
     *
     * @param communicateStatusList
     * @param orderType
     * @param channelCodeList
     * @return String @
     */
    Money sumAmountForQueryResult(List<String> communicateStatusList, String orderType, List channelCodeList,
                                  Date startDate, Date endDate);

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, INST_CODE, INST_ORDER_NO, ORDER_TYPE, PAY_MODE, CURRENCY, SUBMIT_PRIORITY, GMT_BOOKING_SUBMIT, ARCHIVE_TEMPLATE_ID, PRODUCT_CODE, PAYMENT_CODE, AMOUNT, STATUS, COMMUNICATE_TYPE, FUND_CHANNEL_CODE, FUND_CHANNEL_API, COMMUNICATE_STATUS, ARCHIVE_BATCH_ID, FLAG, RISK_STATUS, GMT_SUBMIT, GMT_CREATE, GMT_MODIFIED, MEMO, GMT_NEXT_RETRY, RETRY_TIMES, ROUTE_VERSION, IS_SPLIT, CMF_SEQ_NO, EXTENSION, EXPECT_TIME from TT_INST_ORDER where (ARCHIVE_BATCH_ID = ?) order by INST_ORDER_ID ASC</tt>
     *
     * @param archiveBatchId
     * @return List<InstOrderDO> @
     */
    List<InstOrderDO> loadByArchiveBatchId(Long archiveBatchId);

    /**
     * @param archiveBatchId
     * @param communicateStatus
     * @param instOrderStatus
     * @return @
     */
    List<InstOrderDO> loadByBatchIdStatus(Long archiveBatchId, Date archiveDate, String communicateStatus,
                                          String instOrderStatus);


    int countArchivePages(Long archiveTemplateId, Long hours, String apiCode, String communicateType,
                          Date bookingTime, int rownum);

    List<Long> queryInstOrderList4ArchivePage(Long archiveTemplateId, Long days, String apiCode,
                                              String communicateType, Date bookingTime, int rownum);

    int updateTempBatchByInstOrderId(Long tempBatchId, List<Long> instOrderIdList);

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp</tt>
     *
     * @param tempBatchId
     * @param defId
     * @return int @
     */
    int updateBatchId2Default(Long tempBatchId, Long defId);

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp where (1 != 1)</tt>
     *
     * @param tempBatchId
     * @param archiveBatchId
     * @param instOrderIdList
     * @return int @
     */
    int updateBatchIdListByTempBatchId(Long tempBatchId, Long archiveBatchId, List instOrderIdList);

    /**
     * Query DB table <tt>TT_INST_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select count(1) COUNT_NUM from TT_INST_ORDER where ((ARCHIVE_BATCH_ID = ?) AND (STATUS != 'F'))</tt>
     *
     * @param archiveBatchId
     * @return long @
     */
    long countNotFailureByArchiveBatchId(Long archiveBatchId);

    PageList queryInstOrders(PageRequest pageRequest);

    /**
     * Update DB table <tt>TT_INST_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_ORDER set gmt_modified=systimestamp</tt>
     *
     * @param instOrderId
     * @param flag
     * @param preFlag
     * @return int @
     */
    int updateFlagWithOrderIdAndPreFlag(Long instOrderId, String flag, String preFlag);

    /**
     * 修改机构订单推进状态
     *
     * @param advanceStatus
     * @param instOrderId
     * @param preStatus
     * @return
     */
    int updateAdvanceStatusWithPreStatus(String advanceStatus, Long instOrderId, String preStatus);

    int updateFoOrderRetryWithOrderId(String newInstOrderNo, String memo, String instCode, String status,
                                      String communicateStatus, long instOrderId, String oldInstOrderNo);

    List<InstOrderDO> getRefundOrderByFundInOrder(String fundInOrderNo, List<String> orderStatusStrList);

    Long insertUnique(UniqueOrderDO uniqueOrderDO);

    UniqueOrderDO loadUniqueOrderByNo(String instOrderNo);

    /**
     * 查询单号
     *
     * @param startTime
     * @param endTime
     * @param maxSize
     * @param orderType
     * @return
     */
    List<Long> loadSingleOrder4Query(Date startTime, Date endTime, int maxSize, String orderType,
                                     String fixedTableSuffix);

    /**
     * 单笔出款
     *
     * @param channelList
     * @param code
     * @param startDate
     * @param endDate
     * @param maxSize
     * @return
     */
    List loadSingleOrder4Send(List<String> channelList, List<String> ignoreChannelList, String code, Date startDate,
                              Date endDate, String fixedTableSuffix, int maxSize);

    Money getArchiveBatchAmt(Long archiveBatchId);

    int getArchiveBatchCnt(Long archiveBatchId);

    List<Long> loadInstOrderIdListByBatchId(Long archiveBatchId);


    List<InstOrderDO> loadInstOrderIdListByIds(List<Long> instOrderIdList);


    List<String> queryFOFinishedDateList(String channelCode);

    List<Long> loadInstOrderIdListByBatchIdAndCurrency(Long archiveBatchId, String currency);

    List<String> loadOrderCurrencyListByBatchId(Long archiveBatchId);

    int updateBookingSubmitById(Date bookingSubmit, Long instOrderId);

    int updateAmountAndExtension(long instOrderId, Money amount, String extension);

    int updateRetryDataWithPreStatus(String code, Long instOrderId, String code1, String instOrderNo, String extension);

    int updateUniqueOrder(UniqueOrderDO uniqueOrderDO);
}
