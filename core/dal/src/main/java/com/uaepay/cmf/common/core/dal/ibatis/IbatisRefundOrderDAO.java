package com.uaepay.cmf.common.core.dal.ibatis;

import com.uaepay.cmf.common.core.dal.daointerface.RefundOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.RefundOrderDO;
import org.mybatis.spring.support.SqlSessionDaoSupport;

/**
 * An ibatis based implementation of dao interface <tt>com.uaepay.cmf.common.core.dal.daointerface.RefundOrderDAO</tt>.
 * <p>
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_refund_order.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public class IbatisRefundOrderDAO extends SqlSessionDaoSupport implements RefundOrderDAO {
    /**
     * Insert one <tt>RefundOrderDO</tt> object to DB table <tt>TT_REFUND_ORDER</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_REFUND_ORDER(INST_ORDER_ID,FUNDIN_ORDER_NO,FUNDIN_REAL_AMOUNT,FUNDIN_DATE,GMT_MODIFIED) values (?, ?, ?, ?, systimestamp)</tt>
     *
     * @param refundOrder
     * @return long @
     */
    @Override
    public long insert(RefundOrderDO refundOrder) {
        if (refundOrder == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-REFUND-ORDER-INSERT", refundOrder);

        return refundOrder.getInstOrderId();
    }

    /**
     * Delete records from DB table <tt>TT_REFUND_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>delete from TT_REFUND_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return int @
     */
    @Override
    public int delete(long instOrderId) {
        return getSqlSession().delete("MS-REFUND-ORDER-DELETE", instOrderId);
    }

    /**
     * Query DB table <tt>TT_REFUND_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, FUNDIN_ORDER_NO, FUNDIN_REAL_AMOUNT, FUNDIN_DATE, GMT_MODIFIED from TT_REFUND_ORDER where (INST_ORDER_ID = ?)</tt>
     *
     * @param instOrderId
     * @return RefundOrderDO @
     */
    @Override
    public RefundOrderDO loadById(long instOrderId) {
        return (RefundOrderDO) getSqlSession().selectOne("MS-REFUND-ORDER-LOAD-BY-ID", instOrderId);
    }

    /**
     * Query DB table <tt>TT_REFUND_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select INST_ORDER_ID, FUNDIN_ORDER_NO, FUNDIN_REAL_AMOUNT, FUNDIN_DATE, GMT_MODIFIED from TT_REFUND_ORDER where (INST_ORDER_ID = ?) for update</tt>
     *
     * @param instOrderId
     * @return RefundOrderDO @
     */
    @Override
    public RefundOrderDO lockedById(long instOrderId) {
        return (RefundOrderDO) getSqlSession().selectOne("MS-REFUND-ORDER-LOCKED-BY-ID", instOrderId);
    }

}