package com.uaepay.cmf.common.core.dal.ibatis;

import java.util.List;

import org.mybatis.spring.support.SqlSessionDaoSupport;

import com.uaepay.cmf.common.core.dal.daointerface.SysConfigurationDAO;
import com.uaepay.cmf.common.core.dal.dataobject.SysConfigurationDO;

/**
 * An ibatis based implementation of dao interface
 * <tt>com.uaepay.cmf.common.core.dal.daointerface.SysConfigurationDAO</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tm_sys_configuration.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR> won
 */
public class IbatisSysConfigurationDAO extends SqlSessionDaoSupport implements SysConfigurationDAO {
    /**
     * Insert one <tt>SysConfigurationDO</tt> object to DB table <tt>TM_SYS_CONFIGURATION</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TM_SYS_CONFIGURATION(ATTR_NAME,ATTR_VALUE,MEMO,GMT_CREATED,GMT_MODIFIED) values (?, ?, ?, systimestamp, systimestamp)</tt>
     *
     * @param sysConfiguration
     * @return String @
     */
    @Override
    public String insert(SysConfigurationDO sysConfiguration) {
        if (sysConfiguration == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-SYS-CONFIGURATION-INSERT", sysConfiguration);

        return sysConfiguration.getAttrName();
    }

    @Override
    public int update(SysConfigurationDO sysConfigurationDO) {
        return getSqlSession().update("MS-SYS-CONFIGURATION-UPDATE", sysConfigurationDO);
    }

    /**
     * Query DB table <tt>TM_SYS_CONFIGURATION</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ATTR_NAME, ATTR_VALUE, MEMO, GMT_CREATED, GMT_MODIFIED from TM_SYS_CONFIGURATION where (ATTR_NAME = ?)</tt>
     *
     * @param attrName
     * @return SysConfigurationDO @
     */
    @Override
    public SysConfigurationDO loadByKey(String attrName) {
        return (SysConfigurationDO)getSqlSession().selectOne("MS-SYS-CONFIGURATION-LOAD-BY-KEY", attrName);
    }

    /**
     * Query DB table <tt>TM_SYS_CONFIGURATION</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ATTR_NAME, ATTR_VALUE, MEMO, GMT_CREATED, GMT_MODIFIED from TM_SYS_CONFIGURATION order by ATTR_NAME DESC</tt>
     *
     * @return List<SysConfigurationDO> @
     */
    @Override
    public List<SysConfigurationDO> loadAll() {
        return getSqlSession().selectList("MS-SYS-CONFIGURATION-LOAD-ALL", null);
    }

}
