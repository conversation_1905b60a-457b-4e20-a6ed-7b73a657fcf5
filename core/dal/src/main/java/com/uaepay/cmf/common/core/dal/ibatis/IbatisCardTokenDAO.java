package com.uaepay.cmf.common.core.dal.ibatis;

import com.uaepay.cmf.common.core.dal.daointerface.CardTokenDAO;
import com.uaepay.cmf.common.core.dal.dataobject.CardTokenDO;
import org.mybatis.spring.support.SqlSessionDaoSupport;

import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date IbatisCardTokenDAO.java v1.0  2020-03-28 15:16
 */
public class IbatisCardTokenDAO extends SqlSessionDaoSupport implements CardTokenDAO {

    @Override
    public String insert(CardTokenDO cardToken) {
        if (cardToken == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }
        getSqlSession().insert("MS-CARD-TOKEN-INSERT", cardToken);
        return cardToken.getCardTokenId();
    }

    @Override
    public CardTokenDO selectByPrimaryKey(String cardTokenId) {
        return (CardTokenDO)getSqlSession().selectOne("MS-CARD-TOKEN-LOAD-BY-ID", cardTokenId);
    }

    @Override
    public int updateByPrimaryKey(CardTokenDO cardToken) {
        if (cardToken == null) {
            throw new IllegalArgumentException("Can't update by a null data object.");
        }

        return getSqlSession().update("MS-CARD-TOKEN-UPDATE", cardToken);
    }

    @Override
    public int updateSelective(CardTokenDO cardToken) {
        if (cardToken == null) {
            throw new IllegalArgumentException("Can't update by a null data object.");
        }

        return getSqlSession().update("MS-CARD-TOKEN-UPDATE-SELECTIVE", cardToken);
    }

    @Override
    public CardTokenDO selectByInstOrderId(Long instOrderId) {
        return (CardTokenDO)getSqlSession().selectOne("MS-CARD-TOKEN-LOAD-BY-INST-ORDER-ID", instOrderId);
    }

    @Override
    public int updateInstInfo(Long instOrderId, String instTokenId, String cardTokenId) {
        Map param = new HashMap();

        param.put("instOrderId", instOrderId);
        param.put("instTokenId", instTokenId);
        param.put("cardTokenId", cardTokenId);

        return getSqlSession().update("MS-CARD-TOKEN-UPDATE-INST-INFO", param);
    }
}
