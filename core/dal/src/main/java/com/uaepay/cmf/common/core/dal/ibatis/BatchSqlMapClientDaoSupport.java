package com.uaepay.cmf.common.core.dal.ibatis;

import com.uaepay.basic.cobarclient.mybatis.BatchCobarSqlSessionDaoSupport;

import java.util.List;

/**
 * <p>
 * 批量支持
 * </p>
 * 
 * <AUTHOR> won
 * @version $Id: AbstractBatchDaoSupport.java, v 0.1 2011-1-17 下午04:57:18 sean won Exp $
 */
public class BatchSqlMapClientDaoSupport extends BatchCobarSqlSessionDaoSupport {
    /**
     * 批量更新
     * 
     * @param statementName
     * @param list
     * @return 更新数 oracle可能返回为零
     */
    @SuppressWarnings("unchecked")
    protected int batchUpdate(final String statementName, final List<?> list) {
        return super.batchUpdate(statementName, list);
    }

    /**
     * 批量新增
     * 
     * @param statementName
     * @param list
     */
    @SuppressWarnings("unchecked")
    protected int batchInsert(final String statementName, final List<?> list) {
        return super.batchInsert(statementName, list);

    }

    /**
     * 批量删除
     * 
     * @param statementName
     * @param list
     */
    @SuppressWarnings("unchecked")
    protected int batchDelete(final String statementName, final List<?> list) {
        return super.batchDelete(statementName, list);
    }
}
