package com.uaepay.cmf.common.core.dal.dataobject;

import java.util.Date;

import com.uaepay.common.util.money.Money;
import lombok.Data;

/**
 * A data object class directly models database table <tt>TT_INST_BATCH_RESULT</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_inst_batch_result.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR> won
 */
@Data
public class InstBatchResultDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>BATCH_RESULT_ID</tt>.
     */
    private Long batchResultId;

    /**
     * This property corresponds to db column <tt>ARCHIVE_BATCH_ID</tt>.
     */
    private Long archiveBatchId;

    /**
     * This property corresponds to db column <tt>BATCH_TYPE</tt>.
     */
    private String batchType;

    /**
     * This property corresponds to db column <tt>FUND_CHANNEL_CODE</tt>.
     */
    private String fundChannelCode;

    /**
     * This property corresponds to db column <tt>TOTAL_COUNT</tt>.
     */
    private Long totalCount;

    /**
     * This property corresponds to db column <tt>TOTAL_AMOUNT</tt>.
     */
    private Money totalAmount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);

    /**
     * This property corresponds to db column <tt>SUCCESS_COUNT</tt>.
     */
    private Long successCount;

    /**
     * This property corresponds to db column <tt>SUCCESS_AMOUNT</tt>.
     */
    private Money successAmount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);

    /**
     * This property corresponds to db column <tt>FAILED_COUNT</tt>.
     */
    private Long failedCount;

    /**
     * This property corresponds to db column <tt>FAILED_AMOUNT</tt>.
     */
    private Money failedAmount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);

    /**
     * This property corresponds to db column <tt>DIFFERENT_COUNT</tt>.
     */
    private Long differentCount;

    /**
     * This property corresponds to db column <tt>DIFFERENT_AMOUNT</tt>.
     */
    private Money differentAmount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);

    /**
     * This property corresponds to db column <tt>LESS_COUNT</tt>.
     */
    private Long lessCount;

    /**
     * This property corresponds to db column <tt>MORE_COUNT</tt>.
     */
    private Long moreCount;

    /**
     * This property corresponds to db column <tt>STATUS</tt>.
     */
    private String status;

    /**
     * This property corresponds to db column <tt>FILE_PATH</tt>.
     */
    private String filePath;

    /**
     * This property corresponds to db column <tt>GMT_RETURN</tt>.
     */
    private Date gmtReturn;

    /**
     * This property corresponds to db column <tt>GMT_CREATE</tt>.
     */
    private Date gmtCreate;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

    /**
     * This property corresponds to db column <tt>MEMO</tt>.
     */
    private String memo;

    /**
     * This property corresponds to db column <tt>FUND_CHANNEL_API</tt>.
     */
    private String fundChannelApi;

    /**
     * This property corresponds to db column <tt>BIZ_TYPE</tt>.
     */
    private String bizType;

    /**
     * This property corresponds to db column <tt>CHECK_OPERATER</tt>.
     */
    private String checkOperater;

    /**
     * This property corresponds to db column <tt>IMPORT_OPERATER</tt>.
     */
    private String importOperater;

    /**
     * This property corresponds to db column <tt>IMPORT_TYPE</tt>.
     */
    private String importType;

    /**
     * Setter method for property <tt>totalAmount</tt>.
     * 
     * @param totalAmount
     *            value to be assigned to property totalAmount
     */
    public void setTotalAmount(Money totalAmount) {
        if (totalAmount == null) {
            this.totalAmount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);
        } else {
            this.totalAmount = totalAmount;
        }
    }

    /**
     * Setter method for property <tt>successAmount</tt>.
     * 
     * @param successAmount
     *            value to be assigned to property successAmount
     */
    public void setSuccessAmount(Money successAmount) {
        if (successAmount == null) {
            this.successAmount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);
        } else {
            this.successAmount = successAmount;
        }
    }

}
