package com.uaepay.cmf.common.core.dal.daointerface;

import java.util.Date;
import java.util.List;

import com.uaepay.biz.common.util.PageList;
import com.uaepay.cmf.common.core.dal.dataobject.InstBatchOrderDO;
import com.uaepay.cmf.common.core.query.InstBatchOrderPageQuery;
import com.uaepay.common.util.money.Money;

/**
 * A dao interface provides methods to access database table <tt>TT_INST_BATCH_ORDER</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_inst_batch_order.xml</tt>). Modify the configuration file according to your needs,
 * then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR> won
 */
public interface InstBatchOrderDAO {
    /**
     * Insert one <tt>InstBatchOrderDO</tt> object to DB table <tt>TT_INST_BATCH_ORDER</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_INST_BATCH_ORDER(ARCHIVE_BATCH_ID,ORDER_TYPE,TOTAL_AMOUNT,TOTAL_COUNT,CURRENCY,FUND_CHANNEL_CODE,ARCHIVE_TEMPLATE_ID,FUND_CHANNEL_API,PAY_MODE,STATUS,IS_LOCKED,FILE_PATH,OPERATOR,GMT_ARCHIVE,GMT_CREATE,GMT_MODIFIED,MEMO,CHECK_FLAG) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, systimestamp, ?, ?)</tt>
     *
     * @param instBatchOrder
     * @return Long @
     */
    Long insert(InstBatchOrderDO instBatchOrder);

    /**
     * Query DB table <tt>TT_INST_BATCH_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ARCHIVE_BATCH_ID, ORDER_TYPE, TOTAL_AMOUNT, TOTAL_COUNT, CURRENCY, FUND_CHANNEL_CODE, ARCHIVE_TEMPLATE_ID, FUND_CHANNEL_API, PAY_MODE, STATUS, IS_LOCKED, FILE_PATH, OPERATOR, GMT_ARCHIVE, GMT_CREATE, GMT_MODIFIED, MEMO, CHECK_FLAG from TT_INST_BATCH_ORDER where (ARCHIVE_BATCH_ID = ?)</tt>
     *
     * @param archiveBatchId
     * @return InstBatchOrderDO @
     */
    InstBatchOrderDO loadById(Long archiveBatchId);

    /**
     * Query DB table <tt>TT_INST_BATCH_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select ARCHIVE_BATCH_ID, ORDER_TYPE, TOTAL_AMOUNT, TOTAL_COUNT, CURRENCY, FUND_CHANNEL_CODE, ARCHIVE_TEMPLATE_ID, FUND_CHANNEL_API, PAY_MODE, STATUS, IS_LOCKED, FILE_PATH, OPERATOR, GMT_ARCHIVE, GMT_CREATE, GMT_MODIFIED, MEMO, CHECK_FLAG from TT_INST_BATCH_ORDER where (ARCHIVE_BATCH_ID = ?) for update</tt>
     *
     * @param archiveBatchId
     * @return InstBatchOrderDO @
     */
    InstBatchOrderDO lockedById(Long archiveBatchId);

    /**
     * Update DB table <tt>TT_INST_BATCH_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_BATCH_ORDER set gmt_modified=systimestamp, status=? where (ARCHIVE_BATCH_ID = ?)</tt>
     *
     * @param status
     * @param archiveBatchId
     * @return int @
     */
    int updateStatusById(String status, Long archiveBatchId);

    /**
     * Update DB table <tt>TT_INST_BATCH_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_BATCH_ORDER set gmt_modified=systimestamp, status=? where (ARCHIVE_BATCH_ID = ?)</tt>
     *
     * @param status
     * @param archiveBatchId
     * @return int @
     */
    int updateStatusByIdAndPreStatus(String status, String preStatus, Long archiveBatchId);

    /**
     * 修改状态和打批时间
     * @param status
     * @param gmtArchive
     * @param preStatus
     * @param archiveBatchId
     * @return
     */
    int updateStatusAndArchiveTimeByIdAndPreStatus(String status, Date gmtArchive, String preStatus, Long archiveBatchId);

    /**
     * Update DB table <tt>TT_INST_BATCH_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_BATCH_ORDER set gmt_modified=systimestamp, TOTAL_AMOUNT=?, TOTAL_COUNT=? where (ARCHIVE_BATCH_ID = ?)</tt>
     *
     * @param totalAmount
     * @param totalCount
     * @param archiveBatchId
     * @return int @
     */
    int updateAmountAndCount(Money totalAmount, Integer totalCount, Long archiveBatchId);

    /**
     * Update DB table <tt>TT_INST_BATCH_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_INST_BATCH_ORDER set gmt_modified=systimestamp</tt>
     *
     * @param archiveBatchId
     * @param checkFlag
     * @return int @
     */
    int passStatus(Long archiveBatchId, String checkFlag);

    /**
     * Query DB table <tt>TT_INST_BATCH_ORDER</tt> for records.
     *
     * @param status
     * @param isLocked
     * @param apiCode
     * @param operator
     * @param pageSize
     * @param pageNum
     * @return @
     */
    PageList queryForBatchFundout(String status, String isLocked, String apiCode, String operator, int pageSize,
        int pageNum);

    int updateIsLockedByOriStatus(List<Long> archiveBatchIdList, String code, String oriCode);

    List<Long> loadBatchOrder4Query(String lockStatus, String bizType, int rownum);

    int updateRetryInfoById(Integer retryTimes, Date gmtNextRetry, Long orderId);

    PageList loadBatchList(InstBatchOrderPageQuery query);

    int updateExtensionById(String extension, long archiveBatchId);
}
