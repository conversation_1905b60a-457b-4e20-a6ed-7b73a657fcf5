package com.uaepay.cmf.common.core.dal.daointerface;

import com.uaepay.cmf.common.core.dal.dataobject.CmfRequestDO;

/**
 * A dao interface provides methods to access database table <tt>TT_CMF_REQUEST</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_cmf_request.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public interface CmfRequestDAO {
    /**
     * Insert one <tt>CmfRequestDO</tt> object to DB table <tt>TT_CMF_REQUEST</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_CMF_REQUEST(PAYMENT_SEQ_NO,SETTLEMENT_ID,CAN_RETRY,GMT_CREATE,GMT_MODIFIED) values (?, ?, ?, systimestamp, systimestamp)</tt>
     *
     * @param cmfRequest
     * @return String @
     */
    String insert(CmfRequestDO cmfRequest);

    /**
     * Query DB table <tt>TT_CMF_REQUEST</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select PAYMENT_SEQ_NO, CAN_RETRY, GMT_CREATE, GMT_MODIFIED from TT_CMF_REQUEST where ((PAYMENT_SEQ_NO = ?) AND (SETTLEMENT_ID = ?))</tt>
     *
     * @param paymentSeqNo
     * @param settlementId
     * @return CmfRequestDO @
     */
    CmfRequestDO loadById(String paymentSeqNo, String settlementId);

    /**
     * Query DB table <tt>TT_CMF_REQUEST</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select PAYMENT_SEQ_NO, CAN_RETRY, GMT_CREATE, GMT_MODIFIED from TT_CMF_REQUEST where ((PAYMENT_SEQ_NO = ?) AND (SETTLEMENT_ID = ?)) for update</tt>
     *
     * @param paymentSeqNo
     * @param settlementId
     * @return CmfRequestDO @
     */
    CmfRequestDO lockedById(String paymentSeqNo, String settlementId);

    /**
     * Update DB table <tt>TT_CMF_REQUEST</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_CMF_REQUEST set gmt_modified=systimestamp, CAN_RETRY=? where ((PAYMENT_SEQ_NO = ?) AND (SETTLEMENT_ID = ?))</tt>
     *
     * @param canRetry
     * @param paymentSeqNo
     * @param settlementId
     * @return int @
     */
    int updateStatusById(String canRetry, String paymentSeqNo, String settlementId);

    /**
     * 插入老库
     * 
     * @param cmfRequestDO
     * @return
     */
    String insert2OldDb(CmfRequestDO cmfRequestDO);
}