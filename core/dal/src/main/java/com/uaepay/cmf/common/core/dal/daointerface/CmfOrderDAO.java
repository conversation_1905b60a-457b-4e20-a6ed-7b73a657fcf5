package com.uaepay.cmf.common.core.dal.daointerface;

import com.uaepay.cmf.common.core.dal.dataobject.CmfOrderDO;

import java.util.Date;
import java.util.List;

/**
 * A dao interface provides methods to access database table <tt>TT_CMF_ORDER</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_cmf_order.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public interface CmfOrderDAO {
    /**
     * Insert one <tt>CmfOrderDO</tt> object to DB table <tt>TT_CMF_ORDER</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_CMF_ORDER(CMF_SEQ_NO,PAYMENT_SEQ_NO,SETTLEMENT_ID,REQUEST_BATCH_NO,ORDER_TYPE,PRODUCT_CODE,PAYMENT_CODE,PAY_MODE,SUBMIT_BATCH_NO,MEMBER_ID,AMOUNT,CURRENCY,INST_CODE,EXPECT_TIME,PAYMENT_NOTIFY_STATUS,COMMUNICATE_TYPE,STATUS,CONFIRM_STATUS,OPERATOR,GMT_SUBMIT,INST_ORDER_ID,ORGI_PAYMENT_SEQ_NO,ORGI_SETTLEMENT_ID,GMT_CREATE,BIZ_DATE,GMT_MODIFIED,MEMO,EXTENSION) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, ?, systimestamp, ?, ?)</tt>
     *
     * @param cmfOrder
     * @return String @
     */
    String insert(CmfOrderDO cmfOrder);

    /**
     * Query DB table <tt>TT_CMF_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select CMF_SEQ_NO, PAYMENT_SEQ_NO, SETTLEMENT_ID, REQUEST_BATCH_NO, ORDER_TYPE, PRODUCT_CODE, PAYMENT_CODE, PAY_MODE, SUBMIT_BATCH_NO, MEMBER_ID, AMOUNT, CURRENCY, INST_CODE, EXPECT_TIME, PAYMENT_NOTIFY_STATUS, CONFIRM_STATUS, COMMUNICATE_TYPE, STATUS, CONFIRM_STATUS, OPERATOR, GMT_SUBMIT, INST_ORDER_ID, ORGI_PAYMENT_SEQ_NO, ORGI_SETTLEMENT_ID, GMT_CREATE, BIZ_DATE, GMT_MODIFIED, MEMO, EXTENSION from TT_CMF_ORDER where (CMF_SEQ_NO = ?)</tt>
     *
     * @param cmfSeqNo
     * @return CmfOrderDO @
     */
    CmfOrderDO loadById(String cmfSeqNo);

    /**
     * Query DB table <tt>TT_CMF_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select CMF_SEQ_NO, PAYMENT_SEQ_NO, SETTLEMENT_ID, REQUEST_BATCH_NO, ORDER_TYPE, PRODUCT_CODE, PAYMENT_CODE, PAY_MODE, SUBMIT_BATCH_NO, MEMBER_ID, AMOUNT, CURRENCY, INST_CODE, EXPECT_TIME, PAYMENT_NOTIFY_STATUS, COMMUNICATE_TYPE, STATUS, CONFIRM_STATUS, OPERATOR, GMT_SUBMIT, INST_ORDER_ID, ORGI_PAYMENT_SEQ_NO, ORGI_SETTLEMENT_ID, GMT_CREATE, BIZ_DATE, GMT_MODIFIED, MEMO, EXTENSION from TT_CMF_ORDER where (CMF_SEQ_NO = ?) for update</tt>
     *
     * @param cmfSeqNo
     * @return CmfOrderDO @
     */
    CmfOrderDO lockedById(String cmfSeqNo);

    /**
     * Query DB table <tt>TT_CMF_ORDER</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select CMF_SEQ_NO, PAYMENT_SEQ_NO, SETTLEMENT_ID, REQUEST_BATCH_NO, ORDER_TYPE, PRODUCT_CODE, PAYMENT_CODE, PAY_MODE, SUBMIT_BATCH_NO, MEMBER_ID, AMOUNT, CURRENCY, INST_CODE, EXPECT_TIME, PAYMENT_NOTIFY_STATUS, COMMUNICATE_TYPE, STATUS, CONFIRM_STATUS, OPERATOR, GMT_SUBMIT, INST_ORDER_ID, ORGI_PAYMENT_SEQ_NO, ORGI_SETTLEMENT_ID, GMT_CREATE, BIZ_DATE, GMT_MODIFIED, MEMO, EXTENSION from TT_CMF_ORDER where ((PAYMENT_SEQ_NO = ?) AND (SETTLEMENT_ID = ?)) order by CMF_SEQ_NO DESC</tt>
     *
     * @param paymentSeqNo
     * @param settlementId
     * @return CmfOrderDO @
     */
    CmfOrderDO loadByPaymentNo(String paymentSeqNo, String settlementId);

    /**
     * Update DB table <tt>TT_CMF_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_CMF_ORDER set gmt_modified=systimestamp, PAYMENT_NOTIFY_STATUS=? where (CMF_SEQ_NO = ?)</tt>
     *
     * @param paymentNotifyStatus
     * @param cmfSeqNo
     * @return int @
     */
    int updatePaymentNotifyStatusById(String paymentNotifyStatus, String cmfSeqNo);

    /**
     * Update DB table <tt>TT_CMF_ORDER</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_CMF_ORDER set gmt_modified=systimestamp, CONFIRM_STATUS=? where (CMF_SEQ_NO = ?)</tt>
     *
     * @param confirmStatus
     * @param cmfSeqNo
     * @return int @
     */
    int updateConfirmStatusById(String confirmStatus, String cmfSeqNo);

    /**
     * 根据状态和审核状态更新
     * 
     * @param status
     * @param preStatus
     * @param confirmStatus
     * @param cmfSeqNo
     * @return
     */
    int updateStatusAndConfirmStatusById(String status, String preStatus, String confirmStatus, String cmfSeqNo);

    /**
     * 根据原始结算id查询cmf订单
     * 
     * @param orgiSettlementId
     * @param dateStart
     * @param ignoreInstOrderIds
     * @return
     */
    List<CmfOrderDO> loadByOrgiSettlementId(String orgiSettlementId, Date dateStart, List<Long> ignoreInstOrderIds);

    CmfOrderDO loadByPaymentOrderNo(String paymentOrderNo, Date gmtStart, Date gmtEnd);
}