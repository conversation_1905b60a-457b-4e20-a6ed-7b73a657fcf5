package com.uaepay.cmf.common.core.dal.ibatis;

import com.uaepay.cmf.common.core.dal.daointerface.CmfOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.CmfOrderDO;
import org.mybatis.spring.support.SqlSessionDaoSupport;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * An ibatis based implementation of dao interface <tt>com.uaepay.cmf.common.core.dal.daointerface.CmfOrderDAO</tt>.
 * <p>
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * <p>
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_cmf_order.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public class IbatisCmfOrderDAO extends SqlSessionDaoSupport implements CmfOrderDAO {
    /**
     * Insert one <tt>CmfOrderDO</tt> object to DB table <tt>TT_CMF_ORDER</tt>, return primary key
     * <p>
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_CMF_ORDER(CMF_SEQ_NO,PAYMENT_SEQ_NO,SETTLEMENT_ID,REQUEST_BATCH_NO,ORDER_TYPE,PRODUCT_CODE,PAYMENT_CODE,PAY_MODE,SUBMIT_BATCH_NO,MEMBER_ID,AMOUNT,CURRENCY,INST_CODE,EXPECT_TIME,PAYMENT_NOTIFY_STATUS,COMMUNICATE_TYPE,STATUS,CONFIRM_STATUS,OPERATOR,GMT_SUBMIT,INST_ORDER_ID,ORGI_PAYMENT_SEQ_NO,ORGI_SETTLEMENT_ID,GMT_CREATE,BIZ_DATE,GMT_MODIFIED,MEMO,EXTENSION) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, systimestamp, ?, systimestamp, ?, ?)</tt>
     *
     * @param cmfOrder
     * @return String @
     */
    @Override
    public String insert(CmfOrderDO cmfOrder) {
        if (cmfOrder == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-CMF-ORDER-INSERT", cmfOrder);

        return cmfOrder.getCmfSeqNo();
    }

    /**
     * Query DB table <tt>TT_CMF_ORDER</tt> for records.
     * <p>
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select CMF_SEQ_NO, PAYMENT_SEQ_NO, SETTLEMENT_ID, REQUEST_BATCH_NO, ORDER_TYPE, PRODUCT_CODE, PAYMENT_CODE, PAY_MODE, SUBMIT_BATCH_NO, MEMBER_ID, AMOUNT, CURRENCY, INST_CODE, EXPECT_TIME, PAYMENT_NOTIFY_STATUS, CONFIRM_STATUS, COMMUNICATE_TYPE, STATUS, CONFIRM_STATUS, OPERATOR, GMT_SUBMIT, INST_ORDER_ID, ORGI_PAYMENT_SEQ_NO, ORGI_SETTLEMENT_ID, GMT_CREATE, BIZ_DATE, GMT_MODIFIED, MEMO, EXTENSION from TT_CMF_ORDER where (CMF_SEQ_NO = ?)</tt>
     *
     * @param cmfSeqNo
     * @return CmfOrderDO @
     */
    @Override
    public CmfOrderDO loadById(String cmfSeqNo) {

        return (CmfOrderDO)getSqlSession().selectOne("MS-CMF-ORDER-LOAD-BY-ID", cmfSeqNo);

    }

    /**
     * Query DB table <tt>TT_CMF_ORDER</tt> for records.
     * <p>
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select CMF_SEQ_NO, PAYMENT_SEQ_NO, SETTLEMENT_ID, REQUEST_BATCH_NO, ORDER_TYPE, PRODUCT_CODE, PAYMENT_CODE, PAY_MODE, SUBMIT_BATCH_NO, MEMBER_ID, AMOUNT, CURRENCY, INST_CODE, EXPECT_TIME, PAYMENT_NOTIFY_STATUS, COMMUNICATE_TYPE, STATUS, CONFIRM_STATUS, OPERATOR, GMT_SUBMIT, INST_ORDER_ID, ORGI_PAYMENT_SEQ_NO, ORGI_SETTLEMENT_ID, GMT_CREATE, BIZ_DATE, GMT_MODIFIED, MEMO, EXTENSION from TT_CMF_ORDER where (CMF_SEQ_NO = ?) for update</tt>
     *
     * @param cmfSeqNo
     * @return CmfOrderDO @
     */
    @Override
    public CmfOrderDO lockedById(String cmfSeqNo) {

        return (CmfOrderDO)getSqlSession().selectOne("MS-CMF-ORDER-LOCKED-BY-ID", cmfSeqNo);

    }

    /**
     * Query DB table <tt>TT_CMF_ORDER</tt> for records.
     * <p>
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select CMF_SEQ_NO, PAYMENT_SEQ_NO, SETTLEMENT_ID, REQUEST_BATCH_NO, ORDER_TYPE, PRODUCT_CODE, PAYMENT_CODE, PAY_MODE, SUBMIT_BATCH_NO, MEMBER_ID, AMOUNT, CURRENCY, INST_CODE, EXPECT_TIME, PAYMENT_NOTIFY_STATUS, COMMUNICATE_TYPE, STATUS, CONFIRM_STATUS, OPERATOR, GMT_SUBMIT, INST_ORDER_ID, ORGI_PAYMENT_SEQ_NO, ORGI_SETTLEMENT_ID, GMT_CREATE, BIZ_DATE, GMT_MODIFIED, MEMO, EXTENSION from TT_CMF_ORDER where ((PAYMENT_SEQ_NO = ?) AND (SETTLEMENT_ID = ?)) order by CMF_SEQ_NO DESC</tt>
     *
     * @param paymentSeqNo
     * @param settlementId
     * @return CmfOrderDO @
     */
    @Override
    public CmfOrderDO loadByPaymentNo(String paymentSeqNo, String settlementId) {
        Map param = new HashMap();

        param.put("paymentSeqNo", paymentSeqNo);
        param.put("settlementId", "default".equals(settlementId) ? null : settlementId);

        return (CmfOrderDO)getSqlSession().selectOne("MS-CMF-ORDER-LOAD-BY-PAYMENT-NO", param);

    }

    /**
     * Update DB table <tt>TT_CMF_ORDER</tt>.
     * <p>
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_CMF_ORDER set gmt_modified=systimestamp, PAYMENT_NOTIFY_STATUS=? where (CMF_SEQ_NO = ?)</tt>
     *
     * @param paymentNotifyStatus
     * @param cmfSeqNo
     * @return int @
     */
    @Override
    public int updatePaymentNotifyStatusById(String paymentNotifyStatus, String cmfSeqNo) {
        Map param = new HashMap();

        param.put("paymentNotifyStatus", paymentNotifyStatus);
        param.put("cmfSeqNo", cmfSeqNo);

        return getSqlSession().update("MS-CMF-ORDER-UPDATE-PAYMENT-NOTIFY-STATUS-BY-ID", param);
    }

    /**
     * Update DB table <tt>TT_CMF_ORDER</tt>.
     * <p>
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_CMF_ORDER set gmt_modified=systimestamp, CONFIRM_STATUS=? where (CMF_SEQ_NO = ?)</tt>
     *
     * @param confirmStatus
     * @param cmfSeqNo
     * @return int @
     */
    @Override
    public int updateConfirmStatusById(String confirmStatus, String cmfSeqNo) {
        Map param = new HashMap();

        param.put("confirmStatus", confirmStatus);
        param.put("cmfSeqNo", cmfSeqNo);

        return getSqlSession().update("MS-CMF-ORDER-UPDATE-CONFIRM-STATUS-BY-ID", param);
    }

    @Override
    public int updateStatusAndConfirmStatusById(String status, String preStatus, String confirmStatus,
        String cmfSeqNo) {
        Map param = new HashMap();
        param.put("confirmStatus", confirmStatus);
        param.put("status", status);
        param.put("preStatus", preStatus);
        param.put("cmfSeqNo", cmfSeqNo);
        return getSqlSession().update("MS-CMF-ORDER-UPDATE-STATUS-CONFIRM-STATUS-BY-ID", param);
    }

    @Override
    public List<CmfOrderDO> loadByOrgiSettlementId(String orgiSettlementId, Date dateStart,
        List<Long> ignoreInstOrderIds) {
        Map param = new HashMap();

        param.put("ignoreInstOrderIds", ignoreInstOrderIds);
        param.put("orgiSettlementId", orgiSettlementId);
        param.put("dateStart", dateStart);

        return getSqlSession().selectList("MS-CMF-ORDER-LOAD-BY-ORGI-SETTLEID", param);
    }

    @Override
    public CmfOrderDO loadByPaymentOrderNo(String paymentOrderNo, Date gmtStart, Date gmtEnd) {
        Map param = new HashMap();

        param.put("paymentOrderNo", paymentOrderNo);
        param.put("gmtStart", gmtStart);
        param.put("gmtEnd", gmtEnd);

        return getSqlSession().selectOne("MS-CMF-ORDER-LOAD-BY-PAYMENT-ORDER-NO", param);
    }
}