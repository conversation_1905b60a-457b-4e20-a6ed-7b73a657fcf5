package com.uaepay.cmf.common.core.dal.dataobject;

import java.util.Date;

import com.uaepay.common.util.money.Money;
import lombok.Data;

/**
 * A data object class directly models database table <tt>TT_INST_ORDER</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_inst_order.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
@Data
public class InstOrderDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>INST_ORDER_ID</tt>.
     */
    private Long instOrderId;

    /**
     * This property corresponds to db column <tt>INST_CODE</tt>.
     */
    private String instCode;

    /**
     * This property corresponds to db column <tt>INST_ORDER_NO</tt>.
     */
    private String instOrderNo;

    /**
     * This property corresponds to db column <tt>ORDER_TYPE</tt>.
     */
    private String orderType;

    /**
     * This property corresponds to db column <tt>AMOUNT</tt>.
     */
    private Money amount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);

    /**
     * This property corresponds to db column <tt>STATUS</tt>.
     */
    private String status;

    /**
     * This property corresponds to db column <tt>COMMUNICATE_TYPE</tt>.
     */
    private String communicateType;

    /**
     * This property corresponds to db column <tt>COMMUNICATE_STATUS</tt>.
     */
    private String communicateStatus;

    /**
     * This property corresponds to db column <tt>ARCHIVE_BATCH_ID</tt>.
     */
    private Long archiveBatchId;

    /**
     * This property corresponds to db column <tt>GMT_SUBMIT</tt>.
     *
     */
    private Date gmtSubmit;

    /**
     * This property corresponds to db column <tt>GMT_CREATE</tt>.
     */
    private Date gmtCreate;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

    /**
     * This property corresponds to db column <tt>MEMO</tt>.
     */
    private String memo;

    /**
     * This property corresponds to db column <tt>PRODUCT_CODE</tt>.
     */
    private String productCode;

    /**
     * This property corresponds to db column <tt>PAYMENT_CODE</tt>.
     */
    private String paymentCode;

    /**
     * This property corresponds to db column <tt>PAY_MODE</tt>.
     */
    private String payMode;

    /**
     * This property corresponds to db column <tt>FUND_CHANNEL_CODE</tt>.
     */
    private String fundChannelCode;

    /**
     * This property corresponds to db column <tt>FUND_CHANNEL_API</tt>.
     */
    private String fundChannelApi;

    /**
     * This property corresponds to db column <tt>GMT_BOOKING_SUBMIT</tt>.
     */
    private Date gmtBookingSubmit;

    /**
     * This property corresponds to db column <tt>RETRY_TIMES</tt>.
     */
    private Integer retryTimes;

    /**
     * This property corresponds to db column <tt>GMT_NEXT_RETRY</tt>.
     */
    private Date gmtNextRetry;

    /**
     * This property corresponds to db column <tt>FLAG</tt>.
     */
    private String flag;

    /**
     * This property corresponds to db column <tt>RISK_STATUS</tt>.
     */
    private String riskStatus;

    /**
     * This property corresponds to db column <tt>ROUTE_VERSION</tt>.
     */
    private Integer routeVersion;

    /**
     * This property corresponds to db column <tt>CMF_SEQ_NO</tt>.
     */
    private String cmfSeqNo;

    /**
     * This property corresponds to db column <tt>IS_SPLIT</tt>.
     */
    private String isSplit;

    private String isAdvance;

    private String sendType;

    private String merchantId;

    /**
     * This property corresponds to db column <tt>EXTENSION</tt>.
     */
    private String extension;

    /**
     * Setter method for property <tt>amount</tt>.
     * 
     * @param amount
     *            value to be assigned to property amount
     */
    public void setAmount(Money amount) {
        if (amount == null) {
            this.amount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);
        } else {
            this.amount = amount;
        }
    }

}
