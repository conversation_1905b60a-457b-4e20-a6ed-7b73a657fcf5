package com.uaepay.cmf.common.core.dal.ibatis;

import com.uaepay.cmf.common.core.dal.daointerface.Notify3dsResultDAO;
import com.uaepay.cmf.common.core.dal.dataobject.Notify3dsResultDO;
import org.mybatis.spring.support.SqlSessionDaoSupport;

import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date IbatisNotify3dsResultDAO.java v1.0  2020-10-16 23:04
 */
public class IbatisNotify3dsResultDAO extends SqlSessionDaoSupport implements Notify3dsResultDAO {

    @Override
    public long insert(Notify3dsResultDO record) {
        if (record == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-NOTIFY-3DS-RESULT-INSERT", record);

        return record.getResultId();
    }


    @Override
    public Notify3dsResultDO selectByPrimaryKey(Long resultId) {
        return (Notify3dsResultDO) getSqlSession().selectOne("MS-NOTIFY-3DS-RESULT-LOAD-BY-ID", resultId);
    }

    @Override
    public Notify3dsResultDO selectByCondition(Map<String,String> paramMap) {
        return (Notify3dsResultDO) getSqlSession().selectOne("MS-NOTIFY-3DS-RESULT-LOAD-BY-CONDITION", paramMap);
    }

    @Override
    public int updateByPrimaryKey(Notify3dsResultDO record) {
        if (record == null) {
            throw new IllegalArgumentException("Can't update by a null data object.");
        }

        return getSqlSession().update("MS-NOTIFY-3DS-RESULT-UPDATE", record);
    }
}
