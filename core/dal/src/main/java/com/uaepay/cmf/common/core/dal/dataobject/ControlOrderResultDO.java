package com.uaepay.cmf.common.core.dal.dataobject;

import java.util.Date;

import com.uaepay.common.util.money.Money;
import lombok.Data;

/**
 * A data object class directly models database table <tt>TT_CONTROL_ORDER_RESULT</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_control_order_result.xml</tt>). Modify the configuration file according to your
 * needs, then run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
@Data
public class ControlOrderResultDO extends BaseDO {
    private static final long serialVersionUID = 741231858441822688L;

    // ========== properties ==========

    /**
     * This property corresponds to db column <tt>RESULT_ID</tt>.
     */
    private Long resultId;

    /**
     * This property corresponds to db column <tt>CONTROL_ORDER_ID</tt>.
     */
    private Long controlOrderId;

    /**
     * This property corresponds to db column <tt>AMOUNT</tt>.
     */
    private Money amount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);

    /**
     * This property corresponds to db column <tt>INST_ORDER_NO</tt>.
     */
    private String instOrderNo;

    /**
     * This property corresponds to db column <tt>INST_RESULT_CODE</tt>.
     */
    private String instResultCode;

    /**
     * This property corresponds to db column <tt>API_TYPE</tt>.
     */
    private String apiType;

    /**
     * This property corresponds to db column <tt>STATUS</tt>.
     */
    private String status;

    /**
     * This property corresponds to db column <tt>API_RESULT_CODE</tt>.
     */
    private String apiResultCode;

    /**
     * This property corresponds to db column <tt>API_SUB_RESULT_CODE</tt>.
     */
    private String apiSubResultCode;

    /**
     * This property corresponds to db column <tt>RESULT_MESSAGE</tt>.
     */
    private String resultMessage;

    /**
     * This property corresponds to db column <tt>MEMO</tt>.
     */
    private String memo;

    /**
     * This property corresponds to db column <tt>EXTENSION</tt>.
     */
    private String extension;

    /**
     * This property corresponds to db column <tt>GMT_CREATE</tt>.
     */
    private Date gmtCreate;

    /**
     * This property corresponds to db column <tt>GMT_MODIFIED</tt>.
     */
    private Date gmtModified;

    /**
     * Setter method for property <tt>amount</tt>.
     * 
     * @param amount
     *            value to be assigned to property amount
     */
    public void setAmount(Money amount) {
        if (amount == null) {
            this.amount = new Money(DEFAULT_AMOUNT, DEFAULT_CURRENCY);
        } else {
            this.amount = amount;
        }
    }
}
