package com.uaepay.cmf.common.core.dal.ibatis;

import java.util.HashMap;
import java.util.Map;

import org.mybatis.spring.support.SqlSessionDaoSupport;

import com.uaepay.cmf.common.core.dal.daointerface.CmfRequestDAO;
import com.uaepay.cmf.common.core.dal.dataobject.CmfRequestDO;

/**
 * An ibatis based implementation of dao interface <tt>com.uaepay.cmf.common.core.dal.daointerface.CmfRequestDAO</tt>.
 *
 * This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer) code generation utility specially
 * developed for <tt>iwallet</tt> project.
 * 
 * PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be OVERWRITTEN by someone else. To modify the
 * file, you should go to directory <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding
 * configuration file (<tt>tables/tt_cmf_request.xml</tt>). Modify the configuration file according to your needs, then
 * run <tt>iwallet-dalgen</tt> to generate this file.
 *
 * <AUTHOR>
 */
public class IbatisCmfRequestDAO extends SqlSessionDaoSupport implements CmfRequestDAO {
    /**
     * Insert one <tt>CmfRequestDO</tt> object to DB table <tt>TT_CMF_REQUEST</tt>, return primary key
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>insert into TT_CMF_REQUEST(PAYMENT_SEQ_NO,SETTLEMENT_ID,CAN_RETRY,GMT_CREATE,GMT_MODIFIED) values (?, ?, ?, systimestamp, systimestamp)</tt>
     *
     * @param cmfRequest
     * @return String @
     */
    @Override
    public String insert(CmfRequestDO cmfRequest) {
        if (cmfRequest == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-CMF-REQUEST-INSERT", cmfRequest);

        return cmfRequest.getPaymentSeqNo();
    }

    /**
     * Query DB table <tt>TT_CMF_REQUEST</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select PAYMENT_SEQ_NO, CAN_RETRY, GMT_CREATE, GMT_MODIFIED from TT_CMF_REQUEST where ((PAYMENT_SEQ_NO = ?) AND (SETTLEMENT_ID = ?))</tt>
     *
     * @param paymentSeqNo
     * @param settlementId
     * @return CmfRequestDO @
     */
    @Override
    public CmfRequestDO loadById(String paymentSeqNo, String settlementId) {
        Map param = new HashMap();

        param.put("paymentSeqNo", paymentSeqNo);
        param.put("settlementId", settlementId);

        return (CmfRequestDO)getSqlSession().selectOne("MS-CMF-REQUEST-LOAD-BY-ID", param);

    }

    /**
     * Query DB table <tt>TT_CMF_REQUEST</tt> for records.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>select PAYMENT_SEQ_NO, CAN_RETRY, GMT_CREATE, GMT_MODIFIED from TT_CMF_REQUEST where ((PAYMENT_SEQ_NO = ?) AND (SETTLEMENT_ID = ?)) for update</tt>
     *
     * @param paymentSeqNo
     * @param settlementId
     * @return CmfRequestDO @
     */
    @Override
    public CmfRequestDO lockedById(String paymentSeqNo, String settlementId) {
        Map param = new HashMap();

        param.put("paymentSeqNo", paymentSeqNo);
        param.put("settlementId", settlementId);

        return (CmfRequestDO)getSqlSession().selectOne("MS-CMF-REQUEST-LOCKED-BY-ID", param);

    }

    /**
     * Update DB table <tt>TT_CMF_REQUEST</tt>.
     *
     * <p>
     * The sql statement for this operation is <br>
     * <tt>update TT_CMF_REQUEST set gmt_modified=systimestamp, CAN_RETRY=? where ((PAYMENT_SEQ_NO = ?) AND (SETTLEMENT_ID = ?))</tt>
     *
     * @param canRetry
     * @param paymentSeqNo
     * @param settlementId
     * @return int @
     */
    @Override
    public int updateStatusById(String canRetry, String paymentSeqNo, String settlementId) {
        Map param = new HashMap();

        param.put("canRetry", canRetry);
        param.put("paymentSeqNo", paymentSeqNo);
        param.put("settlementId", settlementId);

        return getSqlSession().update("MS-CMF-REQUEST-UPDATE-STATUS-BY-ID", param);
    }

    @Override
    public String insert2OldDb(CmfRequestDO cmfRequest) {
        if (cmfRequest == null) {
            throw new IllegalArgumentException("Can't insert a null data object into db.");
        }

        getSqlSession().insert("MS-CMF-REQUEST-OLD-DB-INSERT", cmfRequest);

        return cmfRequest.getPaymentSeqNo();
    }

}