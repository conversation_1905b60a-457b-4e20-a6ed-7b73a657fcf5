package com.uaepay.cmf.common.core.dal.dataobject;

import lombok.Data;
import java.util.Date;

@Data
public class ChannelCodeMappingDO {
    private Long id;
    private String oldChannelCode;
    private String newChannelCode;
    private String matchExpression;
    private Integer priority;
    /**
     * Status: Y-enabled, N-disabled
     */
    private String status;
    private String memo;
    /**
     * Rule name
     */
    private String ruleName;
    private Date gmtCreate;
    private Date gmtModified;
} 