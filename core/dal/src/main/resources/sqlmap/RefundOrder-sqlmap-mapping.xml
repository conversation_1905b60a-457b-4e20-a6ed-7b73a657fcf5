<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR>                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_REFUND_ORDER -->
    <resultMap id="RM-REFUND-ORDER" type="com.uaepay.cmf.common.core.dal.dataobject.RefundOrderDO">
        <result property="instOrderId" column="INST_ORDER_ID" javaType="long" jdbcType="NUMERIC" />
        <result property="fundinOrderNo" column="FUNDIN_ORDER_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="fundinRealAmount" column="FUNDIN_REAL_AMOUNT"/>
        <result property="fundinDate" column="FUNDIN_DATE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
    </resultMap>

	<sql id="REFUND-ORDER-COLUMNS">
        INST_ORDER_ID, FUNDIN_ORDER_NO, FUNDIN_REAL_AMOUNT, CURRENCY, FUNDIN_DATE, GMT_MODIFIED
    </sql>

    <!-- ============================================= -->
    <!-- mapped statements for IbatisRefundOrderDAO -->
    <!-- ============================================= -->
    <!-- mapped statement for IbatisRefundOrderDAO.insert -->
    <insert id="MS-REFUND-ORDER-INSERT">

        insert into TT_REFUND_ORDER(
        <include refid="REFUND-ORDER-COLUMNS"/>
		<![CDATA[
        ) values (#{instOrderId}, #{fundinOrderNo}, #{fundinRealAmount.amount}, #{fundinRealAmount.currency}, #{fundinDate}, now())
    ]]>
    </insert>

    <!-- mapped statement for IbatisRefundOrderDAO.delete -->
    <delete id="MS-REFUND-ORDER-DELETE">
    <![CDATA[
        delete from TT_REFUND_ORDER where (INST_ORDER_ID = #{value})
    ]]>
    </delete>

    <!-- mapped statement for IbatisRefundOrderDAO.loadById -->
    <select id="MS-REFUND-ORDER-LOAD-BY-ID" resultMap="RM-REFUND-ORDER">
        select <include refid="REFUND-ORDER-COLUMNS"/>
        <![CDATA[
             from TT_REFUND_ORDER where (INST_ORDER_ID = #{value})
        ]]>
    </select>

    <!-- mapped statement for IbatisRefundOrderDAO.lockedById -->
    <select id="MS-REFUND-ORDER-LOCKED-BY-ID" resultMap="RM-REFUND-ORDER">
        select <include refid="REFUND-ORDER-COLUMNS"/>
    <![CDATA[
         from TT_REFUND_ORDER where (INST_ORDER_ID = #{value}) for update
    ]]>
    </select>

</mapper>