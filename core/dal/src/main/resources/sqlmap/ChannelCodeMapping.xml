<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO">
    
    <resultMap id="BaseResultMap" type="com.uaepay.cmf.common.core.dal.dataobject.ChannelCodeMappingDO">
        <id column="id" property="id" />
        <result column="old_channel_code" property="oldChannelCode" />
        <result column="new_channel_code" property="newChannelCode" />
        <result column="match_expression" property="matchExpression" />
        <result column="priority" property="priority" />
        <result column="status" property="status" />
        <result column="rule_name" property="ruleName" />
        <result column="memo" property="memo" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <sql id="Base_Column_List">
        id, old_channel_code, new_channel_code, match_expression, 
        priority, status, rule_name, memo, gmt_create, gmt_modified
    </sql>

    <select id="com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.selectByOldChannelCode" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM t_channel_code_mapping
        WHERE old_channel_code = #{oldChannelCode}
        AND status = 'Y'
        ORDER BY priority DESC
    </select>

    <insert id="com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.insert" parameterType="com.uaepay.cmf.common.core.dal.dataobject.ChannelCodeMappingDO">
        INSERT INTO t_channel_code_mapping (
            old_channel_code,
            new_channel_code,
            match_expression,
            priority,
            status,
            rule_name,
            memo,
            gmt_create,
            gmt_modified
        )
        VALUES (
            #{oldChannelCode},
            #{newChannelCode},
            #{matchExpression},
            #{priority},
            #{status},
            #{ruleName},
            #{memo},
            NOW(),
            NOW()
        )
    </insert>

    <update id="com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.updateById" parameterType="com.uaepay.cmf.common.core.dal.dataobject.ChannelCodeMappingDO">
        UPDATE t_channel_code_mapping
        <set>
            <if test="oldChannelCode != null">old_channel_code = #{oldChannelCode},</if>
            <if test="newChannelCode != null">new_channel_code = #{newChannelCode},</if>
            <if test="matchExpression != null">match_expression = #{matchExpression},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="status != null">status = #{status},</if>
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="memo != null">memo = #{memo},</if>
            gmt_modified = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.updateStatus">
        UPDATE t_channel_code_mapping
        SET 
            status = #{status},
            gmt_modified = NOW()
        WHERE id = #{id}
    </update>

    <!-- Common where clause for page query -->
    <sql id="Page_Query_Where">
        <where>
            <if test="oldChannelCode != null and oldChannelCode != ''">
                AND old_channel_code = #{oldChannelCode}
            </if>
            <if test="newChannelCode != null and newChannelCode != ''">
                AND new_channel_code = #{newChannelCode}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="ruleName != null and ruleName != ''">
                AND rule_name LIKE CONCAT('%', #{ruleName}, '%')
            </if>
        </where>
    </sql>

    <!-- Page query -->
    <select id="com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.pageQuery" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_channel_code_mapping
        <include refid="Page_Query_Where" />
        ORDER BY priority DESC, id DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- Count total records -->
    <select id="com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.pageCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_channel_code_mapping
        <include refid="Page_Query_Where" />
    </select>

    <select id="com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.selectAllValidRules" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_channel_code_mapping
        WHERE status = 'Y'
        ORDER BY old_channel_code ASC, priority DESC
    </select>

    <delete id="com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO.deleteByOldChannelCode">
        delete from t_channel_code_mapping where old_channel_code = #{oldChannelCode}
    </delete>

</mapper> 