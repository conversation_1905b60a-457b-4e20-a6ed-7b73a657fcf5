<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR>                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_CONTROL_ORDER_RESULT -->
    <resultMap id="RM-CONTROL-ORDER-RESULT" type="com.uaepay.cmf.common.core.dal.dataobject.ControlOrderResultDO">
        <result property="resultId" column="RESULT_ID" javaType="long" jdbcType="NUMERIC"/>
        <result property="controlOrderId" column="CONTROL_ORDER_ID" javaType="long" jdbcType="NUMERIC"/>
        <result property="amount" column="AMOUNT"/>
        <result property="instOrderNo" column="INST_ORDER_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="instResultCode" column="INST_RESULT_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="apiType" column="API_TYPE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="apiResultCode" column="API_RESULT_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="apiSubResultCode" column="API_SUB_RESULT_CODE" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="resultMessage" column="RESULT_MESSAGE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="memo" column="MEMO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="extension" column="EXTENSION" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="GMT_CREATE" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="INST-CONTROL-ORDER-RESULT-COLUMNS">
        RESULT_ID, CONTROL_ORDER_ID, AMOUNT, CURRENCY, INST_ORDER_NO, INST_RESULT_CODE, API_TYPE, STATUS, API_RESULT_CODE,
        API_SUB_RESULT_CODE, RESULT_MESSAGE, MEMO, EXTENSION, GMT_CREATE, GMT_MODIFIED
    </sql>

    <!-- mapped statement for IbatisControlOrderResultDAO.loadWithControlOrderId -->
    <select id="MS-CONTROL-ORDER-RESULT-LOAD-WITH-CONTROL-ORDER-ID" resultMap="RM-CONTROL-ORDER-RESULT">
        select
        <include refid="INST-CONTROL-ORDER-RESULT-COLUMNS"/>
        <![CDATA[
         from TT_CONTROL_ORDER_RESULT where (CONTROL_ORDER_ID = #{value})
    ]]>
    </select>

    <!-- mapped statement for IbatisControlOrderResultDAO.loadWithInstOrderNo -->
    <select id="MS-CONTROL-ORDER-RESULT-LOAD-WITH-INST-ORDER-NO" resultMap="RM-CONTROL-ORDER-RESULT">
        select
        <include refid="INST-CONTROL-ORDER-RESULT-COLUMNS"/>
        <![CDATA[
        from TT_CONTROL_ORDER_RESULT where (INST_ORDER_NO = #{value})
        ]]>
    </select>

    <!-- mapped statement for IbatisControlOrderResultDAO.insert -->
    <insert id="MS-CONTROL-ORDER-RESULT-INSERT">
        insert into TT_CONTROL_ORDER_RESULT(
        <include refid="INST-CONTROL-ORDER-RESULT-COLUMNS"/>
    <![CDATA[
    ) values (#{resultId}, #{controlOrderId}, #{amount.amount}, #{amount.currency}, #{instOrderNo}, #{instResultCode},
    #{apiType}, #{status}, #{apiResultCode}, #{apiSubResultCode}, #{resultMessage}, #{memo}, #{extension}, now(), now())
    ]]>
    </insert>

</mapper>