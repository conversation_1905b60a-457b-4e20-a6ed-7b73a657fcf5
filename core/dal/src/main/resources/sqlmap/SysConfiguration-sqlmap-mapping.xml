<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR> won                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TM_SYS_CONFIGURATION -->
    <resultMap id="RM-SYS-CONFIGURATION" type="com.uaepay.cmf.common.core.dal.dataobject.SysConfigurationDO">
        <result property="attrName" column="ATTR_NAME" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="attrValue" column="ATTR_VALUE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="memo" column="MEMO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="gmtCreated" column="GMT_CREATED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
    </resultMap>


    <!-- ============================================= -->
    <!-- mapped statements for IbatisSysConfigurationDAO -->
    <!-- ============================================= -->
    <!-- mapped statement for IbatisSysConfigurationDAO.insert -->
    <insert id="MS-SYS-CONFIGURATION-INSERT">
    <![CDATA[
        insert into TM_SYS_CONFIGURATION(ATTR_NAME,ATTR_VALUE,MEMO,GMT_CREATED,GMT_MODIFIED) values (#{attrName}, #{attrValue}, #{memo}, now(), now())
    ]]>
    </insert>

    <update id="MS-SYS-CONFIGURATION-UPDATE">
        <![CDATA[
            update TM_SYS_CONFIGURATION set GMT_MODIFIED=now(), ATTR_VALUE=#{attrValue}, MEMO=#{memo} where (ATTR_NAME = #{attrName})
        ]]>
    </update>

    <!-- mapped statement for IbatisSysConfigurationDAO.loadByKey -->
    <select id="MS-SYS-CONFIGURATION-LOAD-BY-KEY" resultMap="RM-SYS-CONFIGURATION">
    <![CDATA[
        select ATTR_NAME, ATTR_VALUE, MEMO, GMT_CREATED, GMT_MODIFIED from TM_SYS_CONFIGURATION where (ATTR_NAME = #{value})
    ]]>
    </select>

    <!-- mapped statement for IbatisSysConfigurationDAO.loadAll -->
    <select id="MS-SYS-CONFIGURATION-LOAD-ALL" resultMap="RM-SYS-CONFIGURATION">
    <![CDATA[
        select ATTR_NAME, ATTR_VALUE, MEMO, GMT_CREATED, GMT_MODIFIED from TM_SYS_CONFIGURATION order by ATTR_NAME DESC
    ]]>
    </select>

</mapper>
