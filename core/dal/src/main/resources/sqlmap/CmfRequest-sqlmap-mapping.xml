<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR>                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_CMF_REQUEST${tabSuffix} -->
    <resultMap id="RM-CMF-REQUEST" type="com.uaepay.cmf.common.core.dal.dataobject.CmfRequestDO">
        <result property="paymentSeqNo" column="PAYMENT_SEQ_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="canRetry" column="CAN_RETRY" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="gmtCreate" column="GMT_CREATE" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="settlementId" column="SETTLEMENT_ID" javaType="java.lang.String" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="CMF-REQUEST-COLUMNS">
        PAYMENT_SEQ_NO, SETTLEMENT_ID, CAN_RETRY, GMT_CREATE, GMT_MODIFIED
    </sql>

    <!-- ============================================= -->
    <!-- mapped statements for IbatisCmfRequestDAO -->
    <!-- ============================================= -->
    <!-- mapped statement for IbatisCmfRequestDAO.insert -->
    <insert id="MS-CMF-REQUEST-INSERT">
        insert into TT_CMF_REQUEST${tabSuffix}(<include refid="CMF-REQUEST-COLUMNS"/>
    <![CDATA[
        ) values (#{paymentSeqNo}, #{settlementId}, #{canRetry}, now(), now())
    ]]>
    </insert>

    <insert id="MS-CMF-REQUEST-OLD-DB-INSERT">

        insert into TT_CMF_REQUEST${tabSuffix}(<include refid="CMF-REQUEST-COLUMNS"/>
        <![CDATA[
        ) values (#{paymentSeqNo}, #{settlementId}, #{canRetry}, now(), now())
    ]]>
    </insert>

    <!-- mapped statement for IbatisCmfRequestDAO.loadById -->
    <select id="MS-CMF-REQUEST-LOAD-BY-ID" resultMap="RM-CMF-REQUEST">

        select <include refid="CMF-REQUEST-COLUMNS"/>
    <![CDATA[
        from TT_CMF_REQUEST${tabSuffix} where ((PAYMENT_SEQ_NO = #{paymentSeqNo}) AND (SETTLEMENT_ID = #{settlementId}))
    ]]>
    </select>

    <!-- mapped statement for IbatisCmfRequestDAO.lockedById -->
    <select id="MS-CMF-REQUEST-LOCKED-BY-ID" resultMap="RM-CMF-REQUEST">

        select <include refid="CMF-REQUEST-COLUMNS"/>
    <![CDATA[
        from TT_CMF_REQUEST${tabSuffix} where ((PAYMENT_SEQ_NO = #{paymentSeqNo}) AND (SETTLEMENT_ID = #{settlementId})) for update
    ]]>
    </select>

    <!-- mapped statement for IbatisCmfRequestDAO.updateStatusById -->
    <update id="MS-CMF-REQUEST-UPDATE-STATUS-BY-ID">
        <![CDATA[
        update TT_CMF_REQUEST${tabSuffix} set gmt_modified=now(), CAN_RETRY=#{canRetry} where ((PAYMENT_SEQ_NO = #{paymentSeqNo})
         AND (SETTLEMENT_ID = #{settlementId}))
    ]]>
    </update>

</mapper>