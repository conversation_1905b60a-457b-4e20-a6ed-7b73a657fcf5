<?xml version="1.0" encoding="GB2312" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR>                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result map for Money class -->

   <!-- <resultMap id="RM-MONEY" type="com.uaepay.common.util.money.Money">
        <result property="amount" columnIndex="1" javaType="java.math.BigDecimal" jdbcType="NUMERIC" />
		&lt;!&ndash;<result property="currencyCode" columnIndex="2" javaType="java.lang.String" jdbcType="VARCHAR" nullValue="AED"/>&ndash;&gt;
    </resultMap>-->
</mapper>