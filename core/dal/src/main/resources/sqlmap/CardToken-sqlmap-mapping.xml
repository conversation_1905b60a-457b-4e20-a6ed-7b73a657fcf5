<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.uaepay.channel.cards.core.dal.mapper.CardTokenMapper">
    <resultMap id="BaseResultMap" type="com.uaepay.cmf.common.core.dal.dataobject.CardTokenDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Mar 28 15:09:02 GST 2020.
        -->
        <id column="card_token_id" jdbcType="VARCHAR" property="cardTokenId"/>
        <result column="inst_order_id" jdbcType="BIGINT" property="instOrderId"/>
        <result column="token_type" jdbcType="VARCHAR" property="tokenType"/>
        <result column="member_id" jdbcType="VARCHAR" property="memberId"/>
        <result column="card_id" jdbcType="BIGINT" property="cardId"/>
        <result column="beneficiary_id" jdbcType="BIGINT" property="beneficiaryId"/>
        <result column="session_id" jdbcType="VARCHAR" property="sessionId"/>
        <result column="inst_code" jdbcType="VARCHAR" property="instCode"/>
        <result column="issue_bank" jdbcType="VARCHAR" property="issueBank"/>
        <result column="dbcr" jdbcType="CHAR" property="dbcr"/>
        <result column="company_or_personal" jdbcType="CHAR" property="companyOrPersonal"/>
        <result column="first_bind" jdbcType="CHAR" property="firstBind"/>
        <result column="is_3ds" jdbcType="CHAR" property="is3DS"/>
        <result column="card_no" jdbcType="VARCHAR" property="cardNo"/>
        <result column="country_code" jdbcType="VARCHAR" property="countryCode"/>
        <result column="card_holder" jdbcType="VARCHAR" property="cardHolder"/>
        <result column="inst_token_id" jdbcType="VARCHAR" property="instTokenId"/>
        <result column="card_expired" jdbcType="VARCHAR" property="cardExpired"/>
        <result column="card_type" jdbcType="VARCHAR" property="cardType"/>
        <result column="card_brand" jdbcType="VARCHAR" property="cardBrand"/>
        <result column="need_csc" jdbcType="VARCHAR" property="needCsc"/>
        <result column="ip_address" jdbcType="VARCHAR" property="ipAddress"/>
        <result column="iban" jdbcType="VARCHAR" property="iban"/>
        <result column="card_account_no" jdbcType="VARCHAR" property="cardAccountNo"/>
        <result column="result_url" jdbcType="VARCHAR" property="resultUrl"/>
        <result column="extension" jdbcType="VARCHAR" property="extension"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Mar 28 15:09:02 GST 2020.
        -->
        card_token_id, inst_order_id, token_type, member_id, card_id, beneficiary_id, session_id, inst_code, issue_bank, dbcr, company_or_personal,
        is_3ds, first_bind,
        card_no, country_code, card_holder, inst_token_id, card_expired, card_type, card_brand, need_csc, ip_address, iban,
        card_account_no,result_url,
        extension, gmt_create, gmt_modified
    </sql>
    <select id="MS-CARD-TOKEN-LOAD-BY-ID" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Mar 28 15:09:02 GST 2020.
        -->
        select
        <include refid="Base_Column_List"/>
        from tb_card_token
        where card_token_id = #{cardTokenId,jdbcType=VARCHAR}
    </select>
    <insert id="MS-CARD-TOKEN-INSERT" parameterType="com.uaepay.cmf.common.core.dal.dataobject.CardTokenDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Mar 28 15:09:02 GST 2020.
        -->
        insert into tb_card_token (card_token_id, inst_order_id, token_type, member_id, card_id, beneficiary_id,
        session_id, inst_code, issue_bank, dbcr, company_or_personal, is_3ds, first_bind,
        card_no,  country_code, card_holder, inst_token_id, card_expired, card_type, card_brand, need_csc, ip_address, iban,
        card_account_no, result_url,
        extension, gmt_create,
        gmt_modified)
        values (#{cardTokenId,jdbcType=VARCHAR}, #{instOrderId,jdbcType=BIGINT}, #{tokenType, jdbcType=VARCHAR},
        #{memberId,jdbcType=VARCHAR}, #{cardId,jdbcType=BIGINT}, #{beneficiaryId,jdbcType=BIGINT},
        #{sessionId,jdbcType=VARCHAR}, #{instCode,jdbcType=VARCHAR}, #{issueBank,jdbcType=VARCHAR}, #{dbcr,jdbcType=CHAR},
        #{companyOrPersonal,jdbcType=CHAR}, #{is3DS,jdbcType=CHAR}, #{firstBind,jdbcType=CHAR},
        #{cardNo,jdbcType=VARCHAR}, #{countryCode,jdbcType=VARCHAR}, #{cardHolder,jdbcType=VARCHAR}, #{instTokenId,jdbcType=VARCHAR},
        #{cardExpired,jdbcType=VARCHAR},
        #{cardType,jdbcType=VARCHAR}, #{cardBrand,jdbcType=VARCHAR}, #{needCsc, jdbcType=VARCHAR},
        #{ipAddress,jdbcType=VARCHAR}, #{iban,jdbcType=VARCHAR}, #{cardAccountNo,jdbcType=VARCHAR}, #{resultUrl,jdbcType=VARCHAR},
        #{extension,jdbcType=VARCHAR}, now(),
        now())
    </insert>
    <update id="MS-CARD-TOKEN-UPDATE" parameterType="com.uaepay.cmf.common.core.dal.dataobject.CardTokenDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Mar 28 15:09:02 GST 2020.
        -->
        update tb_card_token
        set
        inst_order_id = #{instOrderId,jdbcType=BIGINT},
        token_type = #{tokenType,jdbcType=VARCHAR},
        member_id = #{memberId,jdbcType=VARCHAR},
        card_id = #{cardId,jdbcType=BIGINT},
        beneficiary_id = #{beneficiaryId,jdbcType=BIGINT},
        session_id = #{sessionId,jdbcType=VARCHAR},
        inst_code = #{instCode,jdbcType=VARCHAR},
        issue_bank = #{issueBank,jdbcType=VARCHAR},
        dbcr = #{dbcr,jdbcType=CHAR},
        company_or_personal = #{companyOrPersonal,jdbcType=CHAR},
        is_3ds = #{is3DS,jdbcType=CHAR},
        first_bind = #{firstBind,jdbcType=CHAR},
        card_no = #{cardNo,jdbcType=VARCHAR},
        country_code = #{countryCode,jdbcType=VARCHAR},
        card_holder = #{cardHolder,jdbcType=VARCHAR},
        inst_token_id = #{instTokenId,jdbcType=VARCHAR},
        card_expired = #{cardExpired,jdbcType=VARCHAR},
        card_type = #{cardType,jdbcType=VARCHAR},
        card_brand = #{cardBrand,jdbcType=VARCHAR},
        need_csc = #{needCsc,jdbcType=VARCHAR},
        ip_address =  #{ipAddress,jdbcType=VARCHAR},
        iban = #{iban,jdbcType=VARCHAR},
        card_account_no = #{cardAccountNo,jdbcType=VARCHAR},
        result_url = #{resultUrl,jdbcType=VARCHAR},
        extension = #{extension,jdbcType=VARCHAR},
        gmt_modified = now()
        where card_token_id = #{cardTokenId,jdbcType=VARCHAR}
    </update>
    <update id="MS-CARD-TOKEN-UPDATE-SELECTIVE" parameterType="com.uaepay.cmf.common.core.dal.dataobject.CardTokenDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Jan 12 15:16:54 CST 2021.
        -->
        update tb_card_token
        <set>
            <if test="instOrderId != null">
                inst_order_id = #{instOrderId,jdbcType=BIGINT},
            </if>
            <if test="tokenType != null">
                token_type = #{tokenType,jdbcType=VARCHAR},
            </if>
            <if test="memberId != null">
                member_id = #{memberId,jdbcType=VARCHAR},
            </if>
            <if test="cardId != null">
                card_id = #{cardId,jdbcType=BIGINT},
            </if>
            <if test="beneficiaryId != null">
                beneficiary_id = #{beneficiaryId,jdbcType=BIGINT},
            </if>
            <if test="sessionId != null">
                session_id = #{sessionId,jdbcType=VARCHAR},
            </if>
            <if test="instCode != null">
                inst_code = #{instCode,jdbcType=VARCHAR},
            </if>
            <if test="dbcr != null">
                dbcr = #{dbcr,jdbcType=CHAR},
            </if>
            <if test="companyOrPersonal != null">
                company_or_personal = #{companyOrPersonal,jdbcType=CHAR},
            </if>
            <if test="is3DS != null">
                is_3ds = #{is3DS,jdbcType=CHAR},
            </if>
            <if test="firstBind != null">
                first_bind = #{firstBind,jdbcType=CHAR},
            </if>
            <if test="needCsc != null">
                need_csc = #{needCsc,jdbcType=CHAR},
            </if>
            <if test="ipAddress != null">
                ip_address = #{ipAddress,jdbcType=VARCHAR},
            </if>
            <if test="iban != null">
                iban = #{iban,jdbcType=VARCHAR},
            </if>
            <if test="cardAccountNo != null">
                card_account_no = #{cardAccountNo,jdbcType=VARCHAR},
            </if>
            <if test="extension != null">
                extension = #{extension,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null">
                card_no = #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="countryCode != null">
                country_code = #{countryCode,jdbcType=VARCHAR},
            </if>
            <if test="cardHolder != null">
                card_holder = #{cardHolder,jdbcType=VARCHAR},
            </if>
            <if test="instTokenId != null">
                inst_token_id = #{instTokenId,jdbcType=VARCHAR},
            </if>
            <if test="cardExpired != null">
                card_expired = #{cardExpired,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                card_type = #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="cardBrand != null">
                card_brand = #{cardBrand,jdbcType=VARCHAR},
            </if>
            <if test="resultUrl != null">
                result_url = #{resultUrl,jdbcType=VARCHAR},
            </if>
            <if test="issueBank != null">
                issue_bank = #{issueBank,jdbcType=VARCHAR},
            </if>
            gmt_modified = now()
        </set>
        where card_token_id = #{cardTokenId,jdbcType=BIGINT}
    </update>
    <update id="MS-CARD-TOKEN-UPDATE-INST-INFO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Jan 12 15:16:54 CST 2021.
        -->
        update tb_card_token set
                inst_order_id = #{instOrderId,jdbcType=BIGINT},
                inst_token_id = #{instTokenId,jdbcType=VARCHAR},
            gmt_modified = now()
        where card_token_id = #{cardTokenId,jdbcType=BIGINT} and inst_order_id is null and inst_token_id is null
    </update>
    <select id="MS-CARD-TOKEN-LOAD-BY-INST-ORDER-ID" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Mar 28 15:09:02 GST 2020.
        -->
        select
        <include refid="Base_Column_List"/>
        from tb_card_token
        where inst_order_id = #{instOrderId,jdbcType=BIGINT}
    </select>
</mapper>