<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR>                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_FUNDIN_ORDER -->
    <resultMap id="RM-FUNDIN-ORDER" type="com.uaepay.cmf.common.core.dal.dataobject.FundinOrderDO">
        <result property="instOrderId" column="INST_ORDER_ID" javaType="long" jdbcType="NUMERIC" />
        <result property="cardType" column="CARD_TYPE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="payerInstCode" column="PAYER_INST_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="contractNo" column="CONTRACT_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtCreated" column="GMT_CREATED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
    </resultMap>


    <!-- ============================================= -->
    <!-- mapped statements for IbatisFundinOrderDAO -->
    <!-- ============================================= -->
    <!-- mapped statement for IbatisFundinOrderDAO.insert -->
    <insert id="MS-FUNDIN-ORDER-INSERT">
    <![CDATA[
        insert into TT_FUNDIN_ORDER(INST_ORDER_ID,CARD_TYPE,PAYER_INST_CODE,CONTRACT_NO,GMT_MODIFIED,GMT_CREATED) values (#{instOrderId}, #{cardType}, #{payerInstCode}, #{contractNo}, now(), now())
    ]]>
    </insert>

    <!-- mapped statement for IbatisFundinOrderDAO.loadById -->
    <select id="MS-FUNDIN-ORDER-LOAD-BY-ID" resultMap="RM-FUNDIN-ORDER">
    <![CDATA[
        select INST_ORDER_ID, CARD_TYPE, PAYER_INST_CODE, CONTRACT_NO, GMT_MODIFIED, GMT_CREATED from TT_FUNDIN_ORDER where (INST_ORDER_ID = #{value})
    ]]>
    </select>

    <!-- mapped statement for IbatisFundinOrderDAO.delete -->
    <delete id="MS-FUNDIN-ORDER-DELETE">
    <![CDATA[
        delete from TT_FUNDIN_ORDER where (INST_ORDER_ID = #{value})
    ]]>
    </delete>

    <!-- mapped statement for IbatisFundinOrderDAO.lockedById -->
    <select id="MS-FUNDIN-ORDER-LOCKED-BY-ID" resultMap="RM-FUNDIN-ORDER">
    <![CDATA[
        select INST_ORDER_ID, CARD_TYPE, PAYER_INST_CODE, CONTRACT_NO, GMT_MODIFIED, GMT_CREATED from TT_FUNDIN_ORDER where (INST_ORDER_ID = #{value}) for update
    ]]>
    </select>

</mapper>