<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cmf">
    <resultMap id="BaseResultMap" type="com.uaepay.cmf.common.core.dal.dataobject.Notify3dsResultDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 16 22:59:02 GST 2020.
        -->
        <id column="result_id" jdbcType="BIGINT" property="resultId"/>
        <result column="card_token_id" jdbcType="BIGINT" property="cardTokenId"/>
        <result column="inst_order_no" jdbcType="VARCHAR" property="instOrderNo"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="trade_order_no" jdbcType="VARCHAR" property="tradeOrderNo"/>
        <result column="payment_order_no" jdbcType="VARCHAR" property="paymentOrderNo"/>
        <result column="eci" jdbcType="CHAR" property="eci"/>
        <result column="identity_result" jdbcType="CHAR" property="identityResult"/>
        <result column="identity_result_desc" jdbcType="VARCHAR" property="identityResultDesc"/>
        <result column="extension" jdbcType="VARCHAR" property="extension"/>
        <result column="gmt_cmf_request" jdbcType="TIMESTAMP" property="gmtCmfRequest"/>
        <result column="gmt_bank_request" jdbcType="TIMESTAMP" property="gmtBankRequest"/>
        <result column="gmt_bank_response" jdbcType="TIMESTAMP" property="gmtBankResponse"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 16 22:59:02 GST 2020.
        -->
        result_id, card_token_id, inst_order_no, channel_code, trade_order_no, payment_order_no, eci, identity_result,
        identity_result_desc, extension, gmt_cmf_request, gmt_bank_request, gmt_bank_response
    </sql>
    <select id="MS-NOTIFY-3DS-RESULT-LOAD-BY-ID" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 16 22:59:02 GST 2020.
        -->
        select
        <include refid="Base_Column_List"/>
        from tb_notify_3ds_result
        where result_id = #{resultId,jdbcType=BIGINT}
    </select>
    <select id="MS-NOTIFY-3DS-RESULT-LOAD-BY-CONDITION" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 16 22:59:02 GST 2020.
        -->
        select
        <include refid="Base_Column_List"/>
        from tb_notify_3ds_result
        where 1=1
        <if test="instOrderNo != null">
            and inst_order_no=#{instOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="productOrderNo != null">
            and product_order_no=#{productOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="paymentOrderNo != null">
            and payment_order_no=#{paymentOrderNo,jdbcType=VARCHAR}
        </if>
    </select>
    <insert id="MS-NOTIFY-3DS-RESULT-INSERT"
            parameterType="com.uaepay.cmf.common.core.dal.dataobject.Notify3dsResultDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 16 22:59:02 GST 2020.
        -->
        insert into tb_notify_3ds_result (result_id, card_token_id, inst_order_no, channel_code, trade_order_no,
        payment_order_no, eci, identity_result,
        identity_result_desc, extension, gmt_cmf_request,
        gmt_bank_request, gmt_bank_response)
        values (#{resultId,jdbcType=BIGINT}, #{cardTokenId,jdbcType=BIGINT}, #{instOrderNo,jdbcType=VARCHAR}, #{channelCode,jdbcType=VARCHAR}, #{tradeOrderNo,jdbcType=VARCHAR},
        #{paymentOrderNo,jdbcType=VARCHAR}, #{eci,jdbcType=CHAR}, #{identityResult,jdbcType=CHAR},
        #{identityResultDesc,jdbcType=VARCHAR}, #{extension,jdbcType=VARCHAR}, #{gmtCmfRequest,jdbcType=TIMESTAMP},
        #{gmtBankRequest,jdbcType=TIMESTAMP}, #{gmtBankResponse,jdbcType=TIMESTAMP})
    </insert>
    <update id="MS-NOTIFY-3DS-RESULT-UPDATE"
            parameterType="com.uaepay.cmf.common.core.dal.dataobject.Notify3dsResultDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 16 22:59:02 GST 2020.
        -->
        update tb_notify_3ds_result
        set card_token_id = #{cardTokenId,jdbcType=BIGINT},
        inst_order_no = #{instOrderNo,jdbcType=VARCHAR},
        channel_code = #{channelCode,jdbcType=VARCHAR},
        trade_order_no = #{tradeOrderNo,jdbcType=VARCHAR},
        payment_order_no = #{paymentOrderNo,jdbcType=VARCHAR},
        eci = #{eci,jdbcType=CHAR},
        identity_result = #{identityResult,jdbcType=CHAR},
        identity_result_desc = #{identityResultDesc,jdbcType=VARCHAR},
        extension = #{extension,jdbcType=VARCHAR},
        gmt_cmf_request = #{gmtCmfRequest,jdbcType=TIMESTAMP},
        gmt_bank_request = #{gmtBankRequest,jdbcType=TIMESTAMP},
        gmt_bank_response = #{gmtBankResponse,jdbcType=TIMESTAMP}
        where result_id = #{resultId,jdbcType=BIGINT}
    </update>
</mapper>