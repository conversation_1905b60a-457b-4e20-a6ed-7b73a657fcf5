<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR>                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_CMF_ORDER${tabSuffix} -->
    <resultMap id="RM-CMF-ORDER" type="com.uaepay.cmf.common.core.dal.dataobject.CmfOrderDO">
        <result property="cmfSeqNo" column="CMF_SEQ_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="paymentSeqNo" column="PAYMENT_SEQ_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="requestBatchNo" column="REQUEST_BATCH_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="orderType" column="ORDER_TYPE" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="productCode" column="PRODUCT_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="paymentCode" column="PAYMENT_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="memberId" column="MEMBER_ID" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="amount" column="AMOUNT"/>
        <result property="instCode" column="INST_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="paymentNotifyStatus" column="PAYMENT_NOTIFY_STATUS" javaType="java.lang.String"
                jdbcType="CHAR"/>
        <result property="status" column="STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="operator" column="OPERATOR" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="GMT_CREATE" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="bizDate" column="BIZ_DATE" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="memo" column="MEMO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="confirmStatus" column="CONFIRM_STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="payMode" column="PAY_MODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="orgiPaymentSeqNo" column="ORGI_PAYMENT_SEQ_NO" javaType="java.lang.String" jdbcType="NUMERIC"/>
        <result property="extension" column="EXTENSION" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="settlementId" column="SETTLEMENT_ID" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="orgiSettlementId" column="ORGI_SETTLEMENT_ID" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="paymentVoucherNo" column="PAYMENT_VOUCHER_NO" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="merchantId" column="MERCHANT_ID" javaType="java.lang.String" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="CMF-ORDER-COLUMNS">
        CMF_SEQ_NO, PAYMENT_SEQ_NO, SETTLEMENT_ID, REQUEST_BATCH_NO, ORDER_TYPE, PRODUCT_CODE, PAYMENT_CODE, PAY_MODE,
        MEMBER_ID, AMOUNT, CURRENCY, INST_CODE, PAYMENT_NOTIFY_STATUS,
        STATUS, CONFIRM_STATUS, OPERATOR, ORGI_PAYMENT_SEQ_NO, ORGI_SETTLEMENT_ID,
        GMT_CREATE, BIZ_DATE, GMT_MODIFIED, MEMO, EXTENSION, PAYMENT_VOUCHER_NO, MERCHANT_ID
    </sql>


    <!-- ============================================= -->
    <!-- mapped statements for IbatisCmfOrderDAO -->
    <!-- ============================================= -->
    <!-- mapped statement for IbatisCmfOrderDAO.insert -->
    <insert id="MS-CMF-ORDER-INSERT">

        <![CDATA[
            INSERT INTO TT_CMF_ORDER${tabSuffix}(
        ]]>
        <include refid="CMF-ORDER-COLUMNS"/>
        <![CDATA[
            ) VALUES (#{cmfSeqNo}, #{paymentSeqNo}, #{settlementId}, #{requestBatchNo}, #{orderType}, #{productCode}, #{paymentCode}, #{payMode}, #{memberId}, #{amount.amount}, #{amount.currency}, #{instCode}, #{paymentNotifyStatus}, #{status}, #{confirmStatus}, #{operator}, #{orgiPaymentSeqNo}, #{orgiSettlementId}, now(), #{bizDate}, now(), #{memo}, #{extension},#{paymentVoucherNo},#{merchantId})
        ]]>
    </insert>

    <!-- mapped statement for IbatisCmfOrderDAO.loadById -->
    <select id="MS-CMF-ORDER-LOAD-BY-ID" resultMap="RM-CMF-ORDER">
        SELECT
        <include refid="CMF-ORDER-COLUMNS"/>
        <![CDATA[
          FROM TT_CMF_ORDER${tabSuffix} WHERE (CMF_SEQ_NO = #{value})
        ]]>
    </select>

    <!-- mapped statement for IbatisCmfOrderDAO.lockedById -->
    <select id="MS-CMF-ORDER-LOCKED-BY-ID" resultMap="RM-CMF-ORDER">
        SELECT
        <include refid="CMF-ORDER-COLUMNS"/>
        <![CDATA[
            FROM TT_CMF_ORDER${tabSuffix} WHERE (CMF_SEQ_NO = #{value}) FOR UPDATE
        ]]>
    </select>

    <!-- mapped statement for IbatisCmfOrderDAO.loadByPaymentNo -->
    <select id="MS-CMF-ORDER-LOAD-BY-PAYMENT-NO" resultMap="RM-CMF-ORDER">
        <![CDATA[
            SELECT * FROM (
        ]]>
        SELECT
        <include refid="CMF-ORDER-COLUMNS"/>
        FROM TT_CMF_ORDER${tabSuffix} WHERE PAYMENT_SEQ_NO = #{paymentSeqNo}
        <if test="settlementId != null">
                and SETTLEMENT_ID = #{settlementId}
        </if>
        <![CDATA[
            ORDER BY CMF_SEQ_NO DESC ) as t LIMIT 1
        ]]>
    </select>

    <select id="MS-CMF-ORDER-LOAD-BY-PAYMENT-ORDER-NO" resultMap="RM-CMF-ORDER">
        <![CDATA[
            SELECT * FROM (
        SELECT
        ]]>
        <include refid="CMF-ORDER-COLUMNS"/>
        FROM TT_CMF_ORDER${tabSuffix} WHERE PAYMENT_VOUCHER_NO = #{paymentOrderNo}
        <![CDATA[
        and GMT_CREATE >= #{gmtStart}
        and GMT_CREATE <= #{gmtEnd}
            ORDER BY GMT_CREATE ) as t LIMIT 0,1
        ]]>
    </select>

    <!-- mapped statement for IbatisCmfOrderDAO.updateStatusByCmfSeqNo -->
    <update id="MS-CMF-ORDER-UPDATE-STATUS-BY-CMF-SEQ-NO">
        UPDATE TT_CMF_ORDER${tabSuffix}
        SET STATUS = #{newStatus}, GMT_MODIFIED = now()
        WHERE CMF_SEQ_NO = #{id} and STATUS = #{oldStatus}
    </update>

    <!-- mapped statement for IbatisCmfOrderDAO.updatePaymentNotifyStatusById -->
    <update id="MS-CMF-ORDER-UPDATE-PAYMENT-NOTIFY-STATUS-BY-ID">
        <![CDATA[
        UPDATE TT_CMF_ORDER${tabSuffix} SET GMT_MODIFIED=now(), PAYMENT_NOTIFY_STATUS=#{paymentNotifyStatus} WHERE (CMF_SEQ_NO = #{cmfSeqNo})
    ]]>
    </update>

    <!-- mapped statement for IbatisCmfOrderDAO.updateConfirmStatusById -->
    <update id="MS-CMF-ORDER-UPDATE-CONFIRM-STATUS-BY-ID">
        <![CDATA[
        UPDATE TT_CMF_ORDER${tabSuffix} SET GMT_MODIFIED=now(), CONFIRM_STATUS=#{confirmStatus} WHERE (CMF_SEQ_NO = #{cmfSeqNo})
    ]]>
    </update>

    <update id="MS-CMF-ORDER-UPDATE-STATUS-CONFIRM-STATUS-BY-ID">
        <![CDATA[
        UPDATE TT_CMF_ORDER${tabSuffix} SET GMT_MODIFIED=now(), STATUS=#{status},CONFIRM_STATUS=#{confirmStatus}  WHERE (CMF_SEQ_NO = #{cmfSeqNo} AND STATUS=#{preStatus})
    ]]>
    </update>

    <select id="MS-CMF-ORDER-LOAD-BY-ORGI-SETTLEID" resultMap="RM-CMF-ORDER">
        SELECT
        <include refid="CMF-ORDER-COLUMNS"/>
          FROM TT_CMF_ORDER${tabSuffix} WHERE GMT_CREATE > #{dateStart} and ORGI_SETTLEMENT_ID = #{orgiSettlementId}
           <if test="ignoreInstOrderIds != null and ignoreInstOrderIds.size()>0">
            and CMF_SEQ_NO not in (select cmf_seq_no from TT_INST_ORDER${tabSuffix} where inst_order_id in
            <foreach collection="ignoreInstOrderIds" item="ignoreInstOrderId" open="(" close=")" separator="," >
               #{ignoreInstOrderId}

            </foreach>)
          </if>
    </select>

</mapper>