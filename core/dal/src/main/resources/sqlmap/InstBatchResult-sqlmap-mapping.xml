<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR> won                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_INST_BATCH_RESULT -->
    <resultMap id="RM-INST-BATCH-RESULT" type="com.uaepay.cmf.common.core.dal.dataobject.InstBatchResultDO">
        <result property="batchResultId" column="BATCH_RESULT_ID" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="archiveBatchId" column="ARCHIVE_BATCH_ID" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="batchType" column="BATCH_TYPE" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="fundChannelCode" column="FUND_CHANNEL_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="totalCount" column="TOTAL_COUNT" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="totalAmount" column="TOTAL_AMOUNT"/>
        <result property="successCount" column="SUCCESS_COUNT" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="successAmount" column="SUCCESS_AMOUNT"/>
        <result property="failedCount" column="FAILED_COUNT" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="failedAmount" column="FAILED_AMOUNT"/>
        <result property="differentCount" column="DIFFERENT_COUNT" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="differentAmount" column="DIFFERENT_AMOUNT" />
        <result property="lessCount" column="LESS_COUNT" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="moreCount" column="MORE_COUNT" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="status" column="STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="filePath" column="FILE_PATH" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="gmtReturn" column="GMT_RETURN" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="GMT_CREATE" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="memo" column="MEMO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="fundChannelApi" column="FUND_CHANNEL_API" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="bizType" column="BIZ_TYPE" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="checkOperater" column="CHECK_OPERATER" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="importOperater" column="IMPORT_OPERATER" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="importType" column="IMPORT_TYPE" javaType="java.lang.String" jdbcType="CHAR"/>
    </resultMap>

    <!-- ============================================= -->
    <!-- mapped statements for IbatisInstBatchResultDAO -->
    <!-- ============================================= -->
	<sql id="INST-BATCH-RESULT-COLUMNS">
        BATCH_RESULT_ID, ARCHIVE_BATCH_ID, BIZ_TYPE, BATCH_TYPE, FUND_CHANNEL_API, FUND_CHANNEL_CODE, TOTAL_COUNT, TOTAL_AMOUNT,
        CURRENCY, SUCCESS_COUNT, SUCCESS_AMOUNT, FAILED_COUNT, FAILED_AMOUNT, DIFFERENT_COUNT, DIFFERENT_AMOUNT, LESS_COUNT,
        MORE_COUNT, STATUS, GMT_RETURN, GMT_CREATE, GMT_MODIFIED, MEMO, FILE_PATH, IMPORT_OPERATER, CHECK_OPERATER, IMPORT_TYPE
    </sql>
    <!-- mapped statement for IbatisInstBatchResultDAO.insert -->
    <insert id="MS-INST-BATCH-RESULT-INSERT">
        insert into TT_INST_BATCH_RESULT(
		<include refid="INST-BATCH-RESULT-COLUMNS"/>
    <![CDATA[
        ) values (#{batchResultId}, #{archiveBatchId}, #{bizType}, #{batchType}, #{fundChannelApi}, #{fundChannelCode},
        #{totalCount}, #{totalAmount.amount}, #{totalAmount.currency}, #{successCount}, #{successAmount.amount}, #{failedCount}, #{failedAmount.amount},
        #{differentCount}, #{differentAmount.amount}, #{lessCount}, #{moreCount}, #{status}, #{gmtReturn}, now(), now(),
        #{memo}, #{filePath}, #{importOperater}, #{checkOperater}, #{importType})
    ]]>
    </insert>

    <!-- mapped statement for IbatisInstBatchResultDAO.loadById -->
    <select id="MS-INST-BATCH-RESULT-LOAD-BY-ID" resultMap="RM-INST-BATCH-RESULT">
        select <include refid="INST-BATCH-RESULT-COLUMNS"/>
	<![CDATA[
        from TT_INST_BATCH_RESULT where (BATCH_RESULT_ID = #{value})
    ]]>
    </select>

</mapper>
