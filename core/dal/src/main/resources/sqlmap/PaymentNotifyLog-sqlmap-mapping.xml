<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR> won                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_PAYMENT_NOTIFY_LOG -->
    <resultMap id="RM-PAYMENT-NOTIFY-LOG" type="com.uaepay.cmf.common.core.dal.dataobject.PaymentNotifyLogDO">
        <result property="notifyLogId" column="NOTIFY_LOG_ID" javaType="long" jdbcType="NUMERIC" />
        <result property="channelSeqNo" column="CHANNEL_SEQ_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="notifyResult" column="NOTIFY_RESULT" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="gmtCreate" column="GMT_CREATE" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="memo" column="MEMO" javaType="java.lang.String" jdbcType="VARCHAR"/>
    </resultMap>


    <!-- ============================================= -->
    <!-- mapped statements for IbatisPaymentNotifyLogDAO -->
    <!-- ============================================= -->
    <!-- mapped statement for IbatisPaymentNotifyLogDAO.insert -->
    <insert id="MS-PAYMENT-NOTIFY-LOG-INSERT">
    <![CDATA[
        insert into TT_PAYMENT_NOTIFY_LOG(NOTIFY_LOG_ID,CHANNEL_SEQ_NO,NOTIFY_RESULT,GMT_CREATE,MEMO) values (#{notifyLogId}, #{channelSeqNo}, #{notifyResult}, now(), #{memo})
    ]]>
    </insert>

    <!-- mapped statement for IbatisPaymentNotifyLogDAO.loadById -->
    <select id="MS-PAYMENT-NOTIFY-LOG-LOAD-BY-ID" resultMap="RM-PAYMENT-NOTIFY-LOG">
    <![CDATA[
        select NOTIFY_LOG_ID, CHANNEL_SEQ_NO, NOTIFY_RESULT, GMT_CREATE, MEMO from TT_PAYMENT_NOTIFY_LOG where (NOTIFY_LOG_ID = #{value})
    ]]>
    </select>

    <!-- mapped statement for IbatisPaymentNotifyLogDAO.lockedById -->
    <select id="MS-PAYMENT-NOTIFY-LOG-LOCKED-BY-ID" resultMap="RM-PAYMENT-NOTIFY-LOG">
    <![CDATA[
        select NOTIFY_LOG_ID, CHANNEL_SEQ_NO, NOTIFY_RESULT, GMT_CREATE, MEMO from TT_PAYMENT_NOTIFY_LOG where (NOTIFY_LOG_ID = #{value})  for update
    ]]>
    </select>

    <!-- mapped statement for IbatisPaymentNotifyLogDAO.loadAll -->
    <select id="MS-PAYMENT-NOTIFY-LOG-LOAD-ALL" resultMap="RM-PAYMENT-NOTIFY-LOG">
    <![CDATA[
        select NOTIFY_LOG_ID, CHANNEL_SEQ_NO, NOTIFY_RESULT, GMT_CREATE, MEMO from TT_PAYMENT_NOTIFY_LOG order by NOTIFY_LOG_ID ASC
    ]]>
    </select>

</mapper>
