<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR> won                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_INST_BATCH_ORDER -->
    <resultMap id="RM-INST-BATCH-ORDER" type="com.uaepay.cmf.common.core.dal.dataobject.InstBatchOrderDO">
        <result property="archiveBatchId" column="ARCHIVE_BATCH_ID" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="archiveTemplateId" column="ARCHIVE_TEMPLATE_ID" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="orderType" column="ORDER_TYPE" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="fundChannelCode" column="FUND_CHANNEL_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="fundChannelApi" column="FUND_CHANNEL_API" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="payMode" column="PAY_MODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="totalAmount" column="TOTAL_AMOUNT"/>
        <result property="totalCount" column="TOTAL_COUNT" javaType="java.lang.Integer" jdbcType="NUMERIC"/>
        <result property="status" column="STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="isLocked" column="IS_LOCKED" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="operator" column="OPERATOR" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="gmtArchive" column="GMT_ARCHIVE" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="GMT_CREATE" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="memo" column="MEMO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="checkFlag" column="CHECK_FLAG" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="batchOrderNo" column="BATCH_ORDER_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="queryTimes" column="QUERY_TIMES" javaType="java.lang.Integer" jdbcType="NUMERIC"/>
        <result property="gmtNextRetry" column="GMT_NEXT_RETRY" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="extension" column="EXTENSION" javaType="java.lang.String" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="INST-BATCH-ORDER-COLUMNS">
        ARCHIVE_BATCH_ID, ORDER_TYPE, TOTAL_AMOUNT, TOTAL_COUNT, CURRENCY, FUND_CHANNEL_CODE,
        ARCHIVE_TEMPLATE_ID, FUND_CHANNEL_API, PAY_MODE, STATUS, IS_LOCKED, OPERATOR, GMT_ARCHIVE,
        GMT_CREATE, GMT_MODIFIED, MEMO, CHECK_FLAG, BATCH_ORDER_NO, QUERY_TIMES, GMT_NEXT_RETRY, EXTENSION
    </sql>


    <!-- ============================================= -->
    <!-- mapped statements for IbatisInstBatchOrderDAO -->
    <!-- ============================================= -->
    <!-- mapped statement for IbatisInstBatchOrderDAO.insert -->
    <insert id="MS-INST-BATCH-ORDER-INSERT">
        insert into TT_INST_BATCH_ORDER(
        <include refid="INST-BATCH-ORDER-COLUMNS"/>
        <![CDATA[
        ) values (#{archiveBatchId}, #{orderType}, #{totalAmount.amount}, #{totalCount}, #{totalAmount.currency},
         #{fundChannelCode}, #{archiveTemplateId}, #{fundChannelApi}, #{payMode}, #{status}, #{isLocked}, #{operator},
          #{gmtArchive}, now(), now(), #{memo}, #{checkFlag}, #{batchOrderNo}, #{queryTimes}, #{gmtNextRetry}, #{extension})
    ]]>
    </insert>

    <!-- mapped statement for IbatisInstBatchOrderDAO.loadById -->
    <select id="MS-INST-BATCH-ORDER-LOAD-BY-ID" resultMap="RM-INST-BATCH-ORDER">
        select
        <include refid="INST-BATCH-ORDER-COLUMNS"/>
        <![CDATA[
        from TT_INST_BATCH_ORDER where (ARCHIVE_BATCH_ID = #{value})
    ]]>
    </select>

    <select id="MS-INST-BATCH-ORDER-LOAD-4-QUERY" resultType="java.lang.Long">
        <![CDATA[
            SELECT ARCHIVE_BATCH_ID FROM
    (SELECT ARCHIVE_BATCH_ID FROM CMF.TT_INST_BATCH_ORDER
        WHERE
            STATUS IN('S','F')
            AND ORDER_TYPE = #{bizType}
            AND GMT_ARCHIVE > date_sub(now(),INTERVAL 3 DAY)
            AND IS_LOCKED = #{lockStatus}
            AND GMT_NEXT_RETRY <= now()
            ORDER BY ARCHIVE_BATCH_ID ASC
    ) T1 WHERE 1=1 limit #{rownum}
        ]]>
    </select>

    <!-- mapped statement for IbatisInstBatchOrderDAO.lockedById -->
    <select id="MS-INST-BATCH-ORDER-LOCKED-BY-ID" resultMap="RM-INST-BATCH-ORDER">
        select
        <include refid="INST-BATCH-ORDER-COLUMNS"/>
        from TT_INST_BATCH_ORDER where ARCHIVE_BATCH_ID = #{value} for update
    </select>

    <!-- mapped statement for IbatisInstBatchOrderDAO.updateStatusById -->
    <update id="MS-INST-BATCH-ORDER-UPDATE-STATUS-BY-ID">
        <![CDATA[
        update TT_INST_BATCH_ORDER set gmt_modified=now(), status=#{status} where (ARCHIVE_BATCH_ID = #{archiveBatchId})
    ]]>
    </update>

    <update id="MS-INST-BATCH-ORDER-UPDATE-STATUS-BY-ID-AND-PRE-STATUS">
        <![CDATA[
        update TT_INST_BATCH_ORDER set gmt_modified=now(), status=#{status} where (ARCHIVE_BATCH_ID = #{archiveBatchId}
         and status=#{preStatus})
    ]]>
    </update>

    <update id="MS-INST-BATCH-ORDER-UPDATE-STATUS-TIME-BY-ID-AND-PRE">
        <![CDATA[
        update TT_INST_BATCH_ORDER set gmt_modified=now(), status=#{status}, gmt_archive=#{gmtArchive} where (ARCHIVE_BATCH_ID = #{archiveBatchId}
         and status=#{preStatus})
    ]]>
    </update>

    <!-- mapped statement for IbatisInstBatchOrderDAO.updateAmountAndCount -->
    <update id="MS-INST-BATCH-ORDER-UPDATE-AMOUNT-AND-COUNT">
        <![CDATA[
        update TT_INST_BATCH_ORDER set gmt_modified=now(), TOTAL_AMOUNT=#{totalAmount.amount}, TOTAL_COUNT=#{totalCount}
         where (ARCHIVE_BATCH_ID = #{archiveBatchId})
    ]]>
    </update>

    <update id="MS-INST-BATCH-ORDER-UPDATE-IS-LOCKED">
        update TT_INST_BATCH_ORDER set gmt_modified=now(), IS_LOCKED=#{code} where IS_LOCKED=#{oriCode}
        and ARCHIVE_BATCH_ID in
        <foreach collection="archiveBatchIdList" item="archiveBatchId" open="(" close=")" separator="," >
            #{archiveBatchId}
        </foreach>
    </update>

    <!-- mapped statement for IbatisInstBatchOrderDAO.passStatus -->
    <update id="MS-INST-BATCH-ORDER-PASS-STATUS">
        update TT_INST_BATCH_ORDER
        set gmt_modified = now()
        <if test="checkFlag != null">
            ,CHECK_FLAG = #{checkFlag}
        </if>
        where
        ARCHIVE_BATCH_ID = #{archiveBatchId}
    </update>

    <update id="MS-INST-BATCH-ORDER-UPDATE-RETRY-INFO-BY-ID">
        update TT_INST_BATCH_ORDER
        set gmt_modified = now(),
        query_times = #{retryTimes},
        gmt_next_retry = #{gmtNextRetry}
        where
        ARCHIVE_BATCH_ID = #{archiveBatchId}
    </update>


    <select id="MS-INST-BATCH-ORDER-QUERY-FOR-BDC-FUNDOUT" resultType="long">
        select
        <include refid="INST-BATCH-ORDER-COLUMNS"/>
        from TT_INST_BATCH_ORDER br
        where GMT_ARCHIVE &gt;= date_sub(now(),INTERVAL 1 DAY)
        and GMT_ARCHIVE &lt;= date_add(now(),INTERVAL 5 second )
        <if test="status != null">
            and STATUS = #{status}
        </if>
        <if test="isLocked != null">
            and IS_LOCKED = #{isLocked}
        </if>
        <if test="apiCode != null">
            and FUND_CHANNEL_API = #{apiCode}
        </if>
        <if test="operator != null">
            and OPERATOR = #{operator}
        </if>
        order by ARCHIVE_BATCH_ID desc limit #{startRow},#{pageSize}
    </select>


    <select id="MS-INST-BATCH-ORDER-QUERY-FOR-BDC-FUNDOUT-FOR-PAGING" resultType="int">
        select count(1) from TT_INST_BATCH_ORDER br
        where GMT_ARCHIVE &gt;= date_sub(now(),INTERVAL 1 DAY)
        and GMT_ARCHIVE &lt;= date_add(now(),INTERVAL 5 second )
        <if test="status != null">
            and STATUS = #{status}
        </if>
        <if test="isLocked != null">
            and IS_LOCKED = #{isLocked}
        </if>
        <if test="apiCode != null">
            and FUND_CHANNEL_API = #{apiCode}
        </if>
        <if test="operator != null">
            and OPERATOR = #{operator}
        </if>
    </select>

    <select id="MS-INST-BATCH-ORDER-QUERY-FOR-PAGING" resultType="int">
        select count(1) from TT_INST_BATCH_ORDER br
        where 1=1
        <if test="gmtStart != null">
        and GMT_ARCHIVE &gt;= #{gmtStart}
        </if>
        <if test="gmtEnd != null">
        and GMT_ARCHIVE &lt;= #{gmtEnd}
        </if>
        <if test="status != null">
            and STATUS = #{status}
        </if>
        <if test="channelCode != null">
            and FUND_CHANNEL_CODE = #{channelCode}
        </if>
    </select>

    <select id="MS-INST-BATCH-ORDER-QUERY" resultMap="RM-INST-BATCH-ORDER">
        select
        <include refid="INST-BATCH-ORDER-COLUMNS"/>
        from TT_INST_BATCH_ORDER
        where 1=1
        <if test="gmtStart != null">
            and GMT_ARCHIVE &gt;= #{gmtStart}
        </if>
        <if test="gmtEnd != null">
            and GMT_ARCHIVE &lt;= #{gmtEnd}
        </if>
        <if test="status != null">
            and STATUS = #{status}
        </if>
        <if test="channelCode != null">
            and FUND_CHANNEL_CODE = #{channelCode}
        </if>
        order by GMT_ARCHIVE desc limit #{startRow},#{pageSize}
    </select>

    <update id="MS-INST-BATCH-ORDER-UPDATE-EXTENSION-BY-ID">
        <![CDATA[
        update TT_INST_BATCH_ORDER set gmt_modified=now(), extension=#{extension} where (ARCHIVE_BATCH_ID = #{archiveBatchId})
    ]]>
    </update>

</mapper>
