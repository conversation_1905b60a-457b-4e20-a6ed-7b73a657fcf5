<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR>                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_FUNDOUT_ORDER -->
    <resultMap id="RM-FUNDOUT-ORDER" type="com.uaepay.cmf.common.core.dal.dataobject.FundoutOrderDO">
        <result property="instOrderId" column="INST_ORDER_ID" javaType="long" jdbcType="NUMERIC"/>
        <result property="bankCode" column="BANK_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="bankName" column="BANK_NAME" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="bankBranch" column="BANK_BRANCH" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="bankBranchCode" column="BANK_BRANCH_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="bankProvince" column="BANK_PROVINCE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="bankCity" column="BANK_CITY" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="areaCode" column="AREA_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="accountType" column="ACCOUNT_TYPE" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="accountName" column="ACCOUNT_NAME" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="cardNo" column="CARD_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="accountNo" column="ACCOUNT_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="ibanNo" column="IBAN_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="swiftCode" column="SWIFT_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="beneficiaryAddress" column="BENEFICIARY_ADDRESS" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="intermediaryBank" column="INTERMEDIARY_BANK" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="cardType" column="CARD_TYPE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="agreementNo" column="AGREEMENT_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="purpose" column="PURPOSE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="GMT_CREATE" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="ptId" column="PT_ID" javaType="java.lang.String" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="FUNDOUT-ORDER-COLUMNS">
    INST_ORDER_ID, BANK_CODE, BANK_NAME, BANK_BRANCH, BANK_BRANCH_CODE, BANK_PROVINCE, BANK_CITY, AREA_CODE, ACCOUNT_TYPE,
    ACCOUNT_NAME, CARD_NO, ACCOUNT_NO, IBAN_NO, SWIFT_CODE, BENEFICIARY_ADDRESS, INTERMEDIARY_BANK, CARD_TYPE, AGREEMENT_NO, PURPOSE, PT_ID, GMT_MODIFIED, GMT_CREATE
    </sql>

    <!-- ============================================= -->
    <!-- mapped statements for IbatisFundoutOrderDAO -->
    <!-- ============================================= -->
    <!-- mapped statement for IbatisFundoutOrderDAO.insert -->
    <insert id="MS-FUNDOUT-ORDER-INSERT">
        insert into TT_FUNDOUT_ORDER(
        <include refid="FUNDOUT-ORDER-COLUMNS"/>
        <![CDATA[
        ) values (#{instOrderId}, #{bankCode}, #{bankName}, #{bankBranch}, #{bankBranchCode}, #{bankProvince}, #{bankCity}, #{areaCode}, #{accountType}, #{accountName}, #{cardNo}, #{accountNo}, #{ibanNo}, #{swiftCode}, #{beneficiaryAddress}, #{intermediaryBank}, #{cardType}, #{agreementNo}, #{purpose}, #{ptId}, now(), now())
    ]]>
    </insert>

    <!-- mapped statement for IbatisFundoutOrderDAO.delete -->
    <delete id="MS-FUNDOUT-ORDER-DELETE">
        <![CDATA[
          delete from TT_FUNDOUT_ORDER where (INST_ORDER_ID = #{value})
        ]]>
    </delete>

    <!-- mapped statement for IbatisFundoutOrderDAO.loadById -->
    <select id="MS-FUNDOUT-ORDER-LOAD-BY-ID" resultMap="RM-FUNDOUT-ORDER">
        select
        <include refid="FUNDOUT-ORDER-COLUMNS"/>
        <![CDATA[
         from TT_FUNDOUT_ORDER where (INST_ORDER_ID = #{value})
    ]]>
    </select>

    <!-- mapped statement for IbatisFundoutOrderDAO.lockedById -->
    <select id="MS-FUNDOUT-ORDER-LOCKED-BY-ID" resultMap="RM-FUNDOUT-ORDER">
        select
        <include refid="FUNDOUT-ORDER-COLUMNS"/>
        <![CDATA[
         from TT_FUNDOUT_ORDER where (INST_ORDER_ID = #{value}) for update
    ]]>
    </select>

</mapper>