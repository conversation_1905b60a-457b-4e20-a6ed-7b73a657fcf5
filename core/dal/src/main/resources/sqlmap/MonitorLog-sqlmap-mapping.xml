<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR>                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TL_MONITOR_LOG -->
    <resultMap id="RM-MONITOR-LOG" type="com.uaepay.cmf.common.core.dal.dataobject.MonitorLogDO">
        <result property="logId" column="LOG_ID" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="ipAddress" column="IP_ADDRESS" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="orderNo" column="ORDER_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="eventMessage" column="EVENT_MESSAGE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="actionId" column="ACTION_ID" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="alertLevel" column="ALERT_LEVEL" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="logStatus" column="LOG_STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="exceptionLog" column="EXCEPTION_LOG" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="memo" column="MEMO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="GMT_CREATE" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
    </resultMap>


    <!-- ============================================= -->
    <!-- mapped statements for IbatisMonitorLogDAO -->
    <!-- ============================================= -->
    <!-- mapped statement for IbatisMonitorLogDAO.insert -->
    <insert id="MS-MONITOR-LOG-INSERT">

    <![CDATA[
        insert into TL_MONITOR_LOG(LOG_ID,IP_ADDRESS,ORDER_NO,EVENT_MESSAGE,ACTION_ID,ALERT_LEVEL,LOG_STATUS,EXCEPTION_LOG,MEMO,GMT_CREATE,GMT_MODIFIED) values (#{logId}, #{ipAddress}, #{orderNo}, #{eventMessage}, #{actionId}, #{alertLevel}, #{logStatus}, #{exceptionLog}, #{memo}, now(), now())
    ]]>
    </insert>

    <!-- mapped statement for IbatisMonitorLogDAO.updateStatusWithPreStatus -->
    <update id="MS-MONITOR-LOG-UPDATE-STATUS-WITH-PRE-STATUS">
        <![CDATA[
		    update TL_MONITOR_LOG
				set GMT_MODIFIED = now(), LOG_STATUS = #{newStatus}, MEMO = #{memo}
				where LOG_STATUS = #{oldStatus} AND LOG_ID IN  
		     ]]>
        <foreach collection="logIdlist" item="logId" open="(" close=")" separator="," >
            #{logId}
        </foreach>

    </update>

    <!-- mapped statement for IbatisMonitorLogDAO.loadByLogId -->
    <select id="MS-MONITOR-LOG-LOAD-BY-LOG-ID" resultMap="RM-MONITOR-LOG">
    <![CDATA[
        select LOG_ID, IP_ADDRESS, ORDER_NO, EVENT_MESSAGE, ACTION_ID, ALERT_LEVEL, LOG_STATUS, EXCEPTION_LOG, MEMO, GMT_CREATE, GMT_MODIFIED from TL_MONITOR_LOG where (LOG_ID = #{value})
    ]]>
    </select>

    <!-- mapped statement for IbatisMonitorLogDAO.loadByStatus -->
    <select id="MS-MONITOR-LOG-LOAD-BY-STATUS" resultMap="RM-MONITOR-LOG">
        <![CDATA[
				select LOG_ID,
				   IP_ADDRESS,
				   ORDER_NO,
				   EVENT_MESSAGE,
				   ACTION_ID,
				   ALERT_LEVEL,
				   LOG_STATUS,
				   EXCEPTION_LOG,
				   MEMO,
				   GMT_CREATE,
				   GMT_MODIFIED
				from TL_MONITOR_LOG where GMT_CREATE >= date_sub(now(),INTERVAL 3 HOUR ) AND LOG_STATUS = #{logStatus}    limit #{rownum}
			    ]]>
    </select>

</mapper>