<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR>                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_CONTROL_ORDER -->
    <resultMap id="RM-CONTROL-ORDER" type="com.uaepay.cmf.common.core.dal.dataobject.ControlOrderDO">
        <result property="orderId" column="ORDER_ID" javaType="long" jdbcType="NUMERIC" />
        <result property="fundChannelCode" column="FUND_CHANNEL_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="payMode" column="PAY_MODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="instCode" column="INST_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="productCode" column="PRODUCT_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="requestNo" column="REQUEST_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="requestType" column="REQUEST_TYPE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="apiType" column="API_TYPE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="instOrderNo" column="INST_ORDER_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="preRequestNo" column="PRE_REQUEST_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="amount" column="AMOUNT"/>
        <result property="status" column="STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="retryStatus" column="RETRY_STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="gmtCreate" column="GMT_CREATE" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="extension" column="EXTENSION" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="memo" column="MEMO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="notifyStatus" column="NOTIFY_STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="sourceCode" column="SOURCE_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="communicateStatus" column="COMMUNICATE_STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="flag" column="FLAG" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="merchantId" column="MERCHANT_ID" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="retryTimes" column="RETRY_TIMES" javaType="int" jdbcType="NUMERIC"/>
        <result property="gmtNextRetry" column="GMT_NEXT_RETRY" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="INST-CONTROL-ORDER-COLUMNS">
        ORDER_ID, FUND_CHANNEL_CODE, PRODUCT_CODE, INST_CODE, PRE_REQUEST_NO, PAY_MODE, REQUEST_NO, REQUEST_TYPE,
        API_TYPE, INST_ORDER_NO, AMOUNT, CURRENCY, STATUS, RETRY_STATUS, GMT_CREATE, GMT_MODIFIED, EXTENSION, NOTIFY_STATUS,
        SOURCE_CODE, COMMUNICATE_STATUS, FLAG, MEMO, MERCHANT_ID, RETRY_TIMES, GMT_NEXT_RETRY
    </sql>

    <!-- mapped statement for IbatisControlOrderDAO.loadWithRequestNo -->
    <select id="MS-CONTROL-ORDER-LOAD-WITH-REQUEST-NO" resultMap="RM-CONTROL-ORDER">
        select
        <include refid="INST-CONTROL-ORDER-COLUMNS"/>
        <![CDATA[
         from TT_CONTROL_ORDER where (REQUEST_NO = #{value})
    ]]>
    </select>



    <select id="MS-CONTROL-ORDER-LOAD-WITH-REQUEST-NO-API-TYPE" resultMap="RM-CONTROL-ORDER">
        select
        <include refid="INST-CONTROL-ORDER-COLUMNS"/>
        <![CDATA[
         from TT_CONTROL_ORDER where (REQUEST_NO = #{requestNo}  and API_TYPE =#{apiType})
         order by GMT_CREATE desc limit 1
    ]]>
    </select>



    <!-- mapped statement for IbatisControlOrderDAO.loadWithInstOrderNo -->
    <select id="MS-CONTROL-ORDER-LOAD-WITH-INST-ORDER-NO" resultMap="RM-CONTROL-ORDER">
        select
        <include refid="INST-CONTROL-ORDER-COLUMNS"/>
        <![CDATA[
         from TT_CONTROL_ORDER where (INST_ORDER_NO = #{value})
    ]]>
    </select>

    <!-- mapped statement for IbatisControlOrderDAO.loadWithOrderId -->
    <select id="MS-CONTROL-ORDER-LOAD-WITH-ORDER-ID" resultMap="RM-CONTROL-ORDER">
        select
        <include refid="INST-CONTROL-ORDER-COLUMNS"/>
        <![CDATA[
         from TT_CONTROL_ORDER where (ORDER_ID = #{value})
    ]]>
    </select>

    <!-- mapped statement for IbatisControlOrderDAO.insert -->
    <insert id="MS-CONTROL-ORDER-INSERT">
        insert into TT_CONTROL_ORDER(
        <include refid="INST-CONTROL-ORDER-COLUMNS"/>
        <![CDATA[
        ) values (#{orderId}, #{fundChannelCode}, #{productCode}, #{instCode}, #{preRequestNo}, #{payMode}, #{requestNo}, #{requestType}, #{apiType}, #{instOrderNo}, #{amount.amount}, #{amount.currency}, #{status}, #{retryStatus}, now(), now(), #{extension}, #{notifyStatus}, #{sourceCode}, #{communicateStatus}, #{flag}, #{memo}, #{merchantId}, #{retryTimes}, #{gmtNextRetry})
    ]]>
    </insert>

    <!-- mapped statement for IbatisControlOrderDAO.updateStatusById -->
    <update id="MS-CONTROL-ORDER-UPDATE-STATUS-BY-ID">
        <![CDATA[
        update TT_CONTROL_ORDER set gmt_modified=now(), STATUS=#{targetStatus} where ORDER_ID = #{orderId} and STATUS=#{preStatus}
    ]]>
    </update>

    <update id="MS-CONTROL-ORDER-UPDATE-COMMUNICATE-STATUS-BY-ID-PRE-STATUS">
        <![CDATA[
        update TT_CONTROL_ORDER set gmt_modified=now(), communicate_status=#{communicateStatus} where ORDER_ID = #{orderId} and communicate_status=#{preStatus}
    ]]>
    </update>

    <!-- mapped statement for IbatisControlOrderDAO.loadOrderIdForDurationQueryResultPaging -->
    <select id="MS-CONTROL-ORDER-LOAD-4-QUERY" resultType="long">
        SELECT ORDER_ID FROM (<![CDATA[
				SELECT ORDER_ID
                FROM TT_CONTROL_ORDER
                WHERE STATUS = 'I'
                    AND FLAG = 'D'
                	AND GMT_CREATE >= #{startTime}
                	AND GMT_CREATE < #{endTime}
                	AND GMT_NEXT_RETRY < now()
                	AND API_TYPE = #{apiType}
                	AND COMMUNICATE_STATUS in('F', 'S')
              ]]>
        ORDER BY ORDER_ID ASC) T1 WHERE 1=1  limit #{maxSize}
    </select>

    <update id="MS-CONTROL-ORDER-UPDATE-EXTENSION-BY-REQUEST-NO">
        update TT_CONTROL_ORDER
        set gmt_modified = now(),
        extension = #{extension}
        where
        request_no = #{requestNo}
    </update>

    <update id="MS-CONTROL-ORDER-UPDATE-RETRY-TIME-BY-ID">
        update TT_CONTROL_ORDER
        set gmt_modified = now(),
        RETRY_TIMES = #{retryTimes},
        GMT_NEXT_RETRY = #{gmtNextRetry}
        where
        order_id = #{orderId}
    </update>

    <update id="MS-CONTROL-ORDER-UPDATE-FLAG-WITH-ID-AND-PRE-FLAG">
        UPDATE TT_CONTROL_ORDER
        SET FLAG=#{flag},GMT_MODIFIED=now()
        WHERE
        ORDER_ID = #{orderId} AND FLAG=#{preFlag}
    </update>

</mapper>