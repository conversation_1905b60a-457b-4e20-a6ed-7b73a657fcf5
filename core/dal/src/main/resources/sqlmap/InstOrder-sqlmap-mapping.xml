<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ========================================================== -->
<!-- Configuration for ibatis sqlmap mapping. -->
<!-- ========================================================== -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access
	Layer) -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project. -->
<!-- -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may
	be -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding -->
<!-- configuration files. Modify those files according to your needs, then
	run -->
<!-- <tt>iwallet-dalgen</tt> to generate this file. -->
<!-- -->
<!-- <AUTHOR> -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_INST_ORDER-->
    <resultMap id="RM-INST-ORDER"
               type="com.uaepay.cmf.common.core.dal.dataobject.InstOrderDO">
        <result property="instOrderId" column="INST_ORDER_ID" javaType="java.lang.Long"
                jdbcType="NUMERIC"/>
        <result property="instCode" column="INST_CODE" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="instOrderNo" column="INST_ORDER_NO" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="orderType" column="ORDER_TYPE" javaType="java.lang.String"
                jdbcType="CHAR"/>
        <result property="amount" column="AMOUNT"/>
        <result property="status" column="STATUS" javaType="java.lang.String"
                jdbcType="CHAR"/>
        <result property="communicateType" column="COMMUNICATE_TYPE"
                javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="communicateStatus" column="COMMUNICATE_STATUS"
                javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="archiveBatchId" column="ARCHIVE_BATCH_ID"
                javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="gmtSubmit" column="GMT_SUBMIT" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
        <result property="gmtCreate" column="GMT_CREATE" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date"
                jdbcType="TIMESTAMP"/>
        <result property="memo" column="MEMO" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="productCode" column="PRODUCT_CODE" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="paymentCode" column="PAYMENT_CODE" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="payMode" column="PAY_MODE" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="fundChannelCode" column="FUND_CHANNEL_CODE"
                javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="fundChannelApi" column="FUND_CHANNEL_API"
                javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="gmtBookingSubmit" column="GMT_BOOKING_SUBMIT"
                javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="retryTimes" column="RETRY_TIMES" javaType="java.lang.Integer"
                jdbcType="NUMERIC"/>
        <result property="gmtNextRetry" column="GMT_NEXT_RETRY"
                javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="flag" column="FLAG" javaType="java.lang.String"
                jdbcType="CHAR"/>
        <result property="riskStatus" column="RISK_STATUS" javaType="java.lang.String"
                jdbcType="CHAR"/>
        <result property="routeVersion" column="ROUTE_VERSION"
                javaType="int" jdbcType="NUMERIC" />
        <result property="cmfSeqNo" column="CMF_SEQ_NO" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="isSplit" column="IS_SPLIT" javaType="java.lang.String"
                jdbcType="CHAR"/>
        <result property="isAdvance" column="IS_ADVANCE" javaType="java.lang.String"
                jdbcType="CHAR"/>
        <result property="sendType" column="SEND_TYPE" javaType="java.lang.String"
                jdbcType="CHAR"/>
        <result property="merchantId" column="MERCHANT_ID" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="extension" column="EXTENSION" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="RM-UNIQUE-ORDER"
               type="com.uaepay.cmf.common.core.dal.dataobject.UniqueOrderDO">
        <result property="instOrderId" column="INST_ORDER_ID" javaType="java.lang.Long"
                jdbcType="NUMERIC"/>
        <result property="instOrderNo" column="INST_ORDER_NO" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="INST-ORDER-COLUMNS">
        INST_ORDER_ID, INST_CODE, INST_ORDER_NO, ORDER_TYPE, PAY_MODE, CURRENCY, GMT_BOOKING_SUBMIT,
        PRODUCT_CODE, PAYMENT_CODE, AMOUNT, STATUS, COMMUNICATE_TYPE, FUND_CHANNEL_CODE,
        FUND_CHANNEL_API, COMMUNICATE_STATUS, ARCHIVE_BATCH_ID, FLAG, RISK_STATUS, GMT_SUBMIT, GMT_CREATE, GMT_MODIFIED,
        MEMO, GMT_NEXT_RETRY, RETRY_TIMES, ROUTE_VERSION, IS_SPLIT, IS_ADVANCE, CMF_SEQ_NO, EXTENSION,
        SEND_TYPE, MERCHANT_ID
    </sql>

    <insert id="MS-INST-ORDER-INSERT">
        <![CDATA[
        insert into TT_INST_ORDER${tabSuffix}(]]>
        <include refid="INST-ORDER-COLUMNS"/>
        <![CDATA[) values (#{instOrderId}, #{instCode}, #{instOrderNo}, #{orderType}, #{payMode}, #{amount.currency}, #{gmtBookingSubmit}, #{productCode}, #{paymentCode}, #{amount.amount}, #{status}, #{communicateType}, #{fundChannelCode}, #{fundChannelApi}, #{communicateStatus}, #{archiveBatchId}, #{flag}, #{riskStatus}, #{gmtSubmit}, now(), now(), #{memo}, #{gmtNextRetry}, #{retryTimes}, #{routeVersion}, #{isSplit},#{isAdvance}, #{cmfSeqNo}, #{extension}, #{sendType}, #{merchantId})
    ]]>
    </insert>

    <insert id="MS-UNIQUE-ORDER-INSERT">
        <![CDATA[
        insert into TT_UNIQUE_ORDER(INST_ORDER_ID,INST_ORDER_NO) values (#{instOrderId}, #{instOrderNo})
    ]]>
    </insert>

    <delete id="MS-INST-ORDER-DELETE">
        <![CDATA[
        delete from TT_INST_ORDER${tabSuffix} where (INST_ORDER_ID = #{value})
        ]]>
    </delete>

    <!-- mapped statement for IbatisInstOrderDAO.loadById -->
    <select id="MS-INST-ORDER-LOAD-BY-ID" resultMap="RM-INST-ORDER">
        select
        <include refid="INST-ORDER-COLUMNS"/>
        <![CDATA[
         from TT_INST_ORDER${tabSuffix} where (INST_ORDER_ID = #{value})
    ]]>
    </select>

    <select id="MS-UNIQUE-ORDER-LOAD-BY-NO" resultMap="RM-UNIQUE-ORDER">
        <![CDATA[
        select INST_ORDER_ID, INST_ORDER_NO from TT_UNIQUE_ORDER where (INST_ORDER_NO = #{value})
    ]]>
    </select>

    <select id="MS-INST-ORDER-LOCKED-BY-ID" resultMap="RM-INST-ORDER">
        select
        <include refid="INST-ORDER-COLUMNS"/>
        <![CDATA[
         from TT_INST_ORDER${tabSuffix} where (INST_ORDER_ID = #{value}) for update
        ]]>
    </select>

    <update id="MS-INST-ORDER-UPDATE-STATUS-BY-ID">
        <![CDATA[
        update TT_INST_ORDER${tabSuffix} set gmt_modified=now(), STATUS=#{targetStatus} where (INST_ORDER_ID = #{instOrderId}) and STATUS = #{preStatus}
    ]]>
    </update>

    <select id="MS-INST-ORDER-GET-ARCHIVE-BATCH-CNT" resultType="int">
        select COUNT(1) from TT_INST_ORDER${tabSuffix} where (ARCHIVE_BATCH_ID = #{value})
    </select>

    <select id="MS-LOAD-ORDER-IDS-BY-ARCHIVE-ID" resultType="java.lang.Long">
        select INST_ORDER_ID from TT_INST_ORDER${tabSuffix} where (ARCHIVE_BATCH_ID = #{value})
    </select>

    <select id="MS-LOAD-ORDER-IDS-BY-BATCH-ID-AND-CURRENCY" resultType="java.lang.Long">
        select INST_ORDER_ID from TT_INST_ORDER${tabSuffix} where (ARCHIVE_BATCH_ID = #{archiveBatchId} and CURRENCY=#{currency})
    </select>

    <select id="MS-LOAD-CURRENCY-LIST-BY-BATCH-ID" resultType="java.lang.String">
        select CURRENCY from TT_INST_ORDER${tabSuffix} where (ARCHIVE_BATCH_ID = #{value})
        group by CURRENCY
    </select>

    <select id="MS-LOAD-FO-FINISHED-DATE-LIST" resultType="java.lang.String">
        select DATE_FORMAT(gmt_booking_submit,'%Y%m%d') from TT_INST_ORDER${tabSuffix} where (FUND_CHANNEL_CODE = #{value}) and gmt_booking_submit>=DATE_SUB(curdate(),INTERVAL 5 DAY)
        group by DATE_FORMAT(gmt_booking_submit,'%Y%m%d')
    </select>

    <select id="MS-INST-ORDER-GET-ARCHIVE-BATCH-AMT" resultType="double">
        select sum(AMOUNT) TOTAL_AMOUNT from TT_INST_ORDER${tabSuffix} where (ARCHIVE_BATCH_ID = #{value})
    </select>

    <select id="MS-INST-ORDER-LOAD-BY-CMF-SEQ" resultMap="RM-INST-ORDER">
        select
        <include refid="INST-ORDER-COLUMNS"/>
        <![CDATA[
         from TT_INST_ORDER${tabSuffix} where (CMF_SEQ_NO = #{value})
    ]]>
    </select>

    <select id="MS-INST-ORDER-LOCKED-BY-CMF-SEQ" resultMap="RM-INST-ORDER">
        select
        <include refid="INST-ORDER-COLUMNS"/>
        <![CDATA[
         from TT_INST_ORDER${tabSuffix} where (CMF_SEQ_NO = #{value}) for update
    ]]>
    </select>

    <update id="MS-INST-ORDER-UPDATE-COMMUNICATE-STATUS-BY-ID">
        <![CDATA[
        update TT_INST_ORDER${tabSuffix} set gmt_modified=now(), COMMUNICATE_STATUS=#{communicateStatus} where (INST_ORDER_ID = #{instOrderId})
    ]]>
    </update>

    <update id="MS-INST-ORDER-UPDATE-COMMUNICATE-STATUS-BATCH-BY-IDS">
        update TT_INST_ORDER${tabSuffix}
        set COMMUNICATE_STATUS =
        #{nowStatus}, gmt_modified = now()
        where COMMUNICATE_STATUS = #{preStatus}
        and INST_ORDER_ID in
        <foreach collection="instOrderIds" item="instOrderId" open="(" close=")" separator="," >
            #{instOrderId}
        </foreach>

    </update>

    <update id="MS-INST-ORDER-UPDATE-COMMUNICATE-STATUS-WITH-PRE-STATUS">
        <![CDATA[
        update TT_INST_ORDER${tabSuffix} set gmt_modified=now(), COMMUNICATE_STATUS=#{communicateStatus}
        ]]>

        <![CDATA[
        where ((INST_ORDER_ID = #{instOrderId}) AND (COMMUNICATE_STATUS = #{communicateStatus2}))
        ]]>
    </update>

    <update id="MS-INST-ORDER-UPDATE-RETRY-DATA-WITH-PRE-STATUS">
        <![CDATA[
        update TT_INST_ORDER${tabSuffix} set gmt_modified=now(), COMMUNICATE_STATUS=#{communicateStatus},
                                             inst_order_no = #{instOrderNo}, extension = #{extension},
                                             GMT_BOOKING_SUBMIT= now()
        ]]>

        <![CDATA[
        where ((INST_ORDER_ID = #{instOrderId}) AND (COMMUNICATE_STATUS = #{communicateStatus2}))
        ]]>
    </update>

    <update id="MS-INST-ORDER-UPDATE-COMM-STATUS-AND-BOOKING-BY-ARCHIVE-ID">
        <![CDATA[
        update TT_INST_ORDER${tabSuffix} set gmt_modified=now(), COMMUNICATE_STATUS=#{commStatus}, GMT_BOOKING_SUBMIT=#{gmtBooking}
        ]]>
        <![CDATA[
        where ((ARCHIVE_BATCH_ID = #{archiveId}) AND (COMMUNICATE_STATUS = #{preCommStatus}))
        ]]>
    </update>

    <!-- mapped statement for IbatisInstOrderDAO.updateChannelInfoById -->
    <update id="MS-INST-ORDER-UPDATE-CHANNEL-INFO-BY-ID">
        <![CDATA[
        update TT_INST_ORDER${tabSuffix} set gmt_modified=now(), FUND_CHANNEL_CODE=#{fundChannelCode}, FUND_CHANNEL_API=#{fundChannelApi} where (INST_ORDER_ID = #{instOrderId})
    ]]>
    </update>

    <!-- mapped statement for IbatisInstOrderDAO.updateMemoById -->
    <update id="MS-INST-ORDER-UPDATE-MEMO-BY-ID">
        <![CDATA[
        update TT_INST_ORDER${tabSuffix} set gmt_modified=now(), memo=#{memo} where (INST_ORDER_ID = #{instOrderId})
    ]]>
    </update>

    <!-- mapped statement for IbatisInstOrderDAO.updateExtensionById -->
    <update id="MS-INST-ORDER-UPDATE-EXTENSION-BY-ID">
        <![CDATA[
        update TT_INST_ORDER${tabSuffix} set gmt_modified=now(), extension=#{extension} where (INST_ORDER_ID = #{instOrderId})
    ]]>
    </update>

    <!-- mapped statement for IbatisInstOrderDAO.updateRetryInfoById -->
    <update id="MS-INST-ORDER-UPDATE-RETRY-INFO-BY-ID">
        <![CDATA[
				update TT_INST_ORDER${tabSuffix}
				set gmt_modified = now(), GMT_NEXT_RETRY = #{gmtNextRetry},RETRY_TIMES=#{retryTimes}
				where INST_ORDER_ID = #{instOrderId}
			]]>
    </update>

    <!-- mapped statement for IbatisInstOrderDAO.sumAmountForQueryResult -->
    <select id="MS-INST-ORDER-SUM-AMOUNT-FOR-QUERY-RESULT"
            resultType="double">
        <![CDATA[
				select  sum(amount) TOTAL_AMOUNT
                from TT_INST_ORDER${tabSuffix}
                where  STATUS = 'I'
                	and GMT_BOOKING_SUBMIT >= #{startDate}
                	and GMT_BOOKING_SUBMIT < #{endDate}
                	and ORDER_TYPE = #{orderType}
              ]]>
        <if test="communicateStatusList != null and communicateStatusList.size()>0 ">
            and COMMUNICATE_STATUS in
            <foreach collection="communicateStatusList" item="communicateStatus" open="(" close=")" separator="," >
                #{communicateStatus}
            </foreach>
        </if>
        <if test="channelCodeList != null and channelCodeList.size()>0 ">
            and FUND_CHANNEL_CODE in
            <foreach collection="channelCodeList" item="channelCode" open="(" close=")" separator="," >
                #{channelCode}
            </foreach>
        </if>
    </select>

    <!-- mapped statement for IbatisInstOrderDAO.loadByArchiveBatchId -->
    <select id="MS-INST-ORDER-LOAD-BY-ARCHIVE-BATCH-ID" resultMap="RM-INST-ORDER">
        select
        <include refid="INST-ORDER-COLUMNS"/>
        <![CDATA[
        from TT_INST_ORDER${tabSuffix} where (ARCHIVE_BATCH_ID = #{value}) order by INST_ORDER_ID ASC
    ]]>
    </select>


    <select id="MS-INST-ORDER-LOAD-BY-BATCH-ID-STATUS" resultMap="RM-INST-ORDER">

        select
        <include refid="INST-ORDER-COLUMNS"/>
        from TT_INST_ORDER${tabSuffix}
        where ARCHIVE_BATCH_ID =
        #{archiveBatchId}
        <if test="archiveDate != null">
            and <![CDATA[
			GMT_BOOKING_SUBMIT > date_sub(#{archiveDate}, interval 3 day)
         ]]>
        </if>
        <if test="communicateStatus != null">
            and <![CDATA[
			COMMUNICATE_STATUS = #{communicateStatus}
        ]]>
        </if>
        <if test="instOrderStatus != null">
            and <![CDATA[
			STATUS = #{instOrderStatus}
        ]]>
        </if>
        order by INST_ORDER_ID ASC
    </select>

    <select id="MS-INST-ORDER-QUERY-INST-ORDER-4-ARCHIVE-PAGE-SIZE" resultType="int">
        select count(1) from
        TT_INST_ORDER${tabSuffix} where
        ARCHIVE_BATCH_ID = 0
        AND (COMMUNICATE_TYPE =
        #{communicateType})
        <if test="bookingTime != null">
            and GMT_BOOKING_SUBMIT &lt;=
            #{bookingTime}
        </if>
        AND FUND_CHANNEL_API = #{apiCode}
        AND STATUS = 'I'
        and order_type in ('O','T')
        and GMT_BOOKING_SUBMIT > date_sub(now(),INTERVAL #{hours} HOUR)
    </select>

    <select id="MS-INST-ORDER-QUERY-INST-ORDER-4-ARCHIVE-PAGEING" resultType="long">
        select INST_ORDER_ID from
        TT_INST_ORDER${tabSuffix} io where
        ARCHIVE_BATCH_ID = 0
        AND (COMMUNICATE_TYPE =
        #{communicateType})
        <if test="bookingTime != null">
            and GMT_BOOKING_SUBMIT &lt;=
            #{bookingTime}
        </if>
        AND FUND_CHANNEL_API = #{apiCode}
        AND STATUS = 'I'
        and order_type in ('O','T')
        and GMT_BOOKING_SUBMIT > date_sub(now(),INTERVAL #{hours} HOUR )
        limit #{rownum}
    </select>



    <select id="MS-INST-ORDER-QUERY-INST-ORDER-LIST-BY-ID" resultMap="RM-INST-ORDER">
        select <include refid="INST-ORDER-COLUMNS"/>
        from TT_INST_ORDER ${tabSuffix} io
        where inst_order_id in
        <foreach collection="instOrderIdList" item="instOrderId" open="(" close=")" separator="," >
            #{instOrderId}
        </foreach>
    </select>

    <update id="MS-INST-ORDER-UPDATE-TEMP-BY-INST-ORDER-ID">
        update TT_INST_ORDER${tabSuffix}
        set gmt_modified = now(),
        ARCHIVE_BATCH_ID =
        #{tempBatchId}
        where inst_order_id in
        <foreach collection="instOrderIdList" item="instOrderId" open="(" close=")" separator="," >
            #{instOrderId}
        </foreach>

        AND ARCHIVE_BATCH_ID = 0
        AND COMMUNICATE_TYPE = 'B'
        AND STATUS = 'I'
        AND order_type in ('O','T')
    </update>

    <update id="MS-INST-ORDER-UPDATE-BATCH-ID-2-DEFAULT">
        update TT_INST_ORDER${tabSuffix}
        set gmt_modified = now(),
        ARCHIVE_BATCH_ID = #{defId}
        where
        ARCHIVE_BATCH_ID = #{tempBatchId}
        and COMMUNICATE_STATUS = 'A'
        and STATUS = 'I'
    </update>

    <update id="MS-INST-ORDER-UPDATE-BATCH-ID-LIST-BY-TEMP-BATCH-ID">
        update TT_INST_ORDER${tabSuffix}
        set gmt_modified = now(),
        ARCHIVE_BATCH_ID =
        #{archiveBatchId}
        where
        ARCHIVE_BATCH_ID = #{tempBatchId}
        <if test="instOrderIdList != null and instOrderIdList.size()>0 ">
            and INST_ORDER_ID in
            <foreach collection="instOrderIdList" item="instOrderId" open="(" close=")" separator="," >
                #{instOrderId}
            </foreach>

        </if>
    </update>

    <!-- mapped statement for IbatisInstOrderDAO.countNotFailureByArchiveBatchId -->
    <select id="MS-INST-ORDER-COUNT-NOT-FAILURE-BY-ARCHIVE-BATCH-ID"
            resultType="long">
        <![CDATA[
        select count(1) COUNT_NUM from TT_INST_ORDER${tabSuffix} where ((ARCHIVE_BATCH_ID = #{value}) AND (STATUS != 'F'))
    ]]>
    </select>

    <select id="MS-INST-ORDER-PAGE-QUERY" resultMap="RM-INST-ORDER">
        select
        <include refid="INST-ORDER-COLUMNS"/>
        from TT_INST_ORDER${tabSuffix}
        <where>
            1=1
            <if test="query.status != null and query.status !='' ">
                and STATUS = #{query.status}
            </if>
            <if test="query.instCode != null and query.instCode !='' ">
                and INST_CODE = #{query.instCode}
            </if>
            <if test="query.fundChannelCode != null and query.fundChannelCode !='' ">
                and fund_channel_code = #{query.fundChannelCode}
            </if>
            <if test="query.gmtStart != null">
                <![CDATA[
							and GMT_CREATE >= #{query.gmtStart}
				]]>
            </if>
            <if test="query.gmtEnd != null">
                <![CDATA[
						 	and GMT_CREATE <= #{query.gmtEnd}
				]]>
            </if>

        </where>
        order by INST_ORDER_ID desc limit #{startRow},#{pageSize}
    </select>

    <!-- mapped statement for IbatisInstOrderDAO.query, needed by paging -->
    <select id="MS-INST-ORDER-PAGE-QUERY-COUNT-FOR-PAGING" resultType="int">
        select count(1) from TT_INST_ORDER${tabSuffix}
        <where>
            1=1
            <if test="query.status != null and query.status !='' ">
                and STATUS = #{query.status}
            </if>
            <if test="query.instCode != null and query.instCode !='' ">
                and INST_CODE = #{query.instCode}
            </if>
            <if test="query.fundChannelCode != null and query.fundChannelCode !='' ">
                and fund_channel_code =
                #{query.fundChannelCode}
            </if>
            <if test="query.gmtStart != null  ">
                <![CDATA[
							and GMT_CREATE >= #{query.gmtStart}
						]]>
            </if>
            <if test="query.gmtEnd != null  ">
                <![CDATA[
						 	and GMT_CREATE <= #{query.gmtEnd}
						]]>
            </if>
        </where>
    </select>

    <update id="MS-INST-ORDER-UPDATE-FLAG-WITH-ORDER-ID-AND-PRE-FLAG">
        update TT_INST_ORDER${tabSuffix}
        set gmt_modified = now(),
        FLAG = #{flag}
        where
        inst_order_id = #{instOrderId}
        and FLAG = #{preFlag}
    </update>

    <update id="MS-INST-UPDATE-FO-ORDER-RETRY-ORDER-NO">
        update TT_INST_ORDER${tabSuffix}
        set gmt_modified = now(),
        inst_order_no = #{newInstOrderNo},
        memo = #{memo}
        where
        inst_order_id = #{instOrderId}
        and inst_order_no = #{oldInstOrderNo}
        and status = #{status}
        and communicate_status = #{communicateStatus}
        and inst_code= #{instCode}
    </update>

    <update id="MS-INST-ORDER-UPDATE-IS-ADVANCE-WITH-INST_ORDER_ID">
        update TT_INST_ORDER${tabSuffix}
        set is_advance = #{advanceStatus},
        gmt_modified = now()
        where
        is_advance = #{preStatus} and
        INST_ORDER_ID = #{instOrderId}
    </update>

    <update id="MS-INST-UPDATE-BOOKING-SUBMIT-BY-ID">
        update TT_INST_ORDER${tabSuffix}
        set gmt_booking_submit = #{bookingSubmit},
        gmt_modified = now()
        where
        INST_ORDER_ID = #{instOrderId}
    </update>

    <update id="MS-INST-UPDATE-AMOUNT-BY-ID">
        update TT_INST_ORDER${tabSuffix}
        set AMOUNT = #{amount},
        <if test="extension != null and extension !='' ">
            EXTENSION = #{extension},
        </if>
        gmt_modified = now()
        where
        INST_ORDER_ID = #{instOrderId}
    </update>

    <select id="MS-INST-ORDER-QUERY-REFUND-BY-FUNDIN-ORDER" resultMap="RM-INST-ORDER">
        select
        <include refid="INST-ORDER-COLUMNS"/>
        from TT_INST_ORDER${tabSuffix}
        where order_type='B' AND STATUS IN
        <foreach collection="orderStatusList" item="orderStatus" open="(" close=")" separator="," >
            #{orderStatus}
        </foreach>

        AND INST_ORDER_ID IN (SELECT INST_ORDER_ID FROM TT_REFUND_ORDER WHERE FUNDIN_ORDER_NO=#{fundInOrderNo})

    </select>

    <select id="MS-LOAD-SINGLE-ORDER-4-QUERY" resultType="java.lang.Long">
        SELECT INST_ORDER_ID FROM (<![CDATA[
				SELECT  INST_ORDER_ID
                FROM TT_INST_ORDER${tabSuffix}
                WHERE  STATUS IN ('I','V')
                	AND COMMUNICATE_TYPE = 'S'
                	AND IS_ADVANCE in('N','A')
                	AND FLAG = 'D'
                	AND GMT_NEXT_RETRY <= now()
                	AND GMT_BOOKING_SUBMIT >= #{startTime}
                	AND GMT_BOOKING_SUBMIT < #{endTime}
                	AND ORDER_TYPE = #{orderType}
                    AND COMMUNICATE_STATUS IN ('S','F')
              ]]>
        ORDER BY INST_ORDER_ID ASC)
        T1 WHERE 1=1  limit #{maxSize}
    </select>

    <select id="MS-LOAD-SINGLE-ORDER-4-SEND" resultType="java.lang.Long">
        SELECT INST_ORDER_ID FROM (<![CDATA[
				SELECT  INST_ORDER_ID
                FROM TT_INST_ORDER${tabSuffix}
                WHERE  STATUS = 'I'
                	AND COMMUNICATE_TYPE = 'S'
                	AND IS_ADVANCE = 'N'
                	AND FLAG = 'D'
                	AND GMT_BOOKING_SUBMIT < now()
                	AND GMT_BOOKING_SUBMIT >= #{startTime}
                	AND GMT_BOOKING_SUBMIT < #{endTime}
                	AND ORDER_TYPE = #{orderType}
                    AND COMMUNICATE_STATUS IN ('A')
                    AND SEND_TYPE = 'A'
              ]]>
        <if test="channelCodeList != null and channelCodeList.size()>0">
            and FUND_CHANNEL_CODE in
            <foreach collection="channelCodeList" item="channelCode" open="(" close=")" separator="," >
                #{channelCode}
            </foreach>

        </if>
        <if test="ignoreChannelList != null and ignoreChannelList.size()>0">
            and FUND_CHANNEL_CODE not in
            <foreach collection="ignoreChannelList" item="ignoreChannel" open="(" close=")" separator="," >
                #{ignoreChannel}
            </foreach>

        </if>
        ORDER BY INST_ORDER_ID ASC)
        T1 WHERE 1=1  limit #{rownum}
    </select>

    <update id="MS-UNIQUE-UPDATE-INST-ORDER-NO-BY-ID">
            update TT_UNIQUE_ORDER
            set INST_ORDER_NO =  #{instOrderNo}
            where INST_ORDER_ID = #{instOrderId}
    </update>

</mapper>
