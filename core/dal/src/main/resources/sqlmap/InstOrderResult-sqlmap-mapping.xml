<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!-- ==========================================================  -->
<!-- Configuration for ibatis sqlmap mapping.                    -->
<!-- ==========================================================  -->

<!-- ============================================================================= -->
<!-- This file is generated by <tt>counter-dalgen</tt>, a DAL (Data Access Layer)  -->
<!-- code generation utility specially developed for <tt>iwallet</tt> project.     -->
<!--                                                                               -->
<!-- PLEASE DO NOT MODIFY THIS FILE MANUALLY, or else your modification may be     -->
<!-- OVERWRITTEN by someone else. To modify the file, you should go to directory   -->
<!-- <tt>(project-home)/biz/dal/src/conf/dalgen</tt>, and find the corresponding   -->
<!-- configuration files. Modify those files according to your needs, then run     -->
<!-- <tt>iwallet-dalgen</tt> to generate this file.                                -->
<!--                                                                               -->
<!-- <AUTHOR>                                                              -->
<!-- ============================================================================= -->

<mapper namespace="cmf">
    <!-- ============================================= -->
    <!-- RESULT MAPS                                   -->
    <!-- ============================================= -->

    <!-- result maps for database table TT_INST_ORDER_RESULT${tabSuffix} -->
    <resultMap id="RM-INST-ORDER-RESULT" type="com.uaepay.cmf.common.core.dal.dataobject.InstOrderResultDO">
        <result property="resultId" column="RESULT_ID" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="instOrderId" column="INST_ORDER_ID" javaType="java.lang.Long" jdbcType="NUMERIC"/>
        <result property="instSeqNo" column="INST_SEQ_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="orderType" column="ORDER_TYPE" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="realAmount" column="REAL_AMOUNT"/>
        <result property="orgiInstOrderNo" column="ORGI_INST_ORDER_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="instStatus" column="INST_STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="instResultCode" column="INST_RESULT_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="returnTimes" column="RETURN_TIMES" javaType="java.lang.Integer" jdbcType="NUMERIC"/>
        <result property="gmtCreate" column="GMT_CREATE" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="GMT_MODIFIED" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result property="memo" column="MEMO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="fundChannelCode" column="FUND_CHANNEL_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="instOrderNo" column="INST_ORDER_NO" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="operateStatus" column="OPERATE_STATUS" javaType="java.lang.String" jdbcType="CHAR"/>
        <result property="extension" column="EXTENSION" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="apiResultCode" column="API_RESULT_CODE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="apiResultSubCode" column="API_RESULT_SUB_CODE" javaType="java.lang.String"
                jdbcType="VARCHAR"/>
        <result property="apiType" column="API_TYPE" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="riskFlag" column="RISK_FLAG" javaType="java.lang.String" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="INST-RESULT-PARAM">
        RESULT_ID, INST_ORDER_ID, INST_SEQ_NO, ORDER_TYPE, REAL_AMOUNT, INST_ORDER_NO, REAL_CURRENCY CURRENCY, ORGI_INST_ORDER_NO, OPERATE_STATUS, FUND_CHANNEL_CODE, INST_STATUS, INST_RESULT_CODE, RETURN_TIMES, GMT_CREATE, GMT_MODIFIED, MEMO, EXTENSION, API_RESULT_CODE, API_RESULT_SUB_CODE, API_TYPE, RISK_FLAG
    </sql>
    <sql id="INST-RESULT-PARAM-INSERT">
        RESULT_ID, INST_ORDER_ID, INST_SEQ_NO, ORDER_TYPE, REAL_AMOUNT, INST_ORDER_NO, REAL_CURRENCY, ORGI_INST_ORDER_NO, OPERATE_STATUS, FUND_CHANNEL_CODE, INST_STATUS, INST_RESULT_CODE, RETURN_TIMES, GMT_CREATE, GMT_MODIFIED, MEMO, EXTENSION, API_RESULT_CODE, API_RESULT_SUB_CODE, API_TYPE, RISK_FLAG
    </sql>

    <!-- ============================================= -->
    <!-- mapped statements for IbatisInstOrderResultDAO -->
    <!-- ============================================= -->
    <!-- mapped statement for IbatisInstOrderResultDAO.insert -->
    <insert id="MS-INST-ORDER-RESULT-INSERT">
        insert into TT_INST_ORDER_RESULT${tabSuffix}(<include refid="INST-RESULT-PARAM-INSERT"/>)
        <![CDATA[ values (#{resultId}, #{instOrderId}, #{instSeqNo}, #{orderType}, #{realAmount.amount}, #{instOrderNo}, #{realAmount.currency}, #{orgiInstOrderNo}, #{operateStatus}, #{fundChannelCode}, #{instStatus}, #{instResultCode}, #{returnTimes}, now(), now(), #{memo}, #{extension}, #{apiResultCode}, #{apiResultSubCode}, #{apiType}, #{riskFlag})
    ]]>
    </insert>

    <!-- mapped statement for IbatisInstOrderResultDAO.loadById -->
    <select id="MS-INST-ORDER-RESULT-LOAD-BY-ID" resultMap="RM-INST-ORDER-RESULT">
        select
        <include refid="INST-RESULT-PARAM"/>
        <![CDATA[
         from TT_INST_ORDER_RESULT${tabSuffix} where (RESULT_ID = #{value})
    ]]>
    </select>

    <!-- mapped statement for IbatisInstOrderResultDAO.lockedById -->
    <select id="MS-INST-ORDER-RESULT-LOCKED-BY-ID" resultMap="RM-INST-ORDER-RESULT">
        select
        <include refid="INST-RESULT-PARAM"/>
        <![CDATA[
         from TT_INST_ORDER_RESULT${tabSuffix} where (RESULT_ID = #{value}) for update
    ]]>
    </select>

    <!-- mapped statement for IbatisInstOrderResultDAO.loadByOrder -->
    <select id="MS-INST-ORDER-RESULT-LOAD-BY-ORDER" resultMap="RM-INST-ORDER-RESULT">
        select
        <include refid="INST-RESULT-PARAM"/>
        <![CDATA[
         from TT_INST_ORDER_RESULT${tabSuffix} where ((INST_ORDER_ID = #{value}) AND (INST_STATUS != 'Q')) order by RESULT_ID DESC
        ]]>
    </select>

    <!-- mapped statement for IbatisInstOrderResultDAO.updateRiskFlagById -->
    <update id="MS-INST-ORDER-RESULT-UPDATE-RISK-FLAG-BY-ID">
        <![CDATA[
                update TT_INST_ORDER_RESULT${tabSuffix} set gmt_modified=now(), RISK_FLAG=#{riskFlag} where (RESULT_ID = #{resultId})
        ]]>
    </update>

    <!-- mapped statement for IbatisInstOrderResultDAO.updateOperateStatusById -->
    <update id="MS-INST-ORDER-RESULT-UPDATE-OPERATE-STATUS-BY-ID">
        <![CDATA[

                update TT_INST_ORDER_RESULT${tabSuffix} set gmt_modified=now(), OPERATE_STATUS=#{operateStatus} where (RESULT_ID = #{resultId})

        ]]>
    </update>

    <!-- mapped statement for IbatisInstOrderResultDAO.updateOperateStatusByResultIds -->
    <update id="MS-INST-ORDER-RESULT-UPDATE-OPERATE-STATUS-BY-RESULT-IDS">
        update TT_INST_ORDER_RESULT${tabSuffix}
        set gmt_modified = now(), OPERATE_STATUS = #{to}
        where OPERATE_STATUS = #{from}
        and RESULT_ID in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <!-- mapped statement for IbatisInstOrderResultDAO.listByInstOrderId -->
    <select id="MS-INST-ORDER-RESULT-LIST-BY-INST-ORDER-ID" resultMap="RM-INST-ORDER-RESULT">
        select
        <include refid="INST-RESULT-PARAM"/>
        <![CDATA[
         from TT_INST_ORDER_RESULT${tabSuffix} where (INST_ORDER_ID = #{value}) order by GMT_CREATE DESC
        ]]>
    </select>

    <select id="MS-INST-ORDER-RESULT-LIST-BY-INST-ORDER-ID-AND-RESULT" resultMap="RM-INST-ORDER-RESULT">
        select
        <include refid="INST-RESULT-PARAM"/>
        from TT_INST_ORDER_RESULT${tabSuffix} where (INST_ORDER_ID = #{instOrderId})
        <if test="apiType != null">
            and API_TYPE = #{apiType}
        </if>
        <if test="apiType == null">
            and API_TYPE is null
        </if>
        <if test="apiResultCode != null">
            and (API_RESULT_CODE=#{apiResultCode})
        </if>
        <if test="apiResultCode == null">
            and API_RESULT_CODE is null
        </if>
        <if test="apiResultSubCode != null">
            and API_RESULT_SUB_CODE=#{apiResultSubCode}
        </if>
        <if test="apiResultSubCode == null">
            and API_RESULT_SUB_CODE is null
        </if>
        order by RESULT_ID DESC
    </select>

    <update id="MS-INST-ORDER-RESULT-UPDATE-BY-RESULT-ID">
        <![CDATA[
        update TT_INST_ORDER_RESULT${tabSuffix} set gmt_modified=now(),RETURN_TIMES=#{result.returnTimes},INST_STATUS=#{result.instStatus} where (RESULT_ID = #{resultId})
    ]]>
    </update>

</mapper>