<rules>
    <!--<![CDATA[
    分表相关
            MS-INST-ORDER-RESULT-INSERT
            MS-CMF-ORDER-INSERT
            MS-CMF-REQUEST-INSERT
    ]]>-->
    <rule>
        <sqlmap>
            MS-INST-ORDER-INSERT
            MS-INST-ORDER-RESULT-INSERT
        </sqlmap>
        <shardingExpression>route.covert4Pay(instOrderId)</shardingExpression>
        <suffixExpression>route.calTabSuffix(instOrderId)</suffixExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>
    <rule>
        <sqlmap>
            MS-CMF-ORDER-INSERT
        </sqlmap>
        <shardingExpression>route.covert4Pay(cmfSeqNo)</shardingExpression>
        <suffixExpression>route.calTabSuffix(cmfSeqNo)</suffixExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>
    <rule>
        <sqlmap>
            MS-CMF-REQUEST-INSERT
        </sqlmap>
        <shardingExpression>route.covert4Pay(paymentSeqNo)</shardingExpression>
        <suffixExpression>route.calTabSuffix(paymentSeqNo)</suffixExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>

    <!-- instOrder的三张子表未分表 -->
    <rule>
        <sqlmap>
            MS-FUNDIN-ORDER-INSERT
            MS-FUNDOUT-ORDER-INSERT
            MS-REFUND-ORDER-INSERT
        </sqlmap>
        <shardingExpression>route.covert4Pay(instOrderId)</shardingExpression>
        <suffixExpression>route.defaultSuffix()</suffixExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>

    <rule>
        <sqlmap>
            MS-UNIQUE-ORDER-INSERT
        </sqlmap>
        <shardingExpression>route.newDb()</shardingExpression>
        <suffixExpression>route.defaultSuffix()</suffixExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>

    <rule>
        <sqlmap>
            MS-SEQ-BY-NAME
            MS-BATCH-SEQ-BY-NUM
        </sqlmap>
        <shardingExpression>
            if("SEQ_BIZ_CONFIG,SEQ_BIZ_PAYMENT_ORDER,SEQ_BIZ_STATE_HIS,SEQ_FUNDS_PAYMENT_CTRL,SEQ_PARTY_PAYMENT,SEQ_PARTY_PAYMENT_FUNDS,SEQ_PAYMENT_CTRL,SEQ_PAYMENT_ORDER,SEQ_PAYMENT_STATE_HIS".contains(seqName)){
            return "csCmfDb";
            }else
            if("SEQ_CLEARING_ORDER,SEQ_CLEARING_ORDER_SUITE,SEQ_CLEARING_SESSION,SEQ_CS_CARRIER,SEQ_SETTLEMENT_ORDER,SEQ_SETTLEMENT_ORDER_SUITE".contains(seqName)){
            return "csCmfDb";
            }else{
            return "cmfDb";
            }
        </shardingExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>
</rules>