<rules>
    <!--$ROOT 路由 -->
    <rule>
        <sqlmap>
            MS-INST-ORDER-DELETE
            MS-INST-ORDER-LOCKED-BY-ID
            <!-- 拆单与cmf在同一张分表 -->
            MS-INST-ORDER-LOAD-BY-CMF-SEQ
            MS-INST-ORDER-RESULT-LOAD-BY-ID
            MS-INST-ORDER-RESULT-LOCKED-BY-ID
            MS-INST-ORDER-RESULT-LOAD-BY-ORDER
            MS-INST-ORDER-RESULT-LIST-BY-INST-ORDER-ID
            MS-CMF-ORDER-LOAD-BY-ID
            MS-CMF-ORDER-LOCKED-BY-ID
            <!-- Fundin,Fundout,Refund三张表库路由 -->
            MS-FUNDIN-ORDER-LOAD-BY-ID
            MS-FUNDIN-ORDER-DELETE
            MS-FUNDIN-ORDER-LOCKED-BY-ID
            MS-FUNDOUT-ORDER-DELETE
            MS-FUNDOUT-OR<PERSON>R-LOAD-BY-ID
            MS-FUNDOUT-ORDER-LOCKED-BY-ID
            MS-REFUND-ORDER-<PERSON><PERSON>TE
            MS-REFUND-ORDER-LOAD-BY-ID
            MS-REFUND-ORDER-LOCKED-BY-ID
            MS-INST-ORDER-LOCKED-BY-CMF-SEQ
        </sqlmap>
        <shardingExpression>route.covert4Pay($ROOT)</shardingExpression>
        <suffixExpression>route.calTabSuffix($ROOT)</suffixExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>


    <rule>
        <sqlmap>
            MS-INST-ORDER-LOAD-BY-ID
        </sqlmap>
        <shardingExpression>route.covert4PayHis($ROOT)</shardingExpression>
        <suffixExpression>route.calTabSuffix($ROOT)</suffixExpression>
        <shards>csCmfDb,cmfDb,cmfDbHis</shards>
    </rule>

    <rule>
        <sqlmap>
            MS-CMF-ORDER-LOAD-BY-PAYMENT-NO
        </sqlmap>
        <shardingExpression>route.covert4PaySeqNoShards(paymentSeqNo)</shardingExpression>
        <suffixExpression>route.covert4PaySeqNo(paymentSeqNo)</suffixExpression>
        <shards>csCmfDb,cmfDb,cmfDbHis</shards>
        <processor>SERIAL</processor>
    </rule>

    <!--instOrderId 路由 -->
    <rule>
        <sqlmap>
            MS-INST-ORDER-UPDATE-STATUS-BY-ID
            MS-INST-ORDER-UPDATE-COMMUNICATE-STATUS-BY-ID
            MS-INST-ORDER-UPDATE-COMMUNICATE-STATUS-WITH-PRE-STATUS
            MS-INST-ORDER-UPDATE-CHANNEL-INFO-BY-ID
            MS-INST-ORDER-UPDATE-MEMO-BY-ID
            MS-INST-ORDER-UPDATE-EXTENSION-BY-ID
            MS-INST-ORDER-UPDATE-RETRY-INFO-BY-ID
            MS-INST-ORDER-UPDATE-FLAG-WITH-ORDER-ID-AND-PRE-FLAG
            MS-INST-UPDATE-FO-ORDER-RETRY-ORDER-NO
            MS-INST-ORDER-UPDATE-IS-ADVANCE-WITH-INST_ORDER_ID
            MS-INST-UPDATE-BOOKING-SUBMIT-BY-ID
        </sqlmap>
        <shardingExpression>route.covert4Pay(instOrderId)</shardingExpression>
        <suffixExpression>route.calTabSuffix(instOrderId)</suffixExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>
    <!-- resultId 路由 -->
    <rule>
        <sqlmap>
            MS-INST-ORDER-RESULT-UPDATE-OPERATE-STATUS-BY-ID
            MS-INST-ORDER-RESULT-UPDATE-RISK-FLAG-BY-ID
        </sqlmap>
        <shardingExpression>route.covert4Pay(resultId)</shardingExpression>
        <suffixExpression>route.calTabSuffix(resultId)</suffixExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>
    <!-- paymentSeqNo 路由 -->
    <rule>
        <sqlmap>
            MS-CMF-REQUEST-LOAD-BY-ID
            MS-CMF-REQUEST-LOCKED-BY-ID
            MS-CMF-REQUEST-UPDATE-STATUS-BY-ID
        </sqlmap>
        <shardingExpression>route.covert4Pay(paymentSeqNo)</shardingExpression>
        <suffixExpression>route.calTabSuffix(paymentSeqNo)</suffixExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>
    <!-- cmfSeqNo 路由 -->
    <rule>
        <sqlmap>
            MS-CMF-ORDER-UPDATE-PAYMENT-NOTIFY-STATUS-BY-ID
            MS-CMF-ORDER-UPDATE-CONFIRM-STATUS-BY-ID
            MS-CMF-ORDER-UPDATE-STATUS-CONFIRM-STATUS-BY-ID
        </sqlmap>
        <shardingExpression>route.covert4Pay(cmfSeqNo)</shardingExpression>
        <suffixExpression>route.calTabSuffix(cmfSeqNo)</suffixExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>
    <!-- id 路由 -->
    <rule>
        <sqlmap>
            MS-CMF-ORDER-UPDATE-STATUS-BY-CMF-SEQ-NO
        </sqlmap>
        <shardingExpression>route.covert4Pay(id)</shardingExpression>
        <suffixExpression>route.calTabSuffix(id)</suffixExpression>
        <shards>csCmfDb,cmfDb</shards>
    </rule>

    <rule>
        <sqlmap>
            MS-UNIQUE-ORDER-LOAD-BY-NO
        </sqlmap>
        <shardingExpression>route.newDb()</shardingExpression>
        <suffixExpression>route.defaultSuffix()</suffixExpression>
        <shards>csCmfDb</shards>
    </rule>

    <!-- 不配置就走老库,该配置应该无效 -->
    <rule>
        <sqlmap>
            MS-CMF-REQUEST-OLD-DB-INSERT
        </sqlmap>
        <shardingExpression>route.oldDb()</shardingExpression>
        <suffixExpression>route.defaultSuffix()</suffixExpression>
        <shards>cmfDb</shards>
    </rule>

    <!--需要查询10张分表-->
    <rule>
        <sqlmap>
            <!-- 批量订单,待移除 -->
            MS-INST-ORDER-LOAD-BY-ARCHIVE-BATCH-ID
            MS-INST-ORDER-LOAD-BY-BATCH-ID-STATUS
            <!-- 批量订单,待移除 -->
            MS-INST-ORDER-QUERY-INST-ORDER-4-ARCHIVE-PAGEING
            MS-INST-ORDER-QUERY-REFUND-BY-FUNDIN-ORDER
            MS-LOAD-ORDER-IDS-BY-ARCHIVE-ID
            MS-LOAD-CURRENCY-LIST-BY-BATCH-ID
            MS-INST-ORDER-PAGE-QUERY
            MS-INST-ORDER-PAGE-QUERY-COUNT-FOR-PAGING
            MS-INST-ORDER-RESULT-LIST-BY-INST-ORDER-ID-AND-RESULT
        </sqlmap>
        <suffixExpression>" "</suffixExpression>
        <shards>cmfDb</shards>
        <processor>SERIAL</processor>
    </rule>

    <rule>
        <sqlmap>
            MS-LOAD-SINGLE-ORDER-4-QUERY
            MS-LOAD-SINGLE-ORDER-4-SEND
        </sqlmap>
        <shardingExpression>route.routeDb(fixedTableSuffix)</shardingExpression>
        <suffixExpression>fixedTableSuffix</suffixExpression>
        <shards>cmfDb</shards>
        <processor>SERIAL</processor>
    </rule>

    <rule>
        <sqlmap>
            MS-INST-ORDER-QUERY-INST-ORDER-4-ARCHIVE-PAGE-SIZE
            MS-INST-ORDER-UPDATE-BATCH-ID-LIST-BY-TEMP-BATCH-ID
            MS-INST-ORDER-UPDATE-TEMP-BY-INST-ORDER-ID
            MS-INST-ORDER-COUNT-NOT-FAILURE-BY-ARCHIVE-BATCH-ID
            MS-INST-ORDER-UPDATE-BATCH-ID-2-DEFAULT
            MS-INST-ORDER-RESULT-UPDATE-OPERATE-STATUS-BY-RESULT-IDS
            MS-INST-ORDER-UPDATE-COMMUNICATE-STATUS-BATCH-BY-IDS
            MS-INST-ORDER-GET-ARCHIVE-BATCH-CNT
        </sqlmap>
        <suffixExpression>" "</suffixExpression>
        <shards>cmfDb</shards>
        <merger>numberMerger</merger>
        <processor>SERIAL</processor>
    </rule>

    <rule>
        <sqlmap>
            MS-INST-ORDER-SUM-AMOUNT-FOR-QUERY-RESULT
            MS-INST-ORDER-GET-ARCHIVE-BATCH-AMT
        </sqlmap>
        <suffixExpression>" "</suffixExpression>
        <shards>cmfDb</shards>
        <merger>doubleMerger</merger>
        <processor>SERIAL</processor>
    </rule>

</rules>