<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd"
       default-autowire="byName">


    <!-- DAO -->
    <bean id="cmfBaseSqlMapClientDAO" abstract="true">
    </bean>

    <bean name="cmfTransactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="cmfTransactionManager"/>
    </bean>
    <bean name="cmfTransactionTimeoutTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="cmfTransactionManager"/>
        <property name="timeout" value="${cmf.store.timeout:3}"/>
    </bean>

    <bean name="cmfTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="cmfDataSource"/>
    </bean>


    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="cmfDataSource" />
        <property name="configLocation" value="classpath:sqlmap/sqlmap-cmf.xml" />
        <property name="mapperLocations">
            <list>
                <value>classpath:sqlmap/cmf-common-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/CardToken-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/CmfOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/CmfRequest-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/ControlOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/ControlOrderResult-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/FundinOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/FundoutOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/Notify3dsResult-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/InstBatchOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/InstBatchResult-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/InstOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/InstOrderResult-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/MonitorLog-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/PaymentNotifyLog-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/RefundOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/Notify3dsResult-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/SysConfiguration-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/ChannelCodeMapping.xml</value>
            </list>
        </property>
    </bean>
</beans>
