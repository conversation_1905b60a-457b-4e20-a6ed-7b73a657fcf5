<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd"
	default-autowire="byName">
    <!-- ======================================================================== -->
    <!--  DAO                                                            -->
    <!-- ======================================================================== -->
    <bean id="cardTokenDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisCardTokenDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="cmfOrderDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisCmfOrderDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="cmfRequestDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisCmfRequestDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="controlOrderDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisControlOrderDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="controlOrderResultDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisControlOrderResultDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="fundinOrderDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisFundinOrderDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="fundoutOrderDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisFundoutOrderDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="instBatchOrderDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisInstBatchOrderDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="instBatchResultDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisInstBatchResultDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="instOrderDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisInstOrderDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="instOrderResultDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisInstOrderResultDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="notify3dsResultDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisNotify3dsResultDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="monitorLogDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisMonitorLogDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="paymentNotifyLogDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisPaymentNotifyLogDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="refundOrderDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisRefundOrderDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="sysConfigurationDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisSysConfigurationDAO" parent="cmfBaseSqlMapClientDAO"/>
    <bean id="channelCodeMappingDAO" class="com.uaepay.cmf.common.core.dal.ibatis.IbatisChannelCodeMappingDAO" parent="cmfBaseSqlMapClientDAO"/>

</beans>