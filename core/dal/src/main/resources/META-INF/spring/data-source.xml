<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd"
       default-autowire="byName">

    <!-- datasource -->
    <bean id="cmfDataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="java:comp/env/CmfDataSource"/>
        <property name="expectedType" value="javax.sql.DataSource"/>
        <property name="lookupOnStartup" value="false"/>
    </bean>
    <bean id="csCmfDataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="java:comp/env/CmfDataSource"/>
        <property name="expectedType" value="javax.sql.DataSource"/>
        <property name="lookupOnStartup" value="false"/>
    </bean>
    <bean id="cmfDataSourceHistory" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="java:comp/env/CmfDataHistorySource"/>
        <property name="expectedType" value="javax.sql.DataSource"/>
        <property name="lookupOnStartup" value="false"/>
    </bean>
    <bean id="csCmfDataSourceHistory" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="java:comp/env/CmfDataHistorySource"/>
        <property name="expectedType" value="javax.sql.DataSource"/>
        <property name="lookupOnStartup" value="false"/>
    </bean>
</beans>