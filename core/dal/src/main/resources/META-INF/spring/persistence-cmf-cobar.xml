<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
         http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd"
       default-autowire="byName">


    <!-- DAO -->
    <bean id="cmfBaseSqlMapClientDAO" abstract="true">
        <property name="sqlSessionTemplate" ref="cobarSqlSession"></property>
    </bean>

    <bean name="cmfTransactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="cmfTransactionManager"/>
    </bean>
    <bean name="cmfTransactionTimeoutTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="cmfTransactionManager"/>
        <property name="timeout" value="${cmf.store.timeout:3}"/>
    </bean>

    <bean id="cmfTransactionManager"
          class="com.uaepay.basic.cobarclient.transaction.MultipleDataSourcesTransactionManager">
        <property name="cobarDataSourceService" ref="dataSources"/>
        <property name="transactionSynchronization" value="2"/>
    </bean>
    <bean name="cmfTransactionTemplateNew"
          class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="cmfTransactionManager"/>
        <property name="propagationBehaviorName" value="PROPAGATION_REQUIRES_NEW"/>
    </bean>

    <bean id="dataSources" class="com.uaepay.basic.cobarclient.datasources.DefaultCobarDataSourceService">
        <property name="dataSourceDescriptors">
            <set>
                <bean class="com.uaepay.basic.cobarclient.datasources.CobarDataSourceDescriptor">
                    <property name="identity" value="cmfDb"/>
                    <property name="dataSource" ref="cmfDataSource"/>
                </bean>
                <bean class="com.uaepay.basic.cobarclient.datasources.CobarDataSourceDescriptor">
                    <property name="identity" value="csCmfDb"/>
                    <property name="dataSource" ref="csCmfDataSource"/>
                    <property name="poolSize" value="${csCmfDb.history.poolsize:10}"/>
                </bean>
                <bean class="com.uaepay.basic.cobarclient.datasources.CobarDataSourceDescriptor">
                    <property name="identity" value="cmfDbHis"/>
                    <property name="dataSource" ref="cmfDataSourceHistory"/>
                </bean>
                <bean class="com.uaepay.basic.cobarclient.datasources.CobarDataSourceDescriptor">
                    <property name="identity" value="csCmfDbHis"/>
                    <property name="dataSource" ref="csCmfDataSourceHistory"/>
                    <property name="poolSize" value="${csCmfDb.history.poolsize:10}"/>
                </bean>
            </set>
        </property>
    </bean>


    <bean id="dbRouter" class="com.uaepay.cmf.common.core.dal.util.DbRouter"/>
    <util:map id="functionsMap">
        <entry key="route" value-ref="dbRouter"/>
    </util:map>

    <bean id="databaseRouter" class="com.uaepay.basic.cobarclient.config.SimpleRouterFactoryBean">
        <property name="configLocations">
            <list>
                <value>classpath:/routing/cscmf-rules-insert.xml</value>
                <value>classpath:/routing/cscmf-rules-sharding.xml</value>
            </list>
        </property>
        <property name="functions" ref="functionsMap"/>
        <property name="cobarDataSourceService" ref="dataSources"/>
    </bean>
    <bean id="numberMerger" class="com.uaepay.basic.cobarclient.merger.SerialNumberMerger"/>
    <bean id="doubleMerger" class="com.uaepay.basic.cobarclient.merger.SerialDoubleMerger"/>
    <util:map id="mergersMap">
        <entry key="numberMerger" value-ref="numberMerger"/>
        <entry key="doubleMerger" value-ref="doubleMerger"/>
    </util:map>

    <bean id="cobarTransactionFactory" class="com.uaepay.basic.cobarclient.transaction.CobarForSpringJdbcTransactionFactory" />

    <bean id="cobarSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="cmfDataSource" />
        <property name="configLocation" value="classpath:sqlmap/sqlmap-cmf.xml" />
        <property name="mapperLocations">
            <list>
                <value>classpath:sqlmap/cmf-common-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/CardToken-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/CmfOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/CmfRequest-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/ControlOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/ControlOrderResult-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/FundinOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/FundoutOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/Notify3dsResult-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/InstBatchOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/InstBatchResult-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/InstOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/InstOrderResult-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/MonitorLog-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/PaymentNotifyLog-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/RefundOrder-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/Notify3dsResult-sqlmap-mapping.xml</value>
                <value>classpath:sqlmap/SysConfiguration-sqlmap-mapping.xml</value>
            </list>
        </property>
        <property name="transactionFactory" ref="cobarTransactionFactory"/>
    </bean>


    <bean id="cobarSqlSession" class="com.uaepay.basic.cobarclient.mybatis.CobarSqlSessionTemplate">
        <constructor-arg index="0" ref="cobarSqlSessionFactory" />
        <property name="cobarDataSourceService" ref="dataSources"/>
        <property name="router" ref="databaseRouter"/>
        <property name="mergers" ref="mergersMap"/>
        <!--是否打印慢sql-->
        <property name="profileLongTimeRunningSql" value="true" />
        <property name="longTimeRunningSqlIntervalThreshold" value="1000" />
    </bean>


</beans>
