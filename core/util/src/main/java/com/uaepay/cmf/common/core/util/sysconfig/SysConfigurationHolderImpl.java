package com.uaepay.cmf.common.core.util.sysconfig;

import com.uaepay.cmf.common.core.dal.daointerface.SysConfigurationDAO;
import com.uaepay.cmf.common.core.dal.dataobject.SysConfigurationDO;
import com.uaepay.cmf.common.core.domain.config.SysConfiguration;
import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.engine.cache.CacheOperateTemplate;
import com.uaepay.cmf.common.core.engine.cache.DataLoader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.SupplierWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 系统配置信息
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: SysConfigurationHolderImpl.java, v 0.1 2011-6-13 上午09:27:30 sean won Exp $ modified 2015/4/7 12:34
 */
@Service
@Slf4j
public class SysConfigurationHolderImpl implements SysConfigurationHolder, SysConfigureKey {

    @Resource
    private SysConfigurationDAO sysConfigurationDAO;

    /**
     * 缓存数据加载模板
     */
    @Resource
    private CacheOperateTemplate operateTemplate;

    @Override
    public SysConfiguration getConfiguration(final String key) {
        return (SysConfiguration) operateTemplate.load(CacheType.CMF_SYS_CONFIGURATION, key,
                (DataLoader<SysConfiguration>) () -> convert(sysConfigurationDAO.loadByKey(key)));
    }

    @Override
    public String loadConfigureOrDefault(String key, String defaultVal) {
        SysConfiguration sysConfiguration = getConfiguration(key);
        if (sysConfiguration != null) {
            return sysConfiguration.getAttrValue();
        }
        return defaultVal;
    }

    @Override
    public int loadConfigureOrDefault(String key, int defaultVal) {
        SysConfiguration sysConfiguration = getConfiguration(key);
        if (sysConfiguration != null && StringUtils.isNumeric(sysConfiguration.getAttrValue())) {
            return Integer.valueOf(sysConfiguration.getAttrValue());
        }
        return defaultVal;
    }

    @Override
    public SysConfiguration update(String key, String value) {
        SysConfigurationDO sysConfiguration = sysConfigurationDAO.loadByKey(key);
        // 值相等则不更新
        if (StringUtils.equals(sysConfiguration.getAttrValue(), value)) {
            return convert(sysConfiguration);
        }
        sysConfiguration.setAttrValue(value);
        sysConfigurationDAO.update(sysConfiguration);
        refreshCache();
        return convert(sysConfiguration);
    }

    @Override
    public void refreshCache() {
        CompletableFuture.supplyAsync(SupplierWrapper.of(() -> {
            try {
                List<SysConfiguration> sysConfigurationList = (List<SysConfiguration>) operateTemplate.load(CacheType.CMF_SYS_CONFIGURATION, SYS_CONFIGURATION_CACHE_ALL,
                        (DataLoader<List<SysConfiguration>>) () -> {
                            log.info("Async refresh cache started");
                            return loadConfigurations();
                        });
                if (CollectionUtils.isNotEmpty(sysConfigurationList)) {
                    Set<String> keys = sysConfigurationList.stream().map(SysConfiguration::getAttrName).collect(Collectors.toSet());
                    operateTemplate.deleteByKeys(CacheType.CMF_SYS_CONFIGURATION, keys);
                }

                operateTemplate.refresh(CacheType.CMF_SYS_CONFIGURATION, () -> {
                    Map<String, Object> allMap = new HashMap<>(150);
                    for (SysConfiguration sysConfiguration : loadConfigurations()) {
                        allMap.put(sysConfiguration.getAttrName(), sysConfiguration);
                    }
                    allMap.put(SYS_CONFIGURATION_CACHE_ALL, loadConfigurations());
                    return allMap;
                });
                log.info("Async refresh cache completed");
            } catch (Exception e) {
                log.error("Async refresh cache failed", e);
            }
            return null;
        }));
    }

    @Override
    public List<SysConfiguration> loadAll() {
        return (List<SysConfiguration>) operateTemplate.load(CacheType.CMF_SYS_CONFIGURATION,
                SYS_CONFIGURATION_CACHE_ALL, (DataLoader<List<SysConfiguration>>) () -> loadConfigurations());
    }

    private List<SysConfiguration> loadConfigurations() {
        List<SysConfigurationDO> dbItems = sysConfigurationDAO.loadAll();
        if (null == dbItems) {
            return null;
        }
        List<SysConfiguration> items = new ArrayList<>();
        for (SysConfigurationDO item : dbItems) {
            SysConfiguration bean = new SysConfiguration(item.getAttrName(), item.getAttrValue(), item.getMemo());
            items.add(bean);
        }
        return items;
    }

    private SysConfiguration convert(SysConfigurationDO sysConfigurationDO) {
        if (sysConfigurationDO == null) {
            return null;
        }
        return new SysConfiguration(sysConfigurationDO.getAttrName(), sysConfigurationDO.getAttrValue(),
                sysConfigurationDO.getMemo());
    }

    @Override
    public Boolean insert(String key, String value, String memo) {
        SysConfigurationDO sysConfiguration = sysConfigurationDAO.loadByKey(key);

        if (sysConfiguration == null) {
            SysConfigurationDO configurationDO = new SysConfigurationDO(key, value, memo);
            sysConfigurationDAO.insert(configurationDO);
            refreshCache();
            return true;
        }

        // 值相等则不更新
        if (StringUtils.equals(sysConfiguration.getAttrValue(), value)) {
            return true;
        }
        sysConfiguration.setAttrValue(value);
        memo = StringUtils.isNotBlank(memo) ? memo : sysConfiguration.getMemo();
        sysConfiguration.setMemo(memo);
        sysConfigurationDAO.update(sysConfiguration);
        refreshCache();
        return true;
    }
}
