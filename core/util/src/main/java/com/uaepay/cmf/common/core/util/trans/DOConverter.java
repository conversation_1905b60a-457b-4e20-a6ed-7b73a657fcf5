package com.uaepay.cmf.common.core.util.trans;

import java.util.ArrayList;
import java.util.List;

import com.uaepay.basic.cobarclient.support.utils.CollectionUtils;

/**
 * <p>DO到Domain对象的转换类</p>
 *
 * <AUTHOR> won
 * @version $Id: DOConverter.java, v 0.1 2011-3-2 下午07:22:17 sean won Exp $
 */
public abstract class DOConverter<Bo, Doo> {
    public abstract Bo convert(Doo from);

    public List<Bo> convert(List<Doo> ds) {
        if (CollectionUtils.isEmpty(ds)) {
            return new ArrayList<>();
        }
        List<Bo> result = new ArrayList<>();
        for (Doo d : ds) {
            result.add(convert(d));
        }
        return result;
    }
}
