package com.uaepay.cmf.common.core.util.log;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;

/**
 * <p>
 * 日志工具.
 * </p>
 *
 * <AUTHOR>
 * @version LogUtil.java 1.0 @2015/12/15 16:21 $
 */
public class LogUtil implements BasicConstant {

    private static Logger serviceCostLogger = LoggerFactory.getLogger("SERVICE-COST-LOGGER");

    public static void info(String suffix, Long startMillis, Long endMillis) {
        // 超过1秒钟才打日志
        if ((endMillis - startMillis) > 1000) {
            serviceCostLogger.info(suffix + ":TimeRange[" + startMillis + "," + endMillis
                                   + "],Spend[" + (endMillis - startMillis) + "]");
        }
    }

}
