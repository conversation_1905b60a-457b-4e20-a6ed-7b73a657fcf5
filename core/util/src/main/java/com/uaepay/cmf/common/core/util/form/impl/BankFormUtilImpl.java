package com.uaepay.cmf.common.core.util.form.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.uaepay.cmf.common.core.util.sysconfig.SysConfigureKey;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.uaepay.cmf.common.core.domain.config.SysConfiguration;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.core.util.form.BankFormUtil;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import org.springframework.stereotype.Service;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version BankFormUtilImpl.java 1.0 Created@2017-11-27 14:14 $
 */
@Service
public class BankFormUtilImpl implements BankFormUtil, BasicConstant {
    private Logger logger = LoggerFactory
            .getLogger(BankFormUtilImpl.class);

    @Resource
    private SysConfigurationHolder sysConfigurationHolder;

    private static final List<String> filterKeyList = Arrays.asList(ExtensionKey.FC_CODE.key,
            ExtensionKey.INST_AMOUNT.key,
            ExtensionKey.INST_ORDER_NO.key,
            ExtensionKey.PARTNER_ID.key,
            ExtensionKey.EBANK_CHARSET.key, ENCTYPE);

    /**
     * 组装form/Html信息.
     *
     * @param fundResult
     * @return
     */
    @Override
    public String buildSignForm(ChannelFundResult fundResult) {
        if (!needSignForm(fundResult)) {
            return fundResult.getInstUrl();
        }
        Map<String, String> extMap = null;
        if (StringUtils.isNotBlank(fundResult.getExtension())) {
            extMap = MapUtil.jsonToMap(fundResult.getExtension());
        }
        if (extMap != null && extMap.containsKey(ExtensionKey.PAGE_URL_FOR_SIGN.key)) {
            return extMap.get(ExtensionKey.PAGE_URL_FOR_SIGN.key);
        }
        StringBuilder accum = new StringBuilder();
        accum.append("<form id='frmBankID' name='frmBankName' method='post'")
                .append(buildEnctypeField(extMap)).append("action='").append(fundResult.getInstUrl())
                .append("'>");

        if (extMap != null) {
            for (Map.Entry<String, String> entry : extMap.entrySet()) {
                if (isFilterKey(entry.getKey())) {
                    continue;
                }

                accum.append("<input type='hidden' name='").append(entry.getKey())
                        .append("'  value='").append(entry.getValue()).append("' />");
            }
        }
        accum.append("</form>");
        logger.debug("网银B2C跳转{},form信息{}", fundResult.getInstOrderNo(), accum);
        return accum.toString();
    }

    private static String buildEnctypeField(Map<String, String> extMap) {
        if (extMap == null || !extMap.containsKey(ENCTYPE)) {
            return EMPTY_STRING;
        }
        return " " + ENCTYPE + CHAR_EQUAL + extMap.get(ENCTYPE);
    }

    private boolean isFilterKey(String key) {
        return filterKeyList.contains(key);
    }

    private boolean needSignForm(ChannelFundResult fundResult) {
        SysConfiguration config = sysConfigurationHolder
                .getConfiguration(SysConfigureKey.SIGN_FORM_ONLY_URL);
        if (null == config || StringUtils.isEmpty(config.getAttrValue())) {
            return true;
        }

        Map<String, String> extMap = MapUtil.jsonToMap(fundResult.getExtension());

        String fundChannelCode = extMap.get(ExtensionKey.FC_CODE.key);
        if (StringUtils.isEmpty(fundChannelCode)) {
            return true;
        }

        String[] fundChannelCodes = config.getAttrValue().split(CHAR_COMMA);
        for (String fcCode : fundChannelCodes) {
            if (fundChannelCode.equals(fcCode)) {
                return false;
            }
        }
        return true;
    }
}
