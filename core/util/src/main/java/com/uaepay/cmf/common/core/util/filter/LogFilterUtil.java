package com.uaepay.cmf.common.core.util.filter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;

/**
 * <p>日志过滤私密信息.</p>
 *
 *  过滤持卡人信息，包括：卡号、持卡人姓名、身份证号、手机号、有效期
 * <AUTHOR>
 * @version LogFilterUtil.java 1.0 @2015/4/7 17:56 $
 */
public class LogFilterUtil {

    private static List<String> filterList = new ArrayList<>();

    static {
        filterList.addAll(Arrays.asList("name=", "idNo=", "mobileNo=", "cardNo=",
            "accountName=", "bankAccountName=", "validateDate=", "validDate=",
            "mobilePhoneNo=", "bankAccountName=", "bankAccountNumber="));
    }

    public static String filter(String log) {
        if (log == null) {
            return null;
        }

        for (String filter : filterList) {
            int pos = -1;
            if ((pos = log.indexOf(filter)) > -1) {
                pos += filter.length();
                int end = log.indexOf(",", pos);
                end = end < 0 ? log.length() : end;
                //卡号保留后四位
                if ("cardNo=".equals(filter) && (end - pos) > 4) {
                    end -= 4;
                }
                log = append(log, pos, end);
            }
        }
        return log;
    }

    public static String filter(Map<String, Object> dataMap) {
        if (MapUtils.isEmpty(dataMap)) {
            return null;
        }
        return filter(dataMap.toString());
    }

    private static String append(String log, int begin, int end) {
        StringBuilder accum = new StringBuilder(log.substring(0, begin));
        for (; begin < end; begin++) {
            accum.append("*");
        }
        accum.append(log.substring(end));
        return accum.toString();
    }

}
