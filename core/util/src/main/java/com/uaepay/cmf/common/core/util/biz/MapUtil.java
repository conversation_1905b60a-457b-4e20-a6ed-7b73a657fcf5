package com.uaepay.cmf.common.core.util.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>JSON字符串转换工具</p>
 *
 * <AUTHOR>
 * @version $Id: MapUtil.java, v 0.1 2013-2-22 下午1:52:28 User Exp $
 */
public class MapUtil {
    /**
     * JSON字符串转成MAP
     *
     * @param ext
     * @return
     */
    public static Map<String, String> jsonToMap(String ext) {
        return StringUtils.isBlank(ext) ? new HashMap<>() : JSON
                .parseObject(ext, new TypeReference<Map<String, String>>() {
                });
    }

    /**
     * JSON字符串转成MAP，异常返回空MAP
     *
     * @param ext
     * @return
     */
    public static Map<String, String> safeJsonToMap(String ext) {
        try {
            return jsonToMap(ext);
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    /**
     * MAP转换成JSON
     *
     * @param map
     * @return
     */
    public static String mapToJson(Map<String, String> map) {
        if (CollectionUtils.isEmpty(map)) {
            return null;
        }
        return JSON.toJSONString(map, SerializerFeature.UseISO8601DateFormat);
    }

    /**
     * MAP新增VALUE
     *
     * @param source
     * @param key
     * @param value
     * @return
     */
    public static String addValue(String source, String key, String value) {
        Map<String, String> map = MapUtil.jsonToMap(source);
        map.put(key, value);
        return mapToJson(map);
    }

    public static String addValue(String source, Map<String, String> map) {
        Map<String, String> sourceMap = MapUtil.jsonToMap(source);
        if (!CollectionUtils.isEmpty(map)) {
            sourceMap.putAll(map);
        }
        return mapToJson(sourceMap);
    }
}
