package com.uaepay.cmf.common.core.util.filter;

import java.math.BigDecimal;
import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.ChannelInfoExtKey;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.schema.cmf.enums.AccessChannel;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.CompanyOrPersonal;

/**
 * 依据CMF订单与数据库中配置的属性名称mapping获取数据
 *
 * <AUTHOR> won
 * @version $Id: FilterAttributeUtil.java, v 0.1 2011-2-24 上午11:01:23 sean won Exp $
 */
@Service("filterAttributeUtil")
public class FilterAttributeUtil {
    protected static final Logger logger = LoggerFactory
            .getLogger(FilterAttributeUtil.class);

    private static final String ROUTER_FUND_KEY = "fundKey";
    private static final String ROUTER_CONTROL_KEY = "fundControlKey";
    private static final String ROUTER_POS_KEY = "fundPosKey";

    private static Map<String, List<String>> keyMap = new HashMap<>();

    public static List<String> combine(String... key) {
        return new ArrayList<>(Arrays.asList(key));
    }

    static {
        keyMap.put(
                ROUTER_FUND_KEY,
                combine(ExtensionKey.DBCR.key,
                        ExtensionKey.CARD_TYPE.key, ExtensionKey.COMPANY_OR_PERSONAL.key,
                        ExtensionKey.ACCESS_CHANNEL.key, ExtensionKey.PRODUCT_CODE.key,
                        ExtensionKey.SIGN_NO.key));
        keyMap.put(
                ROUTER_CONTROL_KEY,
                combine(ExtensionKey.DBCR.key,
                        ExtensionKey.CARD_TYPE.key, ExtensionKey.COMPANY_OR_PERSONAL.key,
                        ExtensionKey.ACCESS_CHANNEL.key, ExtensionKey.PRODUCT_CODE.key,
                        ExtensionKey.SIGN_NO.key));
        keyMap.put(
                ROUTER_POS_KEY,
                combine(ExtensionKey.DBCR.key,
                        ExtensionKey.CARD_TYPE.key, ExtensionKey.COMPANY_OR_PERSONAL.key,
                        ExtensionKey.ACCESS_CHANNEL.key, ExtensionKey.PRODUCT_CODE.key,
                        ExtensionKey.SIGN_NO.key));
    }

    /**
     * 依据机构订单转换为map
     *
     * @param order
     * @return
     */
    public static Map<String, ?> convert(CmfOrder order) {
        Map<String, Object> map = new HashMap<>();
        if (BizType.FUNDIN.equals(order.getBizType())) {
            String dbcr = order.getExtension().get(ExtensionKey.DBCR.key);
            if (dbcr != null && !"".equals(dbcr)) {
                map.put(ChannelInfoExtKey.DBCR.getCode(), dbcr);
            }
        }
        if (BizType.FUNDOUT.equals(order.getBizType())
                && order.getExtension().get(ExtensionKey.CARD_TYPE.key) != null) {
            map.put(ChannelInfoExtKey.CARD_TYPE.getCode(),
                    order.getExtension().get(ExtensionKey.CARD_TYPE.key));
        }
        String companyOrPersonal = order.getExtension().get(ExtensionKey.COMPANY_OR_PERSONAL.key);
        if (companyOrPersonal == null || "".equals(companyOrPersonal)) {
            map.put(ChannelInfoExtKey.COMPANY_OR_PERSONAL.getCode(),
                    CompanyOrPersonal.PERSONAL.getCode());
        } else {
            map.put(ChannelInfoExtKey.COMPANY_OR_PERSONAL.getCode(), companyOrPersonal);
        }
        String accessChannel = order.getExtension().get(ExtensionKey.ACCESS_CHANNEL.key);
        if (accessChannel == null || "".equals(accessChannel)) {
            map.put(ChannelInfoExtKey.ACCESS_CHANNELS.getCode(),
                    AccessChannel.WEB.getCode());
        } else {
            map.put(ChannelInfoExtKey.ACCESS_CHANNELS.getCode(), accessChannel);
        }
        if (null == order.getAmount() || null == order.getAmount().getAmount()) {
            map.put(ChannelInfoExtKey.AMOUNT.getCode(), "0.00");
        } else {
            map.put(ChannelInfoExtKey.AMOUNT.getCode(), order.getAmount().getAmount().toString());
        }
        map.put(ChannelInfoExtKey.BIZ_TYPE.getCode(), order.getBizType());

        map.put(ChannelInfoExtKey.TARGET_INST.getCode(), order.getInstCode());
        map.put(ChannelInfoExtKey.PRODUCT_CODE.getCode(),
                order.getExtension().get(ExtensionKey.PRODUCT_CODE.key));

        //填充其它参数
        Set<String> keySet = order.getExtension().keySet();
        for (Iterator<String> it = keySet.iterator(); it.hasNext(); ) {
            String key = it.next();
            if (!map.containsKey(key) && !keyMap.get(ROUTER_FUND_KEY).contains(key)) {
                map.put(key, order.getExtension().get(key));
            }
        }

        logger.info("请求流水号:{},路由参数：{}", order.getPaymentSeqNo(), LogFilterUtil.filter(map));

        return map;
    }

    public static Map<String, ?> convert(InstControlOrder controlOrder) {
        Map<String, Object> param = new HashMap<>();
        param.put(ChannelInfoExtKey.TARGET_INST.getCode(), controlOrder.getInstCode());
        String dbcr = controlOrder.getExtension().get(ExtensionKey.DBCR.key);
        if (dbcr != null && !"".equals(dbcr)) {
            param.put(ChannelInfoExtKey.DBCR.getCode(), dbcr);
        }
        if (controlOrder.getExtension().get(ExtensionKey.CARD_TYPE.key) != null) {
            param.put(ChannelInfoExtKey.CARD_TYPE.getCode(),
                    controlOrder.getExtension().get(ExtensionKey.CARD_TYPE.key));
        }
        String companyOrPersonal = controlOrder.getExtension().get(
                ExtensionKey.COMPANY_OR_PERSONAL.key);
        if (companyOrPersonal == null || "".equals(companyOrPersonal)) {
            param.put(ChannelInfoExtKey.COMPANY_OR_PERSONAL.getCode(),
                    CompanyOrPersonal.PERSONAL.getCode());
        } else {
            param.put(ChannelInfoExtKey.COMPANY_OR_PERSONAL.getCode(), companyOrPersonal);
        }
        String accessChannel = controlOrder.getExtension().get(ExtensionKey.ACCESS_CHANNEL.key);
        if (accessChannel == null || "".equals(accessChannel)) {
            param.put(ChannelInfoExtKey.ACCESS_CHANNELS.getCode(),
                    AccessChannel.WEB.getCode());
        } else {
            param.put(ChannelInfoExtKey.ACCESS_CHANNELS.getCode(), accessChannel);
        }
        if (null == controlOrder.getAmount() || null == controlOrder.getAmount().getAmount()) {
            param.put(ChannelInfoExtKey.AMOUNT.getCode(), "0.00");
        } else {
            param.put(ChannelInfoExtKey.AMOUNT.getCode(), controlOrder.getAmount().getAmount()
                    .toString());
        }

        //填充其它参数
        Set<String> keySet = controlOrder.getExtension().keySet();
        for (Iterator<String> it = keySet.iterator(); it.hasNext(); ) {
            String key = it.next();
            if (!param.containsKey(key) && !keyMap.get(ROUTER_POS_KEY).contains(key)) {
                param.put(key, controlOrder.getExtension().get(key));
            }
        }

        logger.info("请求流水号:{},控制类指令路由参数:{}", controlOrder.getRequestNo(),
                LogFilterUtil.filter(param));

        return param;
    }

}
