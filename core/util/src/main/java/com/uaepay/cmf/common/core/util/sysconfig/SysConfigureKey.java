package com.uaepay.cmf.common.core.util.sysconfig;

/**
 * <p>系统配置键值.</p>
 *
 * <AUTHOR>
 * @version SysConfigureKey.java 1.0 Created@2016-08-24 19:19 $
 */
public interface SysConfigureKey {

    // 支付方式映射
    String PAY_MODE_REFLECTION = "PAY_MODE_REFLECTION";

    String OPEN_ACCOUNT_VERIFY_CARD_TYPE = "OPEN_ACCOUNT_VERIFY_CARD_TYPE";

    //取值为：AMOUNT_CHECK_ICBC21501
    String BATCH_FUNDOUT_AMOUNT_CHECK_PREFIX = "AMOUNT_CHECK_";

    String QUERY_BALANCE_CHANNEL_REFLECT = "QUERY_BALANCE_CHANNEL_REFLECT";

    String SYS_CONFIGURATION_CACHE_ALL = "SYS_CONFIGURATION_CACHE_ALL";

    String ALWAYS_FUNDOUT_CHANNEL = "ALWAYS_FUNDOUT_CHANNEL";

    String CARD_BIN_ROUTE_CONFIGURE = "CARD_BIN_ROUTE_CONFIGURE";

    String MA_UNIQUE_SIGN_ID_CHANNEL = "MA_UNIQUE_SIGN_ID_CHANNEL";

    String BALANCE_INSUFFICIENT_CHANNELS = "BALANCE_INSUFFICIENT_CHANNELS";

    String CHANNEL_TOKEN_REFLECT = "CHANNEL_TOKEN_REFLECT";

    String SIGN_FORM_ONLY_URL = "SIGN_FORM_ONLY_URL";

    String UAEPAY_USER_PAY_DOMAIN = "UAEPAY_USER_PAY_DOMAIN";

    String AMOUNT_CHECK_EMAIL_RECEIVERS = "AMOUNT_CHECK_EMAIL_RECEIVERS";

    String AMOUNT_CHECK_MOBILE_RECEIVERS = "AMOUNT_CHECK_MOBILE_RECEIVERS";

    String AMOUNT_CHECK_BUFFER = "AMOUNT_CHECK_BUFFER";

    String WORKDAY_BEGIN = "WORKDAY_BEGIN";
    String WORKDAY_END = "WORKDAY_END";

}
