package com.uaepay.cmf.common.core.util.filter;
//key util, com.uaepay.cmf.common.core.service.channel.Util
public class Util {
    public boolean match(String[] more, String[] less){
        if(more==null && less==null)return true;
        if(more==null || less==null)return false;
        
        for(String r : less){
            if(!in(r, more))return false;
        }
        return true;
    }

    public boolean in(String r, String[] set) {
        if(r==null || set==null)return false;
        
        for(String l : set){
            if(r.equals(l))return true;
        }
        return false;
    }
    
    /**
     * 
     * @param set
     * @param value
     * @return
     */
    public boolean contains(String[] set, String value){
        return in(value, set);
    }
    
    public boolean is(String[] set, String value){
        if(value==null)return false;
        String[] temp = value.split(",");
        return match(set, temp) && match(temp, set);
    }
    
}
