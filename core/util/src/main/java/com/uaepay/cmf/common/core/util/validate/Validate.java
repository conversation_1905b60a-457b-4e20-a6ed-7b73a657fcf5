package com.uaepay.cmf.common.core.util.validate;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.exception.ValidateException;
import com.uaepay.common.util.money.Money;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;

public class Validate implements BasicConstant {

	public static void assertNotNull(String propertyName, Object value) {
		if (value == null) {
			throw new ValidateException(propertyName + "不能为空");
		}
	}

	public static void assertNotBlank(String propertyName, String value) {
		if (StringUtils.isBlank(value)) {
			throw new ValidateException(propertyName + "不能为空");
		}
	}

	public static <T> void assertNotEmpty(String propertyName, List<T> value) {
		if (value == null || value.isEmpty()) {
			throw new ValidateException(propertyName + "不能为空");
		}
	}

	public <T> void assertNotEmpty(String propertyName, Collection<T> value) {
		if (CollectionUtils.isEmpty(value)) {
			throw new ValidateException(propertyName + "不能为空");
		}
	}

	public static void assertGreaterZero(String propertyName, Money value) {
		if ((value == null) || (value.compareTo(ZERO_MONEY) <= 0)) {
			throw new ValidateException(propertyName + "必须大于零");
		}
	}

	public static void assertGreaterEqualZero(String propertyName, Money value) {
		if ((value == null) || (value.compareTo(ZERO_MONEY) < 0)) {
			throw new ValidateException(propertyName + "必须大于等于零");
		}
	}

	public static void assertTrue(String condition, boolean value) {
		if (!value) {
			throw new ValidateException(condition);
		}
	}

	public static void assertNotBlankAndNoSpace(String propertyName, String value) {
		if (StringUtils.isBlank(value)) {
			throw new ValidateException(propertyName + "不能为空");
		}
		if (StringUtils.contains(value, ' ')) {
			throw new ValidateException(propertyName + "不能包含空格");
		}
	}

	public static void assertEquals(String propertyName, String expectValue, String value) {
		if (!StringUtils.equals(expectValue, value)) {
			throw new ValidateException(String.format("%s应该等于%s，实际为%s", propertyName, expectValue, value));
		}
	}

	public static <T> void assertIn(String propertyName, T value, T... expectValues) {
		if (expectValues == null) {
			if (value != null) {
				throw new ValidateException(propertyName + "校验不通过");
			}
		} else {
			for (T expectValue : expectValues) {
				if (value == expectValue) {
					return;
				}
			}
			throw new ValidateException(propertyName + "校验不通过");
		}
	}

}
