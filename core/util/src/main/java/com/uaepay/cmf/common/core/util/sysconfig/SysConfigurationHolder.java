package com.uaepay.cmf.common.core.util.sysconfig;

import com.uaepay.cmf.common.core.domain.config.SysConfiguration;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;

import java.util.List;

/**
 * <p>系统配置信息</p>
 *
 * <AUTHOR> won
 * @version $Id: SysConfigurationHolder.java, v 0.1 2011-6-13 上午09:24:12 sean won Exp $
 */
public interface SysConfigurationHolder extends BasicConstant {

    /**
     * 根据KEY获取系统配置.
     *
     * @param key
     * @return
     */
    SysConfiguration getConfiguration(String key);

    String loadConfigureOrDefault(String key, String defaultVal);

    int loadConfigureOrDefault(String key, int defaultVal);

    /**
     * 更新配置值
     *
     * @param key
     * @param value
     * @return
     */
    SysConfiguration update(String key, String value);

    /**
     * 刷新缓存
     */
    void refreshCache();

    /**
     * 获取所有的
     *
     * @return
     */
    List<SysConfiguration> loadAll();


    Boolean insert(String key, String value, String memo);
}
