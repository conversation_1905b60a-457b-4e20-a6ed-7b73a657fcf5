package com.uaepay.cmf.common.core.util.log;

import com.uaepay.cmf.common.core.util.BaseCoreTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.lang.reflect.Field;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LogUtil类单元测试
 * 测试日志工具功能
 */
@ExtendWith(MockitoExtension.class)
class LogUtilTest extends BaseCoreTest {
    
    @Mock
    private Logger mockLogger;
    
    private Logger originalLogger;
    
    @BeforeEach
    public void setUp() {
        try {
            super.setUp();
            MockitoAnnotations.openMocks(this);
            
            // 使用反射获取原始logger并替换为mock
            Field loggerField = LogUtil.class.getDeclaredField("serviceCostLogger");
            loggerField.setAccessible(true);
            originalLogger = (Logger) loggerField.get(null);
            loggerField.set(null, mockLogger);
        } catch (Exception e) {
            throw new RuntimeException("Failed to setup test", e);
        }
    }
    
    @AfterEach
    public void tearDown() {
        try {
            // 恢复原始logger
            Field loggerField = LogUtil.class.getDeclaredField("serviceCostLogger");
            loggerField.setAccessible(true);
            loggerField.set(null, originalLogger);
        } catch (Exception e) {
            throw new RuntimeException("Failed to teardown test", e);
        }
    }
    
    @Test
    @DisplayName("测试info方法-耗时超过1秒钟打日志")
    void testInfo_LongTime() {
        // Given
        String suffix = "testOperation";
        Long startMillis = 1000L;
        Long endMillis = 2500L; // 耗时1.5秒，超过1秒阈值
        
        // When
        LogUtil.info(suffix, startMillis, endMillis);
        
        // Then
        verify(mockLogger).info(
            eq("testOperation:TimeRange[1000,2500],Spend[1500]")
        );
    }
    
    @Test
    @DisplayName("测试info方法-耗时小于等于1秒不打日志")
    void testInfo_ShortTime() {
        // Given
        String suffix = "testOperation";
        Long startMillis = 1000L;
        Long endMillis = 1500L; // 耗时0.5秒，不超过1秒阈值
        
        // When
        LogUtil.info(suffix, startMillis, endMillis);
        
        // Then
        verify(mockLogger, never()).info(anyString());
    }
    
    @Test
    @DisplayName("测试info方法-耗时正好1秒不打日志")
    void testInfo_ExactlyOneSecond() {
        // Given
        String suffix = "testOperation";
        Long startMillis = 1000L;
        Long endMillis = 2000L; // 耗时正好1秒
        
        // When
        LogUtil.info(suffix, startMillis, endMillis);
        
        // Then
        verify(mockLogger, never()).info(anyString());
    }
    
    @Test
    @DisplayName("测试info方法-耗时超过1秒1毫秒打日志")
    void testInfo_JustOverOneSecond() {
        // Given
        String suffix = "performanceTest";
        Long startMillis = 2000L;
        Long endMillis = 3001L; // 耗时1001毫秒，刚好超过1秒
        
        // When
        LogUtil.info(suffix, startMillis, endMillis);
        
        // Then
        verify(mockLogger).info(
            eq("performanceTest:TimeRange[2000,3001],Spend[1001]")
        );
    }
    
    @Test
    @DisplayName("测试info方法-空字符串suffix")
    void testInfo_EmptySuffix() {
        // Given
        String suffix = "";
        Long startMillis = 1000L;
        Long endMillis = 2500L; // 耗时1.5秒
        
        // When
        LogUtil.info(suffix, startMillis, endMillis);
        
        // Then
        verify(mockLogger).info(
            eq(":TimeRange[1000,2500],Spend[1500]")
        );
    }
    
    @Test
    @DisplayName("测试info方法-null suffix")
    void testInfo_NullSuffix() {
        // Given
        String suffix = null;
        Long startMillis = 1000L;
        Long endMillis = 2500L; // 耗时1.5秒
        
        // When
        LogUtil.info(suffix, startMillis, endMillis);
        
        // Then
        verify(mockLogger).info(
            eq("null:TimeRange[1000,2500],Spend[1500]")
        );
    }
    
    @Test
    @DisplayName("测试info方法-长时间耗时")
    void testInfo_VeryLongTime() {
        // Given
        String suffix = "longOperation";
        Long startMillis = 1000L;
        Long endMillis = 11000L; // 耗时10秒
        
        // When
        LogUtil.info(suffix, startMillis, endMillis);
        
        // Then
        verify(mockLogger).info(
            eq("longOperation:TimeRange[1000,11000],Spend[10000]")
        );
    }
    
    @Test
    @DisplayName("测试边界值-时间参数为0")
    void testInfo_ZeroTime() {
        // Given
        String suffix = "zeroTest";
        Long startMillis = 0L;
        Long endMillis = 1500L; // 耗时1.5秒
        
        // When
        LogUtil.info(suffix, startMillis, endMillis);
        
        // Then
        verify(mockLogger).info(
            eq("zeroTest:TimeRange[0,1500],Spend[1500]")
        );
    }
    
    @Test
    @DisplayName("测试边界值-负数时间差")
    void testInfo_NegativeTimeDiff() {
        // Given
        String suffix = "negativeTest";
        Long startMillis = 2000L;
        Long endMillis = 1000L; // 结束时间小于开始时间
        
        // When
        LogUtil.info(suffix, startMillis, endMillis);
        
        // Then
        verify(mockLogger, never()).info(anyString());
    }
} 