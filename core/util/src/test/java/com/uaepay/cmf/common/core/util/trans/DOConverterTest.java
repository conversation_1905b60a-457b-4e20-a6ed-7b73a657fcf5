package com.uaepay.cmf.common.core.util.trans;

import com.uaepay.cmf.common.core.util.BaseCoreTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import com.uaepay.basic.cobarclient.support.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * DOConverter类单元测试
 * 测试抽象转换器的实现
 */
@ExtendWith(MockitoExtension.class)
class DOConverterTest extends BaseCoreTest {
    
    /**
     * 创建具体实现用于测试
     */
    private static class TestDOConverter extends DOConverter<String, Integer> {
        @Override
        public String convert(Integer from) {
            return from == null ? null : "value_" + from;
        }
    }
    
    private final TestDOConverter converter = new TestDOConverter();
    
    @Test
    @DisplayName("测试convert单个对象-成功")
    void testConvert_Single_Success() {
        // Given
        Integer input = 123;
        
        // When
        String result = converter.convert(input);
        
        // Then
        assertThat(result).isEqualTo("value_123");
    }
    
    @Test
    @DisplayName("测试convert单个对象-null输入")
    void testConvert_Single_Null() {
        // Given
        Integer input = null;
        
        // When
        String result = converter.convert(input);
        
        // Then
        assertThat(result).isNull();
    }
    
    @Test
    @DisplayName("测试convert列表-成功")
    void testConvert_List_Success() {
        // Given
        List<Integer> input = Arrays.asList(1, 2, 3);
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(input)).thenReturn(false);
            
            List<String> result = converter.convert(input);
            
            assertThat(result).hasSize(3);
            assertThat(result).containsExactly("value_1", "value_2", "value_3");
            
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(input));
        }
    }
    
    @Test
    @DisplayName("测试convert列表-空列表")
    void testConvert_List_Empty() {
        // Given
        List<Integer> input = new ArrayList<>();
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(input)).thenReturn(true);
            
            List<String> result = converter.convert(input);
            
            assertThat(result).isNotNull().isEmpty();
            
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(input));
        }
    }
    
    @Test
    @DisplayName("测试convert列表-null输入")
    void testConvert_List_Null() {
        // Given
        List<Integer> input = null;
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(input)).thenReturn(true);
            
            List<String> result = converter.convert(input);
            
            assertThat(result).isNotNull().isEmpty();
            
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(input));
        }
    }
    
    @Test
    @DisplayName("测试convert列表-包含null元素")
    void testConvert_List_WithNullElements() {
        // Given
        List<Integer> input = Arrays.asList(1, null, 3);
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(input)).thenReturn(false);
            
            List<String> result = converter.convert(input);
            
            assertThat(result).hasSize(3);
            assertThat(result).containsExactly("value_1", null, "value_3");
            
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(input));
        }
    }
    
    @Test
    @DisplayName("测试convert列表-全为null元素")
    void testConvert_List_AllNullElements() {
        // Given
        List<Integer> input = Arrays.asList(null, null, null);
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(input)).thenReturn(false);
            
            List<String> result = converter.convert(input);
            
            assertThat(result).hasSize(3);
            assertThat(result).containsOnly((String) null);
            
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(input));
        }
    }
    
    @Test
    @DisplayName("测试convert列表-单元素列表")
    void testConvert_List_SingleElement() {
        // Given
        List<Integer> input = Arrays.asList(42);
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(input)).thenReturn(false);
            
            List<String> result = converter.convert(input);
            
            assertThat(result).hasSize(1);
            assertThat(result).containsExactly("value_42");
            
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(input));
        }
    }
    
    @Test
    @DisplayName("测试convert列表-大列表")
    void testConvert_List_Large() {
        // Given
        List<Integer> input = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            input.add(i);
        }
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(input)).thenReturn(false);
            
            List<String> result = converter.convert(input);
            
            assertThat(result).hasSize(1000);
            assertThat(result.get(0)).isEqualTo("value_0");
            assertThat(result.get(999)).isEqualTo("value_999");
            
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(input));
        }
    }
    
    /**
     * 测试边界值情况
     */
    @Test
    @DisplayName("测试边界值-Integer最大值")
    void testConvert_BoundaryValue_MaxInteger() {
        // Given
        Integer input = Integer.MAX_VALUE;
        
        // When
        String result = converter.convert(input);
        
        // Then
        assertThat(result).isEqualTo("value_" + Integer.MAX_VALUE);
    }
    
    @Test
    @DisplayName("测试边界值-Integer最小值")
    void testConvert_BoundaryValue_MinInteger() {
        // Given
        Integer input = Integer.MIN_VALUE;
        
        // When
        String result = converter.convert(input);
        
        // Then
        assertThat(result).isEqualTo("value_" + Integer.MIN_VALUE);
    }
    
    @Test
    @DisplayName("测试边界值-零值")
    void testConvert_BoundaryValue_Zero() {
        // Given
        Integer input = 0;
        
        // When
        String result = converter.convert(input);
        
        // Then
        assertThat(result).isEqualTo("value_0");
    }
} 