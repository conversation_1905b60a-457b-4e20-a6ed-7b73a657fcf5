package com.uaepay.cmf.common.core.util.sysconfig;

import com.uaepay.cmf.common.core.util.BaseCoreTest;
import com.uaepay.cmf.common.core.dal.daointerface.SysConfigurationDAO;
import com.uaepay.cmf.common.core.dal.dataobject.SysConfigurationDO;
import com.uaepay.cmf.common.core.domain.config.SysConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.*;

/**
 * SysConfigurationHolderImpl简化单元测试
 * 专注于核心功能，避免复杂Mock
 */
@ExtendWith(MockitoExtension.class)
class SimpleSysConfigurationHolderImplTest extends BaseCoreTest {
    
    @Mock
    private SysConfigurationDAO sysConfigurationDAO;
    
    // 使用测试专用实现替代Mock
    private TestCacheOperateTemplate operateTemplate = new TestCacheOperateTemplate();
    private SysConfigurationHolderImpl sysConfigurationHolder;
    
    private SysConfigurationDO testConfigDO;
    
    @BeforeEach
    public void setUp() {
        super.setUp();
        
        // 手动创建并注入依赖
        sysConfigurationHolder = new SysConfigurationHolderImpl();
        try {
            java.lang.reflect.Field sysConfigurationDAOField = SysConfigurationHolderImpl.class.getDeclaredField("sysConfigurationDAO");
            sysConfigurationDAOField.setAccessible(true);
            sysConfigurationDAOField.set(sysConfigurationHolder, sysConfigurationDAO);
            
            java.lang.reflect.Field operateTemplateField = SysConfigurationHolderImpl.class.getDeclaredField("operateTemplate");
            operateTemplateField.setAccessible(true);
            operateTemplateField.set(sysConfigurationHolder, operateTemplate);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject dependencies", e);
        }
        
        // 创建测试数据
        testConfigDO = new SysConfigurationDO("TEST_KEY", "TEST_VALUE", "TEST_MEMO");
    }
    
    @Test
    @DisplayName("测试getConfiguration方法-找到配置")
    void testGetConfiguration_Found() {
        // Given
        String key = "TEST_KEY";
        Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(testConfigDO);
        
        // When
        SysConfiguration result = sysConfigurationHolder.getConfiguration(key);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAttrName()).isEqualTo("TEST_KEY");
        assertThat(result.getAttrValue()).isEqualTo("TEST_VALUE");
        assertThat(result.getMemo()).isEqualTo("TEST_MEMO");
        
        Mockito.verify(sysConfigurationDAO).loadByKey(key);
    }
    
    @Test
    @DisplayName("测试getConfiguration方法-未找到配置")
    void testGetConfiguration_NotFound() {
        // Given
        String key = "INVALID_KEY";
        Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(null);
        
        // When
        SysConfiguration result = sysConfigurationHolder.getConfiguration(key);
        
        // Then
        assertThat(result).isNull();
        Mockito.verify(sysConfigurationDAO).loadByKey(key);
    }
    
    @Test
    @DisplayName("测试loadConfigureOrDefault字符串方法-找到配置")
    void testLoadConfigureOrDefault_String_Found() {
        // Given
        String key = "TEST_KEY";
        String defaultValue = "DEFAULT_VALUE";
        Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(testConfigDO);
        
        // When
        String result = sysConfigurationHolder.loadConfigureOrDefault(key, defaultValue);
        
        // Then
        assertThat(result).isEqualTo("TEST_VALUE");
    }
    
    @Test
    @DisplayName("测试loadConfigureOrDefault字符串方法-未找到配置")
    void testLoadConfigureOrDefault_String_NotFound() {
        // Given
        String key = "INVALID_KEY";
        String defaultValue = "DEFAULT_VALUE";
        Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(null);
        
        // When
        String result = sysConfigurationHolder.loadConfigureOrDefault(key, defaultValue);
        
        // Then
        assertThat(result).isEqualTo("DEFAULT_VALUE");
    }
    
    @Test
    @DisplayName("测试loadConfigureOrDefault整型方法-有效数字")
    void testLoadConfigureOrDefault_Int_Valid() {
        // Given
        String key = "NUMERIC_KEY";
        int defaultValue = 100;
        SysConfigurationDO numericConfigDO = new SysConfigurationDO("NUMERIC_KEY", "200", "TEST_MEMO");
        Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(numericConfigDO);
        
        // When
        int result = sysConfigurationHolder.loadConfigureOrDefault(key, defaultValue);
        
        // Then
        assertThat(result).isEqualTo(200);
    }
    
    @Test
    @DisplayName("测试loadConfigureOrDefault整型方法-无效数字")
    void testLoadConfigureOrDefault_Int_Invalid() {
        // Given
        String key = "NON_NUMERIC_KEY";
        int defaultValue = 100;
        SysConfigurationDO nonNumericConfigDO = new SysConfigurationDO("NON_NUMERIC_KEY", "NOT_A_NUMBER", "TEST_MEMO");
        Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(nonNumericConfigDO);
        
        // When
        int result = sysConfigurationHolder.loadConfigureOrDefault(key, defaultValue);
        
        // Then
        assertThat(result).isEqualTo(100);
    }
    
    @Test
    @DisplayName("测试loadConfigureOrDefault整型方法-配置为null")
    void testLoadConfigureOrDefault_Int_ConfigNull() {
        // Given
        String key = "INVALID_KEY";
        int defaultValue = 100;
        Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(null);
        
        // When
        int result = sysConfigurationHolder.loadConfigureOrDefault(key, defaultValue);
        
        // Then
        assertThat(result).isEqualTo(100);
    }
} 