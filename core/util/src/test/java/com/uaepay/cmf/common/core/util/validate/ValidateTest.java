package com.uaepay.cmf.common.core.util.validate;

import com.uaepay.cmf.common.core.util.BaseCoreTest;
import com.uaepay.cmf.common.core.domain.exception.ValidateException;
import com.uaepay.common.util.money.Money;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * Validate类单元测试
 * 测试各种验证逻辑 - 使用真实Money对象替代Mock
 */
@ExtendWith(MockitoExtension.class)
class ValidateTest extends BaseCoreTest {
    
    // 使用真实Money对象替代Mock - 延迟初始化避免静态初始化问题
    private Money getPositiveAmount() {
        return new Money("100.00", "AED");
    }
    
    private Money getZeroAmount() {
        return new Money("0.00", "AED");
    }
    
    private Money getNegativeAmount() {
        return new Money("-50.00", "AED");
    }
    
    @Test
    @DisplayName("测试assertNotNull方法-成功")
    void testAssertNotNull_Success() {
        // Given
        String propertyName = "testField";
        String value = "value";
        
        // When & Then - 应该不抛异常
        assertThatCode(() -> Validate.assertNotNull(propertyName, value))
                .doesNotThrowAnyException();
    }
    
    @Test
    @DisplayName("测试assertNotNull方法-失败")
    void testAssertNotNull_Failure() {
        // Given
        String propertyName = "testField";
        Object value = null;
        
        // When & Then
        assertThatThrownBy(() -> Validate.assertNotNull(propertyName, value))
                .isInstanceOf(ValidateException.class)
                .hasMessage("testField不能为空");
    }
    
    @Test
    @DisplayName("测试assertNotBlank方法-成功")
    void testAssertNotBlank_Success() {
        // Given
        String propertyName = "testField";
        String value = "value";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank(value)).thenReturn(false);
            
            assertThatCode(() -> Validate.assertNotBlank(propertyName, value))
                    .doesNotThrowAnyException();
            
            stringUtilsMock.verify(() -> StringUtils.isBlank(value));
        }
    }
    
    @Test
    @DisplayName("测试assertNotBlank方法-失败")
    void testAssertNotBlank_Failure() {
        // Given
        String propertyName = "testField";
        String value = "";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank(value)).thenReturn(true);
            
            assertThatThrownBy(() -> Validate.assertNotBlank(propertyName, value))
                    .isInstanceOf(ValidateException.class)
                    .hasMessage("testField不能为空");
            
            stringUtilsMock.verify(() -> StringUtils.isBlank(value));
        }
    }
    
    @Test
    @DisplayName("测试assertNotEmpty方法-List成功")
    void testAssertNotEmpty_List_Success() {
        // Given
        String propertyName = "testField";
        List<String> value = Arrays.asList("item");
        
        // When & Then
        assertThatCode(() -> Validate.assertNotEmpty(propertyName, value))
                .doesNotThrowAnyException();
    }
    
    @Test
    @DisplayName("测试assertNotEmpty方法-List失败-空列表")
    void testAssertNotEmpty_List_Failure_Empty() {
        // Given
        String propertyName = "testField";
        List<String> value = new ArrayList<>();
        
        // When & Then
        assertThatThrownBy(() -> Validate.assertNotEmpty(propertyName, value))
                .isInstanceOf(ValidateException.class)
                .hasMessage("testField不能为空");
    }
    
    @Test
    @DisplayName("测试assertNotEmpty方法-List失败-null")
    void testAssertNotEmpty_List_Failure_Null() {
        // Given
        String propertyName = "testField";
        List<String> value = null;
        
        // When & Then
        assertThatThrownBy(() -> Validate.assertNotEmpty(propertyName, value))
                .isInstanceOf(ValidateException.class)
                .hasMessage("testField不能为空");
    }
    
    @Test
    @DisplayName("测试assertNotEmpty方法-Collection成功")
    void testAssertNotEmpty_Collection_Success() {
        // Given
        String propertyName = "testField";
        Collection<String> value = Arrays.asList("item");
        Validate validate = new Validate();
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(value)).thenReturn(false);
            
            assertThatCode(() -> validate.assertNotEmpty(propertyName, value))
                    .doesNotThrowAnyException();
            
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(value));
        }
    }
    
    @Test
    @DisplayName("测试assertNotEmpty方法-Collection失败")
    void testAssertNotEmpty_Collection_Failure() {
        // Given
        String propertyName = "testField";
        Collection<String> value = new ArrayList<>();
        Validate validate = new Validate();
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(value)).thenReturn(true);
            
            assertThatThrownBy(() -> validate.assertNotEmpty(propertyName, value))
                    .isInstanceOf(ValidateException.class)
                    .hasMessage("testField不能为空");
            
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(value));
        }
    }
    
    @Test
    @DisplayName("测试assertGreaterZero方法-成功")
    void testAssertGreaterZero_Success() {
        // Given
        String propertyName = "testField";
        
        // When & Then - 使用真实的正数Money对象
        assertThatCode(() -> Validate.assertGreaterZero(propertyName, getPositiveAmount()))
                .doesNotThrowAnyException();
    }
    
    @Test
    @DisplayName("测试assertGreaterZero方法-失败-null")
    void testAssertGreaterZero_Failure_Null() {
        // Given
        String propertyName = "testField";
        Money value = null;
        
        // When & Then
        assertThatThrownBy(() -> Validate.assertGreaterZero(propertyName, value))
                .isInstanceOf(ValidateException.class)
                .hasMessage("testField必须大于零");
    }
    
    @Test
    @DisplayName("测试assertGreaterZero方法-失败-等于零")
    void testAssertGreaterZero_Failure_Zero() {
        // Given
        String propertyName = "testField";
        
        // When & Then - 使用真实的零值Money对象
        assertThatThrownBy(() -> Validate.assertGreaterZero(propertyName, getZeroAmount()))
                .isInstanceOf(ValidateException.class)
                .hasMessage("testField必须大于零");
    }
    
    @Test
    @DisplayName("测试assertGreaterZero方法-失败-小于零")
    void testAssertGreaterZero_Failure_Negative() {
        // Given
        String propertyName = "testField";
        
        // When & Then - 使用真实的负数Money对象
        assertThatThrownBy(() -> Validate.assertGreaterZero(propertyName, getNegativeAmount()))
                .isInstanceOf(ValidateException.class)
                .hasMessage("testField必须大于零");
    }
    
    @Test
    @DisplayName("测试assertGreaterEqualZero方法-成功-大于零")
    void testAssertGreaterEqualZero_Success_Greater() {
        // Given
        String propertyName = "testField";
        
        // When & Then - 使用真实的正数Money对象
        assertThatCode(() -> Validate.assertGreaterEqualZero(propertyName, getPositiveAmount()))
                .doesNotThrowAnyException();
    }
    
    @Test
    @DisplayName("测试assertGreaterEqualZero方法-成功-等于零")
    void testAssertGreaterEqualZero_Success_Equal() {
        // Given
        String propertyName = "testField";
        
        // When & Then - 使用真实的零值Money对象
        assertThatCode(() -> Validate.assertGreaterEqualZero(propertyName, getZeroAmount()))
                .doesNotThrowAnyException();
    }
    
    @Test
    @DisplayName("测试assertGreaterEqualZero方法-失败-null")
    void testAssertGreaterEqualZero_Failure_Null() {
        // Given
        String propertyName = "testField";
        Money value = null;
        
        // When & Then
        assertThatThrownBy(() -> Validate.assertGreaterEqualZero(propertyName, value))
                .isInstanceOf(ValidateException.class)
                .hasMessage("testField必须大于等于零");
    }
    
    @Test
    @DisplayName("测试assertGreaterEqualZero方法-失败-小于零")
    void testAssertGreaterEqualZero_Failure_Negative() {
        // Given
        String propertyName = "testField";
        
        // When & Then - 使用真实的负数Money对象
        assertThatThrownBy(() -> Validate.assertGreaterEqualZero(propertyName, getNegativeAmount()))
                .isInstanceOf(ValidateException.class)
                .hasMessage("testField必须大于等于零");
    }
    
    @Test
    @DisplayName("测试assertTrue方法-成功")
    void testAssertTrue_Success() {
        // Given
        String condition = "condition";
        boolean value = true;
        
        // When & Then
        assertThatCode(() -> Validate.assertTrue(condition, value))
                .doesNotThrowAnyException();
    }
    
    @Test
    @DisplayName("测试assertTrue方法-失败")
    void testAssertTrue_Failure() {
        // Given
        String condition = "condition";
        boolean value = false;
        
        // When & Then
        assertThatThrownBy(() -> Validate.assertTrue(condition, value))
                .isInstanceOf(ValidateException.class)
                .hasMessage("condition");
    }
    
    @Test
    @DisplayName("测试assertNotBlankAndNoSpace方法-成功")
    void testAssertNotBlankAndNoSpace_Success() {
        // Given
        String propertyName = "testField";
        String value = "value";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank(value)).thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.contains(value, ' ')).thenReturn(false);
            
            assertThatCode(() -> Validate.assertNotBlankAndNoSpace(propertyName, value))
                    .doesNotThrowAnyException();
            
            stringUtilsMock.verify(() -> StringUtils.isBlank(value));
            stringUtilsMock.verify(() -> StringUtils.contains(value, ' '));
        }
    }
    
    @Test
    @DisplayName("测试assertNotBlankAndNoSpace方法-失败-空白")
    void testAssertNotBlankAndNoSpace_Failure_Blank() {
        // Given
        String propertyName = "testField";
        String value = "";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank(value)).thenReturn(true);
            
            assertThatThrownBy(() -> Validate.assertNotBlankAndNoSpace(propertyName, value))
                    .isInstanceOf(ValidateException.class)
                    .hasMessage("testField不能为空");
            
            stringUtilsMock.verify(() -> StringUtils.isBlank(value));
        }
    }
    
    @Test
    @DisplayName("测试assertNotBlankAndNoSpace方法-失败-包含空格")
    void testAssertNotBlankAndNoSpace_Failure_WithSpace() {
        // Given
        String propertyName = "testField";
        String value = "val ue";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank(value)).thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.contains(value, ' ')).thenReturn(true);
            
            assertThatThrownBy(() -> Validate.assertNotBlankAndNoSpace(propertyName, value))
                    .isInstanceOf(ValidateException.class)
                    .hasMessage("testField不能包含空格");
            
            stringUtilsMock.verify(() -> StringUtils.isBlank(value));
            stringUtilsMock.verify(() -> StringUtils.contains(value, ' '));
        }
    }
    
    @Test
    @DisplayName("测试assertEquals方法-成功")
    void testAssertEquals_Success() {
        // Given
        String propertyName = "testField";
        String expectValue = "expected";
        String value = "expected";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.equals(expectValue, value)).thenReturn(true);
            
            assertThatCode(() -> Validate.assertEquals(propertyName, expectValue, value))
                    .doesNotThrowAnyException();
            
            stringUtilsMock.verify(() -> StringUtils.equals(expectValue, value));
        }
    }
    
    @Test
    @DisplayName("测试assertEquals方法-失败")
    void testAssertEquals_Failure() {
        // Given
        String propertyName = "testField";
        String expectValue = "expected";
        String value = "actual";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.equals(expectValue, value)).thenReturn(false);
            
            assertThatThrownBy(() -> Validate.assertEquals(propertyName, expectValue, value))
                    .isInstanceOf(ValidateException.class)
                    .hasMessage("testField应该等于expected，实际为actual");
            
            stringUtilsMock.verify(() -> StringUtils.equals(expectValue, value));
        }
    }
    
    @Test
    @DisplayName("测试assertIn方法-成功")
    void testAssertIn_Success() {
        // Given
        String propertyName = "testField";
        String value = "value";
        String[] expectValues = {"value", "other"};
        
        // When & Then
        assertThatCode(() -> Validate.assertIn(propertyName, value, expectValues))
                .doesNotThrowAnyException();
    }
    
    @Test
    @DisplayName("测试assertIn方法-失败")
    void testAssertIn_Failure() {
        // Given
        String propertyName = "testField";
        String value = "value";
        String[] expectValues = {"other"};
        
        // When & Then
        assertThatThrownBy(() -> Validate.assertIn(propertyName, value, expectValues))
                .isInstanceOf(ValidateException.class)
                .hasMessage("testField校验不通过");
    }
    
    @Test
    @DisplayName("测试assertIn方法-期望值为null且值为null")
    void testAssertIn_BothNull() {
        // Given
        String propertyName = "testField";
        String value = null;
        String[] expectValues = null;
        
        // When & Then
        assertThatCode(() -> Validate.assertIn(propertyName, value, expectValues))
                .doesNotThrowAnyException();
    }
    
    @Test
    @DisplayName("测试assertIn方法-期望值为null但值不为null")
    void testAssertIn_ExpectNullButValueNotNull() {
        // Given
        String propertyName = "testField";
        String value = "value";
        String[] expectValues = null;
        
        // When & Then
        assertThatThrownBy(() -> Validate.assertIn(propertyName, value, expectValues))
                .isInstanceOf(ValidateException.class)
                .hasMessage("testField校验不通过");
    }
    
    @Test
    @DisplayName("测试assertIn方法-值为null但在期望值中")
    void testAssertIn_ValueNullInExpected() {
        // Given
        String propertyName = "testField";
        String value = null;
        String[] expectValues = {null, "other"};
        
        // When & Then
        assertThatCode(() -> Validate.assertIn(propertyName, value, expectValues))
                .doesNotThrowAnyException();
    }
} 