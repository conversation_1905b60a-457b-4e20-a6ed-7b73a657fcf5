package com.uaepay.cmf.common.core.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.assertj.core.api.Assertions.*;

/**
 * Core模块测试基类
 * 提供通用的测试常量和基础设施
 */
@ExtendWith(MockitoExtension.class)
public abstract class BaseCoreTest {
    
    // 测试常量
    protected static final String TEST_PAYMENT_SEQ_NO = "TEST_PAY_123456";
    protected static final String TEST_MEMBER_ID = "TEST_MEMBER_001";
    protected static final String TEST_INST_CODE = "TEST_INST_001";
    
    // 测试用扩展信息
    protected static final String TEST_EXTENSION_KEY = "testKey";
    protected static final String TEST_EXTENSION_VALUE = "testValue";
    
    @BeforeEach
    public void setUp() {
        // 通用setup逻辑
    }
} 