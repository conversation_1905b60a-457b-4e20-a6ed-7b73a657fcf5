package com.uaepay.cmf.common.core.util.form.impl;

import com.uaepay.cmf.common.core.util.BaseCoreTest;
import com.uaepay.cmf.common.core.domain.config.SysConfiguration;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigureKey;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * BankFormUtilImpl类单元测试
 * 测试表单构建功能
 */
@ExtendWith(MockitoExtension.class)
class BankFormUtilImplTest extends BaseCoreTest {
    
    @Mock
    private SysConfigurationHolder sysConfigurationHolder;
    
    @InjectMocks
    private BankFormUtilImpl bankFormUtil;
    
    private ChannelFundResult testFundResult;
    
    @BeforeEach
    public void setUp() {
        super.setUp();
        
        // 创建测试用的ChannelFundResult对象
        testFundResult = new ChannelFundResult();
        testFundResult.setInstUrl("https://test.bank.com/pay");
        testFundResult.setInstOrderNo("TEST_ORDER_123");
    }
    
    @Test
    @DisplayName("测试buildSignForm方法-不需要form表单")
    void testBuildSignForm_NoFormNeeded() {
        // Given
        Map<String, String> extMap = new HashMap<>();
        extMap.put(ExtensionKey.FC_CODE.key, "NO_FORM_CHANNEL");
        String extensionJson = "{\"fcCode\":\"NO_FORM_CHANNEL\"}";
        testFundResult.setExtension(extensionJson);
        
        SysConfiguration config = new SysConfiguration();
        config.setAttrValue("NO_FORM_CHANNEL,OTHER_CHANNEL");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<MapUtil> mapUtilMock = Mockito.mockStatic(MapUtil.class)) {
            
            Mockito.when(sysConfigurationHolder.getConfiguration(SysConfigureKey.SIGN_FORM_ONLY_URL))
                    .thenReturn(config);
            
            stringUtilsMock.when(() -> StringUtils.isEmpty("NO_FORM_CHANNEL,OTHER_CHANNEL")).thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.isEmpty("NO_FORM_CHANNEL")).thenReturn(false);
            
            mapUtilMock.when(() -> MapUtil.jsonToMap(extensionJson)).thenReturn(extMap);
            
            String result = bankFormUtil.buildSignForm(testFundResult);
            
            assertThat(result).isEqualTo("https://test.bank.com/pay");
        }
    }
    
    @Test
    @DisplayName("测试buildSignForm方法-包含PAGE_URL_FOR_SIGN")
    void testBuildSignForm_WithPageUrl() {
        // Given
        String pageUrl = "https://custom.page.com/sign";
        Map<String, String> extMap = new HashMap<>();
        extMap.put(ExtensionKey.PAGE_URL_FOR_SIGN.key, pageUrl);
        String extensionJson = "{\"pageUrlForSign\":\"" + pageUrl + "\"}";
        testFundResult.setExtension(extensionJson);
        
        SysConfiguration config = new SysConfiguration();
        config.setAttrValue("");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<MapUtil> mapUtilMock = Mockito.mockStatic(MapUtil.class)) {
            
            Mockito.when(sysConfigurationHolder.getConfiguration(SysConfigureKey.SIGN_FORM_ONLY_URL))
                    .thenReturn(config);
            
            stringUtilsMock.when(() -> StringUtils.isNotBlank(extensionJson)).thenReturn(true);
            stringUtilsMock.when(() -> StringUtils.isEmpty("")).thenReturn(true);
            
            mapUtilMock.when(() -> MapUtil.jsonToMap(extensionJson)).thenReturn(extMap);
            
            String result = bankFormUtil.buildSignForm(testFundResult);
            
            assertThat(result).isEqualTo(pageUrl);
        }
    }
    
    @Test
    @DisplayName("测试buildSignForm方法-正常form表单")
    void testBuildSignForm_NormalForm() {
        // Given
        Map<String, String> extMap = new HashMap<>();
        extMap.put("merchantId", "TEST_MERCHANT");
        extMap.put("amount", "100.00");
        extMap.put("currency", "AED");
        String extensionJson = "{\"merchantId\":\"TEST_MERCHANT\",\"amount\":\"100.00\",\"currency\":\"AED\"}";
        testFundResult.setExtension(extensionJson);
        
        SysConfiguration config = new SysConfiguration();
        config.setAttrValue("");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<MapUtil> mapUtilMock = Mockito.mockStatic(MapUtil.class)) {
            
            Mockito.when(sysConfigurationHolder.getConfiguration(SysConfigureKey.SIGN_FORM_ONLY_URL))
                    .thenReturn(config);
            
            stringUtilsMock.when(() -> StringUtils.isNotBlank(extensionJson)).thenReturn(true);
            stringUtilsMock.when(() -> StringUtils.isEmpty("")).thenReturn(true);
            
            mapUtilMock.when(() -> MapUtil.jsonToMap(extensionJson)).thenReturn(extMap);
            
            String result = bankFormUtil.buildSignForm(testFundResult);
            
            assertThat(result).contains("<form id='frmBankID' name='frmBankName' method='post'");
            assertThat(result).contains("action='https://test.bank.com/pay'");
            assertThat(result).contains("name='merchantId'  value='TEST_MERCHANT'");
            assertThat(result).contains("name='amount'  value='100.00'");
            assertThat(result).contains("name='currency'  value='AED'");
            assertThat(result).contains("</form>");
        }
    }
    
    @Test
    @DisplayName("测试buildSignForm方法-包含ENCTYPE")
    void testBuildSignForm_WithEnctype() {
        // Given
        Map<String, String> extMap = new HashMap<>();
        extMap.put("enctype", "multipart/form-data");
        extMap.put("merchantId", "TEST_MERCHANT");
        String extensionJson = "{\"enctype\":\"multipart/form-data\",\"merchantId\":\"TEST_MERCHANT\"}";
        testFundResult.setExtension(extensionJson);
        
        SysConfiguration config = new SysConfiguration();
        config.setAttrValue("");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<MapUtil> mapUtilMock = Mockito.mockStatic(MapUtil.class)) {
            
            Mockito.when(sysConfigurationHolder.getConfiguration(SysConfigureKey.SIGN_FORM_ONLY_URL))
                    .thenReturn(config);
            
            stringUtilsMock.when(() -> StringUtils.isNotBlank(extensionJson)).thenReturn(true);
            stringUtilsMock.when(() -> StringUtils.isEmpty("")).thenReturn(true);
            
            mapUtilMock.when(() -> MapUtil.jsonToMap(extensionJson)).thenReturn(extMap);
            
            String result = bankFormUtil.buildSignForm(testFundResult);
            
            assertThat(result).contains(" enctype=multipart/form-data");
            assertThat(result).contains("name='merchantId'  value='TEST_MERCHANT'");
            // ENCTYPE字段本身不应该出现在input中（被过滤）
            assertThat(result).doesNotContain("name='enctype'");
        }
    }
    
    @Test
    @DisplayName("测试buildSignForm方法-过滤特定字段")
    void testBuildSignForm_FilterKeys() {
        // Given
        Map<String, String> extMap = new HashMap<>();
        extMap.put(ExtensionKey.FC_CODE.key, "FILTERED_FIELD");
        extMap.put(ExtensionKey.INST_AMOUNT.key, "FILTERED_FIELD");
        extMap.put(ExtensionKey.INST_ORDER_NO.key, "FILTERED_FIELD");
        extMap.put(ExtensionKey.PARTNER_ID.key, "FILTERED_FIELD");
        extMap.put(ExtensionKey.EBANK_CHARSET.key, "FILTERED_FIELD");
        extMap.put("normalField", "NORMAL_VALUE");
        String extensionJson = "{\"normalField\":\"NORMAL_VALUE\"}";
        testFundResult.setExtension(extensionJson);
        
        SysConfiguration config = new SysConfiguration();
        config.setAttrValue("");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<MapUtil> mapUtilMock = Mockito.mockStatic(MapUtil.class)) {
            
            Mockito.when(sysConfigurationHolder.getConfiguration(SysConfigureKey.SIGN_FORM_ONLY_URL))
                    .thenReturn(config);
            
            stringUtilsMock.when(() -> StringUtils.isNotBlank(extensionJson)).thenReturn(true);
            stringUtilsMock.when(() -> StringUtils.isEmpty("")).thenReturn(true);
            
            mapUtilMock.when(() -> MapUtil.jsonToMap(extensionJson)).thenReturn(extMap);
            
            String result = bankFormUtil.buildSignForm(testFundResult);
            
            // 过滤字段不应该出现在form中
            assertThat(result).doesNotContain("name='" + ExtensionKey.FC_CODE.key + "'");
            assertThat(result).doesNotContain("name='" + ExtensionKey.INST_AMOUNT.key + "'");
            assertThat(result).doesNotContain("name='" + ExtensionKey.INST_ORDER_NO.key + "'");
            assertThat(result).doesNotContain("name='" + ExtensionKey.PARTNER_ID.key + "'");
            assertThat(result).doesNotContain("name='" + ExtensionKey.EBANK_CHARSET.key + "'");
            
            // 正常字段应该出现在form中
            assertThat(result).contains("name='normalField'  value='NORMAL_VALUE'");
        }
    }
    
    @Test
    @DisplayName("测试buildSignForm方法-extension为空")
    void testBuildSignForm_EmptyExtension() {
        // Given
        testFundResult.setExtension("");
        
        SysConfiguration config = new SysConfiguration();
        config.setAttrValue("");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            
            Mockito.when(sysConfigurationHolder.getConfiguration(SysConfigureKey.SIGN_FORM_ONLY_URL))
                    .thenReturn(config);
            
            stringUtilsMock.when(() -> StringUtils.isNotBlank("")).thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.isEmpty("")).thenReturn(true);
            
            String result = bankFormUtil.buildSignForm(testFundResult);
            
            assertThat(result).contains("<form id='frmBankID' name='frmBankName' method='post'");
            assertThat(result).contains("action='https://test.bank.com/pay'");
            assertThat(result).contains("</form>");
            // 没有input字段
            assertThat(result).doesNotContain("<input");
        }
    }
    
    @Test
    @DisplayName("测试buildSignForm方法-config为null")
    void testBuildSignForm_ConfigNull() {
        // Given
        Map<String, String> extMap = new HashMap<>();
        extMap.put("testField", "testValue");
        String extensionJson = "{\"testField\":\"testValue\"}";
        testFundResult.setExtension(extensionJson);
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<MapUtil> mapUtilMock = Mockito.mockStatic(MapUtil.class)) {
            
            Mockito.when(sysConfigurationHolder.getConfiguration(SysConfigureKey.SIGN_FORM_ONLY_URL))
                    .thenReturn(null);
            
            stringUtilsMock.when(() -> StringUtils.isNotBlank(extensionJson)).thenReturn(true);
            
            mapUtilMock.when(() -> MapUtil.jsonToMap(extensionJson)).thenReturn(extMap);
            
            String result = bankFormUtil.buildSignForm(testFundResult);
            
            // config为null时，需要form表单
            assertThat(result).contains("<form id='frmBankID' name='frmBankName' method='post'");
            assertThat(result).contains("name='testField'  value='testValue'");
        }
    }
    
    @Test
    @DisplayName("测试buildSignForm方法-渠道不在配置列表中")
    void testBuildSignForm_ChannelNotInConfig() {
        // Given
        Map<String, String> extMap = new HashMap<>();
        extMap.put(ExtensionKey.FC_CODE.key, "OTHER_CHANNEL");
        extMap.put("testField", "testValue");
        String extensionJson = "{\"fcCode\":\"OTHER_CHANNEL\",\"testField\":\"testValue\"}";
        testFundResult.setExtension(extensionJson);
        
        SysConfiguration config = new SysConfiguration();
        config.setAttrValue("CHANNEL_A,CHANNEL_B");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<MapUtil> mapUtilMock = Mockito.mockStatic(MapUtil.class)) {
            
            Mockito.when(sysConfigurationHolder.getConfiguration(SysConfigureKey.SIGN_FORM_ONLY_URL))
                    .thenReturn(config);
            
            stringUtilsMock.when(() -> StringUtils.isNotBlank(extensionJson)).thenReturn(true);
            stringUtilsMock.when(() -> StringUtils.isEmpty("CHANNEL_A,CHANNEL_B")).thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.isEmpty("OTHER_CHANNEL")).thenReturn(false);
            
            mapUtilMock.when(() -> MapUtil.jsonToMap(extensionJson)).thenReturn(extMap);
            
            String result = bankFormUtil.buildSignForm(testFundResult);
            
            // 渠道不在配置列表中，需要form表单
            assertThat(result).contains("<form id='frmBankID' name='frmBankName' method='post'");
            assertThat(result).contains("name='testField'  value='testValue'");
        }
    }
} 