package com.uaepay.cmf.common.core.util.sysconfig;

import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.engine.cache.CacheOperateTemplate;
import com.uaepay.cmf.common.core.engine.cache.DataLoader;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试专用的CacheOperateTemplate实现
 * 避免Mock复杂类的问题
 */
public class TestCacheOperateTemplate extends CacheOperateTemplate {
    
    private final Map<String, Object> cache = new HashMap<>();
    
    @Override
    public Object load(CacheType cacheType, String key, DataLoader<?> dataLoader) {
        String cacheKey = cacheType.getCode() + ":" + key;
        
        Object cachedValue = cache.get(cacheKey);
        
        if (cachedValue != null) {
            return cachedValue;
        }
        
        // 模拟从DataLoader加载数据
        Object loadedValue = dataLoader.load();
        if (loadedValue != null) {
            cache.put(cacheKey, loadedValue);
        }
        
        return loadedValue;
    }
    
    /**
     * 用于测试的方法：设置缓存值
     */
    public void setCacheValue(CacheType cacheType, String key, Object value) {
        String cacheKey = cacheType.getCode() + ":" + key;
        cache.put(cacheKey, value);
    }
    
    /**
     * 用于测试的方法：清空缓存
     */
    public void clearCache() {
        cache.clear();
    }
    
    /**
     * 用于测试的方法：移除特定缓存
     */
    public void removeCache(CacheType cacheType, String key) {
        String cacheKey = cacheType.getCode() + ":" + key;
        cache.remove(cacheKey);
    }
} 