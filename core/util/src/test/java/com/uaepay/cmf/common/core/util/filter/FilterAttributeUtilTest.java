package com.uaepay.cmf.common.core.util.filter;

import com.uaepay.cmf.common.core.util.BaseCoreTest;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.ChannelInfoExtKey;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.CompanyOrPersonal;
import com.uaepay.schema.cmf.enums.AccessChannel;
import com.uaepay.common.util.money.Money;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * FilterAttributeUtil类单元测试
 * 测试过滤属性工具功能
 */
@ExtendWith(MockitoExtension.class)
class FilterAttributeUtilTest extends BaseCoreTest {
    
    private CmfOrder testCmfOrder;
    private InstControlOrder testInstControlOrder;
    
    @BeforeEach
    public void setUp() {
        super.setUp();
        
        // 创建测试用的CmfOrder对象
        testCmfOrder = new CmfOrder();
        testCmfOrder.setPaymentSeqNo("TEST_PAY_123456");
        testCmfOrder.setMemberId("TEST_MEMBER_001");
        testCmfOrder.setAmount(new Money("100.00", "AED"));
        testCmfOrder.setInstCode("INST_001");
        testCmfOrder.setGmtCreate(new Date());
        
        // 设置扩展信息
        Map<String, String> cmfExtension = new HashMap<>();
        cmfExtension.put(ExtensionKey.DBCR.key, "D");
        cmfExtension.put(ExtensionKey.CARD_TYPE.key, "VISA");
        cmfExtension.put(ExtensionKey.COMPANY_OR_PERSONAL.key, CompanyOrPersonal.PERSONAL.getCode());
        cmfExtension.put(ExtensionKey.ACCESS_CHANNEL.key, AccessChannel.WEB.getCode());
        cmfExtension.put(ExtensionKey.PRODUCT_CODE.key, "PRODUCT_001");
        cmfExtension.put("customKey", "customValue");
        testCmfOrder.setExtension(cmfExtension);
        
        // 创建测试用的InstControlOrder对象
        testInstControlOrder = new InstControlOrder();
        testInstControlOrder.setRequestNo("INST_REQ_123");
        testInstControlOrder.setInstCode("INST_002");
        testInstControlOrder.setAmount(new Money("200.00", "AED"));
        testInstControlOrder.setGmtCreate(new Date());
        
        Map<String, String> instExtension = new HashMap<>();
        instExtension.put(ExtensionKey.DBCR.key, "C");
        instExtension.put(ExtensionKey.CARD_TYPE.key, "MASTERCARD");
        instExtension.put("instParam1", "value1");
        instExtension.put("instParam2", "value2");
        testInstControlOrder.setExtension(instExtension);
    }
    
    @Test
    @DisplayName("测试convert方法-CmfOrder入款订单")
    void testConvert_CmfOrder_Fundin() {
        // Given
        testCmfOrder.setBizType(BizType.FUNDIN);
        
        // When
        Map<String, Object> result;
        try (MockedStatic<LogFilterUtil> logFilterMock = Mockito.mockStatic(LogFilterUtil.class)) {
            logFilterMock.when(() -> LogFilterUtil.filter(any(Map.class))).thenReturn("filtered_data");
            result = (Map<String, Object>) FilterAttributeUtil.convert(testCmfOrder);
        }
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).containsEntry(ChannelInfoExtKey.DBCR.getCode(), "D");
        assertThat(result).containsEntry(ChannelInfoExtKey.BIZ_TYPE.getCode(), BizType.FUNDIN);
        assertThat(result).containsEntry(ChannelInfoExtKey.TARGET_INST.getCode(), "INST_001");
        assertThat(result).containsEntry(ChannelInfoExtKey.AMOUNT.getCode(), "100.00");
        assertThat(result).containsEntry(ChannelInfoExtKey.COMPANY_OR_PERSONAL.getCode(), CompanyOrPersonal.PERSONAL.getCode());
        assertThat(result).containsEntry(ChannelInfoExtKey.ACCESS_CHANNELS.getCode(), AccessChannel.WEB.getCode());
        assertThat(result).containsEntry(ChannelInfoExtKey.PRODUCT_CODE.getCode(), "PRODUCT_001");
        assertThat(result).containsEntry("customKey", "customValue");
        
        // CARD_TYPE不应该包含在入款订单中
        assertThat(result).doesNotContainKey(ChannelInfoExtKey.CARD_TYPE.getCode());
    }
    
    @Test
    @DisplayName("测试convert方法-CmfOrder出款订单")
    void testConvert_CmfOrder_Fundout() {
        // Given
        testCmfOrder.setBizType(BizType.FUNDOUT);
        
        // When
        Map<String, Object> result;
        try (MockedStatic<LogFilterUtil> logFilterMock = Mockito.mockStatic(LogFilterUtil.class)) {
            logFilterMock.when(() -> LogFilterUtil.filter(any(Map.class))).thenReturn("filtered_data");
            result = (Map<String, Object>) FilterAttributeUtil.convert(testCmfOrder);
        }
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).containsEntry(ChannelInfoExtKey.CARD_TYPE.getCode(), "VISA");
        assertThat(result).containsEntry(ChannelInfoExtKey.BIZ_TYPE.getCode(), BizType.FUNDOUT);
        assertThat(result).containsEntry(ChannelInfoExtKey.TARGET_INST.getCode(), "INST_001");
        
        // DBCR不应该包含在出款订单中（因为bizType不是FUNDIN）
        assertThat(result).doesNotContainKey(ChannelInfoExtKey.DBCR.getCode());
    }
    
    @Test
    @DisplayName("测试convert方法-CmfOrder金额为null")
    void testConvert_CmfOrder_NullAmount() {
        // Given
        testCmfOrder.setBizType(BizType.FUNDIN);
        testCmfOrder.setAmount(null);
        
        // When
        Map<String, Object> result;
        try (MockedStatic<LogFilterUtil> logFilterMock = Mockito.mockStatic(LogFilterUtil.class)) {
            logFilterMock.when(() -> LogFilterUtil.filter(any(Map.class))).thenReturn("filtered_data");
            result = (Map<String, Object>) FilterAttributeUtil.convert(testCmfOrder);
        }
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).containsEntry(ChannelInfoExtKey.AMOUNT.getCode(), "0.00");
    }
    
    @Test
    @DisplayName("测试convert方法-CmfOrder金额Amount为null")
    void testConvert_CmfOrder_NullAmountValue() {
        // Given
        testCmfOrder.setBizType(BizType.FUNDIN);
        Money mockMoney = Mockito.mock(Money.class);
        Mockito.when(mockMoney.getAmount()).thenReturn(null);
        testCmfOrder.setAmount(mockMoney);
        
        // When
        Map<String, Object> result;
        try (MockedStatic<LogFilterUtil> logFilterMock = Mockito.mockStatic(LogFilterUtil.class)) {
            logFilterMock.when(() -> LogFilterUtil.filter(any(Map.class))).thenReturn("filtered_data");
            result = (Map<String, Object>) FilterAttributeUtil.convert(testCmfOrder);
        }
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).containsEntry(ChannelInfoExtKey.AMOUNT.getCode(), "0.00");
    }
    
    @Test
    @DisplayName("测试convert方法-CmfOrder默认值处理")
    void testConvert_CmfOrder_DefaultValues() {
        // Given
        testCmfOrder.setBizType(BizType.FUNDIN);
        Map<String, String> extension = new HashMap<>();
        extension.put(ExtensionKey.DBCR.key, "D");
        // 不设置COMPANY_OR_PERSONAL和ACCESS_CHANNEL，测试默认值
        testCmfOrder.setExtension(extension);
        
        // When
        Map<String, Object> result;
        try (MockedStatic<LogFilterUtil> logFilterMock = Mockito.mockStatic(LogFilterUtil.class)) {
            logFilterMock.when(() -> LogFilterUtil.filter(any(Map.class))).thenReturn("filtered_data");
            result = (Map<String, Object>) FilterAttributeUtil.convert(testCmfOrder);
        }
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).containsEntry(ChannelInfoExtKey.COMPANY_OR_PERSONAL.getCode(), CompanyOrPersonal.PERSONAL.getCode());
        assertThat(result).containsEntry(ChannelInfoExtKey.ACCESS_CHANNELS.getCode(), AccessChannel.WEB.getCode());
    }
    
    @Test
    @DisplayName("测试convert方法-CmfOrder空字符串处理")
    void testConvert_CmfOrder_EmptyStringValues() {
        // Given
        testCmfOrder.setBizType(BizType.FUNDIN);
        Map<String, String> extension = new HashMap<>();
        extension.put(ExtensionKey.DBCR.key, "");  // 空字符串
        extension.put(ExtensionKey.COMPANY_OR_PERSONAL.key, "");  // 空字符串
        extension.put(ExtensionKey.ACCESS_CHANNEL.key, "");  // 空字符串
        testCmfOrder.setExtension(extension);
        
        // When
        Map<String, Object> result;
        try (MockedStatic<LogFilterUtil> logFilterMock = Mockito.mockStatic(LogFilterUtil.class)) {
            logFilterMock.when(() -> LogFilterUtil.filter(any(Map.class))).thenReturn("filtered_data");
            result = (Map<String, Object>) FilterAttributeUtil.convert(testCmfOrder);
        }
        
        // Then
        assertThat(result).isNotNull();
        // 空字符串的DBCR不应该被添加
        assertThat(result).doesNotContainKey(ChannelInfoExtKey.DBCR.getCode());
        // 空字符串的情况应该使用默认值
        assertThat(result).containsEntry(ChannelInfoExtKey.COMPANY_OR_PERSONAL.getCode(), CompanyOrPersonal.PERSONAL.getCode());
        assertThat(result).containsEntry(ChannelInfoExtKey.ACCESS_CHANNELS.getCode(), AccessChannel.WEB.getCode());
    }
    
    @Test
    @DisplayName("测试convert方法-InstControlOrder")
    void testConvert_InstControlOrder() {
        // When
        Map<String, Object> result;
        try (MockedStatic<LogFilterUtil> logFilterMock = Mockito.mockStatic(LogFilterUtil.class)) {
            logFilterMock.when(() -> LogFilterUtil.filter(any(Map.class))).thenReturn("filtered_data");
            result = (Map<String, Object>) FilterAttributeUtil.convert(testInstControlOrder);
        }
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).containsEntry(ChannelInfoExtKey.TARGET_INST.getCode(), "INST_002");
        assertThat(result).containsEntry(ChannelInfoExtKey.DBCR.getCode(), "C");
        assertThat(result).containsEntry(ChannelInfoExtKey.CARD_TYPE.getCode(), "MASTERCARD");
        assertThat(result).containsEntry(ChannelInfoExtKey.AMOUNT.getCode(), "200.00");
        assertThat(result).containsEntry(ChannelInfoExtKey.COMPANY_OR_PERSONAL.getCode(), CompanyOrPersonal.PERSONAL.getCode());
        assertThat(result).containsEntry(ChannelInfoExtKey.ACCESS_CHANNELS.getCode(), AccessChannel.WEB.getCode());
        assertThat(result).containsEntry("instParam1", "value1");
        assertThat(result).containsEntry("instParam2", "value2");
    }
    
    @Test
    @DisplayName("测试convert方法-InstControlOrder金额为null")
    void testConvert_InstControlOrder_NullAmount() {
        // Given
        testInstControlOrder.setAmount(null);
        
        // When
        Map<String, Object> result;
        try (MockedStatic<LogFilterUtil> logFilterMock = Mockito.mockStatic(LogFilterUtil.class)) {
            logFilterMock.when(() -> LogFilterUtil.filter(any(Map.class))).thenReturn("filtered_data");
            result = (Map<String, Object>) FilterAttributeUtil.convert(testInstControlOrder);
        }
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).containsEntry(ChannelInfoExtKey.AMOUNT.getCode(), "0.00");
    }
    
    @Test
    @DisplayName("测试convert方法-InstControlOrder默认值处理")
    void testConvert_InstControlOrder_DefaultValues() {
        // Given
        Map<String, String> extension = new HashMap<>();
        extension.put(ExtensionKey.DBCR.key, "C");
        // 不设置COMPANY_OR_PERSONAL和ACCESS_CHANNEL，测试默认值
        testInstControlOrder.setExtension(extension);
        
        // When
        Map<String, Object> result;
        try (MockedStatic<LogFilterUtil> logFilterMock = Mockito.mockStatic(LogFilterUtil.class)) {
            logFilterMock.when(() -> LogFilterUtil.filter(any(Map.class))).thenReturn("filtered_data");
            result = (Map<String, Object>) FilterAttributeUtil.convert(testInstControlOrder);
        }
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).containsEntry(ChannelInfoExtKey.COMPANY_OR_PERSONAL.getCode(), CompanyOrPersonal.PERSONAL.getCode());
        assertThat(result).containsEntry(ChannelInfoExtKey.ACCESS_CHANNELS.getCode(), AccessChannel.WEB.getCode());
    }
    
    @Test
    @DisplayName("测试combine方法")
    void testCombine() {
        // When
        java.util.List<String> result = FilterAttributeUtil.combine("key1", "key2", "key3");
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(3);
        assertThat(result).containsExactly("key1", "key2", "key3");
    }
    
    @Test
    @DisplayName("测试combine方法-空参数")
    void testCombine_EmptyArgs() {
        // When
        java.util.List<String> result = FilterAttributeUtil.combine();
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
    }
    
    @Test
    @DisplayName("测试日志记录功能-CmfOrder")
    void testLogging_CmfOrder() {
        // Given
        testCmfOrder.setBizType(BizType.FUNDIN);
        
        // When & Then
        try (MockedStatic<LogFilterUtil> logFilterMock = Mockito.mockStatic(LogFilterUtil.class);
             MockedStatic<LoggerFactory> loggerFactoryMock = Mockito.mockStatic(LoggerFactory.class)) {
            
            Logger mockLogger = Mockito.mock(Logger.class);
            loggerFactoryMock.when(() -> LoggerFactory.getLogger(FilterAttributeUtil.class)).thenReturn(mockLogger);
            logFilterMock.when(() -> LogFilterUtil.filter(any(Map.class))).thenReturn("filtered_log_data");
            
            FilterAttributeUtil.convert(testCmfOrder);
            
            // 验证日志方法被调用
            Mockito.verify(mockLogger).info(anyString(), anyString(), anyString());
            logFilterMock.verify(() -> LogFilterUtil.filter(any(Map.class)));
        }
    }
    
    @Test
    @DisplayName("测试日志记录功能-InstControlOrder")
    void testLogging_InstControlOrder() {
        // When & Then
        try (MockedStatic<LogFilterUtil> logFilterMock = Mockito.mockStatic(LogFilterUtil.class);
             MockedStatic<LoggerFactory> loggerFactoryMock = Mockito.mockStatic(LoggerFactory.class)) {
            
            Logger mockLogger = Mockito.mock(Logger.class);
            loggerFactoryMock.when(() -> LoggerFactory.getLogger(FilterAttributeUtil.class)).thenReturn(mockLogger);
            logFilterMock.when(() -> LogFilterUtil.filter(any(Map.class))).thenReturn("filtered_log_data");
            
            FilterAttributeUtil.convert(testInstControlOrder);
            
            // 验证日志方法被调用
            Mockito.verify(mockLogger).info(anyString(), anyString(), anyString());
            logFilterMock.verify(() -> LogFilterUtil.filter(any(Map.class)));
        }
    }
} 