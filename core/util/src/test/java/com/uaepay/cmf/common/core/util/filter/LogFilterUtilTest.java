package com.uaepay.cmf.common.core.util.filter;

import com.uaepay.cmf.common.core.util.BaseCoreTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * LogFilterUtil类单元测试
 * 测试日志脱敏功能
 */
@ExtendWith(MockitoExtension.class)
class LogFilterUtilTest extends BaseCoreTest {
    
    @Test
    @DisplayName("测试filter方法-输入为null")
    void testFilter_Null() {
        // Given
        String log = null;
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).isNull();
    }
    
    @Test
    @DisplayName("测试filter方法-卡号脱敏")
    void testFilter_CardNo() {
        // Given
        String log = "cardNo=****************,other=value";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).contains("cardNo=************3456");
        assertThat(result).contains("other=value");
    }
    
    @Test
    @DisplayName("测试filter方法-卡号长度不足4位")
    void testFilter_CardNo_ShortLength() {
        // Given
        String log = "cardNo=123,other=value";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).contains("cardNo=***");
        assertThat(result).contains("other=value");
    }
    
    @Test
    @DisplayName("测试filter方法-姓名脱敏")
    void testFilter_Name() {
        // Given
        String log = "name=张三,other=value";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).contains("name=**");
        assertThat(result).contains("other=value");
    }
    
    @Test
    @DisplayName("测试filter方法-身份证号脱敏")
    void testFilter_IdNo() {
        // Given
        String log = "idNo=****************78,other=value";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).contains("idNo=******************");
        assertThat(result).contains("other=value");
    }
    
    @Test
    @DisplayName("测试filter方法-手机号脱敏")
    void testFilter_MobileNo() {
        // Given
        String log = "mobileNo=***********,other=value";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).contains("mobileNo=***********");
        assertThat(result).contains("other=value");
    }
    
    @Test
    @DisplayName("测试filter方法-银行账户名脱敏")
    void testFilter_BankAccountName() {
        // Given
        String log = "bankAccountName=测试账户,other=value";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).contains("bankAccountName=****");
        assertThat(result).contains("other=value");
    }
    
    @Test
    @DisplayName("测试filter方法-有效期脱敏")
    void testFilter_ValidDate() {
        // Given
        String log = "validDate=1225,other=value";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).contains("validDate=****");
        assertThat(result).contains("other=value");
    }
    
    @Test
    @DisplayName("测试filter方法-多个敏感字段")
    void testFilter_Multiple() {
        // Given
        String log = "cardNo=****************,name=张三,idNo=****************78,other=value";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).contains("cardNo=************3456");
        assertThat(result).contains("name=**");
        assertThat(result).contains("idNo=******************");
        assertThat(result).contains("other=value");
    }
    
    @Test
    @DisplayName("测试filter方法-无敏感字段")
    void testFilter_NoSensitiveData() {
        // Given
        String log = "normalField=normalValue,other=anotherValue";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).isEqualTo(log);
    }
    
    @Test
    @DisplayName("测试filter方法-字段在末尾无逗号")
    void testFilter_FieldAtEnd() {
        // Given
        String log = "other=value,cardNo=****************";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).contains("cardNo=************3456");
        assertThat(result).contains("other=value");
    }
    
    @Test
    @DisplayName("测试filter方法-Map对象-正常情况")
    void testFilter_Map() {
        // Given
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("cardNo", "****************");
        dataMap.put("other", "value");
        
        // When
        String result = LogFilterUtil.filter(dataMap);
        
        // Then
        assertThat(result).isNotNull();
        // 由于依赖Map的toString()实现，我们主要验证方法被调用且不返回null
    }
    
    @Test
    @DisplayName("测试filter方法-Map为空")
    void testFilter_Map_Empty() {
        // Given & When & Then
        try (MockedStatic<MapUtils> mapUtilsMock = Mockito.mockStatic(MapUtils.class)) {
            Map<String, Object> dataMap = new HashMap<>();
            mapUtilsMock.when(() -> MapUtils.isEmpty(dataMap)).thenReturn(true);
            
            String result = LogFilterUtil.filter(dataMap);
            
            assertThat(result).isNull();
            mapUtilsMock.verify(() -> MapUtils.isEmpty(dataMap));
        }
    }
    
    @Test
    @DisplayName("测试filter方法-Map为null")
    void testFilter_Map_Null() {
        // Given & When & Then
        try (MockedStatic<MapUtils> mapUtilsMock = Mockito.mockStatic(MapUtils.class)) {
            Map<String, Object> dataMap = null;
            mapUtilsMock.when(() -> MapUtils.isEmpty(dataMap)).thenReturn(true);
            
            String result = LogFilterUtil.filter(dataMap);
            
            assertThat(result).isNull();
            mapUtilsMock.verify(() -> MapUtils.isEmpty(dataMap));
        }
    }
    
    @Test
    @DisplayName("测试边界值-空字符串")
    void testFilter_EmptyString() {
        // Given
        String log = "";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).isEmpty();
    }
    
    @Test
    @DisplayName("测试边界值-只包含敏感字段名无值")
    void testFilter_OnlyFieldName() {
        // Given
        String log = "cardNo=";
        
        // When
        String result = LogFilterUtil.filter(log);
        
        // Then
        assertThat(result).isEqualTo("cardNo=");
    }
} 