package com.uaepay.cmf.common.core.util.filter;

import com.uaepay.cmf.common.core.util.BaseCoreTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import static org.assertj.core.api.Assertions.*;

/**
 * Util类单元测试
 * 按照测试计划实现95%覆盖率目标
 */
class UtilTest extends BaseCoreTest {
    
    private final Util util = new Util();
    
    @Test
    @DisplayName("测试match方法-两参数都为null")
    void testMatch_BothNull() {
        // Given
        String[] more = null;
        String[] less = null;
        
        // When
        boolean result = util.match(more, less);
        
        // Then
        assertThat(result).isTrue();
    }
    
    @Test
    @DisplayName("测试match方法-第一个参数为null")
    void testMatch_FirstNull() {
        // Given
        String[] more = null;
        String[] less = {"a"};
        
        // When
        boolean result = util.match(more, less);
        
        // Then
        assertThat(result).isFalse();
    }
    
    @Test
    @DisplayName("测试match方法-第二个参数为null")
    void testMatch_SecondNull() {
        // Given
        String[] more = {"a"};
        String[] less = null;
        
        // When
        boolean result = util.match(more, less);
        
        // Then
        assertThat(result).isFalse();
    }
    
    @Test
    @DisplayName("测试match方法-正常匹配成功")
    void testMatch_Success() {
        // Given
        String[] more = {"a", "b", "c"};
        String[] less = {"a", "b"};
        
        // When
        boolean result = util.match(more, less);
        
        // Then
        assertThat(result).isTrue();
    }
    
    @Test
    @DisplayName("测试match方法-匹配失败")
    void testMatch_Failure() {
        // Given
        String[] more = {"a", "b"};
        String[] less = {"a", "c"};
        
        // When
        boolean result = util.match(more, less);
        
        // Then
        assertThat(result).isFalse();
    }
    
    @Test
    @DisplayName("测试in方法-元素为null")
    void testIn_Null() {
        // Given
        String element = null;
        String[] set = {"a"};
        
        // When
        boolean result = util.in(element, set);
        
        // Then
        assertThat(result).isFalse();
    }
    
    @Test
    @DisplayName("测试in方法-数组为null")
    void testIn_NullArray() {
        // Given
        String element = "a";
        String[] set = null;
        
        // When
        boolean result = util.in(element, set);
        
        // Then
        assertThat(result).isFalse();
    }
    
    @Test
    @DisplayName("测试in方法-找到元素")
    void testIn_Found() {
        // Given
        String element = "a";
        String[] set = {"a", "b"};
        
        // When
        boolean result = util.in(element, set);
        
        // Then
        assertThat(result).isTrue();
    }
    
    @Test
    @DisplayName("测试in方法-未找到元素")
    void testIn_NotFound() {
        // Given
        String element = "c";
        String[] set = {"a", "b"};
        
        // When
        boolean result = util.in(element, set);
        
        // Then
        assertThat(result).isFalse();
    }
    
    @Test
    @DisplayName("测试contains方法")
    void testContains() {
        // Given
        String[] set = {"a", "b"};
        String value = "a";
        
        // When
        boolean result = util.contains(set, value);
        
        // Then
        assertThat(result).isTrue();
    }
    
    @Test
    @DisplayName("测试is方法-value为null")
    void testIs_Null() {
        // Given
        String[] set = {"a", "b"};
        String value = null;
        
        // When
        boolean result = util.is(set, value);
        
        // Then
        assertThat(result).isFalse();
    }
    
    @Test
    @DisplayName("测试is方法-成功匹配")
    void testIs_Success() {
        // Given
        String[] set = {"a", "b"};
        String value = "a,b";
        
        // When
        boolean result = util.is(set, value);
        
        // Then
        assertThat(result).isTrue();
    }
    
    @Test
    @DisplayName("测试is方法-匹配失败")
    void testIs_Failure() {
        // Given
        String[] set = {"a", "b"};
        String value = "a,c";
        
        // When
        boolean result = util.is(set, value);
        
        // Then
        assertThat(result).isFalse();
    }
    
    @ParameterizedTest
    @DisplayName("测试is方法-参数化测试")
    @CsvSource({
        "'a,b', 'a,b', true",
        "'a,b', 'a,c', false",
        "'a', 'a', true",
        ", 'a', false"
    })
    void testIs_Parameterized(String setValue, String testValue, boolean expected) {
        // Given
        String[] set = setValue != null ? setValue.split(",") : null;
        
        // When
        boolean result = util.is(set, testValue);
        
        // Then
        assertThat(result).isEqualTo(expected);
    }
    
    @Test
    @DisplayName("测试边界值-空数组")
    void testEdgeCase_EmptyArray() {
        // Given
        String[] more = {};
        String[] less = {};
        
        // When
        boolean result = util.match(more, less);
        
        // Then
        assertThat(result).isTrue();
    }
    
    @Test
    @DisplayName("测试边界值-单元素数组")
    void testEdgeCase_SingleElement() {
        // Given
        String[] more = {"a"};
        String[] less = {"a"};
        
        // When
        boolean result = util.match(more, less);
        
        // Then
        assertThat(result).isTrue();
    }
} 