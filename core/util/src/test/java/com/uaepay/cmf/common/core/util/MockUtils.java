package com.uaepay.cmf.common.core.util;

import org.mockito.Mockito;
import org.slf4j.Logger;
import com.uaepay.common.util.money.Money;
import java.math.BigDecimal;

/**
 * Mock工具类
 * 提供通用的Mock方法
 */
public class MockUtils {
    
    /**
     * 创建Mock Money对象
     */
    public static Money createMockMoney(String amount) {
        Money money = Mockito.mock(Money.class);
        Mockito.when(money.getAmount()).thenReturn(new BigDecimal(amount));
        return money;
    }
    
    /**
     * 创建Mock Logger对象
     */
    public static Logger createMockLogger() {
        return Mockito.mock(Logger.class);
    }
} 