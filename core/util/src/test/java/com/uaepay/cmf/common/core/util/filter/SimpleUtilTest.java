package com.uaepay.cmf.common.core.util.filter;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import static org.assertj.core.api.Assertions.*;

/**
 * Util类简化单元测试 - 验证基本功能
 * 不使用Mockito，专注验证测试环境
 */
class SimpleUtilTest {
    
    private final Util util = new Util();
    
    @Test
    @DisplayName("测试match方法-两参数都为null")
    void testMatch_BothNull() {
        // Given
        String[] more = null;
        String[] less = null;
        
        // When
        boolean result = util.match(more, less);
        
        // Then
        assertThat(result).isTrue();
    }
    
    @Test
    @DisplayName("测试match方法-正常匹配成功")
    void testMatch_Success() {
        // Given
        String[] more = {"a", "b", "c"};
        String[] less = {"a", "b"};
        
        // When
        boolean result = util.match(more, less);
        
        // Then
        assertThat(result).isTrue();
    }
    
    @Test
    @DisplayName("测试in方法-找到元素")
    void testIn_Found() {
        // Given
        String value = "test";
        String[] array = {"a", "test", "b"};
        
        // When
        boolean result = util.in(value, array);
        
        // Then
        assertThat(result).isTrue();
    }
    
    @Test
    @DisplayName("测试in方法-未找到元素")
    void testIn_NotFound() {
        // Given
        String value = "notfound";
        String[] array = {"a", "test", "b"};
        
        // When
        boolean result = util.in(value, array);
        
        // Then
        assertThat(result).isFalse();
    }
    
    @Test
    @DisplayName("测试is方法-成功匹配")
    void testIs_Success() {
        // Given
        String[] set = {"a", "b"};
        String value = "a,b";
        
        // When
        boolean result = util.is(set, value);
        
        // Then
        assertThat(result).isTrue();
    }
} 