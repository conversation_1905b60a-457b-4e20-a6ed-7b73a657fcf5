package com.uaepay.cmf.common.core.util.sysconfig;

import com.uaepay.cmf.common.core.util.BaseCoreTest;
import com.uaepay.cmf.common.core.dal.daointerface.SysConfigurationDAO;
import com.uaepay.cmf.common.core.dal.dataobject.SysConfigurationDO;
import com.uaepay.cmf.common.core.domain.config.SysConfiguration;
import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.engine.cache.CacheOperateTemplate;
import com.uaepay.cmf.common.core.engine.cache.DataLoader;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * SysConfigurationHolderImpl类单元测试
 * 测试系统配置管理功能
 */
@ExtendWith(MockitoExtension.class)
class SysConfigurationHolderImplTest extends BaseCoreTest {
    
    @Mock
    private SysConfigurationDAO sysConfigurationDAO;
    
    // 使用测试专用实现替代Mock，避免CacheOperateTemplate Mock问题
    private TestCacheOperateTemplate operateTemplate = new TestCacheOperateTemplate();
    
    private SysConfigurationHolderImpl sysConfigurationHolder;
    
    private SysConfigurationDO testConfigDO;
    private SysConfiguration testConfig;
    
    @BeforeEach
    public void setUp() {
        super.setUp();
        
        // 手动创建SysConfigurationHolderImpl并注入依赖
        sysConfigurationHolder = new SysConfigurationHolderImpl();
        
        // 使用反射设置私有字段（简化测试）
        try {
            java.lang.reflect.Field sysConfigurationDAOField = SysConfigurationHolderImpl.class.getDeclaredField("sysConfigurationDAO");
            sysConfigurationDAOField.setAccessible(true);
            sysConfigurationDAOField.set(sysConfigurationHolder, sysConfigurationDAO);
            
            java.lang.reflect.Field operateTemplateField = SysConfigurationHolderImpl.class.getDeclaredField("operateTemplate");
            operateTemplateField.setAccessible(true);
            operateTemplateField.set(sysConfigurationHolder, operateTemplate);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject dependencies", e);
        }
        
        // 创建测试用的配置对象
        testConfigDO = new SysConfigurationDO("TEST_KEY", "TEST_VALUE", "TEST_MEMO");
        testConfig = new SysConfiguration("TEST_KEY", "TEST_VALUE", "TEST_MEMO");
    }
    
    @Test
    @DisplayName("测试getConfiguration方法-找到配置")
    void testGetConfiguration_Found() {
        // Given
        String key = "TEST_KEY";
        
        // Mock DAO返回测试数据
        Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(testConfigDO);
        
        // When
        SysConfiguration result = sysConfigurationHolder.getConfiguration(key);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAttrName()).isEqualTo("TEST_KEY");
        assertThat(result.getAttrValue()).isEqualTo("TEST_VALUE");
        assertThat(result.getMemo()).isEqualTo("TEST_MEMO");
        
        // 验证DAO被调用
        Mockito.verify(sysConfigurationDAO).loadByKey(key);
    }
    
    @Test
    @DisplayName("测试getConfiguration方法-未找到配置")
    void testGetConfiguration_NotFound() {
        // Given
        String key = "INVALID_KEY";
        
        Mockito.when(operateTemplate.load(
                eq(CacheType.CMF_SYS_CONFIGURATION), 
                eq(key), 
                any(DataLoader.class)
        )).thenReturn(null);
        
        // When
        SysConfiguration result = sysConfigurationHolder.getConfiguration(key);
        
        // Then
        assertThat(result).isNull();
        
        Mockito.verify(operateTemplate).load(
                eq(CacheType.CMF_SYS_CONFIGURATION), 
                eq(key), 
                any(DataLoader.class)
        );
    }
    
    @Test
    @DisplayName("测试loadConfigureOrDefault字符串方法-找到配置")
    void testLoadConfigureOrDefault_String_Found() {
        // Given
        String key = "TEST_KEY";
        String defaultValue = "DEFAULT_VALUE";
        
        Mockito.when(operateTemplate.load(
                eq(CacheType.CMF_SYS_CONFIGURATION), 
                eq(key), 
                any(DataLoader.class)
        )).thenReturn(testConfig);
        
        // When
        String result = sysConfigurationHolder.loadConfigureOrDefault(key, defaultValue);
        
        // Then
        assertThat(result).isEqualTo("TEST_VALUE");
    }
    
    @Test
    @DisplayName("测试loadConfigureOrDefault字符串方法-未找到配置")
    void testLoadConfigureOrDefault_String_NotFound() {
        // Given
        String key = "INVALID_KEY";
        String defaultValue = "DEFAULT_VALUE";
        
        Mockito.when(operateTemplate.load(
                eq(CacheType.CMF_SYS_CONFIGURATION), 
                eq(key), 
                any(DataLoader.class)
        )).thenReturn(null);
        
        // When
        String result = sysConfigurationHolder.loadConfigureOrDefault(key, defaultValue);
        
        // Then
        assertThat(result).isEqualTo("DEFAULT_VALUE");
    }
    
    @Test
    @DisplayName("测试loadConfigureOrDefault整型方法-有效数字")
    void testLoadConfigureOrDefault_Int_Valid() {
        // Given
        String key = "TEST_KEY";
        int defaultValue = 100;
        SysConfiguration numericConfig = new SysConfiguration("TEST_KEY", "200", "TEST_MEMO");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            Mockito.when(operateTemplate.load(
                    eq(CacheType.CMF_SYS_CONFIGURATION), 
                    eq(key), 
                    any(DataLoader.class)
            )).thenReturn(numericConfig);
            
            stringUtilsMock.when(() -> StringUtils.isNumeric("200")).thenReturn(true);
            
            int result = sysConfigurationHolder.loadConfigureOrDefault(key, defaultValue);
            
            assertThat(result).isEqualTo(200);
            stringUtilsMock.verify(() -> StringUtils.isNumeric("200"));
        }
    }
    
    @Test
    @DisplayName("测试loadConfigureOrDefault整型方法-无效数字")
    void testLoadConfigureOrDefault_Int_Invalid() {
        // Given
        String key = "TEST_KEY";
        int defaultValue = 100;
        SysConfiguration nonNumericConfig = new SysConfiguration("TEST_KEY", "NOT_A_NUMBER", "TEST_MEMO");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            Mockito.when(operateTemplate.load(
                    eq(CacheType.CMF_SYS_CONFIGURATION), 
                    eq(key), 
                    any(DataLoader.class)
            )).thenReturn(nonNumericConfig);
            
            stringUtilsMock.when(() -> StringUtils.isNumeric("NOT_A_NUMBER")).thenReturn(false);
            
            int result = sysConfigurationHolder.loadConfigureOrDefault(key, defaultValue);
            
            assertThat(result).isEqualTo(100);
            stringUtilsMock.verify(() -> StringUtils.isNumeric("NOT_A_NUMBER"));
        }
    }
    
    @Test
    @DisplayName("测试loadConfigureOrDefault整型方法-配置为null")
    void testLoadConfigureOrDefault_Int_ConfigNull() {
        // Given
        String key = "INVALID_KEY";
        int defaultValue = 100;
        
        Mockito.when(operateTemplate.load(
                eq(CacheType.CMF_SYS_CONFIGURATION), 
                eq(key), 
                any(DataLoader.class)
        )).thenReturn(null);
        
        // When
        int result = sysConfigurationHolder.loadConfigureOrDefault(key, defaultValue);
        
        // Then
        assertThat(result).isEqualTo(100);
    }
    
    @Test
    @DisplayName("测试update方法-值相同不更新")
    void testUpdate_ValueSame_NoUpdate() {
        // Given
        String key = "TEST_KEY";
        String value = "SAME_VALUE";
        SysConfigurationDO configDO = new SysConfigurationDO(key, value, "memo");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(configDO);
            stringUtilsMock.when(() -> StringUtils.equals(value, value)).thenReturn(true);
            
            SysConfiguration result = sysConfigurationHolder.update(key, value);
            
            assertThat(result.getAttrValue()).isEqualTo(value);
            Mockito.verify(sysConfigurationDAO, Mockito.never()).update(any());
            Mockito.verify(sysConfigurationDAO).loadByKey(key);
            stringUtilsMock.verify(() -> StringUtils.equals(value, value));
        }
    }
    
    @Test
    @DisplayName("测试update方法-值不同需要更新")
    void testUpdate_ValueDifferent_Update() {
        // Given
        String key = "TEST_KEY";
        String oldValue = "OLD_VALUE";
        String newValue = "NEW_VALUE";
        SysConfigurationDO configDO = new SysConfigurationDO(key, oldValue, "memo");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<CompletableFuture> completableFutureMock = Mockito.mockStatic(CompletableFuture.class)) {
            
            Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(configDO);
            stringUtilsMock.when(() -> StringUtils.equals(oldValue, newValue)).thenReturn(false);
            
            // Mock异步刷新缓存
            CompletableFuture<Void> mockFuture = Mockito.mock(CompletableFuture.class);
            completableFutureMock.when(() -> CompletableFuture.supplyAsync(any())).thenReturn(mockFuture);
            
            SysConfiguration result = sysConfigurationHolder.update(key, newValue);
            
            assertThat(result.getAttrValue()).isEqualTo(newValue);
            
            Mockito.verify(sysConfigurationDAO).loadByKey(key);
            Mockito.verify(sysConfigurationDAO).update(configDO);
            assertThat(configDO.getAttrValue()).isEqualTo(newValue);
            
            stringUtilsMock.verify(() -> StringUtils.equals(oldValue, newValue));
            completableFutureMock.verify(() -> CompletableFuture.supplyAsync(any()));
        }
    }
    
    @Test
    @DisplayName("测试refreshCache方法-异步刷新")
    void testRefreshCache_AsyncOperation() {
        // Given & When & Then
        try (MockedStatic<CompletableFuture> completableFutureMock = Mockito.mockStatic(CompletableFuture.class)) {
            CompletableFuture<Void> mockFuture = Mockito.mock(CompletableFuture.class);
            completableFutureMock.when(() -> CompletableFuture.supplyAsync(any())).thenReturn(mockFuture);
            
            sysConfigurationHolder.refreshCache();
            
            completableFutureMock.verify(() -> CompletableFuture.supplyAsync(any()));
        }
    }
    
    @Test
    @DisplayName("测试loadAll方法")
    void testLoadAll() {
        // Given
        List<SysConfiguration> configList = Arrays.asList(testConfig);
        
        Mockito.when(operateTemplate.load(
                eq(CacheType.CMF_SYS_CONFIGURATION),
                eq(SysConfigureKey.SYS_CONFIGURATION_CACHE_ALL),
                any(DataLoader.class)
        )).thenReturn(configList);
        
        // When
        List<SysConfiguration> result = sysConfigurationHolder.loadAll();
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getAttrName()).isEqualTo("TEST_KEY");
        
        Mockito.verify(operateTemplate).load(
                eq(CacheType.CMF_SYS_CONFIGURATION),
                eq(SysConfigureKey.SYS_CONFIGURATION_CACHE_ALL),
                any(DataLoader.class)
        );
    }
    
    @Test
    @DisplayName("测试insert方法-新配置")
    void testInsert_New() {
        // Given
        String key = "NEW_KEY";
        String value = "NEW_VALUE";
        String memo = "NEW_MEMO";
        
        // When & Then
        try (MockedStatic<CompletableFuture> completableFutureMock = Mockito.mockStatic(CompletableFuture.class)) {
            Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(null);
            
            CompletableFuture<Void> mockFuture = Mockito.mock(CompletableFuture.class);
            completableFutureMock.when(() -> CompletableFuture.supplyAsync(any())).thenReturn(mockFuture);
            
            Boolean result = sysConfigurationHolder.insert(key, value, memo);
            
            assertThat(result).isTrue();
            
            Mockito.verify(sysConfigurationDAO).loadByKey(key);
            Mockito.verify(sysConfigurationDAO).insert(any(SysConfigurationDO.class));
            completableFutureMock.verify(() -> CompletableFuture.supplyAsync(any()));
        }
    }
    
    @Test
    @DisplayName("测试insert方法-已存在配置且值相同")
    void testInsert_Existing_SameValue() {
        // Given
        String key = "EXISTING_KEY";
        String value = "SAME_VALUE";
        String memo = "NEW_MEMO";
        SysConfigurationDO existingConfig = new SysConfigurationDO(key, value, "OLD_MEMO");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(existingConfig);
            stringUtilsMock.when(() -> StringUtils.equals(value, value)).thenReturn(true);
            
            Boolean result = sysConfigurationHolder.insert(key, value, memo);
            
            assertThat(result).isTrue();
            
            Mockito.verify(sysConfigurationDAO).loadByKey(key);
            Mockito.verify(sysConfigurationDAO, Mockito.never()).insert(any());
            Mockito.verify(sysConfigurationDAO, Mockito.never()).update(any());
            
            stringUtilsMock.verify(() -> StringUtils.equals(value, value));
        }
    }
    
    @Test
    @DisplayName("测试insert方法-已存在配置且值不同")
    void testInsert_Existing_DifferentValue() {
        // Given
        String key = "EXISTING_KEY";
        String oldValue = "OLD_VALUE";
        String newValue = "NEW_VALUE";
        String memo = "NEW_MEMO";
        SysConfigurationDO existingConfig = new SysConfigurationDO(key, oldValue, "OLD_MEMO");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<CompletableFuture> completableFutureMock = Mockito.mockStatic(CompletableFuture.class)) {
            
            Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(existingConfig);
            stringUtilsMock.when(() -> StringUtils.equals(oldValue, newValue)).thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.isNotBlank(memo)).thenReturn(true);
            
            CompletableFuture<Void> mockFuture = Mockito.mock(CompletableFuture.class);
            completableFutureMock.when(() -> CompletableFuture.supplyAsync(any())).thenReturn(mockFuture);
            
            Boolean result = sysConfigurationHolder.insert(key, newValue, memo);
            
            assertThat(result).isTrue();
            
            Mockito.verify(sysConfigurationDAO).loadByKey(key);
            Mockito.verify(sysConfigurationDAO).update(existingConfig);
            assertThat(existingConfig.getAttrValue()).isEqualTo(newValue);
            assertThat(existingConfig.getMemo()).isEqualTo(memo);
            
            stringUtilsMock.verify(() -> StringUtils.equals(oldValue, newValue));
            stringUtilsMock.verify(() -> StringUtils.isNotBlank(memo));
            completableFutureMock.verify(() -> CompletableFuture.supplyAsync(any()));
        }
    }
    
    @Test
    @DisplayName("测试insert方法-memo为空使用原有memo")
    void testInsert_EmptyMemo_UseOriginalMemo() {
        // Given
        String key = "EXISTING_KEY";
        String oldValue = "OLD_VALUE";
        String newValue = "NEW_VALUE";
        String memo = "";
        String originalMemo = "ORIGINAL_MEMO";
        SysConfigurationDO existingConfig = new SysConfigurationDO(key, oldValue, originalMemo);
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<CompletableFuture> completableFutureMock = Mockito.mockStatic(CompletableFuture.class)) {
            
            Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(existingConfig);
            stringUtilsMock.when(() -> StringUtils.equals(oldValue, newValue)).thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.isNotBlank(memo)).thenReturn(false);
            
            CompletableFuture<Void> mockFuture = Mockito.mock(CompletableFuture.class);
            completableFutureMock.when(() -> CompletableFuture.supplyAsync(any())).thenReturn(mockFuture);
            
            Boolean result = sysConfigurationHolder.insert(key, newValue, memo);
            
            assertThat(result).isTrue();
            assertThat(existingConfig.getMemo()).isEqualTo(originalMemo);
            
            stringUtilsMock.verify(() -> StringUtils.isNotBlank(memo));
        }
    }
    
    @Test
    @DisplayName("测试convert方法-正常转换")
    void testConvert_Success() {
        // 通过getConfiguration方法间接测试convert方法
        // Given
        String key = "TEST_KEY";
        
        Mockito.when(operateTemplate.load(
                eq(CacheType.CMF_SYS_CONFIGURATION), 
                eq(key), 
                any(DataLoader.class)
        )).thenAnswer(invocation -> {
            // 模拟DataLoader的调用
            DataLoader<SysConfiguration> loader = invocation.getArgument(2);
            return loader.load(); // 这会调用convert方法
        });
        
        Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(testConfigDO);
        
        // When
        SysConfiguration result = sysConfigurationHolder.getConfiguration(key);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAttrName()).isEqualTo(testConfigDO.getAttrName());
        assertThat(result.getAttrValue()).isEqualTo(testConfigDO.getAttrValue());
        assertThat(result.getMemo()).isEqualTo(testConfigDO.getMemo());
    }
    
    @Test
    @DisplayName("测试convert方法-DO为null")
    void testConvert_DONull() {
        // 通过getConfiguration方法间接测试convert方法
        // Given
        String key = "TEST_KEY";
        
        Mockito.when(operateTemplate.load(
                eq(CacheType.CMF_SYS_CONFIGURATION), 
                eq(key), 
                any(DataLoader.class)
        )).thenAnswer(invocation -> {
            DataLoader<SysConfiguration> loader = invocation.getArgument(2);
            return loader.load();
        });
        
        Mockito.when(sysConfigurationDAO.loadByKey(key)).thenReturn(null);
        
        // When
        SysConfiguration result = sysConfigurationHolder.getConfiguration(key);
        
        // Then
        assertThat(result).isNull();
    }
    
    @Test
    @DisplayName("测试loadConfigurations方法-正常加载")
    void testLoadConfigurations_Success() {
        // 通过loadAll方法间接测试loadConfigurations方法
        // Given
        List<SysConfigurationDO> doList = Arrays.asList(testConfigDO);
        
        Mockito.when(operateTemplate.load(
                eq(CacheType.CMF_SYS_CONFIGURATION),
                eq(SysConfigureKey.SYS_CONFIGURATION_CACHE_ALL),
                any(DataLoader.class)
        )).thenAnswer(invocation -> {
            DataLoader<List<SysConfiguration>> loader = invocation.getArgument(2);
            return loader.load(); // 这会调用loadConfigurations方法
        });
        
        Mockito.when(sysConfigurationDAO.loadAll()).thenReturn(doList);
        
        // When
        List<SysConfiguration> result = sysConfigurationHolder.loadAll();
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getAttrName()).isEqualTo(testConfigDO.getAttrName());
    }
    
    @Test
    @DisplayName("测试loadConfigurations方法-DAO返回null")
    void testLoadConfigurations_DAOReturnsNull() {
        // 通过loadAll方法间接测试loadConfigurations方法
        // Given
        Mockito.when(operateTemplate.load(
                eq(CacheType.CMF_SYS_CONFIGURATION),
                eq(SysConfigureKey.SYS_CONFIGURATION_CACHE_ALL),
                any(DataLoader.class)
        )).thenAnswer(invocation -> {
            DataLoader<List<SysConfiguration>> loader = invocation.getArgument(2);
            return loader.load();
        });
        
        Mockito.when(sysConfigurationDAO.loadAll()).thenReturn(null);
        
        // When
        List<SysConfiguration> result = sysConfigurationHolder.loadAll();
        
        // Then
        assertThat(result).isNull();
    }
    
    /**
     * 测试专用的CacheOperateTemplate实现
     * 避免Mock复杂的泛型和Spring框架类
     */
    private static class TestCacheOperateTemplate extends CacheOperateTemplate {
        private final Map<String, Object> cache = new HashMap<>();
        
        @Override
        @SuppressWarnings("unchecked")
        public Object load(CacheType cacheType, String key, DataLoader<?> loader) {
            String cacheKey = cacheType.getCode() + ":" + key;
            
            if (cache.containsKey(cacheKey)) {
                return cache.get(cacheKey);
            }
            
            // 如果缓存中没有，调用DataLoader加载
            Object data = loader.load();
            if (data != null) {
                cache.put(cacheKey, data);
            }
            return data;
        }
        
        public void putData(CacheType cacheType, String key, Object data) {
            String cacheKey = cacheType.getCode() + ":" + key;
            cache.put(cacheKey, data);
        }
        
        public void clear() {
            cache.clear();
        }
    }
} 