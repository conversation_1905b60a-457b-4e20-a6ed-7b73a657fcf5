package com.uaepay.cmf.common.core.util.biz;

import com.uaepay.cmf.common.core.util.BaseCoreTest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * MapUtil类单元测试
 * 测试JSON/Map转换功能
 */
@ExtendWith(MockitoExtension.class)
class MapUtilTest extends BaseCoreTest {
    
    @Test
    @DisplayName("测试jsonToMap方法-有效JSON")
    void testJsonToMap_Valid() {
        // Given
        String json = "{\"key\":\"value\"}";
        Map<String, String> expectedMap = new HashMap<>();
        expectedMap.put("key", "value");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class)) {
            
            stringUtilsMock.when(() -> StringUtils.isBlank(json)).thenReturn(false);
            jsonMock.when(() -> JSON.parseObject(eq(json), any(TypeReference.class)))
                    .thenReturn(expectedMap);
            
            Map<String, String> result = MapUtil.jsonToMap(json);
            
            assertThat(result).isEqualTo(expectedMap);
            stringUtilsMock.verify(() -> StringUtils.isBlank(json));
            jsonMock.verify(() -> JSON.parseObject(eq(json), any(TypeReference.class)));
        }
    }
    
    @Test
    @DisplayName("测试jsonToMap方法-空白字符串")
    void testJsonToMap_Blank() {
        // Given
        String json = "";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank(json)).thenReturn(true);
            
            Map<String, String> result = MapUtil.jsonToMap(json);
            
            assertThat(result).isNotNull().isEmpty();
            stringUtilsMock.verify(() -> StringUtils.isBlank(json));
        }
    }
    
    @Test
    @DisplayName("测试jsonToMap方法-null输入")
    void testJsonToMap_Null() {
        // Given
        String json = null;
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank(json)).thenReturn(true);
            
            Map<String, String> result = MapUtil.jsonToMap(json);
            
            assertThat(result).isNotNull().isEmpty();
            stringUtilsMock.verify(() -> StringUtils.isBlank(json));
        }
    }
    
    @Test
    @DisplayName("测试safeJsonToMap方法-正常解析")
    void testSafeJsonToMap_Success() {
        // Given
        String json = "{\"key\":\"value\"}";
        Map<String, String> expectedMap = new HashMap<>();
        expectedMap.put("key", "value");
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class)) {
            
            stringUtilsMock.when(() -> StringUtils.isBlank(json)).thenReturn(false);
            jsonMock.when(() -> JSON.parseObject(eq(json), any(TypeReference.class)))
                    .thenReturn(expectedMap);
            
            Map<String, String> result = MapUtil.safeJsonToMap(json);
            
            assertThat(result).isEqualTo(expectedMap);
        }
    }
    
    @Test
    @DisplayName("测试safeJsonToMap方法-异常返回空Map")
    void testSafeJsonToMap_Exception() {
        // Given
        String json = "invalid json";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class)) {
            
            stringUtilsMock.when(() -> StringUtils.isBlank(json)).thenReturn(false);
            jsonMock.when(() -> JSON.parseObject(eq(json), any(TypeReference.class)))
                    .thenThrow(new RuntimeException("Parse error"));
            
            Map<String, String> result = MapUtil.safeJsonToMap(json);
            
            assertThat(result).isNotNull().isEmpty();
        }
    }
    
    @Test
    @DisplayName("测试mapToJson方法-有效Map")
    void testMapToJson_Valid() {
        // Given
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        String expectedJson = "{\"key\":\"value\"}";
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class);
             MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class)) {
            
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(map)).thenReturn(false);
            jsonMock.when(() -> JSON.toJSONString(eq(map), any())).thenReturn(expectedJson);
            
            String result = MapUtil.mapToJson(map);
            
            assertThat(result).isEqualTo(expectedJson);
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(map));
            jsonMock.verify(() -> JSON.toJSONString(eq(map), any()));
        }
    }
    
    @Test
    @DisplayName("测试mapToJson方法-空Map")
    void testMapToJson_Empty() {
        // Given
        Map<String, String> map = new HashMap<>();
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(map)).thenReturn(true);
            
            String result = MapUtil.mapToJson(map);
            
            assertThat(result).isNull();
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(map));
        }
    }
    
    @Test
    @DisplayName("测试mapToJson方法-null Map")
    void testMapToJson_Null() {
        // Given
        Map<String, String> map = null;
        
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(map)).thenReturn(true);
            
            String result = MapUtil.mapToJson(map);
            
            assertThat(result).isNull();
            collectionUtilsMock.verify(() -> CollectionUtils.isEmpty(map));
        }
    }
    
    @Test
    @DisplayName("测试addValue方法-新增键值")
    void testAddValue_NewKey() {
        // Given
        String source = "{\"old\":\"value\"}";
        String newKey = "new";
        String newValue = "newValue";
        Map<String, String> sourceMap = new HashMap<>();
        sourceMap.put("old", "value");
        Map<String, String> resultMap = new HashMap<>(sourceMap);
        resultMap.put(newKey, newValue);
        String expectedJson = "{\"old\":\"value\",\"new\":\"newValue\"}";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            
            // Mock jsonToMap
            stringUtilsMock.when(() -> StringUtils.isBlank(source)).thenReturn(false);
            jsonMock.when(() -> JSON.parseObject(eq(source), any(TypeReference.class)))
                    .thenReturn(sourceMap);
            
            // Mock mapToJson
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(any(Map.class))).thenReturn(false);
            jsonMock.when(() -> JSON.toJSONString(eq(resultMap), any())).thenReturn(expectedJson);
            
            String result = MapUtil.addValue(source, newKey, newValue);
            
            assertThat(result).isEqualTo(expectedJson);
        }
    }
    
    @Test
    @DisplayName("测试addValue方法-添加Map")
    void testAddValue_Map() {
        // Given
        String source = "{\"old\":\"value\"}";
        Map<String, String> addMap = new HashMap<>();
        addMap.put("new1", "value1");
        addMap.put("new2", "value2");
        
        Map<String, String> sourceMap = new HashMap<>();
        sourceMap.put("old", "value");
        Map<String, String> resultMap = new HashMap<>(sourceMap);
        resultMap.putAll(addMap);
        String expectedJson = "{\"old\":\"value\",\"new1\":\"value1\",\"new2\":\"value2\"}";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            
            // Mock jsonToMap
            stringUtilsMock.when(() -> StringUtils.isBlank(source)).thenReturn(false);
            jsonMock.when(() -> JSON.parseObject(eq(source), any(TypeReference.class)))
                    .thenReturn(sourceMap);
            
            // Mock isEmpty for addMap
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(addMap)).thenReturn(false);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(resultMap)).thenReturn(false);
            
            // Mock mapToJson
            jsonMock.when(() -> JSON.toJSONString(eq(resultMap), any())).thenReturn(expectedJson);
            
            String result = MapUtil.addValue(source, addMap);
            
            assertThat(result).isEqualTo(expectedJson);
        }
    }
    
    @Test
    @DisplayName("测试addValue方法-添加空Map")
    void testAddValue_EmptyMap() {
        // Given
        String source = "{\"old\":\"value\"}";
        Map<String, String> addMap = new HashMap<>();
        
        Map<String, String> sourceMap = new HashMap<>();
        sourceMap.put("old", "value");
        String expectedJson = "{\"old\":\"value\"}";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            
            // Mock jsonToMap
            stringUtilsMock.when(() -> StringUtils.isBlank(source)).thenReturn(false);
            jsonMock.when(() -> JSON.parseObject(eq(source), any(TypeReference.class)))
                    .thenReturn(sourceMap);
            
            // Mock isEmpty for addMap
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(addMap)).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(sourceMap)).thenReturn(false);
            
            // Mock mapToJson
            jsonMock.when(() -> JSON.toJSONString(eq(sourceMap), any())).thenReturn(expectedJson);
            
            String result = MapUtil.addValue(source, addMap);
            
            assertThat(result).isEqualTo(expectedJson);
        }
    }
    
    @Test
    @DisplayName("测试addValue方法-添加null Map")
    void testAddValue_NullMap() {
        // Given
        String source = "{\"old\":\"value\"}";
        Map<String, String> addMap = null;
        
        Map<String, String> sourceMap = new HashMap<>();
        sourceMap.put("old", "value");
        String expectedJson = "{\"old\":\"value\"}";
        
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class);
             MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            
            // Mock jsonToMap
            stringUtilsMock.when(() -> StringUtils.isBlank(source)).thenReturn(false);
            jsonMock.when(() -> JSON.parseObject(eq(source), any(TypeReference.class)))
                    .thenReturn(sourceMap);
            
            // Mock isEmpty for addMap
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(addMap)).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(sourceMap)).thenReturn(false);
            
            // Mock mapToJson
            jsonMock.when(() -> JSON.toJSONString(eq(sourceMap), any())).thenReturn(expectedJson);
            
            String result = MapUtil.addValue(source, addMap);
            
            assertThat(result).isEqualTo(expectedJson);
        }
    }
} 