package com.uaepay.cmf.common.core.domain;

import com.uaepay.cmf.common.core.domain.enums.CmfOrderConfirmStatus;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.NotifyStatus;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * CmfOrder单元测试
 * 测试资金渠道管理订单的所有功能
 */
class CmfOrderTest {

    private CmfOrder cmfOrder;

    @BeforeEach
    void setUp() {
        cmfOrder = new CmfOrder();
    }

    @Test
    @DisplayName("测试默认构造函数")
    void testDefaultConstructor() {
        // When
        CmfOrder order = new CmfOrder();

        // Then
        assertThat(order).isNotNull();
        assertThat(order.getAmount()).isNotNull();
        assertThat(order.getAmount().getAmount()).isEqualTo("0.0");
        assertThat(order.getAmount().getCurrency()).isEqualTo("AED");
        assertThat(order.getExtension()).isNotNull();
        assertThat(order.getExtension()).isEmpty();
    }

    @Test
    @DisplayName("测试订单流水号的getter和setter")
    void testOrderSeqNo() {
        // Given
        String orderSeqNo = "ORDER123456789";

        // When
        cmfOrder.setOrderSeqNo(orderSeqNo);

        // Then
        assertThat(cmfOrder.getOrderSeqNo()).isEqualTo(orderSeqNo);
    }

    @Test
    @DisplayName("测试请求类型的getter和setter")
    void testRequestType() {
        // Given - 使用null测试，因为不确定具体枚举值
        RequestType requestType = null;

        // When
        cmfOrder.setRequestType(requestType);

        // Then
        assertThat(cmfOrder.getRequestType()).isEqualTo(requestType);
    }

    @Test
    @DisplayName("测试业务类型的getter和setter")
    void testBizType() {
        // Given - 使用null测试，因为不确定具体枚举值
        BizType bizType = null;

        // When
        cmfOrder.setBizType(bizType);

        // Then
        assertThat(cmfOrder.getBizType()).isEqualTo(bizType);
    }

    @Test
    @DisplayName("测试支付方式的getter和setter")
    void testPayMode() {
        // Given - 使用null测试，因为不确定具体枚举值
        PayMode payMode = null;

        // When
        cmfOrder.setPayMode(payMode);

        // Then
        assertThat(cmfOrder.getPayMode()).isEqualTo(payMode);
    }

    @Test
    @DisplayName("测试机构编码的getter和setter")
    void testInstCode() {
        // Given
        String instCode = "INST001";

        // When
        cmfOrder.setInstCode(instCode);

        // Then
        assertThat(cmfOrder.getInstCode()).isEqualTo(instCode);
    }

    @Test
    @DisplayName("测试请求批次号的getter和setter")
    void testRequestBatchNo() {
        // Given
        String requestBatchNo = "BATCH20231201001";

        // When
        cmfOrder.setRequestBatchNo(requestBatchNo);

        // Then
        assertThat(cmfOrder.getRequestBatchNo()).isEqualTo(requestBatchNo);
    }

    @Test
    @DisplayName("测试支付流水号的getter和setter")
    void testPaymentSeqNo() {
        // Given
        String paymentSeqNo = "PAY123456789";

        // When
        cmfOrder.setPaymentSeqNo(paymentSeqNo);

        // Then
        assertThat(cmfOrder.getPaymentSeqNo()).isEqualTo(paymentSeqNo);
    }

    @Test
    @DisplayName("测试清结算ID的getter和setter")
    void testSettlementId() {
        // Given
        String settlementId = "SETTLE123456";

        // When
        cmfOrder.setSettlementId(settlementId);

        // Then
        assertThat(cmfOrder.getSettlementId()).isEqualTo(settlementId);
    }

    @Test
    @DisplayName("测试产品码的getter和setter")
    void testProductCode() {
        // Given
        String productCode = "PROD001";

        // When
        cmfOrder.setProductCode(productCode);

        // Then
        assertThat(cmfOrder.getProductCode()).isEqualTo(productCode);
    }

    @Test
    @DisplayName("测试支付编码的getter和setter")
    void testPaymentCode() {
        // Given
        String paymentCode = "PAYCODE001";

        // When
        cmfOrder.setPaymentCode(paymentCode);

        // Then
        assertThat(cmfOrder.getPaymentCode()).isEqualTo(paymentCode);
    }

    @Test
    @DisplayName("测试会员ID的getter和setter")
    void testMemberId() {
        // Given
        String memberId = "MEMBER123456";

        // When
        cmfOrder.setMemberId(memberId);

        // Then
        assertThat(cmfOrder.getMemberId()).isEqualTo(memberId);
    }

    @Test
    @DisplayName("测试金额设置-正常金额")
    void testSetAmount_ValidAmount() {
        // Given
        Money amount = new Money("100.50", "AED");

        // When
        cmfOrder.setAmount(amount);

        // Then
        assertThat(cmfOrder.getAmount()).isEqualTo(amount);
        assertThat(cmfOrder.getAmount().getAmount()).isEqualTo("100.50");
        assertThat(cmfOrder.getAmount().getCurrency()).isEqualTo("AED");
    }

    @Test
    @DisplayName("测试金额设置-null值")
    void testSetAmount_NullAmount() {
        // When
        cmfOrder.setAmount(null);

        // Then
        assertThat(cmfOrder.getAmount()).isNotNull();
        assertThat(cmfOrder.getAmount().getAmount()).isEqualTo("0.0");
        assertThat(cmfOrder.getAmount().getCurrency()).isEqualTo("AED");
    }

    @Test
    @DisplayName("测试业务发起时间的getter和setter")
    void testBizTime() {
        // Given
        Date bizTime = new Date();

        // When
        cmfOrder.setBizTime(bizTime);

        // Then
        assertThat(cmfOrder.getBizTime()).isEqualTo(bizTime);
    }

    @Test
    @DisplayName("测试资金渠道编码的getter和setter")
    void testFundChannelCode() {
        // Given
        String fundChannelCode = "CHANNEL001";

        // When
        cmfOrder.setFundChannelCode(fundChannelCode);

        // Then
        assertThat(cmfOrder.getFundChannelCode()).isEqualTo(fundChannelCode);
    }

    @Test
    @DisplayName("测试操作员的getter和setter")
    void testOperator() {
        // Given
        String operator = "admin";

        // When
        cmfOrder.setOperator(operator);

        // Then
        assertThat(cmfOrder.getOperator()).isEqualTo(operator);
    }

    @Test
    @DisplayName("测试创建时间的getter和setter")
    void testGmtCreate() {
        // Given
        Date gmtCreate = new Date();

        // When
        cmfOrder.setGmtCreate(gmtCreate);

        // Then
        assertThat(cmfOrder.getGmtCreate()).isEqualTo(gmtCreate);
    }

    @Test
    @DisplayName("测试最后修改时间的getter和setter")
    void testGmtModified() {
        // Given
        Date gmtModified = new Date();

        // When
        cmfOrder.setGmtModified(gmtModified);

        // Then
        assertThat(cmfOrder.getGmtModified()).isEqualTo(gmtModified);
    }

    @Test
    @DisplayName("测试状态的getter和setter")
    void testStatus() {
        // Given
        CmfOrderStatus status = CmfOrderStatus.SUCCESSFUL;

        // When
        cmfOrder.setStatus(status);

        // Then
        assertThat(cmfOrder.getStatus()).isEqualTo(status);
    }

    @Test
    @DisplayName("测试审核状态的getter和setter")
    void testConfirmStatus() {
        // Given
        CmfOrderConfirmStatus confirmStatus = CmfOrderConfirmStatus.PASS;

        // When
        cmfOrder.setConfirmStatus(confirmStatus);

        // Then
        assertThat(cmfOrder.getConfirmStatus()).isEqualTo(confirmStatus);
    }

    @Test
    @DisplayName("测试支付结果通知状态的getter和setter")
    void testPaymentNotifyStatus() {
        // Given
        NotifyStatus paymentNotifyStatus = NotifyStatus.SUCCESSFUL;

        // When
        cmfOrder.setPaymentNotifyStatus(paymentNotifyStatus);

        // Then
        assertThat(cmfOrder.getPaymentNotifyStatus()).isEqualTo(paymentNotifyStatus);
    }

    @Test
    @DisplayName("测试扩展信息的getter和setter")
    void testExtension() {
        // Given
        Map<String, String> extension = new HashMap<>();
        extension.put("key1", "value1");
        extension.put("key2", "value2");

        // When
        cmfOrder.setExtension(extension);

        // Then
        assertThat(cmfOrder.getExtension()).isEqualTo(extension);
        assertThat(cmfOrder.getExtension()).hasSize(2);
        assertThat(cmfOrder.getExtension().get("key1")).isEqualTo("value1");
        assertThat(cmfOrder.getExtension().get("key2")).isEqualTo("value2");
    }

    @Test
    @DisplayName("测试原始入款订单号的getter和setter")
    void testOrgiPaymentSeqNo() {
        // Given
        String orgiPaymentSeqNo = "ORGI_PAY_123456";

        // When
        cmfOrder.setOrgiPaymentSeqNo(orgiPaymentSeqNo);

        // Then
        assertThat(cmfOrder.getOrgiPaymentSeqNo()).isEqualTo(orgiPaymentSeqNo);
    }

    @Test
    @DisplayName("测试原始入款结算ID的getter和setter")
    void testOrgiSettlementId() {
        // Given
        String orgiSettlementId = "ORGI_SETTLE_123456";

        // When
        cmfOrder.setOrgiSettlementId(orgiSettlementId);

        // Then
        assertThat(cmfOrder.getOrgiSettlementId()).isEqualTo(orgiSettlementId);
    }

    @Test
    @DisplayName("测试支付凭证号的getter和setter")
    void testPaymentVoucherNo() {
        // Given
        String paymentVoucherNo = "VOUCHER_123456";

        // When
        cmfOrder.setPaymentVoucherNo(paymentVoucherNo);

        // Then
        assertThat(cmfOrder.getPaymentVoucherNo()).isEqualTo(paymentVoucherNo);
    }

    @Test
    @DisplayName("测试商户ID的getter和setter")
    void testMerchantId() {
        // Given
        String merchantId = "MERCHANT_123456";

        // When
        cmfOrder.setMerchantId(merchantId);

        // Then
        assertThat(cmfOrder.getMerchantId()).isEqualTo(merchantId);
    }

    @Test
    @DisplayName("测试备注的getter和setter")
    void testMemo() {
        // Given
        String memo = "Test memo for order";

        // When
        cmfOrder.setMemo(memo);

        // Then
        assertThat(cmfOrder.getMemo()).isEqualTo(memo);
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // Given
        cmfOrder.setOrderSeqNo("ORDER123");
        cmfOrder.setPaymentSeqNo("PAY123");
        cmfOrder.setMemberId("MEMBER123");

        // When
        String result = cmfOrder.toString();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isNotEmpty();
        assertThat(result).contains("CmfOrder");
    }

    @Test
    @DisplayName("测试完整订单对象创建")
    void testCompleteOrderCreation() {
        // Given
        String orderSeqNo = "ORDER_TEST_123";
        String paymentSeqNo = "PAY_TEST_123";
        String memberId = "MEMBER_TEST_001";
        Money amount = new Money("500.00", "AED");
        String instCode = "BANK_001";
        Date now = new Date();
        String memo = "Test order";

        Map<String, String> extension = new HashMap<>();
        extension.put("cardType", "VISA");
        extension.put("channel", "WEB");

        // When
        cmfOrder.setOrderSeqNo(orderSeqNo);
        cmfOrder.setPaymentSeqNo(paymentSeqNo);
        cmfOrder.setMemberId(memberId);
        cmfOrder.setAmount(amount);
        cmfOrder.setInstCode(instCode);
        cmfOrder.setGmtCreate(now);
        cmfOrder.setGmtModified(now);
        cmfOrder.setMemo(memo);
        cmfOrder.setExtension(extension);

        // Then
        assertThat(cmfOrder.getOrderSeqNo()).isEqualTo(orderSeqNo);
        assertThat(cmfOrder.getPaymentSeqNo()).isEqualTo(paymentSeqNo);
        assertThat(cmfOrder.getMemberId()).isEqualTo(memberId);
        assertThat(cmfOrder.getAmount()).isEqualTo(amount);
        assertThat(cmfOrder.getInstCode()).isEqualTo(instCode);
        assertThat(cmfOrder.getGmtCreate()).isEqualTo(now);
        assertThat(cmfOrder.getGmtModified()).isEqualTo(now);
        assertThat(cmfOrder.getMemo()).isEqualTo(memo);
        assertThat(cmfOrder.getExtension()).isEqualTo(extension);
    }
}