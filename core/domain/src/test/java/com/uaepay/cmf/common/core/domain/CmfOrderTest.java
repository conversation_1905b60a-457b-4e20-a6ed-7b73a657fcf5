package com.uaepay.cmf.common.core.domain;

import com.uaepay.cmf.common.core.domain.enums.BizType;
import com.uaepay.common.util.money.Money;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * CmfOrder类单元测试
 * 测试核心订单对象的所有功能
 */
class CmfOrderTest {
    
    private CmfOrder cmfOrder;
    
    @BeforeEach
    void setUp() {
        cmfOrder = new CmfOrder();
    }
    
    @Test
    @DisplayName("测试默认构造函数")
    void testDefaultConstructor() {
        // Given & When
        CmfOrder order = new CmfOrder();
        
        // Then
        assertThat(order).isNotNull();
        assertThat(order.getAmount()).isEqualTo(new Money("0.00", "AED")); // 默认金额
    }
    
    @Test
    @DisplayName("测试paymentSeqNo属性")
    void testPaymentSeqNo() {
        // Given
        String paymentSeqNo = "PAY_123456789";
        
        // When
        cmfOrder.setPaymentSeqNo(paymentSeqNo);
        
        // Then
        assertThat(cmfOrder.getPaymentSeqNo()).isEqualTo(paymentSeqNo);
    }
    
    @Test
    @DisplayName("测试memberId属性")
    void testMemberId() {
        // Given
        String memberId = "MEMBER_001";
        
        // When
        cmfOrder.setMemberId(memberId);
        
        // Then
        assertThat(cmfOrder.getMemberId()).isEqualTo(memberId);
    }
    
    @Test
    @DisplayName("测试amount属性-正常金额")
    void testAmount_Normal() {
        // Given
        Money amount = new Money("100.50", "AED");
        
        // When
        cmfOrder.setAmount(amount);
        
        // Then
        assertThat(cmfOrder.getAmount()).isEqualTo(amount);
        assertThat(cmfOrder.getAmount().getAmount()).isEqualByComparingTo("100.50");
        assertThat(cmfOrder.getAmount().getCurrency()).isEqualTo("AED");
    }
    
    @Test
    @DisplayName("测试amount属性-null值处理")
    void testAmount_Null() {
        // Given
        Money defaultAmount = new Money("0.00", "AED");
        
        // When
        cmfOrder.setAmount(null);
        
        // Then
        assertThat(cmfOrder.getAmount()).isEqualTo(defaultAmount);
    }
    
    @Test
    @DisplayName("测试currency属性")
    void testCurrency() {
        // Given
        String currency = "USD";
        
        // When
        cmfOrder.setCurrency(currency);
        
        // Then
        assertThat(cmfOrder.getCurrency()).isEqualTo(currency);
    }
    
    @Test
    @DisplayName("测试bizType属性")
    void testBizType() {
        // Given
        BizType bizType = BizType.FUNDIN;
        
        // When
        cmfOrder.setBizType(bizType);
        
        // Then
        assertThat(cmfOrder.getBizType()).isEqualTo(bizType);
    }
    
    @Test
    @DisplayName("测试instCode属性")
    void testInstCode() {
        // Given
        String instCode = "INST_CODE_001";
        
        // When
        cmfOrder.setInstCode(instCode);
        
        // Then
        assertThat(cmfOrder.getInstCode()).isEqualTo(instCode);
    }
    
    @Test
    @DisplayName("测试status属性")
    void testStatus() {
        // Given
        String status = "SUCCESS";
        
        // When
        cmfOrder.setStatus(status);
        
        // Then
        assertThat(cmfOrder.getStatus()).isEqualTo(status);
    }
    
    @Test
    @DisplayName("测试subStatus属性")
    void testSubStatus() {
        // Given
        String subStatus = "COMPLETED";
        
        // When
        cmfOrder.setSubStatus(subStatus);
        
        // Then
        assertThat(cmfOrder.getSubStatus()).isEqualTo(subStatus);
    }
    
    @Test
    @DisplayName("测试extension属性-设置和获取")
    void testExtension() {
        // Given
        Map<String, String> extension = new HashMap<>();
        extension.put("key1", "value1");
        extension.put("key2", "value2");
        
        // When
        cmfOrder.setExtension(extension);
        
        // Then
        assertThat(cmfOrder.getExtension()).isEqualTo(extension);
        assertThat(cmfOrder.getExtension()).containsEntry("key1", "value1");
        assertThat(cmfOrder.getExtension()).containsEntry("key2", "value2");
    }
    
    @Test
    @DisplayName("测试extension属性-null处理")
    void testExtension_Null() {
        // When
        cmfOrder.setExtension(null);
        
        // Then
        assertThat(cmfOrder.getExtension()).isNull();
    }
    
    @Test
    @DisplayName("测试extension属性-动态添加")
    void testExtension_DynamicAdd() {
        // Given
        cmfOrder.setExtension(new HashMap<>());
        
        // When
        cmfOrder.getExtension().put("dynamicKey", "dynamicValue");
        
        // Then
        assertThat(cmfOrder.getExtension()).containsEntry("dynamicKey", "dynamicValue");
    }
    
    @Test
    @DisplayName("测试gmtCreate属性")
    void testGmtCreate() {
        // Given
        Date gmtCreate = new Date();
        
        // When
        cmfOrder.setGmtCreate(gmtCreate);
        
        // Then
        assertThat(cmfOrder.getGmtCreate()).isEqualTo(gmtCreate);
    }
    
    @Test
    @DisplayName("测试gmtModified属性")
    void testGmtModified() {
        // Given
        Date gmtModified = new Date();
        
        // When
        cmfOrder.setGmtModified(gmtModified);
        
        // Then
        assertThat(cmfOrder.getGmtModified()).isEqualTo(gmtModified);
    }
    
    @Test
    @DisplayName("测试memo属性")
    void testMemo() {
        // Given
        String memo = "This is a test memo";
        
        // When
        cmfOrder.setMemo(memo);
        
        // Then
        assertThat(cmfOrder.getMemo()).isEqualTo(memo);
    }
    
    @Test
    @DisplayName("测试完整的订单对象创建")
    void testCompleteOrderCreation() {
        // Given
        String paymentSeqNo = "PAY_TEST_123";
        String memberId = "MEMBER_TEST_001";
        Money amount = new Money("500.00", "AED");
        BizType bizType = BizType.FUNDOUT;
        String instCode = "BANK_001";
        String status = "PROCESSING";
        String subStatus = "PENDING";
        Date now = new Date();
        String memo = "Test order";
        
        Map<String, String> extension = new HashMap<>();
        extension.put("cardType", "VISA");
        extension.put("channel", "WEB");
        
        // When
        cmfOrder.setPaymentSeqNo(paymentSeqNo);
        cmfOrder.setMemberId(memberId);
        cmfOrder.setAmount(amount);
        cmfOrder.setBizType(bizType);
        cmfOrder.setInstCode(instCode);
        cmfOrder.setStatus(status);
        cmfOrder.setSubStatus(subStatus);
        cmfOrder.setGmtCreate(now);
        cmfOrder.setGmtModified(now);
        cmfOrder.setMemo(memo);
        cmfOrder.setExtension(extension);
        
        // Then
        assertThat(cmfOrder.getPaymentSeqNo()).isEqualTo(paymentSeqNo);
        assertThat(cmfOrder.getMemberId()).isEqualTo(memberId);
        assertThat(cmfOrder.getAmount()).isEqualTo(amount);
        assertThat(cmfOrder.getBizType()).isEqualTo(bizType);
        assertThat(cmfOrder.getInstCode()).isEqualTo(instCode);
        assertThat(cmfOrder.getStatus()).isEqualTo(status);
        assertThat(cmfOrder.getSubStatus()).isEqualTo(subStatus);
        assertThat(cmfOrder.getGmtCreate()).isEqualTo(now);
        assertThat(cmfOrder.getGmtModified()).isEqualTo(now);
        assertThat(cmfOrder.getMemo()).isEqualTo(memo);
        assertThat(cmfOrder.getExtension()).isEqualTo(extension);
    }
    
    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // Given
        cmfOrder.setPaymentSeqNo("PAY_123");
        cmfOrder.setMemberId("MEMBER_123");
        cmfOrder.setAmount(new Money("100.00", "AED"));
        
        // When
        String result = cmfOrder.toString();
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).isNotEmpty();
        assertThat(result).contains("CmfOrder"); // 假设使用了Lombok @ToString
    }
    
    @Test
    @DisplayName("测试equals和hashCode方法")
    void testEqualsAndHashCode() {
        // Given
        CmfOrder order1 = new CmfOrder();
        order1.setPaymentSeqNo("PAY_123");
        order1.setMemberId("MEMBER_123");
        
        CmfOrder order2 = new CmfOrder();
        order2.setPaymentSeqNo("PAY_123");
        order2.setMemberId("MEMBER_123");
        
        CmfOrder order3 = new CmfOrder();
        order3.setPaymentSeqNo("PAY_456");
        order3.setMemberId("MEMBER_456");
        
        // Then
        assertThat(order1).isEqualTo(order2);
        assertThat(order1).isNotEqualTo(order3);
        assertThat(order1.hashCode()).isEqualTo(order2.hashCode());
    }
    
    @Test
    @DisplayName("测试边界值-空字符串")
    void testBoundaryValues_EmptyStrings() {
        // When
        cmfOrder.setPaymentSeqNo("");
        cmfOrder.setMemberId("");
        cmfOrder.setCurrency("");
        cmfOrder.setInstCode("");
        cmfOrder.setStatus("");
        cmfOrder.setSubStatus("");
        cmfOrder.setMemo("");
        
        // Then
        assertThat(cmfOrder.getPaymentSeqNo()).isEmpty();
        assertThat(cmfOrder.getMemberId()).isEmpty();
        assertThat(cmfOrder.getCurrency()).isEmpty();
        assertThat(cmfOrder.getInstCode()).isEmpty();
        assertThat(cmfOrder.getStatus()).isEmpty();
        assertThat(cmfOrder.getSubStatus()).isEmpty();
        assertThat(cmfOrder.getMemo()).isEmpty();
    }
    
    @Test
    @DisplayName("测试边界值-长字符串")
    void testBoundaryValues_LongStrings() {
        // Given
        String longString = "A".repeat(1000);
        
        // When
        cmfOrder.setPaymentSeqNo(longString);
        cmfOrder.setMemberId(longString);
        cmfOrder.setMemo(longString);
        
        // Then
        assertThat(cmfOrder.getPaymentSeqNo()).hasSize(1000);
        assertThat(cmfOrder.getMemberId()).hasSize(1000);
        assertThat(cmfOrder.getMemo()).hasSize(1000);
    }
} 