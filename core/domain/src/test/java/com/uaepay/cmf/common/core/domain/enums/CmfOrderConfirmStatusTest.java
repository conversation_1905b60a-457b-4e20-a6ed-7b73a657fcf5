package com.uaepay.cmf.common.core.domain.enums;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * CmfOrderConfirmStatus枚举单元测试
 * 测试CMF订单确认状态枚举的所有功能
 */
class CmfOrderConfirmStatusTest {

    @Test
    @DisplayName("测试枚举值的存在性")
    void testEnumValues() {
        // Then
        assertThat(CmfOrderConfirmStatus.values()).isNotEmpty();
        assertThat(CmfOrderConfirmStatus.values()).contains(
            CmfOrderConfirmStatus.INIT,
            CmfOrderConfirmStatus.PASS,
            CmfOrderConfirmStatus.REJECT
        );
    }

    @Test
    @DisplayName("测试枚举值数量")
    void testEnumCount() {
        // Then
        assertThat(CmfOrderConfirmStatus.values()).hasSize(3);
    }

    @Test
    @DisplayName("测试INIT状态")
    void testInitStatus() {
        // Given
        CmfOrderConfirmStatus status = CmfOrderConfirmStatus.INIT;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("INIT");
        assertThat(status.toString()).isEqualTo("INIT");
    }

    @Test
    @DisplayName("测试PASS状态")
    void testPassStatus() {
        // Given
        CmfOrderConfirmStatus status = CmfOrderConfirmStatus.PASS;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("PASS");
        assertThat(status.toString()).isEqualTo("PASS");
    }

    @Test
    @DisplayName("测试REJECT状态")
    void testRejectStatus() {
        // Given
        CmfOrderConfirmStatus status = CmfOrderConfirmStatus.REJECT;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("REJECT");
        assertThat(status.toString()).isEqualTo("REJECT");
    }

    @Test
    @DisplayName("测试valueOf方法")
    void testValueOf() {
        // Then
        assertThat(CmfOrderConfirmStatus.valueOf("INIT")).isEqualTo(CmfOrderConfirmStatus.INIT);
        assertThat(CmfOrderConfirmStatus.valueOf("PASS")).isEqualTo(CmfOrderConfirmStatus.PASS);
        assertThat(CmfOrderConfirmStatus.valueOf("REJECT")).isEqualTo(CmfOrderConfirmStatus.REJECT);
    }

    @Test
    @DisplayName("测试valueOf方法-无效值")
    void testValueOf_InvalidValue() {
        // Then
        assertThatThrownBy(() -> CmfOrderConfirmStatus.valueOf("INVALID"))
            .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    @DisplayName("测试valueOf方法-null值")
    void testValueOf_NullValue() {
        // Then
        assertThatThrownBy(() -> CmfOrderConfirmStatus.valueOf(null))
            .isInstanceOf(NullPointerException.class);
    }

    @Test
    @DisplayName("测试枚举比较")
    void testEnumComparison() {
        // Given
        CmfOrderConfirmStatus status1 = CmfOrderConfirmStatus.INIT;
        CmfOrderConfirmStatus status2 = CmfOrderConfirmStatus.INIT;
        CmfOrderConfirmStatus status3 = CmfOrderConfirmStatus.PASS;

        // Then
        assertThat(status1).isEqualTo(status2);
        assertThat(status1).isNotEqualTo(status3);
        assertThat(status1 == status2).isTrue();
        assertThat(status1 == status3).isFalse();
    }

    @Test
    @DisplayName("测试枚举序号")
    void testEnumOrdinal() {
        // Then
        assertThat(CmfOrderConfirmStatus.INIT.ordinal()).isEqualTo(0);
        assertThat(CmfOrderConfirmStatus.PASS.ordinal()).isEqualTo(1);
        assertThat(CmfOrderConfirmStatus.REJECT.ordinal()).isEqualTo(2);
    }

    @Test
    @DisplayName("测试枚举在switch语句中的使用")
    void testEnumInSwitch() {
        // Given & When & Then
        for (CmfOrderConfirmStatus status : CmfOrderConfirmStatus.values()) {
            String description = getStatusDescription(status);
            assertThat(description).isNotNull();
            assertThat(description).isNotEmpty();
        }
    }

    @Test
    @DisplayName("测试业务场景-确认状态流转")
    void testBusinessScenario_ConfirmStatusTransition() {
        // Given
        CmfOrderConfirmStatus initialStatus = CmfOrderConfirmStatus.INIT;
        CmfOrderConfirmStatus passStatus = CmfOrderConfirmStatus.PASS;
        CmfOrderConfirmStatus rejectStatus = CmfOrderConfirmStatus.REJECT;

        // Then - 验证初始状态可以流转到通过或拒绝
        assertThat(initialStatus.ordinal()).isLessThan(passStatus.ordinal());
        assertThat(initialStatus.ordinal()).isLessThan(rejectStatus.ordinal());
    }

    @Test
    @DisplayName("测试业务场景-终态状态识别")
    void testBusinessScenario_FinalStatusIdentification() {
        // When & Then
        assertThat(isFinalStatus(CmfOrderConfirmStatus.PASS)).isTrue();
        assertThat(isFinalStatus(CmfOrderConfirmStatus.REJECT)).isTrue();
        assertThat(isFinalStatus(CmfOrderConfirmStatus.INIT)).isFalse();
    }

    @Test
    @DisplayName("测试业务场景-通过状态识别")
    void testBusinessScenario_PassStatusIdentification() {
        // When & Then
        assertThat(isPassStatus(CmfOrderConfirmStatus.PASS)).isTrue();
        assertThat(isPassStatus(CmfOrderConfirmStatus.REJECT)).isFalse();
        assertThat(isPassStatus(CmfOrderConfirmStatus.INIT)).isFalse();
    }

    @Test
    @DisplayName("测试业务场景-拒绝状态识别")
    void testBusinessScenario_RejectStatusIdentification() {
        // When & Then
        assertThat(isRejectStatus(CmfOrderConfirmStatus.REJECT)).isTrue();
        assertThat(isRejectStatus(CmfOrderConfirmStatus.PASS)).isFalse();
        assertThat(isRejectStatus(CmfOrderConfirmStatus.INIT)).isFalse();
    }

    @Test
    @DisplayName("测试枚举集合操作")
    void testEnumCollectionOperations() {
        // Given
        java.util.Set<CmfOrderConfirmStatus> finalStatuses = java.util.EnumSet.of(
            CmfOrderConfirmStatus.PASS, 
            CmfOrderConfirmStatus.REJECT
        );
        
        java.util.Set<CmfOrderConfirmStatus> pendingStatuses = java.util.EnumSet.of(
            CmfOrderConfirmStatus.INIT
        );

        // Then
        assertThat(finalStatuses).hasSize(2);
        assertThat(pendingStatuses).hasSize(1);
        assertThat(finalStatuses).doesNotContain(CmfOrderConfirmStatus.INIT);
        assertThat(pendingStatuses).doesNotContain(CmfOrderConfirmStatus.PASS);
    }

    @Test
    @DisplayName("测试业务场景-审核工作流")
    void testBusinessScenario_AuditWorkflow() {
        // Given
        CmfOrderConfirmStatus initialStatus = CmfOrderConfirmStatus.INIT;

        // When - 模拟审核通过
        CmfOrderConfirmStatus approvedStatus = CmfOrderConfirmStatus.PASS;
        
        // Then
        assertThat(canTransitionTo(initialStatus, approvedStatus)).isTrue();
        assertThat(isFinalStatus(approvedStatus)).isTrue();

        // When - 模拟审核拒绝
        CmfOrderConfirmStatus rejectedStatus = CmfOrderConfirmStatus.REJECT;
        
        // Then
        assertThat(canTransitionTo(initialStatus, rejectedStatus)).isTrue();
        assertThat(isFinalStatus(rejectedStatus)).isTrue();
    }

    @Test
    @DisplayName("测试业务场景-状态变更权限")
    void testBusinessScenario_StatusChangePermission() {
        // Given
        CmfOrderConfirmStatus passStatus = CmfOrderConfirmStatus.PASS;
        CmfOrderConfirmStatus rejectStatus = CmfOrderConfirmStatus.REJECT;

        // Then - 终态之间不能互相转换
        assertThat(canTransitionTo(passStatus, rejectStatus)).isFalse();
        assertThat(canTransitionTo(rejectStatus, passStatus)).isFalse();
        assertThat(canTransitionTo(passStatus, CmfOrderConfirmStatus.INIT)).isFalse();
        assertThat(canTransitionTo(rejectStatus, CmfOrderConfirmStatus.INIT)).isFalse();
    }

    // 辅助方法
    private String getStatusDescription(CmfOrderConfirmStatus status) {
        switch (status) {
            case INIT:
                return "待确认";
            case PASS:
                return "确认通过";
            case REJECT:
                return "确认拒绝";
            default:
                return "未定义状态";
        }
    }

    private boolean isFinalStatus(CmfOrderConfirmStatus status) {
        return status == CmfOrderConfirmStatus.PASS || status == CmfOrderConfirmStatus.REJECT;
    }

    private boolean isPassStatus(CmfOrderConfirmStatus status) {
        return status == CmfOrderConfirmStatus.PASS;
    }

    private boolean isRejectStatus(CmfOrderConfirmStatus status) {
        return status == CmfOrderConfirmStatus.REJECT;
    }

    private boolean canTransitionTo(CmfOrderConfirmStatus from, CmfOrderConfirmStatus to) {
        // 业务规则：只有INIT状态可以转换到其他状态
        if (from == CmfOrderConfirmStatus.INIT) {
            return to == CmfOrderConfirmStatus.PASS || to == CmfOrderConfirmStatus.REJECT;
        }
        // 终态不能转换到其他状态
        return false;
    }
}
