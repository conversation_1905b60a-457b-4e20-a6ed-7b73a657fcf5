package com.uaepay.cmf.common.core.domain;

import com.uaepay.schema.cmf.enums.SuccessFailure;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Date;

import static org.assertj.core.api.Assertions.*;

/**
 * PaymentNotifyLog单元测试
 * 测试支付结果通知日志对象的所有功能
 */
class PaymentNotifyLogTest {

    private PaymentNotifyLog paymentNotifyLog;

    @BeforeEach
    void setUp() {
        paymentNotifyLog = new PaymentNotifyLog();
    }

    @Test
    @DisplayName("测试默认构造函数")
    void testDefaultConstructor() {
        // When
        PaymentNotifyLog log = new PaymentNotifyLog();

        // Then
        assertThat(log).isNotNull();
        assertThat(log.getNotifyLogId()).isEqualTo(0L); // long默认值为0
    }

    @Test
    @DisplayName("测试通知日志ID的getter和setter")
    void testNotifyLogId() {
        // Given
        long notifyLogId = 123456789L;

        // When
        paymentNotifyLog.setNotifyLogId(notifyLogId);

        // Then
        assertThat(paymentNotifyLog.getNotifyLogId()).isEqualTo(notifyLogId);
    }

    @Test
    @DisplayName("测试渠道流水号的getter和setter")
    void testChannelSeqNo() {
        // Given
        String channelSeqNo = "CHANNEL123456789";

        // When
        paymentNotifyLog.setChannelSeqNo(channelSeqNo);

        // Then
        assertThat(paymentNotifyLog.getChannelSeqNo()).isEqualTo(channelSeqNo);
    }

    @Test
    @DisplayName("测试通知结果的getter和setter-成功")
    void testNotifyResult_Success() {
        // Given
        SuccessFailure notifyResult = SuccessFailure.SUCCESS;

        // When
        paymentNotifyLog.setNotifyResult(notifyResult);

        // Then
        assertThat(paymentNotifyLog.getNotifyResult()).isEqualTo(notifyResult);
    }

    @Test
    @DisplayName("测试通知结果的getter和setter-失败")
    void testNotifyResult_Failure() {
        // Given
        SuccessFailure notifyResult = SuccessFailure.FAILURE;

        // When
        paymentNotifyLog.setNotifyResult(notifyResult);

        // Then
        assertThat(paymentNotifyLog.getNotifyResult()).isEqualTo(notifyResult);
    }

    @Test
    @DisplayName("测试创建时间的getter和setter")
    void testGmtCreate() {
        // Given
        Date gmtCreate = new Date();

        // When
        paymentNotifyLog.setGmtCreate(gmtCreate);

        // Then
        assertThat(paymentNotifyLog.getGmtCreate()).isEqualTo(gmtCreate);
    }

    @Test
    @DisplayName("测试备注的getter和setter")
    void testMemo() {
        // Given
        String memo = "Payment notification successful";

        // When
        paymentNotifyLog.setMemo(memo);

        // Then
        assertThat(paymentNotifyLog.getMemo()).isEqualTo(memo);
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // Given
        paymentNotifyLog.setNotifyLogId(123L);
        paymentNotifyLog.setChannelSeqNo("CHANNEL123");
        paymentNotifyLog.setNotifyResult(SuccessFailure.SUCCESS);

        // When
        String result = paymentNotifyLog.toString();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isNotEmpty();
        assertThat(result).contains("PaymentNotifyLog");
    }

    @Test
    @DisplayName("测试完整通知日志对象创建")
    void testCompleteNotifyLogCreation() {
        // Given
        long notifyLogId = 987654321L;
        String channelSeqNo = "CHANNEL_TEST_123";
        SuccessFailure notifyResult = SuccessFailure.SUCCESS;
        Date gmtCreate = new Date();
        String memo = "Test notification log";

        // When
        paymentNotifyLog.setNotifyLogId(notifyLogId);
        paymentNotifyLog.setChannelSeqNo(channelSeqNo);
        paymentNotifyLog.setNotifyResult(notifyResult);
        paymentNotifyLog.setGmtCreate(gmtCreate);
        paymentNotifyLog.setMemo(memo);

        // Then
        assertThat(paymentNotifyLog.getNotifyLogId()).isEqualTo(notifyLogId);
        assertThat(paymentNotifyLog.getChannelSeqNo()).isEqualTo(channelSeqNo);
        assertThat(paymentNotifyLog.getNotifyResult()).isEqualTo(notifyResult);
        assertThat(paymentNotifyLog.getGmtCreate()).isEqualTo(gmtCreate);
        assertThat(paymentNotifyLog.getMemo()).isEqualTo(memo);
    }

    @Test
    @DisplayName("测试null值处理")
    void testNullValues() {
        // When
        paymentNotifyLog.setChannelSeqNo(null);
        paymentNotifyLog.setNotifyResult(null);
        paymentNotifyLog.setGmtCreate(null);
        paymentNotifyLog.setMemo(null);

        // Then
        assertThat(paymentNotifyLog.getChannelSeqNo()).isNull();
        assertThat(paymentNotifyLog.getNotifyResult()).isNull();
        assertThat(paymentNotifyLog.getGmtCreate()).isNull();
        assertThat(paymentNotifyLog.getMemo()).isNull();
    }

    @Test
    @DisplayName("测试空字符串处理")
    void testEmptyStrings() {
        // When
        paymentNotifyLog.setChannelSeqNo("");
        paymentNotifyLog.setMemo("");

        // Then
        assertThat(paymentNotifyLog.getChannelSeqNo()).isEmpty();
        assertThat(paymentNotifyLog.getMemo()).isEmpty();
    }

    @Test
    @DisplayName("测试长字符串处理")
    void testLongStrings() {
        // Given
        String longString = "A".repeat(1000);

        // When
        paymentNotifyLog.setChannelSeqNo(longString);
        paymentNotifyLog.setMemo(longString);

        // Then
        assertThat(paymentNotifyLog.getChannelSeqNo()).hasSize(1000);
        assertThat(paymentNotifyLog.getMemo()).hasSize(1000);
    }

    @Test
    @DisplayName("测试ID边界值")
    void testIdBoundaryValues() {
        // Given
        long maxId = Long.MAX_VALUE;
        long minId = Long.MIN_VALUE;

        // When & Then
        paymentNotifyLog.setNotifyLogId(maxId);
        assertThat(paymentNotifyLog.getNotifyLogId()).isEqualTo(maxId);

        paymentNotifyLog.setNotifyLogId(minId);
        assertThat(paymentNotifyLog.getNotifyLogId()).isEqualTo(minId);

        paymentNotifyLog.setNotifyLogId(0L);
        assertThat(paymentNotifyLog.getNotifyLogId()).isEqualTo(0L);
    }

    @Test
    @DisplayName("测试时间边界值")
    void testDateBoundaryValues() {
        // Given
        Date pastDate = new Date(0); // 1970-01-01
        Date futureDate = new Date(Long.MAX_VALUE);

        // When & Then
        paymentNotifyLog.setGmtCreate(pastDate);
        assertThat(paymentNotifyLog.getGmtCreate()).isEqualTo(pastDate);

        paymentNotifyLog.setGmtCreate(futureDate);
        assertThat(paymentNotifyLog.getGmtCreate()).isEqualTo(futureDate);
    }

    @Test
    @DisplayName("测试对象相等性")
    void testObjectEquality() {
        // Given
        PaymentNotifyLog log1 = new PaymentNotifyLog();
        log1.setNotifyLogId(123L);
        log1.setChannelSeqNo("CHANNEL123");
        log1.setNotifyResult(SuccessFailure.SUCCESS);

        PaymentNotifyLog log2 = new PaymentNotifyLog();
        log2.setNotifyLogId(123L);
        log2.setChannelSeqNo("CHANNEL123");
        log2.setNotifyResult(SuccessFailure.SUCCESS);

        PaymentNotifyLog log3 = new PaymentNotifyLog();
        log3.setNotifyLogId(456L);
        log3.setChannelSeqNo("CHANNEL456");
        log3.setNotifyResult(SuccessFailure.FAILURE);

        // Then - 注意：由于使用了Lombok，这里测试的是Lombok生成的equals方法
        assertThat(log1).isEqualTo(log2);
        assertThat(log1).isNotEqualTo(log3);
        assertThat(log1.hashCode()).isEqualTo(log2.hashCode());
    }

    @Test
    @DisplayName("测试业务场景-成功通知日志")
    void testBusinessScenario_SuccessNotification() {
        // Given
        long notifyLogId = 1001L;
        String channelSeqNo = "SUCCESS_CHANNEL_001";
        Date notifyTime = new Date();

        // When
        paymentNotifyLog.setNotifyLogId(notifyLogId);
        paymentNotifyLog.setChannelSeqNo(channelSeqNo);
        paymentNotifyLog.setNotifyResult(SuccessFailure.SUCCESS);
        paymentNotifyLog.setGmtCreate(notifyTime);
        paymentNotifyLog.setMemo("Payment notification sent successfully");

        // Then
        assertThat(paymentNotifyLog.getNotifyResult()).isEqualTo(SuccessFailure.SUCCESS);
        assertThat(paymentNotifyLog.getMemo()).contains("successfully");
    }

    @Test
    @DisplayName("测试业务场景-失败通知日志")
    void testBusinessScenario_FailureNotification() {
        // Given
        long notifyLogId = 1002L;
        String channelSeqNo = "FAILURE_CHANNEL_001";
        Date notifyTime = new Date();

        // When
        paymentNotifyLog.setNotifyLogId(notifyLogId);
        paymentNotifyLog.setChannelSeqNo(channelSeqNo);
        paymentNotifyLog.setNotifyResult(SuccessFailure.FAILURE);
        paymentNotifyLog.setGmtCreate(notifyTime);
        paymentNotifyLog.setMemo("Payment notification failed due to network error");

        // Then
        assertThat(paymentNotifyLog.getNotifyResult()).isEqualTo(SuccessFailure.FAILURE);
        assertThat(paymentNotifyLog.getMemo()).contains("failed");
    }

    @Test
    @DisplayName("测试业务场景-重复通知检查")
    void testBusinessScenario_DuplicateNotificationCheck() {
        // Given
        String channelSeqNo = "DUPLICATE_CHECK_001";
        
        PaymentNotifyLog firstLog = new PaymentNotifyLog();
        firstLog.setNotifyLogId(2001L);
        firstLog.setChannelSeqNo(channelSeqNo);
        firstLog.setNotifyResult(SuccessFailure.SUCCESS);
        
        PaymentNotifyLog secondLog = new PaymentNotifyLog();
        secondLog.setNotifyLogId(2002L);
        secondLog.setChannelSeqNo(channelSeqNo);
        secondLog.setNotifyResult(SuccessFailure.SUCCESS);

        // Then - 相同渠道流水号但不同日志ID
        assertThat(firstLog.getChannelSeqNo()).isEqualTo(secondLog.getChannelSeqNo());
        assertThat(firstLog.getNotifyLogId()).isNotEqualTo(secondLog.getNotifyLogId());
    }
}
