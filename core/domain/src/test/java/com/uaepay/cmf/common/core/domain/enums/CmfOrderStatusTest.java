package com.uaepay.cmf.common.core.domain.enums;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * CmfOrderStatus枚举单元测试
 * 测试CMF订单状态枚举的所有功能
 */
class CmfOrderStatusTest {

    @Test
    @DisplayName("测试枚举值的存在性")
    void testEnumValues() {
        // Then
        assertThat(CmfOrderStatus.values()).isNotEmpty();
        assertThat(CmfOrderStatus.values()).contains(
            CmfOrderStatus.INIT,
            CmfOrderStatus.PROCESSING,
            CmfOrderStatus.SUCCESSFUL,
            CmfOrderStatus.FAILED,
            CmfOrderStatus.UNKNOWN
        );
    }

    @Test
    @DisplayName("测试枚举值数量")
    void testEnumCount() {
        // Then
        assertThat(CmfOrderStatus.values()).hasSize(5);
    }

    @Test
    @DisplayName("测试INIT状态")
    void testInitStatus() {
        // Given
        CmfOrderStatus status = CmfOrderStatus.INIT;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("INIT");
        assertThat(status.toString()).isEqualTo("INIT");
    }

    @Test
    @DisplayName("测试PROCESSING状态")
    void testProcessingStatus() {
        // Given
        CmfOrderStatus status = CmfOrderStatus.PROCESSING;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("PROCESSING");
        assertThat(status.toString()).isEqualTo("PROCESSING");
    }

    @Test
    @DisplayName("测试SUCCESSFUL状态")
    void testSuccessfulStatus() {
        // Given
        CmfOrderStatus status = CmfOrderStatus.SUCCESSFUL;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("SUCCESSFUL");
        assertThat(status.toString()).isEqualTo("SUCCESSFUL");
    }

    @Test
    @DisplayName("测试FAILED状态")
    void testFailedStatus() {
        // Given
        CmfOrderStatus status = CmfOrderStatus.FAILED;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("FAILED");
        assertThat(status.toString()).isEqualTo("FAILED");
    }

    @Test
    @DisplayName("测试UNKNOWN状态")
    void testUnknownStatus() {
        // Given
        CmfOrderStatus status = CmfOrderStatus.UNKNOWN;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("UNKNOWN");
        assertThat(status.toString()).isEqualTo("UNKNOWN");
    }

    @Test
    @DisplayName("测试valueOf方法")
    void testValueOf() {
        // Then
        assertThat(CmfOrderStatus.valueOf("INIT")).isEqualTo(CmfOrderStatus.INIT);
        assertThat(CmfOrderStatus.valueOf("PROCESSING")).isEqualTo(CmfOrderStatus.PROCESSING);
        assertThat(CmfOrderStatus.valueOf("SUCCESSFUL")).isEqualTo(CmfOrderStatus.SUCCESSFUL);
        assertThat(CmfOrderStatus.valueOf("FAILED")).isEqualTo(CmfOrderStatus.FAILED);
        assertThat(CmfOrderStatus.valueOf("UNKNOWN")).isEqualTo(CmfOrderStatus.UNKNOWN);
    }

    @Test
    @DisplayName("测试valueOf方法-无效值")
    void testValueOf_InvalidValue() {
        // Then
        assertThatThrownBy(() -> CmfOrderStatus.valueOf("INVALID"))
            .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    @DisplayName("测试valueOf方法-null值")
    void testValueOf_NullValue() {
        // Then
        assertThatThrownBy(() -> CmfOrderStatus.valueOf(null))
            .isInstanceOf(NullPointerException.class);
    }

    @Test
    @DisplayName("测试枚举比较")
    void testEnumComparison() {
        // Given
        CmfOrderStatus status1 = CmfOrderStatus.INIT;
        CmfOrderStatus status2 = CmfOrderStatus.INIT;
        CmfOrderStatus status3 = CmfOrderStatus.PROCESSING;

        // Then
        assertThat(status1).isEqualTo(status2);
        assertThat(status1).isNotEqualTo(status3);
        assertThat(status1 == status2).isTrue();
        assertThat(status1 == status3).isFalse();
    }

    @Test
    @DisplayName("测试枚举序号")
    void testEnumOrdinal() {
        // Then
        assertThat(CmfOrderStatus.INIT.ordinal()).isEqualTo(0);
        assertThat(CmfOrderStatus.PROCESSING.ordinal()).isEqualTo(1);
        assertThat(CmfOrderStatus.SUCCESSFUL.ordinal()).isEqualTo(2);
        assertThat(CmfOrderStatus.FAILED.ordinal()).isEqualTo(3);
        assertThat(CmfOrderStatus.UNKNOWN.ordinal()).isEqualTo(4);
    }

    @Test
    @DisplayName("测试枚举在switch语句中的使用")
    void testEnumInSwitch() {
        // Given & When & Then
        for (CmfOrderStatus status : CmfOrderStatus.values()) {
            String description = getStatusDescription(status);
            assertThat(description).isNotNull();
            assertThat(description).isNotEmpty();
        }
    }

    @Test
    @DisplayName("测试业务场景-订单状态流转")
    void testBusinessScenario_StatusTransition() {
        // Given
        CmfOrderStatus initialStatus = CmfOrderStatus.INIT;
        CmfOrderStatus processingStatus = CmfOrderStatus.PROCESSING;
        CmfOrderStatus finalStatus = CmfOrderStatus.SUCCESSFUL;

        // Then - 验证状态流转的逻辑顺序
        assertThat(initialStatus.ordinal()).isLessThan(processingStatus.ordinal());
        assertThat(processingStatus.ordinal()).isLessThan(finalStatus.ordinal());
    }

    @Test
    @DisplayName("测试业务场景-终态状态识别")
    void testBusinessScenario_FinalStatusIdentification() {
        // When & Then
        assertThat(isFinalStatus(CmfOrderStatus.SUCCESSFUL)).isTrue();
        assertThat(isFinalStatus(CmfOrderStatus.FAILED)).isTrue();
        assertThat(isFinalStatus(CmfOrderStatus.INIT)).isFalse();
        assertThat(isFinalStatus(CmfOrderStatus.PROCESSING)).isFalse();
        assertThat(isFinalStatus(CmfOrderStatus.UNKNOWN)).isFalse();
    }

    @Test
    @DisplayName("测试业务场景-成功状态识别")
    void testBusinessScenario_SuccessStatusIdentification() {
        // When & Then
        assertThat(isSuccessStatus(CmfOrderStatus.SUCCESSFUL)).isTrue();
        assertThat(isSuccessStatus(CmfOrderStatus.FAILED)).isFalse();
        assertThat(isSuccessStatus(CmfOrderStatus.INIT)).isFalse();
        assertThat(isSuccessStatus(CmfOrderStatus.PROCESSING)).isFalse();
        assertThat(isSuccessStatus(CmfOrderStatus.UNKNOWN)).isFalse();
    }

    @Test
    @DisplayName("测试枚举集合操作")
    void testEnumCollectionOperations() {
        // Given
        java.util.Set<CmfOrderStatus> finalStatuses = java.util.EnumSet.of(
            CmfOrderStatus.SUCCESSFUL, 
            CmfOrderStatus.FAILED
        );
        
        java.util.Set<CmfOrderStatus> processingStatuses = java.util.EnumSet.of(
            CmfOrderStatus.INIT, 
            CmfOrderStatus.PROCESSING
        );

        // Then
        assertThat(finalStatuses).hasSize(2);
        assertThat(processingStatuses).hasSize(2);
        assertThat(finalStatuses).doesNotContain(CmfOrderStatus.INIT);
        assertThat(processingStatuses).doesNotContain(CmfOrderStatus.SUCCESSFUL);
    }

    // 辅助方法
    private String getStatusDescription(CmfOrderStatus status) {
        switch (status) {
            case INIT:
                return "订单已初始化";
            case PROCESSING:
                return "订单处理中";
            case SUCCESSFUL:
                return "订单成功";
            case FAILED:
                return "订单失败";
            case UNKNOWN:
                return "订单状态未知";
            default:
                return "未定义状态";
        }
    }

    private boolean isFinalStatus(CmfOrderStatus status) {
        return status == CmfOrderStatus.SUCCESSFUL || status == CmfOrderStatus.FAILED;
    }

    private boolean isSuccessStatus(CmfOrderStatus status) {
        return status == CmfOrderStatus.SUCCESSFUL;
    }
}
