package com.uaepay.cmf.common.core.domain.enums;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * NotifyStatus枚举单元测试
 * 测试通知状态枚举的所有功能
 */
class NotifyStatusTest {

    @Test
    @DisplayName("测试枚举值的存在性")
    void testEnumValues() {
        // Then
        assertThat(NotifyStatus.values()).isNotEmpty();
        assertThat(NotifyStatus.values()).contains(
            NotifyStatus.INIT,
            NotifyStatus.PROCESSING,
            NotifyStatus.SUCCESSFUL,
            NotifyStatus.FAILED
        );
    }

    @Test
    @DisplayName("测试枚举值数量")
    void testEnumCount() {
        // Then
        assertThat(NotifyStatus.values()).hasSize(4);
    }

    @Test
    @DisplayName("测试INIT状态")
    void testInitStatus() {
        // Given
        NotifyStatus status = NotifyStatus.INIT;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("INIT");
        assertThat(status.toString()).isEqualTo("INIT");
    }

    @Test
    @DisplayName("测试PROCESSING状态")
    void testProcessingStatus() {
        // Given
        NotifyStatus status = NotifyStatus.PROCESSING;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("PROCESSING");
        assertThat(status.toString()).isEqualTo("PROCESSING");
    }

    @Test
    @DisplayName("测试SUCCESSFUL状态")
    void testSuccessfulStatus() {
        // Given
        NotifyStatus status = NotifyStatus.SUCCESSFUL;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("SUCCESSFUL");
        assertThat(status.toString()).isEqualTo("SUCCESSFUL");
    }

    @Test
    @DisplayName("测试FAILED状态")
    void testFailedStatus() {
        // Given
        NotifyStatus status = NotifyStatus.FAILED;

        // Then
        assertThat(status).isNotNull();
        assertThat(status.name()).isEqualTo("FAILED");
        assertThat(status.toString()).isEqualTo("FAILED");
    }

    @Test
    @DisplayName("测试valueOf方法")
    void testValueOf() {
        // Then
        assertThat(NotifyStatus.valueOf("INIT")).isEqualTo(NotifyStatus.INIT);
        assertThat(NotifyStatus.valueOf("PROCESSING")).isEqualTo(NotifyStatus.PROCESSING);
        assertThat(NotifyStatus.valueOf("SUCCESSFUL")).isEqualTo(NotifyStatus.SUCCESSFUL);
        assertThat(NotifyStatus.valueOf("FAILED")).isEqualTo(NotifyStatus.FAILED);
    }

    @Test
    @DisplayName("测试valueOf方法-无效值")
    void testValueOf_InvalidValue() {
        // Then
        assertThatThrownBy(() -> NotifyStatus.valueOf("INVALID"))
            .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    @DisplayName("测试valueOf方法-null值")
    void testValueOf_NullValue() {
        // Then
        assertThatThrownBy(() -> NotifyStatus.valueOf(null))
            .isInstanceOf(NullPointerException.class);
    }

    @Test
    @DisplayName("测试枚举比较")
    void testEnumComparison() {
        // Given
        NotifyStatus status1 = NotifyStatus.INIT;
        NotifyStatus status2 = NotifyStatus.INIT;
        NotifyStatus status3 = NotifyStatus.PROCESSING;

        // Then
        assertThat(status1).isEqualTo(status2);
        assertThat(status1).isNotEqualTo(status3);
        assertThat(status1 == status2).isTrue();
        assertThat(status1 == status3).isFalse();
    }

    @Test
    @DisplayName("测试枚举序号")
    void testEnumOrdinal() {
        // Then
        assertThat(NotifyStatus.INIT.ordinal()).isEqualTo(0);
        assertThat(NotifyStatus.PROCESSING.ordinal()).isEqualTo(1);
        assertThat(NotifyStatus.SUCCESSFUL.ordinal()).isEqualTo(2);
        assertThat(NotifyStatus.FAILED.ordinal()).isEqualTo(3);
    }

    @Test
    @DisplayName("测试枚举在switch语句中的使用")
    void testEnumInSwitch() {
        // Given & When & Then
        for (NotifyStatus status : NotifyStatus.values()) {
            String description = getStatusDescription(status);
            assertThat(description).isNotNull();
            assertThat(description).isNotEmpty();
        }
    }

    @Test
    @DisplayName("测试业务场景-通知状态流转")
    void testBusinessScenario_NotifyStatusTransition() {
        // Given
        NotifyStatus initialStatus = NotifyStatus.INIT;
        NotifyStatus processingStatus = NotifyStatus.PROCESSING;
        NotifyStatus successStatus = NotifyStatus.SUCCESSFUL;
        NotifyStatus failedStatus = NotifyStatus.FAILED;

        // Then - 验证状态流转的逻辑顺序
        assertThat(initialStatus.ordinal()).isLessThan(processingStatus.ordinal());
        assertThat(processingStatus.ordinal()).isLessThan(successStatus.ordinal());
        assertThat(processingStatus.ordinal()).isLessThan(failedStatus.ordinal());
    }

    @Test
    @DisplayName("测试业务场景-终态状态识别")
    void testBusinessScenario_FinalStatusIdentification() {
        // When & Then
        assertThat(isFinalStatus(NotifyStatus.SUCCESSFUL)).isTrue();
        assertThat(isFinalStatus(NotifyStatus.FAILED)).isTrue();
        assertThat(isFinalStatus(NotifyStatus.INIT)).isFalse();
        assertThat(isFinalStatus(NotifyStatus.PROCESSING)).isFalse();
    }

    @Test
    @DisplayName("测试业务场景-成功状态识别")
    void testBusinessScenario_SuccessStatusIdentification() {
        // When & Then
        assertThat(isSuccessStatus(NotifyStatus.SUCCESSFUL)).isTrue();
        assertThat(isSuccessStatus(NotifyStatus.FAILED)).isFalse();
        assertThat(isSuccessStatus(NotifyStatus.INIT)).isFalse();
        assertThat(isSuccessStatus(NotifyStatus.PROCESSING)).isFalse();
    }

    @Test
    @DisplayName("测试业务场景-失败状态识别")
    void testBusinessScenario_FailureStatusIdentification() {
        // When & Then
        assertThat(isFailureStatus(NotifyStatus.FAILED)).isTrue();
        assertThat(isFailureStatus(NotifyStatus.SUCCESSFUL)).isFalse();
        assertThat(isFailureStatus(NotifyStatus.INIT)).isFalse();
        assertThat(isFailureStatus(NotifyStatus.PROCESSING)).isFalse();
    }

    @Test
    @DisplayName("测试枚举集合操作")
    void testEnumCollectionOperations() {
        // Given
        java.util.Set<NotifyStatus> finalStatuses = java.util.EnumSet.of(
            NotifyStatus.SUCCESSFUL, 
            NotifyStatus.FAILED
        );
        
        java.util.Set<NotifyStatus> processingStatuses = java.util.EnumSet.of(
            NotifyStatus.INIT, 
            NotifyStatus.PROCESSING
        );

        // Then
        assertThat(finalStatuses).hasSize(2);
        assertThat(processingStatuses).hasSize(2);
        assertThat(finalStatuses).doesNotContain(NotifyStatus.INIT);
        assertThat(processingStatuses).doesNotContain(NotifyStatus.SUCCESSFUL);
    }

    @Test
    @DisplayName("测试业务场景-通知重试逻辑")
    void testBusinessScenario_NotifyRetryLogic() {
        // Given
        NotifyStatus initStatus = NotifyStatus.INIT;
        NotifyStatus processingStatus = NotifyStatus.PROCESSING;
        NotifyStatus failedStatus = NotifyStatus.FAILED;

        // Then - 验证可重试状态
        assertThat(canRetry(initStatus)).isTrue();
        assertThat(canRetry(processingStatus)).isTrue();
        assertThat(canRetry(failedStatus)).isTrue();
        assertThat(canRetry(NotifyStatus.SUCCESSFUL)).isFalse();
    }

    @Test
    @DisplayName("测试业务场景-通知状态变更权限")
    void testBusinessScenario_StatusChangePermission() {
        // Given & When & Then
        // INIT可以转换到PROCESSING
        assertThat(canTransitionTo(NotifyStatus.INIT, NotifyStatus.PROCESSING)).isTrue();
        
        // PROCESSING可以转换到SUCCESSFUL或FAILED
        assertThat(canTransitionTo(NotifyStatus.PROCESSING, NotifyStatus.SUCCESSFUL)).isTrue();
        assertThat(canTransitionTo(NotifyStatus.PROCESSING, NotifyStatus.FAILED)).isTrue();
        
        // 终态不能转换到其他状态
        assertThat(canTransitionTo(NotifyStatus.SUCCESSFUL, NotifyStatus.FAILED)).isFalse();
        assertThat(canTransitionTo(NotifyStatus.FAILED, NotifyStatus.SUCCESSFUL)).isFalse();
        
        // 但失败状态可以重新开始
        assertThat(canTransitionTo(NotifyStatus.FAILED, NotifyStatus.INIT)).isTrue();
    }

    @Test
    @DisplayName("测试业务场景-通知优先级")
    void testBusinessScenario_NotifyPriority() {
        // Given
        java.util.List<NotifyStatus> statusList = java.util.Arrays.asList(
            NotifyStatus.FAILED,
            NotifyStatus.INIT,
            NotifyStatus.SUCCESSFUL,
            NotifyStatus.PROCESSING
        );

        // When - 按优先级排序（失败 > 处理中 > 初始 > 成功）
        statusList.sort((s1, s2) -> getPriority(s2) - getPriority(s1));

        // Then
        assertThat(statusList.get(0)).isEqualTo(NotifyStatus.FAILED);
        assertThat(statusList.get(1)).isEqualTo(NotifyStatus.PROCESSING);
        assertThat(statusList.get(2)).isEqualTo(NotifyStatus.INIT);
        assertThat(statusList.get(3)).isEqualTo(NotifyStatus.SUCCESSFUL);
    }

    // 辅助方法
    private String getStatusDescription(NotifyStatus status) {
        switch (status) {
            case INIT:
                return "通知初始化";
            case PROCESSING:
                return "通知处理中";
            case SUCCESSFUL:
                return "通知成功";
            case FAILED:
                return "通知失败";
            default:
                return "未定义状态";
        }
    }

    private boolean isFinalStatus(NotifyStatus status) {
        return status == NotifyStatus.SUCCESSFUL || status == NotifyStatus.FAILED;
    }

    private boolean isSuccessStatus(NotifyStatus status) {
        return status == NotifyStatus.SUCCESSFUL;
    }

    private boolean isFailureStatus(NotifyStatus status) {
        return status == NotifyStatus.FAILED;
    }

    private boolean canRetry(NotifyStatus status) {
        return status != NotifyStatus.SUCCESSFUL;
    }

    private boolean canTransitionTo(NotifyStatus from, NotifyStatus to) {
        switch (from) {
            case INIT:
                return to == NotifyStatus.PROCESSING;
            case PROCESSING:
                return to == NotifyStatus.SUCCESSFUL || to == NotifyStatus.FAILED;
            case FAILED:
                return to == NotifyStatus.INIT; // 允许重试
            case SUCCESSFUL:
                return false; // 成功状态不能转换
            default:
                return false;
        }
    }

    private int getPriority(NotifyStatus status) {
        switch (status) {
            case FAILED:
                return 4; // 最高优先级
            case PROCESSING:
                return 3;
            case INIT:
                return 2;
            case SUCCESSFUL:
                return 1; // 最低优先级
            default:
                return 0;
        }
    }
}
