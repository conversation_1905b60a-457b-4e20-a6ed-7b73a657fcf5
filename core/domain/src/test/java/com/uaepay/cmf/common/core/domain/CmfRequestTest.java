package com.uaepay.cmf.common.core.domain;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Date;

import static org.assertj.core.api.Assertions.*;

/**
 * CmfRequest单元测试
 * 测试支付请求对象的所有功能
 */
class CmfRequestTest {

    private CmfRequest cmfRequest;

    @BeforeEach
    void setUp() {
        cmfRequest = new CmfRequest();
    }

    @Test
    @DisplayName("测试默认构造函数")
    void testDefaultConstructor() {
        // When
        CmfRequest request = new CmfRequest();

        // Then
        assertThat(request).isNotNull();
        assertThat(request.isCanRetry()).isFalse(); // boolean默认值为false
    }

    @Test
    @DisplayName("测试支付流水号的getter和setter")
    void testPaymentSeqNo() {
        // Given
        String paymentSeqNo = "PAY123456789";

        // When
        cmfRequest.setPaymentSeqNo(paymentSeqNo);

        // Then
        assertThat(cmfRequest.getPaymentSeqNo()).isEqualTo(paymentSeqNo);
    }

    @Test
    @DisplayName("测试结算ID的getter和setter")
    void testSettlementId() {
        // Given
        String settlementId = "SETTLE123456";

        // When
        cmfRequest.setSettlementId(settlementId);

        // Then
        assertThat(cmfRequest.getSettlementId()).isEqualTo(settlementId);
    }

    @Test
    @DisplayName("测试canRetry标志的getter和setter")
    void testCanRetry() {
        // Given
        boolean canRetry = true;

        // When
        cmfRequest.setCanRetry(canRetry);

        // Then
        assertThat(cmfRequest.isCanRetry()).isTrue();
    }

    @Test
    @DisplayName("测试canRetry标志-false值")
    void testCanRetry_False() {
        // Given
        boolean canRetry = false;

        // When
        cmfRequest.setCanRetry(canRetry);

        // Then
        assertThat(cmfRequest.isCanRetry()).isFalse();
    }

    @Test
    @DisplayName("测试创建时间的getter和setter")
    void testGmtCreate() {
        // Given
        Date gmtCreate = new Date();

        // When
        cmfRequest.setGmtCreate(gmtCreate);

        // Then
        assertThat(cmfRequest.getGmtCreate()).isEqualTo(gmtCreate);
    }

    @Test
    @DisplayName("测试最后修改时间的getter和setter")
    void testGmtModified() {
        // Given
        Date gmtModified = new Date();

        // When
        cmfRequest.setGmtModified(gmtModified);

        // Then
        assertThat(cmfRequest.getGmtModified()).isEqualTo(gmtModified);
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // Given
        cmfRequest.setPaymentSeqNo("PAY123");
        cmfRequest.setSettlementId("SETTLE123");
        cmfRequest.setCanRetry(true);

        // When
        String result = cmfRequest.toString();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isNotEmpty();
        assertThat(result).contains("CmfRequest");
    }

    @Test
    @DisplayName("测试完整请求对象创建")
    void testCompleteRequestCreation() {
        // Given
        String paymentSeqNo = "PAY_TEST_123";
        String settlementId = "SETTLE_TEST_123";
        boolean canRetry = true;
        Date now = new Date();

        // When
        cmfRequest.setPaymentSeqNo(paymentSeqNo);
        cmfRequest.setSettlementId(settlementId);
        cmfRequest.setCanRetry(canRetry);
        cmfRequest.setGmtCreate(now);
        cmfRequest.setGmtModified(now);

        // Then
        assertThat(cmfRequest.getPaymentSeqNo()).isEqualTo(paymentSeqNo);
        assertThat(cmfRequest.getSettlementId()).isEqualTo(settlementId);
        assertThat(cmfRequest.isCanRetry()).isTrue();
        assertThat(cmfRequest.getGmtCreate()).isEqualTo(now);
        assertThat(cmfRequest.getGmtModified()).isEqualTo(now);
    }

    @Test
    @DisplayName("测试null值处理")
    void testNullValues() {
        // When
        cmfRequest.setPaymentSeqNo(null);
        cmfRequest.setSettlementId(null);
        cmfRequest.setGmtCreate(null);
        cmfRequest.setGmtModified(null);

        // Then
        assertThat(cmfRequest.getPaymentSeqNo()).isNull();
        assertThat(cmfRequest.getSettlementId()).isNull();
        assertThat(cmfRequest.getGmtCreate()).isNull();
        assertThat(cmfRequest.getGmtModified()).isNull();
    }

    @Test
    @DisplayName("测试空字符串处理")
    void testEmptyStrings() {
        // When
        cmfRequest.setPaymentSeqNo("");
        cmfRequest.setSettlementId("");

        // Then
        assertThat(cmfRequest.getPaymentSeqNo()).isEmpty();
        assertThat(cmfRequest.getSettlementId()).isEmpty();
    }

    @Test
    @DisplayName("测试长字符串处理")
    void testLongStrings() {
        // Given
        String longString = "A".repeat(1000);

        // When
        cmfRequest.setPaymentSeqNo(longString);
        cmfRequest.setSettlementId(longString);

        // Then
        assertThat(cmfRequest.getPaymentSeqNo()).hasSize(1000);
        assertThat(cmfRequest.getSettlementId()).hasSize(1000);
    }

    @Test
    @DisplayName("测试时间边界值")
    void testDateBoundaryValues() {
        // Given
        Date pastDate = new Date(0); // 1970-01-01
        Date futureDate = new Date(Long.MAX_VALUE);

        // When
        cmfRequest.setGmtCreate(pastDate);
        cmfRequest.setGmtModified(futureDate);

        // Then
        assertThat(cmfRequest.getGmtCreate()).isEqualTo(pastDate);
        assertThat(cmfRequest.getGmtModified()).isEqualTo(futureDate);
    }

    @Test
    @DisplayName("测试对象相等性")
    void testObjectEquality() {
        // Given
        CmfRequest request1 = new CmfRequest();
        request1.setPaymentSeqNo("PAY123");
        request1.setSettlementId("SETTLE123");
        request1.setCanRetry(true);

        CmfRequest request2 = new CmfRequest();
        request2.setPaymentSeqNo("PAY123");
        request2.setSettlementId("SETTLE123");
        request2.setCanRetry(true);

        CmfRequest request3 = new CmfRequest();
        request3.setPaymentSeqNo("PAY456");
        request3.setSettlementId("SETTLE456");
        request3.setCanRetry(false);

        // Then - 注意：由于使用了Lombok，这里测试的是Lombok生成的equals方法
        assertThat(request1).isEqualTo(request2);
        assertThat(request1).isNotEqualTo(request3);
        assertThat(request1.hashCode()).isEqualTo(request2.hashCode());
    }

    @Test
    @DisplayName("测试业务场景-重试标志切换")
    void testBusinessScenario_RetryFlagToggle() {
        // Given
        cmfRequest.setPaymentSeqNo("PAY_RETRY_TEST");
        cmfRequest.setCanRetry(false);

        // When - 模拟业务场景：初始不允许重试，后来允许重试
        assertThat(cmfRequest.isCanRetry()).isFalse();
        
        cmfRequest.setCanRetry(true);
        
        // Then
        assertThat(cmfRequest.isCanRetry()).isTrue();
    }

    @Test
    @DisplayName("测试业务场景-时间戳更新")
    void testBusinessScenario_TimestampUpdate() {
        // Given
        Date createTime = new Date();
        cmfRequest.setGmtCreate(createTime);
        
        // When - 模拟业务场景：创建后修改
        try {
            Thread.sleep(10); // 确保时间差异
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Date modifyTime = new Date();
        cmfRequest.setGmtModified(modifyTime);

        // Then
        assertThat(cmfRequest.getGmtCreate()).isEqualTo(createTime);
        assertThat(cmfRequest.getGmtModified()).isEqualTo(modifyTime);
        assertThat(cmfRequest.getGmtModified()).isAfter(cmfRequest.getGmtCreate());
    }
}
