package com.uaepay.cmf.common.core.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <p>支付请求.</p>
 * <AUTHOR> won
 * @version $Id: CmfRequest.java, v 0.1 2010-12-27 下午12:51:55 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class CmfRequest {
    /** 支付流水号 */
    private String paymentSeqNo;
    /** 结算id */
    private String settlementId;
    /** 是否请允许提交 */
    private boolean canRetry;
    /** 创建时间 */
    private Date   gmtCreate;
    /** 最后修改时间 */
    private Date   gmtModified;

}
