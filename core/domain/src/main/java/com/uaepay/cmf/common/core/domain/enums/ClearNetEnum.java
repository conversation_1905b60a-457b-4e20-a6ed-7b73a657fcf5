package com.uaepay.cmf.common.core.domain.enums;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ClearNetEnum.java v1.0
 */
public enum ClearNetEnum {
    //
    LOCAL("local", "国内"),
    SWIFT("swift", "SWIFT"),
    FEDWIRE("fed_wire", "FEDWIRE");

    private String code;
    private String description;

    ClearNetEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static ClearNetEnum getByCode(String code){
        for(ClearNetEnum item: values()){
            if(item.getCode().equals(code)){
                return item;
            }
        }
        return null;
    }

}
