package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <p>风险标识</p>
 * <AUTHOR>
 * @version $Id: RiskFlag.java, v 0.1 2012-8-13 下午1:42:51 liumaoli Exp $
 */
@Getter
public enum RiskFlag {
    
    NON_RISK("nonRisk", "无风险"),

    BANK_CHECK("bankCheck", "银行结果校验有风险"),

    RMS_CHECK("rmsCheck", "风控校验有风险");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    RiskFlag(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static RiskFlag getByCode(String code) {

        for (RiskFlag type : RiskFlag.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}