package com.uaepay.cmf.common.core.domain.institution;

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.NotifyStatus;
import com.uaepay.cmf.common.core.domain.enums.OrderFlag;
import com.uaepay.cmf.common.core.domain.enums.SourceCode;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <p>机构控制订单</p>
 *
 * <AUTHOR>
 * @version $Id: InstControlOrder.java, v 0.1 2012-8-3 下午2:16:49 fuyangbiao Exp $
 */
@Data
@ToString(callSuper = true)
public class InstControlOrder extends InstBaseOrder {
    private static final long serialVersionUID = 5330252480636085828L;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 请求号
     */
    private String requestNo;
    /**
     * 原请求号
     */
    private String preRequestNo;
    /**
     * 原结算Id
     */
    private String preSettlementId;
    /**
     * 请求类型
     */
    private ControlRequestType requestType;
    /**
     * 原机构订单号
     */
    private String preInstOrderNo;
    /**
     * 接口类型
     */
    private FundChannelApiType apiType;
    /**
     * 通知前端结果状态
     */
    private NotifyStatus notifyStatus;
    /**
     * 　数据来源
     */
    private SourceCode sourceCode;
    /**
     * 通讯状态
     */
    private CommunicateStatus communicateStatus;
    /**
     * 数据标志
     */
    private OrderFlag flag;
    /**
     * 重试次数
     */
    private Integer retryTimes;
    /**
     * 商户id
     */
    private String merchantId;
    /**
     * 下次补单时间
     */
    private Date gmtNextRetry;


    /**
     * 是否更新原订单
     */
    private boolean updatePreOrder;

}
