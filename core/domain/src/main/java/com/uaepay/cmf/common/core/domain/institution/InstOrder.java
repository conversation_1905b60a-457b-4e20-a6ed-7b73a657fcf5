package com.uaepay.cmf.common.core.domain.institution;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <p>
 * 提交到机构订单
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: InstOrder.java, v 0.1 2010-12-22 下午03:55:01 sean won Exp $
 */
@Data
@ToString(callSuper = true)
public class InstOrder extends InstBaseOrder {
    private static final long serialVersionUID = -5033275770930095508L;

    /**
     * 机构订单对应cmf订单
     */
    protected CmfOrder cmfOrder;
    /**
     * 机构订单ID
     */
    protected Long instOrderId;
    /**
     * 订单类型
     */
    protected BizType bizType;
    /**
     * 渠道接口
     */
    protected String fundChannelApi;
    /**
     * 支付编码
     */
    protected String paymentCode;
    /**
     * 补单次数
     */
    private Integer retryTimes = 0;
    /**
     * 下次补单时间
     */
    private Date gmtNextRetry;
    /**
     * 执行标志
     */
    private OrderFlag flag;
    /**
     * 通讯类型
     */
    protected InstOrderCommunicateType communicateType;
    /**
     * 通讯状态
     */
    protected CommunicateStatus communicateStatus;
    /**
     * 归档批次ID
     */
    protected Long archiveBatchId;
    /**
     * 归档状态
     */
    protected InstOrderArchiveStatus archiveStatus;
    /**
     * 预计提交时间
     */
    protected Date gmtBookingSubmit;
    /**
     * 提交时间
     */
    protected Date gmtSubmit;
    /**
     * 风险订单状态
     */
    private OrderRiskStatus riskStatus;
    /**
     * 路由版本
     */
    private int routeVersion;
    /**
     * cmf订单号
     */
    private String cmfSeqNo;
    /**
     * 是否拆分
     */
    private YesNo isSplit;
    /**
     * 是否推进
     */
    private IsAdvance isAdvance;
    /**
     * 商户id
     */
    private String merchantId;
    /**
     * 订单发送状态
     */
    private InstOrderSendType sendType;

    @Override
    public InstOrder clone() throws CloneNotSupportedException {
        return (InstOrder) super.clone();
    }

}
