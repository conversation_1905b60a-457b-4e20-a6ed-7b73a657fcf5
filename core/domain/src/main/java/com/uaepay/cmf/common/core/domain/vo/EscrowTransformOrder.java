package com.uaepay.cmf.common.core.domain.vo;

import com.uaepay.common.util.money.Money;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date EscrowTransformOrder.java v1.0
 */
@Data
public class EscrowTransformOrder implements Serializable {
    private static final long serialVersionUID = -7212841822623902013L;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 会员id
     */
    private String memberId;
    /**
     * 订单金额
     */
    private Money amount;
    /**
     * 交易状态 S-成功,I-处理中,F-失败
     */
    private String transStatus;
    /**
     * 交易时间
     */
    private Date transTime;
    /**
     * 交易类型 I-入款,B-退款
     */
    private String transType;
    /**
     * 报送类型 split-拆单
     */
    private String reportType;
    /**
     * 业务产品码
     */
    private String bizProductCode;
    /**
     * 备注
     */
    private String memo;

}
