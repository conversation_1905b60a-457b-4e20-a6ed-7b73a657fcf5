package com.uaepay.cmf.common.core.domain.common;

/**
 * <p>属性与扩展映射关系</p>
 * <AUTHOR>
 * @version $Id: PropertyExtensionMapping.java, v 0.1 2012-8-7 下午5:13:51 fuyangbiao Exp $
 */
public interface PropertyExtensionMapping {
    /**
     * 获取扩展信息键值
     * @return
     */
    String getExtensionKey();

    /**
     * 获取属性名称
     * @return
     */
    String getPropertyName();

    /**
     * 获取对象CLASS
     * @return
     */
    Class<?> getObjectClass();
}
