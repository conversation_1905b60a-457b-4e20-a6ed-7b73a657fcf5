package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * 返回PE/通知counter订单类型
 * <AUTHOR> won
 * @version $Id: ReturnResultNo.java, v 0.1 2011-11-9 下午06:14:38 sean won Exp $
 */
@Getter
public enum ReturnResultNoEnum {
	//
	INST_SEQ_NO("instSeq", "银行返回机构流水号"),
    //退款回传入款订单号
	ORGI_ORDER_NO("orgiOrder","入款机构订单号")
	;
	/** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    ReturnResultNoEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static ReturnResultNoEnum getByCode(String code) {

        for (ReturnResultNoEnum type : ReturnResultNoEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }
    
}
