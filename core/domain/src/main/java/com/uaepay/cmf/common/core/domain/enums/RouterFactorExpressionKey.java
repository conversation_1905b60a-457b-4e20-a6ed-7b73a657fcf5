package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 *
 * <p>路由要素表达式类型</p>
 * <AUTHOR>
 * @version $Id: RouterFactorExpressionKey.java, v 0.1 2012-10-22 下午2:31:54 liumaoli Exp $
 */
@Getter
public enum RouterFactorExpressionKey {
    //
    AMOUNT("amount","金额"),
    CHANNEL_INFO("channel","资金源信息"),
    API_INFO("api","资金源信息"); 
    
    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    RouterFactorExpressionKey(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static RouterFactorExpressionKey getByCode(String code) {

        for (RouterFactorExpressionKey type : RouterFactorExpressionKey.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
