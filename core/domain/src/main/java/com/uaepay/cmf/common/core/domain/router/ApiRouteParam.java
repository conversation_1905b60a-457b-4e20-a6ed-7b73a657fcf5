package com.uaepay.cmf.common.core.domain.router;

import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <p>路由API</p>
 *
 * <AUTHOR>
 * @date ApiRouteParam.java v1.0  2020-09-08 16:26
 */
@Data
@Builder
public class ApiRouteParam {

    private String channelCode;

    private String apiType;

    private boolean genOrderInfo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
