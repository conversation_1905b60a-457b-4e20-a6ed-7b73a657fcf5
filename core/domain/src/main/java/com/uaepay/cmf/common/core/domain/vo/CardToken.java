package com.uaepay.cmf.common.core.domain.vo;

import com.uaepay.cmf.common.core.domain.enums.TokenTypeEnum;
import com.uaepay.schema.cmf.enums.YesNo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardToken.java v1.0  2020-03-28 15:45
 */
@Getter
@Setter
@ToString(callSuper = true)
public class CardToken implements Serializable, Cloneable {

    private static final long serialVersionUID = 5989615871837146287L;
    /**
     * 卡tokenId
     */
    private String cardTokenId;
    /**
     * 机构订单id
     */
    private Long instOrderId;
    /**
     * token类型
     */
    private TokenTypeEnum tokenType;
    /**
     * 会员id
     */
    private String memberId;
    /**
     * 会员卡id
     */
    private Long cardId;
    /**
     * 受益人id
     */
    private Long beneficiaryId;
    /**
     * 支付-sessionId
     */
    private String sessionId;
    /**
     * 目标机构
     */
    private String instCode;
    /**
     * 发卡行
     */
    private String issueBank;
    /**
     * 发卡行名称
     */
    private String issueBankName;
    /**
     * 借记贷记: DC/CC/GC
     */
    private String dbcr;
    /**
     * 对公对私
     */
    private String companyOrPersonal;
    /**
     * 3ds
     */
    private String is3DS;
    /**
     * 首次绑定
     */
    private YesNo firstBind;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 国家编码
     */
    private String countryCode;
    /**
     * 持卡人
     */
    private String cardHolder;
    /**
     * 机构卡tokenId
     */
    private String instTokenId;
    /**
     * 卡有效期
     */
    private String cardExpired;
    /**
     * 卡类型-DC/CC
     */
    private String cardType;
    /**
     * 卡品牌 VISA/MASTERCARD
     */
    private String cardBrand;
    /**
     * 是否需要csc验证
     */
    private String needCsc;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * iban
     */
    private String iban;
    /**
     * 卡账号
     */
    private String cardAccountNo;
    /**
     * 结果url
     */
    private String resultUrl;
    /**
     * 扩展参数
     */
    private String extension;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
}
