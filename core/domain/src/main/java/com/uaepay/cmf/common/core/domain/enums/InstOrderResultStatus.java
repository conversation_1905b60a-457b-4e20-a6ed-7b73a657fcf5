package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>机构订单-状态</p>
 *
 * <AUTHOR> won
 * @version $Id: InstOrderResultStatus.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum InstOrderResultStatus {
    // 'S','F','I','N','U'
    IN_PROCESS("I", "处理中"),

    SUCCESSFUL("S", "成功"),

    HALF_SUCCESSFUL("H","处理完成，待成功确认"),

    RISK("R", "成功但有风险"),

    FAILURE("F", "失败"),

    CANCEL("C", "待撤销"),

    //用于退款和出款查询失败时使用
    NONEXISTS("N", "不存在"),

    UNKNOWN("U", "状态未知"),

    REDIRECT_VERIFY("V", "跳转verify")
    ;

    private static final String COMPATIBLE_IN_PROCESS = "A";
    private static final String COMPATIBLE_UN_KNOWN = "Q";

    /**
     * 代码`
     */
    private final String code;
    /**
     * 信息
     */
    private final String message;

    /**
     * 构造
     *
     * @param code
     * @param message
     */
    InstOrderResultStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     *
     * @param code
     * @return
     */
    public static InstOrderResultStatus getByCode(String code) {

        for (InstOrderResultStatus type : InstOrderResultStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        // 兼容老状态
        if (COMPATIBLE_IN_PROCESS.equals(code)) {
            return IN_PROCESS;
        }
        if (COMPATIBLE_UN_KNOWN.equals(code)) {
            return UNKNOWN;
        }

        return null;
    }

    public boolean isFinalResult() {
        return InstOrderResultStatus.SUCCESSFUL == this || InstOrderResultStatus.FAILURE == this;
    }

}
