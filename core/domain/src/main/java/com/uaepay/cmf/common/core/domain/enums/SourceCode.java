package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * 
 * <p>数据请求来源</p>
 * <AUTHOR>
 * @version $Id: SourceCode.java, v 0.1 2012-10-8 下午1:09:57 liumaoli Exp $
 */
@Getter
public enum SourceCode {
    //
    MAS("MAS", "MAS请求"),
    PE("PE", "PE请求"),
    MGS("MGS", "MGS请求");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    SourceCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static SourceCode getByCode(String code) {

        for (SourceCode type : SourceCode.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
