package com.uaepay.cmf.common.core.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BindCardInfo.java v1.0
 */
@Data
public class BindCardInfo implements Serializable {
    private static final long serialVersionUID = -9211070685043545530L;

    private Long cardId;
    /**
     * 目标机构
     */
    private String instCode;
    /**
     * 发卡行
     */
    private String issueBank;
    /**
     * 发卡行名称
     */
    private String issueBankName;
    /**
     * 渠道编号
     */
    private String channelCode;
    /**
     * 请求号
     */
    private String requestNo;
    /**
     * 机构订单号
     */
    private String instOrderNo;
    /**
     * 会员id
     */
    private String memberId;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 国家编码
     */
    private String countryCode;
    /**
     * 持卡人
     */
    private String cardHolder;
    /**
     * 卡品牌
     */
    private String cardBrand;
    /**
     * 机构Token
     */
    private String instTokenId;
    /**
     * eci状态
     */
    private String eciStatus;
    /**
     * 卡类型- DC/CC
     */
    private String cardType;
    /**
     * 卡分类- TOKEN/COMMON
     */
    private String cardCategory;
    /**
     * 有效期
     */
    private String expiredDate;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 统一结果
     */
    private String unityResultCode;
    /**
     * 银行返回原因
     */
    private String message;
    /**
     * 扩展参数
     */
    private String extension;

}
