package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>扩展键值对中key的取值</p>
 *
 * <AUTHOR> won
 * @version $Id: ExtensionKey.java, v 0.1 2010-12-27 下午06:48:32 sean won Exp $
 */
@Getter
public enum ExtensionKey {
    //公共部分
    SOURCE_CODE("sourceCode"), //请求来源标识.
    //支付流水号，PE传入
    PAYMENT_SEQ_NO("paymentSeqNo"),
    MEMBER_ID("memberId"),
    COMPANY_OR_PERSONAL("companyOrPersonal"),
    IBAN("iban"),
    IS_3DS("is3DS"),
    NEED_CSC("needCsc"),

    CARD_BRAND("cardBrand"),
    ORGI_INST_SEQ_NO("orgiInstSeqNo"),//入款银行返回流水号
    PRODUCT_ORDER_NO("productOrderNo"),
    FC_CODE("fcCode"),//fund channel code
    PAYMENT_ORDER_NO("paymentOrderNo"),
    //出款部分
    ACCOUNT_NAME("accountName"),
    CARD_ACCOUNT_NO("cardAccountNo"),
    PARTNER_ID("partnerId"),
    PURPOSE("purpose"),
    PAYEE_ID("payeeId"),
    PAYER_ID("payerId"),
    MEMO("memo"),//备注
    CARD_TOKEN("cardToken"),
    CARD_TOKEN_ID("cardTokenId"),
    MEMBER_TOKENS("memberTokens"),
    ORG_TOKEN("orgToken"),
    INST_TOKEN_ID("instTokenId"),
    SVA_STATUS("svaStatus"),

    //充退部分
    ORGI_FUNDIN_ORDER_NO("orgiFundinOrderNo"), //原始PE订单号
    ORGI_SETTLEMENT_ID("orgiSettlementId"), //原始PE结算ID
    SETTLEMENT_ID("settlementId"), //结算ID

    GATE_ORDER_NO("gateOrderNo"), //交易服务订单号

    IS_CANCEL("isCancel"),//是否交易撤销
    INNER_FUNDOUT_TYPE("innerFundoutType"),
    TARGET_AMOUNT("targetAmount"),
    TARGET_CURRENCY("targetCurrency"),

    //入款部分 --- 公共
    INST_ORDER_NO("instOrderNo"), //由CMF产生的机构订单号，发给渠道，若是有风险，还会发给风控系统.
    INST_ORDER_TYPE("instOrderType"), //由CMF产生的机构订单号，发给渠道，若是有风险，还会发给风控系统.
    PRE_INST_ORDER_NO("preInstOrderNo"),
    INST_AMOUNT("instAmount"),//支付金额
    CARD_TYPE("cardType"),//卡类型
    CARD_TYPE_UNDERLINE("card_type"), // 卡类型，下划线格式
    DBCR("DBCR"),//借记,贷记
    IP_ADDRESS("ipAddress"),
    MOBILENO("mobileNo"), //用户手机号
    FILE_LIST("fileList"),
    //B2C
    PAGE_URL("pageUrl"), //B2C入款用
    APPLE_WEB_SIGNATURE("signature"), // apple web session使用
    PAGE_URL_FOR_SIGN("PAGE_URL"), //B2C入款用,不能用小写,给OPS用
    RETURN_URL("returnUrl"),
    FORM_3DS2("FORM_3DS2"),
    INST_ORDER_TOKEN("instOrderToken"),
    ACCESS_CHANNEL("accessChannel"), //B2C使用，手机B2C。

    FUNDS_CHANNEL("fundsChannel"), //CMF自己使用. PE传入的原始渠道编号

    //产品码，PE传入
    PRODUCT_CODE("productCode"),
    //业务产品码
    BIZ_PRODUCT_CODE("bizProductCode"),

    //渠道交易时间, 毫秒数
    CHANNEL_TRANS_TIME("channelTransTime"),

    RESULT_SUB_MESSAGE("resultSubMessage"),

    /**
     * 出款卡号
     */
    CARD_NO("cardNo"),
    CARD_HOLDER("cardHolder"),
    EXPIRED_DATE("expiredDate"),
    COUNTRY_CODE("countryCode"),
    ID_TYPE("idType"),
    ID_NO("idNo"),
    SIGN_NO("signNo"),
    SIGN_DATE("signDate"),
    NAME("name"),
    VERIFY_TIMES("verifyTimes"),
    SIGN_URL("signUrl"),

    TRANSFER_BALANCE("transferBalance"),
    IS_BANK_RISK("isBankRisk"),
    EBANK_CHARSET("ebankCharset"),
    UNITY_RESULT_CODE("unityResultCode"),
    UNITY_RESULT_MESSAGE("unityResultMessage"),
    WHITE_CHANNEL_CODE("whiteChannelCode"),
    BLACK_CHANNEL_LIST("blackChannelList"),

    SOURCE_ORDER("sourceOrder"),
    INST_SEQ_NO("instSeqNo"),
    TOPAY_MERCHANT_ID("topayMerchantId"),
    TOPAY_MERCHNAT_NAME("topayMerchantName"),

    BANK_FORM_KEY("bankFormKey"),
    TRADE_VOUCHER_NOS("tradeVoucherNos"),
    ARRIVAL_AMOUNT("arrivalAmount"),
    ARRIVAL_CURRENCY("arrivalCurrency"),

    TRANSACTION_TYPE( "transactionType"),
    PAYLOAD( "payLoad"),

    OperationType("opType"),
    API_RESULT_CODE("apiResultCode"),
    API_RESULT_SUB_CODE("apiResultSubCode"),

    ECI("eci"),
    FRICTIONLESS("frictionless"),
    SIGNED_SOURCE("signedSource"),

    BIZ_TYPE("bizType"),

    ORI_AMOUNT("oriAmount"),
    ORI_CURRENCY("oriCurrency"),

    // merchant config version
    MERCHANT_CONFIG_VERSION("merchantConfigVersion"),

    MERCHANT_ONBOARDING_ID("onboardingId"),

    TRANSFORM_ACCOUNT_ACCESS("allowViaWallet"),
    TRANSFORM_FLAG("viaWallet"),


    CARD_METADATA("cardMetadata"),
    ;

    ExtensionKey(String key) {
        this.key = key;
    }

    public final String key;

}


