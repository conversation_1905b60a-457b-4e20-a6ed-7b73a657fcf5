package com.uaepay.cmf.common.core.domain.institution;

import com.uaepay.schema.cmf.enums.CardType;
import com.uaepay.schema.cmf.enums.CompanyOrPersonal;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <p>
 * 出款订单
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: CmfOrder.java, v 0.1 2010-12-22 下午03:55:01 sean won Exp $
 */
@Data
@ToString(callSuper = true)
public class InstFundoutOrder extends InstOrder {
    private static final long serialVersionUID = -1272362708833577638L;
    /**
     * 银行代码
     */
    private String bankCode;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 开户行支行名
     */
    private String bankBranch;
    /**
     * 电子联行号
     */
    private String bankBranchCode;
    /**
     * 银行所属省份
     */
    private String bankProvince;
    /**
     * 银行所属城市
     */
    private String bankCity;
    /**
     * 地区代码
     */
    private String areaCode;
    /**
     * 账户类型
     */
    private CompanyOrPersonal companyOrPersonal;
    /**
     * 开户名称
     */
    private String accountName;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 帐号
     */
    private String accountNo;
    /**
     * iban号
     */
    private String ibanNo;
    /**
     * 银行交换码
     */
    private String swiftCode;
    /**
     * 受益人地址
     */
    private String beneficiaryAddress;
    /**
     * 中间行
     */
    private String intermediaryBank;
    /**
     * 卡类型
     */
    private CardType cardType;
    /**
     * 协议号
     */
    private String agreementNo;
    /**
     * 期望到帐时间
     */
    private Date expectTime;
    /**
     * 用途
     */
    private String purpose;
    /**
     * 通行证
     */
    private String ptId;

    private String payoutAccount;

    public void setCompanyOrPersonal(String companyOrPersonalStr) {
        this.companyOrPersonal = CompanyOrPersonal.getByCode(companyOrPersonalStr);
    }

    public void setCompanyOrPersonal(CompanyOrPersonal companyOrPersonal) {
        this.companyOrPersonal = companyOrPersonal;
    }

    public void setCardType(String cardType) {
        this.cardType = CardType.getByCode(cardType);
    }

    public void setCardType(CardType cardType) {
        this.cardType = cardType;
    }

    @Override
    public InstFundoutOrder clone() throws CloneNotSupportedException {
        return (InstFundoutOrder) super.clone();
    }

}
