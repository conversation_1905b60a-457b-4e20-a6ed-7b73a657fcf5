package com.uaepay.cmf.common.core.domain.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <p>系统配置</p>
 * <AUTHOR> won
 * @version $Id: SysConfiguration.java, v 0.1 2011-6-13 上午09:13:26 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysConfiguration implements Serializable{


    private static final long serialVersionUID = -5483579041583113776L;
    private String attrName;
    private String attrValue;
    private String memo;
    private Date   gmtCreated;
    private Date   gmtModified;

    public SysConfiguration() {
        super();
    }

    public SysConfiguration(String attrName, String attrValue, String memo) {
        super();
        this.attrName = attrName;
        this.attrValue = attrValue;
        this.memo = memo;
    }

}
