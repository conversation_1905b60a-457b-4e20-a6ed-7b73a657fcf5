package com.uaepay.cmf.common.core.domain.vo;

import com.uaepay.common.domain.Extension;
import com.uaepay.schema.cmf.enums.SuccessFailure;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <p>
 * 状态核对结果.
 * </p>
 *
 * <AUTHOR>
 * @version OrderStatusCheckVO.java 1.0 @2015/7/21 20:18 $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class OrderStatusCheckVO {

    private String         instOrderNo;

    private String         amount;

    private String         accountName;

    private String         accountNo;

    private Date           bankDate;

    private Date           cmfDate;

    private String         fundChannelCode;

    private SuccessFailure bankStatus;

    private SuccessFailure cmfStatus;

    private String         memo;

    private Extension      extension;

}
