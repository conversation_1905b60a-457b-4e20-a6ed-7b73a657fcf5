package com.uaepay.cmf.common.core.domain.institution;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version InstCommonOrder.java 1.0 Created@2017-12-04 23:12 $
 */
@Data
public class InstCommonOrder implements Cloneable, Serializable, BasicConstant {
    private static final long serialVersionUID = -818511060382511923L;
    /**
     * 资金渠道代码
     */
    protected String fundChannelCode;
    /**
     * 接口类型
     */
    protected FundChannelApiType apiType;
    /**
     * 创建时间
     */
    protected Date gmtCreate;
    /**
     * 最后修改时间
     */
    protected Date gmtModified;
    /**
     * 备注
     */
    protected String memo;
    /**
     * 扩展信息
     */
    private Map<String, String> extension = new HashMap<>();

    @Override
    public InstCommonOrder clone() throws CloneNotSupportedException {
        return (InstCommonOrder) super.clone();
    }
}
