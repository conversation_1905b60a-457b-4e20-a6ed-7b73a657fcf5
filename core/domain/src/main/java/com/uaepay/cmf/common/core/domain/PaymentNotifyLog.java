package com.uaepay.cmf.common.core.domain;

import java.util.Date;

import com.uaepay.schema.cmf.enums.SuccessFailure;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>支付结果通知日志</p>
 *
 * <AUTHOR> won
 * @version $Id: PaymentNotifyLog.java, v 0.1 2010-12-22 下午03:55:01 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class PaymentNotifyLog {
    /**
     * 通知日志ID
     */
    private long notifyLogId;
    /**
     * 渠道流水号
     */
    private String channelSeqNo;
    /**
     * 通知结果
     */
    private SuccessFailure notifyResult;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 备注
     */
    private String memo;
}
