package com.uaepay.cmf.common.core.domain.exception;

import com.uaepay.cmf.common.core.domain.enums.ErrorCode;

/**
 * <p>错误状态异常.</p>
 *
 * <AUTHOR>
 * @version WrongStateException.java 1.0 @2016/1/4 17:16 $
 */
public class WrongStateException extends AppCheckedException {
    
	private static final long serialVersionUID = 4222756819476461443L;

	public WrongStateException(ErrorCode errorCode) {
        super(errorCode);
    }

    public WrongStateException(ErrorCode errorCode,String message) {
        super(errorCode,message);
    }

    public WrongStateException(ErrorCode errorCode,String message, Throwable cause) {
        super(errorCode,message, cause);
    }

    public WrongStateException(Throwable cause) {
        super(cause);
    }
}
