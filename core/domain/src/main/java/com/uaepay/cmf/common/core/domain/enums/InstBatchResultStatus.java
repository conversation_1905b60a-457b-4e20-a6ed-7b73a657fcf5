package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>
复核状态：A（待处理）；U（复核不通过）；P（复核通过）；D（废弃）；F（处理结束）
结果状态：A（待处理）；I（处理中）；F（处理结束）
 * </p>
 * <AUTHOR> won
 * @version $Id: InstOrderResultStatus.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 *
 */
@Getter
public enum InstBatchResultStatus {
    //
	AWAIT("A", "待处理"),
	UNSUCCESS("U", "复核不通过"),
	PASS("P", "复核通过"),
	DELETE("D","作废"),
	ING("I","处理中"),
	FINSH("F","处理结束");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    InstBatchResultStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static InstBatchResultStatus getByCode(String code) {

        for (InstBatchResultStatus type : InstBatchResultStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

    public boolean isFinished() {
        return this == InstBatchResultStatus.FINSH;
    }
}
