package com.uaepay.cmf.common.core.domain.constants;

import com.uaepay.common.util.money.Money;

import java.math.BigDecimal;

/**
 * <p>基础常量定义</p>
 *
 * <AUTHOR> won
 * @version $Id: BasicConstant.java, v 0.1 2010-12-22 下午04:39:41 sean won Exp $
 */
public interface BasicConstant {
    /**
     * 文件相关编码
     */
    String FILE_ENCODE = "GBK";
    /**
     * 零金额字符串
     */
    String ZERO_MONEY_STRING = "0.0";
    /**
     * 默认币种
     */
    String DEFAULT_CURRENCY = "AED";
    /**
     * 零金额
     */
    Money ZERO_MONEY = new Money(ZERO_MONEY_STRING, "AED");
    /**
     * 0
     */
    BigDecimal ZERO = new BigDecimal("0");
    /**
     * 分隔符
     */
    String SPLIT_TAG = "-";
    /**
     * 逗号
     */
    String CHAR_COMMA = ",";
    /**
     * 分号
     */
    String CHAR_SEMICOLON = ";";
    /**
     * 空格
     */
    String CHAR_BLANK_SPACE = " ";
    /**
     * 竖线
     */
    String CHAR_VERTICAL_LINE = "|";
    /**
     * ^
     */
    String CHAR_CARET = "^";
    /**
     * 等号
     */
    String CHAR_EQUAL = "=";
    /**
     * 左斜杠
     */
    String SPLASH = "/";
    /**
     * 返回码， 0表示成功
     */
    Integer SUCCESS = 0;
    /**
     * 返回码， -1表示失败
     */
    Integer FAILURE = -1;
    /**
     * 请求地址前缀
     */
    String PREFIX_API_URI = "http://";

    String POSFIX_HTML = "/notify.html";

    String INNER_ERROR_CODE = "cmf.inner.error";

    String ERROR_COMM_CODE = "channel.comm.error";
    /**
     * 渠道通讯异常信息
     */
    String ERROR_COMM = "发送渠道通讯异常";
    /**
     * 银行回调页面地址
     */
    String PAGE_URL = "pageUrl";
    /**
     * 银行回调后台地址
     */
    String SERVER_URL = "serverUrl";
    /**
     * 原订单标志
     */
    String SOURCE_ORDER_INST = "inst";
    Money SPLIT_MIN_AMOUNT = new Money("20000.00", "AED");

    int NEVER_EXPIRE = 0;

    int ONE_HOUR_SECONDS = 60 * 60;

    int THIRTY_MINUTE_SECONDS = 30 * 60;

    int FIFTEEN_MINUTE_SECONDS = 15 * 60;

    String DEFAULT = "default";

    int DEFAULT_SEND_AWATING_MAX_SIZE = 500;
    String SEND_AWATING_MAX_SIZE = "SEND_AWATING_MAX_SIZE";

    int DEFAULT_SINGLE_QUERY_MAX_SIZE = 500;
    String SINGLE_QUERY_MAX_SIZE = "SINGLE_QUERY_MAX_SIZE";
    String CONTROL_QUERY_MAX_SIZE = "CONTROL_QUERY_MAX_SIZE";
    String BATCH_QUERY_MAX_SIZE = "BATCH_QUERY_MAX_SIZE";

    String SYSTEM_BUSY = "系统繁忙,请稍后重试";

    String SUCCESS_MSG = "cmf.success";

    String TARGET_INST = "targetInst";

    Long TIME_OUT_MILLIS = 15000L;

    String DEFAULT_OPERATOR = "system";

    int DEFAULT_RETRY_TIMES = -1;

    int MINUTES_ONE_DAY = 1440;

    String ENCTYPE = "enctype";

    String EMPTY_STRING = "";

    /**
     * 需要放入一个线程逐笔处理退款的资金渠道列表（用,分割）
     */
    String REFUND_IN_SINGLE_THREAD_CHANNELS = "REFUND_IN_SINGLE_THREAD_CHANNELS";


    String REFUND_ORDER_IGNORE_MINUTES = "REFUND_ORDER_IGNORE_MINUTES";

    int DEFAULT_REFUND_ORDER_IGNORE_MINUTES = 300;

    String ANONYMOUS_MEMBER_ID = "anonymousMember";

    String STAR = "*";

    String X = "x";

    String CMF_EXCHANGE_NOTIFY = "exchange.cmf.notify";

    String CLIENT_ID = "cmf";

    String NEED_NOTIFY = "needNotify";

    String INNER_RETURN_FLAG = "innerReturnFlag";

    String DEFAULT_SUCCESS_CODE = "cmf.inner.success";
    String DEFAULT_SUCCESS_SUB_CODE = "not.send.channel";

    String DEFAULT_COUNTRY_CODE = "AE";

    String CACHE_NAMESPACE_CHANNEL_CONFIG = "ChannelConfig";
    String CACHE_NAMESPACE_ACS_CONFIG = "AcsConfig";

    String VERIFY_PARAM = "VERIFY_PARAM";
    String CALLBACK_TYPE = "CALLBACK_TYPE";
    String QUERY_STRING = "QUERY_STRING";

    String CMF_INNER_EXCHANGE = "cmf.inner.exchange";

    String CMF_INNER_VOID_QUEUE = "queue.voidTransaction";

    String QUEUE_VOID_TRANSACTION_ROUTING_KEY = "exchange.queue.voidTransaction";

    String TRANSACTION_TYPE_RESERVATION = "reservation";

    String FISERV_CHANNEL_CODE ="FS104";

    String PRE_AUTH_UPDATE_CODE = "preauth-update";

    String PRE_AUTH_COMPLETE_CODE= "preauth-complete";

    String SIGN_TRANSACTION_ID = "signTransactionId";

    String RESPONSE_DATE = "responseData";

    String HEADER_MAP = "headerMap";

    String APLUS = "APLUS";

    String REQUEST_NO = "requestNo";


    String ACCOUNT_ID = "accountId";

     String THRESHOLD = "threshold";

     String ACCOUNT_BALANCE = "accountBalance";

    String CACHE_NAMESPACE_CHANNEL_CODE_MAPPING = "ChannelCodeMapping";
}
