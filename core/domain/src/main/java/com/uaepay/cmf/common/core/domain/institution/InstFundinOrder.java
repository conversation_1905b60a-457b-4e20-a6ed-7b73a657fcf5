package com.uaepay.cmf.common.core.domain.institution;

import com.uaepay.schema.cmf.enums.CardType;
import lombok.Data;
import lombok.ToString;

/**
 * <p>入款订单</p>
 *
 * <AUTHOR> won
 * @version $Id: CmfOrder.java, v 0.1 2010-12-22 下午03:55:01 sean won Exp $
 */
@Data
@ToString(callSuper = true)
public class InstFundinOrder extends InstOrder {
    private static final long serialVersionUID = -5709513848868609032L;
    /**
     * 卡类型
     */
    private CardType cardType;
    /**
     * 付款机构
     */
    private String payerInstCode;
    /**
     * 协议号
     */
    private String contractNo;

    /**
     * 克隆
     */
    @Override
    public InstFundinOrder clone() throws CloneNotSupportedException {
        return (InstFundinOrder) super.clone();
    }

}
