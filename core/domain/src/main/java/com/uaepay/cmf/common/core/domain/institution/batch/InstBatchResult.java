package com.uaepay.cmf.common.core.domain.institution.batch;

import com.uaepay.cmf.common.core.domain.enums.InstBatchResultStatus;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 机构批次处理结果
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: InstBatchResult.java, v 0.1 2010-12-22 下午03:55:01 sean won Exp
 * $
 */
@Data
@ToString(callSuper = true)
public class InstBatchResult extends InstBaseResult {
    private static final long serialVersionUID = -2368822760433735900L;
    /**
     * 批次结果ID
     */
    private Long batchResultId;
    /**
     * 归档批次ID
     */
    private Long archiveBatchId;
    /**
     * 订单类型
     */
    private BizType bizType;
    /**
     * 实际金额
     */
    private Money realAmount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
    /**
     * 机构状态
     */
    private InstBatchResultStatus batchStatus;
    /**
     * 返回时间
     */
    private Date gmtReturn;
    /**
     * 总笔数
     */
    private Long totalCount;
    /**
     * 总金额
     */
    private Money totalAmount;
    /**
     * 成功笔数
     */
    private Long successCount;
    /**
     * 成功金额
     */
    private Money successAmount;
    /**
     * 失败笔数
     */
    private Long failedCount;
    /**
     * 失败金额
     */
    private Money failedAmount;
    /**
     * 差异笔数
     */
    private Long differentCount;
    /**
     * 差异金额
     */
    private Money differentAmount;
    /**
     * 缺少笔数
     */
    private Long lessCount;
    /**
     * 超出笔数
     */
    private Long moreCount;
    /**
     * 文件路径
     */
    private String filePath;

    private List<InstOrderResult> instOrderResults = new ArrayList<>();

    public void setRealAmount(Money realAmount) {
        if (realAmount == null) {
            this.realAmount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
        } else {
            this.realAmount = realAmount;
        }
    }

}
