package com.uaepay.cmf.common.core.domain.vo;

import com.uaepay.cmf.common.core.domain.enums.SvaAccountEnum;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date GrcLimitCarrier.java v1.0
 */
@Data
public class GrcLimitCarrier implements Serializable {

    private static final long serialVersionUID = -1602365116809147587L;
    /**
     * 业务产品码
     */
    private String bizProductCode;
    /**
     * 支付订单号
     */
    private String paymentOrderNo;
    /**
     * 支付时间
     */
    private Date gmtCreate;
    /**
     * 支付后
     */
    private boolean afterPay;
    /**
     * 是否支付
     */
    private boolean isTopUp;
    /**
     * 交易类型
     */
    private BizType bizType;
    /**
     * sva账户是否存在，是否为vip
     */
    private SvaAccountEnum svaAccount;
    /**
     * 金额
     */
    private Money amount;
    /**
     * 会员id
     */
    private String memberId;
}
