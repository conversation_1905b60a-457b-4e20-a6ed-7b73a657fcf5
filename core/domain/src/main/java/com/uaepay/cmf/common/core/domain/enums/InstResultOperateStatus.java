package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>订单结果操作状态</p>
 * <AUTHOR> won
 * @version $Id: InstResultOperateStatus.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum InstResultOperateStatus {
    //
    AWAITING("A", "待处理"),

    IN_PROCESS("I", "处理中"),

    SUCCESSFUL("S", "成功"),

    FAILURE("F", "失败");

    /**
     * 代码
     */
    private final String code;
    /**
     * 信息
     */
    private final String message;

    /**
     * 构造
     *
     * @param code
     * @param message
     */
    InstResultOperateStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     *
     * @param code
     * @return
     */
    public static InstResultOperateStatus getByCode(String code) {

        for (InstResultOperateStatus type : InstResultOperateStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}