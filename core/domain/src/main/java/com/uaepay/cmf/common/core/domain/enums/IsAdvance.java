package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>标记订单推进状态.</p>
 *
 * <AUTHOR>
 * @version IsAdvance.java 1.0 @2015/3/20 14:19 $
 */
@Getter
public enum IsAdvance {
    YES("Y", "推进订单"),
    NO("N", "非推进订单"),
    //针对推进订单而言
    HAS("H", "订单已推进");

    /**
     * 代码
     */
    private final String code;
    /**
     * 信息
     */
    private final String message;

    IsAdvance(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static IsAdvance getByCode(String code) {
        for (IsAdvance isAdvance : values()) {
            if (isAdvance.getCode().equals(code)) {
                return isAdvance;
            }
        }
        return null;
    }

}