package com.uaepay.cmf.common.core.domain.institution;

import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderType;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import lombok.Data;
import lombok.ToString;

/**
 * <p>提交机构基础订单</p>
 *
 * <AUTHOR>
 * @version $Id: InstBaseOrder.java, v 0.1 2012-8-21 上午10:50:57 fuyangbiao Exp $
 */
@Data
@ToString(callSuper = true)
public class InstBaseOrder extends InstCommonOrder {

    private static final long serialVersionUID = 7292034599890342594L;
    /**
     * 提交机构订单号
     */
    protected String instOrderNo;
    /**
     * 机构流水订单号
     */
    protected String instSeqNo;
    /**
     * 目标机构代码
     */
    protected String instCode;
    /**
     * 支付模式
     */
    private PayMode payMode;
    /**
     * 产品编码
     */
    protected String productCode;
    /**
     * 金额
     */
    protected Money amount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
    /**
     * 状态
     */
    protected InstOrderStatus status;
    /**
     * 机构订单类型
     */
    protected InstOrderType instOrderType;

    @Override
    public InstBaseOrder clone() throws CloneNotSupportedException {
        return (InstBaseOrder) super.clone();
    }
}
