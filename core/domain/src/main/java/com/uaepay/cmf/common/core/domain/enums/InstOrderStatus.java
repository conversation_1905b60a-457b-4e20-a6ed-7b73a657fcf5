package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>机构订单-状态</p>
 * <AUTHOR> won
 * @version $Id: InstOrderStatus.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum InstOrderStatus {
    //
    IN_PROCESS("I", "处理中"),

    SUCCESSFUL("S", "成功"),

    HALF_SUCCESSFUL("H","处理成功，待成功确认"),

    RISK("R", "成功但有风险"),

    FAILURE("F", "失败"),

    CANCEL("C", "撤销"),

    REDIRECT_VERIFY("V", "跳转verify")

    ;

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    InstOrderStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static InstOrderStatus getByCode(String code) {

        for (InstOrderStatus type : InstOrderStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

    public boolean isFinalStatus(){
       return (this == InstOrderStatus.SUCCESSFUL ||  this ==InstOrderStatus.FAILURE);
    }

}
