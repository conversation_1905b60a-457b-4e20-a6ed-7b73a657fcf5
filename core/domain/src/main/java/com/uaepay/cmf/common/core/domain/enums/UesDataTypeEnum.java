package com.uaepay.cmf.common.core.domain.enums;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date UesDataTypeEnum.java v1.0
 */
public enum UesDataTypeEnum {
    //
    NAME("name", "名字"),
    CARD_NO("card_no", "卡号"),
    EXPIRED_DATE("expire_date", "过期日期"),
    IBAN("iban", "iban");

    private String code;
    private String description;

    UesDataTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }


}
