package com.uaepay.cmf.common.core.domain.enums;

import com.uaepay.basis.beacon.service.facade.enums.base.CodeMessageEnum;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date SvaAccountEnum.java v1.0
 */
public enum SvaAccountEnum implements CodeMessageEnum {

    HAS_SVA("Y", "有sva账户"),
    NO_SVA("N", "无sva账户"),
    VIP("V", "vip用户");

    private final String code;

    private final String message;

    SvaAccountEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    public static SvaAccountEnum getByCode(String code) {
        for (SvaAccountEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

}
