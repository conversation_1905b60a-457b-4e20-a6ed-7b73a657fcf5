package com.uaepay.cmf.common.core.domain.exception;

import com.uaepay.cmf.common.core.domain.enums.ErrorCode;

/**
 * 
 * <p>通讯连接异常</p>
 * <AUTHOR> won
 * @version $Id: CommunicateException.java, v 0.1 2010-12-30 上午09:40:35 sean won Exp $
 */
public class CommunicateException extends RuntimeException {

    private static final long serialVersionUID = -3178853059992439281L;
    
    public CommunicateException() {
        super();
    }

    public CommunicateException(String message) {
        super(message);
    }

    public CommunicateException(String message, Throwable cause) {
        super(message, cause);
    }

    public CommunicateException(Throwable cause) {
        super(cause);
    }
    
    ErrorCode errorCode = ErrorCode.COMMUNICATE_ERROR;

    public String getCode() {
        return this.errorCode.getErrorCode();
    }

}
