package com.uaepay.cmf.common.core.domain.institution;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <p>机构基础结果</p>
 *
 * <AUTHOR>
 * @version $Id: InstBaseResult.java, v 0.1 2012-8-21 上午10:52:21 fuyangbiao Exp $
 */
@Data
@ToString(callSuper = true)
@Slf4j
public class InstBaseResult implements Serializable, BasicConstant {
    private static final long serialVersionUID = 9150689954950257297L;
    /**
     * 机构订单号
     */
    protected String instOrderNo;
    /**
     * 资金渠道代码
     */
    protected String fundChannelCode;
    /**
     * 接口类型
     */
    protected FundChannelApiType apiType;
    /**
     * 结果状态
     */
    protected InstOrderResultStatus status;
    /**
     * 统一结果编码
     */
    protected String instResultCode;
    /**
     * 结果信息
     */
    protected String resultMessage;
    /**
     * 渠道API结果码
     */
    protected String apiResultCode;
    /**
     * 渠道API结果子码
     */
    protected String apiResultSubCode;
    /**
     * 扩展信息
     */
    private Map<String, String> extension = new HashMap<>();
    /**
     * 创建时间
     */
    protected Date gmtCreate;
    /**
     * 最后修改时间
     */
    protected Date gmtModified;
    /**
     * 备注
     */
    protected String memo;
    /**
     * 机构订单处理状态。该字段表示CMF与外围系统交互时的状态,不须持久化到数据库.
     */
    private InstOrderProcessStatus processStatus;

    /**
     * 判断是否使用返回码
     *
     * @return
     */
    public boolean isReturnCodeRefacted() {
        return !StringUtils.isBlank(this.getApiResultCode()) && this.getApiType() != null;
    }
}
