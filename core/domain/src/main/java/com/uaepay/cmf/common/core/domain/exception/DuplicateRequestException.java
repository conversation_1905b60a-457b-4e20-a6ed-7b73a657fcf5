package com.uaepay.cmf.common.core.domain.exception;

import com.uaepay.cmf.common.core.domain.enums.ErrorCode;

/**
 * <p>重复请求异常.</p>
 *
 * <AUTHOR> won
 * @version $Id: DuplicateKeyException.java, v 0.1 2010-6-5 下午08:51:37 sean won Exp $
 */
public class DuplicateRequestException extends AppCheckedException {

    private static final long serialVersionUID = 1L;

    public DuplicateRequestException() {
        super();
    }

    public DuplicateRequestException(String message, Throwable cause) {
        super(message, cause);
    }

    public DuplicateRequestException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        super.errorCode = errorCode;
    }

}
