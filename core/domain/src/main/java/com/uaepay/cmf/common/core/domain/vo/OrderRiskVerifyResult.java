package com.uaepay.cmf.common.core.domain.vo;

import java.util.Map;

import com.uaepay.cmf.common.core.domain.enums.OrderRiskStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>风控结果.</p>
 *
 * <AUTHOR>
 * @version RiskOrderVerifyResult.java 1.0 Created@2016-12-14 17:27 $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class OrderRiskVerifyResult {
    /** 风控状态 */
    private OrderRiskStatus     riskOrderStatus;
    /** 风控返回信息 */
    private String              rmsMsg;
    /** 结果参数*/
    private Map<String, Object> extMap;

    public OrderRiskVerifyResult(OrderRiskStatus riskOrderStatus) {
        this.riskOrderStatus = riskOrderStatus;
    }

    public OrderRiskVerifyResult(OrderRiskStatus riskOrderStatus, String rmsMsg) {
        this.riskOrderStatus = riskOrderStatus;
        this.rmsMsg = rmsMsg;
    }

    public OrderRiskVerifyResult(OrderRiskStatus riskOrderStatus, String rmsMsg, Map<String, Object> extMap) {
        this.riskOrderStatus = riskOrderStatus;
        this.rmsMsg = rmsMsg;
        this.extMap = extMap;
    }
}
