package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

import java.text.MessageFormat;

/**
 *
 * <p>Error Code 枚举类</p>
 * <AUTHOR> won
 * @version $Id: ErrorCode.java, v 0.1 2010-12-30 上午09:27:53 sean won Exp $
 */
@Getter
public enum ErrorCode {

    //状态机异常
    RUNTIME_EXCEPTION("cmf.sys.exception","runtime exception"),

    COMMUNICATE_ERROR("cmf.sys.comm_err","communicate failed"),
    VALIDATE_ERROR("cmf.sys.validate_err","validate exists exception"),

    ROUTE_ERROR_NO_VALID_CHANNEL("cmf.route.fail","路由失败,无可用渠道"),
    ROUTER_ERROR_AMOUNT_NOT_MATCH("cmf.route.amount_exceed","路由失败，限额校验失败"),
    ROUTER_ERROR_BANK_NOT_SUPPORT("cmf.route.unsupported_bank","路由失败，目标机构不支持"),
    ROUTER_ERROR_CARD_TYPE_NOT_MATCH("cmf.route.unsupported_drcr","路由失败，卡借/贷类型不支持"),
    ROUTER_ERROR_COMPANY_OR_PERSONAL_NOT_MATCH("cmf.route.unsupported_bc","路由失败，对公/对私卡不支持"),
    ROUTER_ERROR_ACCESS_CHANNEL_NOT_MATCH("cmf.route.unsupported_access","路由失败，访问类型不支持"),
    ROUTER_ERROR_FC_API_NOT_SUPPORT("cmf.route.err_api","渠道接口不支持"),
    ROUTER_ERROR_TRANSFORM_AMOUNT_EXCEED("cmf.route.transform_amount_exceed","路由失败，拆单金额超限"),
    ROUTER_ERROR_ONBOARD_IN_PROCESSING("cmf.route.onboard.in_processing","merchant, store, or device is during onboarding"),
    ROUTER_ERROR_CHANNEL_ACCOUNT_BALANCE_NOT_ENOUGH("cmf.route.balance.not_enough","channel account balance not enough"),
    CMF_SYSTEM_ERROR("cmf.system.err", "cmf系统异常"),

    SUCCESS("cmf.success", "交易成功"),
    FAIL("cmf.failed", "交易失败"),

    // 下面的不对外报
    FUND_VERIFICATION_ERROR("cmf.sys.err_return_no", "渠道返回信息与原机构订单信息不匹配"),
    PARSE_ERROR("cmf.sys.err_parse_file","解析文件报错"),
    GROOVY_EXECUTE_ERROR("cmf.sys.err_groovy","groovy脚本执行出错"),
    ROUTER_ERROR_PRODUCT_CODE_NOT_SUPPORT("cmf.route.err_product_code","渠道产品码不支持"),
    ROUTER_ERROR_CARD_NO_NOT_SUPPORT("cmf.route.err_card","渠道卡不支持"),
    ROUTER_ERROR_MERCHANT_NOT_SUPPORT("cmf.route.err_merchant","渠道商户不支持"),
    ROUTER_ERROR_CARD_NOT_VERIFY("cmf.route.err_card_status","卡状态未认证"),

    WRONG_ORDER_DUPLICATE_PROCESS("cmf.order.duplicate_process", "订单重复处理"),
    ORDER_NOT_FOUND("cmf.order.not_found", "订单未找到"),

    WRONG_ORDER_STATUS_EXCEPTION("cmf.order.wrong_status", "订单状态异常"),
    UNMATCHED_ORDER_AMOUNT_EXCEPTION("cmf.order.unmatched_amount", "订单金额校验异常"),

    WRONG_STATE_EXCEPTION("cmf.sys.err_status","状态不一致异常"),

    INVALID_PARAM("cmf.invalid.param", "参数不合法"),
    OPERATION_FAIL("cmf.operation.fail", "操作失败"),

    QUERY_BENEFICIARY_INFO_FAIL("cmf.query.beneficiary.fail", "查询受益人信息失败")
    ;

    private String errorCode;
    private String errorMessage;

    public String getFormatErrorMessage(String arg) {
        return getFormatErrorMessage(new String[]{arg});
    }

    public String getFormatErrorMessage(String[] args) {
        MessageFormat form = new MessageFormat(errorMessage);
        return form.format(args);
    }

    ErrorCode(String errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    /**
     * 工厂方法,从指定errorcode值获取对应的枚举
     * @param errorCode
     * @return
     */
    public static ErrorCode getFromErrorCode(String errorCode){
        for (ErrorCode e : ErrorCode.values()) {
            if (e.getErrorCode().equals(errorCode)) {
                return e;
            }
        }
        return null;
    }

}
