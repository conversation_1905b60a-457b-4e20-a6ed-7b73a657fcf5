package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>CMF订单-支付结果通知状态</p>
 * <AUTHOR> won
 * @version $Id: CmfOrderNotifyStatus.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum NotifyStatus {
    //
	FAILURE("F", "通知失败"),

    SUCCESSFUL("S", "通知成功"),

    CHANNEL_CODE_NOTIFY_SUCCESS("C", "通知PE出款渠道成功"),

    NOT_NOTIFY("N", "不通知"),

	AWAITING("A","等待通知"),

	IN_PROCESS("I","通知中");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    NotifyStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static NotifyStatus getByCode(String code) {

        for (NotifyStatus type : NotifyStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
