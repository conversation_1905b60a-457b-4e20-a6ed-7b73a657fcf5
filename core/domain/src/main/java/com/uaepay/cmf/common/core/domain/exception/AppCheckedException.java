package com.uaepay.cmf.common.core.domain.exception;

import com.uaepay.cmf.common.core.domain.enums.ErrorCode;

/**
 * 
 * <p>checked exception 基类</p>
 * <AUTHOR> won
 * @version $Id: AppCheckedException.java, v 0.1 2010-12-30 上午09:36:12 sean won Exp $
 */
public class  AppCheckedException extends Exception {

    private static final long serialVersionUID = 7240166912823355206L;

    public AppCheckedException() {
        super();
    }

    public AppCheckedException(String message) {
        super(message);
    }
    
    public AppCheckedException(ErrorCode errorCode,String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public AppCheckedException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
    }
    
    public AppCheckedException(ErrorCode errorCode,String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public AppCheckedException(String message, Throwable cause) {
        super(message, cause);
    }

    public AppCheckedException(Throwable cause) {
        super(cause);
    }
    
    ErrorCode errorCode = ErrorCode.CMF_SYSTEM_ERROR;
    
    public String getCode(){
        return this.errorCode.getErrorCode();
    }

    @Override
    public String getMessage(){
        return this.errorCode == null ? super.getMessage() : this.errorCode.getErrorMessage();
    }

}
