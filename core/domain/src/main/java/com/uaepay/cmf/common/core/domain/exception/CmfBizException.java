package com.uaepay.cmf.common.core.domain.exception;


import com.uaepay.cmf.common.core.domain.enums.ErrorCode;

/**
 *
 * <p>业务异常</p>
 * <AUTHOR>
 */
public class CmfBizException extends RuntimeException {
    private static final long serialVersionUID = 5558419943675631531L;

    /**
     * 应答码
     */
    private ErrorCode code;

    /**
     * 构造方法
     * @param code
     */
    public CmfBizException(ErrorCode code) {
        super(code.getErrorMessage());
        this.code = code;
    }

    /**
     * 构造方法
     * @param code
     * @param message
     */
    public CmfBizException(ErrorCode code, String message) {
        super(message);
        this.code = code;
    }

    /**
     * 构造方法
     * @param code
     * @param message
     * @param cause
     */
    public CmfBizException(ErrorCode code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public ErrorCode getCode() {
        return code;
    }

    public String getCodeStr() {
        return code.getErrorCode();
    }

    public void setCode(ErrorCode code) {
        this.code = code;
    }
}
