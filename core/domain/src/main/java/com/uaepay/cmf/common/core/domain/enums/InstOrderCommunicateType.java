package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>机构订单-通讯类型</p>
 * <AUTHOR> won
 * @version $Id: InstOrderCommunicateType.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum InstOrderCommunicateType {
    //
    SINGLE("S", "单笔通信"),

    BATCH("B", "批量通信");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    InstOrderCommunicateType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static InstOrderCommunicateType getByCode(String code) {

        for (InstOrderCommunicateType type : InstOrderCommunicateType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
