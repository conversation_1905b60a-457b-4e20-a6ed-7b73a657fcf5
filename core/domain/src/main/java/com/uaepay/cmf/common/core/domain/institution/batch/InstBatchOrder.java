package com.uaepay.cmf.common.core.domain.institution.batch;

import com.uaepay.cmf.common.core.domain.enums.InstOrderArchiveStatus;
import com.uaepay.cmf.common.core.domain.institution.InstCommonOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 机构归档批次表
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: InstBatchOrder.java, v 0.1 2010-12-22 下午03:55:01 sean won Exp
 * $
 */
@Data
@ToString(callSuper = true)
public class InstBatchOrder extends InstCommonOrder {
    private static final long serialVersionUID = -1238148008103533091L;
    /**
     * 归档批次ID
     */
    private long archiveBatchId;
    /**
     * 模板id
     */
    private Long archiveTemplateId;
    /**
     * 订单类型
     */
    private BizType bizType;
    /**
     * 机构批次号
     */
    private String instBatchNo;
    /**
     * 总金额
     */
    private Money amount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
    /**
     * 总笔数
     */
    private Integer totalCount;
    /**
     * 状态
     */
    private InstOrderArchiveStatus status;
    /**
     * 是否已锁定
     */
    private YesNo isLocked;
    /**
     * 归档时间
     */
    private Date gmtArchive;

    private PayMode payMode;
    /**
     * 操作员
     */
    private String operator;
    /**
     * String type fundChannelApi
     */
    private String apiCode;

    private YesNo checkFlag;

    private Integer queryTimes;

    private Date gmtNextRetry;

    private List<InstOrder> instOrderList = new ArrayList<>();

    public void setAmount(Money amount) {
        if (amount == null) {
            this.amount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
        } else {
            this.amount = amount;
        }
    }

}
