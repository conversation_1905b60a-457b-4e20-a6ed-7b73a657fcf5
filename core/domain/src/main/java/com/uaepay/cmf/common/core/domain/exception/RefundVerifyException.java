package com.uaepay.cmf.common.core.domain.exception;

import com.uaepay.cmf.common.core.domain.enums.ErrorCode;

/**
 * 
 * <p>退款校验不通过</p>
 * <AUTHOR> won
 * @version $Id: RefundVerifyException.java, v 0.1 2010-12-30 上午11:35:54 sean won Exp $
 */
public class RefundVerifyException extends AppRuntimeException {
    private static final long serialVersionUID = -3445138243342551342L;

    public RefundVerifyException() {
        super();
    }

    public RefundVerifyException(String message) {
        super(message);
    }

    public RefundVerifyException(String message, Throwable cause) {
        super(message, cause);
    }

    public RefundVerifyException(Throwable cause) {
        super(cause);
    }
    
    ErrorCode errorCode = ErrorCode.FUND_VERIFICATION_ERROR;
    @Override
    public String getCode() {
        return this.errorCode.getErrorCode();
    }
}
