package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>CMF订单-审核状态</p>
 * <AUTHOR> won
 * @version $Id: CmfOrderConfirmStatus.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum CmfOrderConfirmStatus {
    //
    NOT_NEED("N", "不复核"),

    AWAITING("A", "待审核"),

    PASS("P", "通过"),

    REJECT("R", "驳回"),
    
    SUBMIT("S","提交审核"),
    
    FAIL("F","提交审核失败");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    CmfOrderConfirmStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static CmfOrderConfirmStatus getByCode(String code) {

        for (CmfOrderConfirmStatus type : CmfOrderConfirmStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
