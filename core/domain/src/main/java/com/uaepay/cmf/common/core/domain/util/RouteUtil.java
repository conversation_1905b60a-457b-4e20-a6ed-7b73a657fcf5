package com.uaepay.cmf.common.core.domain.util;

import com.uaepay.cmf.common.core.domain.router.ApiRouteParam;
import com.uaepay.cmf.common.enums.FundChannelApiType;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date RouteUtil.java v1.0  2020-09-08 20:07
 */
public class RouteUtil {

    private RouteUtil() {

    }

    public static ApiRouteParam getParam(String channelCode, FundChannelApiType apiType) {
        return getParam(channelCode, apiType, false);
    }

    public static ApiRouteParam getParam(String channelCode, FundChannelApiType apiType, boolean genOrderInfo) {
        notNull(channelCode, "渠道不可为空");
        notNull(apiType, "接口类型不可为空");

        return ApiRouteParam.builder().channelCode(channelCode).apiType(apiType.getCode()).genOrderInfo(genOrderInfo).build();
    }

    private static void notNull(Object obj, String msg) {
        if (obj == null) {
            throw new IllegalArgumentException(msg);
        }
    }


}
