package com.uaepay.cmf.common.core.domain.exception;

import com.uaepay.cmf.common.core.domain.enums.ErrorCode;


/**
 * 
 * <p>解析异常</p>
 * <AUTHOR> won
 * @version $Id: ParseException.java, v 0.1 2011-2-24 下午03:39:30 sean won Exp $
 */
public class ParseException extends AppCheckedException {

    private static final long serialVersionUID = 7154510330494799202L;

    ErrorCode errorCode = ErrorCode.PARSE_ERROR;
    
    public ParseException() {
        super(ErrorCode.PARSE_ERROR);
    }

    public ParseException(String message) {
        super(ErrorCode.PARSE_ERROR,message);
    }

    public ParseException(String message, Throwable cause) {
        super(message, cause);
    }

    public ParseException(Throwable cause) {
        super(cause);
    }
}
