package com.uaepay.cmf.common.core.domain.institution;

import com.uaepay.cmf.common.core.domain.enums.InstResultOperateStatus;
import com.uaepay.cmf.common.core.domain.enums.RiskFlag;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.CardType;
import lombok.Data;
import lombok.ToString;

/**
 * <p>
 * 机构订单结果
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: InstOrderResult.java, v 0.1 2010-12-22 下午03:55:01 sean won Exp
 * $
 */
@Data
@ToString(callSuper = true)
public class InstOrderResult extends InstBaseResult {
    private static final long serialVersionUID = 1283501924028799561L;
    /**
     * 订单结果ID
     */
    private Long resultId;
    /**
     * 机构订单ID
     */
    private Long instOrderId;
    /**
     * 机构流水号
     */
    private String instSeqNo;
    /**
     * 订单类型
     */
    private BizType bizType;
    /**
     * 实际金额
     */
    private Money realAmount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
    /**
     * 卡类型
     */
    private CardType cardType;

    /**
     * 该笔订单结果操作状态
     */
    private InstResultOperateStatus operateStatus;
    /**
     * 对无法获取统一编码的结果[instStatus=Question],重新获取编码时设置的标志
     */
    private boolean isQuestionRetry;
    /**
     * 风险标识
     */
    private RiskFlag riskFlag = RiskFlag.NON_RISK;

    public void setRealAmount(Money realAmount) {
        if (realAmount == null) {
            this.realAmount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
        } else {
            this.realAmount = realAmount;
        }
    }

}
