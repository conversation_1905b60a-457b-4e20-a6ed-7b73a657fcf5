package com.uaepay.cmf.common.core.domain.enums;

/**
 * <p>机构订单类型</p>
 * <AUTHOR>
 * @version $Id: InstOrderType.java, v 0.1 2012-8-21 上午10:37:22 fuyangbiao Exp $
 */
public enum InstOrderType {
    // 资金订单
    FUND,

    // 控制订单
    CONTROL;

    public static InstOrderType getByName(String code){
        for(InstOrderType orderType : values()){
            if(orderType.name().equals(code)){
                return orderType;
            }
        }
        return null;
    }
}
