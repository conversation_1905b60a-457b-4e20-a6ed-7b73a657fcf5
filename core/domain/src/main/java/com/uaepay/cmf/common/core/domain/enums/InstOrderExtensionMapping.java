package com.uaepay.cmf.common.core.domain.enums;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.uaepay.cmf.common.core.domain.common.PropertyExtensionMapping;
import com.uaepay.cmf.common.core.domain.institution.InstFundinOrder;
import com.uaepay.cmf.common.core.domain.institution.InstFundoutOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;

/**
 * <p>机构订单属性特殊指定扩展信息</p>
 *
 * <AUTHOR>
 * @version $Id: SpecialExtension.java, v 0.1 2012-8-7 下午3:03:06 fuyangbiao Exp $
 */
public enum InstOrderExtensionMapping implements PropertyExtensionMapping {
    // 帐户名称
    ACCOUNT_NAME("accountName", "accountName", InstFundoutOrder.class),
    // 卡号
    CARD_NO("cardNo", "cardNo", InstFundoutOrder.class),
    // 帐号
    ACCOUNT_NO("cardAccountNo", "accountNo", InstFundoutOrder.class),
    // iban
    IBAN_NO("iban", "ibanNo", InstFundoutOrder.class),
    // 交换码
    SWIFT_CODE("swiftCode","swiftCode", InstFundoutOrder.class),
    // 受益人地址
    BENEFICIARY_ADDRESS("beneficiaryAddress", "beneficiaryAddress", InstFundoutOrder.class),
    // 中间行
    INTERMEDIARY_BANK("intermediaryBank","intermediaryBank", InstFundoutOrder.class),
    // 对公对私
    COMPANY_OR_PERSONAL("companyOrPersonal", "companyOrPersonal", InstFundoutOrder.class),
    // 地区代码
    AREA_CODE("areaCode", "areaCode", InstFundoutOrder.class),
    // 分支行
    BANK_BRANCH("bankBranch", "bankBranch", InstFundoutOrder.class),
    // 联行号
    BANK_LINE_NO("bankLineNo", "bankBranchCode", InstFundoutOrder.class),
    // 省份
    BANK_PROVINCE("bankProvince", "bankProvince", InstFundoutOrder.class),
    // 城市
    BANK_CITY("bankCity", "bankCity", InstFundoutOrder.class),
    // 银行代码
    BANK_CODE("bankCode", "bankCode", InstFundoutOrder.class),
    // 银行名称
    BANK_NAME("bankName", "bankName", InstFundoutOrder.class),
    // 出款卡类型
    OUT_CARD_TYPE("cardType", "cardType", InstFundoutOrder.class),
    // 用途
    PURPOSE("purpose", "purpose", InstFundoutOrder.class),
    // 付款机构
    PAYER_INST_CODE("payerInstCode", "payerInstCode", InstFundinOrder.class),
    // 协议号
    CONTRACT_NO("contractNo", "contractNo", InstFundinOrder.class),
    // 入款卡类型
    IN_CARD_TYPE("cardType", "cardType", InstFundinOrder.class),

    ;

    /**
     * 扩展键值
     */
    private final String extensionKey;
    /**
     * 属性名称
     */
    private final String propertyName;
    /**
     * 对象类
     */
    private final Class<? extends InstOrder> objectClass;

    /**
     * 缓存扩展键值队
     */
    private static Map<Class<? extends InstOrder>, Set<String>> cachedKeySet = new HashMap<>();
    /**
     * 缓存映射列表
     */
    private static Map<Class<? extends InstOrder>, Set<InstOrderExtensionMapping>> cachedMapping = new HashMap<>();

    /**
     * 构造
     *
     * @param extensionKey
     * @param propertyName
     * @param objectClass
     */
    InstOrderExtensionMapping(String extensionKey, String propertyName,
                              Class<? extends InstOrder> objectClass) {
        this.extensionKey = extensionKey;
        this.propertyName = propertyName;
        this.objectClass = objectClass;
    }

    /**
     * 获取键值队
     *
     * @return
     */
    public static Set<String> getKeySet(Class<? extends InstOrder> instOrderClass) {
        if (cachedKeySet.containsKey(instOrderClass)) {
            return cachedKeySet.get(instOrderClass);
        }

        synchronized (InstOrderExtensionMapping.class) {
            if (cachedKeySet.containsKey(instOrderClass)) {
                return cachedKeySet.get(instOrderClass);
            }

            Set<String> tempSet = new HashSet<>();
            for (InstOrderExtensionMapping extension : InstOrderExtensionMapping.values()) {
                if (instOrderClass.equals(extension.getObjectClass())) {
                    tempSet.add(extension.extensionKey);
                }
            }
            cachedKeySet.put(instOrderClass, tempSet);
            return tempSet;
        }
    }

    /**
     * 获取键值队
     *
     * @return
     */
    public static Set<InstOrderExtensionMapping> getMappingSet(Class<? extends InstOrder> instOrderClass) {
        if (cachedMapping.containsKey(instOrderClass)) {
            return cachedMapping.get(instOrderClass);
        }

        synchronized (InstOrderExtensionMapping.class) {
            if (cachedMapping.containsKey(instOrderClass)) {
                return cachedMapping.get(instOrderClass);
            }

            Set<InstOrderExtensionMapping> tempSet = new HashSet<>();
            for (InstOrderExtensionMapping extension : InstOrderExtensionMapping.values()) {
                if (instOrderClass.equals(extension.getObjectClass())) {
                    tempSet.add(extension);
                }
            }
            cachedMapping.put(instOrderClass, tempSet);
            return tempSet;
        }
    }

    @Override
    public String getExtensionKey() {
        return extensionKey;
    }

    @Override
    public String getPropertyName() {
        return propertyName;
    }

    @Override
    public Class<? extends InstOrder> getObjectClass() {
        return objectClass;
    }
}
