package com.uaepay.cmf.common.core.domain.exception;

import com.uaepay.cmf.common.core.domain.enums.ErrorCode;

/**
 * <p>路由异常.</p>
 *
 * <AUTHOR>
 * @version DateValidateException.java 1.0 @2016/1/4 17:16 $
 */
public class RouteChannelException extends AppCheckedException{

    private static final long serialVersionUID = 6311943434224219868L;
    
    public RouteChannelException(ErrorCode errorCode) {
        super(errorCode);
    }

    public RouteChannelException(ErrorCode errorCode,String message) {
        super(errorCode,message);
    }

    public RouteChannelException(ErrorCode errorCode,String message, Throwable cause) {
        super(errorCode,message, cause);
    }

    public RouteChannelException(Throwable cause) {
        super(cause);
    }
}
