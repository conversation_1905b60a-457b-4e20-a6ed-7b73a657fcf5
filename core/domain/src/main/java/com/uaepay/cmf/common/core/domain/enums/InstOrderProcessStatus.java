package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>机构订单-处理状态</p>
 * 该字段表示CMF与外围系统交互时的状态;不须持久化到数据库.
 * <AUTHOR> won
 * @version $Id: InstOrderResultStatus.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum InstOrderProcessStatus {

    //对应PE的请求失败
    SUBMIT_CMF_FAIL("submitCmfFail", "提交CMF失败"),
    //提交机构失败,指查询时机构订单都不存在结果
    SUBMIT_INST_FAIL("submitInstFail", "提交机构失败"),
    //对应PE的CmfFundResultCode.SUCCESS， 同时可以持久化到机构订单结果表.
    SUCCESS("S", "处理成功"),
    //异步等待通知的情形. 对应PE的CmfFundResultCode.REQUEST_SUCCESS
    AWAITING("A", "提交成功,等待通知"),
    //仅用于出款，对应PE的CmfFundResultCode.SUBMIT_INST
    SUBMIT_INST_SUCCESS("IS", "提交渠道成功"),
    //对应PE的CmfFundResultCode.UNKNOW_EXCEPTION
    UNKNOW_EXCEPTION("E", "未知异常"),
    //对应PE的CmfFundResultCode.InProcess
    FAILURE("F", "订单处理中(CMF补单)");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    InstOrderProcessStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static InstOrderProcessStatus getByCode(String code) {

        for (InstOrderProcessStatus type : InstOrderProcessStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

    public boolean isFinished() {
        return this == SUCCESS;
    }

    public boolean canResend() {
        return SUBMIT_INST_FAIL == this;
    }

}
