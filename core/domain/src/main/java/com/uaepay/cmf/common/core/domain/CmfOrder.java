package com.uaepay.cmf.common.core.domain;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderConfirmStatus;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.NotifyStatus;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>资金渠道管理订单</p>
 *
 * <AUTHOR> won
 * @version $Id: CmfOrder.java, v 0.1 2010-12-22 下午03:55:01 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class CmfOrder implements BasicConstant {
    /**
     * 订单流水号
     */
    private String orderSeqNo;
    /**
     * 请求类型
     */
    private RequestType requestType;
    /**
     * 业务类型
     */
    private BizType bizType;
    /**
     * 支付方式
     **/
    private PayMode payMode;
    /**
     * 机构编码 目标机构
     */
    private String instCode;
    /**
     * 请求批次号，批量提交时使用。可空
     */
    private String requestBatchNo;
    /**
     * 支付流水号
     */
    private String paymentSeqNo;
    /**
     * 清结算ID
     */
    private String settlementId;
    /**
     * 产品码
     */
    private String productCode;
    /**
     * 支付编码
     */
    private String paymentCode;
    /**
     * 会员ID
     */
    private String memberId;
    /**
     * 金额
     */
    private Money amount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
    /**
     * 业务发起时间
     */
    private Date bizTime;
    /**
     * 资金渠道，兼容用
     */
    private String fundChannelCode;
    /**
     * 操作员
     */
    private String operator;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 最后修改时间
     */
    private Date gmtModified;
    /**
     * 状态
     */
    private CmfOrderStatus status;
    /**
     * 审核状态
     */
    private CmfOrderConfirmStatus confirmStatus;
    /**
     * 支付结果通知状态
     */
    private NotifyStatus paymentNotifyStatus;
    /**
     * 扩展信息
     */
    private Map<String, String> extension = new HashMap<>();
    /**
     * 原始入款订单号，用于汇总
     **/
    private String orgiPaymentSeqNo;
    /**
     * 原始入款结算ID
     */
    private String orgiSettlementId;
    /**
     * 支付凭证号
     */
    private String paymentVoucherNo;
    /**
     * 商户id
     */
    private String merchantId;
    /**
     * 备注
     */
    private String memo;

    public void setAmount(Money amount) {
        if (amount == null) {
            this.amount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
        } else {
            this.amount = amount;
        }
    }

}
