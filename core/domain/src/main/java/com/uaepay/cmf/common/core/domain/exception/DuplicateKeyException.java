package com.uaepay.cmf.common.core.domain.exception;

/**
 * <p>数据库插入违反唯一约束抛出异常</p>
 * <AUTHOR> won
 * @version $Id: DuplicateKeyException.java, v 0.1 2010-6-5 下午08:51:37 sean won Exp $
 */
public class DuplicateKeyException extends Exception {

    private static final long serialVersionUID = 8807431150111680162L;

    /**
     * 默认构造方法
     */
    public DuplicateKeyException() {
        super();
    }

    /**
     * 创建一个<code>DuplicateKeyException</code>
     *
     * @param message
     */
    public DuplicateKeyException(String message) {
        super(message);
    }

    /**
     * 创建一个<code>DuplicateKeyException</code>
     *
     * @param cause
     */
    public DuplicateKeyException(Throwable cause) {
        super(cause);
    }

    /**
     * 创建一个<code>DuplicateKeyException</code>
     *
     * @param message
     * @param cause
     */
    public DuplicateKeyException(String message, Throwable cause) {
        super(message, cause);
    }
}
