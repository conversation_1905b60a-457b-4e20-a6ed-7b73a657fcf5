package com.uaepay.cmf.common.core.domain.exception;

import com.uaepay.cmf.common.core.domain.enums.ErrorCode;

/**
 * <p>校验时存在异常</p>
 * <p>校验失败并不能使用该异常,返回对应的code即可</p>
 *
 * <AUTHOR> won
 * @version $Id: ValidateException.java, v 0.1 2010-12-30 上午09:39:07 sean won Exp $
 */
public class ValidateException extends AppRuntimeException {

    private static final long serialVersionUID = 3761904068677120547L;

    public ValidateException() {
        super();
    }

    public ValidateException(String message) {
        super(message);
    }

    public ValidateException(String message, Throwable cause) {
        super(message, cause);
    }

    public ValidateException(Throwable cause) {
        super(cause);
    }

    ErrorCode errorCode = ErrorCode.VALIDATE_ERROR;

    public ValidateException(ErrorCode errorCode) {
        super(errorCode);
    }

    @Override
    public String getCode() {
        return this.errorCode.getErrorCode();
    }
}
