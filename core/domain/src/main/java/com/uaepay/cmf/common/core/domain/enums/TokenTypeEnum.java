package com.uaepay.cmf.common.core.domain.enums;

import com.uaepay.payment.common.v2.enums.PayMode;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date TokenTypeEnum.java v1.0  2020-03-30 17:13
 */
public enum TokenTypeEnum {
    /**
     * session支付
     */
    SESSION("session", PayMode.TOKENPAY, "session支付"),
    /**
     * token支付
     */
    TOKEN("token", PayMode.TOKENPAY, "token支付"),
    /**
     * 快捷支付
     */
    QUICK("quick", PayMode.QUICKPAY, "quick支付"),
    /**
     * 提现
     */
    WITHDRAW("withdraw", PayMode.BALANCE, "余额支付"),
    /**
     * 控制订单
     */
    CONTROL("control", PayMode.QUICKPAY, "控制订单"),
    /**
     * ANNI FUND OUT
     */
    ANNI("AANI", PayMode.QUICKPAY, "ANNI FUND IN_OUT"),
    ;

    static final String MOTO = "moto";

    private String code;

    private PayMode payMode;

    private String description;

    TokenTypeEnum(String code, PayMode payMode, String description) {
        this.code = code;
        this.payMode = payMode;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public PayMode getPayMode() {
        return payMode;
    }

    public String getDescription() {
        return description;
    }

    public static TokenTypeEnum getByCode(String code) {
        for (TokenTypeEnum tokenType : values()) {
            if (tokenType.getCode().equals(code)) {
                return tokenType;
            }
        }
        if (MOTO.equals(code)) {
            return QUICK;
        }
        return null;
    }

}
