package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <p>路由要素类型</p>
 * <AUTHOR>
 * @version $Id: RouteFactorType.java, v 0.1 2012-10-22 下午2:31:54 liumaoli Exp $
 */
@Getter
public enum RouteFactorType {
	//
	CHANNEL("channel", "按渠道打分"),

	API("api", "按api打分");
	
	public static final String API_PREFIX = "API_";

	/** 代码 */
	private final String code;
	/** 信息 */
	private final String message;


	RouteFactorType(String code, String message) {
		this.code = code;
		this.message = message;
	}

	/**
	 * 通过代码获取ENUM
	 *
	 * @param code
	 * @return
	 */
	public static RouteFactorType getByCode(String code) {

		for (RouteFactorType status : RouteFactorType.values()) {
			if (status.getCode().equalsIgnoreCase(code)) {
				return status;
			}
		}

		return null;
	}
	
	
	public static RouteFactorType getByFactorCode(String factorCode){
		if (StringUtils.isBlank(factorCode)) {
			return null;
		}
		if(factorCode.startsWith(API_PREFIX)){
			return API;
		}else{
			return CHANNEL;
		}
	}
	
	public static String getRealTypeAfterPrefix(String prefix,String factorCode){
		if(factorCode.startsWith(prefix)){
			return StringUtils.replace(factorCode, prefix,"");
		}
		return null;
	}

}
