package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>CMF订单-状态</p>
 * <AUTHOR> won
 * @version $Id: CmfOrderStatus.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum CmfOrderStatus {
    // cmf订单状态
    AWAITING("A", "待处理"),

    CANCEL("C", "已撤销"),

    IN_PROCESS("I", "处理中"),

    SUCCESSFUL("S", "成功"),

    FAILURE("F", "失败");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    CmfOrderStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static boolean isNotProcessFinished(CmfOrderStatus status) {
        return status == AWAITING || status == IN_PROCESS;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static CmfOrderStatus getByCode(String code) {

        for (CmfOrderStatus type : CmfOrderStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
