package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>机构订单-归档状态</p>
 * <AUTHOR> won
 * @version $Id: InstOrderStatus.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum InstOrderArchiveStatus {
    //
    AWAITING("A", "待归档，分批开始"),

    GENERATED("G", "文件生成||归档完成"),

    IN_PROCESS("I", "发送中"),

    SUBMMITED("S", "已经提交"),

    RECEIVED("R", "已经返回"),

    FAILURE("F", "提交失败"),

    PAUSE("P", "暂停");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    InstOrderArchiveStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static InstOrderArchiveStatus getByCode(String code) {

        for (InstOrderArchiveStatus type : InstOrderArchiveStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
