package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>
 * 监控类型.
 * </p>
 *
 * <AUTHOR>
 * @version MonitorType.java 1.0 @2015/10/15 17:00 $
 */
@Getter
public enum MonitorType {


    //5min
    FUND_CHANNEL_ROUTE_CONFIG_MONITOR("fundChannelRouteConfigMonitor",300,"渠道路由参数配置监控报警"),

    ;

    /** 代码 */
    private final String monitorKey;
    // 监控记录间隔,单位s
    private final long monitorInterval;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * 
     * @param monitorKey
     * @param message
     */
    MonitorType(String monitorKey, long monitorInterval, String message) {
        this.monitorKey = monitorKey;
        this.monitorInterval = monitorInterval;
        this.message = message;
    }

    /**
     * 通过代码获取
     * 
     * @param code
     * @return
     */
    public static MonitorType getByCode(String code) {

        for (MonitorType type : MonitorType.values()) {
            if (type.getMonitorKey().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
