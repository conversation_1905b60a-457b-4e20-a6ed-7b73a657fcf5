package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>缓存类型</p>
 * <AUTHOR> won
 * @version $Id: CacheType.java, v 0.1 2012-2-8 下午02:14:45 sean won Exp $
 */
@Getter
public enum CacheType {
    /**
     * 缓存类型
     */
    CMF_SYS_CONFIGURATION("sysConfiguration", "com.uaepay.cmf.sysconfig:","系统配置"),

    CSC("csc", "csc:", "安全码"),

    FORM("form", "form:", "3ds2.0表单"),

    INST_ORDER_TOKEN("instOrderToken", "instOrderToken:", "机构订单token"),

    ACCOUNT_BALANCE("accountBalance", "accountBalance:", "channel account balance token");

    /** 代码 */
    private final String code;
    /** 前缀 */
    private final String prefix;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param prefix
     * @param message
     */
    CacheType(String code, String prefix, String message) {
        this.code = code;
        this.prefix = prefix;
        this.message = message;
    }

}
