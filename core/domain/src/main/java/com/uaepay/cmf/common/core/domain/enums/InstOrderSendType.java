package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>机构订单-发送类型</p>
 * <AUTHOR> won
 * @version $Id: InstOrderResultStatus.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum InstOrderSendType {
    //
    SYNCHRONIZED("S", "同步发送"),

    ASYNCHRONOUS("A", "异步发送");
    /**
     * 构造
     * @param code
     * @param message
     */
    InstOrderSendType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public boolean isAsync() {
        return this == ASYNCHRONOUS;
    }

    public static InstOrderSendType getByCode(String sendType) {
        for (InstOrderSendType val : values()) {
            if (val.getCode().equals(sendType)) {
                return val;
            }
        }
        return null;
    }
}
