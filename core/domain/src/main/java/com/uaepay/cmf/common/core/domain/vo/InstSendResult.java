package com.uaepay.cmf.common.core.domain.vo;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;

/**
 * <p>机构通讯发送返回.</p>
 * <AUTHOR> won
 * @version $Id: InstSendResult.java, v 0.1 2010-12-30 下午05:45:05 sean won Exp $
 */
public class InstSendResult extends BaseResult implements InstPayResult {
    private static final long serialVersionUID = -3635649819493504495L;

    /** 返回对象. */
    private InstOrderResult   order;

    /** 上次失败，本次成功的标识. */
    private boolean           redo;

    private boolean           needNotifyRMS;

    private boolean           isCompleteSuccess;

    public boolean isSendSuccess() {
        return this.isSuccess();
    }

    public boolean isSendFailed() {
        return !this.isSuccess();
    }

    /**
     * @return the order
     */
    public InstOrderResult getOrder() {
        return order;
    }

    /**
     * @param order the order to set
     */
    public void setOrder(InstOrderResult order) {
        this.order = order;
    }

    @Override
    public boolean isPayFailed() {
        return InstOrderResultStatus.FAILURE.equals(order.getStatus()) && !needNotifyRMS;
    }

    @Override
    public boolean isPaySuccess() {
        return InstOrderResultStatus.SUCCESSFUL.equals(order.getStatus()) && !needNotifyRMS;
    }

    /**
     * @return the isRiskOrder
     */
    @Override
    public boolean isRiskOrder() {
        //R_C:根据返回编码判断risk
        return InstOrderResultStatus.RISK.equals(order.getStatus()) || needNotifyRMS;
    }

    public boolean isRedo() {
        return redo;
    }

    public void setRedo(boolean redo) {
        this.redo = redo;
    }

    public boolean isNeedNotifyRMS() {
        return needNotifyRMS;
    }

    public void setNeedNotifyRMS(boolean needNotifyRMS) {
        this.needNotifyRMS = needNotifyRMS;
    }

    @Override
    public String toString() {
        return "InstSendResult [order=" + order + ", redo=" + redo + ",needNotifyRMS="
               + needNotifyRMS + ",isCompleteSuccess=" + isCompleteSuccess + "]";
    }

    @Override
    public boolean isCompleteSuccess() {
        return isCompleteSuccess;
    }

    public void setCompleteSuccess(boolean isCompleteSuccess) {
        this.isCompleteSuccess = isCompleteSuccess;
    }

}
