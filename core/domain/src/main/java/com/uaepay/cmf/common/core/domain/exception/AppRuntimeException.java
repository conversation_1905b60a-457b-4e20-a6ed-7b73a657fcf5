package com.uaepay.cmf.common.core.domain.exception;

import com.uaepay.cmf.common.core.domain.enums.ErrorCode;

/**
 *
 * <p>运行时异常</p>
 * <AUTHOR> won
 * @version $Id: AppRuntimeException.java, v 0.1 2010-12-30 上午09:36:46 sean won Exp $
 */
public class AppRuntimeException extends RuntimeException {

    private static final long serialVersionUID = -5982000416422002899L;

    public AppRuntimeException() {
        super();
    }

    public AppRuntimeException(String message) {
        super(message);
    }

    public AppRuntimeException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
    }

    public AppRuntimeException(String message, Throwable cause) {
        super(message, cause);
    }

    public AppRuntimeException(Throwable cause) {
        super(cause);
    }

    ErrorCode errorCode = ErrorCode.RUNTIME_EXCEPTION;

    public String getCode(){
        return this.errorCode.getErrorCode();
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }
}
