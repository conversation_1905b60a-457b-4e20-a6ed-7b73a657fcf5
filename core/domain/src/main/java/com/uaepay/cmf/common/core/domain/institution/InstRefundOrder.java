package com.uaepay.cmf.common.core.domain.institution;


import com.uaepay.common.util.money.Money;
import lombok.Data;
import lombok.ToString;

/**
 * <p>退款订单</p>
 *
 * <AUTHOR> won
 * @version $Id: InstRefundOrder.java, v 0.1 2010-12-22 下午03:55:01 sean won Exp $
 */
@Data
@ToString(callSuper = true)
public class InstRefundOrder extends InstOrder {
    private static final long serialVersionUID = 6291517817831711621L;
    /**
     * 入款机构订单号
     */
    private String fundinOrderNo;
    /**
     * 入款机构返回流水号
     */
    private String fundinInstSeqNo;
    /**
     * 入款实收金额
     */
    private Money fundinRealAmount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
    /**
     * 入款时间
     */
    private String fundinDate;

    public void setFundinRealAmount(Money fundinRealAmount) {
        if (fundinRealAmount == null) {
            this.fundinRealAmount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
        } else {
            this.fundinRealAmount = fundinRealAmount;
        }

    }

    /**
     * 克隆
     */
    @Override
    public InstRefundOrder clone() throws CloneNotSupportedException {
        return (InstRefundOrder) super.clone();
    }

}
