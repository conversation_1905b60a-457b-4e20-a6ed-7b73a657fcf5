package com.uaepay.cmf.common.core.domain.exception;

/**
 * <p>时间校验异常.</p>
 *
 * <AUTHOR>
 * @version DateValidateException.java 1.0 @2016/1/4 17:16 $
 */
public class DateValidateException extends AppRuntimeException{

    private static final long serialVersionUID = 2843285611732021516L;

    public DateValidateException() {
        super();
    }

    public DateValidateException(String message) {
        super(message);
    }

    public DateValidateException(String message, Throwable cause) {
        super(message, cause);
    }

    public DateValidateException(Throwable cause) {
        super(cause);
    }
}
