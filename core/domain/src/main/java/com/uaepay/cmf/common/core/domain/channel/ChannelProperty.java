package com.uaepay.cmf.common.core.domain.channel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.uaepay.common.util.money.Money;

/**
 * 
 * <p>渠道属性</p>
 * 如渠道支持的交易金额大小限制，面值，支持的卡种等等，某个属性往往是针对特定机构而言的，
 * 如手机充值卡渠道支持的面值和相应的目标机构相关，网银金额的限制往往和卡种，所属银行相关，
 * <br>
 * 渠道支持的目标机构是通过数据库表相关联的，不需要在渠道属性里面提现
 *
 * <AUTHOR> won
 * @version $Id: ChannelProperty.java, v 0.1 2011-4-4 上午10:45:20 sean won Exp $
 */
public interface ChannelProperty extends Serializable {
    /**
     * 用于获取关联的属性，如金额的限制往往和目标机构或者卡片类型相关联
     * @param propertyName
     * @return
     */
    ChannelProperty get(String propertyName);
    
    /**
     * 检查时候具有某个子属性
     * @param propertyName
     * @return
     */
    boolean has(String propertyName);
    
    
    boolean hasNo(String propertyName);
    
    /**
     * 新增子属性
     * @param domain
     * @return
     */
    ChannelProperty addProperty(String domain);
    
    /**
     * 新增子属性
     * @param channelProperty
     * @return
     */
    void addProperty(ChannelProperty channelProperty);
    
    /**
     * 设置属性值
     * @param values
     */
    void setValues(String values);
    /**
     * 获取该属性的值，一般情况下不需要调用此方法
     * @return
     * 
     */
    String getValue();
    /**
     * 返回属性值，如该属性是一个列表，如支持的所有面值
     * 一般情况下不需要调用此方法
     * @return
     *
     */
    String[] getValues();
    
    
    String getName();
    
    /**
     * 大于
     * @param value
     * @return
     */
    boolean greatThan(BigDecimal value);
    
    boolean greatThan(Money value);
    
    boolean equalsBdecimal(BigDecimal value);
    
    boolean equalsMoney(Money value);
    
    boolean lessThan(BigDecimal value);
    
    boolean lessThan(Money value);
    
    boolean contains(BigDecimal value);
    
    boolean contains(Money value);

    boolean after(Date value);
    
    boolean before(Date value);
    
    /**
     * 小于或等于
     * @param value
     * @return
     */
    boolean lessEqual(BigDecimal value);
    
    boolean lessEqual(Money value);
    /**
     * 大于或等于
     * @param value
     * @return
     */
    boolean greatEqual(BigDecimal value);
    
    boolean greatEqual(Money value);
    
    boolean greatThan(String value);
    
    boolean equalsStr(String value);
    
    boolean lessThan(String value);
    
    boolean contains(String value);
    
    boolean any(String[] values);
    
    boolean lessEqual(String value);
    
    boolean greatEqual(String value);
    /**
     * 给定的值在属性范围以内，该属性必须包含两个可比较的值
     * min &lt= value &lt= max
     * @param value
     * @return
     */
    boolean cover(BigDecimal value);
    
    boolean cover(Money value);
    
    
    boolean isEmpty(String value);
    
    boolean isEmpty(Money value);
    
    boolean isEmpty(BigDecimal value);
}
