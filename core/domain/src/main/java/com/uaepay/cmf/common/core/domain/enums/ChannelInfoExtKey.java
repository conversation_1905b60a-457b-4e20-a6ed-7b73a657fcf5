package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>渠道扩展Key</p>
 * <AUTHOR> won
 * @version $Id: ChannelInfoExtKey.java, v 0.1 2012-2-8 下午02:14:45 sean won Exp $
 */
@Getter
public enum ChannelInfoExtKey {
    // 渠道扩展参数
    TARGET_INST("targetInst","目标机构"),
    //借记卡、贷记卡、综合
    CARD_TYPE("cardType","卡类型"),
    DBCR("dbcr", "借记贷记"),
    AMOUNT("amount","金额"),
    //web，wap, ios, android
    ACCESS_CHANNELS("accessChannel","支持的访问方式"),
    COMPANY_OR_PERSONAL("companyOrPersonal", "对公对私"),
    BIZ_TYPE("bizType","业务类型"),
    //产品编码
    PRODUCT_CODE("productCode","产品编码"),
    MAX_RESEND_TIMES("maxResendTimes","最大重发次数"),
    QUERY_CONFIG("queryConfig","查询配置"),
    NOT_SUPPORT_MANUAL_REFUND("notSupportManualRefund", "不支持人工退款"),
    NOT_SUPPORT_TASK_SEND2COUNTER("notSupportTaskSend2Counter", "不支持cmfTask自动发送给到COUNTER"),
    PAYOUT_ACCOUNT("payoutAccount", "fund out payout account"),
    ACCOUNT_TYPE("accountType", "account type-account no/iban"),
    ;

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    /**
     * 构造
     * @param code
     * @param message
     */
    ChannelInfoExtKey(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static ChannelInfoExtKey getByCode(String code) {

        for (ChannelInfoExtKey type : ChannelInfoExtKey.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
