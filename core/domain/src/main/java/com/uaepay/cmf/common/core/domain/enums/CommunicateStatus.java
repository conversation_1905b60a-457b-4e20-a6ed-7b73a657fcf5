package com.uaepay.cmf.common.core.domain.enums;

import lombok.Getter;

/**
 * <p>机构订单-通讯状态</p>
 * <AUTHOR> won
 * @version $Id: InstOrderCommunicateStatus.java, v 0.1 2010-12-22 下午04:18:56 sean won Exp $
 */
@Getter
public enum CommunicateStatus {
    //
    AWAITING("A", "等待指令发送"),

    IN_PROCESS("I", "发送中"),

    SENT("S", "指令已经发送"),

    RECEIVED("R", "数据已经返回"),

    FAILURE("F", "指令发送失败");

    /** 代码 */
    private final String code;
    /** 信息 */
    private final String message;

    CommunicateStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取
     * @param code
     * @return
     */
    public static CommunicateStatus getByCode(String code) {

        for (CommunicateStatus type : CommunicateStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return null;
    }

}
