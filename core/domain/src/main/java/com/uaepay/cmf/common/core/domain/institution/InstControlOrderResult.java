package com.uaepay.cmf.common.core.domain.institution;

import com.uaepay.common.util.money.Money;
import lombok.Data;
import lombok.ToString;

/**
 * <p>机构控制订单结果</p>
 *
 * <AUTHOR>
 * @version $Id: InstControlOrderResult.java, v 0.1 2012-8-17 下午5:32:57 fuyangbiao Exp $
 */
@Data
@ToString(callSuper = true)
public class InstControlOrderResult extends InstBaseResult {
    private static final long serialVersionUID = 9100978020259867110L;
    /**
     * 结果ID
     */
    private Long resultId;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 金额
     */
    private Money amount;

}
