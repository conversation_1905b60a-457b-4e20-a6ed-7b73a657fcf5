<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd"
       default-autowire="byName">

   <!-- 异步线程池 -->
   <bean id="daemonTaskExecutor"
         class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
      <!-- 核心线程数，默认为1 -->
      <property name="corePoolSize" value="10" />
      <!-- 最大线程数，默认为Integer.MAX_VALUE -->
      <property name="maxPoolSize" value="30" />
      <!-- 队列最大长度，默认为Integer.MAX_VALUE -->
      <property name="queueCapacity" value="300" />
      <!-- 线程池维护线程所允许的空闲时间，默认为60s -->
      <property name="keepAliveSeconds" value="300" />
      <!--
          线程池对拒绝任务（超过待处理队列长度）的处理策略，目前只支持AbortPolicy、CallerRunsPolicy；默认为后者
      -->
      <property name="rejectedExecutionHandler">
         <!--
             AbortPolicy:直接抛出java.util.concurrent.RejectedExecutionException异常
         -->
         <!-- CallerRunsPolicy:若已达到待处理队列长度，将由主线程直接处理请求 -->
         <!-- DiscardOldestPolicy:抛弃旧的任务；会导致被丢弃的任务无法再次被执行 -->
         <!-- DiscardPolicy:抛弃当前任务；会导致被丢弃的任务无法再次被执行 -->
         <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy" />
      </property>
   </bean>


</beans>
