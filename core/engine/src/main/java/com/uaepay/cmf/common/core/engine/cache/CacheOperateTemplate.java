package com.uaepay.cmf.common.core.engine.cache;

import com.uaepay.cmf.common.core.domain.enums.CacheType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * <p>缓存操作模板</p>
 *
 * <AUTHOR> won
 * @version $Id: CacheLoadTemplate.java, v 0.1 2012-2-8 下午02:10:05 sean won Exp $
 */
@Service
public class CacheOperateTemplate {
    private static final Logger logger = LoggerFactory
            .getLogger(CacheOperateTemplate.class);

    /**
     * 默认实现时间， 默认不失效
     */
    private static final int DEFAULT_EXPIRE_SECONDS = CacheClient.ONE_HOUR_SECONDS;

    /**
     * 6小时过期
     */
    private static final int SIX_HOUR_EXPIRES_SECONDS = 60 * 60 * 6;

    /**
     * 本地缓存
     */
    @Resource(name = "localCacheClient")
    private CacheClient localCache;
    /**
     * 统一缓存
     */
    @Resource(name = "memoryCacheClient")
    private CacheClient memoryCache;

    /**
     * 数据获取
     * <ul>1、统一缓存获取
     * <li>成功：清理本地缓存，返回结果。</li>
     * <li>失败：清理本地缓存，原始数据获取。</li>
     * <li>异常：本地缓存获取。</li>
     * </ul>
     * <ul>2、本地缓存获取
     * <li>成功：返回结果。</li>
     * <li>失败：原始数据获取。</li>
     * </ul>
     * <ul>3、原始数据获取
     * <li>成功：返回结果。
     * <ul>统一缓存异常判断
     * <li>异常：设置本地缓存</li>
     * <li>未异常：设置统一缓存</li>
     * </ul>
     * </li>
     * <li>失败：返回空。</li>
     * </ul>
     *
     * @param cacheType 缓存类型
     * @param key       数据键值
     * @param loader    原始数据加载器
     * @return
     */
    public Object load(CacheType cacheType, String key, DataLoader<?> loader) {
        Object value = null;
        boolean isException = false;

        try {
            // 1、统一缓存获取
            value = memoryCache.get(cacheType, key);
            logger.debug("{}通过统一缓存获取信息{}", key, value);

            localCache.remove(cacheType, key);
            if (!isEmpty(value)) {
                return value;
            }
        } catch (Exception e) {
            isException = true;

            // 2、本地缓存获取
            value = localCache.get(cacheType, key);
            logger.debug("{}通过本地缓存获取信息{}", key, value);

            if (!isEmpty(value)) {
                return value;
            }
        }

        // 3、原始数据加载
        value = loader.load();
        logger.debug("{}通过原始数据来源获取信息{}", key, value);

        if (!isEmpty(value)) {
            // case异常：设置本地缓存
            if (isException) {
                localCache.put(cacheType, key, value, SIX_HOUR_EXPIRES_SECONDS);
            }

            // case未异常：设置统一缓存
            else {
                try {
                    memoryCache.put(cacheType, key, value, SIX_HOUR_EXPIRES_SECONDS);
                } catch (Exception e) {
                    // do nothing
                }
            }
        } else {
            //增加日志
            logger.warn("Current Key Cannot Cache:{}", key);
        }

        return value;
    }

    /**
     * 刷新缓存
     *
     * @param cacheType
     * @param loader
     */
    public void refresh(CacheType cacheType, DataLoader<Map<String, ?>> loader) {
        Map<String, ?> allData = loader.load();

        for (Entry<String, ?> entry : allData.entrySet()) {
            try {
                memoryCache.put(cacheType, entry.getKey(), entry.getValue(), DEFAULT_EXPIRE_SECONDS);
            } catch (Exception e) {
                logger.error("统一缓存put异常" + cacheType.getCode() + ",key=" + entry.getKey(), e);
            }
        }
    }

    public void deleteByKeys(CacheType type, Set<String> keys) {
        try {
            memoryCache.removeByKeys(type, keys);
        } catch (Exception e) {
            logger.error("remove by keys error, ", e);
        }
        localCache.removeByKeys(type, keys);
    }

    /**
     * 判断对象是否为空
     *
     * @param obj
     * @return
     */
    @SuppressWarnings("unchecked")
    private boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }

        if (obj instanceof Map) {
            return CollectionUtils.isEmpty((Map) obj);
        }
        return obj instanceof Collection && CollectionUtils.isEmpty((Collection) obj);

    }
}
