package com.uaepay.cmf.common.core.engine.cache.impl;

import com.uaepay.cmf.common.core.engine.cache.CacheLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * <p>CacheLockServiceImpl</p>
 *
 * <AUTHOR>
 * @version CacheLockServiceImpl.java v1.0  2022/11/21 11:24
 */
@Service("cacheLockService")
@Slf4j
public class CacheLockServiceImpl implements CacheLockService {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public boolean lock(String lockKey, Duration lockTime) {
        Boolean lock = null;
        try {
            log.info("获取锁：{}", lockKey);
            lock = redisTemplate.opsForValue().setIfAbsent(lockKey, "lock", lockTime);
        } catch (Exception e) {
            log.error("获取锁失败：{}", lockKey);
        }
        return lock != null && lock;
    }

    @Override
    public void unlock(String lockKey) {
        try {
            log.info("释放锁：{}", lockKey);
            redisTemplate.delete(lockKey);
        } catch (Exception e) {
            log.error("释放锁异常：{}", lockKey);
        }
    }
}
