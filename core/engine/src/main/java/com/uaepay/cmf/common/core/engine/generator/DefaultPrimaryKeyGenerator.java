package com.uaepay.cmf.common.core.engine.generator;

import com.uaepay.basis.sequenceutil.IDGen;
import org.apache.commons.lang3.StringUtils;
import com.uaepay.common.util.DateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version DefaultPrimaryKeyGenerator.java 1.0 Created@2017-05-21 21:35 $
 */
@Service
public class DefaultPrimaryKeyGenerator implements PrimaryKeyGenerator {

    @Resource(name="idGen")
    private IDGen idGen;

    private static final int   SEQ_LENGTH = 9;

    @Override
    public String generateKey(SequenceNameEnum seqName) {
        return DateUtil.getTodayString()
               + StringUtils.leftPad(String.valueOf(getSequence(seqName.getCode(), SEQ_LENGTH)),
                   SEQ_LENGTH, "0");
    }

    /**
     * 获取流水号
     * @param seqName
     * @return
     */
    private long getSequence(String seqName, int length) {
        long seqVal = idGen.get(seqName);
        return seqVal % (int) Math.pow(10, length);
    }

}
