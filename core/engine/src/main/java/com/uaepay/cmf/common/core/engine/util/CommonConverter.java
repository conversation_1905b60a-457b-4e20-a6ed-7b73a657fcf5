package com.uaepay.cmf.common.core.engine.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.Kvp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

/**
 * <p>通用转换类</p>
 *
 * <AUTHOR>
 * @version $Id: CommonConverter.java, v 0.1 2012-8-15 下午8:52:12 fuyangbiao Exp $
 */
public class CommonConverter {
    private static final String KEY_SPLIT_TAG = "_";

    private static final String JSON_START = "{";

    /**
     * 转换扩展信息
     *
     * @param extension
     * @return
     */
    public static java.util.Map<String, String> convertMap(Extension extension) {
        java.util.Map<String, String> map = new HashMap<>();
        if (extension == null) {
            return map;
        }
        for (Kvp kvp : extension.getEntryList()) {
            map.put(convertKey(kvp.getKey()), kvp.getValue());
        }
        return map;
    }

    /**
     * 根据MAP转换为extension
     *
     * @param map
     * @return
     */
    public static Extension convertExtension(java.util.Map<String, String> map) {
        Extension extension = new Extension();
        if (CollectionUtils.isEmpty(map)) {
            return extension;
        }
        for (Entry<String, String> entry : map.entrySet()) {
            extension.add(convertKey(entry.getKey()), entry.getValue());
        }
        return extension;
    }

    /**
     * 根据MAP转换为extension
     * 不转换key
     *
     * @param map
     * @return
     */
    public static Extension convertExtensionWithoutConvertKey(java.util.Map<String, String> map) {
        Extension extension = new Extension();
        if (CollectionUtils.isEmpty(map)) {
            return extension;
        }
        for (Entry<String, String> entry : map.entrySet()) {
            extension.add(entry.getKey(), entry.getValue());
        }
        return extension;
    }


    /**
     * 将扩展对象转换成json字符串保存到数据库
     *
     * @param str
     * @return
     */
    public static java.util.Map<String, String> convertFromDb(String str) {
        java.util.Map<String, String> extension = new HashMap<>();
        if (StringUtils.isEmpty(str)) {
            return extension;
        }
        if (str.startsWith(JSON_START)) {
            return JSON.parseObject(str, new TypeReference<Map<String, String>>() {
            });
        }

        String[][] list = JSON.parseObject(str, String[][].class);
        for (int i = 0; i < list.length; i++) {
            extension.put(convertKey(list[i][0]), list[i][1]);
        }
        return extension;
    }

    /**
     * 将json字符串从数据库中反序列话后使用
     *
     * @param extension
     * @return
     */
    public static String convertToDb(java.util.Map<String, String> extension) {
        if (CollectionUtils.isEmpty(extension)) {
            return StringUtils.EMPTY;
        }
        return JSON.toJSONString(extension, SerializerFeature.UseISO8601DateFormat);

//        int size = extension.size();
//        String[][] list = new String[size][2];
//        int i = 0;
//        for (Entry<String, String> entry : extension.entrySet()) {
//            list[i][0] = convertKey(entry.getKey());
//            list[i][1] = entry.getValue();
//            i = i + 1;
//        }
//        return JSON.toJSONString(list);
    }

    /**
     * 转换字符串
     * 例：
     * AAAA_BBB_CCC-->aaaaBbbCcc
     * aaA_B       -->aaaB
     * aSa_b_C     -->asaBC
     *
     * @param origKey
     * @return
     */
    public static String convertKey(String origKey) {
        if (StringUtils.isBlank(origKey)) {
            return origKey;
        }
        if (!origKey.contains(KEY_SPLIT_TAG)) {
            return origKey;
        }
        String[] wordArray = origKey.trim().toLowerCase().split(KEY_SPLIT_TAG);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < wordArray.length; i++) {
            String word = wordArray[i];
            if (i == 0) {
                sb.append(word);
            } else {
                sb.append(StringUtils.substring(word, 0, 1).toUpperCase());
                sb.append(StringUtils.substring(word, 1, word.length()));
            }
        }

        return sb.toString();
    }
}
