package com.uaepay.cmf.common.core.engine.util.expression;

import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.tools.generic.MathTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.exception.ParseException;
import org.apache.commons.lang3.StringUtils;
import com.uaepay.common.util.money.Money;

/**
 * 
 * <p>velocity工具类</p>
 * <AUTHOR> won
 * @version $Id: VelocityUtil.java, v 0.1 2011-2-24 下午03:50:56 sean won Exp $
 */
public class VelocityUtil implements BasicConstant {
    private static Logger logger = LoggerFactory.getLogger(VelocityUtil.class);
    /** 引擎对象 */
    private static VelocityEngine ve = null;

    private VelocityUtil(){

    }

    /**
     * 获取引擎
     * @return
     */
    public static VelocityEngine loadEngine() {
        if (ve != null) {
            return ve;
        }
        synchronized (VelocityUtil.class) {
            if (ve != null) {
                return ve;
            }

            // 初始化
            VelocityEngine veInit = new VelocityEngine();
            try {
                veInit.init();
                veInit.setProperty(RuntimeConstants.INPUT_ENCODING, FILE_ENCODE);
                veInit.setProperty(RuntimeConstants.OUTPUT_ENCODING, FILE_ENCODE);
            } catch (Exception e) {
                logger.error("velocity error", e);
            }
            ve = veInit;
        }

        return ve;
    }

    /**
     * 获取字数串
     * @param templateContent
     * @param paramMap
     * @return
     */
    public static String getString(String templateContent, Map<String, Object> paramMap)
                                                                                        throws ParseException {
        if (StringUtils.isBlank(templateContent)) {
            return null;
        }

        VelocityContext context = new VelocityContext();
        context.put("math", new MathTool());
        if (!CollectionUtils.isEmpty(paramMap)) {
            for (Entry<String, Object> entry : paramMap.entrySet()) {
                context.put(entry.getKey(), entry.getValue());
            }
        }

        StringWriter writer = new StringWriter();
        try {
            loadEngine().evaluate(context, writer, "velocity", templateContent);

            return writer.toString();
        } catch (Exception e) {
            throw new ParseException("表达式[" + templateContent + "]解析异常", e);
        }
    }

    /**
     * 合并字符串
     * 如 "111" + "222" = "111222"
     * @param templateContent
     * @param paramMap
     * @return
     */
    public static String mergeString(String templateContent, Map<String, Object> paramMap)
                                                                                          throws ParseException {
        return getString(wrapExecuteContent(templateContent), paramMap);
    }

    /**
     * 执行字符串
     * @param templateContent
     * @param paramMap
     * @return
     */
    public static String executeString(String templateContent, Map<String, Object> paramMap)
                                                                                            throws ParseException {
        if (StringUtils.contains(templateContent, "#if")) {
            return getString(templateContent, paramMap);
        } else {
            return mergeString(templateContent, paramMap);
        }
    }

    /**
     * 判断是否为真
     * @param templateContent
     * @param paramMap
     * @return
     */
    public static boolean isTrue(String templateContent, Map<String, Object> paramMap)
                                                                                      throws ParseException {
        return Boolean.valueOf(StringUtils.trim(executeString(templateContent, paramMap)));
    }

    /**
     * 计算权重
     * @param templateContent
     * @param paramMap
     * @return
     * @throws ParseException
     */
    public static int calWeight(String templateContent, Map<String, Object> paramMap)
                                                                                     throws ParseException {
        return Integer.valueOf(StringUtils.trim(executeString(templateContent, paramMap)))
            .intValue();
    }

    /**
     * 获取金额
     * @param templateContent
     * @param paramMap
     * @return
     */
    public static Money getAmount(String templateContent, Map<String, Money> paramMap)
                                                                                      throws ParseException {
        Money emptyAmount = new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
        if (StringUtils.isBlank(templateContent) || CollectionUtils.isEmpty(paramMap)) {
            return emptyAmount;
        }
        Map<String, Object> amountMap = new HashMap<>();
        for (Entry<String, Money> entry : paramMap.entrySet()) {
            amountMap.put(entry.getKey(), entry.getValue().getAmount());
        }

        String resultString = executeString(templateContent, amountMap);
        if (StringUtils.isBlank(resultString)) {
            return emptyAmount;
        }

        return new Money(StringUtils.trim(resultString), DEFAULT_CURRENCY);
    }

    /**
     * 包装变量
     * @param orgiValue
     * @return
     */
    public static String warpVariable(String orgiValue) {
        return "${" + orgiValue + "}";
    }

    /**
     * 封装执行语句
     * @param orgiContent
     * @return
     */
    private static String wrapExecuteContent(String orgiContent) {
        return "#set($temp=" + orgiContent + ")$temp";
    }

}
