package com.uaepay.cmf.common.core.engine.util.collection;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version CollectionUtil.java 1.0 Created@2017-12-13 11:58 $
 */
public class CollectionUtil {

    private CollectionUtil() {
        throw new IllegalAccessError("不可调用的方法");
    }

    public static boolean isEmpty(Collection coll) {
        return coll == null || coll.isEmpty();
    }

    public static boolean isNotEmpty(Collection coll) {
        return !isEmpty(coll);
    }

    public static String join(final Collection<String> collection, String delimiter) {
        if (CollectionUtils.isEmpty(collection)) {
            return StringUtils.EMPTY;
        }
        StringBuilder accum = new StringBuilder();
        for (String item : collection) {
            accum.append(item).append(delimiter);
        }
        String resp = accum.toString();
        return resp.substring(0, resp.length() - 1);
    }

    public static BigDecimal sumList(final List sourceColl, FieldConverter converter) {
        BigDecimal respVal = new BigDecimal("0");
        for (Object item : sourceColl) {
            respVal = respVal.add(converter.convert(item));
        }
        return respVal;
    }

}
