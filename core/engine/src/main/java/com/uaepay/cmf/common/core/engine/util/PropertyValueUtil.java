package com.uaepay.cmf.common.core.engine.util;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import org.springframework.util.ReflectionUtils;

import com.uaepay.cmf.common.core.domain.common.PropertyExtensionMapping;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>对象属性工具类</p>
 * <AUTHOR>
 * @version $Id: InstOrderPropertyUtil.java, v 0.1 2012-8-7 下午4:17:28 fuyangbiao Exp $
 */
public class PropertyValueUtil {
    /** 方法对象缓存 */
    private static final Map<String, Method> cacheMethod = new HashMap<>();

    /**
     * 获取机构订单属性
     * @param obj
     * @param mapping
     * @return
     */
    public static Object getValue(Object obj, PropertyExtensionMapping mapping) {
        Method propertyMethod = getMethod(MethodPrefix.GET, mapping);
        if (propertyMethod == null) {
            throw new IllegalArgumentException("GET方法不存在");
        }

        try {
            return propertyMethod.invoke(obj);
        } catch (Exception e) {
            throw new IllegalArgumentException("获取值异常", e);
        }
    }

    /**
     * 设值
     * @param obj
     * @param mapping
     * @param value
     */
    public static void setValue(Object obj, PropertyExtensionMapping mapping, Object value) {
        if(value == null){
            return; //值为空,不需要设置
        }
        Method propertyMethod = getMethod(MethodPrefix.SET, mapping,
                value.getClass());
        if (propertyMethod == null) {
            throw new IllegalArgumentException(mapping.getPropertyName() + "SET方法不存在");
        }

        try {
            propertyMethod.invoke(obj, value);
        } catch (Exception e) {
            throw new IllegalArgumentException("设值异常", e);
        }
    }

    /**
     * 获取方法
     * @param prefix
     * @param mapping
     * @param paramTypes
     * @return
     */
    private static Method getMethod(MethodPrefix prefix, PropertyExtensionMapping mapping,
                                    Class<?>... paramTypes) {
        String propertyName = mapping.getPropertyName();
        String methodName = prefix.getCode()
                            + StringUtils.substring(propertyName, 0, 1).toUpperCase()
                            + StringUtils.substring(propertyName, 1, propertyName.length());
        if (cacheMethod.containsKey(methodName)) {
            return cacheMethod.get(methodName);
        }

        // 反射获取
        Method propertyMethod = ReflectionUtils.findMethod(mapping.getObjectClass(), methodName,
            paramTypes);
        if (propertyMethod != null) {
            cacheMethod.put(methodName, propertyMethod);
        }

        return propertyMethod;
    }

    /** 方法前缀 */
    public enum MethodPrefix {
        GET("get"),

        SET("set");

        /** 代码 */
        private final String code;

        MethodPrefix(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }
}
