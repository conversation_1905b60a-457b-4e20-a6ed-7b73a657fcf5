package com.uaepay.cmf.common.core.engine.lock;

/**
 * <p>锁异常.</p>
 * <AUTHOR> won
 * @version $Id: LockException.java, v 0.1 2011-3-3 上午11:19:01 sean won Exp $
 */
public class LockException extends Exception {

    private static final long serialVersionUID = 7138794971443706697L;

    /**
     * 默认构造方法
     */
    public LockException() {
        super();
    }

    /**
     * 创建一个<code>LockException</code>
     *
     * @param message
     */
    public LockException(String message) {
        super(message);
    }

    /**
     * 创建一个<code>LockException</code>
     *
     * @param cause
     */
    public LockException(Throwable cause) {
        super(cause);
    }

    /**
     * 创建一个<code>LockException</code>
     *
     * @param message
     * @param cause
     */
    public LockException(String message, Throwable cause) {
        super(message, cause);
    }
}
