package com.uaepay.cmf.common.core.engine.cache.impl;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.common.core.engine.util.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <p>本地缓存对象</p>
 * <AUTHOR> won
 * @version $Id: LocalCacheClient.java, v 0.1 2012-2-8 上午11:24:21 sean won Exp $
 */
@Service("localCacheClient")
public class LocalCacheClient implements CacheClient {
    /** 缓存内容 */
    private Map<String, CachedData> content = new ConcurrentHashMap<>();

    @Override
    public Object get(CacheType cacheType, String key) {
        String unityKey = CommonUtil.buildCacheKey(cacheType, key);
        CachedData data = content.get(unityKey);
        if (data == null) {
            return null;
        }
        if (data.isExpired()) {
            content.remove(unityKey);
            return null;
        } else {
            return data.getValue();
        }
    }

    @Override
    public void put(CacheType cacheType, String key, Object data, int expireSeconds) {
        content.put(CommonUtil.buildCacheKey(cacheType, key), new CachedData(data, expireSeconds));
    }

    @Override
    public Object remove(CacheType cacheType, String key) {
        return content.remove(CommonUtil.buildCacheKey(cacheType, key));
    }

    @Override
    public void flush(CacheType cacheType) {
        content.clear();
    }

    /** 缓存对象 */
    class CachedData {
        /** 值对象 */
        private final Object value;
        /** 创建时间 */
        private long         createTimeMillis;
        /** 过去时间，单位秒 */
        private int          expireSeconds;

        /**
         * 构造方法
         * @param value
         * @param expireSeconds
         */
        public CachedData(Object value, int expireSeconds) {
            this.value = value;
            this.expireSeconds = expireSeconds;
            this.createTimeMillis = System.currentTimeMillis();
        }

        /**
         * 判断数据是否过期
         * @return
         */
        public boolean isExpired() {
            if (this.expireSeconds == NEVER_EXPIRE) {
                return false;
            }
            return System.currentTimeMillis() > this.createTimeMillis + this.expireSeconds * 1000;
        }

        public long getCreateTimeMillis() {
            return createTimeMillis;
        }

        public void setCreateTimeMillis(long createTimeMillis) {
            this.createTimeMillis = createTimeMillis;
        }

        public int getExpireSeconds() {
            return expireSeconds;
        }

        public void setExpireSeconds(int expireSeconds) {
            this.expireSeconds = expireSeconds;
        }

        public Object getValue() {
            return value;
        }
    }

    @Override
    public void removeByKeys(CacheType cacheType, Set<String> keys) {
        if (cacheType != null && CollectionUtils.isNotEmpty(keys)) {
            for (String key : keys) {
                content.remove(CommonUtil.buildCacheKey(cacheType, key));
            }
        }
    }
}
