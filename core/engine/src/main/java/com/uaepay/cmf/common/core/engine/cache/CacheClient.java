package com.uaepay.cmf.common.core.engine.cache;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.CacheType;

import java.util.Set;

/**
 * <p>缓存客户端</p>
 * <AUTHOR> won
 * @version $Id: CacheClient.java, v 0.1 2012-2-8 上午10:40:41 sean won Exp $
 */
public interface CacheClient extends BasicConstant{

    /**
     * 获取缓存对象
     * @param cacheType 缓存类型
     * @param key 键值
     * @return
     */
    Object get(CacheType cacheType, String key);

    /**
     * 增加缓存对象
     * @param cacheType 缓存类型
     * @param key 键值
     * @param data 数据对象
     * @param expireSeconds 过期时间，单位秒，0代表永不过期
     */
    void put(CacheType cacheType, String key, Object data, int expireSeconds);

    /**
     * 移除缓存对象
     * @param cacheType 缓存类型
     * @param key 键值
     * @return
     */
    Object remove(CacheType cacheType, String cdkey);

    /**
     * 清理
     * @param cacheType 缓存类型
     */
    void flush(CacheType cacheType);

    void removeByKeys(CacheType cacheType, Set<String> keys);
}
