package com.uaepay.cmf.common.core.engine.schedule.task;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;

import javax.annotation.Resource;

import com.dangdang.ddframe.job.api.ShardingContext;
import org.apache.skywalking.apm.toolkit.trace.TraceCrossThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.support.TransactionTemplate;

import com.uaepay.cmf.common.core.engine.schedule.DaemonContext;

/**
 * <p>任务抽象实现.</p>
 * <AUTHOR> won
 * @version $Id: AbstractDaemonTask.java, v 0.1 2011-1-12 下午04:01:44 sean won Exp $
 */
public abstract class AbstractDaemonTask<T> implements DaemonTask {
    protected static final Logger  logger    = LoggerFactory.getLogger("quartz");

    /** 事务模板 */
    @Resource
    protected TransactionTemplate  cmfTransactionTemplate;

    /** 线程池，需要内部定制。 */
    @Resource
    private ThreadPoolTaskExecutor daemonTaskExecutor;

    /** 每批处理数量。 */
    protected int                  batchSize = 100;

    /**上下文*/
    protected DaemonContext        context;


    @Override
    public void execute(ShardingContext shardingContext) {
        DaemonContext context =  new DaemonContext();
        context.setTriggerId(shardingContext.getTaskId());
        context.setFireTime(new Date());

        //TODO:需要在elasticjob里自定义一些job特殊的还行参数
        context.setTargetIdent(shardingContext.getJobParameter());

        execute(context);
    }

    @Override
    public DaemonTaskResult execute(DaemonContext context) {
        String triggerId = context.getTriggerId();
        this.setContext(context);
        logger.debug("{}任务开始执行{}", getTaskType().getMessage(), triggerId);

        int totalCount = 0;
        DaemonTaskResult result = new DaemonTaskResult(true, getTaskType());

        boolean hasMore = true;
        while (hasMore) {
            List<T> tasks = loadTask(batchSize);
            if (tasks == null || tasks.isEmpty()) {
                if (totalCount == 0) {
                    logger.debug("已无可执行任务.");
                }
                break; // 执行结束
            } else if (tasks.size() < batchSize) {
                // 可以避免异常数据重复执行
                hasMore = false;
            }
            totalCount = totalCount + tasks.size();

            //提交执行
            Map<T, FutureTask<TaskResult>> futureMap = new HashMap<>();
            for (Iterator<T> iterator = tasks.iterator(); iterator.hasNext();) {
                final T domain = iterator.next();
                FutureTask<TaskResult> futureTask = new FutureTask<>(
                        new FutureTaskCallable(domain));
                futureMap.put(domain, futureTask);
                daemonTaskExecutor.execute(futureTask);
            }

            //结果处理
            for (Entry<T, FutureTask<TaskResult>> entry : futureMap.entrySet()) {
                T domain = entry.getKey();
                String logPrefix = "任务[" + triggerId + "],订单ID[" + domain + "]";
                try {
                    TaskResult taskResult = entry.getValue().get();
                    if (taskResult.isSuccess()) {
                        logger.info(logPrefix + "执行成功");
                    } else {
                        logger.error(logPrefix + "执行失败");
                        sendToMonitor(domain);
                    }
                } catch (InterruptedException e) {
                    logger.error(logPrefix + "执行线程执行意外中断误", e);
                    sendToMonitor(domain);
                } catch (Exception e) {
                    logger.error(logPrefix + "执行时出现未知错误", e);
                    sendToMonitor(domain);
                }
            }
        }

        // 监控total count。
        if (totalCount > 0) {
            monitorTotalCount(totalCount);
        }

        return result;
    }

    /**
     *
     * <p>任务执行结果</p>
     *
     * <AUTHOR> won
     * @version $Id: AbstractDaemonTask.java, v 0.1 2011-6-30 下午05:58:50 sean won Exp $
     */
    protected class TaskResult {
        private boolean success;      //是否执行成功
        private String  resultMessage; //结果消息

        public TaskResult(boolean success, String message) {
            super();
            this.success = success;
            this.resultMessage = message;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getResultMessage() {
            return resultMessage;
        }
    }

    @TraceCrossThread
    public class FutureTaskCallable implements Callable<TaskResult> {
        /** 订单ID */
        private T domain;

        /**
         * 构造
         * @param domain
         */
        public FutureTaskCallable(T domain) {
            this.domain = domain;
        }

        @Override
        public TaskResult call() {
            String logPrefix = "订单ID[" + domain + "]";
            try {
                boolean rev = executeTask(domain);
                return new TaskResult(rev, "任务执行成功");
            } catch (Exception e) {
                logger.error(logPrefix + "出场时出现未知错误", e);
                return new TaskResult(false, e.getMessage());
            }
        }

    }

    /**
     * 加载任务.
     * @param batchSize 最大批次数
     * @return
     */
    protected abstract List<T> loadTask(int batchSize);

    /**
     * 执行任务.
     * @param domain
     * @return
     */
    protected abstract boolean executeTask(T domain);

    /**
     * 发送错误监控消息.
     * @param domain
     */
    protected abstract void sendToMonitor(T domain);

    public DaemonContext getContext() {
        return context;
    }

    public void setContext(DaemonContext context) {
        this.context = context;
    }

    /**
     * 总数监控。
     * @param totalCount 总数
     */
    protected abstract void monitorTotalCount(int totalCount);

    public void setCmfTransactionTemplate(TransactionTemplate cmfTransactionTemplate) {
        this.cmfTransactionTemplate = cmfTransactionTemplate;
    }

    public void setDaemonTaskExecutor(ThreadPoolTaskExecutor daemonTaskExecutor) {
        this.daemonTaskExecutor = daemonTaskExecutor;
    }

    public void setBatchSize(int batchSize) {
        this.batchSize = batchSize;
    }

}
