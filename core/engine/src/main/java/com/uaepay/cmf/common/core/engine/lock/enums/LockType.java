package com.uaepay.cmf.common.core.engine.lock.enums;

import lombok.Getter;

/**
 * <p>锁类型.</p>
 * <AUTHOR> won
 * @version $Id: LockType.java, v 0.1 2011-3-7 下午03:28:08 sean won Exp $
 */
@Getter
public enum LockType {
    //
    SYNCHRON("S", "同步锁"),
    EXCLUSION("E", "互斥锁");

    /** 代码 */
    private final String  code;
    /** 信息 */
    private final String  message;

    LockType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取ENUM
     * @param code
     * @return
     */
    public static LockType getByCode(String code) {

        for (LockType lockType : LockType.values()) {
            if (lockType.getCode().equals(code)) {
                return lockType;
            }
        }

        return null;
    }

}
