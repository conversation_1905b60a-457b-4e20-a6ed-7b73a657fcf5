package com.uaepay.cmf.common.core.engine.schedule.task;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.uaepay.cmf.common.core.engine.schedule.DaemonContext;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;

/**
 * <p>通用定时任务</p>
 * <AUTHOR> won
 * @version $Id: CommonTask.java, v 0.1 2011-1-11 下午02:02:05 sean won Exp $
 */
@Component("commonTask")
public class CommonTask extends AbstractDaemonTask {
    protected static final Logger logger = LoggerFactory.getLogger(CommonTask.class);

    @Override
    public DaemonTaskResult execute(DaemonContext context) {
        String triggerId = context.getTriggerId();

        logger.debug("{}任务开始执行{}", getTaskType().getMessage(), triggerId);

        try {
            // 等待5秒
            Thread.sleep(5000L);
        } catch (InterruptedException e) {
            logger.error("CommonTask.InterruptedException", e);
        }

        // 触发器成功启动。
        DaemonTaskResult result = new DaemonTaskResult(true, new DaemonTaskType("common", "通用任务"));
        logger.debug("{},{}任务执行结束，允许重新执行。", getTaskType().getMessage(), triggerId);
        return result;
    }

    @Override
    public DaemonTaskType getTaskType() {
        return new DaemonTaskType("common", "通用任务");
    }

    @Override
    protected boolean executeTask(Object domain) {
        return false;
    }

    @Override
    protected List loadTask(int batchSize) {
        return null;
    }

    @Override
    protected void monitorTotalCount(int totalCount) {

    }

    @Override
    protected void sendToMonitor(Object domain) {

    }
}
