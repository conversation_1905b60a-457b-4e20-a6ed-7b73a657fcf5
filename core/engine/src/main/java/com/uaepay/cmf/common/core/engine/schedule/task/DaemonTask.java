package com.uaepay.cmf.common.core.engine.schedule.task;

import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.engine.schedule.DaemonContext;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;

/**
 * <p>定时任务</p>
 * <AUTHOR> won
 * @version $Id: DaemonTask.java, v 0.1 2010-6-1 下午01:28:18 sean won Exp $
 */
public interface DaemonTask extends BasicConstant, SimpleJob {

    /**
     * 定时任务执行.
     * @param context
     * @return
     */
    DaemonTaskResult execute(DaemonContext context);

    /**
     * 获取任务类型
     * @return
     */
    DaemonTaskType getTaskType();
}
