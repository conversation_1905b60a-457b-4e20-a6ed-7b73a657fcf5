package com.uaepay.cmf.common.core.engine.cache;

import java.time.Duration;

/**
 * <p>CacheLockService</p>
 *
 * <AUTHOR>
 * @version CacheLockService.java v1.0  2022/11/21 11:21
 */
public interface CacheLockService {


    /**
     * 获取锁
     * @param lockKey 锁定key
     * @param lockTime 锁定时间
     * @return 锁定时长
     */
    boolean lock(String lockKey, Duration lockTime);


    /**
     * 释放锁
     *
     * @param lockKey 锁key
     */
    void unlock(String lockKey);
}
