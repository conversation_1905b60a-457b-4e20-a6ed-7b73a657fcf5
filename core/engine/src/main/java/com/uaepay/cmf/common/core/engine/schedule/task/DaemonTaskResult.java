package com.uaepay.cmf.common.core.engine.schedule.task;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>定时任务执行结果</p>
 * <AUTHOR> won
 * @version $Id: DaemonTaskResult.java, v 0.1 2010-6-1 下午02:09:14 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class DaemonTaskResult extends BaseResult {
    private static final long    serialVersionUID = 2913842637618034117L;

    /** 定时任务类型 */
    private DaemonTaskType       taskType;
    /** 结果代码 */
    private DaemonTaskResultCode resultCode;

    private Object               taskResultVO;

    /**
     * 默认构造
     */
    public DaemonTaskResult() {

    }

    /**
     * 根据结果和任务类型构造
     * @param success
     */
    DaemonTaskResult(boolean success, DaemonTaskType taskType) {
        this.success = success;
        this.taskType = taskType;
    }

    public void setResultCode(DaemonTaskResultCode resultCode) {
        this.resultCode = resultCode;
        this.success = (resultCode == DaemonTaskResultCode.SUCCESS);
    }

    @Override
    public String getResultMessage() {
        String message = StringUtils.defaultIfBlank(resultMessage, StringUtils.EMPTY);
        return resultCode == null ? message : "[" + resultCode.getMessage() + "]" + message;
    }

}
