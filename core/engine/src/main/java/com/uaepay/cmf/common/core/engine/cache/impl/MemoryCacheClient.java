package com.uaepay.cmf.common.core.engine.cache.impl;


import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.common.core.engine.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <p>Cache统一缓存客户端实现</p>
 *
 * <AUTHOR> won
 * @version $Id: MemoryCacheClient.java, v 0.1 2012-2-8 下午01:09:24 sean won Exp $
 */
@Slf4j
@Service("memoryCacheClient")
public class MemoryCacheClient implements CacheClient {

    @Resource
    private RedisTemplate redisTemplate;

    @Value("${spring.application.name}")
    private String applicationName;

    @Override
    public Object get(CacheType cacheType, String key) {
        try {
            return redisTemplate.opsForValue().get(CommonUtil.buildCacheKey(cacheType, key));
        } catch (Exception e) {
            log.error("[" + cacheType.getPrefix() + key + "]获取异常", e);
            throw new RuntimeException("统一缓存访问异常");
        }
    }

    @Override
    public void put(CacheType cacheType, String key, Object data, int expireSeconds) {
        try {
            redisTemplate.opsForValue().set(CommonUtil.buildCacheKey(cacheType, key), data, expireSeconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("[" + cacheType.getPrefix() + key + "]设置异常", e);
            throw new RuntimeException("统一缓存访问异常");
        }
    }

    @Override
    public Object remove(CacheType cacheType, String key) {
        Boolean delete = false;
        try {
            delete = redisTemplate.delete(CommonUtil.buildCacheKey(cacheType, key));
        } catch (Exception e) {
            log.error("[" + cacheType.getPrefix() + key + "] delete error,", e);
            throw new RuntimeException("delete cache error");
        }
        return delete;
    }

    @Override
    public void flush(CacheType cacheType) {
        ScanOptions options = ScanOptions.scanOptions().count(10000).match(applicationName + ":" + cacheType.getPrefix() + "*").build();
        int count = 0;
        try (Cursor<byte[]> cursor = (Cursor<byte[]>) redisTemplate.executeWithStickyConnection(redisConnection -> redisConnection.scan(options))) {
            Set<String> result = new HashSet<>();
            while (cursor.hasNext()) {
                result.add(new String(cursor.next(), StandardCharsets.UTF_8));
                count++;
            }
            if (CollectionUtils.isNotEmpty(result)) {
                redisTemplate.delete(result);
            }
        } catch (Exception e) {
            log.error("clear all error", e);
            throw new RuntimeException("clear all error");
        } finally {
            log.info("clearKeyAllByScan count:{}", count);
        }
    }

    @Override
    public void removeByKeys(CacheType cacheType, Set<String> keys) {
        if (cacheType != null && CollectionUtils.isNotEmpty(keys)) {
            Set<String> redisKeys = new HashSet<>();
            for (String key : keys) {
                redisKeys.add(CommonUtil.buildCacheKey(CacheType.CMF_SYS_CONFIGURATION, key));
            }
            try {
                redisTemplate.delete(redisKeys);
            } catch (Exception e) {
                log.error("remove by keys error, ", e);
                throw new RuntimeException("remove by keys error");
            }
        } else {
            log.warn("removeBykeys keys is empty");
        }
    }
}
