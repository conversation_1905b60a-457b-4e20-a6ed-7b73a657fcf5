package com.uaepay.cmf.common.core.engine.generator;

/**
 * <p>序列名枚举.</p>
 *
 * <AUTHOR>
 * @version SequenceName.java 1.0 Created@2017-11-14 15:32 $
 */
public enum SequenceNameEnum {
    /**
     *
     */
    CARD_TOKEN("SEQ_CARD_TOKEN", "卡token"),
    CMF_ORDER("SEQ_CMF_ORDER", "cmf流水号"),
    PAYMENT_NOTIFY_LOG("SEQ_PAYMENT_NOTIFY_LOG", "pe通知"),
    INST_ORDER("SEQ_INST_ORDER", "机构订单"),
    INST_RESULT("SEQ_INST_ORDER_RESULT", "机构结果"),
    BATCH_ORDER("SEQ_INST_BATCH_ORDER", "批量订单"),
    BATCH_RESULT("SEQ_INST_BATCH_RESULT", "批量结果"),
    CONTROL_ORDER("SEQ_CONTROL_ORDER_ID", "控制订单"),
    CONTROL_RESULT("SEQ_CONTROL_RESULT_ID", "控制结果"),
    NOTIFY_3DS_RESULT("SEQ_NOTIFY_3DS_RESULT_ID", "3ds结果"),
    ;

    private String code;
    private String description;

    SequenceNameEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
