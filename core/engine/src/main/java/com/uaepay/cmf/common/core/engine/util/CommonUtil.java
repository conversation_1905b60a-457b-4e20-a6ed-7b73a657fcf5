package com.uaepay.cmf.common.core.engine.util;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.util.Date;

/**
 * <p>通用工具类</p>
 *
 * <AUTHOR> won
 * @version $Id: BizTypeUtil, v 0.1 2009-10-28 下午01:24:39 cc Exp
 * $
 */
public class CommonUtil implements BasicConstant {

    private static final String LEFT_BIG_BRACKET = "${";
    private static final String RIGHT_BIG_BRACKET = "}";

    /**
     * 根据原子信息组装键值
     *
     * @param factors
     * @return
     */
    public static String key(String... factors) {
        StringBuilder sb = new StringBuilder();
        for (String factor : factors) {
            sb.append(factor).append(SPLIT_TAG);
        }

        return sb.substring(0, sb.length() - 1);
    }


    /**
     * 校验x天以前的订单，不允许继续走下去
     *
     * @param paymentSeqNo
     * @return
     */
    public static boolean validateDateBySeqNo(String paymentSeqNo, long day) {
        if (paymentSeqNo == null || paymentSeqNo.length() < 8) {
            return true;
        }
        String dateStr = paymentSeqNo.substring(0, 8);

        Date date;
        try {
            date = DateUtil.parseDateNoTime(dateStr);
        } catch (ParseException e) {
            return true;
        }
        return !DateUtil.dateLessThanNowAddMin(date, 60L * 24L * day);
    }

    public static String i18nCodeBuild(String text) {
        return i18nCodeBuild(text, LEFT_BIG_BRACKET, RIGHT_BIG_BRACKET);
    }


    public static String i18nCodeBuild(String text, String left, String right) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        if (StringUtils.isBlank(left) || StringUtils.isBlank(right)) {
            left = LEFT_BIG_BRACKET;
            right = RIGHT_BIG_BRACKET;
        }
        return left + text + right;
    }

    public static boolean validateDateBySeqNo(String paymentSeqNo) {
        return validateDateBySeqNo(paymentSeqNo, 90L);
    }

    public static String buildCacheKey(CacheType cacheType, String key) {
        return cacheType.getPrefix() + key;
    }
}
