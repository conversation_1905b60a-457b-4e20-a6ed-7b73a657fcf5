package com.uaepay.cmf.common.core.engine.schedule;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <p>上下文.</p>
 * <AUTHOR> won
 * @version $Id: DaemonContext.java, v 0.1 2011-4-7 上午09:32:48 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class DaemonContext {
    /** 触发器ID. */
    private String triggerId;
    /** 触发时间. */
    private Date fireTime;
    /** 目标标识. */
    private String targetIdent;

}
