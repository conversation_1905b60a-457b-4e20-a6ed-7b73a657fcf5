package com.uaepay.cmf.common.core.engine.lock;

import com.uaepay.cmf.common.core.engine.lock.enums.LockType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <p>锁域对象.</p>
 *
 * <AUTHOR> won
 * @version $Id: Lock.java, v 0.1 2011-3-7 下午05:54:46 sean won Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class Lock {
    /**
     * 锁键.
     */
    private String lockKey;
    /**
     * 联合锁键.
     */
    private List<String> unionKeys;
    /**
     * 锁名.
     */
    private String lockName;
    /**
     * 锁描述.
     */
    private String lockDescription;
    /**
     * 锁类型.
     */
    private LockType lockType;
    /**
     * 超时时间.
     */
    private long lockSecond;

    public Lock() {
    }

    public Lock(String lockKey, String lockName, LockType lockType, long lockSecond) {
        this.lockKey = lockKey;
        this.lockName = lockName;
        this.lockType = lockType;
        this.lockSecond = lockSecond;
    }

}
