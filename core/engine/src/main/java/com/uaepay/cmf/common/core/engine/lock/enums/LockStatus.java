package com.uaepay.cmf.common.core.engine.lock.enums;

import lombok.Getter;

/**
 * <p>锁状态</p>
 * <AUTHOR> won
 * @version $Id: LockStatus.java, v 0.1 2011-3-7 下午06:09:15 sean won Exp $
 */
@Getter
public enum LockStatus {
    //
    EXCUTE("E", "执行"),
    FINISH("F", "完成");

    /** 代码 */
    private final String  code;
    /** 信息 */
    private final String  message;

    LockStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 通过代码获取ENUM
     * @param code
     * @return
     */
    public static LockStatus getByCode(String code) {

        for (LockStatus lockStatus : LockStatus.values()) {
            if (lockStatus.getCode().equals(code)) {
                return lockStatus;
            }
        }

        return null;
    }

}
