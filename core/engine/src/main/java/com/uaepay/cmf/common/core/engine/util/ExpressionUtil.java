package com.uaepay.cmf.common.core.engine.util;

import org.springframework.util.CollectionUtils;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>表达式工具类</p>
 * <AUTHOR>
 * @version $Id: ExpressionUtil.java, v 0.1 2012-8-6 下午4:11:23 fuyangbiao Exp $
 */
public class ExpressionUtil implements BasicConstant {

    /**
     * 判断素组是否包含
     * @param more
     * @param less
     * @return
     */
    public static boolean contains(String[] more, String[] less) {
        if (more == null) {
            return less == null;
        }

        if (less == null) {
            return false;
        }

        for (String value : less) {
            if (!contains(more, value)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 判断数组是否包含值
     * @param array
     * @param value
     * @return
     */
    public static boolean contains(String[] array, String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }

        if (array == null) {
            return false;
        }

        return CollectionUtils.arrayToList(array).contains(value);
    }

    /**
     * 判断数组与素组字符串是否一致
     * 默认以“,”作为分隔符
     * @param array
     * @param arrayStr
     * @return
     */
    public static boolean match(String[] array, String arrayStr) {
        return match(array, arrayStr, null);
    }

    /**
     * 判断数组与素组字符串是否一致
     * @param array
     * @param arrayStr
     * @param spiltTag
     * @return
     */
    public static boolean match(String[] array, String arrayStr, String spiltTag) {
        if (StringUtils.isBlank(arrayStr)) {
            return false;
        }

        String[] valueArray = arrayStr.split(StringUtils.isBlank(spiltTag) ? CHAR_COMMA : spiltTag);
        return contains(array, valueArray) && contains(valueArray, array);
    }
    
    /**
     * 判断数组与值是否包含
     * @param r
     * @param set
     * @return
     */
    public boolean in(String r, String[] set) {
        if(r==null || set==null)return false;
        
        for(String l : set){
            if(r.equals(l))return true;
        }
        return false;
    }
    
    /**
     * 判断数组与值手否一致,后续不会使用
     * @param set
     * @param value
     * @return
     */
    public boolean is(String[] set, String value){
        return match(set, value);
    }
}
