package com.uaepay.cmf.common.core.engine.schedule.task;

import lombok.Getter;

/**
 * <p>定时任务结果代码</p>
 * <AUTHOR> won
 * @version $Id: DaemonTaskResultCode.java, v 0.1 2010-6-1 下午02:12:40 sean won Exp $
 */
@Getter
enum DaemonTaskResultCode {
    EXCEPTION("执行异常"),
    
    SUCCESS("执行成功");
    
    private final String message;
    
    DaemonTaskResultCode(String message){
        this.message = message;
    }

}
