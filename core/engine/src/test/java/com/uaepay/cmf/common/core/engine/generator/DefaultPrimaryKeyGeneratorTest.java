package com.uaepay.cmf.common.core.engine.generator;

import com.uaepay.basis.sequenceutil.IDGen;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * DefaultPrimaryKeyGenerator单元测试
 * 测试主键生成器的所有功能
 */
class DefaultPrimaryKeyGeneratorTest {

    @Mock
    private IDGen idGen;

    @InjectMocks
    private DefaultPrimaryKeyGenerator primaryKeyGenerator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("测试generateKey方法-正常情况")
    void testGenerateKey_Normal() {
        // Given
        when(idGen.get("SEQ_CMF_ORDER")).thenReturn(123L);

        // When
        String result = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);

        // Then
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertThat(result).isEqualTo(today + "000000123");
        verify(idGen).get("SEQ_CMF_ORDER");
    }

    @Test
    @DisplayName("测试generateKey方法-大序列号需要取模")
    void testGenerateKey_LargeSequence() {
        // Given - 序列号超过9位数，需要取模
        when(idGen.get("SEQ_CMF_ORDER")).thenReturn(1000000123L);

        // When
        String result = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);

        // Then
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 1000000123 % 10^9 = 123
        assertThat(result).isEqualTo(today + "000000123");
        verify(idGen).get("SEQ_CMF_ORDER");
    }

    @Test
    @DisplayName("测试generateKey方法-序列号为0")
    void testGenerateKey_ZeroSequence() {
        // Given
        when(idGen.get("SEQ_CMF_ORDER")).thenReturn(0L);

        // When
        String result = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);

        // Then
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertThat(result).isEqualTo(today + "000000000");
        verify(idGen).get("SEQ_CMF_ORDER");
    }

    @Test
    @DisplayName("测试generateKey方法-最大9位序列号")
    void testGenerateKey_MaxSequence() {
        // Given - 最大9位数
        when(idGen.get("SEQ_CMF_ORDER")).thenReturn(999999999L);

        // When
        String result = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);

        // Then
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertThat(result).isEqualTo(today + "999999999");
        verify(idGen).get("SEQ_CMF_ORDER");
    }

    @Test
    @DisplayName("测试generateKey方法-不同的SequenceNameEnum")
    void testGenerateKey_DifferentSequenceNames() {
        // Given
        when(idGen.get("SEQ_CMF_ORDER")).thenReturn(123L);
        when(idGen.get("SEQ_CARD_TOKEN")).thenReturn(456L);

        // When
        String result1 = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);
        String result2 = primaryKeyGenerator.generateKey(SequenceNameEnum.CARD_TOKEN);

        // Then
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertThat(result1).isEqualTo(today + "000000123");
        assertThat(result2).isEqualTo(today + "000000456");
        verify(idGen).get("SEQ_CMF_ORDER");
        verify(idGen).get("SEQ_CARD_TOKEN");
    }

    @Test
    @DisplayName("测试generateKey方法-序列号需要左补零")
    void testGenerateKey_PaddingZeros() {
        // Given
        when(idGen.get("SEQ_CMF_ORDER")).thenReturn(1L);

        // When
        String result = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);

        // Then
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertThat(result).isEqualTo(today + "000000001");
        verify(idGen).get("SEQ_CMF_ORDER");
    }

    @Test
    @DisplayName("测试generateKey方法-中等长度序列号")
    void testGenerateKey_MediumSequence() {
        // Given
        when(idGen.get("SEQ_CMF_ORDER")).thenReturn(12345L);

        // When
        String result = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);

        // Then
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertThat(result).isEqualTo(today + "000012345");
        verify(idGen).get("SEQ_CMF_ORDER");
    }

    @Test
    @DisplayName("测试生成的主键格式")
    void testGenerateKey_Format() {
        // Given
        when(idGen.get("SEQ_CMF_ORDER")).thenReturn(123L);

        // When
        String result = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);

        // Then
        assertThat(result).hasSize(17); // 8位日期 + 9位序列号
        assertThat(result).matches("\\d{17}"); // 全部是数字
        assertThat(result.substring(0, 8)).matches("\\d{8}"); // 前8位是日期
        assertThat(result.substring(8)).matches("\\d{9}"); // 后9位是序列号
    }

    @Test
    @DisplayName("测试多次调用generateKey")
    void testGenerateKey_MultipleCalls() {
        // Given
        when(idGen.get("SEQ_CMF_ORDER")).thenReturn(100L, 200L, 300L);

        // When
        String result1 = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);
        String result2 = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);
        String result3 = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);

        // Then
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertThat(result1).isEqualTo(today + "000000100");
        assertThat(result2).isEqualTo(today + "000000200");
        assertThat(result3).isEqualTo(today + "000000300");
        verify(idGen, times(3)).get("SEQ_CMF_ORDER");
    }

    @Test
    @DisplayName("测试类注解")
    void testClassAnnotations() {
        // When
        Class<?> clazz = primaryKeyGenerator.getClass();

        // Then
        assertThat(clazz.isAnnotationPresent(org.springframework.stereotype.Service.class)).isTrue();
    }

    @Test
    @DisplayName("测试字段注解")
    void testFieldAnnotations() throws NoSuchFieldException {
        // When
        Class<?> clazz = primaryKeyGenerator.getClass();
        Field idGenField = clazz.getDeclaredField("idGen");

        // Then
        assertThat(idGenField.isAnnotationPresent(javax.annotation.Resource.class)).isTrue();
        javax.annotation.Resource resourceAnnotation = idGenField.getAnnotation(javax.annotation.Resource.class);
        assertThat(resourceAnnotation.name()).isEqualTo("idGen");
    }

    @Test
    @DisplayName("测试常量值")
    void testConstants() throws NoSuchFieldException, IllegalAccessException {
        // When
        Class<?> clazz = primaryKeyGenerator.getClass();
        Field seqLengthField = clazz.getDeclaredField("SEQ_LENGTH");
        seqLengthField.setAccessible(true);

        // Then
        assertThat(seqLengthField.get(null)).isEqualTo(9);
        assertThat(java.lang.reflect.Modifier.isStatic(seqLengthField.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isFinal(seqLengthField.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isPrivate(seqLengthField.getModifiers())).isTrue();
    }

    @Test
    @DisplayName("测试接口实现")
    void testInterfaceImplementation() {
        // When
        Class<?> clazz = primaryKeyGenerator.getClass();

        // Then
        assertThat(clazz.getInterfaces()).hasSize(1);
        assertThat(clazz.getInterfaces()[0]).isEqualTo(PrimaryKeyGenerator.class);
    }

    @Test
    @DisplayName("测试字段类型")
    void testFieldTypes() throws NoSuchFieldException {
        // When
        Class<?> clazz = primaryKeyGenerator.getClass();
        Field idGenField = clazz.getDeclaredField("idGen");

        // Then
        assertThat(idGenField.getType()).isEqualTo(IDGen.class);
        assertThat(java.lang.reflect.Modifier.isPrivate(idGenField.getModifiers())).isTrue();
    }

    @Test
    @DisplayName("测试方法存在性")
    void testMethodsExistence() throws NoSuchMethodException {
        // When
        Class<?> clazz = primaryKeyGenerator.getClass();

        // Then
        assertThat(clazz.getMethod("generateKey", SequenceNameEnum.class)).isNotNull();
        assertThat(clazz.getDeclaredMethod("getSequence", String.class, int.class)).isNotNull();
    }

    @Test
    @DisplayName("测试方法修饰符")
    void testMethodModifiers() throws NoSuchMethodException {
        // When
        Class<?> clazz = primaryKeyGenerator.getClass();
        java.lang.reflect.Method generateKeyMethod = clazz.getMethod("generateKey", SequenceNameEnum.class);
        java.lang.reflect.Method getSequenceMethod = clazz.getDeclaredMethod("getSequence", String.class, int.class);

        // Then
        assertThat(java.lang.reflect.Modifier.isPublic(generateKeyMethod.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isPrivate(getSequenceMethod.getModifiers())).isTrue();
    }

    @Test
    @DisplayName("测试方法返回类型")
    void testMethodReturnTypes() throws NoSuchMethodException {
        // When
        Class<?> clazz = primaryKeyGenerator.getClass();
        java.lang.reflect.Method generateKeyMethod = clazz.getMethod("generateKey", SequenceNameEnum.class);
        java.lang.reflect.Method getSequenceMethod = clazz.getDeclaredMethod("getSequence", String.class, int.class);

        // Then
        assertThat(generateKeyMethod.getReturnType()).isEqualTo(String.class);
        assertThat(getSequenceMethod.getReturnType()).isEqualTo(long.class);
    }

    @Test
    @DisplayName("测试边界情况-负数序列号")
    void testGenerateKey_NegativeSequence() {
        // Given - 负数序列号
        when(idGen.get("SEQ_CMF_ORDER")).thenReturn(-123L);

        // When
        String result = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);

        // Then
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // -123 % 10^9 在Java中结果是负数，但实际行为需要验证
        assertThat(result).startsWith(today);
        assertThat(result).hasSize(17);
        verify(idGen).get("SEQ_CMF_ORDER");
    }

    @Test
    @DisplayName("测试getSequence私有方法的逻辑")
    void testGetSequence_Logic() throws Exception {
        // Given
        java.lang.reflect.Method getSequenceMethod = primaryKeyGenerator.getClass()
                .getDeclaredMethod("getSequence", String.class, int.class);
        getSequenceMethod.setAccessible(true);
        when(idGen.get("test")).thenReturn(1234567890L);

        // When
        long result = (Long) getSequenceMethod.invoke(primaryKeyGenerator, "test", 9);

        // Then
        // 1234567890 % 10^9 = 234567890
        assertThat(result).isEqualTo(234567890L);
        verify(idGen).get("test");
    }

    @Test
    @DisplayName("测试所有SequenceNameEnum值")
    void testGenerateKey_AllSequenceNames() {
        // Given
        SequenceNameEnum[] allValues = SequenceNameEnum.values();
        for (int i = 0; i < allValues.length; i++) {
            when(idGen.get(allValues[i].getCode())).thenReturn((long) (i + 1));
        }

        // When & Then
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        for (int i = 0; i < allValues.length; i++) {
            String result = primaryKeyGenerator.generateKey(allValues[i]);
            assertThat(result).startsWith(today);
            assertThat(result).hasSize(17);
            verify(idGen).get(allValues[i].getCode());
        }
    }

    @Test
    @DisplayName("测试类的可见性")
    void testClassVisibility() {
        // When
        Class<?> clazz = primaryKeyGenerator.getClass();

        // Then
        assertThat(java.lang.reflect.Modifier.isPublic(clazz.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isFinal(clazz.getModifiers())).isFalse();
        assertThat(java.lang.reflect.Modifier.isAbstract(clazz.getModifiers())).isFalse();
    }

    @Test
    @DisplayName("测试类构造函数")
    void testClassConstructors() {
        // When
        Class<?> clazz = primaryKeyGenerator.getClass();
        java.lang.reflect.Constructor<?>[] constructors = clazz.getConstructors();

        // Then
        assertThat(constructors).hasSize(1);
        assertThat(constructors[0].getParameterCount()).isEqualTo(0);
        assertThat(java.lang.reflect.Modifier.isPublic(constructors[0].getModifiers())).isTrue();
    }

    @Test
    @DisplayName("测试toString方法")
    void testToStringMethod() {
        // When
        String result = primaryKeyGenerator.toString();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("DefaultPrimaryKeyGenerator");
    }

    @Test
    @DisplayName("测试equals和hashCode方法")
    void testEqualsAndHashCode() {
        // Given
        DefaultPrimaryKeyGenerator generator1 = new DefaultPrimaryKeyGenerator();
        DefaultPrimaryKeyGenerator generator2 = new DefaultPrimaryKeyGenerator();

        // When & Then
        assertThat(generator1).isNotEqualTo(generator2); // 不同实例
        assertThat(generator1.hashCode()).isNotEqualTo(generator2.hashCode());
        assertThat(generator1).isEqualTo(generator1); // 自己等于自己
    }
}
