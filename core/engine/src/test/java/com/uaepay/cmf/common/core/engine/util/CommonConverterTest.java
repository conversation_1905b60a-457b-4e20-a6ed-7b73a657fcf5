package com.uaepay.cmf.common.core.engine.util;

import com.uaepay.common.domain.Extension;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * CommonConverter类单元测试
 * 测试通用转换工具方法
 */
class CommonConverterTest {

    @Test
    @DisplayName("测试convertMap方法-null扩展对象")
    void testConvertMap_NullExtension() {
        // When
        Map<String, String> result = CommonConverter.convertMap(null);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("测试convertMap方法-空扩展对象")
    void testConvertMap_EmptyExtension() {
        // Given
        Extension extension = new Extension();
        
        // When
        Map<String, String> result = CommonConverter.convertMap(extension);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("测试convertMap方法-正常转换")
    void testConvertMap_NormalConversion() {
        // Given
        Extension extension = new Extension();
        extension.add("TEST_KEY", "test_value");
        extension.add("ANOTHER_KEY", "another_value");
        
        // When
        Map<String, String> result = CommonConverter.convertMap(extension);
        
        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get("testKey")).isEqualTo("test_value");
        assertThat(result.get("anotherKey")).isEqualTo("another_value");
    }

    @Test
    @DisplayName("测试convertExtension方法-null Map")
    void testConvertExtension_NullMap() {
        // When
        Extension result = CommonConverter.convertExtension(null);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getEntryList()).isEmpty();
    }

    @Test
    @DisplayName("测试convertExtension方法-空Map")
    void testConvertExtension_EmptyMap() {
        // Given
        Map<String, String> map = new HashMap<>();
        
        // When
        Extension result = CommonConverter.convertExtension(map);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getEntryList()).isEmpty();
    }

    @Test
    @DisplayName("测试convertExtension方法-正常转换")
    void testConvertExtension_NormalConversion() {
        // Given
        Map<String, String> map = new HashMap<>();
        map.put("testKey", "test_value");
        map.put("anotherKey", "another_value");
        
        // When
        Extension result = CommonConverter.convertExtension(map);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getEntryList()).hasSize(2);
        // Extension的getValue方法可能需要原始key或转换后的key，我们测试两种情况
        boolean foundTestValue = result.getValue("TEST_KEY") != null || result.getValue("testKey") != null;
        boolean foundAnotherValue = result.getValue("ANOTHER_KEY") != null || result.getValue("anotherKey") != null;
        assertThat(foundTestValue).isTrue();
        assertThat(foundAnotherValue).isTrue();
    }

    @Test
    @DisplayName("测试convertExtensionWithoutConvertKey方法-不转换键")
    void testConvertExtensionWithoutConvertKey() {
        // Given
        Map<String, String> map = new HashMap<>();
        map.put("testKey", "test_value");
        map.put("AnotherKey", "another_value");
        
        // When
        Extension result = CommonConverter.convertExtensionWithoutConvertKey(map);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getEntryList()).hasSize(2);
        assertThat(result.getValue("testKey")).isEqualTo("test_value");
        assertThat(result.getValue("AnotherKey")).isEqualTo("another_value");
    }

    @Test
    @DisplayName("测试convertFromDb方法-空字符串")
    void testConvertFromDb_EmptyString() {
        // When
        Map<String, String> result = CommonConverter.convertFromDb("");
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("测试convertFromDb方法-null字符串")
    void testConvertFromDb_NullString() {
        // When
        Map<String, String> result = CommonConverter.convertFromDb(null);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("测试convertFromDb方法-JSON格式")
    void testConvertFromDb_JsonFormat() {
        // Given
        String jsonString = "{\"testKey\":\"test_value\",\"anotherKey\":\"another_value\"}";
        
        // When
        Map<String, String> result = CommonConverter.convertFromDb(jsonString);
        
        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get("testKey")).isEqualTo("test_value");
        assertThat(result.get("anotherKey")).isEqualTo("another_value");
    }

    @Test
    @DisplayName("测试convertFromDb方法-数组格式")
    void testConvertFromDb_ArrayFormat() {
        // Given
        String arrayString = "[[\"TEST_KEY\",\"test_value\"],[\"ANOTHER_KEY\",\"another_value\"]]";
        
        // When
        Map<String, String> result = CommonConverter.convertFromDb(arrayString);
        
        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get("testKey")).isEqualTo("test_value");
        assertThat(result.get("anotherKey")).isEqualTo("another_value");
    }

    @Test
    @DisplayName("测试convertToDb方法-空Map")
    void testConvertToDb_EmptyMap() {
        // Given
        Map<String, String> map = new HashMap<>();
        
        // When
        String result = CommonConverter.convertToDb(map);
        
        // Then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("测试convertToDb方法-null Map")
    void testConvertToDb_NullMap() {
        // When
        String result = CommonConverter.convertToDb(null);
        
        // Then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("测试convertToDb方法-正常转换")
    void testConvertToDb_NormalConversion() {
        // Given
        Map<String, String> map = new HashMap<>();
        map.put("testKey", "test_value");
        map.put("anotherKey", "another_value");
        
        // When
        String result = CommonConverter.convertToDb(map);
        
        // Then
        assertThat(result).isNotEmpty();
        assertThat(result).contains("testKey");
        assertThat(result).contains("test_value");
        assertThat(result).contains("anotherKey");
        assertThat(result).contains("another_value");
    }

    @Test
    @DisplayName("测试convertKey方法-空字符串")
    void testConvertKey_EmptyString() {
        // When
        String result = CommonConverter.convertKey("");
        
        // Then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("测试convertKey方法-null字符串")
    void testConvertKey_NullString() {
        // When
        String result = CommonConverter.convertKey(null);
        
        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试convertKey方法-无分隔符")
    void testConvertKey_NoSeparator() {
        // Given
        String key = "testkey";
        
        // When
        String result = CommonConverter.convertKey(key);
        
        // Then
        assertThat(result).isEqualTo("testkey");
    }

    @Test
    @DisplayName("测试convertKey方法-标准转换")
    void testConvertKey_StandardConversion() {
        // When & Then
        assertThat(CommonConverter.convertKey("AAAA_BBB_CCC")).isEqualTo("aaaaBbbCcc");
        assertThat(CommonConverter.convertKey("aaA_B")).isEqualTo("aaaB");
        assertThat(CommonConverter.convertKey("aSa_b_C")).isEqualTo("asaBC");
    }

    @Test
    @DisplayName("测试convertKey方法-单个分隔符")
    void testConvertKey_SingleSeparator() {
        // When
        String result = CommonConverter.convertKey("TEST_KEY");
        
        // Then
        assertThat(result).isEqualTo("testKey");
    }

    @Test
    @DisplayName("测试convertKey方法-多个分隔符")
    void testConvertKey_MultipleSeparators() {
        // When
        String result = CommonConverter.convertKey("FIRST_SECOND_THIRD_FOURTH");
        
        // Then
        assertThat(result).isEqualTo("firstSecondThirdFourth");
    }

    @Test
    @DisplayName("测试convertKey方法-包含空格")
    void testConvertKey_WithSpaces() {
        // When
        String result = CommonConverter.convertKey(" TEST_KEY ");
        
        // Then
        assertThat(result).isEqualTo("testKey");
    }

    @Test
    @DisplayName("测试完整转换流程")
    void testCompleteConversionFlow() {
        // Given
        Map<String, String> originalMap = new HashMap<>();
        originalMap.put("TEST_KEY", "test_value");
        originalMap.put("ANOTHER_KEY", "another_value");
        
        // When
        // Map -> Extension -> Map
        Extension extension = CommonConverter.convertExtension(originalMap);
        Map<String, String> convertedMap = CommonConverter.convertMap(extension);
        
        // Then
        assertThat(convertedMap).hasSize(2);
        assertThat(convertedMap.get("testKey")).isEqualTo("test_value");
        assertThat(convertedMap.get("anotherKey")).isEqualTo("another_value");
    }
} 