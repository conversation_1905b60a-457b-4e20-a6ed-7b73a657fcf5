package com.uaepay.cmf.common.core.engine.lock;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * LockException异常类单元测试
 * 测试锁异常类的所有构造函数
 */
class LockExceptionTest {

    @Test
    @DisplayName("测试默认构造函数")
    void testDefaultConstructor() {
        // When
        LockException exception = new LockException();
        
        // Then
        assertThat(exception).isInstanceOf(Exception.class);
        assertThat(exception.getMessage()).isNull();
        assertThat(exception.getCause()).isNull();
    }

    @Test
    @DisplayName("测试带消息的构造函数")
    void testMessageConstructor() {
        // Given
        String expectedMessage = "锁操作失败";
        
        // When
        LockException exception = new LockException(expectedMessage);
        
        // Then
        assertThat(exception).isInstanceOf(Exception.class);
        assertThat(exception.getMessage()).isEqualTo(expectedMessage);
        assertThat(exception.getCause()).isNull();
    }

    @Test
    @DisplayName("测试带消息的构造函数-null消息")
    void testMessageConstructor_NullMessage() {
        // When
        LockException exception = new LockException((String) null);
        
        // Then
        assertThat(exception).isInstanceOf(Exception.class);
        assertThat(exception.getMessage()).isNull();
        assertThat(exception.getCause()).isNull();
    }

    @Test
    @DisplayName("测试带原因的构造函数")
    void testCauseConstructor() {
        // Given
        RuntimeException cause = new RuntimeException("原因异常");
        
        // When
        LockException exception = new LockException(cause);
        
        // Then
        assertThat(exception).isInstanceOf(Exception.class);
        assertThat(exception.getCause()).isEqualTo(cause);
        assertThat(exception.getMessage()).isEqualTo("java.lang.RuntimeException: 原因异常");
    }

    @Test
    @DisplayName("测试带原因的构造函数-null原因")
    void testCauseConstructor_NullCause() {
        // When
        LockException exception = new LockException((Throwable) null);
        
        // Then
        assertThat(exception).isInstanceOf(Exception.class);
        assertThat(exception.getCause()).isNull();
        assertThat(exception.getMessage()).isNull();
    }

    @Test
    @DisplayName("测试带消息和原因的构造函数")
    void testMessageAndCauseConstructor() {
        // Given
        String expectedMessage = "锁操作失败";
        RuntimeException cause = new RuntimeException("原因异常");
        
        // When
        LockException exception = new LockException(expectedMessage, cause);
        
        // Then
        assertThat(exception).isInstanceOf(Exception.class);
        assertThat(exception.getMessage()).isEqualTo(expectedMessage);
        assertThat(exception.getCause()).isEqualTo(cause);
    }

    @Test
    @DisplayName("测试带消息和原因的构造函数-null值")
    void testMessageAndCauseConstructor_NullValues() {
        // When
        LockException exception = new LockException(null, null);
        
        // Then
        assertThat(exception).isInstanceOf(Exception.class);
        assertThat(exception.getMessage()).isNull();
        assertThat(exception.getCause()).isNull();
    }

    @Test
    @DisplayName("测试序列化版本ID")
    void testSerialVersionUID() {
        // 通过反射验证serialVersionUID字段存在
        assertThatCode(() -> {
            java.lang.reflect.Field field = LockException.class.getDeclaredField("serialVersionUID");
            field.setAccessible(true);
            long serialVersionUID = (Long) field.get(null);
            assertThat(serialVersionUID).isEqualTo(7138794971443706697L);
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试异常抛出和捕获")
    void testThrowAndCatch() {
        // Given
        String expectedMessage = "测试锁异常";
        
        // When & Then
        assertThatThrownBy(() -> {
            throw new LockException(expectedMessage);
        })
        .isInstanceOf(LockException.class)
        .hasMessage(expectedMessage);
    }
} 