package com.uaepay.cmf.common.core.engine.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * ExpressionUtil类单元测试
 * 测试表达式工具方法
 */
class ExpressionUtilTest {

    private ExpressionUtil expressionUtil;

    @BeforeEach
    void setUp() {
        expressionUtil = new ExpressionUtil();
    }

    @Test
    @DisplayName("测试contains静态方法(数组包含数组)-正常包含")
    void testContains_ArrayContainsArray_Success() {
        // Given
        String[] more = {"a", "b", "c", "d"};
        String[] less = {"a", "c"};

        // When
        boolean result = ExpressionUtil.contains(more, less);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试contains静态方法(数组包含数组)-不包含")
    void testContains_ArrayContainsArray_NotContains() {
        // Given
        String[] more = {"a", "b", "c"};
        String[] less = {"a", "d"}; // d不在more数组中

        // When
        boolean result = ExpressionUtil.contains(more, less);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试contains静态方法(数组包含数组)-both null")
    void testContains_ArrayContainsArray_BothNull() {
        // When
        boolean result = ExpressionUtil.contains((String[])null, (String[])null);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试contains静态方法(数组包含数组)-less null")
    void testContains_ArrayContainsArray_LessNull() {
        // Given
        String[] more = {"a", "b", "c"};

        // When
        boolean result = ExpressionUtil.contains(more, (String[])null);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试contains静态方法(数组包含值)-正常包含")
    void testContains_ArrayContainsValue_Success() {
        // Given
        String[] array = {"apple", "banana", "cherry"};
        String value = "banana";

        // When
        boolean result = ExpressionUtil.contains(array, value);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试contains静态方法(数组包含值)-不包含")
    void testContains_ArrayContainsValue_NotContains() {
        // Given
        String[] array = {"apple", "banana", "cherry"};
        String value = "orange";

        // When
        boolean result = ExpressionUtil.contains(array, value);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试contains静态方法(数组包含值)-值为空")
    void testContains_ArrayContainsValue_BlankValue() {
        // Given
        String[] array = {"a", "b", "c"};
        String value = "";

        // When
        boolean result = ExpressionUtil.contains(array, value);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试contains静态方法(数组包含值)-数组为null")
    void testContains_ArrayContainsValue_ArrayNull() {
        // Given
        String value = "test";

        // When
        boolean result = ExpressionUtil.contains((String[])null, value);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试match静态方法(默认分隔符)-匹配成功")
    void testMatch_DefaultSeparator_Success() {
        // Given
        String[] array = {"a", "b", "c"};
        String arrayStr = "a,b,c";

        // When
        boolean result = ExpressionUtil.match(array, arrayStr);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试match静态方法(默认分隔符)-字符串为空")
    void testMatch_DefaultSeparator_BlankString() {
        // Given
        String[] array = {"a", "b", "c"};
        String arrayStr = "";

        // When
        boolean result = ExpressionUtil.match(array, arrayStr);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试match静态方法(自定义分隔符)-匹配成功")
    void testMatch_CustomSeparator_Success() {
        // Given
        String[] array = {"x", "y", "z"};
        String arrayStr = "x;y;z";
        String spiltTag = ";";

        // When
        boolean result = ExpressionUtil.match(array, arrayStr, spiltTag);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试in实例方法-值在数组中")
    void testIn_ValueInArray() {
        // Given
        String r = "test";
        String[] set = {"hello", "test", "world"};

        // When
        boolean result = expressionUtil.in(r, set);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试in实例方法-值不在数组中")
    void testIn_ValueNotInArray() {
        // Given
        String r = "missing";
        String[] set = {"hello", "test", "world"};

        // When
        boolean result = expressionUtil.in(r, set);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试in实例方法-值为null")
    void testIn_ValueNull() {
        // Given
        String r = null;
        String[] set = {"hello", "test", "world"};

        // When
        boolean result = expressionUtil.in(r, set);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试in实例方法-数组为null")
    void testIn_ArrayNull() {
        // Given
        String r = "test";
        String[] set = null;

        // When
        boolean result = expressionUtil.in(r, set);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试is实例方法")
    void testIs_CallsMatchMethod() {
        // Given
        String[] set = {"a", "b", "c"};
        String value = "a,b,c";

        // When
        boolean result = expressionUtil.is(set, value);

        // Then
        assertThat(result).isTrue();
    }
} 