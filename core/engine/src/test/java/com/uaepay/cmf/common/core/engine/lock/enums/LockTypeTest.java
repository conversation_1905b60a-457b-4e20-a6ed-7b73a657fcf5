package com.uaepay.cmf.common.core.engine.lock.enums;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static org.assertj.core.api.Assertions.*;

/**
 * LockType枚举类单元测试
 * 测试锁类型枚举的所有功能
 */
class LockTypeTest {

    @Test
    @DisplayName("测试枚举值数量")
    void testEnumValuesCount() {
        // When
        LockType[] values = LockType.values();
        
        // Then
        assertThat(values).hasSize(2);
    }

    @ParameterizedTest
    @EnumSource(LockType.class)
    @DisplayName("测试所有枚举值的getter方法")
    void testAllEnumValues(LockType lockType) {
        // Then
        assertThat(lockType.getCode()).isNotNull().isNotEmpty();
        assertThat(lockType.getMessage()).isNotNull().isNotEmpty();
    }

    @Test
    @DisplayName("测试SYNCHRON枚举值")
    void testSynchron() {
        // When
        LockType synchron = LockType.SYNCHRON;
        
        // Then
        assertThat(synchron.getCode()).isEqualTo("S");
        assertThat(synchron.getMessage()).isEqualTo("同步锁");
    }

    @Test
    @DisplayName("测试EXCLUSION枚举值")
    void testExclusion() {
        // When
        LockType exclusion = LockType.EXCLUSION;
        
        // Then
        assertThat(exclusion.getCode()).isEqualTo("E");
        assertThat(exclusion.getMessage()).isEqualTo("互斥锁");
    }

    @Test
    @DisplayName("测试getByCode方法-SYNCHRON")
    void testGetByCode_Synchron() {
        // When
        LockType result = LockType.getByCode("S");
        
        // Then
        assertThat(result).isEqualTo(LockType.SYNCHRON);
    }

    @Test
    @DisplayName("测试getByCode方法-EXCLUSION")
    void testGetByCode_Exclusion() {
        // When
        LockType result = LockType.getByCode("E");
        
        // Then
        assertThat(result).isEqualTo(LockType.EXCLUSION);
    }

    @Test
    @DisplayName("测试getByCode方法-不存在的代码")
    void testGetByCode_NotFound() {
        // When
        LockType result = LockType.getByCode("X");
        
        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试getByCode方法-null代码")
    void testGetByCode_NullCode() {
        // When
        LockType result = LockType.getByCode(null);
        
        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试getByCode方法-空字符串代码")
    void testGetByCode_EmptyCode() {
        // When
        LockType result = LockType.getByCode("");
        
        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试valueOf方法")
    void testValueOf() {
        // When & Then
        assertThat(LockType.valueOf("SYNCHRON")).isEqualTo(LockType.SYNCHRON);
        assertThat(LockType.valueOf("EXCLUSION")).isEqualTo(LockType.EXCLUSION);
        
        // 测试异常情况
        assertThatThrownBy(() -> LockType.valueOf("INVALID"))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // When & Then
        assertThat(LockType.SYNCHRON.toString()).isEqualTo("SYNCHRON");
        assertThat(LockType.EXCLUSION.toString()).isEqualTo("EXCLUSION");
    }
} 