package com.uaepay.cmf.common.core.engine.util.collection;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * CollectionUtil类单元测试
 * 测试集合工具方法
 */
class CollectionUtilTest {

    @Test
    @DisplayName("测试isEmpty方法-null集合")
    void testIsEmpty_NullCollection() {
        // When
        boolean result = CollectionUtil.isEmpty(null);
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试isEmpty方法-空集合")
    void testIsEmpty_EmptyCollection() {
        // Given
        Collection<String> collection = new ArrayList<>();
        
        // When
        boolean result = CollectionUtil.isEmpty(collection);
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试isEmpty方法-非空集合")
    void testIsEmpty_NonEmptyCollection() {
        // Given
        Collection<String> collection = Arrays.asList("item1", "item2");
        
        // When
        boolean result = CollectionUtil.isEmpty(collection);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试isNotEmpty方法-null集合")
    void testIsNotEmpty_NullCollection() {
        // When
        boolean result = CollectionUtil.isNotEmpty(null);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试isNotEmpty方法-空集合")
    void testIsNotEmpty_EmptyCollection() {
        // Given
        Collection<String> collection = new ArrayList<>();
        
        // When
        boolean result = CollectionUtil.isNotEmpty(collection);
        
        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试isNotEmpty方法-非空集合")
    void testIsNotEmpty_NonEmptyCollection() {
        // Given
        Collection<String> collection = Arrays.asList("item1", "item2");
        
        // When
        boolean result = CollectionUtil.isNotEmpty(collection);
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试join方法-null集合")
    void testJoin_NullCollection() {
        // When
        String result = CollectionUtil.join(null, ",");
        
        // Then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("测试join方法-空集合")
    void testJoin_EmptyCollection() {
        // Given
        Collection<String> collection = new ArrayList<>();
        
        // When
        String result = CollectionUtil.join(collection, ",");
        
        // Then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("测试join方法-正常集合")
    void testJoin_NormalCollection() {
        // Given
        Collection<String> collection = Arrays.asList("apple", "banana", "cherry");
        
        // When
        String result = CollectionUtil.join(collection, ",");
        
        // Then
        assertThat(result).isEqualTo("apple,banana,cherry");
    }

    @Test
    @DisplayName("测试join方法-单个元素")
    void testJoin_SingleElement() {
        // Given
        Collection<String> collection = Arrays.asList("only");
        
        // When
        String result = CollectionUtil.join(collection, "-");
        
        // Then
        assertThat(result).isEqualTo("only");
    }

    @Test
    @DisplayName("测试join方法-自定义分隔符")
    void testJoin_CustomDelimiter() {
        // Given
        Collection<String> collection = Arrays.asList("one", "two", "three");
        
        // When
        String result = CollectionUtil.join(collection, " | ");
        
        // Then
        assertThat(result).isEqualTo("one | two | three |");
    }

    @Test
    @DisplayName("测试sumList方法-空列表")
    void testSumList_EmptyList() {
        // Given
        List<Integer> list = new ArrayList<>();
        FieldConverter converter = obj -> new BigDecimal(obj.toString());
        
        // When
        BigDecimal result = CollectionUtil.sumList(list, converter);
        
        // Then
        assertThat(result).isEqualTo(new BigDecimal("0"));
    }

    @Test
    @DisplayName("测试sumList方法-正常求和")
    void testSumList_NormalSum() {
        // Given
        List<Integer> list = Arrays.asList(10, 20, 30);
        FieldConverter converter = obj -> new BigDecimal(obj.toString());
        
        // When
        BigDecimal result = CollectionUtil.sumList(list, converter);
        
        // Then
        assertThat(result).isEqualTo(new BigDecimal("60"));
    }

    @Test
    @DisplayName("测试sumList方法-小数求和")
    void testSumList_DecimalSum() {
        // Given
        List<String> list = Arrays.asList("10.5", "20.3", "30.2");
        FieldConverter converter = obj -> new BigDecimal(obj.toString());
        
        // When
        BigDecimal result = CollectionUtil.sumList(list, converter);
        
        // Then
        assertThat(result).isEqualTo(new BigDecimal("61.0"));
    }

    @Test
    @DisplayName("测试sumList方法-复杂对象转换")
    void testSumList_ComplexObjectConversion() {
        // Given
        List<TestObject> list = Arrays.asList(
            new TestObject(15.5),
            new TestObject(25.0),
            new TestObject(10.5)
        );
        FieldConverter converter = obj -> new BigDecimal(((TestObject)obj).getValue());
        
        // When
        BigDecimal result = CollectionUtil.sumList(list, converter);
        
        // Then
        assertThat(result).isEqualTo(new BigDecimal("51.0"));
    }

    // 测试用的简单对象
    private static class TestObject {
        private double value;
        
        public TestObject(double value) {
            this.value = value;
        }
        
        public double getValue() {
            return value;
        }
    }
} 