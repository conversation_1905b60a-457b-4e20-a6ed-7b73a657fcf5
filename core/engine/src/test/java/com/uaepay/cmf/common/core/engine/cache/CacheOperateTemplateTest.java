package com.uaepay.cmf.common.core.engine.cache;

import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.engine.BaseCoreEngineTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.util.*;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * CacheOperateTemplate缓存操作模板测试
 * 测试缓存操作模板的所有功能
 */
class CacheOperateTemplateTest extends BaseCoreEngineTest {

    @Mock
    private CacheClient localCache;
    
    @Mock
    private CacheClient memoryCache;
    
    @Mock
    private DataLoader<String> dataLoader;
    
    @SuppressWarnings("rawtypes")
    @Mock
    private DataLoader mapDataLoader;
    
    private CacheOperateTemplate cacheOperateTemplate;
    
    @BeforeEach
    void setUpCacheOperateTemplate() {
        cacheOperateTemplate = new CacheOperateTemplate();
        // 使用反射设置私有字段
        setField(cacheOperateTemplate, "localCache", localCache);
        setField(cacheOperateTemplate, "memoryCache", memoryCache);
    }

    @Test
    @DisplayName("测试load方法 - 统一缓存命中")
    void testLoadMemoryCacheHit() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        String key = "test_key";
        String expectedValue = "cached_value";
        
        when(memoryCache.get(cacheType, key)).thenReturn(expectedValue);
        
        // When
        Object result = cacheOperateTemplate.load(cacheType, key, dataLoader);
        
        // Then
        assertThat(result).isEqualTo(expectedValue);
        verify(memoryCache).get(cacheType, key);
        verify(localCache).remove(cacheType, key);
        verify(dataLoader, never()).load();
    }

    @Test
    @DisplayName("测试load方法 - 统一缓存未命中，本地缓存命中")
    void testLoadLocalCacheHit() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        String key = "test_key";

        when(memoryCache.get(cacheType, key)).thenReturn(null);
        when(dataLoader.load()).thenReturn("loaded_value");

        // When
        Object result = cacheOperateTemplate.load(cacheType, key, dataLoader);

        // Then
        assertThat(result).isEqualTo("loaded_value");
        verify(memoryCache).get(cacheType, key);
        verify(localCache).remove(cacheType, key);
        verify(dataLoader).load();
    }

    @Test
    @DisplayName("测试load方法 - 缓存未命中，从DataLoader加载")
    void testLoadFromDataLoader() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        String key = "test_key";
        String loadedValue = "loaded_value";
        
        when(memoryCache.get(cacheType, key)).thenReturn(null);
        when(dataLoader.load()).thenReturn(loadedValue);
        
        // When
        Object result = cacheOperateTemplate.load(cacheType, key, dataLoader);
        
        // Then
        assertThat(result).isEqualTo(loadedValue);
        verify(memoryCache).get(cacheType, key);
        verify(localCache).remove(cacheType, key);
        verify(dataLoader).load();
        verify(memoryCache).put(cacheType, key, loadedValue, 60 * 60 * 6);
    }

    @Test
    @DisplayName("测试load方法 - 统一缓存异常，本地缓存命中")
    void testLoadMemoryCacheExceptionLocalCacheHit() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        String key = "test_key";
        String expectedValue = "local_cached_value";
        
        when(memoryCache.get(cacheType, key)).thenThrow(new RuntimeException("Redis异常"));
        when(localCache.get(cacheType, key)).thenReturn(expectedValue);
        
        // When
        Object result = cacheOperateTemplate.load(cacheType, key, dataLoader);
        
        // Then
        assertThat(result).isEqualTo(expectedValue);
        verify(memoryCache).get(cacheType, key);
        verify(localCache).get(cacheType, key);
        verify(dataLoader, never()).load();
    }

    @Test
    @DisplayName("测试load方法 - 统一缓存异常，从DataLoader加载并设置本地缓存")
    void testLoadMemoryCacheExceptionLoadFromDataLoader() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        String key = "test_key";
        String loadedValue = "loaded_value";
        
        when(memoryCache.get(cacheType, key)).thenThrow(new RuntimeException("Redis异常"));
        when(localCache.get(cacheType, key)).thenReturn(null);
        when(dataLoader.load()).thenReturn(loadedValue);
        
        // When
        Object result = cacheOperateTemplate.load(cacheType, key, dataLoader);
        
        // Then
        assertThat(result).isEqualTo(loadedValue);
        verify(memoryCache).get(cacheType, key);
        verify(localCache).get(cacheType, key);
        verify(dataLoader).load();
        verify(localCache).put(cacheType, key, loadedValue, 60 * 60 * 6);
        verify(memoryCache, never()).put(any(), any(), any(), anyInt());
    }

    @Test
    @DisplayName("测试load方法 - DataLoader返回null")
    void testLoadDataLoaderReturnsNull() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        String key = "test_key";
        
        when(memoryCache.get(cacheType, key)).thenReturn(null);
        when(dataLoader.load()).thenReturn(null);
        
        // When
        Object result = cacheOperateTemplate.load(cacheType, key, dataLoader);
        
        // Then
        assertThat(result).isNull();
        verify(memoryCache).get(cacheType, key);
        verify(localCache).remove(cacheType, key);
        verify(dataLoader).load();
        verify(memoryCache, never()).put(any(), any(), any(), anyInt());
        verify(localCache, never()).put(any(), any(), any(), anyInt());
    }

    @Test
    @DisplayName("测试load方法 - 空集合处理")
    void testLoadEmptyCollection() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        String key = "test_key";
        List<String> emptyList = new ArrayList<>();
        
        when(memoryCache.get(cacheType, key)).thenReturn(emptyList);
        when(dataLoader.load()).thenReturn("loaded_value");
        
        // When
        Object result = cacheOperateTemplate.load(cacheType, key, dataLoader);
        
        // Then
        assertThat(result).isEqualTo("loaded_value");
        verify(dataLoader).load();
    }

    @Test
    @DisplayName("测试load方法 - 空Map处理")
    void testLoadEmptyMap() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        String key = "test_key";
        Map<String, String> emptyMap = new HashMap<>();
        
        when(memoryCache.get(cacheType, key)).thenReturn(emptyMap);
        when(dataLoader.load()).thenReturn("loaded_value");
        
        // When
        Object result = cacheOperateTemplate.load(cacheType, key, dataLoader);
        
        // Then
        assertThat(result).isEqualTo("loaded_value");
        verify(dataLoader).load();
    }

    @Test
    @DisplayName("测试refresh方法")
    @SuppressWarnings("unchecked")
    void testRefresh() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        Map<String, Object> testData = new HashMap<>();
        testData.put("key1", "value1");
        testData.put("key2", "value2");

        when(mapDataLoader.load()).thenReturn(testData);

        // When
        cacheOperateTemplate.refresh(cacheType, mapDataLoader);

        // Then
        verify(mapDataLoader).load();
        verify(memoryCache).put(cacheType, "key1", "value1", 3600);
        verify(memoryCache).put(cacheType, "key2", "value2", 3600);
    }

    @Test
    @DisplayName("测试refresh方法 - 统一缓存异常")
    @SuppressWarnings("unchecked")
    void testRefreshMemoryCacheException() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        Map<String, Object> testData = new HashMap<>();
        testData.put("key1", "value1");

        when(mapDataLoader.load()).thenReturn(testData);
        doThrow(new RuntimeException("Redis异常")).when(memoryCache).put(any(), any(), any(), anyInt());

        // When & Then - 不应该抛出异常
        assertThatCode(() -> cacheOperateTemplate.refresh(cacheType, mapDataLoader))
            .doesNotThrowAnyException();

        verify(mapDataLoader).load();
        verify(memoryCache).put(cacheType, "key1", "value1", 3600);
    }

    @Test
    @DisplayName("测试deleteByKeys方法")
    void testDeleteByKeys() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        Set<String> keys = new HashSet<>(Arrays.asList("key1", "key2", "key3"));
        
        // When
        cacheOperateTemplate.deleteByKeys(cacheType, keys);
        
        // Then
        verify(memoryCache).removeByKeys(cacheType, keys);
        verify(localCache).removeByKeys(cacheType, keys);
    }

    @Test
    @DisplayName("测试deleteByKeys方法 - 统一缓存异常")
    void testDeleteByKeysMemoryCacheException() {
        // Given
        CacheType cacheType = CacheType.CMF_SYS_CONFIGURATION;
        Set<String> keys = new HashSet<>(Arrays.asList("key1", "key2"));
        
        doThrow(new RuntimeException("Redis异常")).when(memoryCache).removeByKeys(cacheType, keys);
        
        // When & Then - 不应该抛出异常
        assertThatCode(() -> cacheOperateTemplate.deleteByKeys(cacheType, keys))
            .doesNotThrowAnyException();
        
        verify(memoryCache).removeByKeys(cacheType, keys);
        verify(localCache).removeByKeys(cacheType, keys);
    }

    /**
     * 使用反射设置私有字段
     */
    private void setField(Object target, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(target, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set field: " + fieldName, e);
        }
    }
}
