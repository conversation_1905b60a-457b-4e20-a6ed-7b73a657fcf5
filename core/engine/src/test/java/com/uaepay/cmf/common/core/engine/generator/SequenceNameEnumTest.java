package com.uaepay.cmf.common.core.engine.generator;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static org.assertj.core.api.Assertions.*;

/**
 * SequenceNameEnum枚举类单元测试
 * 测试序列名枚举的所有功能
 */
class SequenceNameEnumTest {

    @Test
    @DisplayName("测试枚举值数量")
    void testEnumValuesCount() {
        // When
        SequenceNameEnum[] values = SequenceNameEnum.values();
        
        // Then
        assertThat(values).hasSize(10);
    }

    @ParameterizedTest
    @EnumSource(SequenceNameEnum.class)
    @DisplayName("测试所有枚举值的getter方法")
    void testAllEnumValues(SequenceNameEnum sequenceNameEnum) {
        // Then
        assertThat(sequenceNameEnum.getCode()).isNotNull().isNotEmpty();
        assertThat(sequenceNameEnum.getDescription()).isNotNull().isNotEmpty();
    }

    @Test
    @DisplayName("测试CARD_TOKEN枚举值")
    void testCardToken() {
        // When
        SequenceNameEnum cardToken = SequenceNameEnum.CARD_TOKEN;
        
        // Then
        assertThat(cardToken.getCode()).isEqualTo("SEQ_CARD_TOKEN");
        assertThat(cardToken.getDescription()).isEqualTo("卡token");
    }

    @Test
    @DisplayName("测试CMF_ORDER枚举值")
    void testCmfOrder() {
        // When
        SequenceNameEnum cmfOrder = SequenceNameEnum.CMF_ORDER;
        
        // Then
        assertThat(cmfOrder.getCode()).isEqualTo("SEQ_CMF_ORDER");
        assertThat(cmfOrder.getDescription()).isEqualTo("cmf流水号");
    }

    @Test
    @DisplayName("测试PAYMENT_NOTIFY_LOG枚举值")
    void testPaymentNotifyLog() {
        // When
        SequenceNameEnum paymentNotifyLog = SequenceNameEnum.PAYMENT_NOTIFY_LOG;
        
        // Then
        assertThat(paymentNotifyLog.getCode()).isEqualTo("SEQ_PAYMENT_NOTIFY_LOG");
        assertThat(paymentNotifyLog.getDescription()).isEqualTo("pe通知");
    }

    @Test
    @DisplayName("测试INST_ORDER枚举值")
    void testInstOrder() {
        // When
        SequenceNameEnum instOrder = SequenceNameEnum.INST_ORDER;
        
        // Then
        assertThat(instOrder.getCode()).isEqualTo("SEQ_INST_ORDER");
        assertThat(instOrder.getDescription()).isEqualTo("机构订单");
    }

    @Test
    @DisplayName("测试INST_RESULT枚举值")
    void testInstResult() {
        // When
        SequenceNameEnum instResult = SequenceNameEnum.INST_RESULT;
        
        // Then
        assertThat(instResult.getCode()).isEqualTo("SEQ_INST_ORDER_RESULT");
        assertThat(instResult.getDescription()).isEqualTo("机构结果");
    }

    @Test
    @DisplayName("测试BATCH_ORDER枚举值")
    void testBatchOrder() {
        // When
        SequenceNameEnum batchOrder = SequenceNameEnum.BATCH_ORDER;
        
        // Then
        assertThat(batchOrder.getCode()).isEqualTo("SEQ_INST_BATCH_ORDER");
        assertThat(batchOrder.getDescription()).isEqualTo("批量订单");
    }

    @Test
    @DisplayName("测试BATCH_RESULT枚举值")
    void testBatchResult() {
        // When
        SequenceNameEnum batchResult = SequenceNameEnum.BATCH_RESULT;
        
        // Then
        assertThat(batchResult.getCode()).isEqualTo("SEQ_INST_BATCH_RESULT");
        assertThat(batchResult.getDescription()).isEqualTo("批量结果");
    }

    @Test
    @DisplayName("测试CONTROL_ORDER枚举值")
    void testControlOrder() {
        // When
        SequenceNameEnum controlOrder = SequenceNameEnum.CONTROL_ORDER;
        
        // Then
        assertThat(controlOrder.getCode()).isEqualTo("SEQ_CONTROL_ORDER_ID");
        assertThat(controlOrder.getDescription()).isEqualTo("控制订单");
    }

    @Test
    @DisplayName("测试CONTROL_RESULT枚举值")
    void testControlResult() {
        // When
        SequenceNameEnum controlResult = SequenceNameEnum.CONTROL_RESULT;
        
        // Then
        assertThat(controlResult.getCode()).isEqualTo("SEQ_CONTROL_RESULT_ID");
        assertThat(controlResult.getDescription()).isEqualTo("控制结果");
    }

    @Test
    @DisplayName("测试NOTIFY_3DS_RESULT枚举值")
    void testNotify3dsResult() {
        // When
        SequenceNameEnum notify3dsResult = SequenceNameEnum.NOTIFY_3DS_RESULT;
        
        // Then
        assertThat(notify3dsResult.getCode()).isEqualTo("SEQ_NOTIFY_3DS_RESULT_ID");
        assertThat(notify3dsResult.getDescription()).isEqualTo("3ds结果");
    }

    @Test
    @DisplayName("测试valueOf方法")
    void testValueOf() {
        // When & Then
        assertThat(SequenceNameEnum.valueOf("CARD_TOKEN")).isEqualTo(SequenceNameEnum.CARD_TOKEN);
        assertThat(SequenceNameEnum.valueOf("CMF_ORDER")).isEqualTo(SequenceNameEnum.CMF_ORDER);
        
        // 测试异常情况
        assertThatThrownBy(() -> SequenceNameEnum.valueOf("INVALID"))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // When & Then
        assertThat(SequenceNameEnum.CARD_TOKEN.toString()).isEqualTo("CARD_TOKEN");
        assertThat(SequenceNameEnum.CMF_ORDER.toString()).isEqualTo("CMF_ORDER");
    }
} 