package com.uaepay.cmf.common.core.engine.util;

import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.text.ParseException;
import java.util.Date;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * CommonUtil类单元测试
 * 测试通用工具方法
 */
@ExtendWith(MockitoExtension.class)
class CommonUtilTest {

    @Test
    @DisplayName("测试key方法-单个参数")
    void testKey_SingleFactor() {
        // When
        String result = CommonUtil.key("factor1");
        
        // Then
        assertThat(result).isEqualTo("factor1");
    }

    @Test
    @DisplayName("测试key方法-多个参数")
    void testKey_MultipleFactors() {
        // When
        String result = CommonUtil.key("factor1", "factor2", "factor3");
        
        // Then
        assertThat(result).isEqualTo("factor1-factor2-factor3");
    }

    @Test
    @DisplayName("测试key方法-空参数")
    void testKey_EmptyFactors() {
        // When & Then - 空参数会导致StringIndexOutOfBoundsException
        assertThatThrownBy(() -> CommonUtil.key())
                .isInstanceOf(StringIndexOutOfBoundsException.class);
    }

    @Test
    @DisplayName("测试key方法-包含空字符串")
    void testKey_WithEmptyString() {
        // When
        String result = CommonUtil.key("factor1", "", "factor3");
        
        // Then
        assertThat(result).isEqualTo("factor1--factor3");
    }

    @Test
    @DisplayName("测试validateDateBySeqNo方法(双参数)-正常日期序列号")
    void testValidateDateBySeqNo_TwoParams_ValidSeqNo() throws ParseException {
        // Given
        String paymentSeqNo = "20230615123456789";
        long day = 30L;
        
        // When & Then
        try (MockedStatic<DateUtil> dateUtilMock = Mockito.mockStatic(DateUtil.class)) {
            Date mockDate = new Date();
            dateUtilMock.when(() -> DateUtil.parseDateNoTime("20230615"))
                    .thenReturn(mockDate);
            dateUtilMock.when(() -> DateUtil.dateLessThanNowAddMin(eq(mockDate), eq(60L * 24L * 30L)))
                    .thenReturn(false);
            
            boolean result = CommonUtil.validateDateBySeqNo(paymentSeqNo, day);
            
            assertThat(result).isTrue();
        }
    }

    @Test
    @DisplayName("测试validateDateBySeqNo方法(双参数)-序列号为null")
    void testValidateDateBySeqNo_TwoParams_NullSeqNo() {
        // When
        boolean result = CommonUtil.validateDateBySeqNo(null, 30L);
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试validateDateBySeqNo方法(双参数)-序列号长度不足")
    void testValidateDateBySeqNo_TwoParams_ShortSeqNo() {
        // When
        boolean result = CommonUtil.validateDateBySeqNo("1234567", 30L);
        
        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试validateDateBySeqNo方法(双参数)-日期解析异常")
    void testValidateDateBySeqNo_TwoParams_ParseException() {
        // Given
        String paymentSeqNo = "abcdefgh123456789";
        long day = 30L;
        
        // When & Then
        try (MockedStatic<DateUtil> dateUtilMock = Mockito.mockStatic(DateUtil.class)) {
            dateUtilMock.when(() -> DateUtil.parseDateNoTime("abcdefgh"))
                    .thenThrow(new ParseException("Invalid date", 0));
            
            boolean result = CommonUtil.validateDateBySeqNo(paymentSeqNo, day);
            
            assertThat(result).isTrue();
        }
    }

    @Test
    @DisplayName("测试validateDateBySeqNo方法(双参数)-超过指定天数")
    void testValidateDateBySeqNo_TwoParams_ExceedDays() throws ParseException {
        // Given
        String paymentSeqNo = "20230615123456789";
        long day = 30L;
        
        // When & Then
        try (MockedStatic<DateUtil> dateUtilMock = Mockito.mockStatic(DateUtil.class)) {
            Date mockDate = new Date();
            dateUtilMock.when(() -> DateUtil.parseDateNoTime("20230615"))
                    .thenReturn(mockDate);
            dateUtilMock.when(() -> DateUtil.dateLessThanNowAddMin(eq(mockDate), eq(60L * 24L * 30L)))
                    .thenReturn(true);
            
            boolean result = CommonUtil.validateDateBySeqNo(paymentSeqNo, day);
            
            assertThat(result).isFalse();
        }
    }

    @Test
    @DisplayName("测试validateDateBySeqNo方法(单参数)-使用默认90天")
    void testValidateDateBySeqNo_SingleParam() throws ParseException {
        // Given
        String paymentSeqNo = "20230615123456789";
        
        // When & Then
        try (MockedStatic<DateUtil> dateUtilMock = Mockito.mockStatic(DateUtil.class)) {
            Date mockDate = new Date();
            dateUtilMock.when(() -> DateUtil.parseDateNoTime("20230615"))
                    .thenReturn(mockDate);
            dateUtilMock.when(() -> DateUtil.dateLessThanNowAddMin(eq(mockDate), eq(60L * 24L * 90L)))
                    .thenReturn(false);
            
            boolean result = CommonUtil.validateDateBySeqNo(paymentSeqNo);
            
            assertThat(result).isTrue();
        }
    }

    @Test
    @DisplayName("测试i18nCodeBuild方法(单参数)-正常文本")
    void testI18nCodeBuild_SingleParam_NormalText() {
        // When
        String result = CommonUtil.i18nCodeBuild("test.message");
        
        // Then
        assertThat(result).isEqualTo("${test.message}");
    }

    @Test
    @DisplayName("测试i18nCodeBuild方法(单参数)-空文本")
    void testI18nCodeBuild_SingleParam_BlankText() {
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank(""))
                    .thenReturn(true);
            
            String result = CommonUtil.i18nCodeBuild("");
            
            assertThat(result).isNull();
        }
    }

    @Test
    @DisplayName("测试i18nCodeBuild方法(单参数)-null文本")
    void testI18nCodeBuild_SingleParam_NullText() {
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank(null))
                    .thenReturn(true);
            
            String result = CommonUtil.i18nCodeBuild(null);
            
            assertThat(result).isNull();
        }
    }

    @Test
    @DisplayName("测试i18nCodeBuild方法(三参数)-正常参数")
    void testI18nCodeBuild_ThreeParams_NormalParams() {
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank("test.message"))
                    .thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.isBlank("["))
                    .thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.isBlank("]"))
                    .thenReturn(false);
            
            String result = CommonUtil.i18nCodeBuild("test.message", "[", "]");
            
            assertThat(result).isEqualTo("[test.message]");
        }
    }

    @Test
    @DisplayName("测试i18nCodeBuild方法(三参数)-左右括号为空")
    void testI18nCodeBuild_ThreeParams_EmptyBrackets() {
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank("test.message"))
                    .thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.isBlank(""))
                    .thenReturn(true);
            
            String result = CommonUtil.i18nCodeBuild("test.message", "", "");
            
            assertThat(result).isEqualTo("${test.message}");
        }
    }

    @Test
    @DisplayName("测试i18nCodeBuild方法(三参数)-左括号为null")
    void testI18nCodeBuild_ThreeParams_NullLeftBracket() {
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank("test.message"))
                    .thenReturn(false);
            stringUtilsMock.when(() -> StringUtils.isBlank(null))
                    .thenReturn(true);
            stringUtilsMock.when(() -> StringUtils.isBlank("]"))
                    .thenReturn(false);
            
            String result = CommonUtil.i18nCodeBuild("test.message", null, "]");
            
            assertThat(result).isEqualTo("${test.message}");
        }
    }

    @Test
    @DisplayName("测试buildCacheKey方法-正常缓存类型和键")
    void testBuildCacheKey_NormalParams() {
        // Given
        CacheType cacheType = CacheType.CSC;
        String key = "test.key";
        
        // When
        String result = CommonUtil.buildCacheKey(cacheType, key);
        
        // Then
        assertThat(result).isEqualTo(cacheType.getPrefix() + key);
    }

    @ParameterizedTest
    @ValueSource(strings = {"20230615123456789", "20220101987654321", "20210331555666777"})
    @DisplayName("测试validateDateBySeqNo方法-多个有效序列号")
    void testValidateDateBySeqNo_MultipleValidSeqNos(String paymentSeqNo) throws ParseException {
        // When & Then
        try (MockedStatic<DateUtil> dateUtilMock = Mockito.mockStatic(DateUtil.class)) {
            Date mockDate = new Date();
            dateUtilMock.when(() -> DateUtil.parseDateNoTime(anyString()))
                    .thenReturn(mockDate);
            dateUtilMock.when(() -> DateUtil.dateLessThanNowAddMin(eq(mockDate), anyLong()))
                    .thenReturn(false);
            
            boolean result = CommonUtil.validateDateBySeqNo(paymentSeqNo, 30L);
            
            assertThat(result).isTrue();
        }
    }

    @ParameterizedTest
    @CsvSource({
        "hello, ${hello}",
        "error.code, ${error.code}",
        "message.success, ${message.success}"
    })
    @DisplayName("测试i18nCodeBuild方法-多个输入")
    void testI18nCodeBuild_MultipleInputs(String input, String expected) {
        // When & Then
        try (MockedStatic<StringUtils> stringUtilsMock = Mockito.mockStatic(StringUtils.class)) {
            stringUtilsMock.when(() -> StringUtils.isBlank(input))
                    .thenReturn(false);
            
            String result = CommonUtil.i18nCodeBuild(input);
            
            assertThat(result).isEqualTo(expected);
        }
    }
} 