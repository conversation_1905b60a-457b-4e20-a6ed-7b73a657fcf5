package com.uaepay.cmf.common.core.engine.schedule.task;

import com.uaepay.cmf.common.core.engine.schedule.DaemonContext;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.*;

import static org.assertj.core.api.Assertions.*;

/**
 * AbstractDaemonTask简单测试
 * 不使用Mockito，通过简单测试验证基本功能
 */
class AbstractDaemonTaskTest {

    private TestDaemonTask daemonTask;

    @BeforeEach
    void setUp() {
        daemonTask = new TestDaemonTask();
    }

    @Test
    @DisplayName("测试execute方法-DaemonContext参数-无任务")
    void testExecute_DaemonContext_NoTasks() {
        // Given
        DaemonContext context = new DaemonContext();
        context.setTriggerId("test-trigger");
        daemonTask.setTasksToReturn(Collections.emptyList());

        // When
        DaemonTaskResult result = daemonTask.execute(context);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(daemonTask.getContext()).isEqualTo(context);
    }

    @Test
    @DisplayName("测试execute方法-DaemonContext参数-有任务")
    void testExecute_DaemonContext_WithTasks() {
        // Given
        DaemonContext context = new DaemonContext();
        context.setTriggerId("test-trigger");

        List<String> tasks = Arrays.asList("task1", "task2", "task3");
        daemonTask.setTasksToReturn(tasks);

        // When
        DaemonTaskResult result = daemonTask.execute(context);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(daemonTask.getExecutedTasks()).containsExactlyInAnyOrder("task1", "task2", "task3");
        assertThat(daemonTask.getMonitoredTotalCount()).isEqualTo(3);
    }

    @Test
    @DisplayName("测试execute方法-批量处理")
    void testExecute_BatchProcessing() {
        // Given
        DaemonContext context = new DaemonContext();
        context.setTriggerId("test-trigger");

        daemonTask.setBatchSize(2);
        List<String> tasks = Arrays.asList("task1", "task2", "task3", "task4", "task5");
        daemonTask.setTasksToReturn(tasks);

        // When
        DaemonTaskResult result = daemonTask.execute(context);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(daemonTask.getExecutedTasks()).containsExactlyInAnyOrder("task1", "task2", "task3", "task4", "task5");
        assertThat(daemonTask.getMonitoredTotalCount()).isEqualTo(5);
    }

    @Test
    @DisplayName("测试execute方法-任务执行失败")
    void testExecute_TaskExecutionFailure() {
        // Given
        DaemonContext context = new DaemonContext();
        context.setTriggerId("test-trigger");

        List<String> tasks = Arrays.asList("task1", "fail-task", "task3");
        daemonTask.setTasksToReturn(tasks);
        daemonTask.setFailingTasks(Collections.singleton("fail-task"));

        // When
        DaemonTaskResult result = daemonTask.execute(context);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(daemonTask.getExecutedTasks()).containsExactlyInAnyOrder("task1", "fail-task", "task3");
        assertThat(daemonTask.getMonitoredTasks()).contains("fail-task");
        assertThat(daemonTask.getMonitoredTotalCount()).isEqualTo(3);
    }

    @Test
    @DisplayName("测试TaskResult内部类")
    void testTaskResult() {
        // Given
        TestDaemonTask.TestTaskResult successResult = daemonTask.new TestTaskResult(true, "Success message");
        TestDaemonTask.TestTaskResult failureResult = daemonTask.new TestTaskResult(false, "Failure message");

        // Then
        assertThat(successResult.isSuccess()).isTrue();
        assertThat(successResult.getResultMessage()).isEqualTo("Success message");

        assertThat(failureResult.isSuccess()).isFalse();
        assertThat(failureResult.getResultMessage()).isEqualTo("Failure message");
    }

    @Test
    @DisplayName("测试setter方法")
    void testSetters() {
        // Given
        DaemonContext newContext = new DaemonContext();

        // When
        daemonTask.setBatchSize(50);
        daemonTask.setContext(newContext);

        // Then
        assertThat(daemonTask.getContext()).isEqualTo(newContext);
    }

    @Test
    @DisplayName("测试空任务列表处理")
    void testExecute_NullTaskList() {
        // Given
        DaemonContext context = new DaemonContext();
        context.setTriggerId("test-trigger");
        daemonTask.setTasksToReturn(null);

        // When
        DaemonTaskResult result = daemonTask.execute(context);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(daemonTask.getMonitoredTotalCount()).isEqualTo(0);
    }

    @Test
    @DisplayName("测试批量大小小于任务数量")
    void testExecute_BatchSizeSmallerThanTasks() {
        // Given
        DaemonContext context = new DaemonContext();
        context.setTriggerId("test-trigger");

        daemonTask.setBatchSize(10);
        List<String> tasks = Arrays.asList("task1", "task2"); // 少于批量大小
        daemonTask.setTasksToReturn(tasks);

        // When
        DaemonTaskResult result = daemonTask.execute(context);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(daemonTask.getExecutedTasks()).containsExactlyInAnyOrder("task1", "task2");
        assertThat(daemonTask.getMonitoredTotalCount()).isEqualTo(2);
    }

    @Test
    @DisplayName("测试FutureTaskCallable内部类-成功执行")
    void testFutureTaskCallable_Success() throws Exception {
        // Given
        TestDaemonTask.TestFutureTaskCallable callable = daemonTask.new TestFutureTaskCallable("test-task");

        // When
        AbstractDaemonTask.TaskResult result = callable.call();

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getResultMessage()).isEqualTo("任务执行成功");
        assertThat(daemonTask.getExecutedTasks()).contains("test-task");
    }

    @Test
    @DisplayName("测试FutureTaskCallable内部类-执行失败")
    void testFutureTaskCallable_Failure() throws Exception {
        // Given
        daemonTask.setFailingTasks(Collections.singleton("fail-task"));
        TestDaemonTask.TestFutureTaskCallable callable = daemonTask.new TestFutureTaskCallable("fail-task");

        // When
        AbstractDaemonTask.TaskResult result = callable.call();

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getResultMessage()).isEqualTo("任务执行成功");
        assertThat(daemonTask.getExecutedTasks()).contains("fail-task");
    }

    @Test
    @DisplayName("测试FutureTaskCallable内部类-抛出异常")
    void testFutureTaskCallable_Exception() throws Exception {
        // Given
        daemonTask.setExceptionTasks(Collections.singleton("exception-task"));
        TestDaemonTask.TestFutureTaskCallable callable = daemonTask.new TestFutureTaskCallable("exception-task");

        // When
        AbstractDaemonTask.TaskResult result = callable.call();

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getResultMessage()).isEqualTo("Test exception");
        assertThat(daemonTask.getExecutedTasks()).contains("exception-task");
    }

    @Test
    @DisplayName("测试FutureTaskCallable构造函数")
    void testFutureTaskCallable_Constructor() {
        // Given & When
        TestDaemonTask.TestFutureTaskCallable callable = daemonTask.new TestFutureTaskCallable("domain-value");

        // Then
        assertThat(callable).isNotNull();
        // 验证domain字段通过call方法的行为
        assertThatCode(() -> callable.call()).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试FutureTaskCallable-不同domain值")
    void testFutureTaskCallable_DifferentDomains() throws Exception {
        // Given
        TestDaemonTask.TestFutureTaskCallable callable1 = daemonTask.new TestFutureTaskCallable("domain1");
        TestDaemonTask.TestFutureTaskCallable callable2 = daemonTask.new TestFutureTaskCallable("domain2");
        TestDaemonTask.TestFutureTaskCallable callable3 = daemonTask.new TestFutureTaskCallable(null);

        // When
        AbstractDaemonTask.TaskResult result1 = callable1.call();
        AbstractDaemonTask.TaskResult result2 = callable2.call();
        AbstractDaemonTask.TaskResult result3 = callable3.call();

        // Then
        assertThat(result1.isSuccess()).isTrue();
        assertThat(result2.isSuccess()).isTrue();
        assertThat(result3.isSuccess()).isTrue();
        assertThat(daemonTask.getExecutedTasks()).containsExactlyInAnyOrder("domain1", "domain2", null);
    }



    @Test
    @DisplayName("测试原始execute方法-使用线程池")
    void testOriginalExecute_WithThreadPool() {
        // Given
        DaemonContext context = new DaemonContext();
        context.setTriggerId("test-trigger");

        List<String> tasks = Arrays.asList("task1", "task2");
        daemonTask.setTasksToReturn(tasks);
        daemonTask.setUseOriginalExecute(true);

        // 创建一个简单的ThreadPoolTaskExecutor
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(1);
        executor.setMaxPoolSize(1);
        executor.setQueueCapacity(10);
        executor.initialize();
        daemonTask.setDaemonTaskExecutor(executor);

        // When
        DaemonTaskResult result = daemonTask.execute(context);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();

        // 等待线程池任务完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        assertThat(daemonTask.getExecutedTasks()).containsExactlyInAnyOrder("task1", "task2");
        assertThat(daemonTask.getMonitoredTotalCount()).isEqualTo(2);

        // 清理
        executor.shutdown();
    }

    /**
     * 测试用的具体实现类
     */
    private static class TestDaemonTask extends AbstractDaemonTask<String> {
        private List<String> tasksToReturn = new ArrayList<>();
        private Set<String> failingTasks = new HashSet<>();
        private Set<String> exceptionTasks = new HashSet<>();
        private List<String> executedTasks = new ArrayList<>();
        private List<String> monitoredTasks = new ArrayList<>();
        private int monitoredTotalCount = 0;
        private int loadTaskCallCount = 0;
        private boolean useOriginalExecute = false;

        @Override
        public DaemonTaskResult execute(DaemonContext context) {
            if (useOriginalExecute) {
                // 使用父类的原始execute方法
                return super.execute(context);
            } else {
                // 使用简化的execute方法进行基本测试
                String triggerId = context.getTriggerId();
                this.setContext(context);
                logger.debug("{}任务开始执行{}", getTaskType().getMessage(), triggerId);

                int totalCount = 0;
                DaemonTaskResult result = new DaemonTaskResult(true, getTaskType());

                boolean hasMore = true;
                while (hasMore) {
                    List<String> tasks = loadTask(batchSize);
                    if (tasks == null || tasks.isEmpty()) {
                        if (totalCount == 0) {
                            logger.debug("已无可执行任务.");
                        }
                        break; // 执行结束
                    } else if (tasks.size() < batchSize) {
                        // 可以避免异常数据重复执行
                        hasMore = false;
                    }
                    totalCount = totalCount + tasks.size();

                    // 直接执行任务，不使用线程池
                    for (String domain : tasks) {
                        String logPrefix = "任务[" + triggerId + "],订单ID[" + domain + "]";
                        try {
                            boolean taskSuccess = executeTask(domain);
                            if (taskSuccess) {
                                logger.info(logPrefix + "执行成功");
                            } else {
                                logger.error(logPrefix + "执行失败");
                                sendToMonitor(domain);
                            }
                        } catch (Exception e) {
                            logger.error(logPrefix + "执行时出现未知错误", e);
                            sendToMonitor(domain);
                        }
                    }
                }

                // 监控total count。
                if (totalCount > 0) {
                    monitorTotalCount(totalCount);
                }

                return result;
            }
        }

        @Override
        protected List<String> loadTask(int batchSize) {
            loadTaskCallCount++;
            if (tasksToReturn == null || tasksToReturn.isEmpty()) {
                return Collections.emptyList();
            }

            int startIndex = (loadTaskCallCount - 1) * batchSize;
            if (startIndex >= tasksToReturn.size()) {
                return Collections.emptyList();
            }

            int endIndex = Math.min(startIndex + batchSize, tasksToReturn.size());
            return new ArrayList<>(tasksToReturn.subList(startIndex, endIndex));
        }

        @Override
        protected boolean executeTask(String domain) {
            executedTasks.add(domain);
            if (exceptionTasks.contains(domain)) {
                throw new RuntimeException("Test exception");
            }
            return !failingTasks.contains(domain);
        }

        @Override
        protected void sendToMonitor(String domain) {
            monitoredTasks.add(domain);
        }

        @Override
        protected void monitorTotalCount(int totalCount) {
            this.monitoredTotalCount = totalCount;
        }

        @Override
        public DaemonTaskType getTaskType() {
            return new DaemonTaskType("TEST", "Test Task");
        }

        public void setUseOriginalExecute(boolean useOriginalExecute) {
            this.useOriginalExecute = useOriginalExecute;
        }

        // Getters and setters for testing
        public void setTasksToReturn(List<String> tasksToReturn) {
            this.tasksToReturn = tasksToReturn;
            this.loadTaskCallCount = 0; // Reset call count
        }

        public void setFailingTasks(Set<String> failingTasks) {
            this.failingTasks = failingTasks;
        }

        public void setExceptionTasks(Set<String> exceptionTasks) {
            this.exceptionTasks = exceptionTasks;
        }

        public List<String> getExecutedTasks() {
            return executedTasks;
        }

        public List<String> getMonitoredTasks() {
            return monitoredTasks;
        }

        public int getMonitoredTotalCount() {
            return monitoredTotalCount;
        }

        // Expose inner classes for testing
        public class TestTaskResult extends TaskResult {
            public TestTaskResult(boolean success, String message) {
                super(success, message);
            }
        }

        public class TestFutureTaskCallable extends FutureTaskCallable {
            public TestFutureTaskCallable(String domain) {
                super(domain);
            }
        }
    }
}
