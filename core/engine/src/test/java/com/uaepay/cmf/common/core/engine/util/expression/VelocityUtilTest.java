package com.uaepay.cmf.common.core.engine.util.expression;

import com.uaepay.cmf.common.core.domain.exception.ParseException;
import com.uaepay.common.util.money.Money;
import org.apache.velocity.app.VelocityEngine;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * VelocityUtil工具类单元测试
 * 测试Velocity模板引擎工具类的所有功能
 */
class VelocityUtilTest {

    @Test
    @DisplayName("测试loadEngine方法-单例模式")
    void testLoadEngine_Singleton() {
        // When
        VelocityEngine engine1 = VelocityUtil.loadEngine();
        VelocityEngine engine2 = VelocityUtil.loadEngine();

        // Then
        assertThat(engine1).isNotNull();
        assertThat(engine2).isNotNull();
        assertThat(engine1).isSameAs(engine2); // 验证单例模式
    }

    @Test
    @DisplayName("测试getString方法-正常模板")
    void testGetString_NormalTemplate() throws ParseException {
        // Given
        String template = "Hello ${name}!";
        Map<String, Object> params = new HashMap<>();
        params.put("name", "World");

        // When
        String result = VelocityUtil.getString(template, params);

        // Then
        assertThat(result).isEqualTo("Hello World!");
    }

    @Test
    @DisplayName("测试getString方法-空模板")
    void testGetString_EmptyTemplate() throws ParseException {
        // Given
        String template = "";
        Map<String, Object> params = new HashMap<>();

        // When
        String result = VelocityUtil.getString(template, params);

        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试getString方法-null模板")
    void testGetString_NullTemplate() throws ParseException {
        // Given
        String template = null;
        Map<String, Object> params = new HashMap<>();

        // When
        String result = VelocityUtil.getString(template, params);

        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试getString方法-空参数")
    void testGetString_EmptyParams() throws ParseException {
        // Given
        String template = "Hello ${name}!";
        Map<String, Object> params = new HashMap<>();

        // When
        String result = VelocityUtil.getString(template, params);

        // Then
        assertThat(result).isEqualTo("Hello ${name}!");
    }

    @Test
    @DisplayName("测试getString方法-null参数")
    void testGetString_NullParams() throws ParseException {
        // Given
        String template = "Hello World!";
        Map<String, Object> params = null;

        // When
        String result = VelocityUtil.getString(template, params);

        // Then
        assertThat(result).isEqualTo("Hello World!");
    }

    @Test
    @DisplayName("测试getString方法-数学运算")
    void testGetString_MathOperation() throws ParseException {
        // Given
        String template = "Result: $math.add(10, 20)";
        Map<String, Object> params = new HashMap<>();

        // When
        String result = VelocityUtil.getString(template, params);

        // Then
        assertThat(result).isEqualTo("Result: 30");
    }

    @Test
    @DisplayName("测试getString方法-复杂模板")
    void testGetString_ComplexTemplate() throws ParseException {
        // Given
        String template = "#if($age > 18)Adult: ${name}#else Child: ${name}#end";
        Map<String, Object> params = new HashMap<>();
        params.put("name", "John");
        params.put("age", 25);

        // When
        String result = VelocityUtil.getString(template, params);

        // Then
        assertThat(result).isEqualTo("Adult: John");
    }

    @Test
    @DisplayName("测试getString方法-无效模板抛出异常")
    void testGetString_InvalidTemplate() {
        // Given
        String template = "#if($invalid";
        Map<String, Object> params = new HashMap<>();

        // When & Then
        assertThatThrownBy(() -> VelocityUtil.getString(template, params))
                .isInstanceOf(ParseException.class);
    }

    @Test
    @DisplayName("测试mergeString方法-字符串连接")
    void testMergeString_StringConcatenation() throws ParseException {
        // Given
        String template = "\"Hello\" + \" \" + \"World\"";
        Map<String, Object> params = new HashMap<>();

        // When
        String result = VelocityUtil.mergeString(template, params);

        // Then
        assertThat(result).isEqualTo("Hello World");
    }

    @Test
    @DisplayName("测试mergeString方法-变量连接")
    void testMergeString_VariableConcatenation() throws ParseException {
        // Given
        String template = "$first + \" \" + $second";
        Map<String, Object> params = new HashMap<>();
        params.put("first", "Hello");
        params.put("second", "World");

        // When
        String result = VelocityUtil.mergeString(template, params);

        // Then
        assertThat(result).isEqualTo("Hello World");
    }

    @Test
    @DisplayName("测试executeString方法-包含if语句")
    void testExecuteString_WithIfStatement() throws ParseException {
        // Given
        String template = "#if($flag)Yes#else No#end";
        Map<String, Object> params = new HashMap<>();
        params.put("flag", true);

        // When
        String result = VelocityUtil.executeString(template, params);

        // Then
        assertThat(result).isEqualTo("Yes");
    }

    @Test
    @DisplayName("测试executeString方法-不包含if语句")
    void testExecuteString_WithoutIfStatement() throws ParseException {
        // Given
        String template = "\"Hello\" + \" World\"";
        Map<String, Object> params = new HashMap<>();

        // When
        String result = VelocityUtil.executeString(template, params);

        // Then
        assertThat(result).isEqualTo("Hello World");
    }

    @Test
    @DisplayName("测试isTrue方法-返回true")
    void testIsTrue_ReturnsTrue() throws ParseException {
        // Given
        String template = "$flag";
        Map<String, Object> params = new HashMap<>();
        params.put("flag", true);

        // When
        boolean result = VelocityUtil.isTrue(template, params);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试isTrue方法-返回false")
    void testIsTrue_ReturnsFalse() throws ParseException {
        // Given
        String template = "$flag";
        Map<String, Object> params = new HashMap<>();
        params.put("flag", false);

        // When
        boolean result = VelocityUtil.isTrue(template, params);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试isTrue方法-字符串true")
    void testIsTrue_StringTrue() throws ParseException {
        // Given
        String template = "\"true\"";
        Map<String, Object> params = new HashMap<>();

        // When
        boolean result = VelocityUtil.isTrue(template, params);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("测试isTrue方法-字符串false")
    void testIsTrue_StringFalse() throws ParseException {
        // Given
        String template = "\"false\"";
        Map<String, Object> params = new HashMap<>();

        // When
        boolean result = VelocityUtil.isTrue(template, params);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试calWeight方法-计算权重")
    void testCalWeight_CalculateWeight() throws ParseException {
        // Given
        String template = "$math.add($weight1, $weight2)";
        Map<String, Object> params = new HashMap<>();
        params.put("weight1", 10);
        params.put("weight2", 20);

        // When
        int result = VelocityUtil.calWeight(template, params);

        // Then
        assertThat(result).isEqualTo(30);
    }

    @Test
    @DisplayName("测试calWeight方法-字符串数字")
    void testCalWeight_StringNumber() throws ParseException {
        // Given
        String template = "\"100\"";
        Map<String, Object> params = new HashMap<>();

        // When
        int result = VelocityUtil.calWeight(template, params);

        // Then
        assertThat(result).isEqualTo(100);
    }

    @Test
    @DisplayName("测试getAmount方法-正常计算")
    void testGetAmount_NormalCalculation() throws ParseException {
        // Given
        String template = "$math.add($amount1, $amount2)";
        Map<String, Money> params = new HashMap<>();
        params.put("amount1", new Money("100.50", "AED"));
        params.put("amount2", new Money("200.25", "AED"));

        // When
        Money result = VelocityUtil.getAmount(template, params);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAmount()).isEqualByComparingTo("300.75");
        assertThat(result.getCurrency()).isEqualTo("AED");
    }

    @Test
    @DisplayName("测试getAmount方法-空模板")
    void testGetAmount_EmptyTemplate() throws ParseException {
        // Given
        String template = "";
        Map<String, Money> params = new HashMap<>();

        // When
        Money result = VelocityUtil.getAmount(template, params);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAmount()).isEqualByComparingTo("0.00");
        assertThat(result.getCurrency()).isEqualTo("AED");
    }

    @Test
    @DisplayName("测试getAmount方法-空参数")
    void testGetAmount_EmptyParams() throws ParseException {
        // Given
        String template = "$amount1";
        Map<String, Money> params = new HashMap<>();

        // When
        Money result = VelocityUtil.getAmount(template, params);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAmount()).isEqualByComparingTo("0.00");
        assertThat(result.getCurrency()).isEqualTo("AED");
    }

    @Test
    @DisplayName("测试warpVariable方法-包装变量")
    void testWarpVariable_WrapVariable() {
        // Given
        String variable = "testVar";

        // When
        String result = VelocityUtil.warpVariable(variable);

        // Then
        assertThat(result).isEqualTo("${testVar}");
    }

    @Test
    @DisplayName("测试warpVariable方法-空变量")
    void testWarpVariable_EmptyVariable() {
        // Given
        String variable = "";

        // When
        String result = VelocityUtil.warpVariable(variable);

        // Then
        assertThat(result).isEqualTo("${}");
    }

    @Test
    @DisplayName("测试warpVariable方法-null变量")
    void testWarpVariable_NullVariable() {
        // Given
        String variable = null;

        // When
        String result = VelocityUtil.warpVariable(variable);

        // Then
        assertThat(result).isEqualTo("${null}");
    }

    @Test
    @DisplayName("测试多线程安全性")
    void testThreadSafety() throws InterruptedException {
        // Given
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        boolean[] results = new boolean[threadCount];

        // When
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    VelocityEngine engine = VelocityUtil.loadEngine();
                    String template = "Hello ${name}!";
                    Map<String, Object> params = new HashMap<>();
                    params.put("name", "Thread" + index);
                    String result = VelocityUtil.getString(template, params);
                    results[index] = ("Hello Thread" + index + "!").equals(result);
                } catch (Exception e) {
                    results[index] = false;
                }
            });
            threads[i].start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // Then
        for (boolean result : results) {
            assertThat(result).isTrue();
        }
    }
}
