package com.uaepay.cmf.common.core.engine.schedule.enums;

import com.uaepay.cmf.common.core.engine.BaseCoreEngineTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.Assertions.*;

/**
 * DaemonTaskType类单元测试
 * 测试定时任务类型类的基本功能
 */
class DaemonTaskTypeTest extends BaseCoreEngineTest {

    @Test
    @DisplayName("测试构造函数和getter方法")
    void testConstructorAndGetters() {
        // Given
        String expectedCode = "testCode";
        String expectedMessage = "测试消息";
        
        // When
        DaemonTaskType taskType = new DaemonTaskType(expectedCode, expectedMessage);
        
        // Then
        assertThat(taskType.getCode()).isEqualTo(expectedCode);
        assertThat(taskType.getMessage()).isEqualTo(expectedMessage);
    }

    @Test
    @DisplayName("测试构造函数-null值")
    void testConstructorWithNullValues() {
        // When
        DaemonTaskType taskType = new DaemonTaskType(null, null);
        
        // Then
        assertThat(taskType.getCode()).isNull();
        assertThat(taskType.getMessage()).isNull();
    }

    @Test
    @DisplayName("测试构造函数-空字符串")
    void testConstructorWithEmptyStrings() {
        // Given
        String emptyCode = "";
        String emptyMessage = "";
        
        // When
        DaemonTaskType taskType = new DaemonTaskType(emptyCode, emptyMessage);
        
        // Then
        assertThat(taskType.getCode()).isEmpty();
        assertThat(taskType.getMessage()).isEmpty();
    }

    @Test
    @DisplayName("测试不同实例的独立性")
    void testInstanceIndependence() {
        // Given
        DaemonTaskType taskType1 = new DaemonTaskType("code1", "message1");
        DaemonTaskType taskType2 = new DaemonTaskType("code2", "message2");

        // Then
        assertThat(taskType1.getCode()).isNotEqualTo(taskType2.getCode());
        assertThat(taskType1.getMessage()).isNotEqualTo(taskType2.getMessage());
        assertThat(taskType1).isNotEqualTo(taskType2);
    }

    @ParameterizedTest
    @CsvSource({
        "common, 通用任务",
        "payment, 支付任务",
        "refund, 退款任务",
        "notify, 通知任务",
        "reconcile, 对账任务"
    })
    @DisplayName("测试不同类型的任务类型创建")
    void testDifferentTaskTypes(String code, String message) {
        // When
        DaemonTaskType taskType = new DaemonTaskType(code, message);

        // Then
        assertThat(taskType.getCode()).isEqualTo(code);
        assertThat(taskType.getMessage()).isEqualTo(message);
    }

    @Test
    @DisplayName("测试长字符串")
    void testLongStrings() {
        // Given
        StringBuilder longCodeBuilder = new StringBuilder();
        StringBuilder longMessageBuilder = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            longCodeBuilder.append("a");
        }
        for (int i = 0; i < 50; i++) {
            longMessageBuilder.append("测试");
        }
        String longCode = longCodeBuilder.toString();
        String longMessage = longMessageBuilder.toString();

        // When
        DaemonTaskType taskType = new DaemonTaskType(longCode, longMessage);

        // Then
        assertThat(taskType.getCode()).hasSize(100);
        assertThat(taskType.getMessage()).hasSize(100); // 50个中文字符 = 100个字符长度
    }

    @Test
    @DisplayName("测试特殊字符")
    void testSpecialCharacters() {
        // Given
        String specialCode = "task!@#$%^&*()_+-=[]{}|;':\",./<>?";
        String specialMessage = "任务!@#$%^&*()_+-=[]{}|;':\",./<>?测试";

        // When
        DaemonTaskType taskType = new DaemonTaskType(specialCode, specialMessage);

        // Then
        assertThat(taskType.getCode()).isEqualTo(specialCode);
        assertThat(taskType.getMessage()).isEqualTo(specialMessage);
    }

    @Test
    @DisplayName("测试使用测试基类的工厂方法")
    void testUsingBaseTestFactory() {
        // When
        DaemonTaskType taskType = createTestDaemonTaskType();

        // Then
        assertThat(taskType).isNotNull();
        assertThat(taskType.getCode()).isEqualTo("test");
        assertThat(taskType.getMessage()).isEqualTo("测试任务");
    }

    @Test
    @DisplayName("测试对象不可变性")
    void testImmutability() {
        // Given
        String originalCode = "originalTask";
        String originalMessage = "原始任务";
        DaemonTaskType taskType = new DaemonTaskType(originalCode, originalMessage);

        // When - 字段是final的，无法修改
        // Then
        assertThat(taskType.getCode()).isEqualTo(originalCode);
        assertThat(taskType.getMessage()).isEqualTo(originalMessage);
    }

    @Test
    @DisplayName("测试Unicode字符")
    void testUnicodeCharacters() {
        // Given
        String unicodeCode = "task_测试_🚀";
        String unicodeMessage = "任务测试 🎯 Unicode支持";

        // When
        DaemonTaskType taskType = new DaemonTaskType(unicodeCode, unicodeMessage);

        // Then
        assertThat(taskType.getCode()).isEqualTo(unicodeCode);
        assertThat(taskType.getMessage()).isEqualTo(unicodeMessage);
    }
}