package com.uaepay.cmf.common.core.engine.schedule.enums;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * DaemonTaskType类单元测试
 * 测试定时任务类型类的基本功能
 */
class DaemonTaskTypeTest {

    @Test
    @DisplayName("测试构造函数和getter方法")
    void testConstructorAndGetters() {
        // Given
        String expectedCode = "testCode";
        String expectedMessage = "测试消息";
        
        // When
        DaemonTaskType taskType = new DaemonTaskType(expectedCode, expectedMessage);
        
        // Then
        assertThat(taskType.getCode()).isEqualTo(expectedCode);
        assertThat(taskType.getMessage()).isEqualTo(expectedMessage);
    }

    @Test
    @DisplayName("测试构造函数-null值")
    void testConstructorWithNullValues() {
        // When
        DaemonTaskType taskType = new DaemonTaskType(null, null);
        
        // Then
        assertThat(taskType.getCode()).isNull();
        assertThat(taskType.getMessage()).isNull();
    }

    @Test
    @DisplayName("测试构造函数-空字符串")
    void testConstructorWithEmptyStrings() {
        // Given
        String emptyCode = "";
        String emptyMessage = "";
        
        // When
        DaemonTaskType taskType = new DaemonTaskType(emptyCode, emptyMessage);
        
        // Then
        assertThat(taskType.getCode()).isEmpty();
        assertThat(taskType.getMessage()).isEmpty();
    }

    @Test
    @DisplayName("测试不同实例的独立性")
    void testInstanceIndependence() {
        // Given
        DaemonTaskType taskType1 = new DaemonTaskType("code1", "message1");
        DaemonTaskType taskType2 = new DaemonTaskType("code2", "message2");
        
        // Then
        assertThat(taskType1.getCode()).isNotEqualTo(taskType2.getCode());
        assertThat(taskType1.getMessage()).isNotEqualTo(taskType2.getMessage());
        assertThat(taskType1).isNotEqualTo(taskType2);
    }
} 