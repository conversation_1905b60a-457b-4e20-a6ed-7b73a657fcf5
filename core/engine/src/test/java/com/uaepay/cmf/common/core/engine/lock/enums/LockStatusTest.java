package com.uaepay.cmf.common.core.engine.lock.enums;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static org.assertj.core.api.Assertions.*;

/**
 * LockStatus枚举类单元测试
 * 测试锁状态枚举的所有功能
 */
class LockStatusTest {

    @Test
    @DisplayName("测试枚举值数量")
    void testEnumValuesCount() {
        // When
        LockStatus[] values = LockStatus.values();
        
        // Then
        assertThat(values).hasSize(2);
    }

    @ParameterizedTest
    @EnumSource(LockStatus.class)
    @DisplayName("测试所有枚举值的getter方法")
    void testAllEnumValues(LockStatus lockStatus) {
        // Then
        assertThat(lockStatus.getCode()).isNotNull().isNotEmpty();
        assertThat(lockStatus.getMessage()).isNotNull().isNotEmpty();
    }

    @Test
    @DisplayName("测试EXCUTE枚举值")
    void testExcute() {
        // When
        LockStatus excute = LockStatus.EXCUTE;
        
        // Then
        assertThat(excute.getCode()).isEqualTo("E");
        assertThat(excute.getMessage()).isEqualTo("执行");
    }

    @Test
    @DisplayName("测试FINISH枚举值")
    void testFinish() {
        // When
        LockStatus finish = LockStatus.FINISH;
        
        // Then
        assertThat(finish.getCode()).isEqualTo("F");
        assertThat(finish.getMessage()).isEqualTo("完成");
    }

    @Test
    @DisplayName("测试getByCode方法-EXCUTE")
    void testGetByCode_Excute() {
        // When
        LockStatus result = LockStatus.getByCode("E");
        
        // Then
        assertThat(result).isEqualTo(LockStatus.EXCUTE);
    }

    @Test
    @DisplayName("测试getByCode方法-FINISH")
    void testGetByCode_Finish() {
        // When
        LockStatus result = LockStatus.getByCode("F");
        
        // Then
        assertThat(result).isEqualTo(LockStatus.FINISH);
    }

    @Test
    @DisplayName("测试getByCode方法-不存在的代码")
    void testGetByCode_NotFound() {
        // When
        LockStatus result = LockStatus.getByCode("X");
        
        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试getByCode方法-null代码")
    void testGetByCode_NullCode() {
        // When
        LockStatus result = LockStatus.getByCode(null);
        
        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试getByCode方法-空字符串代码")
    void testGetByCode_EmptyCode() {
        // When
        LockStatus result = LockStatus.getByCode("");
        
        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试valueOf方法")
    void testValueOf() {
        // When & Then
        assertThat(LockStatus.valueOf("EXCUTE")).isEqualTo(LockStatus.EXCUTE);
        assertThat(LockStatus.valueOf("FINISH")).isEqualTo(LockStatus.FINISH);
        
        // 测试异常情况
        assertThatThrownBy(() -> LockStatus.valueOf("INVALID"))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // When & Then
        assertThat(LockStatus.EXCUTE.toString()).isEqualTo("EXCUTE");
        assertThat(LockStatus.FINISH.toString()).isEqualTo("FINISH");
    }
} 