package com.uaepay.cmf.common.core.engine.cache.impl;

import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.engine.util.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * LocalCacheClient类单元测试
 * 测试本地缓存功能
 */
@ExtendWith(MockitoExtension.class)
class LocalCacheClientTest {
    
    private LocalCacheClient localCacheClient;
    private static final CacheType TEST_CACHE_TYPE = CacheType.CMF_SYS_CONFIGURATION;
    private static final String TEST_KEY = "test_key";
    private static final String TEST_VALUE = "test_value";
    private static final String CACHE_KEY = "CMF_SYS_CONFIGURATION:test_key";
    
    @BeforeEach
    void setUp() {
        localCacheClient = new LocalCacheClient();
    }
    
    @Test
    @DisplayName("测试put和get方法-正常存取")
    void testPutAndGet_Success() {
        // When & Then
        try (MockedStatic<CommonUtil> commonUtilMock = Mockito.mockStatic(CommonUtil.class)) {
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, TEST_KEY))
                    .thenReturn(CACHE_KEY);
            
            // Put操作
            localCacheClient.put(TEST_CACHE_TYPE, TEST_KEY, TEST_VALUE, 60);
            
            // Get操作
            Object result = localCacheClient.get(TEST_CACHE_TYPE, TEST_KEY);
            
            // 验证结果
            assertThat(result).isEqualTo(TEST_VALUE);
            
            // 验证CommonUtil被正确调用
            commonUtilMock.verify(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, TEST_KEY), 
                    Mockito.times(2));
        }
    }
    
    @Test
    @DisplayName("测试get方法-缓存不存在")
    void testGet_CacheNotExists() {
        // When & Then
        try (MockedStatic<CommonUtil> commonUtilMock = Mockito.mockStatic(CommonUtil.class)) {
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, TEST_KEY))
                    .thenReturn(CACHE_KEY);
            
            Object result = localCacheClient.get(TEST_CACHE_TYPE, TEST_KEY);
            
            assertThat(result).isNull();
            commonUtilMock.verify(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, TEST_KEY));
        }
    }
    
    @Test
    @DisplayName("测试缓存过期-永不过期")
    void testCache_NeverExpire() {
        // When & Then
        try (MockedStatic<CommonUtil> commonUtilMock = Mockito.mockStatic(CommonUtil.class)) {
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, TEST_KEY))
                    .thenReturn(CACHE_KEY);
            
            // 使用NEVER_EXPIRE常量（0）
            localCacheClient.put(TEST_CACHE_TYPE, TEST_KEY, TEST_VALUE, 0);
            
            Object result = localCacheClient.get(TEST_CACHE_TYPE, TEST_KEY);
            
            assertThat(result).isEqualTo(TEST_VALUE);
        }
    }
    
    @Test
    @DisplayName("测试缓存过期-已过期数据")
    void testCache_Expired() throws Exception {
        // When & Then
        try (MockedStatic<CommonUtil> commonUtilMock = Mockito.mockStatic(CommonUtil.class)) {
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, TEST_KEY))
                    .thenReturn(CACHE_KEY);
            
            // 缓存1秒过期
            localCacheClient.put(TEST_CACHE_TYPE, TEST_KEY, TEST_VALUE, 1);
            
            // 使用反射修改创建时间，模拟2秒前创建的缓存
            modifyCacheCreateTime(CACHE_KEY, System.currentTimeMillis() - 2000L);
            
            // 获取缓存，应该已过期
            Object result = localCacheClient.get(TEST_CACHE_TYPE, TEST_KEY);
            
            assertThat(result).isNull();
            
            // 验证过期数据被移除，再次获取仍然为null
            Object result2 = localCacheClient.get(TEST_CACHE_TYPE, TEST_KEY);
            assertThat(result2).isNull();
        }
    }
    
    @Test
    @DisplayName("测试缓存过期-未过期数据")
    void testCache_NotExpired() throws Exception {
        // When & Then
        try (MockedStatic<CommonUtil> commonUtilMock = Mockito.mockStatic(CommonUtil.class)) {
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, TEST_KEY))
                    .thenReturn(CACHE_KEY);
            
            // 缓存1秒过期
            localCacheClient.put(TEST_CACHE_TYPE, TEST_KEY, TEST_VALUE, 1);
            
            // 使用反射修改创建时间，模拟0.5秒前创建的缓存
            modifyCacheCreateTime(CACHE_KEY, System.currentTimeMillis() - 500L);
            
            // 获取缓存，应该还未过期
            Object result = localCacheClient.get(TEST_CACHE_TYPE, TEST_KEY);
            
            assertThat(result).isEqualTo(TEST_VALUE);
        }
    }
    
    /**
     * 使用反射修改缓存的创建时间
     */
    private void modifyCacheCreateTime(String cacheKey, long createTime) throws Exception {
        // 获取content字段
        Field contentField = LocalCacheClient.class.getDeclaredField("content");
        contentField.setAccessible(true);
        Map<String, Object> content = (Map<String, Object>) contentField.get(localCacheClient);
        
        // 获取CachedData对象
        Object cachedData = content.get(cacheKey);
        assertThat(cachedData).isNotNull();
        
        // 修改createTimeMillis字段
        Field createTimeField = cachedData.getClass().getDeclaredField("createTimeMillis");
        createTimeField.setAccessible(true);
        createTimeField.set(cachedData, createTime);
    }
    
    @Test
    @DisplayName("测试remove方法")
    void testRemove() {
        // When & Then
        try (MockedStatic<CommonUtil> commonUtilMock = Mockito.mockStatic(CommonUtil.class)) {
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, TEST_KEY))
                    .thenReturn(CACHE_KEY);
            
            // 先放入缓存
            localCacheClient.put(TEST_CACHE_TYPE, TEST_KEY, TEST_VALUE, 60);
            
            // 移除缓存
            Object removedValue = localCacheClient.remove(TEST_CACHE_TYPE, TEST_KEY);
            
            // 验证移除成功
            assertThat(removedValue).isNotNull();
            
            // 验证移除后获取为null
            Object result = localCacheClient.get(TEST_CACHE_TYPE, TEST_KEY);
            assertThat(result).isNull();
        }
    }
    
    @Test
    @DisplayName("测试remove方法-不存在的key")
    void testRemove_NonExistentKey() {
        // When & Then
        try (MockedStatic<CommonUtil> commonUtilMock = Mockito.mockStatic(CommonUtil.class)) {
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, TEST_KEY))
                    .thenReturn(CACHE_KEY);
            
            Object removedValue = localCacheClient.remove(TEST_CACHE_TYPE, TEST_KEY);
            
            assertThat(removedValue).isNull();
        }
    }
    
    @Test
    @DisplayName("测试flush方法")
    void testFlush() {
        // When & Then
        try (MockedStatic<CommonUtil> commonUtilMock = Mockito.mockStatic(CommonUtil.class)) {
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(any(CacheType.class), anyString()))
                    .thenReturn("key1", "key2");
            
            // 放入多个缓存
            localCacheClient.put(TEST_CACHE_TYPE, "key1", "value1", 60);
            localCacheClient.put(TEST_CACHE_TYPE, "key2", "value2", 60);
            
            // 清空缓存
            localCacheClient.flush(TEST_CACHE_TYPE);
            
            // 验证所有缓存都被清空
            Object result1 = localCacheClient.get(TEST_CACHE_TYPE, "key1");
            Object result2 = localCacheClient.get(TEST_CACHE_TYPE, "key2");
            
            assertThat(result1).isNull();
            assertThat(result2).isNull();
        }
    }
    
    @Test
    @DisplayName("测试removeByKeys方法-正常删除")
    void testRemoveByKeys_Success() {
        // Given
        Set<String> keysToRemove = new HashSet<>();
        keysToRemove.add("key1");
        keysToRemove.add("key2");
        
        // When & Then
        try (MockedStatic<CommonUtil> commonUtilMock = Mockito.mockStatic(CommonUtil.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, "key1"))
                    .thenReturn("cache_key1");
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, "key2"))
                    .thenReturn("cache_key2");
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(TEST_CACHE_TYPE, "key3"))
                    .thenReturn("cache_key3");
            
            collectionUtilsMock.when(() -> CollectionUtils.isNotEmpty(keysToRemove))
                    .thenReturn(true);
            
            // 先放入缓存
            localCacheClient.put(TEST_CACHE_TYPE, "key1", "value1", 60);
            localCacheClient.put(TEST_CACHE_TYPE, "key2", "value2", 60);
            localCacheClient.put(TEST_CACHE_TYPE, "key3", "value3", 60);
            
            // 批量删除
            localCacheClient.removeByKeys(TEST_CACHE_TYPE, keysToRemove);
            
            // 验证指定的key被删除
            Object result1 = localCacheClient.get(TEST_CACHE_TYPE, "key1");
            Object result2 = localCacheClient.get(TEST_CACHE_TYPE, "key2");
            Object result3 = localCacheClient.get(TEST_CACHE_TYPE, "key3");
            
            assertThat(result1).isNull();
            assertThat(result2).isNull();
            assertThat(result3).isEqualTo("value3");  // key3没有被删除
        }
    }
    
    @Test
    @DisplayName("测试removeByKeys方法-cacheType为null")
    void testRemoveByKeys_NullCacheType() {
        // Given
        Set<String> keysToRemove = new HashSet<>();
        keysToRemove.add("key1");
        
        // When
        localCacheClient.removeByKeys(null, keysToRemove);
        
        // Then - 应该没有异常，方法正常返回
        // 这个测试主要验证方法的防御性编程
        assertThat(true).isTrue(); // 验证方法执行完成
    }
    
    @Test
    @DisplayName("测试removeByKeys方法-keys为空")
    void testRemoveByKeys_EmptyKeys() {
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isNotEmpty(any()))
                    .thenReturn(false);
            
            localCacheClient.removeByKeys(TEST_CACHE_TYPE, new HashSet<>());
            
            // 验证CollectionUtils.isNotEmpty被调用
            collectionUtilsMock.verify(() -> CollectionUtils.isNotEmpty(any()));
        }
    }
    
    @Test
    @DisplayName("测试removeByKeys方法-keys为null")
    void testRemoveByKeys_NullKeys() {
        // When & Then
        try (MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            collectionUtilsMock.when(() -> CollectionUtils.isNotEmpty(null))
                    .thenReturn(false);
            
            localCacheClient.removeByKeys(TEST_CACHE_TYPE, null);
            
            // 验证CollectionUtils.isNotEmpty被调用
            collectionUtilsMock.verify(() -> CollectionUtils.isNotEmpty(null));
        }
    }
    
    @Test
    @DisplayName("测试并发访问-多线程安全")
    void testConcurrentAccess() throws InterruptedException {
        // Given
        int threadCount = 10;
        int operationsPerThread = 100;
        
        // When & Then
        try (MockedStatic<CommonUtil> commonUtilMock = Mockito.mockStatic(CommonUtil.class)) {
            // Mock CommonUtil返回一致的结果
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(any(CacheType.class), anyString()))
                    .thenAnswer(invocation -> {
                        CacheType cacheType = invocation.getArgument(0);
                        String key = invocation.getArgument(1);
                        return cacheType.getCode() + ":" + key;
                    });
            
            Thread[] threads = new Thread[threadCount];
            
            for (int i = 0; i < threadCount; i++) {
                final int threadId = i;
                threads[i] = new Thread(() -> {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String key = "thread_" + threadId + "_key_" + j;
                        String value = "thread_" + threadId + "_value_" + j;
                        
                        // 执行缓存操作
                        localCacheClient.put(TEST_CACHE_TYPE, key, value, 60);
                        Object result = localCacheClient.get(TEST_CACHE_TYPE, key);
                        assertThat(result).isEqualTo(value);
                    }
                });
            }
            
            // 启动所有线程
            for (Thread thread : threads) {
                thread.start();
            }
            
            // 等待所有线程完成
            for (Thread thread : threads) {
                thread.join();
            }
            
            // 验证没有异常发生，所有操作正常完成
            assertThat(true).isTrue();
        }
    }
    
    @Test
    @DisplayName("测试缓存数据类型-不同数据类型")
    void testDifferentDataTypes() {
        // When & Then
        try (MockedStatic<CommonUtil> commonUtilMock = Mockito.mockStatic(CommonUtil.class)) {
            commonUtilMock.when(() -> CommonUtil.buildCacheKey(any(CacheType.class), anyString()))
                    .thenAnswer(invocation -> {
                        CacheType cacheType = invocation.getArgument(0);
                        String key = invocation.getArgument(1);
                        return cacheType.getCode() + ":" + key;
                    });
            
            // 测试字符串
            localCacheClient.put(TEST_CACHE_TYPE, "string_key", "string_value", 60);
            assertThat(localCacheClient.get(TEST_CACHE_TYPE, "string_key")).isEqualTo("string_value");
            
            // 测试整数
            localCacheClient.put(TEST_CACHE_TYPE, "int_key", 123, 60);
            assertThat(localCacheClient.get(TEST_CACHE_TYPE, "int_key")).isEqualTo(123);
            
            // 测试对象
            TestObject testObject = new TestObject("test", 456);
            localCacheClient.put(TEST_CACHE_TYPE, "object_key", testObject, 60);
            assertThat(localCacheClient.get(TEST_CACHE_TYPE, "object_key")).isEqualTo(testObject);
            
            // 测试null值
            localCacheClient.put(TEST_CACHE_TYPE, "null_key", null, 60);
            assertThat(localCacheClient.get(TEST_CACHE_TYPE, "null_key")).isNull();
        }
    }
    
    // 测试用的简单对象
    private static class TestObject {
        private String name;
        private int value;
        
        public TestObject(String name, int value) {
            this.name = name;
            this.value = value;
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestObject that = (TestObject) obj;
            return value == that.value && name.equals(that.name);
        }
        
        @Override
        public int hashCode() {
            return name.hashCode() + value;
        }
    }
} 