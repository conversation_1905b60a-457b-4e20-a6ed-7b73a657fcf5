package com.uaepay.cmf.common.core.engine.schedule.task;

import com.uaepay.cmf.common.core.engine.BaseCoreEngineTest;
import com.uaepay.cmf.common.core.engine.schedule.DaemonContext;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * CommonTask通用定时任务测试
 * 测试通用定时任务的所有功能
 */
class CommonTaskTest extends BaseCoreEngineTest {

    private CommonTask commonTask;

    @BeforeEach
    void setUpCommonTask() {
        commonTask = new CommonTask();
    }

    @Test
    @DisplayName("测试getTaskType方法")
    void testGetTaskType() {
        // When
        DaemonTaskType taskType = commonTask.getTaskType();
        
        // Then
        assertThat(taskType).isNotNull();
        assertThat(taskType.getCode()).isEqualTo("common");
        assertThat(taskType.getMessage()).isEqualTo("通用任务");
    }

    @Test
    @DisplayName("测试execute方法正常执行")
    void testExecuteNormalFlow() {
        // Given
        DaemonContext context = createTestDaemonContext();
        
        // When
        long startTime = System.currentTimeMillis();
        DaemonTaskResult result = commonTask.execute(context);
        long endTime = System.currentTimeMillis();
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTaskType()).isNotNull();
        assertThat(result.getTaskType().getCode()).isEqualTo("common");
        assertThat(result.getTaskType().getMessage()).isEqualTo("通用任务");
        
        // 验证执行时间大约5秒（允许一些误差）
        long executionTime = endTime - startTime;
        assertThat(executionTime).isBetween(4900L, 5500L);
    }

    // 注意：由于Thread.sleep是静态方法且不能被mock，我们跳过中断测试
    // 实际的中断处理已经在CommonTask.execute方法中正确实现

    @Test
    @DisplayName("测试executeTask方法")
    void testExecuteTask() {
        // Given
        Object domain = new Object();
        
        // When
        boolean result = commonTask.executeTask(domain);
        
        // Then
        assertThat(result).isFalse(); // CommonTask的executeTask总是返回false
    }

    @Test
    @DisplayName("测试loadTask方法")
    void testLoadTask() {
        // Given
        int batchSize = 100;
        
        // When
        List result = commonTask.loadTask(batchSize);
        
        // Then
        assertThat(result).isNull(); // CommonTask的loadTask总是返回null
    }

    @Test
    @DisplayName("测试execute方法的日志输出")
    void testExecuteLogging() {
        // Given
        DaemonContext context = createTestDaemonContext();
        
        // When
        DaemonTaskResult result = commonTask.execute(context);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        // 注意：实际的日志验证需要使用日志框架的测试工具
        // 这里我们主要验证方法执行没有抛出异常
    }

    @Test
    @DisplayName("测试execute方法的上下文处理")
    void testExecuteContextHandling() {
        // Given
        DaemonContext context = new DaemonContext();
        context.setTriggerId("test_trigger_123");
        context.setTargetIdent("test_target");
        
        // When
        DaemonTaskResult result = commonTask.execute(context);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        // 验证结果中的任务类型
        DaemonTaskType resultTaskType = result.getTaskType();
        assertThat(resultTaskType.getCode()).isEqualTo("common");
        assertThat(resultTaskType.getMessage()).isEqualTo("通用任务");
    }

    @Test
    @DisplayName("测试execute方法的空上下文处理")
    void testExecuteWithNullContext() {
        // When & Then
        assertThatThrownBy(() -> commonTask.execute((DaemonContext) null))
            .isInstanceOf(NullPointerException.class);
    }

    @Test
    @DisplayName("测试execute方法的空triggerId处理")
    void testExecuteWithNullTriggerId() {
        // Given
        DaemonContext context = new DaemonContext();
        context.setTriggerId(null);
        
        // When
        DaemonTaskResult result = commonTask.execute(context);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
    }

    @Test
    @DisplayName("测试任务类型的一致性")
    void testTaskTypeConsistency() {
        // Given
        DaemonContext context = createTestDaemonContext();
        
        // When
        DaemonTaskType getTaskTypeResult = commonTask.getTaskType();
        DaemonTaskResult executeResult = commonTask.execute(context);
        
        // Then
        assertThat(getTaskTypeResult.getCode()).isEqualTo("common");
        assertThat(executeResult.getTaskType().getCode()).isEqualTo("common");
        assertThat(getTaskTypeResult.getMessage()).isEqualTo("通用任务");
        assertThat(executeResult.getTaskType().getMessage()).isEqualTo("通用任务");
    }

    @Test
    @DisplayName("测试多次执行的独立性")
    void testMultipleExecutionIndependence() {
        // Given
        DaemonContext context1 = createTestDaemonContext();
        DaemonContext context2 = createTestDaemonContext();
        context2.setTriggerId("different_trigger");
        
        // When
        DaemonTaskResult result1 = commonTask.execute(context1);
        DaemonTaskResult result2 = commonTask.execute(context2);
        
        // Then
        assertThat(result1).isNotNull();
        assertThat(result2).isNotNull();
        assertThat(result1.isSuccess()).isTrue();
        assertThat(result2.isSuccess()).isTrue();
        
        // 验证结果是独立的对象
        assertThat(result1).isNotSameAs(result2);
    }

    @Test
    @DisplayName("测试继承的抽象方法实现")
    void testAbstractMethodImplementations() {
        // When & Then
        assertThat(commonTask.executeTask(new Object())).isFalse();
        assertThat(commonTask.loadTask(100)).isNull();
        assertThat(commonTask.getTaskType()).isNotNull();
    }

    @Test
    @DisplayName("测试Spring组件注解")
    void testSpringComponentAnnotation() {
        // When & Then
        assertThat(CommonTask.class.isAnnotationPresent(org.springframework.stereotype.Component.class))
            .isTrue();
        
        // 验证组件名称
        org.springframework.stereotype.Component annotation = 
            CommonTask.class.getAnnotation(org.springframework.stereotype.Component.class);
        assertThat(annotation.value()).isEqualTo("commonTask");
    }
}
