package com.uaepay.cmf.common.core.engine.schedule.task;

import com.uaepay.cmf.common.core.engine.BaseCoreEngineTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static org.assertj.core.api.Assertions.*;

/**
 * DaemonTaskResultCode枚举类单元测试
 * 测试定时任务结果代码枚举的所有功能
 */
class DaemonTaskResultCodeTest extends BaseCoreEngineTest {

    @Test
    @DisplayName("测试枚举值数量")
    void testEnumValuesCount() {
        // When
        DaemonTaskResultCode[] values = DaemonTaskResultCode.values();
        
        // Then
        assertThat(values).hasSize(2);
    }

    @ParameterizedTest
    @EnumSource(DaemonTaskResultCode.class)
    @DisplayName("测试所有枚举值的getter方法")
    void testAllEnumValues(DaemonTaskResultCode resultCode) {
        // Then
        assertThat(resultCode.getMessage()).isNotNull().isNotEmpty();
    }

    @Test
    @DisplayName("测试EXCEPTION枚举值")
    void testException() {
        // When
        DaemonTaskResultCode exception = DaemonTaskResultCode.EXCEPTION;
        
        // Then
        assertThat(exception.getMessage()).isEqualTo("执行异常");
    }

    @Test
    @DisplayName("测试SUCCESS枚举值")
    void testSuccess() {
        // When
        DaemonTaskResultCode success = DaemonTaskResultCode.SUCCESS;
        
        // Then
        assertThat(success.getMessage()).isEqualTo("执行成功");
    }

    @Test
    @DisplayName("测试valueOf方法")
    void testValueOf() {
        // When & Then
        assertThat(DaemonTaskResultCode.valueOf("EXCEPTION")).isEqualTo(DaemonTaskResultCode.EXCEPTION);
        assertThat(DaemonTaskResultCode.valueOf("SUCCESS")).isEqualTo(DaemonTaskResultCode.SUCCESS);
        
        // 测试异常情况
        assertThatThrownBy(() -> DaemonTaskResultCode.valueOf("INVALID"))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // When & Then
        assertThat(DaemonTaskResultCode.EXCEPTION.toString()).isEqualTo("EXCEPTION");
        assertThat(DaemonTaskResultCode.SUCCESS.toString()).isEqualTo("SUCCESS");
    }

    @Test
    @DisplayName("测试枚举常量的唯一性")
    void testEnumUniqueness() {
        // When
        DaemonTaskResultCode exception1 = DaemonTaskResultCode.EXCEPTION;
        DaemonTaskResultCode exception2 = DaemonTaskResultCode.EXCEPTION;
        DaemonTaskResultCode success1 = DaemonTaskResultCode.SUCCESS;
        DaemonTaskResultCode success2 = DaemonTaskResultCode.SUCCESS;
        
        // Then
        assertThat(exception1).isSameAs(exception2);
        assertThat(success1).isSameAs(success2);
        assertThat(exception1).isNotSameAs(success1);
    }

    @Test
    @DisplayName("测试枚举比较")
    void testEnumComparison() {
        // When
        DaemonTaskResultCode exception = DaemonTaskResultCode.EXCEPTION;
        DaemonTaskResultCode success = DaemonTaskResultCode.SUCCESS;
        
        // Then
        assertThat(exception).isEqualTo(DaemonTaskResultCode.EXCEPTION);
        assertThat(success).isEqualTo(DaemonTaskResultCode.SUCCESS);
        assertThat(exception).isNotEqualTo(success);
    }

    @Test
    @DisplayName("测试枚举序号")
    void testEnumOrdinal() {
        // When & Then
        assertThat(DaemonTaskResultCode.EXCEPTION.ordinal()).isEqualTo(0);
        assertThat(DaemonTaskResultCode.SUCCESS.ordinal()).isEqualTo(1);
    }

    @Test
    @DisplayName("测试枚举在switch语句中的使用")
    void testEnumInSwitch() {
        // Given
        DaemonTaskResultCode exception = DaemonTaskResultCode.EXCEPTION;
        DaemonTaskResultCode success = DaemonTaskResultCode.SUCCESS;
        
        // When & Then
        String exceptionResult = getResultDescription(exception);
        String successResult = getResultDescription(success);
        
        assertThat(exceptionResult).isEqualTo("任务执行异常");
        assertThat(successResult).isEqualTo("任务执行成功");
    }

    @Test
    @DisplayName("测试枚举的hashCode和equals")
    void testHashCodeAndEquals() {
        // Given
        DaemonTaskResultCode exception1 = DaemonTaskResultCode.EXCEPTION;
        DaemonTaskResultCode exception2 = DaemonTaskResultCode.EXCEPTION;
        DaemonTaskResultCode success = DaemonTaskResultCode.SUCCESS;
        
        // Then
        assertThat(exception1.hashCode()).isEqualTo(exception2.hashCode());
        assertThat(exception1.hashCode()).isNotEqualTo(success.hashCode());
        assertThat(exception1.equals(exception2)).isTrue();
        assertThat(exception1.equals(success)).isFalse();
    }

    @Test
    @DisplayName("测试枚举的name方法")
    void testEnumName() {
        // When & Then
        assertThat(DaemonTaskResultCode.EXCEPTION.name()).isEqualTo("EXCEPTION");
        assertThat(DaemonTaskResultCode.SUCCESS.name()).isEqualTo("SUCCESS");
    }

    @Test
    @DisplayName("测试枚举的Comparable接口")
    void testEnumComparable() {
        // Given
        DaemonTaskResultCode exception = DaemonTaskResultCode.EXCEPTION;
        DaemonTaskResultCode success = DaemonTaskResultCode.SUCCESS;
        
        // When & Then
        assertThat(exception.compareTo(success)).isLessThan(0);
        assertThat(success.compareTo(exception)).isGreaterThan(0);
        assertThat(exception.compareTo(exception)).isEqualTo(0);
    }

    /**
     * 辅助方法：根据结果代码获取描述
     */
    private String getResultDescription(DaemonTaskResultCode resultCode) {
        switch (resultCode) {
            case EXCEPTION:
                return "任务执行异常";
            case SUCCESS:
                return "任务执行成功";
            default:
                return "未知状态";
        }
    }
}
