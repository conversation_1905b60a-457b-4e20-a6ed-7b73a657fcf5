package com.uaepay.cmf.common.core.engine.cache.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.redis.core.RedisTemplate;


import java.time.Duration;

import static org.assertj.core.api.Assertions.*;

/**
 * CacheLockServiceImpl简单测试
 * 不使用Mockito，通过简单测试验证基本功能
 */
class CacheLockServiceImplTest {

    private CacheLockServiceImpl cacheLockService;

    @BeforeEach
    void setUp() {
        cacheLockService = new CacheLockServiceImpl();
    }

    @Test
    @DisplayName("测试lock方法-无RedisTemplate时返回false")
    void testLock_NoRedisTemplate() {
        // Given
        String lockKey = "test_lock_key";
        Duration lockTime = Duration.ofSeconds(30);

        // When
        boolean result = cacheLockService.lock(lockKey, lockTime);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试unlock方法-无RedisTemplate时不抛异常")
    void testUnlock_NoRedisTemplate() {
        // Given
        String lockKey = "test_lock_key";

        // When & Then - 应该不抛异常
        assertThatCode(() -> cacheLockService.unlock(lockKey)).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试lock方法-不同的锁时间")
    void testLock_DifferentLockTimes() {
        // Given
        String lockKey = "test_lock_key";
        Duration shortTime = Duration.ofSeconds(10);
        Duration longTime = Duration.ofMinutes(5);

        // When
        boolean result1 = cacheLockService.lock(lockKey, shortTime);
        boolean result2 = cacheLockService.lock(lockKey, longTime);

        // Then
        assertThat(result1).isFalse();
        assertThat(result2).isFalse();
    }

    @Test
    @DisplayName("测试lock方法-不同的锁键")
    void testLock_DifferentLockKeys() {
        // Given
        String lockKey1 = "lock_key_1";
        String lockKey2 = "lock_key_2";
        Duration lockTime = Duration.ofSeconds(30);

        // When
        boolean result1 = cacheLockService.lock(lockKey1, lockTime);
        boolean result2 = cacheLockService.lock(lockKey2, lockTime);

        // Then
        assertThat(result1).isFalse();
        assertThat(result2).isFalse();
    }

    @Test
    @DisplayName("测试unlock方法-不同的锁键")
    void testUnlock_DifferentLockKeys() {
        // Given
        String lockKey1 = "lock_key_1";
        String lockKey2 = "lock_key_2";

        // When & Then - 应该不抛异常
        assertThatCode(() -> cacheLockService.unlock(lockKey1)).doesNotThrowAnyException();
        assertThatCode(() -> cacheLockService.unlock(lockKey2)).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试类注解")
    void testClassAnnotations() {
        // When
        Class<?> clazz = cacheLockService.getClass();

        // Then
        assertThat(clazz.isAnnotationPresent(org.springframework.stereotype.Service.class)).isTrue();

        org.springframework.stereotype.Service serviceAnnotation =
            clazz.getAnnotation(org.springframework.stereotype.Service.class);
        assertThat(serviceAnnotation.value()).isEqualTo("cacheLockService");
    }

    @Test
    @DisplayName("测试字段注解")
    void testFieldAnnotations() throws NoSuchFieldException {
        // When
        Class<?> clazz = cacheLockService.getClass();

        // Then - 验证redisTemplate字段有@Resource注解
        assertThat(clazz.getDeclaredField("redisTemplate")
                .isAnnotationPresent(javax.annotation.Resource.class)).isTrue();
    }

    @Test
    @DisplayName("测试接口实现")
    void testInterfaceImplementation() {
        // When
        Class<?> clazz = cacheLockService.getClass();

        // Then
        assertThat(clazz.getInterfaces()).hasSize(1);
        assertThat(clazz.getInterfaces()[0].getSimpleName()).isEqualTo("CacheLockService");
    }

    @Test
    @DisplayName("测试方法存在性")
    void testMethodsExistence() throws NoSuchMethodException {
        // When
        Class<?> clazz = cacheLockService.getClass();

        // Then
        assertThat(clazz.getMethod("lock", String.class, Duration.class)).isNotNull();
        assertThat(clazz.getMethod("unlock", String.class)).isNotNull();
    }

    @Test
    @DisplayName("测试方法返回类型")
    void testMethodReturnTypes() throws NoSuchMethodException {
        // When
        Class<?> clazz = cacheLockService.getClass();

        // Then
        assertThat(clazz.getMethod("lock", String.class, Duration.class).getReturnType())
                .isEqualTo(boolean.class);
        assertThat(clazz.getMethod("unlock", String.class).getReturnType())
                .isEqualTo(void.class);
    }

    @Test
    @DisplayName("测试字段类型")
    void testFieldTypes() throws NoSuchFieldException {
        // When
        Class<?> clazz = cacheLockService.getClass();

        // Then
        assertThat(clazz.getDeclaredField("redisTemplate").getType())
                .isEqualTo(RedisTemplate.class);
    }

    @Test
    @DisplayName("测试边界情况-空字符串锁键")
    void testLock_EmptyLockKey() {
        // Given
        String lockKey = "";
        Duration lockTime = Duration.ofSeconds(30);

        // When
        boolean result = cacheLockService.lock(lockKey, lockTime);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试边界情况-零秒锁时间")
    void testLock_ZeroDurationLockTime() {
        // Given
        String lockKey = "test_lock_key";
        Duration lockTime = Duration.ZERO;

        // When
        boolean result = cacheLockService.lock(lockKey, lockTime);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("测试完整的锁获取和释放流程")
    void testLockAndUnlockFlow() {
        // Given
        String lockKey = "test_lock_key";
        Duration lockTime = Duration.ofSeconds(30);

        // When
        boolean lockResult = cacheLockService.lock(lockKey, lockTime);
        cacheLockService.unlock(lockKey);

        // Then
        assertThat(lockResult).isFalse();
    }

    @Test
    @DisplayName("测试多次获取同一个锁")
    void testMultipleLockAttempts() {
        // Given
        String lockKey = "test_lock_key";
        Duration lockTime = Duration.ofSeconds(30);

        // When
        boolean result1 = cacheLockService.lock(lockKey, lockTime);
        boolean result2 = cacheLockService.lock(lockKey, lockTime);
        boolean result3 = cacheLockService.lock(lockKey, lockTime);

        // Then
        assertThat(result1).isFalse();
        assertThat(result2).isFalse();
        assertThat(result3).isFalse();
    }

    @Test
    @DisplayName("测试类的可见性")
    void testClassVisibility() {
        // When
        Class<?> clazz = cacheLockService.getClass();

        // Then
        assertThat(java.lang.reflect.Modifier.isPublic(clazz.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isFinal(clazz.getModifiers())).isFalse();
        assertThat(java.lang.reflect.Modifier.isAbstract(clazz.getModifiers())).isFalse();
    }

    @Test
    @DisplayName("测试toString方法")
    void testToStringMethod() {
        // When
        String result = cacheLockService.toString();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("CacheLockServiceImpl");
    }

    @Test
    @DisplayName("测试equals和hashCode方法")
    void testEqualsAndHashCode() {
        // Given
        CacheLockServiceImpl service1 = new CacheLockServiceImpl();
        CacheLockServiceImpl service2 = new CacheLockServiceImpl();

        // When & Then
        assertThat(service1).isNotEqualTo(service2); // 不同实例
        assertThat(service1.hashCode()).isNotEqualTo(service2.hashCode());
        assertThat(service1).isEqualTo(service1); // 自己等于自己
    }
}
