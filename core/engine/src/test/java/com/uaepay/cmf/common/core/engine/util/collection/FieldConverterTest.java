package com.uaepay.cmf.common.core.engine.util.collection;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.*;

/**
 * FieldConverter接口单元测试
 * 测试字段转换器接口的功能
 */
class FieldConverterTest {

    // 测试用的实现类
    private static class TestFieldConverter implements FieldConverter {
        @Override
        public BigDecimal convert(Object source) {
            if (source == null) {
                return BigDecimal.ZERO;
            }
            if (source instanceof Number) {
                return new BigDecimal(source.toString());
            }
            if (source instanceof String) {
                try {
                    return new BigDecimal((String) source);
                } catch (NumberFormatException e) {
                    return BigDecimal.ZERO;
                }
            }
            return BigDecimal.ZERO;
        }
    }

    // 另一个测试实现类
    private static class AlwaysOneConverter implements FieldConverter {
        @Override
        public BigDecimal convert(Object source) {
            return BigDecimal.ONE;
        }
    }

    // 抛出异常的实现类
    private static class ExceptionConverter implements FieldConverter {
        @Override
        public BigDecimal convert(Object source) {
            throw new RuntimeException("转换异常");
        }
    }

    @Test
    @DisplayName("测试接口基本功能")
    void testInterfaceBasicFunctionality() {
        // Given
        FieldConverter converter = new TestFieldConverter();

        // When & Then
        assertThat(converter).isNotNull();
        assertThat(converter).isInstanceOf(FieldConverter.class);
    }

    @Test
    @DisplayName("测试convert方法-数字转换")
    void testConvert_NumberConversion() {
        // Given
        FieldConverter converter = new TestFieldConverter();

        // When & Then
        assertThat(converter.convert(100)).isEqualTo(new BigDecimal("100"));
        assertThat(converter.convert(100L)).isEqualTo(new BigDecimal("100"));
        assertThat(converter.convert(100.5f)).isEqualTo(new BigDecimal("100.5"));
        assertThat(converter.convert(100.5d)).isEqualTo(new BigDecimal("100.5"));
        assertThat(converter.convert(new BigDecimal("123.45"))).isEqualTo(new BigDecimal("123.45"));
    }

    @Test
    @DisplayName("测试convert方法-字符串转换")
    void testConvert_StringConversion() {
        // Given
        FieldConverter converter = new TestFieldConverter();

        // When & Then
        assertThat(converter.convert("100")).isEqualTo(new BigDecimal("100"));
        assertThat(converter.convert("100.5")).isEqualTo(new BigDecimal("100.5"));
        assertThat(converter.convert("0")).isEqualTo(new BigDecimal("0"));
        assertThat(converter.convert("-100")).isEqualTo(new BigDecimal("-100"));
    }

    @Test
    @DisplayName("测试convert方法-null值处理")
    void testConvert_NullValue() {
        // Given
        FieldConverter converter = new TestFieldConverter();

        // When
        BigDecimal result = converter.convert(null);

        // Then
        assertThat(result).isEqualTo(BigDecimal.ZERO);
    }

    @Test
    @DisplayName("测试convert方法-无效字符串")
    void testConvert_InvalidString() {
        // Given
        FieldConverter converter = new TestFieldConverter();

        // When & Then
        assertThat(converter.convert("invalid")).isEqualTo(BigDecimal.ZERO);
        assertThat(converter.convert("")).isEqualTo(BigDecimal.ZERO);
        assertThat(converter.convert("abc123")).isEqualTo(BigDecimal.ZERO);
    }

    @Test
    @DisplayName("测试convert方法-其他类型对象")
    void testConvert_OtherObjectTypes() {
        // Given
        FieldConverter converter = new TestFieldConverter();

        // When & Then
        assertThat(converter.convert(new Object())).isEqualTo(BigDecimal.ZERO);
        assertThat(converter.convert(true)).isEqualTo(BigDecimal.ZERO);
        assertThat(converter.convert(new java.util.Date())).isEqualTo(BigDecimal.ZERO);
    }

    @Test
    @DisplayName("测试不同的实现类")
    void testDifferentImplementations() {
        // Given
        FieldConverter converter1 = new TestFieldConverter();
        FieldConverter converter2 = new AlwaysOneConverter();

        // When & Then
        assertThat(converter1.convert(100)).isEqualTo(new BigDecimal("100"));
        assertThat(converter2.convert(100)).isEqualTo(BigDecimal.ONE);
        
        assertThat(converter1.convert("200")).isEqualTo(new BigDecimal("200"));
        assertThat(converter2.convert("200")).isEqualTo(BigDecimal.ONE);
        
        assertThat(converter1.convert(null)).isEqualTo(BigDecimal.ZERO);
        assertThat(converter2.convert(null)).isEqualTo(BigDecimal.ONE);
    }

    @Test
    @DisplayName("测试异常处理实现")
    void testExceptionHandlingImplementation() {
        // Given
        FieldConverter converter = new ExceptionConverter();

        // When & Then
        assertThatThrownBy(() -> converter.convert("test"))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("转换异常");
    }

    @Test
    @DisplayName("测试接口方法签名")
    void testInterfaceMethodSignature() throws NoSuchMethodException {
        // When
        Class<FieldConverter> clazz = FieldConverter.class;
        java.lang.reflect.Method convertMethod = clazz.getMethod("convert", Object.class);

        // Then
        assertThat(convertMethod).isNotNull();
        assertThat(convertMethod.getReturnType()).isEqualTo(BigDecimal.class);
        assertThat(convertMethod.getParameterTypes()).hasSize(1);
        assertThat(convertMethod.getParameterTypes()[0]).isEqualTo(Object.class);
    }

    @Test
    @DisplayName("测试接口是否为函数式接口")
    void testFunctionalInterface() {
        // When
        Class<FieldConverter> clazz = FieldConverter.class;

        // Then
        assertThat(clazz.isInterface()).isTrue();
        assertThat(clazz.getMethods()).hasSize(1); // 只有一个抽象方法
    }

    @Test
    @DisplayName("测试Lambda表达式实现")
    void testLambdaImplementation() {
        // Given
        FieldConverter converter = source -> {
            if (source instanceof Integer) {
                return new BigDecimal((Integer) source * 2);
            }
            return BigDecimal.ZERO;
        };

        // When & Then
        assertThat(converter.convert(5)).isEqualTo(new BigDecimal("10"));
        assertThat(converter.convert("5")).isEqualTo(BigDecimal.ZERO);
        assertThat(converter.convert(null)).isEqualTo(BigDecimal.ZERO);
    }

    @Test
    @DisplayName("测试方法引用实现")
    void testMethodReferenceImplementation() {
        // Given
        FieldConverter converter = this::customConvert;

        // When & Then
        assertThat(converter.convert(100)).isEqualTo(new BigDecimal("100"));
        assertThat(converter.convert(null)).isEqualTo(BigDecimal.ZERO);
    }

    private BigDecimal customConvert(Object source) {
        return source != null ? new BigDecimal(source.toString()) : BigDecimal.ZERO;
    }

    @Test
    @DisplayName("测试边界值转换")
    void testBoundaryValueConversion() {
        // Given
        FieldConverter converter = new TestFieldConverter();

        // When & Then
        assertThat(converter.convert(Integer.MAX_VALUE)).isEqualTo(new BigDecimal(Integer.MAX_VALUE));
        assertThat(converter.convert(Integer.MIN_VALUE)).isEqualTo(new BigDecimal(Integer.MIN_VALUE));
        assertThat(converter.convert(Long.MAX_VALUE)).isEqualTo(new BigDecimal(Long.MAX_VALUE));
        assertThat(converter.convert(Long.MIN_VALUE)).isEqualTo(new BigDecimal(Long.MIN_VALUE));
        // 对于Double.MAX_VALUE和MIN_VALUE，使用字符串比较避免精度问题
        assertThat(converter.convert(Double.MAX_VALUE).toString()).contains("1.79769");
        assertThat(converter.convert(Double.MIN_VALUE).toString()).contains("4.9E-324");
    }

    @Test
    @DisplayName("测试精度保持")
    void testPrecisionMaintenance() {
        // Given
        FieldConverter converter = new TestFieldConverter();

        // When & Then
        assertThat(converter.convert("123.456789")).isEqualTo(new BigDecimal("123.456789"));
        assertThat(converter.convert("0.000001")).isEqualTo(new BigDecimal("0.000001"));
        assertThat(converter.convert("999999999.999999999")).isEqualTo(new BigDecimal("999999999.999999999"));
    }

    @Test
    @DisplayName("测试科学计数法")
    void testScientificNotation() {
        // Given
        FieldConverter converter = new TestFieldConverter();

        // When & Then
        assertThat(converter.convert("1E+2")).isEqualTo(new BigDecimal("1E+2"));
        assertThat(converter.convert("1.23E-4")).isEqualTo(new BigDecimal("1.23E-4"));
    }

    @Test
    @DisplayName("测试多个实现类的组合使用")
    void testMultipleImplementationsCombined() {
        // Given
        FieldConverter[] converters = {
            new TestFieldConverter(),
            new AlwaysOneConverter(),
            source -> source != null ? new BigDecimal("999") : BigDecimal.ZERO
        };

        Object testValue = 100;

        // When & Then
        assertThat(converters[0].convert(testValue)).isEqualTo(new BigDecimal("100"));
        assertThat(converters[1].convert(testValue)).isEqualTo(BigDecimal.ONE);
        assertThat(converters[2].convert(testValue)).isEqualTo(new BigDecimal("999"));
    }
}
