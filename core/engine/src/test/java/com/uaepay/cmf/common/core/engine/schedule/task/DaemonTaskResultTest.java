package com.uaepay.cmf.common.core.engine.schedule.task;

import com.uaepay.cmf.common.core.engine.BaseCoreEngineTest;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * DaemonTaskResult定时任务执行结果测试
 * 测试定时任务执行结果的所有功能
 */
class DaemonTaskResultTest extends BaseCoreEngineTest {

    @Test
    @DisplayName("测试默认构造函数")
    void testDefaultConstructor() {
        // When
        DaemonTaskResult result = new DaemonTaskResult();
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTaskType()).isNull();
        assertThat(result.getResultCode()).isNull();
        assertThat(result.getTaskResultVO()).isNull();
        assertThat(result.isSuccess()).isFalse(); // BaseResult默认为false
    }

    @Test
    @DisplayName("测试带参数的构造函数")
    void testParameterizedConstructor() {
        // Given
        boolean success = true;
        DaemonTaskType taskType = createTestDaemonTaskType();
        
        // When
        DaemonTaskResult result = new DaemonTaskResult(success, taskType);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTaskType()).isEqualTo(taskType);
        assertThat(result.getResultCode()).isNull(); // 构造函数中没有设置
        assertThat(result.getTaskResultVO()).isNull();
    }

    @Test
    @DisplayName("测试设置和获取taskType")
    void testSetAndGetTaskType() {
        // Given
        DaemonTaskResult result = new DaemonTaskResult();
        DaemonTaskType taskType = new DaemonTaskType("test", "测试任务");
        
        // When
        result.setTaskType(taskType);
        
        // Then
        assertThat(result.getTaskType()).isEqualTo(taskType);
    }

    @Test
    @DisplayName("测试设置和获取resultCode")
    void testSetAndGetResultCode() {
        // Given
        DaemonTaskResult result = new DaemonTaskResult();
        DaemonTaskResultCode resultCode = DaemonTaskResultCode.SUCCESS;
        
        // When
        result.setResultCode(resultCode);
        
        // Then
        assertThat(result.getResultCode()).isEqualTo(resultCode);
    }

    @Test
    @DisplayName("测试设置和获取taskResultVO")
    void testSetAndGetTaskResultVO() {
        // Given
        DaemonTaskResult result = new DaemonTaskResult();
        Object taskResultVO = "测试结果对象";
        
        // When
        result.setTaskResultVO(taskResultVO);
        
        // Then
        assertThat(result.getTaskResultVO()).isEqualTo(taskResultVO);
    }

    @Test
    @DisplayName("测试完整的结果对象设置")
    void testCompleteResultSetup() {
        // Given
        DaemonTaskResult result = new DaemonTaskResult();
        DaemonTaskType taskType = new DaemonTaskType("payment", "支付任务");
        DaemonTaskResultCode resultCode = DaemonTaskResultCode.SUCCESS;
        String taskResultVO = "支付成功";
        
        // When
        result.setTaskType(taskType);
        result.setResultCode(resultCode);
        result.setTaskResultVO(taskResultVO);
        result.setSuccess(true);
        
        // Then
        assertThat(result.getTaskType()).isEqualTo(taskType);
        assertThat(result.getResultCode()).isEqualTo(resultCode);
        assertThat(result.getTaskResultVO()).isEqualTo(taskResultVO);
        assertThat(result.isSuccess()).isTrue();
    }

    @Test
    @DisplayName("测试成功结果的创建")
    void testSuccessResultCreation() {
        // Given
        DaemonTaskType taskType = new DaemonTaskType("common", "通用任务");
        
        // When
        DaemonTaskResult result = new DaemonTaskResult(true, taskType);
        result.setResultCode(DaemonTaskResultCode.SUCCESS);
        result.setTaskResultVO("执行成功");
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTaskType()).isEqualTo(taskType);
        assertThat(result.getResultCode()).isEqualTo(DaemonTaskResultCode.SUCCESS);
        assertThat(result.getTaskResultVO()).isEqualTo("执行成功");
    }

    @Test
    @DisplayName("测试失败结果的创建")
    void testFailureResultCreation() {
        // Given
        DaemonTaskType taskType = new DaemonTaskType("payment", "支付任务");
        
        // When
        DaemonTaskResult result = new DaemonTaskResult(false, taskType);
        result.setResultCode(DaemonTaskResultCode.EXCEPTION);
        result.setTaskResultVO("执行失败：网络超时");
        
        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getTaskType()).isEqualTo(taskType);
        assertThat(result.getResultCode()).isEqualTo(DaemonTaskResultCode.EXCEPTION);
        assertThat(result.getTaskResultVO()).isEqualTo("执行失败：网络超时");
    }

    @Test
    @DisplayName("测试空值处理")
    void testNullValues() {
        // Given
        DaemonTaskResult result = new DaemonTaskResult();
        
        // When
        result.setTaskType(null);
        result.setResultCode(null);
        result.setTaskResultVO(null);
        
        // Then
        assertThat(result.getTaskType()).isNull();
        assertThat(result.getResultCode()).isNull();
        assertThat(result.getTaskResultVO()).isNull();
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // Given
        DaemonTaskResult result = new DaemonTaskResult();
        result.setTaskType(new DaemonTaskType("test", "测试任务"));
        result.setResultCode(DaemonTaskResultCode.SUCCESS);
        result.setSuccess(true);
        
        // When
        String toString = result.toString();
        
        // Then
        assertThat(toString)
            .isNotNull()
            .contains("DaemonTaskResult");
    }

    @Test
    @DisplayName("测试不同类型的taskResultVO")
    void testDifferentTaskResultVOTypes() {
        // Given
        DaemonTaskResult result = new DaemonTaskResult();
        
        // When & Then - 测试字符串类型
        result.setTaskResultVO("字符串结果");
        assertThat(result.getTaskResultVO()).isInstanceOf(String.class);
        
        // When & Then - 测试数字类型
        result.setTaskResultVO(12345);
        assertThat(result.getTaskResultVO()).isInstanceOf(Integer.class);
        
        // When & Then - 测试Map类型
        result.setTaskResultVO(createTestMapData());
        assertThat(result.getTaskResultVO()).isInstanceOf(java.util.Map.class);
    }

    @Test
    @DisplayName("测试继承自BaseResult的属性")
    void testBaseResultProperties() {
        // Given
        DaemonTaskResult result = new DaemonTaskResult();

        // When
        result.setSuccess(true);
        result.setResultMessage("执行成功");

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getResultMessage()).contains("执行成功");
    }

    @Test
    @DisplayName("测试序列化兼容性")
    void testSerializationCompatibility() {
        // Given
        DaemonTaskResult result = new DaemonTaskResult();
        result.setTaskType(new DaemonTaskType("test", "测试"));
        result.setResultCode(DaemonTaskResultCode.SUCCESS);
        
        // When & Then - 验证serialVersionUID存在
        assertThat(DaemonTaskResult.class.getDeclaredFields())
            .extracting("name")
            .contains("serialVersionUID");
    }

    @Test
    @DisplayName("测试构造函数的边界情况")
    void testConstructorBoundaryConditions() {
        // When & Then - 测试null taskType
        assertThatCode(() -> new DaemonTaskResult(true, null))
            .doesNotThrowAnyException();
        
        DaemonTaskResult result = new DaemonTaskResult(true, null);
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getTaskType()).isNull();
    }
}
