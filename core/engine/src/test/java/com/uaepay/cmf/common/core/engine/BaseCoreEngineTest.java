package com.uaepay.cmf.common.core.engine;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import com.uaepay.basis.sequenceutil.IDGen;
import com.uaepay.cmf.common.core.engine.schedule.DaemonContext;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Core Engine模块测试基类
 * 提供通用的测试常量、Mock工具和基础设施
 */
@ExtendWith(MockitoExtension.class)
public abstract class BaseCoreEngineTest {
    
    // ==================== 测试常量 ====================
    
    // 缓存相关常量
    protected static final String TEST_CACHE_KEY = "test_cache_key";
    protected static final String TEST_CACHE_VALUE = "test_cache_value";
    protected static final int TEST_EXPIRE_SECONDS = 3600;
    
    // 锁相关常量
    protected static final String TEST_LOCK_KEY = "test_lock_key";
    protected static final String TEST_LOCK_VALUE = "test_lock_value";
    protected static final long TEST_LOCK_TIMEOUT = 5000L;
    
    // 任务调度相关常量
    protected static final String TEST_TRIGGER_ID = "test_trigger_001";
    protected static final String TEST_TASK_PARAMETER = "test_parameter";
    protected static final String TEST_JOB_NAME = "testJob";
    
    // 序列生成相关常量
    protected static final String TEST_SEQUENCE_NAME = "TEST_SEQ";
    protected static final long TEST_SEQUENCE_VALUE = 123456L;
    protected static final String TEST_DATE_STRING = "20241217";
    
    // 通用测试数据
    protected static final String TEST_APPLICATION_NAME = "cmf-test";
    protected static final String TEST_REDIS_KEY_PREFIX = "cmf:test:";
    
    @BeforeEach
    public void setUp() {
        // 通用setup逻辑
        setupCommonMocks();
    }
    
    /**
     * 设置通用Mock
     */
    protected void setupCommonMocks() {
        // 子类可以重写此方法添加特定的Mock设置
    }
    
    // ==================== 测试数据构建器 ====================
    
    /**
     * 创建测试用的DaemonContext
     */
    protected DaemonContext createTestDaemonContext() {
        DaemonContext context = new DaemonContext();
        context.setTriggerId(TEST_TRIGGER_ID);
        context.setFireTime(new Date());
        context.setTargetIdent(TEST_TASK_PARAMETER);
        return context;
    }
    
    /**
     * 创建测试用的DaemonTaskType
     */
    protected DaemonTaskType createTestDaemonTaskType() {
        return new DaemonTaskType("test", "测试任务");
    }
    
    /**
     * 创建测试用的缓存键
     */
    protected String createTestCacheKey() {
        return TEST_REDIS_KEY_PREFIX + TEST_CACHE_KEY;
    }
    
    /**
     * 创建测试用的Map数据
     */
    protected Map<String, Object> createTestMapData() {
        Map<String, Object> data = new HashMap<>();
        data.put("key1", "value1");
        data.put("key2", 123);
        data.put("key3", true);
        return data;
    }
    
    // ==================== Mock工具方法 ====================
    
    /**
     * 创建Mock的IDGen
     */
    protected IDGen createMockIDGen() {
        IDGen mockIDGen = mock(IDGen.class);
        when(mockIDGen.get(anyString())).thenReturn(TEST_SEQUENCE_VALUE);
        return mockIDGen;
    }
    
    /**
     * 验证缓存操作调用
     */
    protected void verifyCacheOperation(Object mockCache, String methodName, int times) {
        // 子类可以实现具体的验证逻辑
    }
    
    /**
     * 验证异步操作完成
     */
    protected void verifyAsyncOperation(Runnable verification, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        Exception lastException = null;
        
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            try {
                verification.run();
                return; // 验证成功
            } catch (Exception e) {
                lastException = e;
                try {
                    Thread.sleep(100); // 等待100ms后重试
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while waiting for async operation", ie);
                }
            }
        }
        
        throw new AssertionError("Async operation verification failed after " + timeoutMs + "ms", lastException);
    }
    
    // ==================== 断言工具方法 ====================
    
    /**
     * 断言缓存键格式正确
     */
    protected void assertCacheKeyFormat(String cacheKey, String prefix, String key) {
        assertThat(cacheKey)
            .isNotNull()
            .startsWith(prefix)
            .contains(key);
    }
    
    /**
     * 断言序列号格式正确
     */
    protected void assertSequenceFormat(String sequence, String dateString) {
        assertThat(sequence)
            .isNotNull()
            .hasSize(17) // 8位日期 + 9位序列号
            .startsWith(dateString);
    }
    
    /**
     * 断言任务执行结果
     */
    protected void assertTaskResult(Object result, boolean expectedSuccess) {
        assertThat(result).isNotNull();
        // 子类可以添加更具体的断言
    }
}
