package com.uaepay.cmf.common.core.engine.lock;

import com.uaepay.cmf.common.core.engine.lock.enums.LockType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * Lock类单元测试
 * 测试锁域对象的所有功能
 */
class LockTest {

    @Test
    @DisplayName("测试默认构造方法")
    void testDefaultConstructor() {
        // When
        Lock lock = new Lock();

        // Then
        assertThat(lock).isNotNull();
        assertThat(lock.getLockKey()).isNull();
        assertThat(lock.getUnionKeys()).isNull();
        assertThat(lock.getLockName()).isNull();
        assertThat(lock.getLockDescription()).isNull();
        assertThat(lock.getLockType()).isNull();
        assertThat(lock.getLockSecond()).isEqualTo(0L);
    }

    @Test
    @DisplayName("测试带参数的构造方法")
    void testParameterizedConstructor() {
        // Given
        String lockKey = "test_lock_key";
        String lockName = "测试锁";
        LockType lockType = LockType.EXCLUSION;
        long lockSecond = 3600L;

        // When
        Lock lock = new Lock(lockKey, lockName, lockType, lockSecond);

        // Then
        assertThat(lock).isNotNull();
        assertThat(lock.getLockKey()).isEqualTo(lockKey);
        assertThat(lock.getLockName()).isEqualTo(lockName);
        assertThat(lock.getLockType()).isEqualTo(lockType);
        assertThat(lock.getLockSecond()).isEqualTo(lockSecond);
        assertThat(lock.getUnionKeys()).isNull();
        assertThat(lock.getLockDescription()).isNull();
    }

    @Test
    @DisplayName("测试setter和getter方法")
    void testSettersAndGetters() {
        // Given
        Lock lock = new Lock();
        String lockKey = "test_key";
        List<String> unionKeys = Arrays.asList("key1", "key2", "key3");
        String lockName = "测试锁名称";
        String lockDescription = "这是一个测试锁的描述";
        LockType lockType = LockType.SYNCHRON;
        long lockSecond = 1800L;

        // When
        lock.setLockKey(lockKey);
        lock.setUnionKeys(unionKeys);
        lock.setLockName(lockName);
        lock.setLockDescription(lockDescription);
        lock.setLockType(lockType);
        lock.setLockSecond(lockSecond);

        // Then
        assertThat(lock.getLockKey()).isEqualTo(lockKey);
        assertThat(lock.getUnionKeys()).isEqualTo(unionKeys);
        assertThat(lock.getLockName()).isEqualTo(lockName);
        assertThat(lock.getLockDescription()).isEqualTo(lockDescription);
        assertThat(lock.getLockType()).isEqualTo(lockType);
        assertThat(lock.getLockSecond()).isEqualTo(lockSecond);
    }

    @Test
    @DisplayName("测试空值设置")
    void testNullValues() {
        // Given
        Lock lock = new Lock();

        // When
        lock.setLockKey(null);
        lock.setUnionKeys(null);
        lock.setLockName(null);
        lock.setLockDescription(null);
        lock.setLockType(null);
        lock.setLockSecond(0L);

        // Then
        assertThat(lock.getLockKey()).isNull();
        assertThat(lock.getUnionKeys()).isNull();
        assertThat(lock.getLockName()).isNull();
        assertThat(lock.getLockDescription()).isNull();
        assertThat(lock.getLockType()).isNull();
        assertThat(lock.getLockSecond()).isEqualTo(0L);
    }

    @Test
    @DisplayName("测试空字符串设置")
    void testEmptyStringValues() {
        // Given
        Lock lock = new Lock();

        // When
        lock.setLockKey("");
        lock.setLockName("");
        lock.setLockDescription("");

        // Then
        assertThat(lock.getLockKey()).isEmpty();
        assertThat(lock.getLockName()).isEmpty();
        assertThat(lock.getLockDescription()).isEmpty();
    }

    @Test
    @DisplayName("测试负数锁时间")
    void testNegativeLockSecond() {
        // Given
        Lock lock = new Lock();

        // When
        lock.setLockSecond(-100L);

        // Then
        assertThat(lock.getLockSecond()).isEqualTo(-100L);
    }

    @Test
    @DisplayName("测试很大的锁时间")
    void testLargeLockSecond() {
        // Given
        Lock lock = new Lock();
        long largeLockSecond = Long.MAX_VALUE;

        // When
        lock.setLockSecond(largeLockSecond);

        // Then
        assertThat(lock.getLockSecond()).isEqualTo(largeLockSecond);
    }

    @Test
    @DisplayName("测试空的联合键列表")
    void testEmptyUnionKeys() {
        // Given
        Lock lock = new Lock();
        List<String> emptyList = Arrays.asList();

        // When
        lock.setUnionKeys(emptyList);

        // Then
        assertThat(lock.getUnionKeys()).isEmpty();
    }

    @Test
    @DisplayName("测试包含null元素的联合键列表")
    void testUnionKeysWithNullElements() {
        // Given
        Lock lock = new Lock();
        List<String> keysWithNull = Arrays.asList("key1", null, "key3");

        // When
        lock.setUnionKeys(keysWithNull);

        // Then
        assertThat(lock.getUnionKeys()).hasSize(3);
        assertThat(lock.getUnionKeys()).containsExactly("key1", null, "key3");
    }

    @Test
    @DisplayName("测试不同锁类型")
    void testDifferentLockTypes() {
        // Given
        Lock exclusionLock = new Lock();
        Lock synchronLock = new Lock();

        // When
        exclusionLock.setLockType(LockType.EXCLUSION);
        synchronLock.setLockType(LockType.SYNCHRON);

        // Then
        assertThat(exclusionLock.getLockType()).isEqualTo(LockType.EXCLUSION);
        assertThat(synchronLock.getLockType()).isEqualTo(LockType.SYNCHRON);
        assertThat(exclusionLock.getLockType()).isNotEqualTo(synchronLock.getLockType());
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // Given
        Lock lock = new Lock("test_key", "测试锁", LockType.EXCLUSION, 3600L);
        lock.setLockDescription("测试描述");
        lock.setUnionKeys(Arrays.asList("union1", "union2"));

        // When
        String result = lock.toString();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("test_key");
        assertThat(result).contains("测试锁");
        assertThat(result).contains("EXCLUSION");
        assertThat(result).contains("3600");
        assertThat(result).contains("测试描述");
    }

    @Test
    @DisplayName("测试对象相等性")
    void testEquality() {
        // Given
        Lock lock1 = new Lock("key1", "name1", LockType.EXCLUSION, 100L);
        Lock lock2 = new Lock("key1", "name1", LockType.EXCLUSION, 100L);
        Lock lock3 = new Lock("key2", "name2", LockType.SYNCHRON, 200L);

        // When & Then
        assertThat(lock1).isNotEqualTo(lock2); // 不同的对象实例
        assertThat(lock1).isNotEqualTo(lock3); // 不同的属性值
        assertThat(lock1).isNotEqualTo(null);
        assertThat(lock1).isNotEqualTo("not a lock");
    }

    @Test
    @DisplayName("测试hashCode方法")
    void testHashCode() {
        // Given
        Lock lock1 = new Lock("key1", "name1", LockType.EXCLUSION, 100L);
        Lock lock2 = new Lock("key1", "name1", LockType.EXCLUSION, 100L);

        // When & Then
        // 注意：由于Lock类使用了Lombok的@ToString但没有@EqualsAndHashCode，
        // 所以hashCode是基于Object的默认实现
        assertThat(lock1.hashCode()).isNotEqualTo(lock2.hashCode());
    }

    @Test
    @DisplayName("测试构造方法参数边界值")
    void testConstructorBoundaryValues() {
        // Given & When
        Lock lockWithZeroSecond = new Lock("key", "name", LockType.EXCLUSION, 0L);
        Lock lockWithMaxSecond = new Lock("key", "name", LockType.SYNCHRON, Long.MAX_VALUE);
        Lock lockWithMinSecond = new Lock("key", "name", LockType.EXCLUSION, Long.MIN_VALUE);

        // Then
        assertThat(lockWithZeroSecond.getLockSecond()).isEqualTo(0L);
        assertThat(lockWithMaxSecond.getLockSecond()).isEqualTo(Long.MAX_VALUE);
        assertThat(lockWithMinSecond.getLockSecond()).isEqualTo(Long.MIN_VALUE);
    }

    @Test
    @DisplayName("测试构造方法null参数")
    void testConstructorWithNullParameters() {
        // When
        Lock lock = new Lock(null, null, null, 100L);

        // Then
        assertThat(lock.getLockKey()).isNull();
        assertThat(lock.getLockName()).isNull();
        assertThat(lock.getLockType()).isNull();
        assertThat(lock.getLockSecond()).isEqualTo(100L);
    }

    @Test
    @DisplayName("测试字段修改后的状态")
    void testFieldModification() {
        // Given
        Lock lock = new Lock("original_key", "original_name", LockType.EXCLUSION, 100L);

        // When - 修改所有字段
        lock.setLockKey("new_key");
        lock.setLockName("new_name");
        lock.setLockType(LockType.SYNCHRON);
        lock.setLockSecond(200L);
        lock.setLockDescription("new_description");
        lock.setUnionKeys(Arrays.asList("new_union1", "new_union2"));

        // Then
        assertThat(lock.getLockKey()).isEqualTo("new_key");
        assertThat(lock.getLockName()).isEqualTo("new_name");
        assertThat(lock.getLockType()).isEqualTo(LockType.SYNCHRON);
        assertThat(lock.getLockSecond()).isEqualTo(200L);
        assertThat(lock.getLockDescription()).isEqualTo("new_description");
        assertThat(lock.getUnionKeys()).containsExactly("new_union1", "new_union2");
    }

    @Test
    @DisplayName("测试联合键列表的可变性")
    void testUnionKeysListMutability() {
        // Given
        Lock lock = new Lock();
        List<String> originalKeys = Arrays.asList("key1", "key2");
        lock.setUnionKeys(originalKeys);

        // When
        List<String> retrievedKeys = lock.getUnionKeys();

        // Then
        assertThat(retrievedKeys).isSameAs(originalKeys);
        assertThat(retrievedKeys).containsExactly("key1", "key2");
    }
}
