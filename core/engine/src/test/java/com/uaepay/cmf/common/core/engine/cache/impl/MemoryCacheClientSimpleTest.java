package com.uaepay.cmf.common.core.engine.cache.impl;

import com.uaepay.cmf.common.core.domain.enums.CacheType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.redis.core.RedisTemplate;

import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.Set;

import static org.assertj.core.api.Assertions.*;

/**
 * MemoryCacheClient简单测试
 * 不使用Mockito，通过简单测试验证基本功能
 */
class MemoryCacheClientSimpleTest {

    private MemoryCacheClient memoryCacheClient;

    @BeforeEach
    void setUp() {
        memoryCacheClient = new MemoryCacheClient();
    }

    @Test
    @DisplayName("测试类实例化")
    void testClassInstantiation() {
        // When & Then
        assertThat(memoryCacheClient).isNotNull();
        assertThat(memoryCacheClient).isInstanceOf(MemoryCacheClient.class);
    }

    @Test
    @DisplayName("测试类注解")
    void testClassAnnotations() {
        // When
        Class<?> clazz = memoryCacheClient.getClass();

        // Then
        assertThat(clazz.isAnnotationPresent(org.springframework.stereotype.Service.class)).isTrue();
        
        org.springframework.stereotype.Service serviceAnnotation = 
            clazz.getAnnotation(org.springframework.stereotype.Service.class);
        assertThat(serviceAnnotation.value()).isEqualTo("memoryCacheClient");
    }

    @Test
    @DisplayName("测试字段存在性")
    void testFieldsExistence() throws NoSuchFieldException {
        // When
        Class<?> clazz = memoryCacheClient.getClass();

        // Then - 验证字段存在
        assertThat(clazz.getDeclaredField("redisTemplate")).isNotNull();
        assertThat(clazz.getDeclaredField("applicationName")).isNotNull();
    }

    @Test
    @DisplayName("测试字段注解")
    void testFieldAnnotations() throws NoSuchFieldException {
        // When
        Class<?> clazz = memoryCacheClient.getClass();
        
        // Then - 验证redisTemplate字段有@Resource注解
        assertThat(clazz.getDeclaredField("redisTemplate")
                .isAnnotationPresent(javax.annotation.Resource.class)).isTrue();
        
        // 验证applicationName字段有@Value注解
        assertThat(clazz.getDeclaredField("applicationName")
                .isAnnotationPresent(org.springframework.beans.factory.annotation.Value.class)).isTrue();
        
        org.springframework.beans.factory.annotation.Value valueAnnotation = 
            clazz.getDeclaredField("applicationName")
                .getAnnotation(org.springframework.beans.factory.annotation.Value.class);
        assertThat(valueAnnotation.value()).isEqualTo("${spring.application.name}");
    }

    @Test
    @DisplayName("测试get方法在redisTemplate为null时抛异常")
    void testGetMethodWithNullRedisTemplate() {
        // Given - redisTemplate默认为null（没有注入）

        // When & Then - 应该抛出异常
        assertThatThrownBy(() -> memoryCacheClient.get(CacheType.CMF_SYS_CONFIGURATION, "test_key"))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("统一缓存访问异常");
    }

    @Test
    @DisplayName("测试put方法在redisTemplate为null时抛异常")
    void testPutMethodWithNullRedisTemplate() {
        // Given - redisTemplate默认为null（没有注入）

        // When & Then - 应该抛出异常
        assertThatThrownBy(() -> memoryCacheClient.put(CacheType.CMF_SYS_CONFIGURATION, "test_key", "test_value", 300))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("统一缓存访问异常");
    }

    @Test
    @DisplayName("测试remove方法在redisTemplate为null时抛异常")
    void testRemoveMethodWithNullRedisTemplate() {
        // Given - redisTemplate默认为null（没有注入）

        // When & Then - 应该抛出异常
        assertThatThrownBy(() -> memoryCacheClient.remove(CacheType.CMF_SYS_CONFIGURATION, "test_key"))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("delete cache error");
    }

    @Test
    @DisplayName("测试flush方法在redisTemplate为null时抛异常")
    void testFlushMethodWithNullRedisTemplate() {
        // Given - redisTemplate默认为null（没有注入）

        // When & Then - 应该抛出异常
        assertThatThrownBy(() -> memoryCacheClient.flush(CacheType.CMF_SYS_CONFIGURATION))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("clear all error");
    }

    @Test
    @DisplayName("测试removeByKeys方法在redisTemplate为null时抛异常")
    void testRemoveByKeysMethodWithNullRedisTemplate() {
        // Given
        Set<String> keys = new HashSet<>();
        keys.add("key1");
        keys.add("key2");

        // When & Then - 应该抛出异常
        assertThatThrownBy(() -> memoryCacheClient.removeByKeys(CacheType.CMF_SYS_CONFIGURATION, keys))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("remove by keys error");
    }

    @Test
    @DisplayName("测试removeByKeys方法-空keys处理")
    void testRemoveByKeysMethodWithEmptyKeys() {
        // Given
        Set<String> emptyKeys = new HashSet<>();

        // When & Then - 应该不抛异常
        assertThatCode(() -> memoryCacheClient.removeByKeys(CacheType.CMF_SYS_CONFIGURATION, emptyKeys))
                .doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试removeByKeys方法-null keys处理")
    void testRemoveByKeysMethodWithNullKeys() {
        // When & Then - 应该不抛异常
        assertThatCode(() -> memoryCacheClient.removeByKeys(CacheType.CMF_SYS_CONFIGURATION, null))
                .doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试removeByKeys方法-null cacheType处理")
    void testRemoveByKeysMethodWithNullCacheType() {
        // Given
        Set<String> keys = new HashSet<>();
        keys.add("key1");

        // When & Then - 应该不抛异常
        assertThatCode(() -> memoryCacheClient.removeByKeys(null, keys))
                .doesNotThrowAnyException();
    }

    @Test
    @DisplayName("测试所有必需的方法都存在")
    void testAllRequiredMethodsExist() throws NoSuchMethodException {
        // When
        Class<?> clazz = memoryCacheClient.getClass();

        // Then - 验证所有方法都存在
        assertThat(clazz.getMethod("get", CacheType.class, String.class)).isNotNull();
        assertThat(clazz.getMethod("put", CacheType.class, String.class, Object.class, int.class)).isNotNull();
        assertThat(clazz.getMethod("remove", CacheType.class, String.class)).isNotNull();
        assertThat(clazz.getMethod("flush", CacheType.class)).isNotNull();
        assertThat(clazz.getMethod("removeByKeys", CacheType.class, Set.class)).isNotNull();
    }

    @Test
    @DisplayName("测试方法返回类型")
    void testMethodReturnTypes() throws NoSuchMethodException {
        // When
        Class<?> clazz = memoryCacheClient.getClass();

        // Then
        assertThat(clazz.getMethod("get", CacheType.class, String.class).getReturnType())
                .isEqualTo(Object.class);
        assertThat(clazz.getMethod("put", CacheType.class, String.class, Object.class, int.class).getReturnType())
                .isEqualTo(void.class);
        assertThat(clazz.getMethod("remove", CacheType.class, String.class).getReturnType())
                .isEqualTo(Object.class);
        assertThat(clazz.getMethod("flush", CacheType.class).getReturnType())
                .isEqualTo(void.class);
        assertThat(clazz.getMethod("removeByKeys", CacheType.class, Set.class).getReturnType())
                .isEqualTo(void.class);
    }

    @Test
    @DisplayName("测试类实现的接口")
    void testImplementedInterfaces() {
        // When
        Class<?> clazz = memoryCacheClient.getClass();

        // Then
        assertThat(clazz.getInterfaces()).hasSize(1);
        assertThat(clazz.getInterfaces()[0].getSimpleName()).isEqualTo("CacheClient");
    }

    @Test
    @DisplayName("测试字段类型")
    void testFieldTypes() throws NoSuchFieldException {
        // When
        Class<?> clazz = memoryCacheClient.getClass();

        // Then
        assertThat(clazz.getDeclaredField("redisTemplate").getType())
                .isEqualTo(RedisTemplate.class);
        assertThat(clazz.getDeclaredField("applicationName").getType())
                .isEqualTo(String.class);
    }

    @Test
    @DisplayName("测试字段修饰符")
    void testFieldModifiers() throws NoSuchFieldException {
        // When
        Class<?> clazz = memoryCacheClient.getClass();
        Field redisTemplateField = clazz.getDeclaredField("redisTemplate");
        Field applicationNameField = clazz.getDeclaredField("applicationName");

        // Then
        assertThat(java.lang.reflect.Modifier.isPrivate(redisTemplateField.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isPrivate(applicationNameField.getModifiers())).isTrue();
    }

    @Test
    @DisplayName("测试类包信息")
    void testClassPackageInfo() {
        // When
        Class<?> clazz = memoryCacheClient.getClass();

        // Then
        assertThat(clazz.getPackage().getName())
                .isEqualTo("com.uaepay.cmf.common.core.engine.cache.impl");
    }

    @Test
    @DisplayName("测试类继承关系")
    void testClassInheritance() {
        // When
        Class<?> clazz = memoryCacheClient.getClass();

        // Then
        assertThat(clazz.getSuperclass()).isEqualTo(Object.class);
        assertThat(clazz.isInterface()).isFalse();
        assertThat(java.lang.reflect.Modifier.isAbstract(clazz.getModifiers())).isFalse();
    }

    @Test
    @DisplayName("测试类构造函数")
    void testClassConstructors() {
        // When
        Class<?> clazz = memoryCacheClient.getClass();
        java.lang.reflect.Constructor<?>[] constructors = clazz.getConstructors();

        // Then
        assertThat(constructors).hasSize(1);
        assertThat(constructors[0].getParameterCount()).isEqualTo(0);
        assertThat(java.lang.reflect.Modifier.isPublic(constructors[0].getModifiers())).isTrue();
    }

    @Test
    @DisplayName("测试toString方法")
    void testToStringMethod() {
        // When
        String result = memoryCacheClient.toString();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("MemoryCacheClient");
    }

    @Test
    @DisplayName("测试equals和hashCode方法")
    void testEqualsAndHashCode() {
        // Given
        MemoryCacheClient client1 = new MemoryCacheClient();
        MemoryCacheClient client2 = new MemoryCacheClient();

        // When & Then
        assertThat(client1).isNotEqualTo(client2); // 不同实例
        assertThat(client1.hashCode()).isNotEqualTo(client2.hashCode());
        assertThat(client1).isEqualTo(client1); // 自己等于自己
    }

    @Test
    @DisplayName("测试类的可见性")
    void testClassVisibility() {
        // When
        Class<?> clazz = memoryCacheClient.getClass();

        // Then
        assertThat(java.lang.reflect.Modifier.isPublic(clazz.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isFinal(clazz.getModifiers())).isFalse();
    }

    @Test
    @DisplayName("测试类的注解数量")
    void testClassAnnotationCount() {
        // When
        Class<?> clazz = memoryCacheClient.getClass();

        // Then
        assertThat(clazz.getAnnotations()).hasSizeGreaterThanOrEqualTo(1); // @Service (Slf4j在运行时不可见)
        assertThat(clazz.getDeclaredAnnotations()).hasSizeGreaterThanOrEqualTo(1);
    }

    @Test
    @DisplayName("测试方法参数类型")
    void testMethodParameterTypes() throws NoSuchMethodException {
        // When
        Class<?> clazz = memoryCacheClient.getClass();

        // Then
        java.lang.reflect.Method getMethod = clazz.getMethod("get", CacheType.class, String.class);
        assertThat(getMethod.getParameterTypes()).hasSize(2);
        assertThat(getMethod.getParameterTypes()[0]).isEqualTo(CacheType.class);
        assertThat(getMethod.getParameterTypes()[1]).isEqualTo(String.class);

        java.lang.reflect.Method putMethod = clazz.getMethod("put", CacheType.class, String.class, Object.class, int.class);
        assertThat(putMethod.getParameterTypes()).hasSize(4);
        assertThat(putMethod.getParameterTypes()[0]).isEqualTo(CacheType.class);
        assertThat(putMethod.getParameterTypes()[1]).isEqualTo(String.class);
        assertThat(putMethod.getParameterTypes()[2]).isEqualTo(Object.class);
        assertThat(putMethod.getParameterTypes()[3]).isEqualTo(int.class);
    }

    @Test
    @DisplayName("测试不同CacheType的处理")
    void testDifferentCacheTypes() {
        // When & Then - 测试不同的CacheType都会抛出相同的异常
        assertThatThrownBy(() -> memoryCacheClient.get(CacheType.CMF_SYS_CONFIGURATION, "key"))
                .isInstanceOf(RuntimeException.class);
        assertThatThrownBy(() -> memoryCacheClient.get(CacheType.CSC, "key"))
                .isInstanceOf(RuntimeException.class);
        assertThatThrownBy(() -> memoryCacheClient.get(CacheType.FORM, "key"))
                .isInstanceOf(RuntimeException.class);
    }

    @Test
    @DisplayName("测试不同参数值的处理")
    void testDifferentParameterValues() {
        // When & Then - 测试不同的参数值
        assertThatThrownBy(() -> memoryCacheClient.get(CacheType.CMF_SYS_CONFIGURATION, ""))
                .isInstanceOf(RuntimeException.class);
        assertThatThrownBy(() -> memoryCacheClient.get(CacheType.CMF_SYS_CONFIGURATION, "very_long_key_name"))
                .isInstanceOf(RuntimeException.class);
        assertThatThrownBy(() -> memoryCacheClient.put(CacheType.CMF_SYS_CONFIGURATION, "key", "value", 0))
                .isInstanceOf(RuntimeException.class);
        assertThatThrownBy(() -> memoryCacheClient.put(CacheType.CMF_SYS_CONFIGURATION, "key", "value", 3600))
                .isInstanceOf(RuntimeException.class);
    }
}
