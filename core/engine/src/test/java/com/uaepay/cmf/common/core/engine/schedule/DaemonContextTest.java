package com.uaepay.cmf.common.core.engine.schedule;

import com.uaepay.cmf.common.core.engine.BaseCoreEngineTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Date;

import static org.assertj.core.api.Assertions.*;

/**
 * DaemonContext定时任务上下文测试
 * 测试定时任务上下文的所有功能
 */
class DaemonContextTest extends BaseCoreEngineTest {

    @Test
    @DisplayName("测试默认构造函数")
    void testDefaultConstructor() {
        // When
        DaemonContext context = new DaemonContext();
        
        // Then
        assertThat(context).isNotNull();
        assertThat(context.getTriggerId()).isNull();
        assertThat(context.getFireTime()).isNull();
        assertThat(context.getTargetIdent()).isNull();
    }

    @Test
    @DisplayName("测试设置和获取triggerId")
    void testSetAndGetTriggerId() {
        // Given
        DaemonContext context = new DaemonContext();
        String triggerId = "test_trigger_123";
        
        // When
        context.setTriggerId(triggerId);
        
        // Then
        assertThat(context.getTriggerId()).isEqualTo(triggerId);
    }

    @Test
    @DisplayName("测试设置和获取fireTime")
    void testSetAndGetFireTime() {
        // Given
        DaemonContext context = new DaemonContext();
        Date fireTime = new Date();
        
        // When
        context.setFireTime(fireTime);
        
        // Then
        assertThat(context.getFireTime()).isEqualTo(fireTime);
    }

    @Test
    @DisplayName("测试设置和获取targetIdent")
    void testSetAndGetTargetIdent() {
        // Given
        DaemonContext context = new DaemonContext();
        String targetIdent = "test_target_001";
        
        // When
        context.setTargetIdent(targetIdent);
        
        // Then
        assertThat(context.getTargetIdent()).isEqualTo(targetIdent);
    }

    @Test
    @DisplayName("测试完整的上下文设置")
    void testCompleteContextSetup() {
        // Given
        DaemonContext context = new DaemonContext();
        String triggerId = "trigger_001";
        Date fireTime = new Date();
        String targetIdent = "target_001";
        
        // When
        context.setTriggerId(triggerId);
        context.setFireTime(fireTime);
        context.setTargetIdent(targetIdent);
        
        // Then
        assertThat(context.getTriggerId()).isEqualTo(triggerId);
        assertThat(context.getFireTime()).isEqualTo(fireTime);
        assertThat(context.getTargetIdent()).isEqualTo(targetIdent);
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // Given
        DaemonContext context = new DaemonContext();
        context.setTriggerId("test_trigger");
        context.setFireTime(new Date());
        context.setTargetIdent("test_target");
        
        // When
        String toString = context.toString();
        
        // Then
        assertThat(toString)
            .isNotNull()
            .contains("DaemonContext")
            .contains("test_trigger")
            .contains("test_target");
    }

    @Test
    @DisplayName("测试空值处理")
    void testNullValues() {
        // Given
        DaemonContext context = new DaemonContext();
        
        // When
        context.setTriggerId(null);
        context.setFireTime(null);
        context.setTargetIdent(null);
        
        // Then
        assertThat(context.getTriggerId()).isNull();
        assertThat(context.getFireTime()).isNull();
        assertThat(context.getTargetIdent()).isNull();
    }

    @Test
    @DisplayName("测试使用测试基类的工厂方法")
    void testUsingBaseTestFactory() {
        // When
        DaemonContext context = createTestDaemonContext();
        
        // Then
        assertThat(context).isNotNull();
        assertThat(context.getTriggerId()).isEqualTo(TEST_TRIGGER_ID);
        assertThat(context.getFireTime()).isNotNull();
        assertThat(context.getTargetIdent()).isEqualTo(TEST_TASK_PARAMETER);
    }

    @Test
    @DisplayName("测试边界值 - 空字符串")
    void testEmptyStringValues() {
        // Given
        DaemonContext context = new DaemonContext();
        
        // When
        context.setTriggerId("");
        context.setTargetIdent("");
        
        // Then
        assertThat(context.getTriggerId()).isEmpty();
        assertThat(context.getTargetIdent()).isEmpty();
    }

    @Test
    @DisplayName("测试边界值 - 长字符串")
    void testLongStringValues() {
        // Given
        DaemonContext context = new DaemonContext();
        String longString = "a".repeat(1000);
        
        // When
        context.setTriggerId(longString);
        context.setTargetIdent(longString);
        
        // Then
        assertThat(context.getTriggerId()).hasSize(1000);
        assertThat(context.getTargetIdent()).hasSize(1000);
    }

    @Test
    @DisplayName("测试特殊字符处理")
    void testSpecialCharacters() {
        // Given
        DaemonContext context = new DaemonContext();
        String specialChars = "!@#$%^&*()_+-=[]{}|;':\",./<>?中文测试";
        
        // When
        context.setTriggerId(specialChars);
        context.setTargetIdent(specialChars);
        
        // Then
        assertThat(context.getTriggerId()).isEqualTo(specialChars);
        assertThat(context.getTargetIdent()).isEqualTo(specialChars);
    }
}
