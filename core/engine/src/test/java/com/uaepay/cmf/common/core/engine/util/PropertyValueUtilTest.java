package com.uaepay.cmf.common.core.engine.util;

import com.uaepay.cmf.common.core.domain.common.PropertyExtensionMapping;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * PropertyValueUtil工具类单元测试
 * 测试对象属性操作工具类的所有功能
 */
class PropertyValueUtilTest {

    // 测试用的简单对象
    public static class TestObject {
        private String name;
        private Integer age;
        private Boolean active;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }

        public Boolean getActive() {
            return active;
        }

        public void setActive(Boolean active) {
            this.active = active;
        }
    }

    // 测试用的PropertyExtensionMapping实现
    public static class TestPropertyExtensionMapping implements PropertyExtensionMapping {
        private final String extensionKey;
        private final String propertyName;
        private final Class<?> objectClass;

        public TestPropertyExtensionMapping(String extensionKey, String propertyName, Class<?> objectClass) {
            this.extensionKey = extensionKey;
            this.propertyName = propertyName;
            this.objectClass = objectClass;
        }

        @Override
        public String getExtensionKey() {
            return extensionKey;
        }

        @Override
        public String getPropertyName() {
            return propertyName;
        }

        @Override
        public Class<?> getObjectClass() {
            return objectClass;
        }
    }

    @Test
    @DisplayName("测试getValue方法-成功获取字符串属性")
    void testGetValue_StringProperty() {
        // Given
        TestObject obj = new TestObject();
        obj.setName("test_name");
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "name_key", "name", TestObject.class);

        // When
        Object result = PropertyValueUtil.getValue(obj, mapping);

        // Then
        assertThat(result).isEqualTo("test_name");
    }

    @Test
    @DisplayName("测试getValue方法-成功获取Integer属性")
    void testGetValue_IntegerProperty() {
        // Given
        TestObject obj = new TestObject();
        obj.setAge(25);
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "age_key", "age", TestObject.class);

        // When
        Object result = PropertyValueUtil.getValue(obj, mapping);

        // Then
        assertThat(result).isEqualTo(25);
    }

    @Test
    @DisplayName("测试getValue方法-成功获取Boolean属性")
    void testGetValue_BooleanProperty() {
        // Given
        TestObject obj = new TestObject();
        obj.setActive(true);
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "active_key", "active", TestObject.class);

        // When
        Object result = PropertyValueUtil.getValue(obj, mapping);

        // Then
        assertThat(result).isEqualTo(true);
    }

    @Test
    @DisplayName("测试getValue方法-属性为null")
    void testGetValue_NullProperty() {
        // Given
        TestObject obj = new TestObject();
        // name默认为null
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "name_key", "name", TestObject.class);

        // When
        Object result = PropertyValueUtil.getValue(obj, mapping);

        // Then
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试getValue方法-不存在的属性")
    void testGetValue_NonExistentProperty() {
        // Given
        TestObject obj = new TestObject();
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "nonexistent_key", "nonExistent", TestObject.class);

        // When & Then
        assertThatThrownBy(() -> PropertyValueUtil.getValue(obj, mapping))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("GET方法不存在");
    }

    @Test
    @DisplayName("测试setValue方法-成功设置字符串属性")
    void testSetValue_StringProperty() {
        // Given
        TestObject obj = new TestObject();
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "name_key", "name", TestObject.class);

        // When
        PropertyValueUtil.setValue(obj, mapping, "new_name");

        // Then
        assertThat(obj.getName()).isEqualTo("new_name");
    }

    @Test
    @DisplayName("测试setValue方法-成功设置Integer属性")
    void testSetValue_IntegerProperty() {
        // Given
        TestObject obj = new TestObject();
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "age_key", "age", TestObject.class);

        // When
        PropertyValueUtil.setValue(obj, mapping, 30);

        // Then
        assertThat(obj.getAge()).isEqualTo(30);
    }

    @Test
    @DisplayName("测试setValue方法-成功设置Boolean属性")
    void testSetValue_BooleanProperty() {
        // Given
        TestObject obj = new TestObject();
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "active_key", "active", TestObject.class);

        // When
        PropertyValueUtil.setValue(obj, mapping, false);

        // Then
        assertThat(obj.getActive()).isEqualTo(false);
    }

    @Test
    @DisplayName("测试setValue方法-值为null时不设置")
    void testSetValue_NullValue() {
        // Given
        TestObject obj = new TestObject();
        obj.setName("original_name");
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "name_key", "name", TestObject.class);

        // When
        PropertyValueUtil.setValue(obj, mapping, null);

        // Then - 值应该保持不变
        assertThat(obj.getName()).isEqualTo("original_name");
    }

    @Test
    @DisplayName("测试setValue方法-不存在的属性")
    void testSetValue_NonExistentProperty() {
        // Given
        TestObject obj = new TestObject();
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "nonexistent_key", "nonExistent", TestObject.class);

        // When & Then
        assertThatThrownBy(() -> PropertyValueUtil.setValue(obj, mapping, "value"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("nonExistentSET方法不存在");
    }

    @Test
    @DisplayName("测试MethodPrefix枚举-GET")
    void testMethodPrefix_GET() {
        // When
        PropertyValueUtil.MethodPrefix prefix = PropertyValueUtil.MethodPrefix.GET;

        // Then
        assertThat(prefix.getCode()).isEqualTo("get");
    }

    @Test
    @DisplayName("测试MethodPrefix枚举-SET")
    void testMethodPrefix_SET() {
        // When
        PropertyValueUtil.MethodPrefix prefix = PropertyValueUtil.MethodPrefix.SET;

        // Then
        assertThat(prefix.getCode()).isEqualTo("set");
    }

    @Test
    @DisplayName("测试MethodPrefix枚举-所有值")
    void testMethodPrefix_AllValues() {
        // When
        PropertyValueUtil.MethodPrefix[] values = PropertyValueUtil.MethodPrefix.values();

        // Then
        assertThat(values).hasSize(2);
        assertThat(values[0]).isEqualTo(PropertyValueUtil.MethodPrefix.GET);
        assertThat(values[1]).isEqualTo(PropertyValueUtil.MethodPrefix.SET);
    }

    @Test
    @DisplayName("测试MethodPrefix枚举-valueOf")
    void testMethodPrefix_ValueOf() {
        // When & Then
        assertThat(PropertyValueUtil.MethodPrefix.valueOf("GET"))
                .isEqualTo(PropertyValueUtil.MethodPrefix.GET);
        assertThat(PropertyValueUtil.MethodPrefix.valueOf("SET"))
                .isEqualTo(PropertyValueUtil.MethodPrefix.SET);
    }

    @Test
    @DisplayName("测试方法缓存机制")
    void testMethodCaching() {
        // Given
        TestObject obj1 = new TestObject();
        TestObject obj2 = new TestObject();
        obj1.setName("name1");
        obj2.setName("name2");
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "name_key", "name", TestObject.class);

        // When - 多次调用相同的方法
        Object result1 = PropertyValueUtil.getValue(obj1, mapping);
        Object result2 = PropertyValueUtil.getValue(obj2, mapping);

        // Then - 应该都能正常工作（验证缓存机制不影响功能）
        assertThat(result1).isEqualTo("name1");
        assertThat(result2).isEqualTo("name2");
    }

    @Test
    @DisplayName("测试属性名大小写处理")
    void testPropertyNameCaseHandling() {
        // Given - 测试单字符属性名
        TestObject obj = new TestObject();
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "name_key", "name", TestObject.class);

        // When
        PropertyValueUtil.setValue(obj, mapping, "test");
        Object result = PropertyValueUtil.getValue(obj, mapping);

        // Then
        assertThat(result).isEqualTo("test");
    }

    @Test
    @DisplayName("测试不同类型的值设置")
    void testDifferentValueTypes() {
        // Given
        TestObject obj = new TestObject();
        
        PropertyExtensionMapping nameMapping = new TestPropertyExtensionMapping(
            "name_key", "name", TestObject.class);
        PropertyExtensionMapping ageMapping = new TestPropertyExtensionMapping(
            "age_key", "age", TestObject.class);
        PropertyExtensionMapping activeMapping = new TestPropertyExtensionMapping(
            "active_key", "active", TestObject.class);

        // When
        PropertyValueUtil.setValue(obj, nameMapping, "test_name");
        PropertyValueUtil.setValue(obj, ageMapping, 25);
        PropertyValueUtil.setValue(obj, activeMapping, true);

        // Then
        assertThat(PropertyValueUtil.getValue(obj, nameMapping)).isEqualTo("test_name");
        assertThat(PropertyValueUtil.getValue(obj, ageMapping)).isEqualTo(25);
        assertThat(PropertyValueUtil.getValue(obj, activeMapping)).isEqualTo(true);
    }

    @Test
    @DisplayName("测试边界情况-空字符串属性名")
    void testEmptyPropertyName() {
        // Given
        TestObject obj = new TestObject();
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "empty_key", "", TestObject.class);

        // When & Then
        assertThatThrownBy(() -> PropertyValueUtil.getValue(obj, mapping))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("GET方法不存在");
    }

    @Test
    @DisplayName("测试连续的get和set操作")
    void testConsecutiveGetSetOperations() {
        // Given
        TestObject obj = new TestObject();
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "name_key", "name", TestObject.class);

        // When & Then - 连续操作
        PropertyValueUtil.setValue(obj, mapping, "value1");
        assertThat(PropertyValueUtil.getValue(obj, mapping)).isEqualTo("value1");
        
        PropertyValueUtil.setValue(obj, mapping, "value2");
        assertThat(PropertyValueUtil.getValue(obj, mapping)).isEqualTo("value2");
        
        PropertyValueUtil.setValue(obj, mapping, "value3");
        assertThat(PropertyValueUtil.getValue(obj, mapping)).isEqualTo("value3");
    }

    @Test
    @DisplayName("测试getValue异常处理")
    void testGetValue_ExceptionHandling() {
        // Given - 创建一个会抛出异常的对象
        TestObject obj = new TestObject() {
            @Override
            public String getName() {
                throw new RuntimeException("测试异常");
            }
        };
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "name_key", "name", TestObject.class);

        // When & Then
        assertThatThrownBy(() -> PropertyValueUtil.getValue(obj, mapping))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("获取值异常");
    }

    @Test
    @DisplayName("测试setValue异常处理")
    void testSetValue_ExceptionHandling() {
        // Given - 创建一个会抛出异常的对象
        TestObject obj = new TestObject() {
            @Override
            public void setName(String name) {
                throw new RuntimeException("测试异常");
            }
        };
        
        PropertyExtensionMapping mapping = new TestPropertyExtensionMapping(
            "name_key", "name", TestObject.class);

        // When & Then
        assertThatThrownBy(() -> PropertyValueUtil.setValue(obj, mapping, "test"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("设值异常");
    }
}
