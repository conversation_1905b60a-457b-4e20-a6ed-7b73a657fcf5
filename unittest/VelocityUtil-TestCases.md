# VelocityUtil 单元测试用例设计文档

## 1. 测试概述

### 1.1 测试目标
对 `VelocityUtil` 类进行全面的单元测试，确保表达式引擎工具类的所有功能正常工作，包括模板解析、表达式执行、类型转换等核心功能。

### 1.2 被测试类
- **类名**: `com.uaepay.cmf.common.core.engine.util.expression.VelocityUtil`
- **类型**: 静态工具类
- **主要功能**: Velocity模板引擎的封装，提供表达式解析和执行功能

### 1.3 测试范围
- `loadEngine()` - 引擎初始化
- `getString(String, Map<String, Object>)` - 模板字符串处理
- `mergeString(String, Map<String, Object>)` - 字符串合并
- `executeString(String, Map<String, Object>)` - 表达式执行
- `isTrue(String, Map<String, Object>)` - 布尔值判断
- `calWeight(String, Map<String, Object>)` - 权重计算
- `getAmount(String, Map<String, Money>)` - 金额计算
- `warpVariable(String)` - 变量包装

### 1.4 测试环境准备
```
测试数据准备：
- testParams (Map<String, Object>):
  * name: "张三"
  * amount: BigDecimal("100.50")
  * count: 5
  * isValid: true
  * rate: 0.8

- moneyParams (Map<String, Money>):
  * price: Money("100.00", "AED")
  * discount: Money("20.00", "AED")
  * fee: Money("5.50", "AED")

- emptyParams: 空的HashMap

- nullParams: null
```

## 2. loadEngine() 方法测试用例

### 2.1 正常情况测试

| 用例ID | 测试场景 | 测试步骤 | 预期结果 | 优先级 |
|--------|----------|----------|----------|--------|
| LE_001 | 首次调用loadEngine | 1.调用VelocityUtil.loadEngine()<br/>2.检查返回的VelocityEngine实例 | 1.返回非null的VelocityEngine实例<br/>2.引擎配置正确(编码为GBK) | 高 |
| LE_002 | 多次调用loadEngine | 1.调用VelocityUtil.loadEngine()获取engine1<br/>2.再次调用获取engine2<br/>3.比较两个实例 | engine1和engine2是同一个实例(单例模式) | 高 |
| LE_003 | 并发调用loadEngine | 1.创建10个线程同时调用loadEngine()<br/>2.收集所有返回的实例<br/>3.检查实例唯一性 | 所有线程返回同一个实例，线程安全 | 中 |

### 2.2 配置验证测试

| 用例ID | 测试场景 | 测试步骤 | 预期结果 | 优先级 |
|--------|----------|----------|----------|--------|
| LE_004 | 验证引擎编码配置 | 1.获取VelocityEngine实例<br/>2.检查INPUT_ENCODING属性<br/>3.检查OUTPUT_ENCODING属性 | 1.INPUT_ENCODING = "GBK"<br/>2.OUTPUT_ENCODING = "GBK" | 中 |

## 3. getString() 方法测试用例

### 3.1 正常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| GS_001 | 简单变量替换 | "Hello ${name}" | testParams | "Hello 张三" | 高 |
| GS_002 | 数学运算 | "${math.add(10, 20)}" | testParams | "30" | 高 |
| GS_003 | 条件判断-真 | "#if(${isValid})有效#else无效#end" | testParams | "有效" | 高 |
| GS_004 | 条件判断-假 | "#if(${count} < 3)少#else多#end" | testParams | "多" | 高 |
| GS_005 | 复杂表达式 | "${name}的金额是${amount}" | testParams | "张三的金额是100.50" | 中 |
| GS_006 | 数学乘法 | "${math.mul(${count}, 10)}" | testParams | "50" | 中 |
| GS_007 | 多重条件嵌套 | "#if(${isValid})#if(${count} > 3)A#else B#end#else C#end" | testParams | "A" | 中 |
| GS_008 | 循环语句 | "#foreach($i in [1..3])${i}#end" | testParams | "123" | 中 |

### 3.2 边界值测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| GS_B001 | 空模板 | null | testParams | null | 高 |
| GS_B002 | 空白模板 | "" | testParams | "" | 高 |
| GS_B003 | 空格模板 | "   " | testParams | "   " | 中 |
| GS_B004 | 空参数Map | "Hello World" | null | "Hello World" | 高 |
| GS_B005 | 空参数Map | "Hello World" | emptyParams | "Hello World" | 中 |
| GS_B006 | 未定义变量 | "${undefined}" | testParams | "${undefined}" | 高 |
| GS_B007 | 特殊字符模板 | "特殊字符: !@#$%^&*()" | testParams | "特殊字符: !@#$%^&*()" | 中 |
| GS_B008 | 中文模板 | "测试中文：${name}" | testParams | "测试中文：张三" | 中 |
| GS_B009 | 长模板 | 超过1000字符的模板 | testParams | 正确解析的长字符串 | 低 |

### 3.3 异常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| GS_E001 | 语法错误-未闭合if | "#if(${test" | testParams | 抛出ParseException | 高 |
| GS_E002 | 语法错误-未闭合循环 | "#foreach($i in [1..3" | testParams | 抛出ParseException | 高 |
| GS_E003 | 无效方法调用 | "${math.invalidMethod()}" | testParams | 抛出ParseException | 中 |
| GS_E004 | 语法错误-错误变量格式 | "${name" | testParams | 抛出ParseException | 中 |
| GS_E005 | 语法错误-多余的end | "test#end" | testParams | 抛出ParseException | 中 |

## 4. mergeString() 方法测试用例

### 4.1 正常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| MS_001 | 简单字符串合并 | "${name} + ${count}" | testParams | "张三 + 5" | 高 |
| MS_002 | 数学表达式合并 | "${count} * 2" | testParams | "5 * 2" | 高 |
| MS_003 | 多变量合并 | "${name}-${amount}-${isValid}" | testParams | "张三-100.50-true" | 中 |
| MS_004 | 带文字合并 | "用户${name}的数量是${count}个" | testParams | "用户张三的数量是5个" | 中 |

### 4.2 边界值测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| MS_B001 | 空模板 | null | testParams | null | 高 |
| MS_B002 | 空白模板 | "" | testParams | "" | 高 |
| MS_B003 | 无变量模板 | "纯文本" | testParams | "纯文本" | 中 |
| MS_B004 | 空参数 | "${name}" | null | "${name}" | 高 |

### 4.3 异常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| MS_E001 | 语法错误 | "${name" | testParams | 抛出ParseException | 高 |

## 5. executeString() 方法测试用例

### 5.1 正常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| ES_001 | 包含#if的条件模板 | "#if(${isValid})true#end" | testParams | "true" | 高 |
| ES_002 | 不包含#if的合并模板 | "${name} + ${count}" | testParams | "张三 + 5" | 高 |
| ES_003 | 复杂条件逻辑 | "#if(${count} > 3)大于3#else小于等于3#end" | testParams | "大于3" | 高 |
| ES_004 | 多层条件嵌套 | "#if(${isValid})#if(${count} > 3)A#else B#end#else C#end" | testParams | "A" | 中 |
| ES_005 | 包含#if的复杂模板 | "#if(${isValid})${name}有效#else无效#end" | testParams | "张三有效" | 中 |

### 5.2 边界值测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| ES_B001 | 空模板 | null | testParams | null | 高 |
| ES_B002 | 仅包含#if但无完整语法 | "#if" | testParams | "#if" (按mergeString处理) | 中 |
| ES_B003 | 空的if条件 | "#if()真#else假#end" | testParams | 根据条件结果 | 中 |

### 5.3 异常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| ES_E001 | if语法错误 | "#if(${test" | testParams | 抛出ParseException | 高 |

## 6. isTrue() 方法测试用例

### 6.1 正常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| IT_001 | 返回true的变量 | "${isValid}" | testParams | true | 高 |
| IT_002 | 返回false的条件 | "#if(${count} < 3)true#else false#end" | testParams | false | 高 |
| IT_003 | 直接true字符串 | "true" | testParams | true | 高 |
| IT_004 | 直接false字符串 | "false" | testParams | false | 高 |
| IT_005 | 比较运算结果 | "#if(${count} > 3)true#else false#end" | testParams | true | 中 |
| IT_006 | 逻辑运算 | "#if(${isValid} && ${count} > 0)true#else false#end" | testParams | true | 中 |

### 6.2 边界值测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| IT_B001 | 空字符串结果 | "" | testParams | false | 高 |
| IT_B002 | 包含空格的true | " true " | testParams | true | 高 |
| IT_B003 | 大小写TRUE | "TRUE" | testParams | false | 中 |
| IT_B004 | 大小写False | "False" | testParams | false | 中 |
| IT_B005 | 非布尔字符串 | "yes" | testParams | false | 中 |
| IT_B006 | 数字字符串 | "1" | testParams | false | 中 |
| IT_B007 | 空模板 | null | testParams | false | 高 |

### 6.3 异常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| IT_E001 | 模板解析异常 | "#if(${test" | testParams | 抛出ParseException | 高 |

## 7. calWeight() 方法测试用例

### 7.1 正常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| CW_001 | 简单正整数 | "100" | testParams | 100 | 高 |
| CW_002 | 数学加法运算 | "${math.add(50, 30)}" | testParams | 80 | 高 |
| CW_003 | 变量运算 | "${count} * 10" | testParams | "50" -> 50 | 高 |
| CW_004 | 条件运算 | "#if(${isValid})100#else 0#end" | testParams | 100 | 高 |
| CW_005 | 数学减法 | "${math.sub(100, 20)}" | testParams | 80 | 中 |
| CW_006 | 复杂数学表达式 | "${math.mul(${count}, ${count})}" | testParams | 25 | 中 |

### 7.2 边界值测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| CW_B001 | 负数 | "-50" | testParams | -50 | 高 |
| CW_B002 | 零 | "0" | testParams | 0 | 高 |
| CW_B003 | 最大整数 | "2147483647" | testParams | 2147483647 | 中 |
| CW_B004 | 最小整数 | "-2147483648" | testParams | -2147483648 | 中 |
| CW_B005 | 包含空格的数字 | " 100 " | testParams | 100 | 中 |
| CW_B006 | 前导零 | "0100" | testParams | 100 | 低 |

### 7.3 异常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| CW_E001 | 非数字字符串 | "abc" | testParams | 抛出NumberFormatException | 高 |
| CW_E002 | 小数 | "100.5" | testParams | 抛出NumberFormatException | 高 |
| CW_E003 | 超出整数范围 | "9999999999999999999" | testParams | 抛出NumberFormatException | 中 |
| CW_E004 | 特殊字符 | "100a" | testParams | 抛出NumberFormatException | 中 |
| CW_E005 | 空字符串 | "" | testParams | 抛出NumberFormatException | 中 |
| CW_E006 | 模板解析异常 | "#if(${test" | testParams | 抛出ParseException | 高 |

## 8. getAmount() 方法测试用例

### 8.1 正常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| GA_001 | 简单金额相加 | "${price.amount} + ${discount.amount}" | moneyParams | Money("120.00", "AED") | 高 |
| GA_002 | 单个金额 | "${price.amount}" | moneyParams | Money("100.00", "AED") | 高 |
| GA_003 | 金额相减 | "${price.amount} - ${discount.amount}" | moneyParams | Money("80.00", "AED") | 高 |
| GA_004 | 金额乘法 | "${price.amount} * 2" | moneyParams | Money("200.00", "AED") | 高 |
| GA_005 | 金额除法 | "${price.amount} / 2" | moneyParams | Money("50.00", "AED") | 中 |
| GA_006 | 复杂运算 | "(${price.amount} + ${discount.amount}) * 0.5" | moneyParams | Money("60.00", "AED") | 中 |
| GA_007 | 多个金额运算 | "${price.amount} + ${discount.amount} - ${fee.amount}" | moneyParams | Money("114.50", "AED") | 中 |

### 8.2 边界值测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| GA_B001 | 空模板 | null | moneyParams | Money("0.0", "AED") | 高 |
| GA_B002 | 空白模板 | "" | moneyParams | Money("0.0", "AED") | 高 |
| GA_B003 | 空参数Map | "${price.amount}" | null | Money("0.0", "AED") | 高 |
| GA_B004 | 空参数Map | "${price.amount}" | emptyParams | Money("0.0", "AED") | 高 |
| GA_B005 | 零金额结果 | "0" | moneyParams | Money("0.0", "AED") | 中 |
| GA_B006 | 负金额结果 | "${discount.amount} - ${price.amount}" | moneyParams | Money("-80.00", "AED") | 中 |
| GA_B007 | 很小的金额 | "0.01" | moneyParams | Money("0.01", "AED") | 中 |
| GA_B008 | 很大的金额 | "999999999.99" | moneyParams | Money("999999999.99", "AED") | 低 |

### 8.3 异常情况测试

| 用例ID | 测试场景 | 输入模板 | 输入参数 | 预期结果 | 优先级 |
|--------|----------|----------|----------|----------|--------|
| GA_E001 | 非数字结果 | "abc" | moneyParams | 抛出NumberFormatException或返回空金额 | 高 |
| GA_E002 | 模板解析错误 | "#if(${test" | moneyParams | 抛出ParseException | 高 |
| GA_E003 | 除零运算 | "${price.amount} / 0" | moneyParams | 可能的异常或特殊处理 | 中 |

## 9. warpVariable() 方法测试用例

### 9.1 正常情况测试

| 用例ID | 测试场景 | 输入值 | 预期结果 | 优先级 |
|--------|----------|--------|----------|--------|
| WV_001 | 普通变量名 | "name" | "${name}" | 高 |
| WV_002 | 数字变量名 | "var123" | "${var123}" | 中 |
| WV_003 | 下划线变量名 | "user_name" | "${user_name}" | 中 |
| WV_004 | 点号变量名 | "obj.property" | "${obj.property}" | 中 |

### 9.2 边界值测试

| 用例ID | 测试场景 | 输入值 | 预期结果 | 优先级 |
|--------|----------|--------|----------|--------|
| WV_B001 | 空字符串 | "" | "${}" | 高 |
| WV_B002 | 空值 | null | "${null}" | 高 |
| WV_B003 | 空格 | " " | "${ }" | 中 |
| WV_B004 | 特殊字符 | "var@name" | "${var@name}" | 中 |

## 10. 综合集成测试用例

### 10.1 方法组合测试

| 用例ID | 测试场景 | 测试步骤 | 预期结果 | 优先级 |
|--------|----------|----------|----------|--------|
| IT_001 | getString + isTrue组合 | 1.用getString生成布尔字符串<br/>2.用isTrue判断结果 | 组合功能正常 | 中 |
| IT_002 | executeString + calWeight组合 | 1.用executeString生成数字字符串<br/>2.用calWeight解析权重 | 组合功能正常 | 中 |
| IT_003 | warpVariable + getString组合 | 1.用warpVariable包装变量<br/>2.用getString解析模板 | 组合功能正常 | 中 |

### 10.2 性能测试

| 用例ID | 测试场景 | 测试方法 | 预期结果 | 优先级 |
|--------|----------|----------|----------|--------|
| PT_001 | 大量模板解析 | 连续解析1000个简单模板 | 性能在可接受范围内 | 低 |
| PT_002 | 复杂模板解析 | 解析包含多层嵌套的复杂模板 | 性能在可接受范围内 | 低 |
| PT_003 | 引擎初始化性能 | 测量loadEngine()的执行时间 | 初始化时间合理 | 低 |

## 11. 测试数据管理

### 11.1 测试数据文件
```
- valid-templates.txt: 包含各种有效的模板示例
- invalid-templates.txt: 包含各种无效的模板示例
- test-parameters.json: 测试参数的JSON格式数据
- expected-results.json: 预期结果的JSON格式数据
```

### 11.2 测试环境配置
```
- JUnit 5
- Mockito (如需要)
- AssertJ (断言库)
- 测试覆盖率工具: JaCoCo
- 最小覆盖率要求: 90%
```

## 12. 风险和注意事项

### 12.1 已知风险
1. **编码问题**: GBK编码可能导致中文字符处理问题
2. **内存泄漏**: Velocity引擎的单例模式需要注意内存管理
3. **线程安全**: 静态变量的线程安全性需要验证
4. **异常处理**: ParseException的具体信息需要验证

### 12.2 测试注意事项
1. 测试环境需要支持GBK编码
2. 异常测试需要验证异常类型和消息内容
3. 性能测试需要在相对稳定的环境中执行
4. 边界值测试需要覆盖各种特殊字符和格式

## 13. 测试执行计划

### 13.1 测试阶段
1. **第一阶段**: 基本功能测试 (高优先级用例)
2. **第二阶段**: 边界值和异常测试 (中高优先级用例)
3. **第三阶段**: 性能和集成测试 (低优先级用例)

### 13.2 测试完成标准
1. 所有高优先级测试用例通过
2. 代码覆盖率达到90%以上
3. 所有已知缺陷得到修复
4. 性能测试满足要求

---
**文档版本**: 1.0  
**创建日期**: 2024年12月  
**最后更新**: 2024年12月 