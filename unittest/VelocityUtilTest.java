package com.uaepay.cmf.common.core.engine.util.expression;

import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.IntStream;

import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import com.uaepay.cmf.common.core.domain.exception.ParseException;
import com.uaepay.common.util.money.Money;

/**
 * VelocityUtil 单元测试类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@DisplayName("VelocityUtil 工具类测试")
public class VelocityUtilTest {

    // 测试数据
    private Map<String, Object> testParams;
    private Map<String, Money> moneyParams;
    private Map<String, Object> emptyParams;

    @BeforeEach
    void setUp() {
        // 初始化测试参数
        testParams = new HashMap<>();
        testParams.put("name", "张三");
        testParams.put("amount", new BigDecimal("100.50"));
        testParams.put("count", 5);
        testParams.put("isValid", true);
        testParams.put("rate", 0.8);

        // 初始化金额参数
        moneyParams = new HashMap<>();
        moneyParams.put("price", new Money("100.00", "AED"));
        moneyParams.put("discount", new Money("20.00", "AED"));
        moneyParams.put("fee", new Money("5.50", "AED"));

        // 空参数
        emptyParams = new HashMap<>();
    }

    @Nested
    @DisplayName("loadEngine() 方法测试")
    class LoadEngineTest {

        @Test
        @DisplayName("LE_001: 首次调用loadEngine")
        void testFirstCallLoadEngine() {
            // 执行
            VelocityEngine engine = VelocityUtil.loadEngine();

            // 验证
            assertNotNull(engine, "返回的VelocityEngine实例不应为null");
            assertEquals("GBK", engine.getProperty(RuntimeConstants.INPUT_ENCODING));
            assertEquals("GBK", engine.getProperty(RuntimeConstants.OUTPUT_ENCODING));
        }

        @Test
        @DisplayName("LE_002: 多次调用loadEngine")
        void testMultipleCallsLoadEngine() {
            // 执行
            VelocityEngine engine1 = VelocityUtil.loadEngine();
            VelocityEngine engine2 = VelocityUtil.loadEngine();

            // 验证 - 单例模式
            assertSame(engine1, engine2, "多次调用应返回同一个实例");
        }

        @Test
        @DisplayName("LE_003: 并发调用loadEngine")
        @Timeout(5)
        void testConcurrentCallsLoadEngine() throws Exception {
            ExecutorService executor = Executors.newFixedThreadPool(10);

            // 创建10个并发任务
            CompletableFuture<VelocityEngine>[] futures = IntStream.range(0, 10)
                .mapToObj(i -> CompletableFuture.supplyAsync(VelocityUtil::loadEngine, executor))
                .toArray(CompletableFuture[]::new);

            // 等待所有任务完成并收集结果
            VelocityEngine[] engines = new VelocityEngine[10];
            for (int i = 0; i < 10; i++) {
                engines[i] = futures[i].get();
            }

            // 验证所有实例都相同
            VelocityEngine firstEngine = engines[0];
            for (int i = 1; i < 10; i++) {
                assertSame(firstEngine, engines[i], "并发调用应返回同一个实例");
            }

            executor.shutdown();
        }
    }

    @Nested
    @DisplayName("getString() 方法测试")
    class GetStringTest {

        @Nested
        @DisplayName("正常情况测试")
        class NormalCases {

            @Test
            @DisplayName("GS_001: 简单变量替换")
            void testSimpleVariableReplacement() throws ParseException {
                String result = VelocityUtil.getString("Hello ${name}", testParams);
                assertEquals("Hello 张三", result);
            }

            @Test
            @DisplayName("GS_002: 数学运算")
            void testMathOperation() throws ParseException {
                String result = VelocityUtil.getString("${math.add(10, 20)}", testParams);
                assertEquals("30", result);
            }

            @Test
            @DisplayName("GS_003: 条件判断-真")
            void testConditionTrue() throws ParseException {
                String result = VelocityUtil.getString("#if(${isValid})有效#else无效#end", testParams);
                assertEquals("有效", result);
            }

            @Test
            @DisplayName("GS_004: 条件判断-假")
            void testConditionFalse() throws ParseException {
                String result = VelocityUtil.getString("#if(${count} < 3)少#else多#end", testParams);
                assertEquals("多", result);
            }

            @Test
            @DisplayName("GS_005: 复杂表达式")
            void testComplexExpression() throws ParseException {
                String result = VelocityUtil.getString("${name}的金额是${amount}", testParams);
                assertEquals("张三的金额是100.50", result);
            }

            @Test
            @DisplayName("GS_006: 数学乘法")
            void testMathMultiplication() throws ParseException {
                String result = VelocityUtil.getString("${math.mul(${count}, 10)}", testParams);
                assertEquals("50", result);
            }

            @Test
            @DisplayName("GS_007: 多重条件嵌套")
            void testNestedConditions() throws ParseException {
                String result = VelocityUtil.getString("#if(${isValid})#if(${count} > 3)A#else B#end#else C#end", testParams);
                assertEquals("A", result);
            }

            @Test
            @DisplayName("GS_008: 循环语句")
            void testLoopStatement() throws ParseException {
                String result = VelocityUtil.getString("#foreach($i in [1..3])${i}#end", testParams);
                assertEquals("123", result);
            }
        }

        @Nested
        @DisplayName("边界值测试")
        class BoundaryTests {

            @Test
            @DisplayName("GS_B001: 空模板")
            void testNullTemplate() throws ParseException {
                String result = VelocityUtil.getString(null, testParams);
                assertNull(result);
            }

            @Test
            @DisplayName("GS_B002: 空白模板")
            void testEmptyTemplate() throws ParseException {
                String result = VelocityUtil.getString("", testParams);
                assertEquals("", result);
            }

            @Test
            @DisplayName("GS_B003: 空格模板")
            void testSpaceTemplate() throws ParseException {
                String result = VelocityUtil.getString("   ", testParams);
                assertEquals("   ", result);
            }

            @Test
            @DisplayName("GS_B004: 空参数Map")
            void testNullParams() throws ParseException {
                String result = VelocityUtil.getString("Hello World", null);
                assertEquals("Hello World", result);
            }

            @Test
            @DisplayName("GS_B005: 空参数Map")
            void testEmptyParams() throws ParseException {
                String result = VelocityUtil.getString("Hello World", emptyParams);
                assertEquals("Hello World", result);
            }

            @Test
            @DisplayName("GS_B006: 未定义变量")
            void testUndefinedVariable() throws ParseException {
                String result = VelocityUtil.getString("${undefined}", testParams);
                assertEquals("${undefined}", result);
            }

            @Test
            @DisplayName("GS_B007: 特殊字符模板")
            void testSpecialCharacters() throws ParseException {
                String result = VelocityUtil.getString("特殊字符: !@#$%^&*()", testParams);
                assertEquals("特殊字符: !@#$%^&*()", result);
            }

            @Test
            @DisplayName("GS_B008: 中文模板")
            void testChineseTemplate() throws ParseException {
                String result = VelocityUtil.getString("测试中文：${name}", testParams);
                assertEquals("测试中文：张三", result);
            }
        }

        @Nested
        @DisplayName("异常情况测试")
        class ExceptionTests {

            @Test
            @DisplayName("GS_E001: 语法错误-未闭合if")
            void testUnclosedIf() {
                assertThrows(ParseException.class, () -> {
                    VelocityUtil.getString("#if(${test", testParams);
                });
            }

            @Test
            @DisplayName("GS_E002: 语法错误-未闭合循环")
            void testUnclosedLoop() {
                assertThrows(ParseException.class, () -> {
                    VelocityUtil.getString("#foreach($i in [1..3", testParams);
                });
            }

            @Test
            @DisplayName("GS_E004: 语法错误-错误变量格式")
            void testInvalidVariableFormat() {
                assertThrows(ParseException.class, () -> {
                    VelocityUtil.getString("${name", testParams);
                });
            }
        }
    }

    @Nested
    @DisplayName("mergeString() 方法测试")
    class MergeStringTest {

        @Test
        @DisplayName("MS_001: 简单字符串合并")
        void testSimpleStringMerge() throws ParseException {
            String result = VelocityUtil.mergeString("${name} + ${count}", testParams);
            assertEquals("张三 + 5", result);
        }

        @Test
        @DisplayName("MS_002: 数学表达式合并")
        void testMathExpressionMerge() throws ParseException {
            String result = VelocityUtil.mergeString("${count} * 2", testParams);
            assertEquals("5 * 2", result);
        }

        @Test
        @DisplayName("MS_003: 多变量合并")
        void testMultiVariableMerge() throws ParseException {
            String result = VelocityUtil.mergeString("${name}-${amount}-${isValid}", testParams);
            assertEquals("张三-100.50-true", result);
        }

        @Test
        @DisplayName("MS_004: 带文字合并")
        void testTextWithVariablesMerge() throws ParseException {
            String result = VelocityUtil.mergeString("用户${name}的数量是${count}个", testParams);
            assertEquals("用户张三的数量是5个", result);
        }

        @Test
        @DisplayName("MS_B001: 空模板")
        void testNullTemplateMerge() throws ParseException {
            String result = VelocityUtil.mergeString(null, testParams);
            assertNull(result);
        }

        @Test
        @DisplayName("MS_B002: 空白模板")
        void testEmptyTemplateMerge() throws ParseException {
            String result = VelocityUtil.mergeString("", testParams);
            assertEquals("", result);
        }

        @Test
        @DisplayName("MS_E001: 语法错误")
        void testSyntaxErrorMerge() {
            assertThrows(ParseException.class, () -> {
                VelocityUtil.mergeString("${name", testParams);
            });
        }
    }

    @Nested
    @DisplayName("executeString() 方法测试")
    class ExecuteStringTest {

        @Test
        @DisplayName("ES_001: 包含#if的条件模板")
        void testConditionalTemplate() throws ParseException {
            String result = VelocityUtil.executeString("#if(${isValid})true#end", testParams);
            assertEquals("true", result);
        }

        @Test
        @DisplayName("ES_002: 不包含#if的合并模板")
        void testMergeTemplate() throws ParseException {
            String result = VelocityUtil.executeString("${name} + ${count}", testParams);
            assertEquals("张三 + 5", result);
        }

        @Test
        @DisplayName("ES_003: 复杂条件逻辑")
        void testComplexConditionalLogic() throws ParseException {
            String result = VelocityUtil.executeString("#if(${count} > 3)大于3#else小于等于3#end", testParams);
            assertEquals("大于3", result);
        }

        @Test
        @DisplayName("ES_004: 多层条件嵌套")
        void testNestedConditionalLogic() throws ParseException {
            String result = VelocityUtil.executeString("#if(${isValid})#if(${count} > 3)A#else B#end#else C#end", testParams);
            assertEquals("A", result);
        }

        @Test
        @DisplayName("ES_005: 包含#if的复杂模板")
        void testComplexConditionalTemplate() throws ParseException {
            String result = VelocityUtil.executeString("#if(${isValid})${name}有效#else无效#end", testParams);
            assertEquals("张三有效", result);
        }

        @Test
        @DisplayName("ES_B001: 空模板")
        void testNullTemplateExecute() throws ParseException {
            String result = VelocityUtil.executeString(null, testParams);
            assertNull(result);
        }

        @Test
        @DisplayName("ES_E001: if语法错误")
        void testIfSyntaxError() {
            assertThrows(ParseException.class, () -> {
                VelocityUtil.executeString("#if(${test", testParams);
            });
        }
    }

    @Nested
    @DisplayName("isTrue() 方法测试")
    class IsTrueTest {

        @Test
        @DisplayName("IT_001: 返回true的变量")
        void testTrueVariable() throws ParseException {
            boolean result = VelocityUtil.isTrue("${isValid}", testParams);
            assertTrue(result);
        }

        @Test
        @DisplayName("IT_002: 返回false的条件")
        void testFalseCondition() throws ParseException {
            boolean result = VelocityUtil.isTrue("#if(${count} < 3)true#else false#end", testParams);
            assertFalse(result);
        }

        @Test
        @DisplayName("IT_003: 直接true字符串")
        void testDirectTrueString() throws ParseException {
            boolean result = VelocityUtil.isTrue("true", testParams);
            assertTrue(result);
        }

        @Test
        @DisplayName("IT_004: 直接false字符串")
        void testDirectFalseString() throws ParseException {
            boolean result = VelocityUtil.isTrue("false", testParams);
            assertFalse(result);
        }

        @Test
        @DisplayName("IT_005: 比较运算结果")
        void testComparisonResult() throws ParseException {
            boolean result = VelocityUtil.isTrue("#if(${count} > 3)true#else false#end", testParams);
            assertTrue(result);
        }

        @Test
        @DisplayName("IT_006: 逻辑运算")
        void testLogicalOperation() throws ParseException {
            boolean result = VelocityUtil.isTrue("#if(${isValid} && ${count} > 0)true#else false#end", testParams);
            assertTrue(result);
        }

        @Test
        @DisplayName("IT_B001: 空字符串结果")
        void testEmptyStringResult() throws ParseException {
            boolean result = VelocityUtil.isTrue("", testParams);
            assertFalse(result);
        }

        @Test
        @DisplayName("IT_B002: 包含空格的true")
        void testTrueWithSpaces() throws ParseException {
            boolean result = VelocityUtil.isTrue(" true ", testParams);
            assertTrue(result);
        }

        @ParameterizedTest
        @ValueSource(strings = {"TRUE", "False", "yes", "1"})
        @DisplayName("IT_B003-B006: 非标准布尔字符串")
        void testNonStandardBooleanStrings(String input) throws ParseException {
            boolean result = VelocityUtil.isTrue(input, testParams);
            assertFalse(result);
        }

        @Test
        @DisplayName("IT_B007: 空模板")
        void testNullTemplate() throws ParseException {
            boolean result = VelocityUtil.isTrue(null, testParams);
            assertFalse(result);
        }

        @Test
        @DisplayName("IT_E001: 模板解析异常")
        void testTemplateParseException() {
            assertThrows(ParseException.class, () -> {
                VelocityUtil.isTrue("#if(${test", testParams);
            });
        }
    }

    @Nested
    @DisplayName("calWeight() 方法测试")
    class CalWeightTest {

        @Test
        @DisplayName("CW_001: 简单正整数")
        void testSimplePositiveInteger() throws ParseException {
            int result = VelocityUtil.calWeight("100", testParams);
            assertEquals(100, result);
        }

        @Test
        @DisplayName("CW_002: 数学加法运算")
        void testMathAddition() throws ParseException {
            int result = VelocityUtil.calWeight("${math.add(50, 30)}", testParams);
            assertEquals(80, result);
        }

        @Test
        @DisplayName("CW_003: 变量运算")
        void testVariableCalculation() throws ParseException {
            int result = VelocityUtil.calWeight("${count} * 10", testParams);
            assertEquals(50, result);
        }

        @Test
        @DisplayName("CW_004: 条件运算")
        void testConditionalCalculation() throws ParseException {
            int result = VelocityUtil.calWeight("#if(${isValid})100#else 0#end", testParams);
            assertEquals(100, result);
        }

        @Test
        @DisplayName("CW_005: 数学减法")
        void testMathSubtraction() throws ParseException {
            int result = VelocityUtil.calWeight("${math.sub(100, 20)}", testParams);
            assertEquals(80, result);
        }

        @Test
        @DisplayName("CW_006: 复杂数学表达式")
        void testComplexMathExpression() throws ParseException {
            int result = VelocityUtil.calWeight("${math.mul(${count}, ${count})}", testParams);
            assertEquals(25, result);
        }

        @Test
        @DisplayName("CW_B001: 负数")
        void testNegativeNumber() throws ParseException {
            int result = VelocityUtil.calWeight("-50", testParams);
            assertEquals(-50, result);
        }

        @Test
        @DisplayName("CW_B002: 零")
        void testZero() throws ParseException {
            int result = VelocityUtil.calWeight("0", testParams);
            assertEquals(0, result);
        }

        @Test
        @DisplayName("CW_B003: 最大整数")
        void testMaxInteger() throws ParseException {
            int result = VelocityUtil.calWeight("2147483647", testParams);
            assertEquals(2147483647, result);
        }

        @Test
        @DisplayName("CW_B004: 最小整数")
        void testMinInteger() throws ParseException {
            int result = VelocityUtil.calWeight("-2147483648", testParams);
            assertEquals(-2147483648, result);
        }

        @Test
        @DisplayName("CW_B005: 包含空格的数字")
        void testNumberWithSpaces() throws ParseException {
            int result = VelocityUtil.calWeight(" 100 ", testParams);
            assertEquals(100, result);
        }

        @ParameterizedTest
        @ValueSource(strings = {"abc", "100.5", "100a", ""})
        @DisplayName("CW_E001-E005: 无效数字格式")
        void testInvalidNumberFormat(String input) {
            assertThrows(NumberFormatException.class, () -> {
                VelocityUtil.calWeight(input, testParams);
            });
        }

        @Test
        @DisplayName("CW_E003: 超出整数范围")
        void testIntegerOverflow() {
            assertThrows(NumberFormatException.class, () -> {
                VelocityUtil.calWeight("9999999999999999999", testParams);
            });
        }

        @Test
        @DisplayName("CW_E006: 模板解析异常")
        void testTemplateParseException() {
            assertThrows(ParseException.class, () -> {
                VelocityUtil.calWeight("#if(${test", testParams);
            });
        }
    }

    @Nested
    @DisplayName("getAmount() 方法测试")
    class GetAmountTest {

        @Test
        @DisplayName("GA_001: 简单金额相加")
        void testSimpleAmountAddition() throws ParseException {
            Money result = VelocityUtil.getAmount("${price.amount} + ${discount.amount}", moneyParams);
            assertEquals(new Money("120.00", "AED"), result);
        }

        @Test
        @DisplayName("GA_002: 单个金额")
        void testSingleAmount() throws ParseException {
            Money result = VelocityUtil.getAmount("${price.amount}", moneyParams);
            assertEquals(new Money("100.00", "AED"), result);
        }

        @Test
        @DisplayName("GA_003: 金额相减")
        void testAmountSubtraction() throws ParseException {
            Money result = VelocityUtil.getAmount("${price.amount} - ${discount.amount}", moneyParams);
            assertEquals(new Money("80.00", "AED"), result);
        }

        @Test
        @DisplayName("GA_004: 金额乘法")
        void testAmountMultiplication() throws ParseException {
            Money result = VelocityUtil.getAmount("${price.amount} * 2", moneyParams);
            assertEquals(new Money("200.00", "AED"), result);
        }

        @Test
        @DisplayName("GA_005: 金额除法")
        void testAmountDivision() throws ParseException {
            Money result = VelocityUtil.getAmount("${price.amount} / 2", moneyParams);
            assertEquals(new Money("50.00", "AED"), result);
        }

        @Test
        @DisplayName("GA_006: 复杂运算")
        void testComplexCalculation() throws ParseException {
            Money result = VelocityUtil.getAmount("(${price.amount} + ${discount.amount}) * 0.5", moneyParams);
            assertEquals(new Money("60.00", "AED"), result);
        }

        @Test
        @DisplayName("GA_007: 多个金额运算")
        void testMultipleAmountCalculation() throws ParseException {
            Money result = VelocityUtil.getAmount("${price.amount} + ${discount.amount} - ${fee.amount}", moneyParams);
            assertEquals(new Money("114.50", "AED"), result);
        }

        @Test
        @DisplayName("GA_B001: 空模板")
        void testNullTemplate() throws ParseException {
            Money result = VelocityUtil.getAmount(null, moneyParams);
            assertEquals(new Money("0.0", "AED"), result);
        }

        @Test
        @DisplayName("GA_B002: 空白模板")
        void testEmptyTemplate() throws ParseException {
            Money result = VelocityUtil.getAmount("", moneyParams);
            assertEquals(new Money("0.0", "AED"), result);
        }

        @Test
        @DisplayName("GA_B003: 空参数Map")
        void testNullParams() throws ParseException {
            Money result = VelocityUtil.getAmount("${price.amount}", null);
            assertEquals(new Money("0.0", "AED"), result);
        }

        @Test
        @DisplayName("GA_B004: 空参数Map")
        void testEmptyParams() throws ParseException {
            Map<String, Money> emptyMoneyParams = new HashMap<>();
            Money result = VelocityUtil.getAmount("${price.amount}", emptyMoneyParams);
            assertEquals(new Money("0.0", "AED"), result);
        }

        @Test
        @DisplayName("GA_B005: 零金额结果")
        void testZeroAmount() throws ParseException {
            Money result = VelocityUtil.getAmount("0", moneyParams);
            assertEquals(new Money("0.0", "AED"), result);
        }

        @Test
        @DisplayName("GA_B006: 负金额结果")
        void testNegativeAmount() throws ParseException {
            Money result = VelocityUtil.getAmount("${discount.amount} - ${price.amount}", moneyParams);
            assertEquals(new Money("-80.00", "AED"), result);
        }

        @Test
        @DisplayName("GA_B007: 很小的金额")
        void testVerySmallAmount() throws ParseException {
            Money result = VelocityUtil.getAmount("0.01", moneyParams);
            assertEquals(new Money("0.01", "AED"), result);
        }

        @Test
        @DisplayName("GA_E002: 模板解析错误")
        void testTemplateParseError() {
            assertThrows(ParseException.class, () -> {
                VelocityUtil.getAmount("#if(${test", moneyParams);
            });
        }
    }

    @Nested
    @DisplayName("warpVariable() 方法测试")
    class WarpVariableTest {

        @Test
        @DisplayName("WV_001: 普通变量名")
        void testNormalVariableName() {
            String result = VelocityUtil.warpVariable("name");
            assertEquals("${name}", result);
        }

        @Test
        @DisplayName("WV_002: 数字变量名")
        void testNumericVariableName() {
            String result = VelocityUtil.warpVariable("var123");
            assertEquals("${var123}", result);
        }

        @Test
        @DisplayName("WV_003: 下划线变量名")
        void testUnderscoreVariableName() {
            String result = VelocityUtil.warpVariable("user_name");
            assertEquals("${user_name}", result);
        }

        @Test
        @DisplayName("WV_004: 点号变量名")
        void testDotVariableName() {
            String result = VelocityUtil.warpVariable("obj.property");
            assertEquals("${obj.property}", result);
        }

        @Test
        @DisplayName("WV_B001: 空字符串")
        void testEmptyString() {
            String result = VelocityUtil.warpVariable("");
            assertEquals("${}", result);
        }

        @Test
        @DisplayName("WV_B002: 空值")
        void testNullValue() {
            String result = VelocityUtil.warpVariable(null);
            assertEquals("${null}", result);
        }

        @Test
        @DisplayName("WV_B003: 空格")
        void testSpaceValue() {
            String result = VelocityUtil.warpVariable(" ");
            assertEquals("${ }", result);
        }

        @Test
        @DisplayName("WV_B004: 特殊字符")
        void testSpecialCharacters() {
            String result = VelocityUtil.warpVariable("var@name");
            assertEquals("${var@name}", result);
        }
    }

    @Nested
    @DisplayName("综合集成测试")
    class IntegrationTest {

        @Test
        @DisplayName("IT_001: getString + isTrue组合")
        void testGetStringAndIsTrueCombination() throws ParseException {
            // 使用getString生成布尔字符串
            String booleanString = VelocityUtil.getString("#if(${isValid})true#else false#end", testParams);
            
            // 使用isTrue判断结果
            boolean result = VelocityUtil.isTrue(booleanString, testParams);
            
            assertTrue(result);
        }

        @Test
        @DisplayName("IT_002: executeString + calWeight组合")
        void testExecuteStringAndCalWeightCombination() throws ParseException {
            // 使用executeString生成数字字符串
            String numberString = VelocityUtil.executeString("#if(${isValid})${count}#else 0#end", testParams);
            
            // 使用calWeight解析权重
            int weight = VelocityUtil.calWeight(numberString, testParams);
            
            assertEquals(5, weight);
        }

        @Test
        @DisplayName("IT_003: warpVariable + getString组合")
        void testWarpVariableAndGetStringCombination() throws ParseException {
            // 使用warpVariable包装变量
            String wrappedVariable = VelocityUtil.warpVariable("name");
            
            // 使用getString解析模板
            String result = VelocityUtil.getString("Hello " + wrappedVariable, testParams);
            
            assertEquals("Hello 张三", result);
        }
    }

    @Nested
    @DisplayName("性能测试")
    class PerformanceTest {

        @Test
        @DisplayName("PT_001: 大量模板解析")
        @Timeout(10)
        void testMassiveTemplateProcessing() throws ParseException {
            String template = "Hello ${name}, your count is ${count}";
            
            for (int i = 0; i < 1000; i++) {
                String result = VelocityUtil.getString(template, testParams);
                assertEquals("Hello 张三, your count is 5", result);
            }
        }

        @Test
        @DisplayName("PT_002: 复杂模板解析")
        @Timeout(5)
        void testComplexTemplateProcessing() throws ParseException {
            String complexTemplate = "#if(${isValid})" +
                "#if(${count} > 3)" +
                "Hello ${name}, your amount is ${amount}" +
                "#else" +
                "Count too low" +
                "#end" +
                "#else" +
                "Invalid user" +
                "#end";
            
            String result = VelocityUtil.getString(complexTemplate, testParams);
            assertEquals("Hello 张三, your amount is 100.50", result);
        }

        @Test
        @DisplayName("PT_003: 引擎初始化性能")
        @Timeout(2)
        void testEngineInitializationPerformance() {
            long startTime = System.currentTimeMillis();
            VelocityEngine engine = VelocityUtil.loadEngine();
            long endTime = System.currentTimeMillis();
            
            assertNotNull(engine);
            assertTrue((endTime - startTime) < 1000, "引擎初始化时间应小于1秒");
        }
    }
} 