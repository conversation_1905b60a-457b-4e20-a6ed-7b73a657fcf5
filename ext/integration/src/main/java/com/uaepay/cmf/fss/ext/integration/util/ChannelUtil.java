package com.uaepay.cmf.fss.ext.integration.util;

import com.alibaba.fastjson.JSONObject;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ChannelInfoExtKey;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.router.service.facade.domain.channel.ChannelExtVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 渠道工具类.
 * </p>
 *
 * <AUTHOR>
 * @version ChannelUtil.java 1.0 @2015/7/14 19:39 $
 */
public class ChannelUtil {

    private static final String URL_SUFFIX = "paymentOrderNo=";

    private ChannelUtil() {

    }

    public static boolean isManualRefund(FundChannelApiType fundChannelApiType) {
        return FundChannelApiType.MANUAL_REFUND == fundChannelApiType;
    }

    public static String getFundChannelCode(ChannelVO channel) {
        return channel.getChannelCode();
    }

    public static String getExtVal(List<ChannelExtVO> extList, ChannelInfoExtKey extKey) {
        ChannelExtVO ext = getExt(extList, extKey);
        return ext == null ? null : ext.getAttrValue();
    }

    public static ChannelExtVO getExt(List<ChannelExtVO> extList, ChannelInfoExtKey extKey) {
        if (CollectionUtils.isEmpty(extList) || extKey == null) {
            return null;
        }
        for (ChannelExtVO item : extList) {
            if (extKey.getCode().equals(item.getAttrKey())) {
                return item;
            }
        }
        return null;
    }

    /**
     * 是否为外部url
     *
     * @param extension
     * @return
     */
    public static boolean isOuterUrl(String extension) {
        try {
            return StringUtils.isNotEmpty(extension) && JSONObject.parseObject(extension).getBooleanValue(BasicConstant.INNER_RETURN_FLAG);
        } catch (Exception e) {
            return false;
        }
    }

}