package com.uaepay.cmf.fss.ext.integration.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>CMF相关MQ动作定义枚举</p>
 *
 * <AUTHOR>
 * @version $Id: MQActionEnum.java, v 0.1 2012-8-08 下午07:33:32 Shi Exp $
 */
@Getter
public enum MQActionEnum {
    /**
     * mq通知类型
     */
    FUNDS_RESULT_QUEUE("uaepay.payment.fundsResultQueue", "资金源支付通知队列"),
    CASHDESK_BIND_CARD_QUEUE("uaepay.cashdesk.bindCardQueue", "绑卡请求通知队列"),
    CMF_BIND_CARD_NOTIFY_EXCHANGE("exchange.cmf.bindCard.notify", "cmf绑卡通知交换机"),
    CHANNEL_FUND_ORDER_QUEUE("uaepay.escrow.channelFundOrderQueue", "渠道支付订单通知队列"),
    GRC_3DS_RESULT_NOTIFY_QUEUE("uaepay.grc.notify3DSResultQueue", "3ds结果通知队列")
    ;


    /**
     * 队列名称
     */
    private final String queueName;
    /**
     * 描述信息
     */
    private final String message;

    /**
     * 构造
     *
     * @param queueName
     * @param message
     */
    MQActionEnum(String queueName, String message) {
        this.queueName = queueName;
        this.message = message;
    }

    /**
     * 根据actionId获取enum
     *
     * @param queueName
     * @return
     */
    public static MQActionEnum getByQueueName(String queueName) {
        if (StringUtils.isBlank(queueName)) {
            return null;
        }
        for (MQActionEnum action : MQActionEnum.values()) {
            if (queueName.equalsIgnoreCase(action.getQueueName())) {
                return action;
            }
        }
        return null;
    }
}
