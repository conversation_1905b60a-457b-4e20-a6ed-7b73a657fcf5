package com.uaepay.cmf.fss.ext.integration.proxy.dubbo;

import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade;
import com.uaepay.cmf.fss.ext.integration.proxy.ChannelRemoteProxy;
import com.uaepay.cmf.fss.ext.integration.util.InstCovertUtil;
import org.springframework.stereotype.Service;

/**
 * <p>
 * dubbo资金接口
 * </p>
 *
 * <AUTHOR>
 * @date DubboChannelRemoteProxyImpl.java v1.0 2019-11-08 14:55
 */
@Service("dubboChannelFundRemoteProxy")
public class DubboChannelFundRemoteProxyImpl extends AbstractDubboRemoteProxy<ChannelFundFacade>
        implements ChannelRemoteProxy {

    @Override
    public ChannelFundResult applyFund(ChannelRequest request, Long timeout) {
        ChannelFundResult result = null;
        logger.info("[CMF->Channel]渠道请求信息: request={}", request);
        try {
            Integer intTimeout = timeout == null ? defaultTimeout : timeout.intValue();
            result = this.getTarget(request.getApiUrl(), intTimeout).apply(InstCovertUtil.convert(request));
        } catch (Exception e) {
            logger.error("[CMF<-Channel]渠道请求异常: ", e);
            throw new CommunicateException(e);
        }
        logger.info("[CMF<-Channel]渠道响应信息: result={}", result);
        return result;
    }

}
