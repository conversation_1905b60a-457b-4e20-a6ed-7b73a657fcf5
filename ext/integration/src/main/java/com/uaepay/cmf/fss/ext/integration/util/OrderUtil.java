package com.uaepay.cmf.fss.ext.integration.util;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version OrderUtil.java 1.0 Created@2016-10-24 11:22 $
 */
public class OrderUtil {

    private static final String TICKET_REGEX = "^((P|C)\\d{3})[^']*";

    private static final Pattern DATA_PATTERN = Pattern.compile(TICKET_REGEX);

    private OrderUtil() {

    }

    /**
     * 更新新key
     *
     * @param extMap
     * @param origKey
     * @param newKey
     */
    public static void putExtIfEmpty(Map<String, String> extMap, ExtensionKey origKey,
                                     ExtensionKey newKey) {
        if (MapUtils.isEmpty(extMap) || origKey == null || newKey == null
                || !StringUtils.isEmpty(extMap.get(origKey.key))
                || StringUtils.isEmpty(extMap.get(newKey.key))) {
            return;
        }
        extMap.put(origKey.key, extMap.get(newKey.key));
    }

    /**
     * 退款或交易撤销
     *
     * @param cmfOrder
     * @return
     */
    public static boolean isRefund(CmfOrder cmfOrder) {
        return cmfOrder != null
                && cmfOrder.getBizType() == BizType.REFUND;
    }

    public static boolean isEncryptedData(String data) {
        if(StringUtils.isEmpty(data)){
            return false;
        }
        return DATA_PATTERN.matcher(data).matches();
    }

}
