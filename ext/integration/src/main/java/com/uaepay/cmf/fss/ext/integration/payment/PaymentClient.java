package com.uaepay.cmf.fss.ext.integration.payment;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.service.facade.result.CmfResult;


/**
 * <p>PE客户端</p>
 *
 * <AUTHOR>
 * @version $Id: PaymentClient.java, v 0.1 2012-8-9 下午1:58:52 malianhao Exp $
 */
public interface PaymentClient {


    /**
     * 通知PE机构支付结果信息
     * @param cmfResult 机构支付结果信息
     * @return 通知结果
     */
    BaseResult sendInstCmfResult(CmfResult cmfResult);

}
