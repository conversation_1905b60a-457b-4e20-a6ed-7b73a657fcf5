package com.uaepay.cmf.fss.ext.integration.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.domain.ChannelRequest;

/**
 * <p>实例转换工具类</p>
 * <AUTHOR>
 * @version $Id: InstCovertUtil.java, v 0.1 2012-8-23 下午1:16:23 malianhao Exp $
 */
public class InstCovertUtil {
    /**
    * 转换控制类或资金控制类请求实例
    *
    * @param request 原始请求
    * @return
    */
    public static String convert(ChannelRequest request) {
        return JSON.toJSONString(request, SerializerFeature.UseISO8601DateFormat);
    }

    /**
    * 转换资金类请求对象
    *
    * @param request 原始请求
    * @return
    */
    public static String convert(ChannelFundRequest request) {
        return JSON.toJSONString(request, SerializerFeature.UseISO8601DateFormat);
    }
}
