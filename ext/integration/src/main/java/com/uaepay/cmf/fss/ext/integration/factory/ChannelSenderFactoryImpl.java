package com.uaepay.cmf.fss.ext.integration.factory;

import com.uaepay.cmf.common.domain.*;
import org.springframework.stereotype.Service;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date ChannelSenderFactoryImpl.java v1.0 2019-11-12 16:23
 */
@Service
public class ChannelSenderFactoryImpl extends AbstractChannelSenderFactory {

    @Override
    public ChannelFundResult applyFund(ChannelRequest request, Long timeout) {
        return getSingleProxy(request).applyFund(request, timeout);
    }

    @Override
    public ChannelCommonResult applyManager(ChannelRequest request) {
        return getCommonProxy(request).applyManager(request);
    }

    @Override
    public ChannelFundBatchResult applyBatch(ChannelFundRequest request) {
        return getBatchProxy(request).applyFund(request);
    }

    @Override
    public ChannelFundBatchResult applyBatchQuery(ChannelRequest request) {
        return getBatchProxy(request).applyQuery(request);
    }

}
