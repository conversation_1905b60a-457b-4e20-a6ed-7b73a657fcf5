package com.uaepay.cmf.fss.ext.integration.factory;

import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.domain.*;

/**
 * <p>
 * 渠道代理
 * </p>
 *
 * <AUTHOR>
 * @date ChannelProxy.java v1.0 2019-11-12 10:20
 */
public interface ChannelSenderFactory {

    /**
     *
     * 资金类交易
     * 
     * @param request
     * @param timeout
     * @return
     * @throws CommunicateException
     */
    ChannelFundResult applyFund(ChannelRequest request, Long timeout);

    /**
     * 控制类交易
     *
     * @param request
     *            请求参数
     *
     * @return 处理结果
     */
    ChannelCommonResult applyManager(ChannelRequest request);

    /**
     * 批量请求
     *
     * @param request
     *            请求参数
     * @return
     */
    ChannelFundBatchResult applyBatch(ChannelFundRequest request);

    /**
     * 批量查询
     *
     * @param request
     *            请求参数
     * @return
     */
    ChannelFundBatchResult applyBatchQuery(ChannelRequest request);

}
