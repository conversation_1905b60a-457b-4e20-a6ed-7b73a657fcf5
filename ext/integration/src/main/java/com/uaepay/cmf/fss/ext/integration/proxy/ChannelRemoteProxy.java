package com.uaepay.cmf.fss.ext.integration.proxy;

import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelRequest;

/**
 * 功能描述：远程调用基础接口
 *
 * <AUTHOR>
 * time : 2012-8-6 下午6:37:06
 */
public interface ChannelRemoteProxy {

    /**
     * 转发外部交易请求
     *
     * @param request 请求参数
     * @param timeout 超时时间
     *
     * @return 处理结果
     */
    ChannelFundResult applyFund(ChannelRequest request, Long timeout);
}
