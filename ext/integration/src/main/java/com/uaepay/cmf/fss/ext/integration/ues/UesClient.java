/*
 * Copyright 2012 uaepay.com, Inc. All rights reserved.
 * sdp.com PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 * creator : malianhao
 * create time : 2012-8-7 下午4:53:07
 */
package com.uaepay.cmf.fss.ext.integration.ues;

import java.util.List;
import java.util.Map;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.UesDataTypeEnum;
import com.uaepay.ues.model.UesResult;

/**
 * 功能描述：加解密服务客户端
 * <AUTHOR>
 * time : 2012-8-7 下午4:53:07
 */
public interface UesClient extends BasicConstant {

    /**
     * 获取单个数据的解密信息
     * @param key
     * @return
     */
    String getDataByTicket(String key);

    /**
     * 保存数据
     * @param plainData
     * @param uesType
     * @return
     */
    String saveData(String plainData, UesDataTypeEnum uesType);

    /**
     * 加密
     * @param plainData 明文数据
     * @param summary
     * @param uesType
     * @return
     */
    String saveData(String plainData, String summary, UesDataTypeEnum uesType);

    /**
     * 清除临时ticket
     * @param ticket
     * @return
     */
    Boolean deleteTempDataByTicket(String ticket);
}
