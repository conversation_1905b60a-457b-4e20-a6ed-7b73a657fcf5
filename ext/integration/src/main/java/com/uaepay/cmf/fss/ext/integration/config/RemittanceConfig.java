package com.uaepay.cmf.fss.ext.integration.config;

import com.uaepay.basis.beacon.service.facade.enums.common.YesNoEnum;
import com.uaepay.cmf.common.core.domain.vo.TransformInfo;
import com.uaepay.common.util.money.Money;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date RemittanceConfig.java v1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "escrow.notify.remittance")
public class RemittanceConfig implements ITransformConfig{

    protected String channels = "";

    protected String topUpBizCode = "230101";

    protected String consumeBizCode = "200000";

    protected String transformBizCodes = "";

    protected String vipPass = "Y";

    private static final String ALL_TRANSFORM_CHANNEL = "ALL";

    @Override
    public boolean isVipPass() {
        return YesNoEnum.YES.getCode().equals(vipPass);
    }

    @Override
    public Map<String, Integer> getRatioBizCodeMap() {
        return null;
    }

    @Override
    public boolean isTransformChannel(String channelCode) {
        if (ALL_TRANSFORM_CHANNEL.equals(this.channels)){
            return true;
        }
        return this.channels != null && channelCode != null && this.channels.contains(channelCode);
    }

    /**
     * 业务产品码
     *
     * @param info 产品业务码
     * @return true or false
     */
    @Override
    public boolean isTransformTarget(TransformInfo info) {
        return transformBizCodes != null && info != null && info.getBizProductCode() != null
                && transformBizCodes.contains(info.getBizProductCode());
    }


    @Override
    public boolean isTransform(TransformInfo info) {

        return info != null && isTransformChannel(info.getFundChannelCode()) && isTransformTarget(info);
    }

    @Override
    public boolean isAmountInRange(Money amount) {
        return true;
    }

}