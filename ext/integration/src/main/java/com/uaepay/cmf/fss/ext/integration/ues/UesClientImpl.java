package com.uaepay.cmf.fss.ext.integration.ues;

import com.uaepay.cmf.common.core.domain.enums.UesDataTypeEnum;
import com.uaepay.ues.UesClientV2;
import com.uaepay.ues.ctx.EncryptContextV2;
import com.uaepay.ues.ctx.params.EchoSummary;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 功能描述：
 *
 * <AUTHOR> time : 2012-8-7 下午4:55:19
 */
@Service
public class UesClientImpl implements UesClient {
    private static final Logger logger = LoggerFactory.getLogger(UesClientImpl.class);

    @Autowired
    private UesClientV2 uesClientV2;

    @Override
    public String getDataByTicket(String ticket) {
        if (StringUtils.isEmpty(ticket)) {
            return ticket;
        }
        try {
            EncryptContextV2 context = new EncryptContextV2();
            context.setTicket(ticket);
            boolean result = uesClientV2.getDataByTicket(context);
            if (result) {
                return context.getPlainData();
            }
        } catch (Exception e) {
            logger.info("[CMF<-UES]转换ticket异常", e);
        }
        return ticket;
    }

    @Override
    public String saveData(String plainData, UesDataTypeEnum uesType) {
        return saveData(plainData, null, uesType);
    }

    @Override
    public String saveData(String plainData, String summary, UesDataTypeEnum uesType) {
        try {
            EncryptContextV2 context = new EncryptContextV2(plainData, uesType.getCode());
            if (StringUtils.isNotEmpty(summary)) {
                // 自定义摘要信息，可以不使用
                context.withSummariable(new EchoSummary(summary));
            }
            boolean result = uesClientV2.saveData(context);
            if (result) {
                return context.getTicket();
            }
        } catch (Exception e) {
            logger.info("[CMF<-UES]加密异常:" + plainData, e);
        }
        return plainData;
    }

    @Override
    public Boolean deleteTempDataByTicket(String ticket) {
        if (StringUtils.isEmpty(ticket)){
            return true;
        }

        try {
            boolean result = uesClientV2.deleteTempEncryptData(ticket);
            logger.info("[cmf => ues] deleteTempEncryptData result : {}", result);
            return result;
        } catch (Exception e) {
            logger.error("[CMF->UES]删除临时ticket异常:", e);
        }

        return false;
    }
}
