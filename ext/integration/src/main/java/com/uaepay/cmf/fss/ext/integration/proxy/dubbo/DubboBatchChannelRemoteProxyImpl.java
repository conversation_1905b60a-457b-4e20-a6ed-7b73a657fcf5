package com.uaepay.cmf.fss.ext.integration.proxy.dubbo;

import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.fss.ext.common.api.ChannelFundBatchFacade;
import com.uaepay.cmf.fss.ext.integration.proxy.BatchChannelRemoteProxy;
import com.uaepay.cmf.fss.ext.integration.util.InstCovertUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <p>
 * dubbo批量接口
 * </p>
 *
 * <AUTHOR>
 * @date DubboBatchChannelRemoteProxyImpl.java v1.0 2019-11-08 14:57
 */
@Service("dubboBatchChannelRemoteProxy")
public class DubboBatchChannelRemoteProxyImpl extends AbstractDubboRemoteProxy<ChannelFundBatchFacade>
        implements BatchChannelRemoteProxy {

    @Value("${dubbo.batch.timeout:120000}")
    private int batchTimeout;

    @Override
    public ChannelFundBatchResult applyFund(ChannelFundRequest request) {
        ChannelFundBatchResult result = null;
        logger.info("[CMF->Channel]渠道请求信息: request={}", request);
        try {
            result = super.getTarget(request.getApiUrl(), batchTimeout)
                    .apply(InstCovertUtil.convert(request));
        } catch (Exception e) {
            logger.error("[CMF<-BatchChannel]渠道批量资金请求异常: instOrderNo=" + request.getInstOrderNo() + ", apiType="
                    + request.getApiType().getCode() + ", apiUri=" + request.getApiUrl(), e);
            throw new CommunicateException(e);
        }
        logger.info("[CMF<-Channel]渠道响应信息: result={}", result);

        return result;
    }

    @Override
    public ChannelFundBatchResult applyQuery(ChannelRequest request) {
        ChannelFundBatchResult result = null;
        logger.info("[CMF->Channel]batchChannel.applyQuery: request={}", request);
        try {
            result = super.getTarget(request.getApiUrl(), batchTimeout)
                    .apply(InstCovertUtil.convert(request));
        } catch (Exception e) {
            logger.error("[CMF<-BatchChannel]渠道批量查询请求异常: instOrderNo=" + request.getInstOrderNo() + ", apiType="
                    + request.getApiType().getCode() + ", apiUri=" + request.getApiUrl(), e);
            throw new CommunicateException(e);
        }
        logger.info("[CMF<-Channel]batchChannel.batchResult: result={}", result);

        return result;
    }

}
