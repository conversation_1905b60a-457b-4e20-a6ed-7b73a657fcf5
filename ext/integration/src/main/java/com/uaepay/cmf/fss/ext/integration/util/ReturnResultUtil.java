package com.uaepay.cmf.fss.ext.integration.util;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.exception.RouteChannelException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.fundin.ebank.EBankChannelVerifyRequest;
import com.uaepay.cmf.service.facade.domain.CmfCommonResultCode;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>返回结果.</p>
 *
 * <AUTHOR>
 * @version InstResultUtil.java 1.0 @2015/7/28 14:22 $
 */
public class ReturnResultUtil implements BasicConstant {

    private ReturnResultUtil() {

    }

    /**
     * 路由失败置cmf失败逻辑
     *
     * @param cmfOrder
     * @return
     */
    public static InstOrderResult routeFail(CmfOrder cmfOrder, RouteChannelException routeChannelException) {

        InstOrderResult result = new InstOrderResult();
        result.setRealAmount(cmfOrder.getAmount());
        result.setBizType(cmfOrder.getBizType());

        Map<String, String> extension = new HashMap<>();

        if (routeChannelException != null) {
            result.setInstResultCode(routeChannelException.getCode());
            String errorMsg =
                    StringUtils.isEmpty(routeChannelException.getMessage()) ?
                            "路由失败,没有可用渠道" : routeChannelException.getMessage();
            result.setResultMessage(errorMsg);
            result.setMemo(errorMsg);
            extension.put(ExtensionKey.UNITY_RESULT_CODE.key, routeChannelException.getCode());
        }
        extension.put(ExtensionKey.PAYMENT_SEQ_NO.key, cmfOrder.getPaymentSeqNo());
        result.setStatus(InstOrderResultStatus.FAILURE);
        result.setProcessStatus(InstOrderProcessStatus.SUCCESS);
        result.setExtension(extension);
        return result;
    }

    /**
     * 推进结果控制
     *
     * @param resultCode
     * @param routeChannelException
     * @return
     */
    public static InstControlOrderResult routeFail(CmfCommonResultCode resultCode, RouteChannelException routeChannelException) {
        InstControlOrderResult result = new InstControlOrderResult();
        Map<String, String> extension = new HashMap<>();
        result.setStatus(InstOrderResultStatus.FAILURE);

        if (routeChannelException != null) {
            result.setInstResultCode(routeChannelException.getCode());
            String errorMsg = StringUtils.isEmpty(routeChannelException.getMessage()) ?
                            "路由失败,没有可用渠道" : routeChannelException.getMessage();
            result.setResultMessage(errorMsg);
            result.setMemo(errorMsg);
            extension.put(ExtensionKey.UNITY_RESULT_CODE.key, routeChannelException.getCode());
        }
        result.setExtension(extension);

        return result;
    }

    public static InstOrderResult buildInProcess(CmfOrder order, String msg) {
        InstOrderResult result = new InstOrderResult();
        result.setRealAmount(order.getAmount());
        result.setBizType(order.getBizType());

        Map<String, String> extension = new HashMap<>();

        result.setResultMessage(msg);
        result.setMemo(msg);
        result.setStatus(InstOrderResultStatus.IN_PROCESS);
        result.setProcessStatus(InstOrderProcessStatus.UNKNOW_EXCEPTION);
        result.setExtension(extension);
        return result;
    }

    public static InstOrderResult buildInProcess(InstOrder order, String msg) {
        InstOrderResult result = new InstOrderResult();
        BeanUtils.copyProperties(order, result);
        result.setRealAmount(order.getAmount());
        Map<String, String> extension = new HashMap<>();
        result.setResultMessage(msg);
        result.setStatus(InstOrderResultStatus.IN_PROCESS);
        result.setProcessStatus(InstOrderProcessStatus.UNKNOW_EXCEPTION);
        result.setExtension(extension);
        return result;
    }

    public static InstControlOrderResult buildInProcess(InstControlOrder order, String msg) {
        InstControlOrderResult result = new InstControlOrderResult();
        BeanUtils.copyProperties(order, result);
        Map<String, String> extension = new HashMap<>();
        result.setResultMessage(msg);
        result.setStatus(InstOrderResultStatus.IN_PROCESS);
        result.setProcessStatus(InstOrderProcessStatus.UNKNOW_EXCEPTION);
        result.setExtension(extension);
        return result;
    }

    public static InstOrderResult buildInProcessResult(VerifySignRequest request) {
        InstOrderResult result = new InstOrderResult();
        result.setInstOrderNo(request.getInstOrderNo());
        result.setFundChannelCode(request.getChannelCode());
        result.setProcessStatus(InstOrderProcessStatus.AWAITING);
        result.setStatus(InstOrderResultStatus.IN_PROCESS);
        //临时
        result.setBizType(BizType.FUNDIN);
        return result;
    }
}
