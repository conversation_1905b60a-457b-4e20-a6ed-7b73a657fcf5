package com.uaepay.cmf.fss.ext.integration.factory;

import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.common.enums.ApiMethodEnum;
import com.uaepay.cmf.fss.ext.integration.proxy.BatchChannelRemoteProxy;
import com.uaepay.cmf.fss.ext.integration.proxy.ChannelCommonRemoteProxy;
import com.uaepay.cmf.fss.ext.integration.proxy.ChannelRemoteProxy;
import org.apache.commons.lang.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * 渠道发送抽象工厂类
 * </p>
 *
 * <AUTHOR>
 * @date AbstractChannelSenderFactory.java v1.0 2019-11-12 16:34
 */
public abstract class AbstractChannelSenderFactory implements ChannelSenderFactory {

    private Map<ApiMethodEnum, ChannelRemoteProxy> singleProxyMap = new ConcurrentHashMap<>(4);

    private Map<ApiMethodEnum, ChannelCommonRemoteProxy> commonProxyMap = new ConcurrentHashMap<>(4);

    private Map<ApiMethodEnum, BatchChannelRemoteProxy> batchProxyMap = new ConcurrentHashMap<>(4);

    @Resource(name = "channelRemoteProxy")
    private ChannelRemoteProxy channelRemoteProxy;
    @Resource(name = "dubboChannelFundRemoteProxy")
    private ChannelRemoteProxy dubboChannelFundRemoteProxy;
    @Resource(name = "channelCommonRemoteProxy")
    private ChannelCommonRemoteProxy channelCommonRemoteProxy;
    @Resource(name = "dubboChannelCommonRemoteProxy")
    private ChannelCommonRemoteProxy dubboChannelCommonRemoteProxy;
    @Resource(name = "batchChannelRemoteProxy")
    private BatchChannelRemoteProxy batchChannelRemoteProxy;
    @Resource(name = "dubboBatchChannelRemoteProxy")
    private BatchChannelRemoteProxy dubboBatchChannelRemoteProxy;


    protected ChannelRemoteProxy getSingleProxy(ChannelRequest request) {
        return getSingleProxyMap().get(request.getApiMethod());
    }

    protected ChannelCommonRemoteProxy getCommonProxy(ChannelRequest request) {
        return getCommonProxyMap().get(request.getApiMethod());
    }

    protected BatchChannelRemoteProxy getBatchProxy(ChannelRequest request) {
        return getBatchProxyMap().get(request.getApiMethod());
    }

    private static boolean isHttp(String url) {
        return StringUtils.isNotEmpty(url) && url.startsWith("http");
    }

    public Map<ApiMethodEnum, ChannelRemoteProxy> getSingleProxyMap() {
        return singleProxyMap;
    }

    public Map<ApiMethodEnum, ChannelCommonRemoteProxy> getCommonProxyMap() {
        return commonProxyMap;
    }

    public Map<ApiMethodEnum, BatchChannelRemoteProxy> getBatchProxyMap() {
        return batchProxyMap;
    }

    @PostConstruct()
    void init() {
        singleProxyMap.put(ApiMethodEnum.WS, channelRemoteProxy);
        singleProxyMap.put(ApiMethodEnum.DUBBO, dubboChannelFundRemoteProxy);
        commonProxyMap.put(ApiMethodEnum.WS, channelCommonRemoteProxy);
        commonProxyMap.put(ApiMethodEnum.DUBBO, dubboChannelCommonRemoteProxy);
        batchProxyMap.put(ApiMethodEnum.WS, batchChannelRemoteProxy);
        batchProxyMap.put(ApiMethodEnum.DUBBO, dubboBatchChannelRemoteProxy);


    }
}
