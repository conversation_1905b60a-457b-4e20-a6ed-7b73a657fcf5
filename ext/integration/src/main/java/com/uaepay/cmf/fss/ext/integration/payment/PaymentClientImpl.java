package com.uaepay.cmf.fss.ext.integration.payment;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.fss.ext.integration.common.AbstractJmsSender;
import com.uaepay.cmf.fss.ext.integration.enums.MQActionEnum;
import com.uaepay.cmf.service.facade.result.CmfResult;
import org.springframework.stereotype.Service;

/**
 * <p>通知PE支付结果实现类</p>
 * <AUTHOR>
 * @version $Id: PaymentClientImpl.java, v 0.1 2012-8-9 下午2:02:41 malianhao Exp $
 */
@Service
public class PaymentClientImpl extends AbstractJmsSender implements PaymentClient {

    @Override
    public BaseResult sendInstCmfResult(CmfResult cmfResult) {
        return super.send(cmfResult, MQActionEnum.FUNDS_RESULT_QUEUE);
    }
}
