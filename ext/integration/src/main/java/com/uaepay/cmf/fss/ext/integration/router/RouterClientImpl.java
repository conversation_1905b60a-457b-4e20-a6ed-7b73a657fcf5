package com.uaepay.cmf.fss.ext.integration.router;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.router.ApiRouteParam;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.router.service.facade.ChannelFacade;
import com.uaepay.router.service.facade.ResultFacade;
import com.uaepay.router.service.facade.RouteFacade;
import com.uaepay.router.service.facade.domain.ResultCodeRequest;
import com.uaepay.router.service.facade.domain.ResultCodeResult;
import com.uaepay.router.service.facade.domain.RouteRequest;
import com.uaepay.router.service.facade.domain.RouteResponse;
import com.uaepay.router.service.facade.domain.channel.ChannelBatchArchiveVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.router.service.facade.domain.channel.InstCurrencyVO;
import com.uaepay.router.service.facade.domain.request.*;
import com.uaepay.router.service.facade.domain.response.PkResponse;
import com.uaepay.router.service.facade.domain.response.QueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date RouterClientImpl.java v1.0  2020-09-05 23:59
 */
@Slf4j
@Service
public class RouterClientImpl implements RouterClient {

    @Reference
    private RouteFacade routeFacade;

    @Reference
    private ChannelFacade channelFacade;

    @Reference
    private ResultFacade resultFacade;

    @Override
    public RouteResponse<ChannelVO> route(RouteRequest request) {
        log.info("RouterClient.route.request:{}", request);
        RouteResponse<ChannelVO> resp = null;
        try {
            resp = routeFacade.route(request);
        } catch (Exception e) {
            log.error("RouterClient.route.error", e);
            return buildFailResp(e.getMessage());
        }

        log.info("RouterClient.route.result:{}", resp);
        return resp;
    }

    @Override
    public RouteResponse<ChannelVO> route(ApiRouteParam param) {
        RouteApiRequest request = new RouteApiRequest();
        BeanUtils.copyProperties(param, request);
        request.setClientId(CLIENT_ID);
        log.info("RouterClient.route.request:{}", request);
        RouteResponse<ChannelVO> resp = null;
        try {
            resp = routeFacade.routeApi(request);
        } catch (Exception e) {
            log.error("RouterClient.route.error", e);
            return buildFailResp(e.getMessage());
        }

        log.info("RouterClient.route.result:{}", resp);
        return resp;
    }

    @Override
    public String genOrderNo(String channelCode, FundChannelApiType apiType) {
        return genOrderNo(channelCode, apiType, false);
    }

    @Override
    public String genOrderNo(String channelCode, FundChannelApiType apiType, boolean isArchive) {
        GenOrderNoRequest request = buildRequest(channelCode, apiType, isArchive);
        log.info("RouterClient.genOrderNo.request:{}", request);
        PkResponse<String> resp = channelFacade.genOrderNo(request);
        log.info("RouterClient.genOrderNo.result:{}", resp);
        return resp.getItem();
    }

    @Override
    public ResultCodeResult parseResult(ResultCodeRequest request) {
        log.info("RouterClient.parseResult.request:{}", request);
        ResultCodeResult resp = resultFacade.convertResult(request);
        log.info("RouterClient.parseResult.result:{}", resp);
        return resp;
    }

    @Override
    public List<ChannelVO> getChannelsByApiTypes(String channelCode, List<String> apiTypes) {
        log.info("RouterClient.getChannelsByApiTypes.request:{}-{}", channelCode, apiTypes);
        ApiTypeRequest request = new ApiTypeRequest();
        request.setChannelCode(channelCode);
        request.setApiTypes(apiTypes);
        request.setClientId(CLIENT_ID);
        QueryResponse<ChannelVO> resp = channelFacade.getChannelsByApiType(request);
        log.info("RouterClient.getChannelsByApiTypes.result:{}", resp);
        return resp.getResults();
    }

    @Override
    public ChannelBatchArchiveVO queryChannelArchive(String apiCode){
        log.info("RouterClient.queryChannelArchive.request:{}", apiCode);
        ArchiveQueryRequest request = new ArchiveQueryRequest();
        request.setApiCode(apiCode);
        request.setClientId(CLIENT_ID);
        PkResponse<ChannelBatchArchiveVO> response = channelFacade.queryBatchArchive(request);
        log.info("RouterClient.queryChannelArchive.result:{}", response);
        Assert.isTrue(response.getApplyStatus()==ApplyStatusEnum.SUCCESS && response.getItem()!=null, "请求失败");
        return response.getItem();
    }

    @Override
    public boolean is3ds2Channel(String channelCode) {
        RouteResponse<ChannelVO> resp = route(RouteUtil.getParam(channelCode, FundChannelApiType.ADVANCE_3DS2));
        return resp != null && resp.getChannel() != null && resp.getChannel().getChannelApi() != null;
    }

    @Override
    public List<InstCurrencyVO> queryInstCurrency(List<String> apiTypes) {

        QueryInstCurrencyRequest request = new QueryInstCurrencyRequest();
        request.setClientId(CLIENT_ID);
        request.setApiTypeList(apiTypes);
        log.info("RouterClient.queryInstCurrency.request:{}", request);
        QueryResponse<InstCurrencyVO> response = channelFacade.queryInstCurrency(request);
        log.info("RouterClient.queryInstCurrency.response:{}", response);
        return response.getResults();
    }

    private GenOrderNoRequest buildRequest(String channelCode, FundChannelApiType apiType, boolean isArchive) {
        GenOrderNoRequest request = new GenOrderNoRequest();
        request.setChannelCode(channelCode);
        request.setClientId(CLIENT_ID);
        if (apiType != null) {
            request.setApiType(apiType.getCode());
        }
        request.setArchive(isArchive);
        return request;
    }

    private static RouteResponse<ChannelVO> buildFailResp(String reason) {
        RouteResponse<ChannelVO> resp = new RouteResponse<>();
        resp.setApplyStatus(ApplyStatusEnum.FAIL);
        resp.setMessage(reason);
        return resp;
    }
}
