package com.uaepay.cmf.fss.ext.integration.proxy;

import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade;
import com.uaepay.cmf.fss.ext.integration.common.AbstractRemoteProxy;
import com.uaepay.cmf.fss.ext.integration.util.InstCovertUtil;
import org.springframework.stereotype.Service;

/**
 * 功能描述：渠道远程调用基础实现
 *
 * 支持业务如下：
 * <ul>
 *  <li>消费/撤销</li>
 *  <li>退款</li>
 *  <li>出款</li>
 * </ul>
 * <AUTHOR>
 * time : 2012-8-6 下午6:37:06
 */
@Service("channelRemoteProxy")
public class ChannelRemoteProxyImpl extends AbstractRemoteProxy<ChannelFundFacade> implements
                                                                                  ChannelRemoteProxy {

    @Override
    public ChannelFundResult applyFund(ChannelRequest request, Long timeout){
        logger.info("[CMF->Channel]渠道请求信息: request={}", request);
        ChannelFundResult result = null;
        try {
            result = super.getTarget(request.getFundChannelCode(), request.getApiType(),
                request.getApiUrl(), timeout).apply(InstCovertUtil.convert(request));
        } catch (Exception e) {
            logger.error("[CMF<-Channel]渠道请求异常: ", e);
            //异常移除缓存创建的实例 2012.09.07 malianhao
            cachedMapping.remove(complentApiKey(request.getFundChannelCode(), request.getApiType(),
                complentApiUrl(request.getApiUrl())));
            logger.debug("[CMF->Channel]渠道:对应服务实例清空结果:{}",
                !cachedMapping.containsKey(complentApiUrl(request.getApiUrl())));
            throw new CommunicateException(e);
        }

        logger.info("[CMF<-Channel]渠道返回结果: result={}", result);
        return result;
    }
}
