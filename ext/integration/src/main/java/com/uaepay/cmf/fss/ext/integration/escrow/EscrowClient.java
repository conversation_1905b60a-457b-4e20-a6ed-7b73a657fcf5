package com.uaepay.cmf.fss.ext.integration.escrow;

import com.uaepay.basis.beacon.service.facade.enums.common.YesNoEnum;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.SvaAccountEnum;
import com.uaepay.cmf.common.core.domain.vo.EscrowTransformOrder;
import com.uaepay.cmf.service.facade.domain.query.SimpleOrder;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date EscrowClient.java v1.0  2020-10-16 15:34
 */
public interface EscrowClient extends BasicConstant {

    /**
     * 发送订单
     * @param simpleOrder
     */
    void sendOrder(SimpleOrder simpleOrder);

    void sendTransformOrder(EscrowTransformOrder order);

    /**
     * 查询sva账户
     * @param memberId
     * @return
     */
    SvaAccountEnum querySvaAccount(String memberId);
}
