package com.uaepay.cmf.fss.ext.integration.config;

import com.uaepay.basis.beacon.service.facade.enums.common.YesNoEnum;
import com.uaepay.cmf.common.core.domain.vo.TransformInfo;
import com.uaepay.common.util.money.Money;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date TransformConfig.java v1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "escrow.notify.transform")
@Slf4j
public class TransformConfig implements ITransformConfig{

    private String versionTag = "N";

    private String channels = "MC101,MC104,MC105,FS101,FS102,FS103";

    private String topUpBizCode = "230101";

    private String consumeBizCode = "200000";

    private String financialBizCodes = "260291,260202";

    protected String vipPass = "N";

    private Map<String, Integer> ratioBizCodeMap = new HashMap<String, Integer>() {{
        put("260291", 10);
        put("260202", 10);
    }};

    private BigDecimal amountRangeBegin = new BigDecimal(0);

    private BigDecimal amountRangeEnd = new BigDecimal(5000);

    @Override
    public boolean isVipPass() {
        return YesNoEnum.YES.getCode().equals(vipPass);
    }

    @Override
    public boolean isTransformChannel(String channelCode) {
        return this.channels != null && channelCode != null && this.channels.contains(channelCode);
    }

    @Override
    public boolean isTransformTarget(TransformInfo info) {

        if (info == null){
            return false;
        }

        // 在指定产品码内，是拆单对象
        return info.getBizProductCode() != null && financialBizCodes.contains(info.getBizProductCode());

    }

    @Override
    public boolean isTransform(TransformInfo info) {
        if (info == null){
            return false;
        }

        // 符合产品码范围并且走了规定的拆单渠道，那么就进行escrow上报
        return isTransformChannel(info.getFundChannelCode())
                && info.getBizProductCode() != null && financialBizCodes.contains(info.getBizProductCode());
    }

    @Override
    public boolean isAmountInRange(Money amount) {
        return amount != null && amount.getAmount().compareTo(amountRangeBegin) >= 0 && amount.getAmount().compareTo(amountRangeEnd) <= 0;
    }

}