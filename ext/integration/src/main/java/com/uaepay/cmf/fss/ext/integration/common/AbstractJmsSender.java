package com.uaepay.cmf.fss.ext.integration.common;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.fss.ext.integration.enums.MQActionEnum;
import com.uaepay.cmf.service.facade.result.BindCardRequest;
import com.uaepay.mq.constant.DeliveryMode;
import com.uaepay.mq.constant.MessageFormat;
import com.uaepay.mq.core.MQService;
import com.uaepay.mq.request.notify.DefaultNotifyRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jms.UncategorizedJmsException;
import org.springframework.jms.ResourceAllocationException;
import org.springframework.jms.TransactionRolledBackException;

/**
 * <p>
 * JMS message sending client abstract class
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: AbstractJmsSender.java, v 0.1 2012-8-09 上午10:01:28 sean won Exp $
 */
public abstract class AbstractJmsSender {
    protected static final Logger logger = LoggerFactory.getLogger(AbstractJmsSender.class);

    /**
     * MQ message service
     */
    @Autowired
    private MQService mqService;
    
    @Value("${cmf.jms.retry.interval:100}")
    private long retryIntervalMs;
    
    @Value("${cmf.jms.retry.count:1}")
    private int retryCount;

    /**
     * Send message
     *
     * @param content Message content
     * @param queueName Queue name
     * @return Result
     */
    protected BaseResult send(final Object content, final MQActionEnum queueName) {
        DefaultNotifyRequest notifyRequest = new DefaultNotifyRequest(queueName.getQueueName(), content);
        notifyRequest.setMessageFormat(MessageFormat.OBJECT);
        notifyRequest.setTransacted(true);
        notifyRequest.setDeliveryMode(DeliveryMode.PERSISTENT);
        try {
            logger.debug("Sending JMS message to queue: {}", queueName.getQueueName());
            mqService.sendMessage(notifyRequest);
            return new BaseResult(true);
        } catch (UncategorizedJmsException | ResourceAllocationException | TransactionRolledBackException e) {
            logger.warn("JMS exception occurred, will retry. Queue: {}, Error: {}", queueName.getQueueName(), e.getMessage(), e);
            return retryJmsSend(notifyRequest, queueName, 1);
        } catch (Exception e) {
            logger.error("Failed to send JMS message. Queue: {}, Error: {}", queueName.getQueueName(), e.getMessage(), e);
            return new BaseResult(false);
        }
    }
    
    /**
     * Retry sending JMS message
     * 
     * @param notifyRequest The notification request
     * @param queueName Queue name for logging
     * @param currentRetry Current retry count
     * @return Result of the operation
     */
    private BaseResult retryJmsSend(DefaultNotifyRequest notifyRequest, MQActionEnum queueName, int currentRetry) {
        try {
            Thread.sleep(retryIntervalMs);
            logger.info("Retrying JMS message send (attempt {}/{}). Queue: {}", currentRetry, retryCount, queueName.getQueueName());
            mqService.sendMessage(notifyRequest);
            logger.info("JMS message retry successful. Queue: {}", queueName.getQueueName());
            return new BaseResult(true);
        } catch (Exception ex) {
            if (currentRetry < retryCount) {
                logger.warn("JMS retry attempt {} failed, will try again. Queue: {}, Error: {}", 
                    currentRetry, queueName.getQueueName(), ex.getMessage());
                return retryJmsSend(notifyRequest, queueName, currentRetry + 1);
            } else {
                logger.error("JMS message send failed after {} retries. Queue: {}, Error: {}", 
                    retryCount, queueName.getQueueName(), ex.getMessage(), ex);
                return new BaseResult(false);
            }
        }
    }

    protected BaseResult sendJson(final BindCardRequest bindRequest, final MQActionEnum queueName) {
        try {
            DefaultNotifyRequest<BindCardRequest> notifyRequest = new DefaultNotifyRequest<>();
            notifyRequest.setDestination(queueName.getQueueName());
            notifyRequest.setMessageFormat(MessageFormat.JSON);
            notifyRequest.setContent(bindRequest);
            logger.debug("Sending JSON JMS message to queue: {}", queueName.getQueueName());
            mqService.sendMessage(notifyRequest);
            return new BaseResult(true);
        } catch (Exception e) {
            logger.error("Failed to send JSON JMS message. Queue: {}, Error: {}", queueName.getQueueName(), e.getMessage(), e);
            return new BaseResult(false);
        }
    }
}
