package com.uaepay.cmf.fss.ext.integration.config;

import com.uaepay.cmf.common.core.domain.vo.TransformInfo;
import com.uaepay.common.util.money.Money;

import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ITransformConfig.java v1.0
 */
public interface ITransformConfig {

    boolean isVipPass();

    /**
     * 获取比例
     * @return
     */
    Map<String, Integer> getRatioBizCodeMap();

    /**
     * 获取渠道列表
     * @return
     */
    String getChannels();

    /**
     * 是否为拆单渠道
     * @param channelCode
     * @return
     */
    boolean isTransformChannel(String channelCode);

    /**
     * 是否是拆单交易对象
     *
     * @param info
     * @return
     */
    boolean isTransformTarget(TransformInfo info);

    /**
     * 是否拆单
     *
     * @param info@return
     */
    boolean isTransform(TransformInfo info);

    /**
     * 金额是否在范围内
     * @param amount
     * @return
     */
    boolean isAmountInRange(Money amount);

    /**
     * 充值业务产品码
     * @return
     */
    String getTopUpBizCode();

    /**
     * 消费特定产品码
     * @return
     */
    String getConsumeBizCode();
}
