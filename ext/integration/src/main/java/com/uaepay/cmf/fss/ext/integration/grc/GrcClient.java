package com.uaepay.cmf.fss.ext.integration.grc;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.vo.GrcLimitCarrier;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import com.uaepay.grc.connect.api.vo.domain.CheckInfo;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date GrcClient.java v1.0  2020-10-16 15:34
 */
public interface GrcClient extends BasicConstant {

    /**
     * 校验限额
     * @param carrier
     * @return
     */
    boolean validateLimit(GrcLimitCarrier carrier);

    /**
     * 发送订单
     * @param notify3dsResult
     */
    void send3DsNotify(Notify3dsResult notify3dsResult);


    /**
     *  grc check
     * @param checkInfos checkInfos
     * @return checkInfo list
     */
    List<CheckInfo> checkResult(List<CheckInfo> checkInfos);
}
