package com.uaepay.cmf.fss.ext.integration.router;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.router.ApiRouteParam;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.router.service.facade.domain.ResultCodeRequest;
import com.uaepay.router.service.facade.domain.ResultCodeResult;
import com.uaepay.router.service.facade.domain.RouteRequest;
import com.uaepay.router.service.facade.domain.RouteResponse;
import com.uaepay.router.service.facade.domain.channel.ChannelBatchArchiveVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.router.service.facade.domain.channel.InstCurrencyVO;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date RouterClient.java v1.0  2020-09-05 23:58
 */
public interface RouterClient extends BasicConstant {

    /**
     * 路由参数
     *
     * @param request
     * @return
     */
    RouteResponse<ChannelVO> route(RouteRequest request);

    /**
     * 根据api路由
     *
     * @param request
     * @return
     */
    RouteResponse<ChannelVO> route(ApiRouteParam request);

    /**
     * 生成订单号
     *
     * @param channelCode
     * @param apiType
     * @return
     */
    String genOrderNo(String channelCode, FundChannelApiType apiType);

    String genOrderNo(String channelCode, FundChannelApiType apiType, boolean isArchive);

    /**
     * 结果转换
     *
     * @param request
     * @return
     */
    ResultCodeResult parseResult(ResultCodeRequest request);

    /**
     * @param apiTypes
     * @return
     */
    List<ChannelVO> getChannelsByApiTypes(String channelCode, List<String> apiTypes);

    /**
     * 查询ChannelArchive
     * @param apiCode
     * @return
     */
    ChannelBatchArchiveVO queryChannelArchive(String apiCode);

    /**
     * 3ds2.0渠道
     * @param channelCode
     * @return
     */
    boolean is3ds2Channel(String channelCode);

    /**
     * 查询机构币种
     * @param apiTypes
     * @return
     */
    List<InstCurrencyVO> queryInstCurrency(List<String> apiTypes);
}
