package com.uaepay.cmf.fss.ext.integration.counter;

import com.alibaba.fastjson.JSONObject;
import com.uaepay.cmf.service.facade.domain.counter.RefundOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CounterClientImpl.java v1.0  11/22/20 5:36 PM
 */
@Slf4j
@Service
public class CounterClientImpl implements CounterClient {

    @Autowired
    AmqpAdmin amqpAdmin;

    @Autowired
    private AmqpTemplate amqpTemplate;

    private static final String QUEUE_CMF_REFUND_ORDER = "queue.cmf.refundOrder";

    @Override
    public void sendOrder(RefundOrder refundOrder) {
        try {
            amqpTemplate.convertAndSend("", QUEUE_CMF_REFUND_ORDER, JSONObject.toJSONString(refundOrder));
            log.info("CounterClient-{}-notify success", refundOrder);
        } catch (Exception e) {
            log.error("CounterClient.sendOrder.error", e);
        }
    }

    @PostConstruct
    public void init() {
        Queue queue = QueueBuilder.durable(QUEUE_CMF_REFUND_ORDER).build();
        amqpAdmin.declareQueue(queue);
        log.info("已创建exchange: {}", QUEUE_CMF_REFUND_ORDER);
    }
}
