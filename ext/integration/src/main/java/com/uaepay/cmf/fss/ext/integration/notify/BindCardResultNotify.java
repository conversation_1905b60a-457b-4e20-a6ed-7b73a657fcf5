package com.uaepay.cmf.fss.ext.integration.notify;

import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.vo.BindCardInfo;
import com.uaepay.cmf.service.facade.domain.counter.RefundOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.core.ExchangeBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BindCardNotify.java v1.0
 */
@Slf4j
@Service
public class BindCardResultNotify implements BasicConstant {

    @Autowired
    private AmqpAdmin amqpAdmin;
    @Autowired
    private AmqpTemplate amqpTemplate;

    private static final String BIND_CARD_NOTIFY_EXCHANGE = "exchange.cmf.bindCard";
    private static final String BIND_CARD_0_ROUTING_KEY = "exchange.cmf.auth0";


    public boolean notifyResult(BindCardInfo info) {
        try {
            // 通知
            amqpTemplate.convertAndSend(BIND_CARD_NOTIFY_EXCHANGE, BIND_CARD_0_ROUTING_KEY,
                    JsonUtil.toJsonStringMute(info));
            log.info("processAuthBindCardMessage.message:{}", info);
            return true;
        } catch (Throwable e) {
            log.error("BindCardNotify.processAuthBindCardMessage.error", e);
            return false;
        }
    }


    @PostConstruct
    public void init() {
        amqpAdmin.declareExchange(
                ExchangeBuilder.topicExchange(BIND_CARD_NOTIFY_EXCHANGE).durable(true).build());
        log.info("已创建通知exchange: {}", BIND_CARD_NOTIFY_EXCHANGE);
    }

}
