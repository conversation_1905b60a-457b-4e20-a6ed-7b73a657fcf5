package com.uaepay.cmf.fss.ext.integration.proxy.dubbo;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import org.apache.dubbo.config.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * Dubbo远程访问代理
 * </p>
 *
 * <AUTHOR>
 * @date AbstractDubboRemoteProxy.java v1.0 2019-11-08 14:57
 */
public abstract class AbstractDubboRemoteProxy<T> implements BasicConstant {
    protected static final Logger logger = LoggerFactory.getLogger(AbstractDubboRemoteProxy.class);

    @Resource
    private ApplicationConfig applicationConfig;

    @Resource
    private ConsumerConfig consumerConfig;

    protected Integer defaultTimeout = 15000;

    private final ConcurrentHashMap<String, T> serviceMap = new ConcurrentHashMap<>(30);

    /**
     * consumer://************/com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade?
     * application=gp002_cmf&category=providers,configurators,routers&check=false&dubbo=2.0.2&general=false&group=qpaytap&interface=com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade&lazy=false&methods=apply&pid=8&qos.enable=false&release=2.7.3&retries=0&side=consumer&sticky=false&timeout=15000&timestamp=1581796530226, dubbo version: 2.7.3, current host: ************
     * @param group
     * @return
     */
    public T getTarget(String group){
        return getTarget(group, defaultTimeout);
    }

    protected T getTarget(String group, Integer timeout) {
        String key = buildCacheKey(group);
        T service = serviceMap.get(key);
        if (service != null) {
            return service;
        }
        synchronized (serviceMap) {
            service = serviceMap.get(key);
            if (service != null) {
                return service;
            }
            service = createService(group, timeout);
            serviceMap.put(key, service);
        }
        return service;
    }

    /**
     * consumer://************/com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade?
     * application=gp002_cmf&category=providers,configurators,routers&check=false&dubbo=2.0.2&general=false&group=qpaytap&interface=com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade&lazy=false&methods=apply&pid=8&qos.enable=false&release=2.7.3&retries=0&side=consumer&sticky=false&timeout=15000&timestamp=1581796530226, dubbo version: 2.7.3, current host: ************
     * @param group
     * @return
     */

    protected T createService(String group, Integer timeout){

        ReferenceConfig<T> reference = new ReferenceConfig<>();
        Class<T> entityClass =
                (Class<T>)((ParameterizedType)getClass().getGenericSuperclass()).getActualTypeArguments()[0];
//        reference.setUrl("dubbo://127.0.0.1:20881?group=gp200_qpay-fsii");
//        reference.setRegistry(new RegistryConfig("N/A"));
        reference.setApplication(applicationConfig);
        reference.setConsumer(consumerConfig);
        reference.setInterface(entityClass);
        reference.setGroup(group);
        if(timeout!=null) {
            reference.setTimeout(timeout);
        }else{
            reference.setTimeout(defaultTimeout);
        }
        return reference.get();
    }

    /**
     * 补全缓存urlkey值 返回: qpaytap-ChannelFundFacade
     *
     * @param group
     * @return
     */
    protected String buildCacheKey(String group) {
        Class<T> entityClass =
                (Class<T>)((ParameterizedType)getClass().getGenericSuperclass()).getActualTypeArguments()[0];
        return group + SPLASH + entityClass.getName();
    }

    //    protected T createService(String group, Integer timeout){
//        ReferenceConfig<T> reference = new ReferenceConfig<>();
//        Class<T> entityClass =
//                (Class<T>)((ParameterizedType)getClass().getGenericSuperclass()).getActualTypeArguments()[0];
//        reference.setApplication(applicationConfig);
//        reference.setConsumer(consumerConfig);
//        reference.setInterface(entityClass);
//        reference.setGroup(group);
//        if(timeout!=null) {
//            reference.setTimeout(timeout);
//        }else{
//            reference.setTimeout(defaultTimeout);
//        }
//        ReferenceConfigCache cache = ReferenceConfigCache.getCache();
//        return cache.get(reference);
//    }

}
