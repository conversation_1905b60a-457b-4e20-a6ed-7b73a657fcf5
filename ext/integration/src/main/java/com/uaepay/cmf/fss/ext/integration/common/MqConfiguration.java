package com.uaepay.cmf.fss.ext.integration.common;

import java.util.concurrent.ScheduledThreadPoolExecutor;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.uaepay.mq.failover.impl.TimerRetryExecutor;
import com.uaepay.mq.jms.JmsAccessor;

/**
 * <p>
 * MQ配置
 * </p>
 *
 * <AUTHOR>
 * @version $Id: MqConfiguration.java, v 0.1 2019-8-09 上午10:01:28 $
 */
@Configuration
public class MqConfiguration {

    @Bean
    public TimerRetryExecutor timeRetryExecutor(@Qualifier(value = "mqStarterJmsAccessor") JmsAccessor jmsAccessor) {
        ScheduledThreadPoolExecutor executorService = new ScheduledThreadPoolExecutor(1);
        TimerRetryExecutor timeRetryExecutor = new TimerRetryExecutor(jmsAccessor);
        timeRetryExecutor.setExecutorService(executorService);
        return timeRetryExecutor;
    }

}
