package com.uaepay.cmf.fss.ext.integration.ma;

import com.uaepay.cmf.common.core.domain.ma.MemberChannelToken;
import com.uaepay.member.service.base.model.BeneficiaryInfo;
import com.uaepay.member.service.base.model.CardInfo;

import java.util.List;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version MemberClient.java 1.0 @2016/5/5 17:08 $
 */
public interface MemberClient {

    /**
     * 查询卡号id
     *
     * @param cardId
     * @return
     */
    CardInfo queryCardInfoById(Long cardId);

    /**
     * 查询受益人信息
     * @param beneficiaryId
     * @return
     */
    BeneficiaryInfo queryBeneficiaryInfoById(Long beneficiaryId);

    /**
     * 查询token信息
     * @param cardId
     * @return
     */
    List<MemberChannelToken> queryTokens(Long cardId);
}
