package com.uaepay.cmf.fss.ext.integration.proxy.dubbo;

import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.domain.ChannelCommonResult;
import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.fss.ext.common.api.ChannelCommonFacade;
import com.uaepay.cmf.fss.ext.integration.proxy.ChannelCommonRemoteProxy;
import com.uaepay.cmf.fss.ext.integration.util.InstCovertUtil;
import org.springframework.stereotype.Service;

/**
 * <p>
 *     dubbo控制类接口
 * </p>
 *
 * <AUTHOR>
 * @date DubboChannelCommonRemoteProxyImpl.java v1.0 2019-11-08 14:56
 */
@Service("dubboChannelCommonRemoteProxy")
public class DubboChannelCommonRemoteProxyImpl extends AbstractDubboRemoteProxy<ChannelCommonFacade>
    implements ChannelCommonRemoteProxy {

    @Override
    public ChannelCommonResult applyManager(ChannelRequest request) {
        ChannelCommonResult result = null;
        logger.info("[CMF->Channel]渠道请求信息: request={}", request);
        try {
            result = super.getTarget(request.getApiUrl(), request.getApiType()== FundChannelApiType.CONTROL_VOID_TRANSACTION ? null : 120000).apply(InstCovertUtil.convert(request));
        } catch (Exception e) {
            logger.error("[CMF<-CommonChannel]渠道请求异常", e);
            throw new CommunicateException(e);
        }
        logger.info("[CMF<-Channel]渠道响应信息: result={}", result);

        return result;
    }

}
