package com.uaepay.cmf.fss.ext.integration.grc;

import com.uaepay.basis.beacon.common.exception.ErrorException;
import com.uaepay.basis.beacon.service.facade.domain.response.ObjectQueryResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.basis.beacon.service.facade.enums.common.CommonReturnCode;
import com.uaepay.cmf.common.core.domain.vo.GrcLimitCarrier;
import com.uaepay.cmf.fss.ext.integration.amqp.MqTemplate;
import com.uaepay.cmf.fss.ext.integration.enums.MQActionEnum;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import com.uaepay.grc.common.bean.Decission;
import com.uaepay.grc.common.enums.FoundDirection;
import com.uaepay.grc.common.enums.TransKind;
import com.uaepay.grc.common.enums.TransactionPoint;
import com.uaepay.grc.common.error.ErrorMsg;
import com.uaepay.grc.connect.api.facade.QueryFacade;
import com.uaepay.grc.connect.api.vo.domain.CheckInfo;
import com.uaepay.grc.connect.api.vo.request.WithdrawCheckRequest;
import com.uaepay.grc.cps.api.LimitQuotaFrequencyStub;
import com.uaepay.grc.cps.api.vo.LimitQuotaAndTimesRequest;
import com.uaepay.grc.cps.api.vo.MemberCashFlow;
import com.uaepay.rm.unbreakable.Result;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date GrcClientImpl.java v1.0  2020-10-16 15:34
 */
@Slf4j
@Service
public class GrcClientImpl implements GrcClient {

    @Resource
    private MqTemplate<Notify3dsResult> mqTemplate;
    @Reference
    private LimitQuotaFrequencyStub limitQuotaFrequencyStub;

    @Reference
    QueryFacade queryFacade;

    @Override
    public boolean validateLimit(GrcLimitCarrier carrier) {
        LimitQuotaAndTimesRequest request = convert(carrier);
        log.info("GrcClient.validateLimit.request:{}", request);
        Result<ErrorMsg, Decission> result = limitQuotaFrequencyStub.limitQuotaAndTimes(request);
        log.info("GrcClient.validateLimit.result:{}", result);
        return result != null && result.isRight() && result.rightValue().unsafeGet().isPassed();
    }

    private LimitQuotaAndTimesRequest convert(GrcLimitCarrier carrier) {
        MemberCashFlow cashFlow = MemberCashFlow.builder().memberId(carrier.getMemberId())
                .amount(carrier.getAmount())
                .foundDirection(carrier.isTopUp() ? FoundDirection.IN : FoundDirection.OUT)
                .build();
        return LimitQuotaAndTimesRequest.builder()
                .bizProductCode(carrier.getBizProductCode())
                .payChannelCode(DEFAULT_PAY_CHANNEL)
                .paymentOrderId(carrier.getPaymentOrderNo())
                .payTime(carrier.getGmtCreate().getTime())
                .transactionPoint(carrier.isAfterPay() ? TransactionPoint.POSTTRADE : TransactionPoint.PRETRADE)
                .transKind(carrier.getBizType() == BizType.FUNDIN ? TransKind.POSITIVE : TransKind.NEGATIVE)
                .settlement(Boolean.FALSE)
                .memberCashFlows(Collections.singletonList(cashFlow))
                .build();
    }


    private static final String DEFAULT_PAY_CHANNEL = "14";


    @Override
    public void send3DsNotify(Notify3dsResult notify3dsResult) {
        try {
            mqTemplate.sendMessage(MQActionEnum.GRC_3DS_RESULT_NOTIFY_QUEUE, notify3dsResult);
            log.info("GrcClient-{}-notify success", notify3dsResult);
        } catch (Exception e) {
            log.error("GrcClient.send3DsNotify.error", e);
        }
    }


    @Override
    public List<CheckInfo> checkResult(List<CheckInfo> checkInfos) {

        if (CollectionUtils.isEmpty(checkInfos)) {
            log.info("GrcClient.checkInfos is empty...");
            return new ArrayList<>();
        }
        WithdrawCheckRequest request = new WithdrawCheckRequest();
        request.setClientId(CLIENT_ID);
        request.setList(checkInfos);

        log.info("GrcClient.check.request:{}", request);

        ObjectQueryResponse<List<CheckInfo>> response = queryFacade.check(request);

        log.info("GrcClient.check.response:{}", response);

        if (response.getApplyStatus() != ApplyStatusEnum.SUCCESS) {
            throw new ErrorException(CommonReturnCode.SYSTEM_ERROR);
        }

        return Optional.ofNullable(response.getResult()).orElse(new ArrayList<>());

    }

}
