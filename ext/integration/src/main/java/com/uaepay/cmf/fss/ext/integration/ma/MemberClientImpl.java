package com.uaepay.cmf.fss.ext.integration.ma;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.ma.MemberChannelToken;
import com.uaepay.member.service.base.model.BeneficiaryInfo;
import com.uaepay.member.service.base.model.CardInfo;
import com.uaepay.member.service.base.response.BankCardTokenInfo;
import com.uaepay.member.service.facade.IBeneficiaryFacade;
import com.uaepay.member.service.facade.IMemberCardFacade;
import com.uaepay.member.service.request.beneficiary.QueryByIdRequest;
import com.uaepay.member.service.request.card.QueryCardByCardIdRequest;
import com.uaepay.member.service.response.beneficiary.QueryByIdResponse;
import com.uaepay.member.service.response.card.QueryCardByCardIdResponse;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version MemberClientImpl.java 1.0 @2016/5/5 17:08 $
 */
@Service("maClient")
public class MemberClientImpl implements MemberClient, BasicConstant {
    private Logger logger = LoggerFactory
            .getLogger(MemberClientImpl.class);

    @Reference
    private IMemberCardFacade iMemberCardFacade;
    @Reference
    private IBeneficiaryFacade beneficiaryFacade;

    @Override
    public CardInfo queryCardInfoById(Long cardId) {
        QueryCardByCardIdResponse resp = null;
        logger.info("[CMF->MA]Query Card Info CardId:{}", cardId);
        if (cardId == null) {
            return null;
        }
        for (int times = 0; times < 2; times++) {
            try {
                QueryCardByCardIdRequest request = new QueryCardByCardIdRequest();
                request.setCardId(cardId);
                request.setQueryDisabledFlag(false);
                resp = iMemberCardFacade.queryCardByCardId(request);
                break;
            } catch (Exception e) {
                logger.error("memberClient.queryCardInfoById.Error", e);
            }
        }
        logger.info("Query Card Info Response:{}", resp != null ? resp.getCardInfo() : null);
        if (resp != null) {
            return resp.getCardInfo();
        }
        return null;
    }

    @Override
    public BeneficiaryInfo queryBeneficiaryInfoById(Long beneficiaryId) {
        QueryByIdResponse resp = null;
        logger.info("memberClient.queryBeneficiaryInfoById.beneficiaryId:{}", beneficiaryId);
        if (beneficiaryId == null) {
            return null;
        }
        for (int times = 0; times < 2; times++) {
            try {
                QueryByIdRequest request = new QueryByIdRequest();
                request.setId(beneficiaryId);
                request.setClientId(CLIENT_ID);
                resp = beneficiaryFacade.queryById(request);
                break;
            } catch (Exception e) {
                logger.error("memberClient.queryBeneficiaryInfoById.Error", e);
            }
        }
        logger.info("memberClient.queryBeneficiaryInfoById.response:{}", resp != null ? resp.getBeneficiaryInfo() : null);
        if (resp != null) {
            return resp.getBeneficiaryInfo();
        }
        return null;
    }

    @Override
    public List<MemberChannelToken> queryTokens(Long cardId) {
        if (cardId == null) {
            return null;
        }
        CardInfo cardInfo = queryCardInfoById(cardId);
        if (cardInfo == null || CollectionUtils.isEmpty(cardInfo.getTokenInfos())) {
            return null;
        }
        return cardInfo.getTokenInfos().stream().map(MemberClientImpl::convert).collect(Collectors.toList());
    }

    private static MemberChannelToken convert(BankCardTokenInfo info) {
        MemberChannelToken token = new MemberChannelToken();
        token.setFundChannelCode(info.getChannel());
        token.setToken(info.getToken());
        return token;
    }

}
