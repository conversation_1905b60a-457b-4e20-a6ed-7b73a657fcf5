//package com.uaepay.cmf.fss.ext.integration.amqp;
//
//import com.alibaba.fastjson.JSON;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.rabbit.connection.CorrelationData;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
///**
// * <p> .</p>
// *
// * <AUTHOR>
// * @version MqTemplate 1.0 Created@2020/2/11 10:26 上午$
// */
//@Slf4j
//@Component
//public class ChannelConsistentMqTemplate<T>  implements RabbitTemplate.ConfirmCallback, RabbitTemplate.ReturnCallback{
//
//
//    @Autowired
//    RabbitTemplate rabbitTemplate;
//    @Value("${exchange.supplierChannel.mobileTopUp.name}")
//    private String chanelExchange;
//    @Value("${exchange.supplierChannel.mobileTopUp.routing-key}")
//    private String rechargeRouteKey;
//
//
//
//
//    public void sendMessage(T t){
//        log.info("mq发送请求，详情为....{}",t.toString());
//        rabbitTemplate.convertAndSend(chanelExchange,  rechargeRouteKey, JSON.toJSONString(t));
//    }
//
//
//    @Override
//    public void confirm(CorrelationData correlationData, boolean b, String cause) {
//        log.info("消息唯一标识id:{}", correlationData);
//        log.info("消息确认结果:{}",b);
//        if(!b) {
//            log.error("消息失败原因,cause:{}", cause);
//        }
//    }
//
//    /**
//     * 消费异常时返回
//     * @param message
//     * @param replyCode
//     * @param replyText
//     * @param exchange
//     * @param routingKey
//     */
//    @Override
//    public void returnedMessage(Message message, int replyCode, String replyText, String exchange, String routingKey) {
//        log.info("消息发送失败id:{}", message.getMessageProperties().getCorrelationId());
//        log.info("消息主体 message : ", message);
//        log.info("消息主体 message : ", replyCode);
//        log.info("描述：" + replyText);
//        log.info("消息使用的交换器 exchange : ", exchange);
//        log.info("消息使用的路由键 routing : ", routingKey);
//
//    }
//}
