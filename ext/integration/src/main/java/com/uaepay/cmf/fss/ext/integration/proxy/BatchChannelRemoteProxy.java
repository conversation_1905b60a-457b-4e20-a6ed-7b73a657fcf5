package com.uaepay.cmf.fss.ext.integration.proxy;

import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.domain.ChannelRequest;

/**
 * <p>
 * 渠道批量发送代理
 * </p>
 *
 * <AUTHOR>
 * @version $Id: BatchChannelRemoteProxy.java, v 0.1 2012-8-16 下午1:58:38 malianhao Exp $
 */
public interface BatchChannelRemoteProxy {

    /**
     * 发送外部批量机构交易请求
     *
     * @param request
     *            请求参数
     * @return
     */
    ChannelFundBatchResult applyFund(ChannelFundRequest request);

    /**
     * 发送批量查询订单
     *
     * @param request
     *            请求参数
     * @return
     */
    ChannelFundBatchResult applyQuery(ChannelRequest request);

}
