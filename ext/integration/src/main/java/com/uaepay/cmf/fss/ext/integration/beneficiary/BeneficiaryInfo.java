package com.uaepay.cmf.fss.ext.integration.beneficiary;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 25/12/2023 15:21
 */
@Data
public class BeneficiaryInfo {

    private String memberId;

    private String bankCode;

    /**
     * CC-Credit Card
     * DC-Debit Card
     */
    private String cardType;

    private String targetInst;

    /**
     * encrypted string
     */
    private String iban;

    /**
     * encrypted string
     */
    private String accountName;

    /**
     * encrypted string
     */
    private String beneficiaryAddress;


    /**
     * 目前默认个人提现类型 220401
     */
    private String bizProductCode;


    /**
     * default balance - 14
     */
    private String payChannel;

    /**
     * default anonymous
     */
    private String payeeId;

    /**
     * company-B
     * personal-C
     */
    private String companyOrPersonal;

}
