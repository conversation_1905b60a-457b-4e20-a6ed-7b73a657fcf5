package com.uaepay.cmf.fss.ext.integration.beneficiary;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.service.facade.api.QueryBeneficiaryInfoFacade;
import com.uaepay.cmf.service.facade.domain.fundout.QueryFundoutBeneficiaryInfoRequest;
import com.uaepay.cmf.service.facade.domain.fundout.QueryFundoutBeneficiaryInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ConsumerConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 25/12/2023 15:00
 */
@Component
@Slf4j
public class QueryBeneficiaryInfoClientImpl implements QueryBeneficiaryInfoClient {

    @Resource
    private ApplicationConfig applicationConfig;
    @Resource
    private ConsumerConfig consumerConfig;

    private final Integer defaultTimeout = 15000;

    private final ConcurrentHashMap<String, QueryBeneficiaryInfoFacade> serviceMap = new ConcurrentHashMap<>(16);

    @Override
    public BeneficiaryInfo queryBeneficiaryInfo(String groupCode, QueryFundoutBeneficiaryInfoRequest request) {
        log.info("QueryBeneficiaryInfoClientImpl.queryBeneficiaryInfo.groupCode:{},request:{}", groupCode, request);
        BeneficiaryInfo beneficiaryInfo = new BeneficiaryInfo();
        try {
            QueryFundoutBeneficiaryInfoResponse response = getTarget(groupCode).queryBeneficiaryInfo(request);
            log.info("QueryBeneficiaryInfoClientImpl.queryBeneficiaryInfo.response:{}", response);
            if (!ApplyStatusEnum.SUCCESS.equals(response.getApplyStatus())) {
                return null;
            }
            BeanUtils.copyProperties(response, beneficiaryInfo);
        } catch (Exception e) {
            log.error("QueryBeneficiaryInfoClientImpl.queryBeneficiaryInfo.error", e);
            return null;
        }

        return beneficiaryInfo;
    }


    private QueryBeneficiaryInfoFacade getTarget(String group) {
        return getTarget(group, defaultTimeout);
    }

    private QueryBeneficiaryInfoFacade getTarget(String group, Integer timeout) {
        String key = group + "/" + QueryBeneficiaryInfoFacade.class.getName();
        QueryBeneficiaryInfoFacade service = serviceMap.get(key);
        if (service != null) {
            return service;
        }
        synchronized (serviceMap) {
            service = serviceMap.get(key);
            if (service != null) {
                return service;
            }
            service = createService(group, timeout);
            serviceMap.put(key, service);
        }
        return service;
    }

    private QueryBeneficiaryInfoFacade createService(String group, Integer timeout) {

        ReferenceConfig<QueryBeneficiaryInfoFacade> reference = new ReferenceConfig<>();
        reference.setApplication(applicationConfig);
        reference.setConsumer(consumerConfig);
        reference.setInterface(QueryBeneficiaryInfoFacade.class);
        reference.setGroup(group);
        if (timeout != null) {
            reference.setTimeout(timeout);
        } else {
            reference.setTimeout(defaultTimeout);
        }
        return reference.get();
    }
}
