package com.uaepay.cmf.fss.ext.integration.cashdesk;

import com.rabbitmq.client.AMQP;
import com.uaepay.cmf.fss.ext.integration.amqp.MqTemplate;
import com.uaepay.cmf.fss.ext.integration.enums.MQActionEnum;
import com.uaepay.cmf.service.facade.result.BindCardRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.ExchangeBuilder;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CashdeskClientImpl.java v1.0  2020-03-27 21:06
 */
@Slf4j
@Service("cashdeskClient")
public class CashdeskClientImpl implements CashdeskClient, InitializingBean {

    @Resource
    private MqTemplate<BindCardRequest> mqTemplate;

    @Resource
    private AmqpAdmin amqpAdmin;

    @Override
    public void sendBindCardRequest(BindCardRequest request) {
        if (StringUtils.isNotEmpty(request.getClientId())){
            mqTemplate.sendMessageByExchange(MQActionEnum.CMF_BIND_CARD_NOTIFY_EXCHANGE, request.getClientId(), request);
        }
        log.info("CashdeskClient-{}-notify success", request.getInstOrderNo());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        amqpAdmin.declareExchange(ExchangeBuilder.topicExchange(MQActionEnum.CMF_BIND_CARD_NOTIFY_EXCHANGE.getQueueName()).durable(true).build());
        log.info("exchange created, exchange name is {}", MQActionEnum.CMF_BIND_CARD_NOTIFY_EXCHANGE.getQueueName());
    }
}
