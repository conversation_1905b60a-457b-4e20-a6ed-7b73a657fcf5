package com.uaepay.cmf.fss.ext.integration.cards;

import com.uaepay.channel.cards.service.facade.BankFacade;
import com.uaepay.channel.cards.service.facade.domain.BankVO;
import com.uaepay.channel.cards.service.facade.domain.request.PkQueryRequest;
import com.uaepay.channel.cards.service.facade.domain.response.PkQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardsClientImpl.java v1.0  2020-10-18 15:32
 */
@Slf4j
@Service
public class CardsClientImpl implements CardsClient{

    @Reference
    private BankFacade bankFacade;

    @Override
    public BankVO queryByBankCode(String bankCode){
        PkQueryRequest<String> request = new PkQueryRequest<>();
        request.setPk(bankCode);
        log.info("CardsClient.queryByBankCode.request:{}", request);
        try {
            PkQueryResult<BankVO> response = bankFacade.queryByBankCode(request);
            log.info("CardsClient.queryByBankCode.response:{}", response);
            return response.getItem();
        }catch (Exception e){
            log.error("CardsClient.queryByBankCode.error", e);
            return null;
        }
    }
}
