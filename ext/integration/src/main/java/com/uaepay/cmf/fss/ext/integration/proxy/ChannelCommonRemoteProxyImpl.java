package com.uaepay.cmf.fss.ext.integration.proxy;

import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.core.util.filter.LogFilterUtil;
import com.uaepay.cmf.common.domain.ChannelCommonResult;
import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.fss.ext.common.api.ChannelCommonFacade;
import com.uaepay.cmf.fss.ext.integration.common.AbstractRemoteProxy;
import com.uaepay.cmf.fss.ext.integration.util.InstCovertUtil;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 功能描述：远程调用基础实现
 * </p>
 *
 * <AUTHOR>
 * @version $Id: ChannelCommonRemoteProxyImpl.java, v 1.0 2012-8-16 下午5:38:25 malianhao Exp $
 */
@Service("channelCommonRemoteProxy")
public class ChannelCommonRemoteProxyImpl extends AbstractRemoteProxy<ChannelCommonFacade>
    implements ChannelCommonRemoteProxy {

    @Override
    public ChannelCommonResult applyManager(ChannelRequest request) {
        logger.info("[CMF->CommonChannel]渠道请求信息: request={}", LogFilterUtil.filter(request.toString()));
        ChannelCommonResult result = null;
        try {
            result = super.getTarget(request.getFundChannelCode(), request.getApiType(), request.getApiUrl(), null)
                .apply(InstCovertUtil.convert(request));

        } catch (Exception e) {
            // 异常移除缓存创建的实例 2012.09.07 malianhao
            cachedMapping.remove(complentApiKey(request.getFundChannelCode(), request.getApiType(),
                complentApiUrl(request.getApiUrl())));
            logger.error("[CMF<-CommonChannel]渠道请求异常", e);
            throw new CommunicateException(e);
        }

        logger.info("[CMF<-CommonChannel]渠道返回结果:result={}", result);
        return result;
    }

}
