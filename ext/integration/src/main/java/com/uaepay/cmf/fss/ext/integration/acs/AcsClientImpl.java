package com.uaepay.cmf.fss.ext.integration.acs;

import com.uaepay.acs.service.facade.legacy.SettingFacade;
import com.uaepay.acs.service.facade.legacy.common.MerchantSetting;
import com.uaepay.acs.service.facade.legacy.request.MerchantSettingRequest;
import com.uaepay.acs.service.facade.legacy.response.MerchantSettingResponse;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date AcsClientImpl.java v1.0
 */
@Slf4j
@Service
public class AcsClientImpl implements AcsClient, BasicConstant {

    @Reference
    private SettingFacade settingFacade;

    /**
     * 查询商户配置
     * @param merchantId
     * @param paramKey
     * @return
     */
    @Override
    @Cacheable(cacheNames = CACHE_NAMESPACE_ACS_CONFIG, key = "#merchantId + '-' +#paramKey")
    public MerchantSetting getMerchantSetting(String merchantId, String paramKey) {
        MerchantSettingRequest request = new MerchantSettingRequest();
        request.setMerchantId(merchantId);
        request.setParamKey(paramKey);
        log.info("AcsClient.getMerchantSetting.request:{}", request);
        MerchantSettingResponse response = settingFacade.getMerchantSetting(request);
        log.info("AcsClient.getMerchantSetting.response:{}", response);
        return response == null || CollectionUtils.isEmpty(response.getMerchantSettingList())
                ? null : response.getMerchantSettingList().get(0);
    }

}
