package com.uaepay.cmf.fss.ext.integration.escrow;

import com.alibaba.fastjson.JSON;
import com.uaepay.cmf.common.core.domain.enums.SvaAccountEnum;
import com.uaepay.cmf.common.core.domain.vo.EscrowTransformOrder;
import com.uaepay.cmf.service.facade.domain.query.SimpleOrder;
import com.uaepay.member.service.facade.IMemberKycFacade;
import com.uaepay.member.service.request.member.QueryKycInfoRequest;
import com.uaepay.member.service.response.QueryKycInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.core.ExchangeBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date EscrowClientImpl.java v1.0  2020-10-16 15:34
 */
@Slf4j
@Service
public class EscrowClientImpl implements EscrowClient {

    @Autowired
    AmqpAdmin amqpAdmin;

    @Autowired
    private AmqpTemplate amqpTemplate;
    @Reference
    private IMemberKycFacade iMemberKycFacade;

    private static final String ROUTING_SIMPLE_ORDER = "routing.cmf.simpleOrder";

    private static final String ROUTING_TRANSFORM_ORDER = "routing.cmf.transform.order";

    @Override
    public void sendOrder(SimpleOrder simpleOrder) {
        try {
            amqpTemplate.convertAndSend(CMF_EXCHANGE_NOTIFY, ROUTING_SIMPLE_ORDER, JSON.toJSONString(simpleOrder));
            log.info("EscrowClient-{}-notify success", simpleOrder);
        } catch (Exception e) {
            log.error("EscrowClient.sendOrder.error", e);
        }
    }

    @Override
    public void sendTransformOrder(EscrowTransformOrder order) {
        try {
            amqpTemplate.convertAndSend(CMF_EXCHANGE_NOTIFY, ROUTING_TRANSFORM_ORDER, JSON.toJSONString(order));
            log.info("EscrowClient.sendTransformOrder-{}-notify success", order);
        } catch (Exception e) {
            log.error("EscrowClient.sendTransformOrder.error", e);
        }
    }

    @Override
    public SvaAccountEnum querySvaAccount(String memberId) {

        if (StringUtils.isEmpty(memberId) || ANONYMOUS_MEMBER_ID.equals(memberId)){
            return SvaAccountEnum.NO_SVA;
        }

        QueryKycInfoRequest request = new QueryKycInfoRequest();
        request.setMemberId(memberId);
        request.setClientId(CLIENT_ID);
        QueryKycInfoResponse response = iMemberKycFacade.queryKycInfo(request);

        if (VIP_TYPE.equals(response.getKycType())){
            return SvaAccountEnum.VIP;
        }

        return response.isAvailable() ? SvaAccountEnum.HAS_SVA : SvaAccountEnum.NO_SVA;
    }

    @PostConstruct
    public void init() {
        // exchange类型按业务特性自行选择
        amqpAdmin.declareExchange(ExchangeBuilder.fanoutExchange(CMF_EXCHANGE_NOTIFY).durable(true).build());
        log.info("已创建exchange: {}", CMF_EXCHANGE_NOTIFY);
    }

    private static final String VIP_TYPE = "VIP";
}
