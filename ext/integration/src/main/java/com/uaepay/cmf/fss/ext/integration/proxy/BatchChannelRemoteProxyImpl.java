package com.uaepay.cmf.fss.ext.integration.proxy;

import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.fss.ext.common.api.ChannelFundBatchFacade;
import com.uaepay.cmf.fss.ext.integration.common.AbstractRemoteProxy;
import com.uaepay.cmf.fss.ext.integration.util.InstCovertUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 渠道批量发送代理实现
 * </p>
 * 支持业务如下：
 * <ul>
 * <li>批量退款</li>
 * <li>批量支付</li>
 * <li>批量查询</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version $Id: BatchChannelRemoteProxyImpl.java, v 0.1 2012-8-16 下午3:55:34 malianhao Exp $
 */
@Service("batchChannelRemoteProxy")
public class BatchChannelRemoteProxyImpl extends AbstractRemoteProxy<ChannelFundBatchFacade>
    implements BatchChannelRemoteProxy {

    @Value("${cmf.batch.query.timeoutMillis:120000}")
    private Long batchQueryTimeoutMillis;

    @Override
    public ChannelFundBatchResult applyFund(ChannelFundRequest request) {
        logger.info("[CMF->BatchChannel]渠道批量资金请求信息: instOrderNo=" + request.getInstOrderNo() + ", apiType="
            + request.getApiType().getCode());
        ChannelFundBatchResult result = null;
        try {
            result = super.getTarget(request.getFundChannelCode(), request.getApiType(), request.getApiUrl(), null)
                .apply(InstCovertUtil.convert(request));
        } catch (Exception e) {
            // 异常移除缓存创建的实例 2012.09.07 malianhao
            cachedMapping.remove(complentApiKey(request.getFundChannelCode(), request.getApiType(),
                complentApiUrl(request.getApiUrl())));
            logger.error("[CMF<-BatchChannel]渠道批量资金请求异常: instOrderNo=" + request.getInstOrderNo() + ", apiType="
                + request.getApiType().getCode() + ", apiUri=" + request.getApiUrl(), e);
            throw new CommunicateException(e);
        }

        logger.info("[CMF<-BatchChannel]渠道批量资金返回结果: instOrderNo=" + request.getInstOrderNo() + ", apiType="
            + request.getApiType().getCode());

        return result;

    }

    @Override
    public ChannelFundBatchResult applyQuery(ChannelRequest request) {
        logger.info("[CMF->BatchChannel]渠道批量查询请求信息: instOrderNo=" + request.getInstOrderNo() + ", apiType="
            + request.getApiType().getCode());
        ChannelFundBatchResult result = null;
        try {
            result = super.getTarget(request.getFundChannelCode(), request.getApiType(), request.getApiUrl(), batchQueryTimeoutMillis)
                .apply(InstCovertUtil.convert(request));
        } catch (Exception e) {
            // 异常移除缓存创建的实例 2012.09.07 malianhao
            cachedMapping.remove(complentApiKey(request.getFundChannelCode(), request.getApiType(),
                complentApiUrl(request.getApiUrl())));
            logger.error("[CMF<-BatchChannel]渠道批量查询请求异常: instOrderNo=" + request.getInstOrderNo() + ", apiType="
                + request.getApiType().getCode() + ", apiUri=" + request.getApiUrl(), e);
            throw new CommunicateException(e);
        }
        logger.info("[CMF<-BatchChannel]渠道批量查询返回结果: instOrderNo=" + request.getInstOrderNo() + ", apiType="
            + request.getApiType().getCode());

        return result;

    }

}
