package com.uaepay.cmf.fss.ext.integration.proxy;

import com.uaepay.cmf.common.domain.ChannelCommonResult;
import com.uaepay.cmf.common.domain.ChannelRequest;

/**
 * 功能描述：远程调用基础接口 * <br/>
 * 支持业务如下：
 * <ul>
 * <li>签到</li>
 * <li>签退</li>
 * <li>批结</li>
 * <li>查询</li>
 * <li>鉴权</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version $Id: ChannelCommonRemoteProxy.java, v 1.0 2012-8-16 下午5:38:25 malianhao Exp $
 */
public interface ChannelCommonRemoteProxy {

    /**
     * 转发POS相关的管理请求
     *
     * @param request
     *            请求参数
     *
     * @return 处理结果
     */
    ChannelCommonResult applyManager(ChannelRequest request);
}
