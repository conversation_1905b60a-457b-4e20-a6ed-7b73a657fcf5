<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:http-conf="http://cxf.apache.org/transports/http/configuration"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
      http://www.springframework.org/schema/beans/spring-beans-3.0.xsd

      http://cxf.apache.org/transports/http/configuration
      http://cxf.apache.org/schemas/configuration/http-conf.xsd"

       default-autowire="byName">

    <!-- WS timeout: out=10s in=5s -->
    <http-conf:conduit name="*.http-conduit">
        <http-conf:client ConnectionTimeout="20000" ReceiveTimeout="10000" Connection="Keep-Alive"
                          MaxRetransmits="1" AllowChunking="false"/>
    </http-conf:conduit>

    <bean id="uesService" class="com.uaepay.ues.UesClient"/>

    <!-- UES Client -->
    <bean id="uesClient" class="com.uaepay.cmf.fss.ext.integration.ues.UesClientImpl"/>

    <bean id="abstractChannelSenderFactory"
          class="com.uaepay.cmf.fss.ext.integration.factory.AbstractChannelSenderFactory" abstract="true">
        <property name="singleProxyMap">
            <map>
                <entry key="ws" value-ref="channelRemoteProxy"/>
                <entry key="dubbo" value-ref="dubboChannelFundRemoteProxy"/>
            </map>
        </property>
        <property name="commonProxyMap">
            <map>
                <entry key="ws" value-ref="channelCommonRemoteProxy"/>
                <entry key="dubbo" value-ref="dubboChannelCommonRemoteProxy"/>
            </map>
        </property>
        <property name="batchProxyMap">
            <map>
                <entry key="ws" value-ref="batchChannelRemoteProxy"/>
                <entry key="dubbo" value-ref="dubboBatchChannelRemoteProxy"/>
            </map>
        </property>
    </bean>

</beans>
