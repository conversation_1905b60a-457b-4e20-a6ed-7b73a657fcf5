<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.uaepay.fund.cmf</groupId>
        <artifactId>cmf-ext-parent</artifactId>
        <version>1.1.19-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cmf-ext-integration</artifactId>

    <properties>
        <maven.install.skip>false</maven.install.skip>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.uaepay.fund.cmf</groupId>
            <artifactId>cmf-service-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.uaepay.fund.cmf</groupId>
            <artifactId>cmf-core-util</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mvel2</artifactId>
                    <groupId>org.mvel</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-mapper-asl</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.fund.cmf</groupId>
            <artifactId>cmf-ext-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.fund.cmf</groupId>
            <artifactId>cmf-core-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.fund.cmf</groupId>
            <artifactId>cmf-ext-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uaepay.starter</groupId>
            <artifactId>dubbo-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.common.basic</groupId>
            <artifactId>basic-util</artifactId>
        </dependency>
        <!-- MNS -->
        <dependency>
            <groupId>com.uaepay.public.mns</groupId>
            <artifactId>mns-service-facade</artifactId>
        </dependency>

        <!-- UES -->
        <dependency>
            <groupId>com.uaepay.basis.ues</groupId>
            <artifactId>ues-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.public.router</groupId>
            <artifactId>router-service-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.member</groupId>
            <artifactId>member-facade</artifactId>
        </dependency>
        <!-- grc cps -->
        <dependency>
            <groupId>com.uaepay.grc</groupId>
            <artifactId>grc-cps-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uaepay.grc</groupId>
            <artifactId>grc-component-connect-api</artifactId>
        </dependency>


        <dependency>
            <groupId>com.uaepay.escrow</groupId>
            <artifactId>escrow-service-facade</artifactId>
        </dependency>

        <!-- cxf spring boot-->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>cxf-rt-bindings-soap</artifactId>
                    <groupId>org.apache.cxf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cxf-rt-wsdl</artifactId>
                    <groupId>org.apache.cxf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>cards-service-facade</artifactId>
            <groupId>com.uaepay.channel.cards</groupId>
        </dependency>


    </dependencies>

</project>
