<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.uaepay.fund.cmf</groupId>
        <artifactId>cmf-ext-parent</artifactId>
        <version>1.1.19-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cmf-ext-common</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.uaepay.fund.cmf</groupId>
            <artifactId>cmf-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.fund.cmf</groupId>
            <artifactId>cmf-service-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uaepay.basis.beacon</groupId>
            <artifactId>beacon-service-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.common.basic</groupId>
            <artifactId>basic-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.common.basic</groupId>
            <artifactId>basic-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.starter</groupId>
            <artifactId>redis-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>
</project>
