package com.uaepay.cmf.fss.ext.common.domain.authenticate;

import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.common.domain.base.BankCardInfo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * <p>授权请求参数</p>
 * <AUTHOR>
 * @version $Id: AuthenticateRequest.java, v 0.1 2012-9-17 上午10:48:22 liumaoli Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class AuthenticateRequest extends ChannelRequest {

    private static final long serialVersionUID = 1589332562770963532L;
    /** 卡信息 */
    private BankCardInfo      bankCardInfo;
    /** 手机号 */
    private String            mobilePhoneNo;

}