package com.uaepay.cmf.fss.ext.common.api;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.ws.Action;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

import com.uaepay.cmf.common.domain.ChannelCommonResult;

/**
 * <p>渠道通用类统一门面</p>
 * 渠道所有非支付请求通过此门面进行交互
 * <br/>
 * 支持业务如下：
 * <ul>
 *  <li>查询</li>
 *  <li>鉴权</li>
 *  <li>支付推进</li>
 *  <li>开户/查询余额</li>
 * </ul>
 * <AUTHOR>
 * @version $Id: ChannelCommonFacade.java, v 0.1 2011-10-2 上午09:36:09 sean won Exp $
 */
@WebService(targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/", name = "ChannelCommonFacade")
public interface ChannelCommonFacade {
    /**
     * 通用请求
     * 请求对象及结果对象根据不同的业务大类进行扩展
     * @param request
     * @return
     */
    @WebResult(name = "channelCommonResult", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/")
    @Action(input = "http://api.common.ext.fss.cmf.uaepay.com/ChannelCommonFacade/apply", output = "http://api.common.ext.fss.cmf.uaepay.com/ChannelCommonFacade/applyResponse")
    @RequestWrapper(localName = "apply", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/", className = "com.uaepay.cmf.fss.ext.common.api.ChannelCommonFacade_Type")
    @WebMethod(action = "http://api.common.ext.fss.cmf.uaepay.com/ChannelCommonFacade/apply")
    @ResponseWrapper(localName = "applyResponse", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/", className = "com.uaepay.cmf.fss.ext.common.api.ChannelCommonFacadeResponse")
    ChannelCommonResult apply(@WebParam(name = "arg0", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/") String request);
}
