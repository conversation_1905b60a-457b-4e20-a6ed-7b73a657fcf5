package com.uaepay.cmf.fss.ext.common.channel.processor.factory;

import java.util.Map;

import com.uaepay.cmf.common.enums.FundChannelApiType;

public class AbstractProcessorFactory<T> implements ProcessorFactory {

    protected Map<String, T> container;

    public Map<String, T> getContainer() {
        return container;
    }

    public void setContainer(Map<String, T> container) {
        this.container = container;
    }

    @Override
    public T getProcessor(String fundChannelCode, FundChannelApiType apiType) {
        return container.get(fundChannelCode + "-" + apiType.getCode());
    }

}
