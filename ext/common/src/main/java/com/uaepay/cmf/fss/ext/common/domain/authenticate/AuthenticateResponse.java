package com.uaepay.cmf.fss.ext.common.domain.authenticate;

import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.schema.cmf.enums.CardType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 
 * <p>授权返回参数</p>
 * <AUTHOR>
 * @version $Id: AuthenticateResponse.java, v 0.1 2012-9-17 上午10:48:04 liumaoli Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class AuthenticateResponse extends ChannelResult {

    private static final long serialVersionUID = 6883842702605340727L;
    /**
     * 渠道编号
     */
    private String            fundChannelCode;
    /** 协议号  */
    private String            signNo;
    /** 签约日期 */
    private Date              signDate;

}
