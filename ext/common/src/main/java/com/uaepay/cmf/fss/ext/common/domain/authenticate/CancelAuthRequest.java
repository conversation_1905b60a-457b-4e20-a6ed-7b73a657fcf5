package com.uaepay.cmf.fss.ext.common.domain.authenticate;

import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.common.domain.base.BankCardInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * <p>撤销预授权请求参数</p>
 * <AUTHOR>
 * @version $Id: CancelAuthRequest.java, v 0.1 2012-9-19 下午5:35:12 liumaoli Exp $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class CancelAuthRequest extends ChannelRequest {

    private static final long serialVersionUID = 8400669733770172472L;
    /** 协议号 */
    private String            signNo;
    /** 卡号信息 */
    private BankCardInfo      bankCardInfo;
    /** 手机号 */
    private String            mobilePhoneNo;

}
