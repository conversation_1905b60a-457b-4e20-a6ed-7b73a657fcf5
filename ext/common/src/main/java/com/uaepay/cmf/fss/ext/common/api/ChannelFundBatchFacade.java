package com.uaepay.cmf.fss.ext.common.api;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.ws.Action;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

import com.uaepay.cmf.common.domain.ChannelFundBatchResult;

/**
 * <p>渠道资金批量处理门面</p>
 * 支持业务如下：
 * <ul>
 *  <li>批量退款</li>
 *  <li>批量出款</li>
 *  <li>批量查询</li>
 * </ul>
 * <AUTHOR>
 * @version $Id: ChannelFundBatchFacade.java, v 0.1 2012-8-10 上午10:52:05 fuyangbiao Exp $
 */
@WebService(targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/", name = "ChannelFundBatchFacade")
public interface ChannelFundBatchFacade {
    /**
     * 批量申请
     * 与支付相关渠道请求由此发起
     * 请求对象及结果对象根据不同的业务大类进行扩展
     * @param request
     * @return
     */
    @WebResult(name = "channelFundBatchResult", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/")
    @Action(input = "http://api.common.ext.fss.cmf.uaepay.com/ChannelFundBatchFacade/apply", output = "http://api.common.ext.fss.cmf.uaepay.com/ChannelFundBatchFacade/applyResponse")
    @RequestWrapper(localName = "apply", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/", className = "com.uaepay.cmf.fss.ext.common.api.ChannelFundBatchFacade_Type")
    @WebMethod(action = "http://api.common.ext.fss.cmf.uaepay.com/ChannelFundBatchFacade/apply")
    @ResponseWrapper(localName = "applyResponse", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/", className = "com.uaepay.cmf.fss.ext.common.api.ChannelFundBatchFacadeResponse")
    ChannelFundBatchResult apply(@WebParam(name = "arg0", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/") String request);
}
