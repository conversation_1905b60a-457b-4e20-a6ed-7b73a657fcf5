package com.uaepay.cmf.fss.ext.common.domain.authenticate;

import com.uaepay.cmf.common.domain.ChannelRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 21/02/2025 15:36
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChannelRetrieveCardMetadataRequest extends ChannelRequest {
    private static final long serialVersionUID = -7718027822537511590L;

    private String cardNo;

}
