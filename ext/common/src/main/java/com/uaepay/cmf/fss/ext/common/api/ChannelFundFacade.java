package com.uaepay.cmf.fss.ext.common.api;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.ws.Action;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

import com.uaepay.cmf.common.domain.ChannelFundResult;

/**
 * <p>渠道资金类统一门面</p>
 * 渠道所有支付相关请求通过此门面进行交互
 * <br/>
 * 支持业务如下：
 * <ul>
 *  <li>消费/撤销</li>
 *  <li>退款</li>
 *  <li>出款</li>
 * </ul>
 * <AUTHOR>
 * @version $Id: ChannelFundFacade.java, v 0.1 2011-10-2 上午09:19:05 sean won Exp $
 */
@WebService(targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/", name = "ChannelFundFacade")
public interface ChannelFundFacade {
    /**
     * 单笔渠道申请
     * 与支付相关渠道请求由此发起
     * 请求对象及结果对象根据不同的业务大类进行扩展
     * @param request
     * @return
     */
    @WebResult(name = "channelResult", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/")
    @Action(input = "http://api.common.ext.fss.cmf.uaepay.com/ChannelFundFacade/apply", output = "http://api.common.ext.fss.cmf.uaepay.com/ChannelFundFacade/applyResponse")
    @RequestWrapper(localName = "apply", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/", className = "com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade_Type")
    @WebMethod(action = "http://api.common.ext.fss.cmf.uaepay.com/ChannelFundFacade/apply")
    @ResponseWrapper(localName = "applyResponse", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/", className = "com.uaepay.cmf.fss.ext.common.api.ChannelFundFacadeResponse")
    ChannelFundResult apply(@WebParam(name = "arg0", targetNamespace = "http://api.common.ext.fss.cmf.uaepay.com/") String request);
}
