<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:reg="http://www.dangdang.com/schema/ddframe/reg"
       xmlns:job="http://www.dangdang.com/schema/ddframe/job"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.dangdang.com/schema/ddframe/reg http://www.dangdang.com/schema/ddframe/reg/reg.xsd http://www.dangdang.com/schema/ddframe/job http://www.dangdang.com/schema/ddframe/job/job.xsd"
       default-autowire="byName">

    <reg:zookeeper id="regCenter" server-lists="${elastic.job.serverLists}" namespace="${elastic.job.namespace}"
                   base-sleep-time-milliseconds="${elastic.job.baseSleepTimeMilliseconds}"
                   max-sleep-time-milliseconds="${elastic.job.maxSleepTimeMilliseconds}"
                   max-retries="${elastic.job.maxRetries}"/>

    <bean id="singleQueryTask" class="com.uaepay.cmf.fss.ext.resend.newtask.SingleQueryTask">
        <property name="threadPoolTaskExecutor">
            <bean class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
                <property name="corePoolSize" value="${singleQuery.task.corePoolSize:10}"/>
                <property name="maxPoolSize" value="${singleQuery.task.maxPoolSize:30}"/>
                <property name="queueCapacity" value="${singleQuery.task.queueCapacity:1000}"/>
                <property name="rejectedExecutionHandler">
                    <bean class="java.util.concurrent.ThreadPoolExecutor$DiscardOldestPolicy"/>
                </property>
            </bean>
        </property>
    </bean>
    <job:simple id="cmf_singleQuery" job-ref="singleQueryTask"
                registry-center-ref="regCenter" sharding-total-count="1"
                disabled="${elastic.job.singleQuery.disable}"
                cron="${elastic.job.singleQuery.crontab}" overwrite="false"
                description="cmf-单笔查询任务:查询机构订单"
    />

    <bean id="batchQueryTask" class="com.uaepay.cmf.fss.ext.resend.newtask.BatchQueryTask">
        <property name="threadPoolTaskExecutor">
            <bean class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
                <property name="corePoolSize" value="${batchQuery.task.corePoolSize:5}"/>
                <property name="maxPoolSize" value="${batchQuery.task.maxPoolSize:10}"/>
                <property name="queueCapacity" value="${batchQuery.task.queueCapacity:1000}"/>
                <property name="rejectedExecutionHandler">
                    <bean class="java.util.concurrent.ThreadPoolExecutor$DiscardOldestPolicy"/>
                </property>
            </bean>
        </property>
    </bean>
    <job:simple id="cmf_batchQuery" job-ref="batchQueryTask"
                registry-center-ref="regCenter" sharding-total-count="1"
                disabled="${elastic.job.batchQuery.disable}"
                cron="${elastic.job.batchQuery.crontab}" overwrite="false"
                description="cmf-批量查询任务"
    />

    <bean id="batchSendTask" class="com.uaepay.cmf.fss.ext.resend.newtask.BatchSendTask"/>
    <job:simple id="cmf_batchSend" job-ref="batchSendTask"
                registry-center-ref="regCenter" sharding-total-count="1"
                disabled="${elastic.job.batchSend.disable}"
                cron="${elastic.job.batchSend.crontab}" overwrite="false"
                description="cmf-批量出款Task任务"
    />

    <!--    <bean id="singleSendTask" class="com.uaepay.cmf.fss.ext.resend.newtask.SingleSendTask"/>-->
    <!--    <job:simple id="cmf_singleSend" job-ref="singleSendTask"-->
    <!--                registry-center-ref="regCenter" sharding-total-count="1"-->
    <!--                disabled="${elastic.job.singleSend.disable}"-->
    <!--                cron="${elastic.job.singleSend.crontab}" overwrite="false"-->
    <!--                description="cmf-单笔发送任务,发送出款、退款等异步订单"-->
    <!--    />-->


    <!--    <bean id="controlQueryTask" class="com.uaepay.cmf.fss.ext.resend.newtask.ControlQueryTask">-->
    <!--        <property name="threadPoolTaskExecutor">-->
    <!--            <bean class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">-->
    <!--                <property name="corePoolSize" value="${controlQuery.task.corePoolSize:10}"/>-->
    <!--                <property name="maxPoolSize" value="${controlQuery.task.maxPoolSize:30}"/>-->
    <!--                <property name="queueCapacity" value="${controlQuery.task.queueCapacity:1000}"/>-->
    <!--                <property name="rejectedExecutionHandler">-->
    <!--                    <bean class="java.util.concurrent.ThreadPoolExecutor$DiscardOldestPolicy"/>-->
    <!--                </property>-->
    <!--            </bean>-->
    <!--        </property>-->
    <!--    </bean>-->
    <!--    <job:simple id="cmf_controlQuery" job-ref="controlQueryTask"-->
    <!--                registry-center-ref="regCenter" sharding-total-count="1"-->
    <!--                disabled="${elastic.job.controlQuery.disable}"-->
    <!--                cron="${elastic.job.controlQuery.crontab}" overwrite="false"-->
    <!--                description="cmf-控制订单查询, 查询控制订单"-->
    <!--    />-->

    <!--    <bean id="fileMigrateTask" class="com.uaepay.cmf.fss.ext.resend.newtask.FileMigrateTask"/>-->
    <!--    <job:simple id="cmf_fileMigrate" job-ref="fileMigrateTask"-->
    <!--                registry-center-ref="regCenter" sharding-total-count="1"-->
    <!--                disabled="${elastic.job.fileMigrate.disable}"-->
    <!--                cron="${elastic.job.fileMigrate.crontab}" overwrite="false"-->
    <!--                description="cmf-bdcBalanceCheck"-->
    <!--    />-->

    <bean id="bdcBalanceCheckTask" class="com.uaepay.cmf.fss.ext.resend.schedule.BdcBalanceCheckTask"/>
    <job:simple id="cmf_bdcBalanceCheck" job-ref="bdcBalanceCheckTask"
                registry-center-ref="regCenter" sharding-total-count="1"
                disabled="${elastic.job.bdcBalanceCheck.disable}"
                cron="${elastic.job.bdcBalanceCheck.crontab}" overwrite="false"
                description="cmf-bdcBalanceCheck"
    />


    <bean id="monitorLogSendTask" class="com.uaepay.cmf.fss.ext.resend.schedule.MonitorLogSendTask"/>
    <job:simple id="cmf_monitorLogSend" job-ref="monitorLogSendTask"
                registry-center-ref="regCenter" sharding-total-count="1"
                disabled="${elastic.job.monitorLogSend.disable}"
                cron="${elastic.job.monitorLogSend.crontab}" overwrite="false"
                description="cmf-监控日志邮件发送任务"
    />

    <bean id="taskLockService" class="com.uaepay.cmf.fss.ext.resend.lock.TaskLockServiceImpl"/>

    <!-- 异步线程池 -->
    <bean id="notifyChannelExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数，默认为1 -->
        <property name="corePoolSize" value="${cmf.notify.corePoolSize}"/>
        <!-- 最大线程数，默认为Integer.MAX_VALUE -->
        <property name="maxPoolSize" value="${cmf.notify.maxPoolSize}"/>
        <!-- 队列最大长度，默认为Integer.MAX_VALUE -->
        <property name="queueCapacity" value="${cmf.notify.queueCapacity}"/>
        <!-- 线程池维护线程所允许的空闲时间，默认为60s -->
        <property name="keepAliveSeconds" value="${cmf.notify.keepAliveSeconds}"/>
        <!-- 线程池对拒绝任务（超过待处理队列长度）的处理策略，目前只支持AbortPolicy、CallerRunsPolicy；默认为后者 -->
        <property name="rejectedExecutionHandler">
            <!-- AbortPolicy:直接抛出java.util.concurrent.RejectedExecutionException异常 -->
            <!-- CallerRunsPolicy:若已达到待处理队列长度，将由主线程直接处理请求 -->
            <!-- DiscardOldestPolicy:抛弃旧的任务；会导致被丢弃的任务无法再次被执行 -->
            <!-- DiscardPolicy:抛弃当前任务；会导致被丢弃的任务无法再次被执行 -->
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>

    <bean id="batchChannelExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数，默认为1 -->
        <property name="corePoolSize" value="${cmf.batch.corePoolSize}"/>
        <!-- 最大线程数，默认为Integer.MAX_VALUE -->
        <property name="maxPoolSize" value="${cmf.batch.maxPoolSize}"/>
        <!-- 队列最大长度，默认为Integer.MAX_VALUE -->
        <property name="queueCapacity" value="${cmf.batch.queueCapacity}"/>
        <!-- 线程池维护线程所允许的空闲时间，默认为60s -->
        <property name="keepAliveSeconds" value="${cmf.batch.keepAliveSeconds}"/>
        <!-- 线程池对拒绝任务（超过待处理队列长度）的处理策略，目前只支持AbortPolicy、CallerRunsPolicy；默认为后者 -->
        <property name="rejectedExecutionHandler">
            <!-- AbortPolicy:直接抛出java.util.concurrent.RejectedExecutionException异常 -->
            <!-- CallerRunsPolicy:若已达到待处理队列长度，将由主线程直接处理请求 -->
            <!-- DiscardOldestPolicy:抛弃旧的任务；会导致被丢弃的任务无法再次被执行 -->
            <!-- DiscardPolicy:抛弃当前任务；会导致被丢弃的任务无法再次被执行 -->
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>

    <!-- 异步线程池 -->
    <bean id="channelQueryExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数，默认为1 -->
        <property name="corePoolSize" value="5"/>
        <!-- 最大线程数，默认为Integer.MAX_VALUE -->
        <property name="maxPoolSize" value="10"/>
        <!-- 队列最大长度，默认为Integer.MAX_VALUE -->
        <property name="queueCapacity" value="500"/>
        <!-- 线程池维护线程所允许的空闲时间，默认为60s -->
        <property name="keepAliveSeconds" value="200"/>
        <!-- 线程池对拒绝任务（超过待处理队列长度）的处理策略，目前只支持AbortPolicy、CallerRunsPolicy；默认为后者 -->
        <property name="rejectedExecutionHandler">
            <!-- AbortPolicy:直接抛出java.util.concurrent.RejectedExecutionException异常 -->
            <!-- CallerRunsPolicy:若已达到待处理队列长度，将由主线程直接处理请求 -->
            <!-- DiscardOldestPolicy:抛弃旧的任务；会导致被丢弃的任务无法再次被执行 -->
            <!-- DiscardPolicy:抛弃当前任务；会导致被丢弃的任务无法再次被执行 -->
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>


    <!-- 待迁移 -->
    <bean id="abstractResultProcessor" class="com.uaepay.cmf.fss.ext.resend.processor.AbstractResultProcessor"
          abstract="true"/>

    <bean id="queryResultProcessor" class="com.uaepay.cmf.fss.ext.resend.processor.QueryResultProcessor"
          parent="abstractResultProcessor">
        <property name="resultHandlerMap">
            <map>
                <entry key="DEFAULT" value-ref="defaultResultHandler"/>
                <entry key="RETRY" value-ref="retryResultHandler"/>
                <entry key="RETRY_NEW_ORDER" value-ref="retryNewOrderResultHandler"/>
                <entry key="COUNTER" value-ref="counterResultHandler"/>
            </map>
        </property>
    </bean>

    <bean id="batchQueryResultProcessor" class="com.uaepay.cmf.fss.ext.resend.processor.BatchQueryResultProcessor"
          parent="abstractResultProcessor">
        <property name="resultHandlerMap">
            <map>
                <entry key="BATCH_DEFAULT" value-ref="batchDefaultResultHandler"/>
                <entry key="BATCH_RETRY" value-ref="batchRetryResultHandler"/>
            </map>
        </property>
    </bean>

    <bean id="notifyChannelSingleThreadExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="1"/>
        <property name="maxPoolSize" value="1"/>
        <property name="queueCapacity" value="1000"/>
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$DiscardPolicy"/>
        </property>
    </bean>

</beans>
