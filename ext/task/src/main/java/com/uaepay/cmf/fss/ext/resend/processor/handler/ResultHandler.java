package com.uaepay.cmf.fss.ext.resend.processor.handler;

import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.common.core.domain.institution.InstCommonOrder;
import com.uaepay.cmf.common.domain.ChannelResult;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version ResultHandler.java 1.0 Created@2017-12-04 13:27 $
 */
public interface ResultHandler<Order extends InstCommonOrder, Result extends ChannelResult, Response extends InstBaseResult> {

    /**
     * 处理渠道结果
     * @param order
     * @param result
     * @return
     */
    Response handle(Order order, Result result);

}
