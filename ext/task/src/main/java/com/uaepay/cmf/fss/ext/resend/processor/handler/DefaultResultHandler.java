package com.uaepay.cmf.fss.ext.resend.processor.handler;

import javax.annotation.Resource;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor;
import org.springframework.stereotype.Service;

/**
 * <p>默认结果处理器.</p>
 *
 * <AUTHOR>
 * @version DefaultResultHandler.java 1.0 Created@2017-12-04 13:28 $
 */
@Service("defaultResultHandler")
public class DefaultResultHandler implements
        ResultHandler<InstOrder, ChannelFundResult, InstOrderResult> {

    @Resource
    private InstResultProcessor instResultProcessor;

    @Override
    public InstOrderResult handle(InstOrder instOrder, ChannelFundResult channelFundResult) {
        if (channelFundResult == null) {
            return new InstOrderResult();
        }
        return instResultProcessor.process(instOrder, channelFundResult);
    }
}
