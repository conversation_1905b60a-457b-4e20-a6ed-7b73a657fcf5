package com.uaepay.cmf.fss.ext.resend.processor.handler;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.ManualRefundType;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.domainservice.main.process.RefundProcessService;
import com.uaepay.cmf.domainservice.main.process.ResendProcessor;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import org.springframework.stereotype.Service;

/**
 * <p>发送Counter.</p>
 *
 * <AUTHOR>
 * @version RetryResultHandler.java 1.0 Created@2017-12-04 13:29 $
 */
@Service("counterResultHandler")
public class CounterResultHandler implements ResultHandler<InstOrder, ChannelFundResult, InstOrderResult> {
    private Logger                 logger = LoggerFactory.getLogger(CounterResultHandler.class);

    @Resource
    private ResendProcessor        resendProcessor;

    @Resource
    private InstOrderRepository    instOrderRepository;

    @Resource
    private RefundProcessService   refundProcessService;

    @Override
    public InstOrderResult handle(InstOrder instOrder, ChannelFundResult channelFundResult) {
        try {

            ManualRefundType refundType = ManualRefundType.getByCode(instOrder.getMemo());
            refundType = refundType == null ? ManualRefundType.REFUND_NON_RETURN_RESULT
                : refundType;
            refundProcessService.processRefund(instOrder, refundType);
            instOrderRepository.updateCommunicateStatusWithPreStatus(instOrder,
                CommunicateStatus.AWAITING, instOrder.getCommunicateStatus());
            return resendProcessor.process(instOrder);
        } catch (Exception e) {
            logger.info("QueryResultProcessor.send.error", e);
            return null;
        }
    }
}
