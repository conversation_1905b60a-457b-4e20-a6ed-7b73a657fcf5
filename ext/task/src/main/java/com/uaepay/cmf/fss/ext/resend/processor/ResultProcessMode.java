package com.uaepay.cmf.fss.ext.resend.processor;

/**
 * <p>处理模式.</p>
 *
 * <AUTHOR>
 * @version ProcessMode.java 1.0 Created@2017-12-04 11:15 $
 */
public enum ResultProcessMode {

    // 渠道处理模式
    DEFAULT("D", "默认"),
    RETRY("R", "重试"),
    COUNTER("C", "发送到Counter"),
    BATCH_DEFAULT("BD", "批量默认"),
    BATCH_RETRY("BR","批量重试"),
    ;

    private String code;
    private String message;

    ResultProcessMode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}

