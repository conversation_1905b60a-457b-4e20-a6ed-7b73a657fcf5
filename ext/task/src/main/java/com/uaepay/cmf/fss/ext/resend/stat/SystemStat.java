package com.uaepay.cmf.fss.ext.resend.stat;

/**
 * <p>系统状态</p>
 * <AUTHOR> won
 * @version $Id: SysStat.java, v 0.1 2010-11-22 下午05:09:59 Yun=sean wonyun02 Exp $
 */
public class SystemStat {
    private static final int MB = 1024 * 1024;
    private static long      totalMemory;
    private static long      freeMemory;
    private static long      maxMemory;

    public static void reload() {
        // 可使用内存
        totalMemory = Runtime.getRuntime().totalMemory() / MB;
        // 剩余内存
        freeMemory = Runtime.getRuntime().freeMemory() / MB;
        // 最大可使用内存
        maxMemory = Runtime.getRuntime().maxMemory() / MB;
    }

    public static long getTotalMemory() {
        return totalMemory;
    }

    public static long getFreeMemory() {
        return freeMemory;
    }

    public static long getMaxMemory() {
        return maxMemory;
    }
}
