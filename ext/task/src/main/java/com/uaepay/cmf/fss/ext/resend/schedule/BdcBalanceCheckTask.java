package com.uaepay.cmf.fss.ext.resend.schedule;

import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import com.uaepay.cmf.common.core.engine.schedule.task.AbstractDaemonTask;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.FundChannelManager;
import com.uaepay.cmf.domainservice.main.process.ChannelBalanceService;
import com.uaepay.cmf.fss.ext.resend.lock.LockDomain;
import com.uaepay.cmf.fss.ext.resend.lock.TaskLockService;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

public class BdcBalanceCheckTask extends AbstractDaemonTask<String> {

    @Resource
    private FundChannelManager fundChannelManager;

    @Resource
    private TaskLockService taskLockService;

    @Resource
    private ChannelBalanceService channelBalanceService;

    @Override
    public DaemonTaskType getTaskType() {
        return new DaemonTaskType("bdcBalanceCheckTask", "银企直连余额报警任务");
    }

    @Override
    protected List<String> loadTask(int batchSize) {
        List<ChannelVO> fundChannels = fundChannelManager.loadByApiType(FundChannelApiType.QUERY_BALANCE);
        return filter(fundChannels);
    }

    private List<String> filter(List<ChannelVO> fundChannels) {
        if (fundChannels == null) {
            return null;
        }
        List<String> list = new ArrayList<>();
        StringBuilder channelStr = new StringBuilder();
        for (ChannelVO channel : fundChannels) {
            if (channel.isAvailable()) {
                list.add(channel.getChannelCode());
                channelStr.append(channel.getChannelCode()).append(",");
            }
        }
        logger.info("BdcBalanceQueryTask query list :" + channelStr.toString());
        return list;
    }

    @Override
    public boolean executeTask(String domain) {
        logger.info("余额查询启动[" + domain + "]");
        LockDomain lockDomain = null;
        try {
            String domainKey = domain.length() > 80 ? domain.substring(0, 80) : domain;
            String lockKey = domainKey + "-bdcBalanceQuery";
            lockDomain = taskLockService.lockTask(lockKey, this.getTaskType(), 120);
        } catch (Exception e) {
            logger.error("[余额查询失败]:锁单异常" + domain, e);
            return false;
        }

        if (!lockDomain.isLockSuccess()) {
            logger.error("[余额查询失败]:锁单异常,可能其它任务在执行" + domain);
            return false;
        }

        try {

            return channelBalanceService.checkBalance(domain, true);
        } catch (Exception e) {
//            mnsNotifyClient.sendMsg(domain + "查询出款余额出错", NotifyProtocol.SNS, "");
            logger.error("[余额查询失败]:" + domain, e);
            return false;
        } finally {
            taskLockService.release(lockDomain);
        }
    }

    @Override
    protected void sendToMonitor(String domain) {

    }

    @Override
    protected void monitorTotalCount(int totalCount) {

    }
}
