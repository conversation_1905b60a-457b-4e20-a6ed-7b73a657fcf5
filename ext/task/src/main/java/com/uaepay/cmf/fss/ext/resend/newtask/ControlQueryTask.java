package com.uaepay.cmf.fss.ext.resend.newtask;

import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.enums.OrderFlag;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import com.uaepay.cmf.common.core.engine.schedule.task.AbstractDaemonTask;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.process.DistributeQueryService;
import com.uaepay.cmf.domainservice.main.process.ResendProcessor;
import com.uaepay.cmf.domainservice.main.process.buffer.ChannelOrderHourCache;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.result.ControlResultProcessor;
import com.uaepay.cmf.fss.ext.resend.lock.LockDomain;
import com.uaepay.cmf.fss.ext.resend.lock.TaskLockService;
import org.apache.skywalking.apm.toolkit.trace.TraceCrossThread;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;

/**
 * <p>控制订单查询, 查询控制订单.</p>
 *
 * <AUTHOR>
 * @version ControlQueryTask.java 1.0 Created@2017-11-30 17:53 $
 */
public class ControlQueryTask extends AbstractDaemonTask<FundChannelApiType> {

    @Resource
    private InstControlOrderRepository instControlOrderRepository;

    @Resource
    private DistributeQueryService distributeQueryService;

    @Resource
    private TaskLockService taskLockService;

    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    private ControlResultProcessor controlResultProcessor;

    @Resource
    private ResendProcessor resendProcessor;

    @Resource
    private SysConfigurationHolder sysConfigurationHolder;

    @Override
    protected List<FundChannelApiType> loadTask(int batchSize) {
        return Arrays.asList(FundChannelApiType.DEBIT_ADVANCE, FundChannelApiType.NOTIFY);
    }

    @Override
    public DaemonTaskType getTaskType() {
        return new DaemonTaskType("controlQueryTask", "控制订单单笔查询");
    }

    @Override
    public boolean executeTask(FundChannelApiType apiType) {
        logger.info("controlQuery.executeTask:{}", apiType);
        LockDomain lockDomain = taskLockService.lockTask(apiType.getCode(), getTaskType(), 120);
        if (!lockDomain.isLockSuccess()) {
            logger.error("controlQuery.lockFail:{}", apiType);
            return false;
        }
        try {

            int maxSize = sysConfigurationHolder.loadConfigureOrDefault(CONTROL_QUERY_MAX_SIZE,
                    DEFAULT_SINGLE_QUERY_MAX_SIZE);

            for (Date[] dateArr : ChannelOrderHourCache.getInstance().getQueryTimeRange(
                    getTaskType().getCode(), 12)) {
                List<Long> orderIdList = instControlOrderRepository.loadControlOrder4Query(apiType,
                        dateArr[0], dateArr[1], maxSize);

                executeFutureTask(orderIdList);
            }
            logger.info("controlQuery.complete");
        } catch (Exception e) {
            logger.error("controlQuery.error", e);
            return false;
        } finally {
            taskLockService.release(lockDomain);
        }
        return true;
    }

    private Map<Long, FutureTask<TaskResult>> executeFutureTask(List<Long> controlOrderIdList)
            throws InterruptedException {
        logger.info("controlQuery.current.process:{}", controlOrderIdList);
        Map<Long, FutureTask<TaskResult>> futureMap = new HashMap<>();
        for (final Long domain : controlOrderIdList) {
            FutureTask<TaskResult> futureTask = new FutureTask<TaskResult>(new FutureTaskCallable(
                    domain));
            futureMap.put(domain, futureTask);
            threadPoolTaskExecutor.execute(futureTask);
        }

        processFutureTaskResult(futureMap);
        return futureMap;
    }

    protected void processFutureTaskResult(Map<Long, FutureTask<TaskResult>> futureMap)
            throws InterruptedException {
        for (Map.Entry<Long, FutureTask<TaskResult>> entry : futureMap.entrySet()) {
            Long domain = entry.getKey();
            String logPrefix = "control.query[" + domain + "]";
            try {
                TaskResult taskResult = entry.getValue().get();
                logger.info("{}执行状态{}", logPrefix, taskResult.isSuccess());
            } catch (InterruptedException e) {
                logger.error(logPrefix + "执行线程执行意外中断误", e);
                throw e;
            } catch (Exception e) {
                logger.error(logPrefix + "执行时出现未知错误", e);
            }
        }
    }

    @TraceCrossThread
    private class FutureTaskCallable implements Callable<TaskResult> {
        /**
         * 订单ID
         */
        private Long domain;

        /**
         * 构造
         *
         * @param domain
         */
        public FutureTaskCallable(Long domain) {
            this.domain = domain;
        }

        @Override
        public TaskResult call() {
            String logPrefix = "订单ID[" + domain + "]";
            try {
                boolean rev = reorder(domain);
                return new TaskResult(rev, "任务执行成功");
            } catch (Exception e) {
                logger.error(logPrefix + "出场时出现未知错误", e);
                return new TaskResult(false, e.getMessage());
            }
        }

    }

    /**
     * 针对一笔订单补单，查询渠道结果.
     *
     * @param orderId
     */
    private boolean reorder(Long orderId) {
        logger.info("controlOrder.begin2Query:{}", orderId);
        int rows = instControlOrderRepository.updateFlagWithOrderIdAndPreFlag(orderId,
                OrderFlag.LOCKED, OrderFlag.DEFAULT);
        if (rows != 1) {
            logger.info("锁定状态更新失败");
            return false;
        }
        try {
            InstControlOrder instControlOrder = instControlOrderRepository.loadWithOrderId(orderId);
            ChannelResult channelResult = distributeQueryService
                    .queryControlResult(instControlOrder);
            if (channelResult == null) {
                return false;
            }
            InstControlOrderResult instControlOrderResult = controlResultProcessor.process(
                    instControlOrder, channelResult);
            boolean needResend = needResend(instControlOrderResult, instControlOrder);
            if (needResend) {
                logger.info("currentOrder.canResend");
                return resendProcessor.process(instControlOrder).getProcessStatus().isFinished();
            }

            return true;
        } catch (Exception e) {
            logger.error("[单笔控制订单查询结果失败]:", e);
            return false;
        } finally {
            instControlOrderRepository.updateFlagWithOrderIdAndPreFlag(orderId, OrderFlag.DEFAULT,
                    OrderFlag.LOCKED);
        }
    }

    private boolean needResend(InstControlOrderResult instControlOrderResult,
                               InstControlOrder instControlOrder) {
        if (instControlOrderResult == null || instControlOrder == null) {
            return false;
        }
        // 发送失败的订单需要重发
        return InstOrderProcessStatus.SUBMIT_INST_FAIL.equals(instControlOrderResult
                .getProcessStatus());
    }

    public void setThreadPoolTaskExecutor(ThreadPoolTaskExecutor threadPoolTaskExecutor) {
        this.threadPoolTaskExecutor = threadPoolTaskExecutor;
    }

    @Override
    protected void sendToMonitor(FundChannelApiType domain) {

    }

    @Override
    protected void monitorTotalCount(int totalCount) {

    }
}
