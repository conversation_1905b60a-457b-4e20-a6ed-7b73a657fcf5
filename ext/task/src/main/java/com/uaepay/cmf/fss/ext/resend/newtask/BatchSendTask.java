package com.uaepay.cmf.fss.ext.resend.newtask;

import com.uaepay.cmf.common.core.domain.config.SysConfiguration;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import com.uaepay.cmf.common.core.engine.schedule.task.AbstractDaemonTask;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.domainservice.batch.processor.BatchOrderArchiveService;
import com.uaepay.cmf.domainservice.batch.processor.BatchResendProcessor;
import com.uaepay.cmf.domainservice.batch.result.ArchiveDetail;
import com.uaepay.cmf.domainservice.channel.FundChannelManager;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.process.ChannelBalanceService;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.cmf.fss.ext.resend.lock.LockDomain;
import com.uaepay.cmf.fss.ext.resend.lock.TaskLockService;
import com.uaepay.cmf.service.facade.enums.ArchiveStatusEnum;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.uaepay.cmf.common.enums.FundChannelApiType.*;

/**
 * <p>批量出款Task任务.</p>
 *
 * <AUTHOR>
 * @version BatchSendTask.java 1.0 @2015/4/21 18:06 $
 */
public class BatchSendTask extends AbstractDaemonTask<String> {

    @Resource
    protected FundChannelManager fundChannelManager;

    @Resource
    protected ThreadPoolTaskExecutor batchChannelExecutor;

    @Resource
    protected TaskLockService taskLockService;

    @Resource
    protected SysConfigurationHolder sysConfigurationHolder;

    @Resource
    protected BatchResendProcessor batchResendProcessor;

    @Resource
    protected ChannelBalanceService channelBalanceService;

    @Resource
    protected BatchOrderArchiveService prepareArchiveService;

    @Resource
    private ChannelApiRouter channelRouter;

    @Resource
    protected InstOrderRepository instOrderRepository;



    private static final int DEFAULT_SEND_AWATING_BATCH_SIZE = 200;

    private static final String SEND_AWATING_BATCH_SIZE = "SEND_AWATING_BATCH_SIZE";

    @Override
    public List<String> loadTask(int batchSize) {
        // 批量出款渠道
        final List<String> batchPayFcs = batchPayFcs();

        // 过滤余额不足和没有查询接口的渠道
        List<String> availableList = channelBalanceService.filterBalance(batchPayFcs);
        logger.info("batchTask.availableChannelList:{}", availableList);
        return availableList;
    }

    @Override
    public boolean executeTask(String fcCode) {
        LockDomain lockDomain = taskLockService.lockTask(fcCode, getTaskType(), 120);
        if (!lockDomain.isLockSuccess()) {
            logger.error("batchSendTask.lockFail:{}", fcCode);
            return false;
        }

        try {
            logger.info("batchSend.channel.begin:{}", fcCode);
            // Step1:订单打批，生成批次订单
            List<ChannelVO> channelList = fundChannelManager.getFundChannel(fcCode, BATCH_PAY, BATCH_FILE_PAY, MANUAL_FILE_PAY);
            // 一个渠道只能有一个付款接口
            Assert.notNull(channelList, "channel.notSupport.batchSend");
            ChannelVO channel = channelList.get(0);

            // 一次打批的订单在本次并不一定能处理完
            List<ArchiveDetail> archiveList = prepareArchiveService.archiveOrder(channel.getChannelApi());
            if (CollectionUtils.isNotEmpty(archiveList)) {
                logger.info("batchSend.current.batchNum:{}", archiveList.size());
            }
            // Step2:获取批量订单，并修改IS_LOCKED状态，在未发送成功前，限制其它方法取本批量订单
            List<Long> archiveBatchIds = instOrderRepository.loadBatchInstOrders(
                    ArchiveStatusEnum.AWAIT, YesNo.NO, channel.getChannelApi().getApiCode(), DEFAULT_OPERATOR,
                    batchSize(), 1);
            if (CollectionUtils.isEmpty(archiveBatchIds)) {
                logger.info("batchSend.ids.isEmpty:{}", fcCode);
                return true;
            }

            // 修改批量订单IS_CHECKED状态 为YES
            int count = instOrderRepository.updateIsLockedByOriStatus(archiveBatchIds, YesNo.YES,
                    YesNo.NO);
            Assert.isTrue(count == archiveBatchIds.size(), "更新条数不正确");

            logger.info("{}.batchSend.size:{}", fcCode, archiveBatchIds.size());

            // Step3: 多线程 or 单线程 出款
            boolean isSingleThread = isSingleThread(fcCode);
            for (final Long instBatchOrderId : archiveBatchIds) {

                logger.info("BatchFundOut-archiveBatchId:{}", instBatchOrderId);



                if (isSingleThread) {
                    // 批量单线程出款
                    batchFundOut(instBatchOrderId);
                } else { // 批量多线程出款

                    batchChannelExecutor.execute(RunnableWrapper.of(() -> batchFundOut(instBatchOrderId)));
                }
            }
            return true;
        } catch (Exception e) {
            logger.error("[发送待发机构订单任务失败]:", e);
            return false;
        } finally {
            taskLockService.release(lockDomain);
        }
    }


    @Override
    protected void sendToMonitor(String domain) {

    }

    /**
     * 出款方法实现
     */
    public boolean batchFundOut(Long archiveBatchId) {
        LockDomain lockDomain = taskLockService.lockTask(archiveBatchId + "", getTaskType(), 60);
        if (!lockDomain.isLockSuccess()) {
            logger.error("batchSendTask.lockFail:{}", archiveBatchId);
            return false;
        }
        final InstBatchOrder batchOrder = instOrderRepository.loadById(archiveBatchId);
        try (ChannelCarrier carrier = channelRouter.route(RouteUtil.getParam(batchOrder.getFundChannelCode(), batchOrder.getApiType()))) {

            // 组装机构订单列表
            buildInstOrders(batchOrder);

            InstBatchResult result = batchResendProcessor.process(batchOrder);
            return result != null && result.getBatchStatus().isFinished();
        } catch (Exception e) {
            logger.error("batchSend.error:", e);
            return false;
        } finally {
            try {
                instOrderRepository.updateIsLockedByOriStatus(archiveBatchId, YesNo.NO, YesNo.YES);
            } catch (Exception e) {
                logger.error("UNLOCK_FAILED:" + archiveBatchId, e);
            }
            taskLockService.release(lockDomain);
        }
    }

    private void buildInstOrders(InstBatchOrder batchOrder) {
        // 批量文件出款方式，不直接由cmf发送订单详情到渠道
        if (batchOrder.getApiType() == BATCH_FILE_PAY || batchOrder.getApiType() == MANUAL_FILE_PAY) {
            return;
        }

        List<InstOrder> sendInstOrders = instOrderRepository
                .getInstOrderListByAichiveBatchId(batchOrder.getArchiveBatchId());
        CollectionUtils.filter(sendInstOrders, arg -> {
            InstOrder instOrder = (InstOrder) arg;
            return instOrder.getCommunicateStatus() == CommunicateStatus.AWAITING;
        });
        Assert.notNull(sendInstOrders, "batchSend.noAvailable.orders");

        batchOrder.setInstOrderList(sendInstOrders);
    }

    private List<String> batchPayFcs() {
        List<ChannelVO> fundChannels = fundChannelManager
                .loadByApiTypes(BATCH_PAY, BATCH_FILE_PAY, MANUAL_FILE_PAY);
        return fundChannels.stream().map(ChannelUtil::getFundChannelCode).collect(Collectors.toList());
    }

    protected int batchSize() {
        SysConfiguration batchSizeConfig = sysConfigurationHolder
                .getConfiguration(SEND_AWATING_BATCH_SIZE);
        if (batchSizeConfig != null && StringUtils.isNumeric(batchSizeConfig.getAttrValue())) {
            return Integer.valueOf(batchSizeConfig.getAttrValue());
        }
        return DEFAULT_SEND_AWATING_BATCH_SIZE;
    }

    protected boolean isSingleThread(String fcCode) {
        SysConfiguration sysConfiguration = sysConfigurationHolder
                .getConfiguration("FUNDOUT_SIGNLE_THREAD_BANK");
        return fcCode != null && sysConfiguration != null
                && sysConfiguration.getAttrValue().contains(fcCode);
    }

    @Override
    protected void monitorTotalCount(int totalCount) {

    }

    @Override
    public DaemonTaskType getTaskType() {
        return new DaemonTaskType("batchSendTask", "批量发送任务");
    }

}
