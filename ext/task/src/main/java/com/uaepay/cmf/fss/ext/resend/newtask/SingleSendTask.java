package com.uaepay.cmf.fss.ext.resend.newtask;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.config.SysConfiguration;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.OrderFlag;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigureKey;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.FundChannelManager;
import com.uaepay.cmf.domainservice.main.process.ChannelBalanceService;
import com.uaepay.cmf.domainservice.main.process.ResendProcessor;
import com.uaepay.cmf.domainservice.main.process.buffer.ChannelOrderHourCache;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.resend.lock.LockDomain;
import com.uaepay.cmf.fss.ext.resend.lock.TaskLockService;
import com.uaepay.common.util.DateUtil;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

import static com.uaepay.cmf.common.core.engine.util.collection.CollectionUtil.isNotEmpty;
import static java.util.Collections.EMPTY_LIST;

/**
 * <p>
 * 单笔发送任务,发送出款、退款等异步订单.
 * </p>
 *
 * <AUTHOR>
 * @version SingleSendTask.java 1.0 Created@2017-11-30 17:53 $
 */
public class SingleSendTask extends AbstractBizDaemonTask implements SysConfigureKey {

    @Resource
    private TaskLockService taskLockService;

    @Resource
    protected InstOrderRepository instOrderRepository;

    @Resource
    protected CmfOrderRepository cmfOrderRepository;

    @Resource
    protected FundChannelManager fundChannelManager;

    @Resource
    protected ThreadPoolTaskExecutor notifyChannelExecutor;

    @Resource
    protected ThreadPoolTaskExecutor notifyChannelSingleThreadExecutor;

    @Resource
    protected SysConfigurationHolder sysConfigurationHolder;

    @Resource
    protected ResendProcessor resendProcessor;

    @Resource
    protected ChannelBalanceService channelBalanceService;

    @Override
    public DaemonTaskType getTaskType() {
        return new DaemonTaskType("singleSendTask", "单笔发送任务");
    }

    @Override
    public boolean executeTask(BizType bizType) {

        LockDomain lockDomain = taskLockService.lockTask(bizType.getCode(), getTaskType(), 120);
        if (!lockDomain.isLockSuccess()) {
            logger.error("singleSendTask.lockFail:{}", bizType);
            return false;
        }

        try {
            // 如果是退款，则特殊处理
            if (bizType == BizType.REFUND) {
                refundOrdersSend();
            } else {
                // 非退款业务单笔发送处理
                execute(loadChannelCodeByBizType(bizType), EMPTY_LIST, bizType, false);
            }
        } catch (Exception e) {
            logger.error("singleSendTask.error:" + bizType, e);
            return false;
        } finally {
            taskLockService.release(lockDomain);
        }

        return true;
    }

    private void refundOrdersSend() {

        List<String> specialChannels = loadRefundSingleThreadChannels();

        // 获取需要特殊处理的退款渠道列表并处理
        if (isNotEmpty(specialChannels)) {
            execute(specialChannels, EMPTY_LIST, BizType.REFUND, true);
        }

        execute(EMPTY_LIST, specialChannels, BizType.REFUND, false);
    }

    @Override
    protected InstBaseResult executeSingle(Long orderId) {
        LockDomain lockDomain = taskLockService.lockTask(orderId + "", getTaskType(), 60);
        if (!lockDomain.isLockSuccess()) {
            logger.error("singleSendTask.lockFail:{}", orderId);
            return buildResult(InstOrderProcessStatus.AWAITING);
        }
        // 状态锁定
        int rows = instOrderRepository.updateFlagWithOrderIdAndPreFlag(orderId, OrderFlag.LOCKED, OrderFlag.DEFAULT);
        if (rows != 1) {
            logger.info("锁定状态更新失败");
            return null;
        }

        try {
            InstOrder instOrder = instOrderRepository.loadById(orderId, false);
            // 状态校验
            Assert.isTrue(instOrder.getStatus() == InstOrderStatus.IN_PROCESS, "订单状态不为处理中");

            return resendProcessor.process(instOrder);
        } catch (Exception e) {
            logger.error("singleSend.executeSingle.error:" + orderId, e);
            return buildResult(InstOrderProcessStatus.AWAITING);
        } finally {
            instOrderRepository.updateFlagWithOrderIdAndPreFlag(orderId, OrderFlag.DEFAULT, OrderFlag.LOCKED);
            taskLockService.release(lockDomain);
        }
    }

    public List<String> loadChannelCodeByBizType(BizType bizType) {

        // 非出款、退款交易不关心渠道编号
        if (bizType != BizType.FUNDOUT) {
            return new ArrayList<>(0);
        }

        return loadFundOutChannelList();
    }

    /**
     * 加载出款资金渠道列表
     *
     * @return
     */
    private List<String> loadFundOutChannelList() {

        List<String> channelList = loadFundChannels(BizType.FUNDOUT, FundChannelApiType.SINGLE_PAY);

        addAlwaysFOChannel(channelList);

        logger.info("singleSendTask.FO.channelList:{}", channelList);
        // 出款交易余额过滤
        List<String> afterFilterList = channelBalanceService.filterBalance(channelList);
        logger.info("singleSendTask.FO.afterFilter:{}", afterFilterList);

        return afterFilterList;
    }

    /**
     * 根据业务类型与API类型加载渠道列表
     *
     * @param bizType 业务类型
     * @param apiType API类型
     * @return
     */
    private List<String> loadFundChannels(BizType bizType, FundChannelApiType apiType) {
        List<ChannelVO> fundChannelList = fundChannelManager.loadByApiType(apiType);

        if (CollectionUtils.isEmpty(fundChannelList)) {
            return new ArrayList<>(0);
        }

        List<String> list = new ArrayList<>();
        for (ChannelVO channel : fundChannelList) {
            if (isAsyncChannel(channel, apiType) && BizType.getByCode(channel.getBizType()) == bizType) {
                list.add(channel.getChannelCode());
            }
        }

        return list;
    }

    private boolean isAsyncChannel(ChannelVO channel, FundChannelApiType apiType) {
        return true;
    }

    private void addAlwaysFOChannel(List<String> fundChannelCodeList) {
        SysConfiguration alwaysFOSysConfiguration = sysConfigurationHolder.getConfiguration(ALWAYS_FUNDOUT_CHANNEL);
        // 过滤
        if (CollectionUtils.isEmpty(fundChannelCodeList) || alwaysFOSysConfiguration == null
                || StringUtils.isEmpty(alwaysFOSysConfiguration.getAttrValue())) {
            return;
        }

        String[] fcList = alwaysFOSysConfiguration.getAttrValue().split(CHAR_SEMICOLON);
        if (fcList.length > 0) {
            fundChannelCodeList.addAll(Arrays.asList(fcList));
        }

    }

    /**
     * 从CMF系统配置加载需要单线程逐笔发送到渠道的资金渠道编码列表
     *
     * @return
     */
    private List<String> loadRefundSingleThreadChannels() {
        String values = sysConfigurationHolder.loadConfigureOrDefault(REFUND_IN_SINGLE_THREAD_CHANNELS, null);
        if (values != null) {
            String[] arrays = StringUtils.splitByWholeSeparatorPreserveAllTokens(values, CHAR_COMMA);
            if (arrays != null && arrays.length > 0) {
                return new ArrayList<>(new HashSet<>(Arrays.asList(arrays)));
            }
        }

        return new ArrayList<>(0);
    }

    private void execute(List<String> channelList, List<String> ignoreChannelList, BizType bizType,
                         boolean isSingleThreadMode) {
        int maxSize =
                sysConfigurationHolder.loadConfigureOrDefault(SEND_AWATING_MAX_SIZE, DEFAULT_SEND_AWATING_MAX_SIZE);
        for (Date[] dateArr : ChannelOrderHourCache.getInstance().getQueryTimeRange(getTaskType().getCode(), 12)) {
            Set<Long> instOrderIdSet = instOrderRepository.loadInstOrder4Send(channelList, ignoreChannelList, bizType,
                    dateArr[0], dateArr[1], maxSize);
            logger.info("now.process.order:{}", instOrderIdSet);
            if (CollectionUtils.isEmpty(instOrderIdSet)) {
                continue;
            }

            if (isSingleThreadMode) {
                executeTaskInSingleThread(instOrderIdSet, bizType);
            } else {
                executeTask(instOrderIdSet);
            }
        }
    }

    /**
     * 默认的多线程提交方式
     *
     * @param instOrderIdSet
     */
    private void executeTask(Set<Long> instOrderIdSet) {
        for (final Long instOrderId : instOrderIdSet) {
            logger.info("begin.to.process,in multiThread mode：{}", instOrderId);
            notifyChannelExecutor.execute(RunnableWrapper.of(() -> executeSingle(instOrderId)));
        }
    }

    /**
     * 单线程逐笔提交方式
     *
     * @param instOrderIdSet
     */
    private void executeTaskInSingleThread(final Set<Long> instOrderIdSet, final BizType bizType) {
        for (final Long instOrderId : instOrderIdSet) {

            notifyChannelSingleThreadExecutor.execute(RunnableWrapper.of(() -> {
                // 如果是退款，则需要检查是否存在原入款订单相同的处理中部分退款订单
                if (bizType == BizType.REFUND) {
                    if (!canSendToChannel(instOrderId)) {
                        logger.info("has other partRefund processing order,skip:{}", instOrderId);
                        return;
                    }
                }
                logger.info("begin.to.process,in single thread mode：{}", instOrderId);
                executeSingle(instOrderId);
            }));
        }
    }

    /**
     * 检查指定退款订单当前能否发往渠道
     *
     * @param instOrderId
     * @return
     * @see <url>http://************:8080/browse/PAY-221</url>
     */
    private boolean canSendToChannel(Long instOrderId) {

        // 根据instOrderId获取cmf订单，获取ORGI_SETTLEMENT_ID
        InstOrder instOrder = instOrderRepository.loadById(instOrderId, false);
        CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(instOrder.getCmfSeqNo(), false);

        // 理论上退款订单的cmfOrder和orgiSettlementId不为空，出现为空的，直接发到渠道
        if (cmfOrder == null || StringUtils.isEmpty(cmfOrder.getOrgiSettlementId())) {
            return true;
        }

        // 通过orgiSettlementId加载处理中的cmf订单，排除自己
        List<Long> ignoreInstIds = new ArrayList<>();
        ignoreInstIds.add(instOrderId);
        List<CmfOrder> cmfOrders = cmfOrderRepository.loadByOrgiSettlementId(cmfOrder.getOrgiSettlementId(),
                DateUtil.getDayBegin(cmfOrder.getGmtCreate()), ignoreInstIds);

        // 如果不存在相同原始入款订单的部分退款订单，则返回true
        if (CollectionUtils.isEmpty(cmfOrders)) {
            return true;
        }

        // 若存在相同原始入款订单的部分退款订单，查询其机构订单
        for (CmfOrder o : cmfOrders) {
            if (!checkInstOrder(o.getOrderSeqNo())) {
                logger.info("current {} can not send!other partRefund order {}", instOrderId, o.getOrderSeqNo());
                return false;
            }
        }

        return true;
    }

    /**
     * 检查一笔退款订单能否发送到渠道（查看是否存在其他部分退款订单）
     *
     * @param cmfSeqNo 待检查的同一笔入款的其他部分退款订单
     * @return
     */
    private boolean checkInstOrder(String cmfSeqNo) {
        if (cmfSeqNo == null) {
            return true;
        }

        // 加载机构订单
        List<InstOrder> instOrderList = instOrderRepository.loadByCmfSeqNo(cmfSeqNo);
        if (instOrderList == null) {
            return true;
        }

        Assert.isTrue(instOrderList.size()==1, "记录关联不正确");

        InstOrder instOrder = instOrderList.get(0);

        // 被检查的其他部分退款订单状态不为I，跳过
        if (instOrder.getStatus() != InstOrderStatus.IN_PROCESS) {
            return true;
        }

        // 尚未发送，跳过
        if (instOrder.getCommunicateStatus() == CommunicateStatus.AWAITING) {
            return true;
        }

        // 已发送，状态为处理中，但发送时间已超过阈值的，跳过
        int minutesIgnore = sysConfigurationHolder.loadConfigureOrDefault(REFUND_ORDER_IGNORE_MINUTES,
                DEFAULT_REFUND_ORDER_IGNORE_MINUTES);
        if (DateUtil.addMinutes(instOrder.getGmtCreate(), minutesIgnore).before(new Date())) {
            return true;
        }

        // 所有可以发送条件都不满足，跳出
        return false;
    }

}
