package com.uaepay.cmf.fss.ext.resend.processor.handler;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>重试新订单结果处理.</p>
 *
 * <AUTHOR>
 * @version RetryNewOrderResultHandler.java 1.0 Created@2017-12-04 13:30 $
 */
@Service("retryNewOrderResultHandler")
public class RetryNewOrderResultHandler extends RetryResultHandler {

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private RouterClient routerClient;

    @Override
    public InstOrderResult handle(InstOrder instOrder, ChannelFundResult channelFundResult) {
        String newInstOrderNo = routerClient.genOrderNo(instOrder.getFundChannelCode(), instOrder.getApiType());
        int rowCount = instOrderRepository.updateOrderNoAndStatus(newInstOrderNo, instOrder);
        if (rowCount != 1) {
            return null;
        }
        return super.handle(instOrder, channelFundResult);
    }
}
