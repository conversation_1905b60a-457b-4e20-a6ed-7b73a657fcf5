package com.uaepay.cmf.fss.ext.resend.newtask;

import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.common.core.engine.schedule.task.AbstractDaemonTask;
import com.uaepay.cmf.fss.ext.resend.stat.SystemStat;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.skywalking.apm.toolkit.trace.TraceCrossThread;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version AbstractBizDaemonTask.java 1.0 Created@2017-12-04 10:52 $
 */
public abstract class AbstractBizDaemonTask extends AbstractDaemonTask<BizType> {

    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    protected List<BizType> loadTask(int batchSize) {
        monitorSystemStat();
        return Arrays.asList(BizType.values());
    }

    private void monitorSystemStat() {
        SystemStat.reload();
        logger.info("task.systemStat.monitor-totalMemory:{}MB,freeMemory:{}MB,maxMemory:{}MB",
                SystemStat.getTotalMemory(), SystemStat.getFreeMemory(), SystemStat.getMaxMemory());
    }

    @Override
    protected void sendToMonitor(BizType bizType) {

    }

    @Override
    protected void monitorTotalCount(int totalCount) {

    }

    void processFutureTaskResult(Map<Long, FutureTask<TaskResult>> futureMap) {
        for (Map.Entry<Long, FutureTask<TaskResult>> entry : futureMap.entrySet()) {
            Long domain = entry.getKey();
            String logPrefix = "SingleQueryTask.query[" + domain + "]";
            try {
                TaskResult taskResult = entry.getValue().get();
                logger.info("{}执行状态:{}", logPrefix, taskResult.isSuccess());
            } catch (InterruptedException e) {
                logger.error(logPrefix + "执行线程执行意外中断误", e);
            } catch (Exception e) {
                logger.error(logPrefix + "执行时出现未知错误", e);
            }
        }
    }

    @TraceCrossThread
    class FutureQueryTaskCallable implements Callable<TaskResult> {
        /**
         * 订单ID
         */
        private Long domain;

        /**
         * 构造
         *
         * @param domain
         */
        FutureQueryTaskCallable(Long domain) {
            this.domain = domain;
        }

        @Override
        public TaskResult call() {
            String logPrefix = "订单ID[" + domain + "]";
            try {
                InstBaseResult baseResult = executeSingle(domain);
                if (baseResult == null || baseResult.getProcessStatus() == null) {
                    return new TaskResult(false, "返回结果为空");
                }
                return new TaskResult(baseResult.getProcessStatus().isFinished(), "任务执行成功");
            } catch (Exception e) {
                logger.error(logPrefix + "出场时出现未知错误", e);
                return new TaskResult(false, e.getMessage());
            }
        }
    }

    protected abstract InstBaseResult executeSingle(Long domain);

    static InstBaseResult buildResult(InstOrderProcessStatus processStatus) {
        InstBaseResult instBaseResult = new InstBaseResult();
        instBaseResult.setProcessStatus(processStatus);
        return instBaseResult;
    }

    public void setThreadPoolTaskExecutor(ThreadPoolTaskExecutor threadPoolTaskExecutor) {
        this.threadPoolTaskExecutor = threadPoolTaskExecutor;
    }
}
