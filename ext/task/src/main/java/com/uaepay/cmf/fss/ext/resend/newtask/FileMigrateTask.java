package com.uaepay.cmf.fss.ext.resend.newtask;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import com.uaepay.cmf.common.core.engine.schedule.task.AbstractDaemonTask;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.FundChannelManager;
import com.uaepay.cmf.domainservice.main.convert.CmfRequestConverter;
import com.uaepay.cmf.domainservice.main.general.impl.FileMigrateProcessor;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.cmf.fss.ext.resend.lock.LockDomain;
import com.uaepay.cmf.fss.ext.resend.lock.TaskLockService;
import com.uaepay.cmf.service.facade.domain.control.CmfFileRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfFileResponse;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.uaepay.cmf.common.enums.FundChannelApiType.FILE_MIGRATE;

/**
 * <p>
 * 文件迁移任务
 * </p>
 *
 * <AUTHOR>
 * @version SingleSendTask.java 1.0 Created@2017-11-30 17:53 $
 */
public class FileMigrateTask extends AbstractDaemonTask<String> {

    @Resource
    private TaskLockService taskLockService;

    @Resource
    protected FundChannelManager fundChannelManager;

    @Resource
    protected InstOrderRepository instOrderRepository;

    @Resource
    protected RouterClient routerClient;

    @Resource
    protected FileMigrateProcessor fileMigrateProcessor;

    @Override
    public List<String> loadTask(int batchSize) {
        // 文件迁移渠道
        final List<String> migrateList = migrateChannelList();

        logger.info("FileMigrateTask.migrateList:{}", migrateList);
        return migrateList;
    }

    @Override
    public DaemonTaskType getTaskType() {
        return new DaemonTaskType("fileMigrateTask", "文件迁移任务");
    }

    @Override
    public boolean executeTask(String channelCode) {

        LockDomain lockDomain = taskLockService.lockTask(channelCode, getTaskType(), 120);
        if (!lockDomain.isLockSuccess()) {
            logger.error("fileMigrateTask.lockFail:{}", channelCode);
            return false;
        }

        try {
            executeMigrate(channelCode);
        } catch (Exception e) {
            logger.error("fileMigrateTask.error:" + channelCode, e);
            return false;
        } finally {
            taskLockService.release(lockDomain);
        }
        return true;
    }


    protected boolean executeMigrate(String channelCode) {

        try {
            //1.判定
            List<String> dateList = instOrderRepository.queryMigrateDateList(channelCode);

            //2.迁移
            if(!CollectionUtils.isEmpty(dateList)) {
                for(String fileDate: dateList) {

                    CmfFileRequest request = initCmfFileRequest(channelCode, fileDate);
                    CmfFileResponse resp = fileMigrateProcessor.process(request);

                }
            }

            return true;
        } catch (Exception e) {
            logger.error("fileMigrateTask.executeSingle.error:" + channelCode, e);
            return false;
        }
    }

    private CmfFileRequest initCmfFileRequest(String channelCode, String fileDate) {
        CmfFileRequest request = new CmfFileRequest();
        request.setRequestNo(routerClient.genOrderNo(channelCode, FILE_MIGRATE));
        request.setRequestType(ControlRequestType.FILE_MIGRATE);
        request.setChannelCode(channelCode);
        request.setFileDate(fileDate);

        return request;
    }

    private List<String> migrateChannelList() {
        List<ChannelVO> channelList = fundChannelManager
                .loadByApiTypes(FILE_MIGRATE);
        return channelList.stream().map(ChannelUtil::getFundChannelCode).collect(Collectors.toList());
    }

    @Override
    protected void sendToMonitor(String domain) {

    }

    @Override
    protected void monitorTotalCount(int totalCount) {

    }

}
