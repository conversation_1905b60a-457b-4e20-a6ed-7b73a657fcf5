package com.uaepay.cmf.fss.ext.resend.processor;

import com.uaepay.cmf.common.core.domain.enums.ChannelInfoExtKey;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.domainservice.main.convert.ChannelResultConverter;
import com.uaepay.cmf.domainservice.main.process.ResultCodeService;
import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.common.util.DateUtil;
import com.uaepay.router.service.facade.domain.channel.ChannelExtVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version BatchQueryResultProcessor.java 1.0 Created@2017-12-04 17:11 $
 */
public class BatchQueryResultProcessor
        extends
        AbstractResultProcessor<InstBatchOrder, ChannelFundBatchResult, InstBatchResult> {
    private Logger logger = LoggerFactory.getLogger(QueryResultProcessor.class);

    @Resource
    private ResultCodeService resultCodeService;

    private static final Integer RETRY_GAP_MINUTES = 30;

    @Override
    protected ResultProcessMode obtainProcessMode(InstBatchOrder instBatchOrder,
                                                  ChannelFundBatchResult channelFundBatchResult) {

        try {
            Assert.notNull(channelFundBatchResult, "渠道结果不可为空");

            InstOrderResult instResult = ChannelResultConverter.convert(channelFundBatchResult,
                    instBatchOrder);

            // 设置统一返回码
            resultCodeService.fillResultStatus(instResult);

            if (canResend(instBatchOrder, instResult)) {
                return ResultProcessMode.BATCH_RETRY;
            }
        } catch (Exception e) {
            logger.info("obtainProcessMode.error:{}", e.getMessage());
        }
        return ResultProcessMode.BATCH_DEFAULT;
    }

    private boolean canResend(InstBatchOrder instBatchOrder, InstOrderResult instResult) {
        ChannelVO channel = ChannelHolder.get();
        return channel != null && instResult.getProcessStatus().canResend()
                && instBatchOrder.getBizType().isFundOutRefund()
                && canResend(channel, instBatchOrder.getQueryTimes())
                && DateUtil.isBeforeNow(DateUtil.addMinutes(instBatchOrder.getGmtArchive(), RETRY_GAP_MINUTES));
    }

    private static boolean canResend(ChannelVO channel, int queryTimes) {
        ChannelExtVO ext = ChannelUtil.getExt(channel.getExtList(), ChannelInfoExtKey.MAX_RESEND_TIMES);

        return ext != null && StringUtils.isNumeric(ext.getAttrValue())
                && queryTimes <= Integer.valueOf(ext.getAttrValue());
    }

}
