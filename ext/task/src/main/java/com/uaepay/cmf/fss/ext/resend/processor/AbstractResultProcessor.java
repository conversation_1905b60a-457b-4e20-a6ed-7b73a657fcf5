package com.uaepay.cmf.fss.ext.resend.processor;

import com.uaepay.cmf.common.core.domain.enums.ChannelInfoExtKey;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.common.core.domain.institution.InstCommonOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.router.ApiRouteParam;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.cmf.fss.ext.resend.processor.handler.ResultHandler;
import com.uaepay.common.util.DateUtil;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.router.service.facade.domain.channel.ChannelExtVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version BaseResultProcessor.java 1.0 Created@2017-11-30 18:25 $
 */
public abstract class AbstractResultProcessor<Order extends InstCommonOrder, Result extends ChannelResult, Response extends InstBaseResult>
        implements
        ResultProcessor<Order, Result, Response> {
    protected Logger logger = LoggerFactory
            .getLogger(AbstractResultProcessor.class);

    private Map<String, ResultHandler> resultHandlerMap;

    @Resource
    private ChannelApiRouter channelApiRouter;

    /**
     * 订单处理-流程模板化
     *
     * @param order
     * @param result
     * @return
     */
    @Override
    public Response process(Order order, Result result) {
        // 获取处理模式
        ResultProcessMode processMode = obtainProcessMode(order, result);

        logger.info("resultProcessor.process.processMode:{}", processMode);

        ResultHandler resultHandler = resultHandlerMap.get(processMode.name());

        return (Response) resultHandler.handle(order, result);
    }

    /**
     * 获取处理模式
     *
     * @param order
     * @param result
     * @return
     */
    protected abstract ResultProcessMode obtainProcessMode(Order order, Result result);

    protected boolean canSend2Counter(InstOrder instOrder, InstOrderResult instResult) {
        return instOrder.getBizType() == BizType.REFUND
                && !instResult.getProcessStatus().isFinished()
                && (DateUtil.addMinutes(instOrder.getGmtBookingSubmit(), 60).compareTo(new Date()) < 0)
                && !notSupportManualRefund(instOrder);
    }

    /**
     * 判断是否不支持人工自动发送到counter
     *
     * @param instOrder
     * @return
     */
    private boolean notSupportManualRefund(InstOrder instOrder) {
        try (ChannelCarrier carrier = channelApiRouter.route(
                ApiRouteParam.builder().channelCode(instOrder.getFundChannelCode())
                        .apiType(getApiType(instOrder).getCode()).build())) {
            ChannelVO channel = carrier.getChannel();
            ChannelExtVO ext = ChannelUtil.getExt(channel.getExtList(), ChannelInfoExtKey.NOT_SUPPORT_MANUAL_REFUND);
            ChannelExtVO taskExt = ChannelUtil.getExt(channel.getExtList(), ChannelInfoExtKey.NOT_SUPPORT_TASK_SEND2COUNTER);
            return (ext != null && YesNo.YES.getCode().equals(ext.getAttrValue()))
                    || (taskExt != null && YesNo.YES.getCode().equals(taskExt.getAttrValue()));
        } catch (Exception e) {
            return false;
        }

    }

    public FundChannelApiType getApiType(InstOrder instOrder) {
        return instOrder.getPayMode() == PayMode.NETBANK ? FundChannelApiType.SIGN
                : FundChannelApiType.DEBIT;
    }


    public void setResultHandlerMap(Map<String, ResultHandler> resultHandlerMap) {
        this.resultHandlerMap = resultHandlerMap;
    }
}
