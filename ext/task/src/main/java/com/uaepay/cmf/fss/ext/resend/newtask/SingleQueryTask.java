package com.uaepay.cmf.fss.ext.resend.newtask;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.FutureTask;

import javax.annotation.Resource;

import com.uaepay.cmf.common.core.domain.enums.OrderFlag;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.domainservice.main.process.DistributeQueryService;
import com.uaepay.cmf.domainservice.main.process.buffer.ChannelOrderHourCache;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.resend.lock.LockDomain;
import com.uaepay.cmf.fss.ext.resend.lock.TaskLockService;
import com.uaepay.cmf.fss.ext.resend.processor.ResultProcessor;
import com.uaepay.schema.cmf.enums.BizType;

/**
 * <p>
 * 单笔查询任务:查询机构订单.
 * </p>
 *
 * <AUTHOR>
 * @version SingleQueryTask.java 1.0 Created@2017-11-13 19:54 $
 */
public class SingleQueryTask extends AbstractBizDaemonTask {

    @Resource
    private TaskLockService taskLockService;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private DistributeQueryService distributeQueryService;

    @Resource
    private SysConfigurationHolder sysConfigurationHolder;

    @Resource
    private ResultProcessor<InstOrder, ChannelFundResult, InstOrderResult> queryResultProcessor;

    @Override
    public boolean executeTask(BizType bizType) {
        LockDomain lockDomain = taskLockService.lockTask(bizType.getCode(), getTaskType(), 120);
        if (!lockDomain.isLockSuccess()) {
            logger.error("singleQuery.lockFail:{}", bizType);
            return false;
        }
        try {
            int maxSize =
                sysConfigurationHolder.loadConfigureOrDefault(SINGLE_QUERY_MAX_SIZE, DEFAULT_SINGLE_QUERY_MAX_SIZE);
            // 订单获取
            // 将查询最大查询范围修改为48小时
            for (Date[] dateArr : ChannelOrderHourCache.getInstance().getQueryTimeRange(getTaskType().getCode(), 48)) {
                Set<Long> instOrderIdList = loadOrders(bizType, dateArr[0], dateArr[1], maxSize);
                logger.info("singleQuery.currentProcess:{}", instOrderIdList);
                executeFutureTask(instOrderIdList);
            }
        } catch (Exception e) {
            logger.error("singleQuery.error", e);
            return false;
        } finally {
            taskLockService.release(lockDomain);
        }
        return true;
    }

    private Set<Long> loadOrders(BizType bizType, Date startDate, Date endDate, int maxSize) {
        return instOrderRepository.loadSingleOrder4Query(startDate, endDate, maxSize, bizType);
    }

    private Map<Long, FutureTask<TaskResult>> executeFutureTask(Set<Long> instOrderIdList) {
        Map<Long, FutureTask<TaskResult>> futureMap = new HashMap<>();

        logger.info("singleQuery.currentProcess.orderList:{}", instOrderIdList);
        for (final Long instOrderId : instOrderIdList) {
            FutureTask<TaskResult> futureTask = new FutureTask<>(new FutureQueryTaskCallable(instOrderId));
            futureMap.put(instOrderId, futureTask);
            threadPoolTaskExecutor.execute(futureTask);
        }

        processFutureTaskResult(futureMap);
        return futureMap;
    }

    @Override
    public InstOrderResult executeSingle(Long instOrderId) {
        InstOrder instOrder = instOrderRepository.loadById(instOrderId, false);

        int rows =
            instOrderRepository.updateFlagWithOrderIdAndPreFlag(instOrderId, OrderFlag.LOCKED, OrderFlag.DEFAULT);
        if (rows != 1) {
            logger.info("锁定状态更新失败");
            return null;
        }
        try {
            ChannelFundResult channelFundResult = distributeQueryService.queryResult(instOrder);
            return queryResultProcessor.process(instOrder, channelFundResult);
        } catch (IllegalArgumentException iae) {
            logger.info("singleQueryTask.executeSingle.iae:{}", iae.getMessage());
            return new InstOrderResult();
        } finally {
            instOrderRepository.updateFlagWithOrderIdAndPreFlag(instOrderId, OrderFlag.DEFAULT, OrderFlag.LOCKED);
        }
    }

    @Override
    public DaemonTaskType getTaskType() {
        return new DaemonTaskType("singleQueryTask", "查询单笔结果任务");
    }

}
