package com.uaepay.cmf.fss.ext.resend.processor.handler;

import java.util.List;

import javax.annotation.Resource;

import com.uaepay.cmf.common.core.domain.router.ApiRouteParam;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderArchiveStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.domainservice.batch.processor.BatchResendProcessor;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;

/**
 * <p>批量重试结果处理.</p>
 *
 * <AUTHOR>
 * @version BatchRetryResultHandler.java 1.0 Created@2017-12-05 10:07 $
 */
@Service("batchRetryResultHandler")
public class BatchRetryResultHandler
        implements
        ResultHandler<InstBatchOrder, ChannelFundBatchResult, InstBatchResult> {
    private Logger logger = LoggerFactory.getLogger(BatchRetryResultHandler.class);

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private BatchResendProcessor batchResendProcessor;

    @Resource
    private TransactionTemplate cmfTransactionTemplate;

    @Resource
    private ChannelApiRouter channelApiRouter;

    @Override
    public InstBatchResult handle(InstBatchOrder instBatchOrder,
                                  ChannelFundBatchResult channelFundBatchResult) {

            logger.info("batchResult.retry.result:{}", channelFundBatchResult);
            final InstBatchOrder sendBatchOrder = instOrderRepository.loadById(instBatchOrder
                    .getArchiveBatchId());
        try(ChannelCarrier carrier = channelApiRouter.route(RouteUtil.getParam(sendBatchOrder.getFundChannelCode(), sendBatchOrder.getApiType()))) {
            int batchCount = instOrderRepository.updateBatchOrderStatus(
                    sendBatchOrder.getArchiveBatchId(), InstOrderArchiveStatus.AWAITING,
                    instBatchOrder.getStatus());
            if (batchCount == 0) {
                logger.info("批量订单发送状态更新失败,该订单已执行过:{}", instBatchOrder.getArchiveBatchId());
                return null;
            }
            sendBatchOrder.setStatus(InstOrderArchiveStatus.AWAITING);
            if (sendBatchOrder.getApiType() != FundChannelApiType.BATCH_FILE_PAY && sendBatchOrder.getApiType() != FundChannelApiType.MANUAL_FILE_PAY) {
                List<InstOrder> sendInstOrders = instOrderRepository
                        .getInstOrderListByAichiveBatchId(instBatchOrder.getArchiveBatchId());
                sendBatchOrder.setInstOrderList(sendInstOrders);

                boolean executeStatus = cmfTransactionTemplate
                        .execute(status -> {
                            int itemsCount = instOrderRepository.updateCommunicateStatusByInstOrders(
                                    sendBatchOrder.getInstOrderList(), CommunicateStatus.AWAITING,
                                    sendBatchOrder.getInstOrderList().get(0).getCommunicateStatus());
                            boolean modSuccess = itemsCount == sendBatchOrder.getInstOrderList().size();
                            if (!modSuccess) {
                                status.setRollbackOnly();
                            }
                            return modSuccess;
                        });
                if (!executeStatus) {
                    logger.info("状态更新执行错误");
                    return null;
                }
            }

            InstBatchResult batchResult = batchResendProcessor.process(sendBatchOrder);
            logger.info("补发返回结果:{}", batchResult);
            return batchResult;
        } catch (Exception e) {
            logger.error("batchResult.retry.error", e);
            return null;
        }
    }
}
