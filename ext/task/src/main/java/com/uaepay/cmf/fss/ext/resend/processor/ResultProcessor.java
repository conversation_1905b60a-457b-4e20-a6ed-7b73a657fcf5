package com.uaepay.cmf.fss.ext.resend.processor;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.common.core.domain.institution.InstCommonOrder;
import com.uaepay.cmf.common.domain.ChannelResult;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version ResultProcessor.java 1.0 Created@2017-12-05 10:28 $
 */
public interface ResultProcessor<Order extends InstCommonOrder, Result extends ChannelResult, Response extends InstBaseResult> extends BasicConstant {

    /**
     * 处理订单结果
     * @param order
     * @param result
     * @return
     */
    Response process(Order order, Result result);
}
