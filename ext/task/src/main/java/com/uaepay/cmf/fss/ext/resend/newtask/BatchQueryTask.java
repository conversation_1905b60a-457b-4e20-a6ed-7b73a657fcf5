package com.uaepay.cmf.fss.ext.resend.newtask;

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderArchiveStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.batch.repository.InstBatchOrderRepository;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.process.DistributeQueryService;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.resend.lock.LockDomain;
import com.uaepay.cmf.fss.ext.resend.lock.TaskLockService;
import com.uaepay.cmf.fss.ext.resend.processor.ResultProcessor;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.FutureTask;

/**
 * <p>批量查询任务.</p>
 *
 * <AUTHOR>
 * @version BatchQueryTask.java 1.0 Created@2017-11-30 17:54 $
 */
public class BatchQueryTask extends AbstractBizDaemonTask {

    @Resource
    private TaskLockService taskLockService;

    @Resource
    private InstBatchOrderRepository instBatchOrderRepository;

    @Resource
    private DistributeQueryService distributeQueryService;

    @Resource
    protected InstOrderRepository instOrderRepository;

    @Resource
    private ResultProcessor<InstBatchOrder, ChannelFundBatchResult, InstBatchResult> batchQueryResultProcessor;

    @Resource
    private SysConfigurationHolder sysConfigurationHolder;

    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    private ChannelApiRouter channelApiRouter;

    @Override
    public boolean executeTask(BizType bizType) {
        LockDomain lockDomain = taskLockService.lockTask(bizType.getCode(), getTaskType(), 120);
        if (!lockDomain.isLockSuccess()) {
            logger.error("batchQuery.lockFail:{}", bizType);
            return false;
        }
        try {
            int maxSize = sysConfigurationHolder.loadConfigureOrDefault(BATCH_QUERY_MAX_SIZE,
                    batchSize);
            List<Long> archiveBatchIds = instBatchOrderRepository.loadBatchOrder4Query(YesNo.NO,
                    bizType, maxSize);

            executeFutureTask(archiveBatchIds);

            logger.info("batchQuery.complete:{}", bizType);
        } catch (Exception e) {
            logger.error("batchQuery.error:", e);
            return false;
        } finally {
            taskLockService.release(lockDomain);
        }
        return true;
    }

    private Map<Long, FutureTask<TaskResult>> executeFutureTask(List<Long> archiveBatchIdList) {
        Map<Long, FutureTask<TaskResult>> futureMap = new HashMap<>(archiveBatchIdList.size() * 2);
        logger.info("batchQuery.currentProcess.orderList:{}", archiveBatchIdList);
        for (final Long domain : archiveBatchIdList) {
            FutureTask<TaskResult> futureTask = new FutureTask<>(
                    new FutureQueryTaskCallable(domain));
            futureMap.put(domain, futureTask);
            threadPoolTaskExecutor.execute(futureTask);
        }

        // 结果处理
        processFutureTaskResult(futureMap);
        return futureMap;
    }

    @Override
    public InstBatchResult executeSingle(Long archiveBatchId) {
        long beginTime = System.currentTimeMillis();
        logger.info("batchQuery.begin.process:{}", archiveBatchId);
        int rows = instOrderRepository.updateIsLockedByOriStatus(archiveBatchId, YesNo.YES,
                YesNo.NO);
        if (rows != 1) {
            logger.info("锁定状态更新失败");
            return null;
        }

        final InstBatchOrder instBatchOrder = loadBatchOrder4Query(archiveBatchId);
        if (instBatchOrder == null) {
            return null;
        }
        try(ChannelCarrier carrier = channelApiRouter.route(RouteUtil.getParam(instBatchOrder.getFundChannelCode(),
                FundChannelApiType.BATCH_QUERY))) {

            ChannelFundBatchResult channelFundBatchResult = distributeQueryService
                    .queryBatchItemResult(instBatchOrder);
            InstBatchResult result = batchQueryResultProcessor.process(instBatchOrder,
                    channelFundBatchResult);
            logger.info("batchQuery.finished.process:{},cost:{}", archiveBatchId,
                    System.currentTimeMillis() - beginTime);

            return result;
        } catch (Exception iae) {
            logger.info("batchQueryTask.executeSingle.iae:{}", iae.getMessage());
            return new InstBatchResult();
        } finally {
            instOrderRepository.updateIsLockedByOriStatus(archiveBatchId, YesNo.NO, YesNo.YES);
        }
    }

    private InstBatchOrder loadBatchOrder4Query(Long archiveBatchId) {
        final InstBatchOrder instBatchOrder = instOrderRepository.loadById(archiveBatchId);

        CommunicateStatus communicateStatus = convertStatus(instBatchOrder.getStatus());
        if (communicateStatus == null) {
            //可能已处理
            return null;
        }
        List<InstOrder> instOrders = instOrderRepository.getInstOrderListByBatchIdStatus(
                archiveBatchId, instBatchOrder.getGmtArchive(), null,
                null);
        if (CollectionUtils.isEmpty(instOrders)) {
            logger.info("current file has processed");
            return null;
        }
        instBatchOrder.setInstOrderList(instOrders);
        return instBatchOrder;
    }

    private CommunicateStatus convertStatus(InstOrderArchiveStatus status) {
        if (status == InstOrderArchiveStatus.SUBMMITED) {
            return CommunicateStatus.SENT;
        } else if (status == InstOrderArchiveStatus.FAILURE) {
            return CommunicateStatus.FAILURE;
        }
        return null;
    }

    @Override
    public void setThreadPoolTaskExecutor(ThreadPoolTaskExecutor threadPoolTaskExecutor) {
        this.threadPoolTaskExecutor = threadPoolTaskExecutor;
    }

    @Override
    public DaemonTaskType getTaskType() {
        return new DaemonTaskType("batchQueryTask", "批量查询结果任务");
    }

}
