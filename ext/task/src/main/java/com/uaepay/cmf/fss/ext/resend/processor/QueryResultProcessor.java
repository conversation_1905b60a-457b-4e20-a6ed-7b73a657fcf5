package com.uaepay.cmf.fss.ext.resend.processor;

import com.uaepay.cmf.common.core.domain.enums.ChannelInfoExtKey;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.domainservice.main.convert.ChannelResultConverter;
import com.uaepay.cmf.domainservice.main.process.ResultCodeService;
import com.uaepay.common.domain.Extension;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version QueryResultProcessor.java 1.0 Created@2017-11-30 18:16 $
 */
public class QueryResultProcessor
        extends
        AbstractResultProcessor<InstOrder, ChannelFundResult, InstOrderResult> {
    private Logger logger = LoggerFactory.getLogger(QueryResultProcessor.class);

    @Resource
    private ResultCodeService resultCodeService;

    /**
     * 获取处理模式
     *
     * @param channelFundResult
     * @param instOrder
     * @return
     */
    @Override
    protected ResultProcessMode obtainProcessMode(InstOrder instOrder,
                                                  ChannelFundResult channelFundResult) {

        try {
            Assert.notNull(channelFundResult);

            InstOrderResult instResult = ChannelResultConverter.convert(instOrder, channelFundResult);

            // 设置统一返回码
            resultCodeService.fillResultStatus(instResult);

            // 状态可重发
            if (canRetryOrder(instResult, null, instOrder.getRetryTimes())) {
                return ResultProcessMode.RETRY;
            } else if (canSend2Counter(instOrder, instResult)) {
                return ResultProcessMode.COUNTER;
            }
        } catch (Exception e) {
            logger.info("obtainProcessMode.error:{}", e.getMessage());
        }
        return ResultProcessMode.DEFAULT;
    }


    private boolean canRetryOrder(InstOrderResult instResult, ChannelApiVO channelApi, int retryTimes) {
        if (channelApi == null) {
            return false;
        }
        Extension ext = null; //channelApi.getExtension();
        return ext != null
                && instResult.getProcessStatus().canResend()
                && StringUtils.isNumeric(ext.getValue(ChannelInfoExtKey.MAX_RESEND_TIMES.getCode()))
                && retryTimes <= Integer.valueOf(ext.getValue(ChannelInfoExtKey.MAX_RESEND_TIMES
                .getCode()));
    }


}
