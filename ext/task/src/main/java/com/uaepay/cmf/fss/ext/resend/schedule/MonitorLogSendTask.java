package com.uaepay.cmf.fss.ext.resend.schedule;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.support.TransactionTemplate;

import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import com.uaepay.cmf.common.core.engine.schedule.task.AbstractDaemonTask;
import com.uaepay.cmf.common.enums.MonitorItem;
import com.uaepay.cmf.common.enums.MonitorLogStatus;
import com.uaepay.cmf.common.monitor.MonitorLog;
import com.uaepay.cmf.domainservice.main.repository.MonitorLogRepository;

/**
 * 监控日志邮件发送任务.
 *
 * <AUTHOR> won
 * @version $Id: MonitorLogSendTask.java, v 0.1 2011-1-14 下午01:24:55 sean won Exp $
 */
public class MonitorLogSendTask extends AbstractDaemonTask<String> {
    Logger                              logger          = LoggerFactory
                                                            .getLogger(MonitorLogSendTask.class);
    private final static DaemonTaskType BATCH_TASK_TYPE = new DaemonTaskType("monitorLogSend",
                                                            "监控日志邮件");
    @Resource
    private MonitorLogRepository        monitorLogRepository;

    //CMF系统监控.
//    @Resource(name = "systemMonitor")
//    private SysMonitorProxy             systemMonitor;

    /** 事务模板 */
    @Resource
    protected TransactionTemplate       cmfTransactionTemplate;

    @Override
    public DaemonTaskType getTaskType() {
        return BATCH_TASK_TYPE;
    }

    @Override
    protected List<String> loadTask(final int batchSize) {
        boolean hasMore = true;
        try {
            while (hasMore) {
                List<MonitorLog> logs = null;
                try {
                    logs = cmfTransactionTemplate
                        .execute(status -> {
                            final List<MonitorLog> dbLogs = monitorLogRepository.loadByStatus(
                                batchSize, MonitorLogStatus.AWAITING);

                            //如果没记录，则表示无日志需要发送.
                            //如果查出来的日志不是AWAITING，表示已经被集群其他机器执行了
                            if (null == dbLogs || dbLogs.isEmpty()
                                || MonitorLogStatus.AWAITING != dbLogs.get(0).getStatus()) {
                                return null;
                            }

                            //1. 设置为“处理中”
                            if (CollectionUtils.isNotEmpty(populateLogIds(dbLogs))) {
                                monitorLogRepository.updateStatusWithPreStatus(
                                    populateLogIds(dbLogs), MonitorLogStatus.IN_PROCESS,
                                    MonitorLogStatus.AWAITING, "开始发送");
                            }

                            return dbLogs;
                        });
                } catch (Exception e) {
                    logger.error("无监控日志发送或其他任务正在发送!", e);
                }

                if (null == logs || logs.isEmpty()) {
                    logger.info("无监控日志发送.");
                    break;
                } else if (logs.size() <= batchSize) {
                    //退出循环
                    hasMore = false;
                }

                //2. 发送邮件; 邮件发送失败，状态会停留在IN_PROCESS；监控邮件不必补发。
                boolean needSend = checkNeedEmail(logs);
//                if (needSend) {
//                    systemMonitor.sendMonitorEmail(logs);
//                }

                //3. 设置为成功.
                String resultMessage = (needSend) ? "email发送成功" : "超时日志,不必发送";
                if (CollectionUtils.isNotEmpty(populateLogIds(logs))) {
                    monitorLogRepository.updateStatusWithPreStatus(populateLogIds(logs),
                        (needSend) ? MonitorLogStatus.SUCCESSFUL : MonitorLogStatus.NOT_SEND,
                        MonitorLogStatus.IN_PROCESS, resultMessage);
                }
            }
        } catch (Exception e) {
            logger.error("监控日志邮件发送异常!", e);
        }

        return new ArrayList<>();
    }

    /**
     * 若只有超时3秒钟的日志，就不必要发送了.
     * @param logs
     * @return
     */
    private boolean checkNeedEmail(List<MonitorLog> logs) {
        if (null == logs || logs.isEmpty()) {
            return false;
        }

        for (MonitorLog log : logs) {
            if (MonitorItem.PROCESS_TIME_OUT != log.getMonitorItem()) {
                return true;
            }
        }
        return false;
    }

    private List<String> populateLogIds(List<MonitorLog> logs) {
        if (logs == null) {
            return new ArrayList<>();
        }

        List<String> ids = new ArrayList<>();
        for (MonitorLog log : logs) {
            ids.add(log.getLogId().toString());
        }

        return ids;
    }

    @Override
    protected boolean executeTask(String domain) {
        return true;
    }

    @Override
    protected void sendToMonitor(String domain) {
    }

    @Override
    protected void monitorTotalCount(int totalCount) {
    }
}
