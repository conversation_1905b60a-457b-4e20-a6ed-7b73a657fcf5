package com.uaepay.cmf.fss.ext.resend.processor.handler;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.domainservice.main.process.ResendProcessor;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import org.springframework.stereotype.Service;

/**
 * <p>重试结果处理.</p>
 *
 * <AUTHOR>
 * @version RetryResultHandler.java 1.0 Created@2017-12-04 13:29 $
 */
@Service("retryResultHandler")
public class RetryResultHandler implements ResultHandler<InstOrder,ChannelFundResult, InstOrderResult>  {
    private Logger              logger = LoggerFactory.getLogger(RetryResultHandler.class);

    @Resource
    private ResendProcessor     resendProcessor;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Override
    public InstOrderResult handle(InstOrder instOrder, ChannelFundResult channelFundResult) {
        try {
            InstOrder sendOrder = instOrderRepository.loadById(instOrder.getInstOrderId(), false);
            //将订单置为待发送
            int count = instOrderRepository.updateCommunicateStatusWithPreStatus(sendOrder,
                CommunicateStatus.AWAITING, instOrder.getCommunicateStatus());

            if (count == 0) {
                logger.info("QueryResultProcessor.updateStatus.Fail");
                return null;
            }
            return resendProcessor.process(sendOrder);
        } catch (Exception e) {
            logger.info("QueryResultProcessor.send.error", e);
            return null;
        }
    }
}
