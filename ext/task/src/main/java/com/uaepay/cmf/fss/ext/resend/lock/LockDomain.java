package com.uaepay.cmf.fss.ext.resend.lock;

import com.uaepay.cmf.common.core.engine.lock.Lock;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version LockDomain.java 1.0 Created@2017-11-13 16:33 $
 */

public class LockDomain {
    private boolean lockSuccess;
    private Lock    lock;

    public LockDomain(boolean lockSuccess, Lock lock) {
        this.lockSuccess = lockSuccess;
        this.lock = lock;
    }

    public boolean isLockSuccess() {
        return lockSuccess;
    }

    public void setLockSuccess(boolean lockSuccess) {
        this.lockSuccess = lockSuccess;
    }

    public Lock getLock() {
        return lock;
    }

    public void setLock(Lock lock) {
        this.lock = lock;
    }
}
