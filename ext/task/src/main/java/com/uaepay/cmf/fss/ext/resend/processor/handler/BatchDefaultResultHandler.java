package com.uaepay.cmf.fss.ext.resend.processor.handler;

import javax.annotation.Resource;

import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.domainservice.batch.processor.BatchResultProcessor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>批量默认结果处理.</p>
 *
 * <AUTHOR>
 * @version DefaultBatchResultHandler.java 1.0 Created@2017-12-05 09:59 $
 */
@Slf4j
@Service("batchDefaultResultHandler")
public class BatchDefaultResultHandler
                                      implements
                                      ResultHandler<InstBatchOrder, ChannelFundBatchResult, InstBatchResult> {

    @Resource
    private BatchResultProcessor     batchResultProcessor;

    @Override
    public InstBatchResult handle(InstBatchOrder instBatchOrder,
                                  ChannelFundBatchResult channelFundBatchResult) {
        log.info("[补发批量机构订单有明确结果]:{}", instBatchOrder.getInstBatchNo());
        return batchResultProcessor.process(instBatchOrder,
            channelFundBatchResult, false);
    }

}
