package com.uaepay.cmf.fss.ext.resend.lock;

import com.uaepay.cmf.common.core.engine.lock.Lock;
import com.uaepay.cmf.common.core.engine.lock.enums.LockType;
import com.uaepay.cmf.common.core.engine.schedule.enums.DaemonTaskType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version TaskLockServiceImpl.java 1.0 Created@2017-11-13 16:21 $
 */
public class TaskLockServiceImpl implements TaskLockService {
    protected final Logger logger = LoggerFactory.getLogger(TaskLockServiceImpl.class);

    @Resource
    @Qualifier(value = "redisTemplate")
    private RedisTemplate redisTemplate;

    @Override
    public LockDomain lockTask(String key, DaemonTaskType taskType, long lockSeconds) {

        Lock lock = buildLock(key, taskType, lockSeconds);
        try {
            boolean status = redisTemplate.opsForValue().setIfAbsent(lock.getLockKey(), lock.getLockKey(), lockSeconds, TimeUnit.SECONDS);
            return new LockDomain(status, lock);
        } catch (Exception e) {
            logger.error("lockService.fail:Key-{},description-{}", key, taskType.getMessage(), e);
            return new LockDomain(false, lock);
        }

    }

    @Override
    public void release(LockDomain lockDomain) {
        redisTemplate.delete(lockDomain.getLock().getLockKey());
    }

    private static Lock buildLock(String key, DaemonTaskType taskType, long lockSeconds) {
        Lock lock = new Lock();
        String lockKey = key.length() > 80 ? key.substring(0, 80) : key;
        lock.setLockKey(lockKey + taskType.getCode());
        lock.setLockName(taskType.getMessage());
        lock.setLockSecond(lockSeconds);
        lock.setLockType(LockType.EXCLUSION);
        return lock;
    }

}
