<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.uaepay.fund.cmf</groupId>
        <artifactId>cmf-ext-parent</artifactId>
        <version>1.1.19-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cmf-ext-service</artifactId>

    <properties>
        <maven.install.skip>false</maven.install.skip>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.uaepay.fund.cmf</groupId>
            <artifactId>cmf-domainservice-main</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uaepay.fund.cmf</groupId>
            <artifactId>cmf-domainservice-batch</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.uaepay.cmf.service</groupId>-->
        <!--            <artifactId>cmf-service-facade</artifactId>-->
        <!--        </dependency>-->

        <!-- cxf spring boot-->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
        </dependency>

        <dependency>
            <groupId>com.uaepay.public.csa</groupId>
            <artifactId>csa-facade-compensate</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vaadin.external.google</groupId>
            <artifactId>android-json</artifactId>
            <version>0.0.20131108.vaadin1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.uaepay.public.csc</groupId>
            <artifactId>csc-compensation-facade</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.redisson</groupId>-->
<!--            <artifactId>redisson</artifactId>-->
<!--        </dependency>-->
    </dependencies>
</project>
