<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd"
	   default-autowire="byName">

	<!-- factory -->
	<bean id="validatorFactory" class="com.uaepay.cmf.ext.service.validator.factory.DefaultValidatorFactory">
		<property name="controlValidatorMap">
			<map>
				<entry key="ADVANCE" value-ref="controlRequestCommonValidator" />
				<entry key="AUTHENTICATE" value-ref="controlRequestCommonValidator" />
				<entry key="AUTHENTICATE_ADVANCE" value-ref="controlRequestCommonValidator" />
				<entry key="VOID_TRANSACTION" value-ref="voidTransactionValidator" />
				<entry key="PREAUTH_UPDATE" value-ref="preAuthRequestCommonValidator"/>
				<entry key="PREAUTH_COMPLETE" value-ref="preAuthRequestCommonValidator"/>
				<entry key="PREAUTH_VOID" value-ref="preAuthRequestCommonValidator"/>
			</map>
		</property>
		<property name="fundValidatorMap">
			<map>
				<entry key="FUNDIN" value-ref="fundRequestCommonValidator" />
				<entry key="FUNDOUT" value-ref="fundOutValidator" />
				<entry key="REFUND" value-ref="refundValidator" />
			</map>
		</property>
	</bean>

	<!--fundout-->
	<bean id="fundOutValidator" class="com.uaepay.validate.ValidatorChain">
		<property name="validators">
			<list>
				<ref bean="fundRequestCommonValidator" />
			</list>
		</property>
	</bean>

	<!--refund-->
	<bean id="refundValidator" class="com.uaepay.validate.ValidatorChain">
		<property name="validators">
			<list>
				<ref bean="refundRequestCommonValidator" />
			</list>
		</property>
	</bean>
</beans>