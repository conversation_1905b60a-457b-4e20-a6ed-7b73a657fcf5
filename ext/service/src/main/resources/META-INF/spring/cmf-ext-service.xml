<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd"
	   default-autowire="byName">

	<!-- 对外服务实现 -->
	<bean id="fundRequestFacade" class="com.uaepay.cmf.ext.service.impl.DefaultFundRequestFacade" />
	<bean id="controlRequestFacade" class="com.uaepay.cmf.ext.service.impl.DefaultControlRequestFacade"/>
	<bean id="resultNotifyFacade" class="com.uaepay.cmf.ext.service.impl.DefaultResultNotifyFacade" />
	<bean id="refundValidateFacade" class="com.uaepay.cmf.ext.service.impl.RefundFacadeImpl" />
	<bean id="channelReceiveFacade" class="com.uaepay.cmf.ext.service.impl.ChannelReceiveFacadeImpl" />
	<bean id="defaultCacheFacade" class="com.uaepay.cmf.ext.service.impl.DefaultCacheFacade" />
	<bean id="orderQueryFacade" class="com.uaepay.cmf.ext.service.impl.DefaultOrderQueryFacade" />
	<bean id="cardTokenFacade" class="com.uaepay.cmf.ext.service.impl.CardTokenFacadeImpl" />
	<bean id="grcNotifyFacade" class="com.uaepay.cmf.ext.service.grc.GrcNotifyFacadeImpl" />

	<bean id="instOrderProcessFacade" class="com.uaepay.cmf.ext.service.counter.DefaultInstOrderProcessFacade" />
	<bean id="queryChannelAccountBalanceFacade" class="com.uaepay.cmf.ext.service.counter.DefaultQueryChannelAccountBalanceFacade" />
	<bean id="channelCodeMappingFacade" class="com.uaepay.cmf.ext.service.counter.DefaultChannelCodeMappingFacade" />

</beans>