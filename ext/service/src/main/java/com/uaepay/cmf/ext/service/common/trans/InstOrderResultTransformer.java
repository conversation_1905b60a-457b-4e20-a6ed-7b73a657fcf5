package com.uaepay.cmf.ext.service.common.trans;

import java.text.SimpleDateFormat;
import java.util.Date;

import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.schema.cmf.enums.InstOrderStatus;

/**
 * 结果转换
 *
 * <AUTHOR> won
 * @version $Id: InstOrderResultTransformer.java, v 0.1 2011-3-14 下午02:30:03 sean won Exp $
 */
@Deprecated
public class InstOrderResultTransformer {

    public static InstOrderResult convert(com.uaepay.cmf.common.domain.base.InstOrderResult src) {
        com.uaepay.cmf.common.domain.base.InstOrderResult from = src;
        if (null == from) {
            return null;
        }
        //转换基本信息.
        InstOrderResult result = transformBaseInfo(from);
        //按是否返回银行真实信息
        transform(from, result);
        return result;
    }

    /**
     * 不包含银行真实渠道信息.
     *
     * @param src
     * @param result
     */
    public static void transform(com.uaepay.cmf.common.domain.base.InstOrderResult src,
                                 InstOrderResult result) {
        if (src.getInstReturnInfo() != null) {
            result.setInstResultCode(src.getInstReturnInfo().getReturnCode());
        }
        if (InstOrderStatus.S.equals(src.getInstOrderStatus())) {
            result.setStatus(InstOrderResultStatus.SUCCESSFUL);
            result.setProcessStatus(InstOrderProcessStatus.SUCCESS);
        } else if (InstOrderStatus.F.equals(src.getInstOrderStatus())) {
            result.setStatus(InstOrderResultStatus.FAILURE);
            result.setProcessStatus(InstOrderProcessStatus.SUCCESS);
        } else if (InstOrderStatus.N.equals(src.getInstOrderStatus())) {
            result.setStatus(InstOrderResultStatus.NONEXISTS);
            result.setProcessStatus(InstOrderProcessStatus.SUBMIT_INST_FAIL);
            result.setMemo("未查询到结果,订单提交失败");
        } else if (InstOrderStatus.I.equals(src.getInstOrderStatus())) {
            result.setStatus(InstOrderResultStatus.IN_PROCESS);
            result.setProcessStatus(InstOrderProcessStatus.AWAITING);
        }
        if (src.getExtension() != null) {
            result.getExtension().putAll(src.getExtension());
        }
    }

    /**
     * 转换基本信息.
     *
     * @param src
     * @return
     */
    public static InstOrderResult transformBaseInfo(com.uaepay.cmf.common.domain.base.InstOrderResult src) {
        InstOrderResult result = new InstOrderResult();

        result.setInstSeqNo(src.getInstReturnOrderNo());
        result.setInstOrderNo(src.getInstOrderNo());
        result.setBizType(src.getOrderType());
        result.setOperateStatus(InstResultOperateStatus.AWAITING);

        String str = "";
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        if (src.getGmtSettle() != null) {
            str = format.format(src.getGmtSettle());
        } else if (src.getGmtProcess() != null) {
            str = format.format(src.getGmtProcess());
        } else {
            str = format.format(new Date());
        }
        result.getExtension().put(ExtensionKey.CHANNEL_TRANS_TIME.key, str);

        if (src.getRealAmount() != null && src.getRealAmount().getAmount() != null) {
            result.setRealAmount(src.getRealAmount());
        }

        if (src.getReturnInfo() != null && src.getReturnInfo().getReturnMsg() != null) {
            //此处去除ReturnCode
            result.setMemo(src.getReturnInfo().getReturnMsg());
        }

        return result;
    }

}
