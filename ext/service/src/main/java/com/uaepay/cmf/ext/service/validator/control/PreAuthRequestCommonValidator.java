package com.uaepay.cmf.ext.service.validator.control;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.validate.Validator;
import com.uaepay.validate.exception.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p>PreAuthRequestCommonValidator</p>
 *
 * <AUTHOR>
 * @version PreAuthRequestCommonValidator.java v1.0  2022/10/9 23:25
 */
@Service("preAuthRequestCommonValidator")
@Slf4j
public class PreAuthRequestCommonValidator implements Validator {
    @Resource
    private OrderLoaderService orderLoaderService;

    @Resource
    private InstOrderRepository instOrderRepository;


    @Override
    public void validate(Object model) throws ValidationException {
        CmfControlRequest request = (CmfControlRequest) model;
        try {
            Assert.isTrue(StringUtils.isNotBlank(request.getRequestNo()), "请求号不能为空");
            //原支付流水号
            InstOrder preOrder = loadPreInstOrder(request);
            Assert.notNull(preOrder, "原订单不存在");

            //不同的预授权类型校验放在一起
            switch (request.getRequestType()) {
                case PREAUTH_UPDATE:
                case PREAUTH_COMPLETE:
                    //更新和完成 需要校验订单为申请成功
                    Assert.isTrue(preOrder.getStatus() == InstOrderStatus.HALF_SUCCESSFUL, "原订单状态不正确");
                    break;
                case PREAUTH_VOID:
                    //撤销需要校验原订单状态为完成或者申请成功 如果有金额必须与原金额相等，已撤销过不可以再撤销（包含有已经在撤销中的订单）
                    voidValidate(request, preOrder);
                    break;
                default:
                    break;
            }
        } catch (IllegalArgumentException e) {
            throw new ValidationException(e.getMessage());
        }
    }

    private void voidValidate(CmfControlRequest request, InstOrder preOrder) {
        //预授权撤销，申请成功的订单可以走撤销
        Assert.isTrue(InstOrderStatus.HALF_SUCCESSFUL == preOrder.getStatus(), "原订单状态不正确");

        //撤销理论上不需要传原订单金额，如果传了必须和原订单一致
        if (request.getAmount() != null) {
            Assert.isTrue(request.getAmount().equals(preOrder.getAmount()), "撤销金额与原订单金额必须一致");
        }
    }

    protected InstOrder loadPreInstOrder(CmfControlRequest request) {
        String orgiPaymentSeqNo = request.getExtension().getValue(ExtensionKey.ORGI_FUNDIN_ORDER_NO.key);
        if (StringUtils.isNotEmpty(orgiPaymentSeqNo)) {
            InstBaseOrder preOrder = orderLoaderService.loadPreOrder(RequestType.REFUND.name(), orgiPaymentSeqNo, null, null);
            Assert.notNull(preOrder, "original fund in order no is non-exists");
            request.setPreRequestNo(preOrder.getInstOrderNo());
        }
        return instOrderRepository.loadByNo(request.getPreRequestNo());
    }
}
