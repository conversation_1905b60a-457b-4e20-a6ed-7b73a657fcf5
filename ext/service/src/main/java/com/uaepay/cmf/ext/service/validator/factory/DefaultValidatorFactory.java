package com.uaepay.cmf.ext.service.validator.factory;

import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.validate.Validator;
import com.uaepay.validate.exception.ValidationException;

import java.util.Map;

/**
 * <p>校验器工厂默认实现</p>
 * <AUTHOR>
 * @version $Id: DefaultValidatorFactory.java, v 0.1 2012-8-18 上午10:27:11 fuyangbiao Exp $
 */
public class DefaultValidatorFactory implements ValidatorFactory {
    /** 控制校验器MAP */
    private Map<String, Validator> controlValidatorMap;
    /** 资金校验器MAP */
    private Map<String, Validator> fundValidatorMap;

    @Override
    public Validator load(ControlRequestType requestType) throws ValidationException {
        if (requestType == null) {
            throw new ValidationException("请求类型不能为空");
        }

        return controlValidatorMap.get(requestType.name());
    }

    @Override
    public Validator load(BizType bizType) throws ValidationException {
        if (bizType == null) {
            throw new ValidationException("业务类型不能为空");
        }

        return fundValidatorMap.get(bizType.name());
    }

    public void setControlValidatorMap(Map<String, Validator> controlValidatorMap) {
        this.controlValidatorMap = controlValidatorMap;
    }

    public void setFundValidatorMap(Map<String, Validator> fundValidatorMap) {
        this.fundValidatorMap = fundValidatorMap;
    }
}
