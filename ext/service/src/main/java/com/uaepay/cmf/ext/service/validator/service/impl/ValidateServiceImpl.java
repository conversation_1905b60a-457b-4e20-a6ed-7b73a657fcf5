package com.uaepay.cmf.ext.service.validator.service.impl;

import com.uaepay.cmf.common.core.domain.exception.DateValidateException;
import com.uaepay.cmf.common.core.domain.exception.RefundVerifyException;
import com.uaepay.cmf.domainservice.main.convert.CmfResultConverter;
import com.uaepay.cmf.ext.service.validator.factory.ValidatorFactory;
import com.uaepay.cmf.ext.service.validator.service.ValidateService;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import com.uaepay.validate.exception.ValidationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version ValidateServiceImpl.java 1.0 Created@2018-03-02 14:26 $
 */
@Service
public class ValidateServiceImpl implements ValidateService {
    private Logger           logger = LoggerFactory.getLogger(ValidateServiceImpl.class);

    @Resource
    private ValidatorFactory validatorFactory;

    @Override
    public CmfFundResult validate(CmfRequest request) {
        try {
            // 1.请求验证
            validatorFactory.load(request.getBizType()).validate(request);
        } catch (ValidationException e) {
            return CmfResultConverter.buildFundResult(CmfFundResultCode.PARAMETER_INVALID,
                e.getMessage());
        } catch (RefundVerifyException rve) {
            // 由于退款订单可能超时重发，后一笔校验不通过返回处理中
            logger.warn("fundProcessService.rve:{}", rve.getMessage());
            return CmfResultConverter.buildFundResult(CmfFundResultCode.IN_PROCESS,
                rve.getMessage());
        } catch (DateValidateException de) {
            logger.info("订单超过允许发送时间");
            return CmfResultConverter
                .buildFundResult(CmfFundResultCode.IN_PROCESS, de.getMessage());
        }
        return null;
    }
}
