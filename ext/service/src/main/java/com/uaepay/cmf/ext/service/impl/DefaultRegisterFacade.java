package com.uaepay.cmf.ext.service.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.domainservice.main.general.impl.APlusMerchantRegisterProcessor;
import com.uaepay.cmf.domainservice.main.general.impl.APlusMerchantRegisterQueryProcessor;
import com.uaepay.cmf.service.facade.api.RegisterFacade;
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterQueryRequest;
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterQueryResponse;
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterRequest;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version: DefaultRegisterFacade.class v1.0
 */
@Service
public class DefaultRegisterFacade implements RegisterFacade {

    @Resource
    private APlusMerchantRegisterProcessor aPlusMerchantRegisterProcessor;
    @Resource
    private APlusMerchantRegisterQueryProcessor aPlusMerchantRegisterQueryProcessor;


    @Override
    public CommonResponse registerAPlusMerchant(APlusMerchantRegisterRequest request) {
        return aPlusMerchantRegisterProcessor.process(request);
    }

    @Override
    public APlusMerchantRegisterQueryResponse queryRegisterResponse(APlusMerchantRegisterQueryRequest request) {
        return aPlusMerchantRegisterQueryProcessor.process(request);
    }
}
