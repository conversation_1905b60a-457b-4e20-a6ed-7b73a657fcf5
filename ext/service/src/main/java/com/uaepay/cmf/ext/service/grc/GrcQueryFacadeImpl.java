package com.uaepay.cmf.ext.service.grc;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.TypeReference;
import com.uaepay.basis.beacon.service.facade.domain.response.ObjectQueryResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository;
import com.uaepay.cmf.service.facade.domain.counter.InstOrderVO;
import com.uaepay.cmf.service.facade.domain.grc.QueryOrderAllInfoRequest;
import com.uaepay.cmf.service.facade.grc.GrcQueryFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;


/**
 * <AUTHOR>
 * @date 18/02/2025 09:37
 */
@Service
@Slf4j
public class GrcQueryFacadeImpl implements GrcQueryFacade {

    @Resource
    private CmfOrderRepository cmfOrderRepository;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private InstOrderResultRepository instOrderResultRepository;

    /**
     * Channel field mapping configuration for normalizing different channel's field names.
     */
    @Value("${cmf.grc.response.alias:{\"CKO\":{\"auth_code\":\"authCode\",\"response_summary\":\"acquirerMessage\",\"avs_check\":\"avsCheck\",\"cvv_check\":\"cvvCheck\",\"payment_type\":\"paymentType\"},\"MPGS\":{\"auth_code\":\"authCode\",\"eci\":\"resEci\",\"source\":\"paymentType\"}}}")
    private String keyMappingConfigAlias;

    /**
     * Cache for the parsed channel mappings configuration
     */
    private Map<String, Map<String, String>> channelMappingsCache;

    @PostConstruct
    public void init() {
        try {
            if (StringUtils.isNotBlank(keyMappingConfigAlias)) {
                channelMappingsCache = JSON.parseObject(
                        keyMappingConfigAlias,
                        new TypeReference<Map<String, Map<String, String>>>() {
                        }
                );
                log.info("Initialized channel field mapping configuration with {} channel definitions",
                        channelMappingsCache.size());
            } else {
                log.warn("Channel field mapping configuration is empty");
                channelMappingsCache = new HashMap<>();
            }
        } catch (JSONException e) {
            log.error("Failed to parse channel field mapping configuration: {}", e.getMessage(), e);
            channelMappingsCache = new HashMap<>();
        }
    }

    @Override
    public ObjectQueryResponse<InstOrderVO> queryOrderAllInfo(QueryOrderAllInfoRequest request) {
        if (request == null || StringUtils.isBlank(request.getPaymentSeqNo())) {
            log.warn("Invalid request: paymentSeqNo is null or empty");
            ObjectQueryResponse<InstOrderVO> response = new ObjectQueryResponse<>();
            response.setApplyStatus(ApplyStatusEnum.SUCCESS);
            response.setCode(ErrorCode.ORDER_NOT_FOUND.getErrorCode());
            return response;
        }

        try {
            CmfOrder cmfOrder = cmfOrderRepository.loadByPaymentSeqNo(request.getPaymentSeqNo(), null);
            if (cmfOrder != null) {
                List<InstOrder> instOrderList = instOrderRepository.loadByCmfSeqNo(cmfOrder.getOrderSeqNo());
                if (!CollectionUtils.isEmpty(instOrderList)) {
                    InstOrderVO instOrderVO = convertToVO(instOrderList.get(0));
                    log.info("[Grc.queryOrderAllInfo] InstOrderVO={}", instOrderVO);
                    return new ObjectQueryResponse<InstOrderVO>().success(instOrderVO);
                }
            }

            log.warn("Grc.queryOrderAllInfo.can't find the inst order, paymentSeqNo: {}", request.getPaymentSeqNo());
            ObjectQueryResponse<InstOrderVO> response = new ObjectQueryResponse<>();
            response.setApplyStatus(ApplyStatusEnum.SUCCESS);
            response.setCode(ErrorCode.ORDER_NOT_FOUND.getErrorCode());
            return response;

        } catch (Exception e) {
            log.error("Grc.queryOrderAllInfo.error-paymentSeqNo: {}", request.getPaymentSeqNo(), e);
            ObjectQueryResponse<InstOrderVO> response = new ObjectQueryResponse<>();
            response.setApplyStatus(ApplyStatusEnum.ERROR);
            response.setCode(ErrorCode.CMF_SYSTEM_ERROR.getErrorCode());
            return response;
        }
    }

    /**
     * Convert InstOrder entity to InstOrderVO for API response
     *
     * @param instOrder The institution order to convert
     * @return The InstOrderVO object or null if input is null
     */
    public InstOrderVO convertToVO(InstOrder instOrder) {
        if (instOrder == null) {
            return null;
        }
        InstOrderVO vo = new InstOrderVO();

        BeanUtils.copyProperties(instOrder, vo);

        // Set the gateOrderNo from extension
        String gateOrderNo = getExtensionValue(instOrder.getExtension(), ExtensionKey.GATE_ORDER_NO.key);
        vo.setGateOrderNo(gateOrderNo);

        vo.setInstStatus(instOrder.getStatus().getCode());
        vo.setArchiveBatchNo(instOrder.getArchiveBatchId());
        vo.setBizType(instOrder.getBizType().getCode());

        // Get payment sequence number from related cmf order
        CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(instOrder.getCmfSeqNo(), false);
        if (cmfOrder != null) {
            vo.setPaymentSeqNo(cmfOrder.getPaymentSeqNo());
        }

        // Get order results - getAllResult now always returns a non-null list (empty if no records)
        List<InstOrderResult> resultList = instOrderResultRepository.getAllResult(instOrder.getInstOrderId());
        if (CollectionUtils.isEmpty(resultList)) {
            // Early return if no result records are available
            vo.setExtension(JSON.toJSONString(instOrder.getExtension()));
            vo.setCanManualChange(false);
            return vo;
        }

        // Get the first result for memo and result codes
        InstOrderResult instOrderResult = resultList.get(0);
        vo.setMemo(instOrderResult.getMemo());

        // Create a new extension map to avoid modifying the original
        Map<String, String> extensionMap = new HashMap<>(instOrder.getExtension());

        // Add API result codes from result if available
        if (instOrderResult.getApiResultCode() != null) {
            extensionMap.put(ExtensionKey.API_RESULT_CODE.key, instOrderResult.getApiResultCode());
        }
        if (instOrderResult.getApiResultSubCode() != null) {
            extensionMap.put(ExtensionKey.API_RESULT_SUB_CODE.key, instOrderResult.getApiResultSubCode());
        }

        // Combine extensions from all result records
        Map<String, String> combinedResultExtMap = collectResultExtensions(resultList);

        // Normalize the channel-specific fields using the cached mappings
        Map<String, String> normalizedMap = normalizeChannelFields(
                combinedResultExtMap, extensionMap, instOrder.getFundChannelCode());

        // Add the normalized fields to the extension map
        extensionMap.putAll(normalizedMap);

        vo.setExtension(JSON.toJSONString(extensionMap));
        vo.setCanManualChange(false);
        return vo;
    }

    /**
     * Safely get a value from an extension map
     *
     * @param extension The extension map
     * @param key       The key to look up
     * @return The value or null if not found or map is null
     */
    private String getExtensionValue(Map<String, String> extension, String key) {
        if (extension == null || key == null) {
            return null;
        }
        return extension.get(key);
    }

    /**
     * Collect all extension values from a list of results into a single map
     *
     * @param resultList The list of results to process
     * @return A map containing all extension values
     */
    private Map<String, String> collectResultExtensions(List<InstOrderResult> resultList) {
        Map<String, String> combinedExtMap = new HashMap<>();

        if (CollectionUtils.isEmpty(resultList)) {
            return combinedExtMap;
        }

        for (InstOrderResult result : resultList) {
            if (result.getExtension() != null && !result.getExtension().isEmpty()) {
                combinedExtMap.putAll(result.getExtension());
            }
        }

        return combinedExtMap;
    }

    /**
     * Normalizes different channel-specific field names to standardized ones
     * using the cached channel mapping configuration.
     *
     * @param sourceExtMap Source extension map containing channel-specific field names
     * @param orderExtMap  Order extension map (not modified)
     * @param channelCode  The channel code to identify which normalization to apply
     * @return A new map with normalized field names
     */
    private Map<String, String> normalizeChannelFields(
            Map<String, String> sourceExtMap,
            Map<String, String> orderExtMap,
            String channelCode) {

        Map<String, String> normalizedMap = new HashMap<>();

        // Early return for invalid inputs
        if (sourceExtMap == null || sourceExtMap.isEmpty() || StringUtils.isBlank(channelCode)) {
            return normalizedMap;
        }

        try {
            AtomicReference<Map<String, String>> fieldMappings = new AtomicReference<>();
            channelMappingsCache.entrySet().forEach(entry -> {
                String channelPrefix = entry.getKey();
                if (channelCode.startsWith(channelPrefix)) {
                    fieldMappings.set(entry.getValue());
                    log.debug("Using prefix match for channel code: {} -> {}", channelCode, channelPrefix);
                }
            });
            // If no exact match found, look for prefix matches
            if (fieldMappings.get() == null) {
                fieldMappings.set(findPrefixMatch(channelCode));
            }

            // Apply field mappings if found
            if (fieldMappings.get() != null) {
                mapFieldsByConfiguration(sourceExtMap, orderExtMap, fieldMappings.get(), normalizedMap);
            } else {
                log.debug("No field mapping found for channel: {}", channelCode);
            }

        } catch (Exception e) {
            log.warn("Error normalizing channel fields for channel {}: {}", channelCode, e.getMessage());
        }

        return normalizedMap;
    }

    /**
     * Find a channel mapping configuration by prefix matching
     *
     * @param channelCode The channel code to match
     * @return The mapping configuration or null if not found
     */
    private Map<String, String> findPrefixMatch(String channelCode) {
        for (Map.Entry<String, Map<String, String>> entry : channelMappingsCache.entrySet()) {
            String channelPrefix = entry.getKey();
            if (channelCode.startsWith(channelPrefix)) {
                log.debug("Using prefix match for channel code: {} -> {}", channelCode, channelPrefix);
                return entry.getValue();
            }
        }
        return null;
    }

    /**
     * Apply field mappings according to the configuration
     *
     * @param sourceExtMap  Source extension map
     * @param orderExtMap   Order extension map
     * @param fieldMappings Field mapping configuration
     * @param normalizedMap Map to store normalized values
     */
    private void mapFieldsByConfiguration(
            Map<String, String> sourceExtMap,
            Map<String, String> orderExtMap,
            Map<String, String> fieldMappings,
            Map<String, String> normalizedMap) {

        for (Map.Entry<String, String> fieldMapping : fieldMappings.entrySet()) {
            String sourceField = fieldMapping.getKey();
            String targetField = fieldMapping.getValue();

            // Check source extension map
            if (sourceExtMap.containsKey(sourceField)) {
                normalizedMap.put(targetField, sourceExtMap.get(sourceField));
            }

            // Check order extension map (without modifying it)
            if (orderExtMap != null && orderExtMap.containsKey(sourceField)) {
                normalizedMap.put(targetField, orderExtMap.get(sourceField));
            }
        }
    }
}
