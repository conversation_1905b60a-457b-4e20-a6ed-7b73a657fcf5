package com.uaepay.cmf.ext.service.counter;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.domain.router.ApiRouteParam;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.process.ChannelBalanceService;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.cmf.service.facade.counter.QueryChannelAccountBalanceFacade;
import com.uaepay.cmf.service.facade.domain.counter.QueryAccountBalanceRequest;
import com.uaepay.cmf.service.facade.domain.counter.QueryAccountBalanceResponse;
import com.uaepay.common.util.money.Money;
import com.uaepay.router.service.facade.domain.RouteResponse;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.uaepay.cmf.common.core.domain.constants.BasicConstant.ACCOUNT_BALANCE;
import static com.uaepay.cmf.common.core.domain.constants.BasicConstant.THRESHOLD;
import static com.uaepay.cmf.common.core.domain.enums.ChannelInfoExtKey.ACCOUNT_TYPE;
import static com.uaepay.cmf.common.core.domain.enums.ChannelInfoExtKey.PAYOUT_ACCOUNT;

/**
 * <AUTHOR>
 * @date 27/08/2024 15:23
 */
@Slf4j
@Service
public class DefaultQueryChannelAccountBalanceFacade implements QueryChannelAccountBalanceFacade {

    @Resource
    private RouterClient routerClient;

    @Resource
    private ChannelBalanceService channelBalanceService;

    @Resource(name = "memoryCacheClient")
    private CacheClient cacheClient;

    private static final String SYNC_INTERVAL_KEY = "syncInterval";

    @Override
    public QueryAccountBalanceResponse queryAccountBalance(QueryAccountBalanceRequest request) {
        QueryAccountBalanceResponse response = new QueryAccountBalanceResponse();

        log.info("QueryChannelAccountBalance.queryAccountBalance.request:{}", request);
        try {
            ApiRouteParam routeParam = ApiRouteParam.builder().channelCode(request.getChannelCode()).apiType(ControlRequestType.QUERY_BALANCE.getCode())
                    .genOrderInfo(false).build();
            RouteResponse<ChannelVO> route = routerClient.route(routeParam);

            ChannelVO channel = route.getChannel();
            if (!route.getApplyStatus().equals(ApplyStatusEnum.SUCCESS) || channel == null) {
                response.setApplyStatus(ApplyStatusEnum.FAIL);
                response.setMessage("query api fail");
                return response;
            }

            String payoutAccount = ChannelUtil.getExtVal(channel.getExtList(), PAYOUT_ACCOUNT);
            if (StringUtils.isBlank(payoutAccount)) {
                response.setApplyStatus(ApplyStatusEnum.ERROR);
                response.setMessage("account is not config");
                return response;
            }
            String genOrderNo = routerClient.genOrderNo(request.getChannelCode(), FundChannelApiType.QUERY_BALANCE);
            Map<String, String> extension = new HashMap<>();
            extension.put(ACCOUNT_TYPE.getCode(), request.getAccountType());
            extension.put(PAYOUT_ACCOUNT.getCode(), payoutAccount);
            ChannelFundResult channelFundResult = channelBalanceService.queryBalanceResult(channel, extension, genOrderNo);
            log.info("QueryChannelAccountBalance.queryAccountBalance.response:{}", channelFundResult);
            Money balance = channelFundResult.getRealAmount();
            if (balance == null || balance.getAmount() == null) {
                log.info("{}.checkBalance.fail:{}", channel.getChannelCode(), balance);
                response.setApplyStatus(ApplyStatusEnum.FAIL);
                response.setMessage("query channel balance fail");
            } else {
                Map<String, Money> data = new HashMap<>();
                data.put(THRESHOLD, request.getThreshold());
                data.put(ACCOUNT_BALANCE, channelFundResult.getRealAmount());
                cacheClient.put(CacheType.ACCOUNT_BALANCE, payoutAccount, data, getExpireTime(request.getExtension()));
                response.setAccountBalance(channelFundResult.getRealAmount());
                response.setApplyStatus(ApplyStatusEnum.SUCCESS);
            }
        } catch (Exception e) {
            log.error("QueryChannelAccountBalance.queryAccountBalance.error,", e);
            response.setApplyStatus(ApplyStatusEnum.ERROR);
            response.setMessage("queryAccountBalance error");
        }

        return response;
    }

    private int getExpireTime(Map<String, String> extension) {
        if (extension == null || StringUtils.isBlank(extension.get(SYNC_INTERVAL_KEY))) {
            return 30;
        }

        return Integer.valueOf(extension.get(SYNC_INTERVAL_KEY)) + 5;
    }
}
