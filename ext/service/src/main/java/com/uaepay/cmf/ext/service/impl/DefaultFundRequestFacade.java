package com.uaepay.cmf.ext.service.impl;

import javax.annotation.Resource;

import com.uaepay.cmf.domainservice.main.process.DuplicateResultProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;

import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.util.filter.LogFilterUtil;
import com.uaepay.cmf.domainservice.main.convert.CmfResultConverter;
import com.uaepay.cmf.ext.service.process.ProcessService;
import com.uaepay.cmf.service.facade.api.FundRequestFacade;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.common.domain.OperationEnvironment;

import java.util.Objects;

/**
 * 出款,入款冲退门面请求实现
 *
 * <AUTHOR> won
 * @version $Id: DefaultFundRequestFacade.java, v 0.1 2011-3-21 下午04:44:33 sean
 *          won Exp $
 */
@Service
@Slf4j
public class DefaultFundRequestFacade implements FundRequestFacade {

    @Resource
    private ProcessService<CmfRequest, CmfFundResult> fundProcessService;

    @Resource
    private DuplicateResultProcessService duplicateResultProcessService;

    /**
     * 资金类请求
     * @param request
     * @param environment
     * @return
     */
    @Override
    public CmfFundResult apply(CmfRequest request, OperationEnvironment environment) {

        try {
            log.info("fundRequest.req:{}", LogFilterUtil.filter(request.toString()));

            CmfFundResult result = fundProcessService.process(request);

            log.info("fundRequest.resp:{}", LogFilterUtil.filter(result.toString()));

            return result;
        } catch (Exception e) {
            log.error("fundRequest.apply.error", e);
            return CmfResultConverter.fail(null, InstOrderProcessStatus.FAILURE,
                request.getBizType(), e);
        }
    }

    /**
     *
     * 退款-资金请求的不同入口
     * @param request
     * @param environment
     * @return
     */
    @Override
    public CmfFundResult refund(CmfRequest request, OperationEnvironment environment) {
        try {
            log.info("充退请求:{}", LogFilterUtil.filter(request.toString()));

            CmfFundResult result = fundProcessService.process(request);

            log.info("充退响应:{}", LogFilterUtil.filter(result.toString()));

            return result;
        } catch (Exception e) {
            log.error("[充退]处理异常(paymentSeqNo=" + request.getPaymentSeqNo() + "):", e);
            return CmfResultConverter.fail(null, InstOrderProcessStatus.FAILURE,
                request.getBizType(), e);
        }
    }
}
