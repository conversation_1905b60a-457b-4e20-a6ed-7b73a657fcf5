package com.uaepay.cmf.ext.service.process.impl;

import com.uaepay.cmf.domainservice.main.process.MonitorService;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.ext.service.process.ProcessService;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
 * <p>抽象处理服务.</p>
 *
 * <AUTHOR>
 * @version AbstractProcessService.java 1.0 Created@2017-04-05 11:41 $
 */
public abstract class AbstractProcessService<Req, Resp> implements ProcessService<Req, Resp> {

    /**
     * CMF仓储服务
     */
    @Resource
    protected CmfOrderRepository cmfOrderRepository;

    @Resource
    protected InstOrderRepository instOrderRepository;

    /**
     * 监控报警
     */
    @Resource
    protected MonitorService monitorService;

    /**
     * 事务模板
     */
    @Resource(name = "cmfTransactionTimeoutTemplate")
    protected TransactionTemplate cmfTransactionTimeoutTemplate;


}
