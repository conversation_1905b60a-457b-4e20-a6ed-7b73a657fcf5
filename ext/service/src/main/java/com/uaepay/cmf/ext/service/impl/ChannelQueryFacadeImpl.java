package com.uaepay.cmf.ext.service.impl;

import com.google.common.base.Joiner;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.service.facade.api.ChannelQueryFacade;
import com.uaepay.cmf.service.facade.domain.fundin.SubscriptionChannelQueryRequest;
import com.uaepay.cmf.service.facade.result.SubscriptionChannelQueryResult;
import com.uaepay.common.util.money.Money;
import com.uaepay.router.service.facade.RouteFacade;
import com.uaepay.router.service.facade.domain.RouteRequest;
import com.uaepay.router.service.facade.domain.RouteResponse;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;

import static com.uaepay.cmf.common.core.domain.enums.ExtensionKey.*;

@Slf4j
@Service
public class ChannelQueryFacadeImpl implements ChannelQueryFacade, BasicConstant {

    @Reference
    private RouteFacade routeFacade;

    @Override
    public SubscriptionChannelQueryResult querySubscriptionChannel(SubscriptionChannelQueryRequest request) {

        SubscriptionChannelQueryResult result = new SubscriptionChannelQueryResult(false);
        if (request.getSignChannels() == null || request.getSignChannels().isEmpty()){
            return result;
        }

        try {
            RouteRequest routeRequest = buildRouteRequest(request);
            RouteResponse<ChannelVO> routeResponse = routeFacade.preRoute(routeRequest);
            if (routeResponse != null && routeResponse.getChannel() != null){
                result.setAvailable(true);
            }
        } catch (Exception e){
            log.warn("ChannelQueryFacade.querySubscriptionChannel failed!", e);
        }

        log.info("ChannelQueryFacade.querySubscriptionChannel.result:{}", result);
        return result;
    }

    private RouteRequest buildRouteRequest(SubscriptionChannelQueryRequest request){

        request.getExtension().put(BIZ_PRODUCT_CODE.getKey(), request.getBizProductCode());
        request.getExtension().put(COMPANY_OR_PERSONAL.getKey(), request.getCompanyOrPersonal());
        request.getExtension().put(DBCR.getKey(), request.getCardType());
        request.getExtension().put(MEMBER_ID.getKey(), request.getMemberId());
        request.getExtension().put(TOPAY_MERCHANT_ID.getKey(), request.getMerchantId());
        request.getExtension().put(FRICTIONLESS.getKey(), "Y");
        request.getExtension().put(SIGNED_SOURCE.getKey(), Joiner.on(",").join(request.getSignChannels()));
        request.getExtension().put(IS_3DS.getKey(), "N");
        request.getExtension().put(CARD_BRAND.getKey(), request.getCardBrand());

        RouteRequest routeRequest = new RouteRequest();

        routeRequest.setAmount(request.getAmount());
        routeRequest.setBizProductCode(request.getBizProductCode());
        routeRequest.setBizType(BizType.FUNDIN.getCode());
        routeRequest.setCompanyOrPersonal(request.getCompanyOrPersonal());
        routeRequest.setDbcr(request.getCardType());
        routeRequest.setInstCode(request.getInstCode());
        routeRequest.setMemberId(request.getMemberId());
        routeRequest.setMerchantId(request.getMerchantId());
        routeRequest.setPayMode(request.getPayMode());
        routeRequest.setRequestType(RequestType.FUND_IN.getCode());
        routeRequest.setExtension(request.getExtension());
        routeRequest.setClientId(CLIENT_ID);


        return routeRequest;
    }
}
