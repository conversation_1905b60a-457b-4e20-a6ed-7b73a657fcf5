package com.uaepay.cmf.ext.service.counter;

import com.alibaba.fastjson.JSON;
import com.uaepay.basis.beacon.common.exception.ErrorException;
import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.domain.response.ObjectQueryResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.institution.*;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.batch.processor.BatchResultProcessor;
import com.uaepay.cmf.domainservice.batch.processor.FundOutRetryProcessor;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.general.impl.UpdateBookingProcessor;
import com.uaepay.cmf.domainservice.main.process.DistributeQueryService;
import com.uaepay.cmf.domainservice.main.process.NotifyPaymentService;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository;
import com.uaepay.cmf.domainservice.main.result.ControlResultProcessor;
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor;
import com.uaepay.cmf.fss.ext.integration.ues.UesClient;
import com.uaepay.cmf.service.facade.counter.InstOrderProcessFacade;
import com.uaepay.cmf.service.facade.domain.counter.*;
import com.uaepay.cmf.service.facade.domain.fundout.FundOutRetryRequest;
import com.uaepay.cmf.service.facade.domain.fundout.UpdateBookingTimeRequest;
import com.uaepay.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * CMF机构订单处理服务--提供给Counter调用.
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: DefaultInstOrderProcessFacade.java, v 0.1 2011-3-21 下午04:44:33 sean won Exp $
 */
@Service
public class DefaultInstOrderProcessFacade implements InstOrderProcessFacade, BasicConstant {

    private static final Logger logger = LoggerFactory.getLogger(DefaultInstOrderProcessFacade.class);

    @Resource
    InstOrderRepository instOrderRepository;
    @Resource
    InstControlOrderRepository instControlOrderRepository;
    @Resource
    CmfOrderRepository cmfOrderRepository;
    @Resource
    InstOrderResultRepository instOrderResultRepository;
    @Resource
    private NotifyPaymentService notifyPaymentService;
    @Resource
    DistributeQueryService distributeQueryService;
    @Resource
    private InstResultProcessor instResultProcessor;
    @Resource
    private ControlResultProcessor controlResultProcessor;
    @Resource
    private UesClient uesClient;
    @Resource
    private BatchResultProcessor batchResultProcessor;
    @Resource
    private UpdateBookingProcessor updateBookingProcessor;
    @Resource
    private FundOutRetryProcessor fundOutRetryProcessor;
    @Resource
    private ChannelApiRouter channelApiRouter;

    @Override
    public InstOrderVO getInstOrder(String instOrderNo) {
        Assert.isTrue(StringUtils.isNotEmpty(instOrderNo));
        logger.info("[查询订单]instOrderNo={}", instOrderNo);
        InstOrder instOrder = instOrderRepository.loadByNo(instOrderNo);
        InstOrderVO instOrderVO = convert2VO(instOrder);
        logger.info("[查询订单结果]InstOrderVO={}", instOrderVO);
        return instOrderVO;
    }

    @Override
    public ObjectQueryResponse<InstOrderVO> getInstOrderByPaymentSeqNo(String paymentSeqNo) {

        try {
            CmfOrder cmfOrder = cmfOrderRepository.loadByPaymentSeqNo(paymentSeqNo, null);
            if (cmfOrder != null){
                List<InstOrder> instOrderList = instOrderRepository.loadByCmfSeqNo(cmfOrder.getOrderSeqNo());
                if (!CollectionUtils.isEmpty(instOrderList)){
                    InstOrderVO instOrderVO = convert2VO(instOrderList.get(0));
                    logger.info("[查询订单结果]InstOrderVO={}", instOrderVO);
                    return new ObjectQueryResponse<InstOrderVO>().success(instOrderVO);
                }
            }

            logger.warn("can't find the inst order,and paymentSeqNo: {}", paymentSeqNo);
            ObjectQueryResponse<InstOrderVO> response = new ObjectQueryResponse<>();
            response.setApplyStatus(ApplyStatusEnum.SUCCESS);
            response.setCode(ErrorCode.ORDER_NOT_FOUND.getErrorCode());
            return response;

        } catch (Exception e) {
            logger.error("getInstOrderByPaymentSeqNo.error-paymentSeqNo:{}", paymentSeqNo, e);
            ObjectQueryResponse<InstOrderVO> response = new ObjectQueryResponse<>();
            response.setApplyStatus(ApplyStatusEnum.ERROR);
            response.setCode(ErrorCode.CMF_SYSTEM_ERROR.getErrorCode());
        }

        return null;
    }

    @Override
    public List<InstOrderVO> getInstOrders(InstOrderQuery query) {
        logger.info("[查询出款订单]InstOrderQuery={}", query);
        if (StringUtils.isEmpty(query.getInstOrderNo())) {
            return null;
        }
        InstOrderVO instOrderVO = getInstOrder(query.getInstOrderNo());

        return Arrays.asList(instOrderVO);
    }


    @Override
    public BaseResult notifyPE(String cmfSeqNo) {
        logger.info("instOrderProcessFacade.notifyPE.cmfSeqNo:{}", cmfSeqNo);
        BaseResult result = new BaseResult();
        notifyPaymentService.notifyPE(cmfSeqNo, true);
        result.setResultMessage("通知PE成功");
        result.setSuccess(true);
        logger.info("instOrderProcessFacade.notifyPE.success:{}", cmfSeqNo);
        return result;
    }


    @Override
    public QueryOrderResult queryInstOrderResult(String instOrderNo) {
        try {
            logger.info("订单状态查询,订单号={}", instOrderNo);
            InstControlOrder instControlOrder = instControlOrderRepository.loadByNo(instOrderNo);
            if (instControlOrder != null) {
                instOrderNo = instControlOrder.getPreRequestNo();
            }
            InstOrder instOrder = instOrderRepository.loadByNo(instOrderNo);

            if (needProcessLockFlag(instOrder)) {
                instOrderRepository.updateFlagWithOrderIdAndPreFlag(instOrder.getInstOrderId(), OrderFlag.DEFAULT, instOrder.getFlag());
            }

            if (instOrder == null) {
                return new QueryOrderResult(com.uaepay.schema.cmf.enums.InstOrderStatus.F, "查询订单不存在异常");
            } else if (instOrder.getIsAdvance() == IsAdvance.YES) {
                return new QueryOrderResult(com.uaepay.schema.cmf.enums.InstOrderStatus.F, "订单未推进，不允许查询");
            }
            if (needProcessCommunicateStatus(instOrder)) {
                CommunicateStatus sentStatus = CommunicateStatus.SENT;
                instOrderRepository.updateCommunicateStatusById(sentStatus, instOrder.getInstOrderId());
                instOrder.setCommunicateStatus(sentStatus);
            }

            if (instControlOrder != null) {
                ChannelResult channelResult = distributeQueryService.queryControlResult(instControlOrder);
                InstControlOrderResult instControlOrderResult = controlResultProcessor.process(instControlOrder, channelResult);
                return convertResult(channelResult, instControlOrderResult.getStatus(), instControlOrder.getInstOrderNo());
            }

            if (instOrder.getCommunicateType() == InstOrderCommunicateType.BATCH) {
                ChannelFundResult channelFundResult = bulkQuery(instOrder);
                return convertQueryResult(channelFundResult, instOrderNo);
            }
            ChannelFundResult channelFundResult = distributeQueryService.queryResult(instOrder);

            InstOrderResult instOrderResult = instResultProcessor.process(instOrder, channelFundResult);
            return convertQueryResult(channelFundResult, instOrderResult, instOrderNo);
        } catch (Exception e) {
            logger.error("机构订单" + instOrderNo + "查询异常", e);
            return new QueryOrderResult(com.uaepay.schema.cmf.enums.InstOrderStatus.I, "查询异常");
        }
    }

    @Override
    public QueryRefundResponse queryRefundListByInstOrderNo(QueryRefundRequest request) {
        logger.info("instOrderProcessFacade.queryRefundListByInstOrderNo.request:{}", request);
        QueryRefundResponse response = new QueryRefundResponse();
        try {
            InstOrder instOrder = instOrderRepository.loadByNo(request.getInstOrderNo());

            Assert.notNull(instOrder, "can't find instOrder by instOrderNo");
            InstOrderVO instOrderVO = convert2VO(instOrder);
            response.setInstOrder(instOrderVO);

            List<InstOrderStatus> instOrderStatus = new ArrayList<>(Arrays.asList(InstOrderStatus.IN_PROCESS, InstOrderStatus.SUCCESSFUL, InstOrderStatus.RISK));
            List<InstOrder> refundList = instOrderRepository.getRefundOrderByFundInOrder(request.getInstOrderNo(), instOrderStatus);

            if (!CollectionUtils.isEmpty(refundList)) {
                List<InstOrderVO> instOrderVOList = new ArrayList<>();
                for (InstOrder order : refundList) {
                    instOrderVOList.add(convert2VO(order));
                }
                response.setRefundList(instOrderVOList);
            }
            response.setApplyStatus(ApplyStatusEnum.SUCCESS);
            logger.info("instOrderProcessFacade.queryRefundListByInstOrderNo.response:{}", request);
        } catch (Exception e) {
            logger.info("instOrderProcessFacade.queryRefundListByInstOrderNo.error,", e);
            response.setApplyStatus(ApplyStatusEnum.ERROR);
            response.setCode(e.getMessage());
        }
        return response;
    }

    @Override
    public CommonResponse updateGmtBooking(UpdateBookingTimeRequest request) {
        return updateBookingProcessor.process(request);
    }

    @Override
    public CommonResponse retry(FundOutRetryRequest request) {
        return fundOutRetryProcessor.process(request);
    }

    private QueryOrderResult convertQueryResult(ChannelFundResult channelFundResult, String instOrderNo) {
        QueryOrderResult result = new QueryOrderResult();
        result.setMessag("查询请求已提交");
        result.setStatus(com.uaepay.schema.cmf.enums.InstOrderStatus.I);
        return result;
    }

    private boolean needProcessCommunicateStatus(InstOrder instOrder) {
        return instOrder.getStatus() == InstOrderStatus.IN_PROCESS && (instOrder.getCommunicateStatus() == CommunicateStatus.IN_PROCESS || instOrder.getCommunicateStatus() == CommunicateStatus.RECEIVED);
    }

    private boolean needProcessLockFlag(InstOrder instOrder) {
        return instOrder != null && ((instOrder.getFlag() == OrderFlag.LOCKED || instOrder.getFlag() == OrderFlag.PAUSE) && DateUtil.dateLessThanNowAddMin(instOrder.getGmtModified(), 10));
    }


    private ChannelFundResult bulkQuery(InstOrder instOrder) {

        ChannelFundResult channelFundResult = new ChannelFundResult();
        InstBatchOrder instBatchOrder = instOrderRepository.loadById(instOrder.getArchiveBatchId());
        if (instBatchOrder == null) {
            return null;
        }

        try (ChannelCarrier carrier = channelApiRouter.route(RouteUtil.getParam(instBatchOrder.getFundChannelCode(), FundChannelApiType.BATCH_QUERY))) {
            logger.info("批次查询订单结果开始,批次ID[" + instBatchOrder.getArchiveBatchId() + "]");

            ChannelFundBatchResult channelFundBatchResult = null;
            // 查询所有该批次下处理中的订单
            List<InstOrder> sendInstOrders = instOrderRepository.getInstOrderListByAichiveBatchId(instBatchOrder.getArchiveBatchId());

            instBatchOrder.setInstOrderList(sendInstOrders);

            channelFundBatchResult = distributeQueryService.queryBatchItemResult(instBatchOrder);

            logger.info("批次订单结果处理开始,批次ID[" + instBatchOrder.getArchiveBatchId() + "]");
            InstBatchResult instBatchResult = batchResultProcessor.process(instBatchOrder, channelFundBatchResult, false);
            if (!CollectionUtils.isEmpty(channelFundBatchResult.getFundResultList())) {
                for (ChannelFundResult result : channelFundBatchResult.getFundResultList()) {
                    if (StringUtils.equals(instOrder.getInstOrderNo(), result.getInstOrderNo())) {
                        channelFundResult = result;
                    }
                }
            }

        } catch (Exception e) {
            logger.error("[批量查询结果失败]批次id:" + instBatchOrder.getArchiveBatchId(), e);
        }

        return channelFundResult;
    }


    /**
     * @param channelResult
     * @param resultStatus
     * @param instOrderNo
     * @return
     */
    private QueryOrderResult convertResult(ChannelResult channelResult, InstOrderResultStatus resultStatus, String instOrderNo) {
        com.uaepay.schema.cmf.enums.InstOrderStatus status;

        String code = channelResult.getApiResultCode();

        String message;
        switch (resultStatus) {
            case SUCCESSFUL:
                status = com.uaepay.schema.cmf.enums.InstOrderStatus.S;
                message = "订单已成功";
                break;
            case FAILURE:
                status = com.uaepay.schema.cmf.enums.InstOrderStatus.F;
                message = "订单已失败";
                break;
            case UNKNOWN:
                status = com.uaepay.schema.cmf.enums.InstOrderStatus.N;
                message = "订单状态未知";
                break;
            case IN_PROCESS:
                status = com.uaepay.schema.cmf.enums.InstOrderStatus.I;
                message = "订单处理中";
                break;
            default:
                message = "结果异常" + channelResult.getApiResultMessage();
                status = com.uaepay.schema.cmf.enums.InstOrderStatus.N;
        }
        message = message + ",银行返回结果码" + code;

        logger.info("订单状态查询成功,订单号:{},MSG:{}", instOrderNo, message);
        return new QueryOrderResult(status, message);
    }

    private QueryOrderResult convertQueryResult(ChannelResult channelResult, InstOrderResult instResult, String instOrderNo) {
        com.uaepay.schema.cmf.enums.InstOrderStatus status;

        String code = channelResult.getApiResultCode();

        String message;
        switch (instResult.getStatus()) {
            case SUCCESSFUL:
                status = com.uaepay.schema.cmf.enums.InstOrderStatus.S;
                message = "订单已成功";
                break;
            case FAILURE:
                status = com.uaepay.schema.cmf.enums.InstOrderStatus.F;
                message = "订单已失败";
                break;
            case UNKNOWN:
                status = com.uaepay.schema.cmf.enums.InstOrderStatus.N;
                message = "订单状态未知";
                break;
            case IN_PROCESS:
                status = com.uaepay.schema.cmf.enums.InstOrderStatus.I;
                message = "订单处理中";
                break;
            default:
                message = "结果异常" + channelResult.getApiResultMessage();
                status = com.uaepay.schema.cmf.enums.InstOrderStatus.N;
        }

        StringBuilder messageBuffer = new StringBuilder();
        messageBuffer.append(message).append(",统一映射结果码:").append(instResult.getInstResultCode());
        if (instResult.getExtension() != null) {
            messageBuffer.append(",统一返回信息:").append(instResult.getExtension().get("unityResultMessage"));
        }
        messageBuffer.append(",银行返回结果主码:").append(code);
        if (StringUtils.isNotBlank(instResult.getApiResultSubCode())) {
            messageBuffer.append(",子码:").append(instResult.getApiResultSubCode());
        }
        messageBuffer.append(",返回信息:");
        if (StringUtils.isNotBlank(channelResult.getResultMessage())) {
            messageBuffer.append(channelResult.getResultMessage());
        } else if (StringUtils.isNotBlank(channelResult.getApiResultMessage())) {
            messageBuffer.append(channelResult.getApiResultMessage());
        }
        if (StringUtils.isNotBlank(channelResult.getApiResultSubMessage())) {
            messageBuffer.append(channelResult.getApiResultSubMessage());
        }
        if (StringUtils.isNotBlank(instResult.getMemo())) {
            messageBuffer.append(",系统处理结果:");
            messageBuffer.append(instResult.getMemo());
        }

        message = messageBuffer.toString();

        logger.info("订单状态查询成功,订单号:{},MSG:{}", instOrderNo, message);
        return new QueryOrderResult(status, message);
    }

    private InstOrderVO convert2VO(InstOrder instOrder) {
        if (instOrder == null) {
            return null;
        }
        InstOrderVO vo = new InstOrderVO();

        BeanUtils.copyProperties(instOrder, vo);
        String geteOrderNo = instOrder.getExtension().get(ExtensionKey.GATE_ORDER_NO.key);
        vo.setGateOrderNo(geteOrderNo);
        vo.setInstStatus(instOrder.getStatus().getCode());
        vo.setArchiveBatchNo(instOrder.getArchiveBatchId());
        vo.setBizType(instOrder.getBizType().getCode());

        convertFundoutVo(vo, instOrder);
        CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(instOrder.getCmfSeqNo(), false);
        if (cmfOrder != null) {
            vo.setPaymentSeqNo(cmfOrder.getPaymentSeqNo());
        }

        InstOrderResult instOrderResult = instOrderResultRepository.getLastResult(instOrder.getInstOrderId());
        if (null == instOrderResult) {
            return vo;
        }
        vo.setMemo(instOrderResult.getMemo());

        if (instOrderResult.getApiResultCode() != null) {
            instOrder.getExtension().put(ExtensionKey.API_RESULT_CODE.key, instOrderResult.getApiResultCode());
        }
        if (instOrderResult.getApiResultSubCode() != null) {
            instOrder.getExtension().put(ExtensionKey.API_RESULT_SUB_CODE.key, instOrderResult.getApiResultSubCode());
        }

        if (instOrderResult.getExtension().containsKey(ExtensionKey.ECI.key)) {
            instOrder.getExtension().put(ExtensionKey.ECI.key, instOrderResult.getExtension().get(ExtensionKey.ECI.key));
        }

        vo.setExtension(JSON.toJSONString(instOrder.getExtension()));
        vo.setCanManualChange(false);
        return vo;
    }

    private void convertFundoutVo(InstOrderVO vo, InstOrder instOrder) {
        if (!(instOrder instanceof InstFundoutOrder)) {
            return;
        }
        InstFundoutOrder fo = (InstFundoutOrder) instOrder;
        vo.setInstName(fo.getBankName());
        vo.setFundInstCode(fo.getInstCode());
        vo.setFundInstName(fo.getBankName());
        vo.setFundInstBranchCode(fo.getBankBranchCode());
        vo.setFundInstBranchName(fo.getBankBranch());
        vo.setArchiveBatchNo(fo.getArchiveBatchId());
        vo.setInstStatus(fo.getStatus().getCode());
        vo.setMemberId(fo.getPtId());
        vo.setAccountName(uesClient.getDataByTicket(fo.getAccountName()));
        vo.setAccountNo(uesClient.getDataByTicket(fo.getAccountNo()));
        vo.setIbanNo(uesClient.getDataByTicket(fo.getIbanNo()));
        vo.setCardNo(uesClient.getDataByTicket(fo.getCardNo()));

    }

}