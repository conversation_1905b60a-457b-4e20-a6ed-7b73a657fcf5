package com.uaepay.cmf.ext.service.convertor;

import com.uaepay.basis.beacon.service.facade.enums.common.YesNoEnum;
import com.uaepay.channel.cards.service.facade.CardBinFacade;
import com.uaepay.channel.cards.service.facade.domain.CardBinVO;
import com.uaepay.channel.cards.service.facade.domain.request.CardBinValidateRequest;
import com.uaepay.channel.cards.service.facade.domain.response.ValidateResult;
import com.uaepay.cmf.common.core.domain.enums.SvaAccountEnum;
import com.uaepay.cmf.common.core.domain.enums.TokenTypeEnum;
import com.uaepay.cmf.common.core.domain.enums.UesDataTypeEnum;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.domainservice.main.domain.CardCategoryEnum;
import com.uaepay.cmf.domainservice.main.domain.MaCardTypeEnum;
import com.uaepay.cmf.fss.ext.integration.ues.UesClient;
import com.uaepay.cmf.fss.ext.integration.util.OrderUtil;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateRequest;
import com.uaepay.cmf.service.facade.domain.card.CardTokenQueryResult;
import com.uaepay.cmf.service.facade.domain.card.CardTokenUpdateRequest;
import com.uaepay.member.service.base.model.BeneficiaryInfo;
import com.uaepay.member.service.base.model.CardInfo;
import com.uaepay.member.service.enums.CardStatus;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenRequestConverter.java v1.0  2020-03-28 15:56
 */
public class CardTokenRequestConverter {

    private static final Map<Integer, String> CARD_TYPE_MAPPING = new HashMap<>(4);

    static {
        CARD_TYPE_MAPPING.put(1, "DC");
        CARD_TYPE_MAPPING.put(2, "CC");
    }

    private CardTokenRequestConverter() {

    }

    public static CardToken convert(CardTokenCreateRequest request, CardInfo cardInfo, BeneficiaryInfo beneficiaryInfo,
                                    UesClient uesClient, CardBinFacade cardBinFacade, SvaAccountEnum svaAccount, YesNoEnum storeInfoSwitch) {
        if (request == null) {
            return null;
        }
        CardToken cardToken = new CardToken();
        BeanUtils.copyProperties(request, cardToken);
        Map<String,String> extMap = StringUtils.isNotEmpty(cardToken.getExtension()) ? MapUtil.jsonToMap(cardToken.getExtension()): new HashMap<>(5);

        if (StringUtils.isNotEmpty(request.getIban()) || StringUtils.isNotEmpty(request.getCardAccountNo())) {
            cardToken.setTokenType(TokenTypeEnum.WITHDRAW);
        } else if (StringUtils.isNotEmpty(request.getSessionId())) {
            cardToken.setTokenType(TokenTypeEnum.SESSION);
        } else if (StringUtils.isNotEmpty(request.getCardNo())) {
            cardToken.setTokenType(TokenTypeEnum.QUICK);
        } else if (cardInfo != null && CollectionUtils.isNotEmpty(cardInfo.getTokenInfos())) {
            cardToken.setTokenType(TokenTypeEnum.TOKEN);
        }
        // 查询卡信息
        if (cardInfo != null) {
            if (StringUtils.isEmpty(cardToken.getInstCode())) {
                if (cardToken.getTokenType() == TokenTypeEnum.TOKEN && CollectionUtils.isNotEmpty(cardInfo.getTokenInfos())) {
                    cardToken.setInstCode(cardInfo.getTokenInfos().get(0).getInstitutionCode());
                } else {
                    cardToken.setInstCode(cardInfo.getBankCode());
                }
            }
            Assert.isTrue(StringUtils.isNotEmpty(cardToken.getInstCode()), "目标机构检查为空!");
            cardToken.setFirstBind(isNotVerified(cardInfo) ? YesNo.YES : YesNo.NO);
            if (cardInfo.getCardType() != null) {
                MaCardTypeEnum cardTypeEnum = MaCardTypeEnum.getByCode(cardInfo.getCardType().getInsCode());
                if (cardTypeEnum != null) {
                    cardToken.setCardType(cardTypeEnum.name());
                }
            }

            // 快捷支付
            // cardInfo中返回的cardCategory=COMMON，不再是数字
            CardCategoryEnum cardType = CardCategoryEnum.getByName(cardInfo.getCardCategory());
            if (cardType != null && cardType.isQuickPay()) {
                cardToken.setTokenType(TokenTypeEnum.QUICK);
                putCardInfos(cardInfo, uesClient, cardToken);
            } else if (cardType != null && cardType.isFundOut()) {
                cardToken.setTokenType(TokenTypeEnum.WITHDRAW);
                cardToken.setCardHolder(cardInfo.getBankAccountNameTicket());
                cardToken.setIban(cardInfo.getIbanTicket());
            }
            // 保存信息, 用于mock渠道
            if (storeInfoSwitch == YesNoEnum.YES && cardType == CardCategoryEnum.TOKEN) {
                putCardInfos(cardInfo, uesClient, cardToken);
            }
            // 保存cardBin信息
            if (StringUtils.isNotEmpty(cardInfo.getCardNoTicket())){
                String cardNo = uesClient.getDataByTicket(cardInfo.getCardNoTicket());
                extMap.put("first6", StringUtils.substring(cardNo,0,6));

                CardBinValidateRequest validateRequest = new CardBinValidateRequest();
                validateRequest.setCardNo(cardNo);
                ValidateResult<CardBinVO> validateResult = cardBinFacade.validate(validateRequest);
                if (validateResult != null && validateResult.getItem() != null){
                    extMap.put("cardBin", validateResult.getItem().getCardBin());
                    extMap.put("isPrepaid", validateResult.getItem().getCardLevel() != null && validateResult.getItem().getCardLevel().toUpperCase().contains("PREPAID") ? YesNo.YES.getCode() : YesNo.NO.getCode());
                }
            }

        }else if(beneficiaryInfo!=null){
            cardToken.setTokenType(TokenTypeEnum.WITHDRAW);
            cardToken.setCardHolder(beneficiaryInfo.getHolderNameTicket());
            cardToken.setIban(beneficiaryInfo.getAccountNo());
        } else {
            cardToken.setFirstBind(YesNo.YES);
            if (!OrderUtil.isEncryptedData(cardToken.getCardNo())) {
                String summary = getSummary(cardToken.getCardNo());
                cardToken.setCardNo(saveUesData(uesClient, cardToken.getCardNo(), summary, UesDataTypeEnum.CARD_NO));
            }
            if (!OrderUtil.isEncryptedData(cardToken.getCardHolder())) {
                cardToken.setCardHolder(saveUesData(uesClient, cardToken.getCardHolder(), UesDataTypeEnum.NAME));
            }
            if (StringUtils.isEmpty(cardToken.getCardExpired()) && StringUtils.isNotEmpty(request.getExpiredYear()) && StringUtils.isNotEmpty(request.getExpiredMonth())) {
                cardToken.setCardExpired(request.getExpiredYear() + "-" + request.getExpiredMonth());
            }
            if (StringUtils.isNotEmpty(cardToken.getCardExpired()) && !OrderUtil.isEncryptedData(cardToken.getCardExpired())) {
                cardToken.setCardExpired(saveUesData(uesClient, cardToken.getCardExpired(), UesDataTypeEnum.EXPIRED_DATE));
            }
            if (StringUtils.isNotEmpty(cardToken.getIban()) && !OrderUtil.isEncryptedData(cardToken.getIban())) {
                String suffix = cardToken.getIban().length() >= 4 ? cardToken.getIban().substring(cardToken.getIban().length() - 4) : null;
                cardToken.setIban(saveUesData(uesClient, cardToken.getIban(), suffix, UesDataTypeEnum.IBAN));
            }
        }
        Assert.notNull(cardToken.getTokenType(), "当前卡支付类型不支持");
        if (StringUtils.isEmpty(cardToken.getNeedCsc())) {
            cardToken.setNeedCsc(StringUtils.isEmpty(request.getCsc()) ? YesNo.NO.getCode() : YesNo.YES.getCode());
        }
        cardToken.setResultUrl(request.getResultUrl());

        extMap.put("svaStatus", svaAccount.getCode());
        extMap.put("clientId", request.getClientId());
        cardToken.setExtension(MapUtil.mapToJson(extMap));
        return cardToken;
    }

    private static boolean isNotVerified(CardInfo cardInfo) {
        return CardStatus.UNACTIVE == cardInfo.getStatus()||com.uaepay.member.service.enums.YesNo.YES!=cardInfo.getIsVerified();
    }

    private static String saveUesData(UesClient uesClient, String plainData, UesDataTypeEnum uesType) {
        return saveUesData(uesClient, plainData, null, uesType);
    }

    private static String saveUesData(UesClient uesClient, String plainData, String summary, UesDataTypeEnum uesType) {
        // 若已加密，则不进行二次加密
        if (OrderUtil.isEncryptedData(plainData) || StringUtils.isEmpty(plainData)) {
            return plainData;
        }
        if (StringUtils.isEmpty(summary)) {
            return uesClient.saveData(plainData, uesType);
        }
        return uesClient.saveData(plainData, summary, uesType);
    }

    private static String getSummary(String cardNo) {
        if (StringUtils.isEmpty(cardNo) || cardNo.length() < 4) {
            return null;
        }
        return cardNo.substring(cardNo.length() - 4);
    }

    private static void putCardInfos(CardInfo cardInfo, UesClient uesClient, CardToken cardToken) {
        cardToken.setCardNo(cardInfo.getCardNoTicket());
        if (StringUtils.isNotEmpty(cardInfo.getCardValidDate())) {
            // todo: expiredDate不应该再由cmf解析，再渠道修改完毕后需要去除这里的逻辑
//            String expiredDate = uesClient.getDataByTicket(cardInfo.getCardValidDate());
//            Assert.isTrue(expiredDate.contains("-"), "cardValidDate格式有误");
            cardToken.setCardExpired(cardInfo.getCardValidDate());
        }
        if (cardInfo.getCardType() != null) {
            cardToken.setCardType(CARD_TYPE_MAPPING.get(cardInfo.getCardType().getInsCode()));
        }
        if (StringUtils.isNotEmpty(cardInfo.getBankCode()) && cardInfo.getBankCode().length() <= 4) {
            cardToken.setIssueBank(cardInfo.getBankCode());
        }
        cardToken.setCardHolder(cardInfo.getBankAccountNameTicket());
        if (cardInfo.getCardOrg() != null) {
            cardToken.setCardBrand(cardInfo.getCardOrg());
        }
    }

    public static CardToken convert(CardTokenUpdateRequest request) {
        if (request == null) {
            return null;
        }
        CardToken cardToken = new CardToken();
        BeanUtils.copyProperties(request, cardToken);
        return cardToken;
    }


    public static CardTokenQueryResult convert(CardToken cardToken) {
        CardTokenQueryResult result = CardTokenQueryResult.buildSuccess();
        BeanUtils.copyProperties(cardToken, result);
        return result;
    }

    public static CardToken convert(CardTokenCreateRequest request) {
        CardToken cardToken = new CardToken();
        BeanUtils.copyProperties(request, cardToken);
        cardToken.setTokenType(TokenTypeEnum.getByCode(request.getCardTokenType()));
        if (StringUtils.isEmpty(cardToken.getNeedCsc())) {
            cardToken.setNeedCsc(StringUtils.isEmpty(request.getCsc()) ? YesNo.NO.getCode() : YesNo.YES.getCode());
        }
        return cardToken;
    }
}
