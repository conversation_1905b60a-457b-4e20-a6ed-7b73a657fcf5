package com.uaepay.cmf.ext.service.mq.handler;

import com.alibaba.fastjson.JSON;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelNotifyResult;
import com.uaepay.cmf.service.facade.api.ChannelReceiveFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ResultNotifyHandler.java v1.0
 */
@Slf4j
@Service
public class ResultNotifyHandler {

    @Autowired
    private AmqpAdmin amqpAdmin;

    @Resource
    private ChannelReceiveFacade channelReceiveFacade;

    private static final String QUEUE_CMF_TIMEOUT_RESULT = "queue.cmf.timeoutResult";

    @RabbitListener(queues = QUEUE_CMF_TIMEOUT_RESULT)
    public void receive(Message message) {
        ChannelFundResult fundResult = JSON.parseObject(message.getBody(), ChannelFundResult.class);
        ChannelNotifyResult notifyResult = channelReceiveFacade.fundNotify(fundResult);
        log.info("ResultNotifyHandler.notifyResult:{}", notifyResult);
    }

    @PostConstruct
    public void init() {
        Queue queue = QueueBuilder.durable(QUEUE_CMF_TIMEOUT_RESULT).build();
        log.info("已创建queue: {}", amqpAdmin.declareQueue(queue));
    }

}
