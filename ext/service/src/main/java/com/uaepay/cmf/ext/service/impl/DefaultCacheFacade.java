package com.uaepay.cmf.ext.service.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.domainservice.main.general.impl.ChannelKeyImportProcessor;
import com.uaepay.cmf.domainservice.main.general.impl.ChannelKeyQueryProcessor;
import com.uaepay.cmf.domainservice.main.general.impl.QueryAccountBalanceCacheProcessor;
import com.uaepay.cmf.service.facade.api.CacheFacade;
import com.uaepay.cmf.service.facade.domain.cache.*;
import com.uaepay.cmf.service.facade.domain.query.PkQueryRequest;
import com.uaepay.cmf.service.facade.result.PkQueryResult;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;

@Service
public class DefaultCacheFacade implements CacheFacade {

    @Resource(name = "memoryCacheClient")
    private CacheClient cacheClient;

    @Resource
    private ChannelKeyImportProcessor channelKeyImportProcessor;

    @Resource
    private ChannelKeyQueryProcessor channelKeyQueryProcessor;

    @Resource
    private QueryAccountBalanceCacheProcessor queryAccountBalanceCacheProcessor;

    @Override
    public String getCsc(String cardTokenId) {
        return (String) cacheClient.get(CacheType.CSC, cardTokenId);
    }

    @Override
    public PkQueryResult<String> query3ds2Form(PkQueryRequest<String> request) {
        PkQueryResult<String> result = new PkQueryResult<>();
        String form = (String) cacheClient.get(CacheType.FORM, request.getPk());
        result.setApplyStatus(ApplyStatusEnum.SUCCESS);
        result.setItem(form);
        return result;
    }

    @Override
    public CommonResponse importChannelKey(ImportChannelKeyRequest request) {
        CommonResponse process = channelKeyImportProcessor.process(request);
        return process;
    }

    @Override
    public QueryChannelKeyResponse queryChannelKey(QueryChannelKeyRequest request) {
        QueryChannelKeyResponse process = channelKeyQueryProcessor.process(request);
        return process;
    }

    @Override
    public QueryAccountBalanceCacheResponse queryBalanceCache(QueryAccountBalanceCacheRequest request) {
        return queryAccountBalanceCacheProcessor.process(request);
    }
}
