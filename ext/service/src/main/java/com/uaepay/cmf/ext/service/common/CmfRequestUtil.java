package com.uaepay.cmf.ext.service.common;

import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;

import java.util.Objects;
import java.util.Optional;

import static com.uaepay.cmf.common.core.domain.enums.ExtensionKey.SOURCE_CODE;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/8/19
 */
public class CmfRequestUtil {

    public static boolean isPosFiserv(CmfControlRequest request){
        return Optional.of(request)
                .map(CmfControlRequest::getExtension)
                .map(ext-> Objects.equals(ext.getValue(SOURCE_CODE.getKey()),"POS_FISERV"))
                .orElse(false);
    }

    public static boolean isPosFiservControlRequest(CmfControlRequest request){
            return Optional.of(request)
                    .map(CmfControlRequest::getExtension)
                    .map(ext-> Objects.equals(ext.getValue(SOURCE_CODE.getKey()),"POS_FISERV"))
                    .orElse(false);
    }
}
