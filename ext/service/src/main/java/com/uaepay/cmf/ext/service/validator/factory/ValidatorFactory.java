package com.uaepay.cmf.ext.service.validator.factory;

import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.validate.Validator;
import com.uaepay.validate.exception.ValidationException;

/**
 * <p>校验器工厂</p>
 * <AUTHOR>
 * @version $Id: ValidatorFactory.java, v 0.1 2012-8-18 上午10:22:29 fuyangbiao Exp $
 */
public interface ValidatorFactory {
    /**
     * 根据控制请求类型获取校验器
     * @param requestType
     * @return
     * @throws ValidationException
     */
    Validator load(ControlRequestType requestType) throws ValidationException;

    /**
     * 根据资金业务类型获取校验器
     * @param bizType
     * @return
     * @throws ValidationException
     */
    Validator load(BizType bizType) throws ValidationException;
}
