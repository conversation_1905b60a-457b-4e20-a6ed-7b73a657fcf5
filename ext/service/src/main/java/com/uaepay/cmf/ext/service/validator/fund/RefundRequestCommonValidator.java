package com.uaepay.cmf.ext.service.validator.fund;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.exception.RefundVerifyException;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import com.uaepay.validate.exception.ValidationException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p>
 * .
 * </p>
 *
 * <AUTHOR>
 * @version RefundRequestCommonValidator.java 1.0 Created@2017-12-13 10:57 $
 */
@Service("refundRequestCommonValidator")
public class RefundRequestCommonValidator extends FundRequestCommonValidator {
    private Logger logger = LoggerFactory.getLogger(RefundRequestCommonValidator.class);

    @Resource
    private OrderLoaderService orderLoaderService;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Override
    public void validate(Object model) throws ValidationException {
        super.validate(model);
        CmfRequest request = (CmfRequest) model;
        try {
            String orgiPaymentSeqNo = request.getExtension().getValue(ExtensionKey.ORGI_FUNDIN_ORDER_NO.key);
            String orgiSettlementId = request.getExtension().getValue(ExtensionKey.ORGI_SETTLEMENT_ID.key);


            InstBaseOrder preOrder = orderLoaderService.loadPreOrder(RequestType.REFUND.name(), orgiPaymentSeqNo, orgiSettlementId, null);
            Assert.notNull(preOrder, "未找到原入款订单");
            Assert.isTrue(preOrder.getAmount().getCurrency().equals(request.getAmount().getCurrency()), "退款订单币种与原订单不同");
            Assert.isTrue(canRefund(preOrder, request), "原订单不可退款");

            if (StringUtils.isEmpty(request.getExtension().getValue(ExtensionKey.BIZ_PRODUCT_CODE.getKey()))) {
                request.getExtension().add(ExtensionKey.BIZ_PRODUCT_CODE.getKey(), preOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.getKey()));
            }

        } catch (IllegalArgumentException e) {
            throw new RefundVerifyException(e.getMessage());
        }
    }

    private boolean canRefund(InstBaseOrder preOrder, CmfRequest cmfRequest) {

        Money refundAmount = cmfRequest.getAmount();

        // 愿订单不存在以及原订单状态不为成功的情况下不可退款
        if (preOrder == null ) {
            return false;
        }

        // 原交易不为入款订单
        if (preOrder instanceof InstOrder && ((InstOrder) preOrder).getBizType() != BizType.FUNDIN) {
            return false;
        }

        //校验状态
        if (preOrder.getStatus() != InstOrderStatus.SUCCESSFUL && preOrder.getStatus()!=InstOrderStatus.HALF_SUCCESSFUL){
            return false;
        }

        // 对退款操作做锁定，对于锁定失败情况不做下一步处理
        String preInstOrderNo = preOrder.getInstOrderNo();

        Money hasRefundAmount = instOrderRepository.getHasRefundAmount(preInstOrderNo, preOrder.getAmount().getCurrency());
        logger.info("已退款金额:{}", hasRefundAmount);
        // TODO：订单锁定
        return !refundAmount.greaterThan(preOrder.getAmount().subtract(hasRefundAmount));
    }
}
