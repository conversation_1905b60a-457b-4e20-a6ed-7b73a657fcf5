package com.uaepay.cmf.ext.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.domain.ChannelCommonResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelNotifyResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.result.ControlResultProcessor;
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor;
import com.uaepay.cmf.domainservice.main.spi.BankFormService;
import com.uaepay.cmf.service.facade.api.ChannelReceiveFacade;
import com.uaepay.schema.cmf.enums.InstOrderStatus;
import org.apache.dubbo.config.annotation.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;

@Service
public class ChannelReceiveFacadeImpl implements ChannelReceiveFacade, BasicConstant {
    private Logger logger = LoggerFactory.getLogger(ChannelReceiveFacadeImpl.class);

    @Resource
    private InstResultProcessor instResultProcessor;
    @Resource
    private InstOrderRepository instOrderRepository;
    @Resource
    private ControlResultProcessor controlResultProcessor;
    @Resource
    private InstControlOrderRepository instControlOrderRepository;
    @Resource
    private ChannelApiRouter apiRouter;
    @Resource
    private BankFormService bankFormService;

    @Override
    public ChannelNotifyResult fundNotify(ChannelFundResult request) {
        logger.info("channelReceiveFacade.fundNotify.request:{}", request);
        Assert.notNull(request, "通知不可为空");
        try(ChannelCarrier carrier = apiRouter.route(RouteUtil.getParam(request.getFundChannelCode(), request.getApiType()))) {
            boolean isControl = FundChannelApiType.isControl(request.getApiType());
            if(isControl){
                return processControlNotify(request);
            }
            InstOrder instOrder = instOrderRepository.loadByNo(request.getInstOrderNo());
            if (instOrder == null) {
                logger.info("机构订单不存在," + request.toString());
                return buildReturnInfo(ErrorCode.FAIL.getErrorCode(), "不存在订单");
            }
            Assert.isTrue(instOrder.getCommunicateStatus()!= CommunicateStatus.IN_PROCESS, "发送状态为处理中");
            Assert.isTrue(!instOrder.getStatus().isFinalStatus(), "订单已有最终结果");
            bankFormService.processInstResult(instOrder, request);
            instOrderRepository.updateCommunicateStatusWithPreStatus(instOrder,
                    CommunicateStatus.IN_PROCESS, instOrder.getCommunicateStatus());
            InstOrderResult orderResult = instResultProcessor.process(instOrder, request);
            logger.info("channelReceiveFacade.fundNotify.result:{}", orderResult);
            return buildNotifyResult(orderResult);
        } catch (IllegalArgumentException iae){
            logger.warn("ChannelReceiveFacade.fundNotify.iae-request:{},-error:{}", request, iae.getMessage());
            return buildReturnInfo(FAILURE.toString(), "CMF处理失败:" + iae.getMessage());
        } catch (Exception e) {
            logger.error("通知CMF失败:" + request, e);
            return buildReturnInfo(FAILURE.toString(), "CMF处理失败:" + e.getMessage());
        }
    }

    private ChannelNotifyResult processControlNotify(ChannelFundResult request) {
        InstControlOrder instControlOrder = instControlOrderRepository.loadByNo(request.getInstOrderNo());
        if (instControlOrder == null) {
            logger.info("控制订单不存在," + request.toString());
            return buildReturnInfo(ErrorCode.FAIL.getErrorCode(), "不存在订单");
        }
        Assert.isTrue(instControlOrder.getCommunicateStatus()!= CommunicateStatus.IN_PROCESS, "发送状态为处理中");
        Assert.isTrue(!instControlOrder.getStatus().isFinalStatus(), "订单已有最终结果");
        instControlOrderRepository.updateCommunicateStatusByIdAndPreStatus(instControlOrder,
                CommunicateStatus.IN_PROCESS, instControlOrder.getCommunicateStatus());
        InstControlOrderResult orderResult = controlResultProcessor.process(instControlOrder, request);
        logger.info("channelReceiveFacade.fundNotify.result:{}", orderResult);
        return buildReturnInfo(orderResult.getStatus().getCode(), orderResult.getResultMessage());
    }

    @Override
    public ChannelNotifyResult notify(ChannelCommonResult request) {
        logger.info("channelReceiveFacade.notify.request:{}", request);
        try {
            ChannelResult channelResult;
            try {
                channelResult = (ChannelResult) JSONObject.parseObject(request.getResultJason(),
                        Class.forName(request.getResultClass()));
            } catch (ClassNotFoundException e) {
                logger.error("结果反射异常," + request.toString(), e);
                return buildReturnInfo(FAILURE.toString(), "不存在订单");
            }
            InstControlOrder instControlOrder = instControlOrderRepository.loadByNo(channelResult.getInstOrderNo());
            if (instControlOrder == null) {
                logger.info("机构订单不存在,result:{}", request);
                return buildReturnInfo(FAILURE.toString(), "不存在订单");
            }
            InstControlOrderResult resp = controlResultProcessor.process(instControlOrder, channelResult);
            logger.info("channelReceiveFacade.notify.result:{}", resp);
            return buildReturnInfo(resp.getStatus().getCode(), resp.getResultMessage());
        }catch (IllegalArgumentException iae){
            logger.warn("ChannelReceiveFacade.notify.iae-request:{},-error:{}", request, iae.getMessage());
            return buildReturnInfo(FAILURE.toString(), "CMF处理失败:" + iae.getMessage());
        } catch (Exception e) {
            logger.error("通知CMF失败:" + request, e);
            return buildReturnInfo(FAILURE.toString(), "CMF处理失败:" + e.getMessage());
        }
    }

    private ChannelNotifyResult buildReturnInfo(String returnCode, String resultMsg) {
        ChannelNotifyResult returnInfo = new ChannelNotifyResult();
        returnInfo.setReturnCode(returnCode);
        returnInfo.setResultMessage(resultMsg);
        return returnInfo;
    }

    private ChannelNotifyResult buildNotifyResult(InstOrderResult orderResult) {
        ChannelNotifyResult result = new ChannelNotifyResult();
        if (orderResult.getStatus() != null) {
            InstOrderStatus status = InstOrderStatus.I;
            switch (orderResult.getStatus()) {
                case SUCCESSFUL:
                    status = InstOrderStatus.S;
                    break;
                case FAILURE:
                    status = InstOrderStatus.F;
                    break;
                case NONEXISTS:
                    status = InstOrderStatus.N;
                    break;
                default:
                    break;
            }
            result.setInstOrderStatus(status);
        }
        result.setProcessStatus(orderResult.getProcessStatus().getCode());
        result.setReturnCode(orderResult.getInstResultCode());
        if (orderResult.getExtension() != null) {
            result.setExtension(MapUtil.mapToJson(orderResult.getExtension()));
        }
        return result;
    }
}
