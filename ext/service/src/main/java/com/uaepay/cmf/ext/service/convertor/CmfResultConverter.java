package com.uaepay.cmf.ext.service.convertor;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.service.facade.domain.CmfCommonResultCode;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>CMF结果转换器</p>
 *
 * <AUTHOR>
 * @version $Id: CmfResultConverter.java, v 0.1 2012-8-17 下午5:27:04 fuyangbiao Exp $
 */
public class CmfResultConverter {

    /**
     * 根据控制订单转换为结果
     *
     * @param order
     * @return
     */
    public static CmfControlResult convert(InstControlOrder order,
                                           InstControlOrderResult orderResult) {
        CmfControlResult result = new CmfControlResult();
        result.setFundsChannel(order.getFundChannelCode());
        result.setInstOrderNo(order.getInstOrderNo());
        result.setInstResultCode(orderResult.getInstResultCode());
        result.setReturnMessage(orderResult.getResultMessage());
        result.setExtension(CommonConverter.convertExtension(orderResult.getExtension()));
        CmfCommonResultCode cmfResultCode = convertOrderStatus(order.getStatus());
        result.setResultCode(cmfResultCode);

        // FS104 特殊处理
        // FS104 需要把处理中的也成功返回给前端
        if (StringUtils.equals(BasicConstant.FISERV_CHANNEL_CODE, result.getFundsChannel())
                && StringUtils.equals(BasicConstant.SUCCESS_MSG, result.getInstResultCode())) {
            result.setResultCode(CmfCommonResultCode.SUCCESS);
        }

        return result;
    }

    public static CmfCommonResultCode convertOrderStatus(InstOrderStatus status) {
        CmfCommonResultCode resultCode = null;
        if (status != null) {
            switch (status) {
                case SUCCESSFUL:
                    resultCode = CmfCommonResultCode.SUCCESS;
                    break;
                case FAILURE:
                    resultCode = CmfCommonResultCode.FAILED;
                    break;
                default:
                    resultCode = CmfCommonResultCode.IN_PROCESS;
                    break;
            }
        }
        return resultCode;
    }

    /**
     * @param resultStatus
     * @return
     */
    public static InstOrderStatus convertOrderResultStatus(InstOrderResultStatus resultStatus) {
        InstOrderStatus orderStatus = null;
        if (resultStatus != null) {
            switch (resultStatus) {
                case SUCCESSFUL:
                    orderStatus = InstOrderStatus.SUCCESSFUL;
                    break;
                case FAILURE:
                    orderStatus = InstOrderStatus.FAILURE;
                    break;
                default:
                    orderStatus = InstOrderStatus.IN_PROCESS;
                    break;
            }
        }
        return orderStatus;
    }

    /**
     * 根据结果码和信息组装
     *
     * @param resultCode
     * @param resultMessage
     * @return
     */
    public static CmfControlResult convert(CmfCommonResultCode resultCode, String resultMessage) {
        CmfControlResult result = new CmfControlResult();
        if (resultCode == null) {
            resultCode = CmfCommonResultCode.UNKNOW_EXCEPTION;
        }
        result.setResultCode(resultCode);
        result.setReturnMessage(resultMessage);

        return result;
    }

}
