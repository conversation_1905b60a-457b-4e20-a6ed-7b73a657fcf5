package com.uaepay.cmf.ext.service.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.PageResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.biz.common.util.PageList;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.domainservice.main.convert.InstOrderConverter;
import com.uaepay.cmf.domainservice.main.general.impl.BatchOrderQueryProcessor;
import com.uaepay.cmf.domainservice.main.general.impl.ControlOrderQueryProcessor;
import com.uaepay.cmf.domainservice.main.general.impl.InstOrderQueryProcessor;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.ext.service.common.ValidatorUtil;
import com.uaepay.cmf.service.facade.api.OrderQueryFacade;
import com.uaepay.cmf.service.facade.domain.query.*;
import com.uaepay.cmf.service.facade.domain.query.order.BatchOrderVO;
import com.uaepay.cmf.service.facade.domain.query.order.ControlOrderVO;
import com.uaepay.cmf.service.facade.result.PkQueryResult;
import com.uaepay.common.lang.Paginator;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 出款,入款冲退门面请求实现
 *
 * <AUTHOR> won
 * @version $Id: DefaultFundRequestFacade.java, v 0.1 2011-3-21 下午04:44:33 sean won Exp $
 */
@Service
public class DefaultOrderQueryFacade implements OrderQueryFacade {
    private static final Logger logger = LoggerFactory.getLogger(DefaultOrderQueryFacade.class);

    @Resource
    private InstOrderRepository instOrderRepository;
    @Resource
    private CmfOrderRepository cmfOrderRepository;
    @Resource
    private BatchOrderQueryProcessor batchOrderQueryProcessor;
    @Resource
    private InstOrderQueryProcessor instOrderQueryProcessor;
    @Resource
    private ControlOrderQueryProcessor controlOrderQueryProcessor;


    @Override
    public PageResponse<SimpleOrder> queryOrdersByPage(OrderPageQueryRequest request) {
        PageResponse<SimpleOrder> response = null;
        try {
            // 校验
            ValidatorUtil.validate(request);
            // 参数查询
            Pair<List<InstOrder>, Paginator> info = instOrderRepository.queryChannelOrders(request);
            List<SimpleOrder> dataList = info.getLeft().stream().map(InstOrderConverter::transfer).collect(Collectors.toList());
            response = buildResponse(ApplyStatusEnum.SUCCESS, BasicConstant.SUCCESS_MSG, dataList, info.getRight().getItems());
        } catch (Exception e) {
            logger.error("queryOrdersByPage.error:", e);
            response = buildResponse(ApplyStatusEnum.FAIL, e.getMessage(), null, null);
        }
        return response;
    }

    @Override
    public PkQueryResult<SimpleOrder> queryByOrderNo(OrderNoQueryRequest request) {
        PkQueryResult<SimpleOrder> result = null;
        try {
            // 校验
            ValidatorUtil.validate(request);
            CmfOrder cmfOrder = cmfOrderRepository.loadByPaymentOrderNo(request);
            if (cmfOrder == null) {
                return buildPkResponse(ApplyStatusEnum.FAIL, "Transaction not found", null);
            }
            InstOrder instOrder = instOrderRepository.loadByCmfSeqNoSingle(cmfOrder.getOrderSeqNo());

            result = buildPkResponse(ApplyStatusEnum.SUCCESS, "Query success", InstOrderConverter.transfer(instOrder));
        } catch (Exception e) {
            logger.error("queryByOrderNo.error:", e);
            result = buildPkResponse(ApplyStatusEnum.FAIL, e.getMessage(), null);
        }
        return result;
    }

    @Override
    public PkQueryResult<ControlOrderVO> queryControlOrder(PkQueryRequest<String> request) {
        return controlOrderQueryProcessor.process(request);
    }

    @Override
    public PageResponse<SimpleOrder> queryByInstOrderNoList(InstOrderQueryRequest request) {
        return instOrderQueryProcessor.process(request);
    }

    @Override
    public PageResponse<BatchOrderVO> queryBatchOrder(BatchOrderQueryRequest request) {
        return batchOrderQueryProcessor.process(request);
    }

    private PkQueryResult<SimpleOrder> buildPkResponse(ApplyStatusEnum applyStatus, String message, SimpleOrder item) {
        PkQueryResult<SimpleOrder> result = new PkQueryResult<>();
        result.setApplyStatus(applyStatus);
        result.setMessage(message);
        result.setItem(item);
        return result;
    }

    private PageResponse<SimpleOrder> buildResponse(ApplyStatusEnum applyStatus, String message, List<SimpleOrder> dataList, Integer totalSize) {
        PageResponse<SimpleOrder> response = new PageResponse<>();
        response.setApplyStatus(applyStatus);
        response.setMessage(message);
        response.setDataList(dataList);
        response.setTotalSize(totalSize);
        return response;
    }

}
