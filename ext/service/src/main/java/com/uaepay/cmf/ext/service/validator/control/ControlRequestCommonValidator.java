package com.uaepay.cmf.ext.service.validator.control;

import javax.annotation.Resource;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import org.apache.commons.lang3.StringUtils;
import com.uaepay.validate.Validator;
import com.uaepay.validate.exception.ValidationException;

import java.util.Objects;

import static com.uaepay.cmf.common.core.domain.constants.BasicConstant.TRANSACTION_TYPE_RESERVATION;

/**
 * <p>控制请求通用校验器</p>
 *
 * <AUTHOR>
 * @version $Id: ControlRequestValidator.java, v 0.1 2012-8-18 上午10:29:14 fuyangbiao Exp $
 */
@Service("controlRequestCommonValidator")
public class ControlRequestCommonValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(ControlRequestCommonValidator.class);

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private InstControlOrderRepository instControlOrderRepository;

    @Resource
    private OrderLoaderService orderLoaderService;

    @Override
    public void validate(Object model) throws ValidationException {
        CmfControlRequest request = (CmfControlRequest) model;
        try {
            Assert.isTrue(StringUtils.isNotBlank(request.getRequestNo()), "请求号不能为空");
            String preRequestNo = request.getPreRequestNo();

            InstControlOrder instControlOrder = instControlOrderRepository.loadByRequestNo(request
                    .getRequestNo());
            if (instControlOrder != null) {
                throw new ValidationException("请求号重复");
            }

            String orgiPaymentSeqNo = request.getExtension().getValue(ExtensionKey.ORGI_FUNDIN_ORDER_NO.key);
            if(StringUtils.isNotEmpty(orgiPaymentSeqNo)){
                InstBaseOrder preOrder = orderLoaderService.loadPreOrder(request.getRequestType().name(), orgiPaymentSeqNo, null, null);

                Assert.notNull(preOrder, "orgi OrderNo is non exists");
                preRequestNo = preOrder.getInstOrderNo();
                request.setPreRequestNo(preRequestNo);
            }

            if (request.getRequestType() == ControlRequestType.ADVANCE) {

                InstOrder instOrder = instOrderRepository.loadByNo(preRequestNo);

                Assert.notNull(instOrder, "原请求号不存在");
                logger.info("原订单号:{}, 原订单状态:{}", instOrder.getInstOrderNo(), instOrder.getStatus());
                Assert.isTrue(InstOrderStatus.FAILURE != instOrder.getStatus(), "原订单已失败");
                Assert.isTrue(InstOrderStatus.SUCCESSFUL != instOrder.getStatus(), "原订单已经支付成功，请不要重复支付");
            }
        } catch (IllegalArgumentException e) {
            throw new ValidationException(e.getMessage());
        }
    }

}
