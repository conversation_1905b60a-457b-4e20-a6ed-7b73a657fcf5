package com.uaepay.cmf.ext.service.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.basis.beacon.service.facade.enums.common.YesNoEnum;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.base.ReturnInfo;
import com.uaepay.cmf.domainservice.main.convert.InstOrderResultConverter;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor;
import com.uaepay.cmf.ext.service.common.trans.InstOrderResultTransformer;
import com.uaepay.cmf.service.facade.api.ResultNotifyFacade;
import com.uaepay.cmf.service.facade.domain.fundout.FundOutCancelRequest;
import com.uaepay.cmf.service.facade.domain.fundschannel.OrderResult;
import com.uaepay.cmf.service.facade.result.FundOutCancelResult;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.dubbo.config.annotation.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Date;

@Service
public class DefaultResultNotifyFacade implements ResultNotifyFacade, BasicConstant {

    private Logger logger = LoggerFactory.getLogger(DefaultResultNotifyFacade.class);

    @Resource
    private InstOrderRepository instOrderRepository;
    @Resource
    private InstResultProcessor instResultProcessor;

    @Override
    public ReturnInfo notify(OrderResult result) {

        ReturnInfo returnInfo = new ReturnInfo();
        logger.info("机构通知结果:{}", result);
        InstOrderResult instResult = null;
        InstOrder instOrder = null;
        try {
            Assert.notNull(result, "通知结果为空");
            Assert.notNull(result.getOrderResult(), "通知结果为空");
            instResult = InstOrderResultTransformer.convert(result.getOrderResult());
            Assert.notNull(instResult, "通知结果为空");
            instOrder = instOrderRepository.loadByNo(instResult.getInstOrderNo());
            Assert.notNull(instOrder, "未找到订单");

            if (instOrder.getInstOrderId() == null) {
                logger.warn("[渠道回调通知]cmf处理失败[" + instResult.getInstOrderNo() + "],机构订单不存在");
                returnInfo.setReturnCode("-1");
                returnInfo.setReturnMsg("cmf处理失败[" + instResult.getInstOrderNo() + "],机构订单不存在");
                return returnInfo;
            }
            if (!validateAmount(instResult, instOrder)) {
                logger.warn("[渠道回调通知]cmf处理失败[" + instOrder.getInstOrderNo() + "],金额校验不通过,返回金额["
                        + instResult.getRealAmount() + "],原订单金额[" + instOrder.getAmount() + "]");
                returnInfo.setReturnCode("-1");
                returnInfo.setReturnMsg("cmf处理失败[" + instOrder.getInstOrderNo() + "],金额校验不通过");
                return returnInfo;
            }
            Assert.isTrue(instOrder.getIsAdvance() != IsAdvance.YES, "未推进订单不可置结果");
            InstOrderResultConverter.convert(instOrder, instResult);
        } catch (IllegalArgumentException iae) {
            returnInfo.setReturnCode("-1");
            returnInfo.setReturnMsg("校验失败," + iae.getMessage());
            return returnInfo;
        }
        try {
            instResult.getExtension().put(NEED_NOTIFY, YesNoEnum.YES.getCode());

            CommonResponse commonResponse = instResultProcessor.process(instOrder, instResult);
            if (commonResponse.getApplyStatus() == ApplyStatusEnum.SUCCESS) {
                returnInfo.setReturnCode("0");
                returnInfo.setReturnMsg(commonResponse.getMessage());
                return returnInfo;
            } else {
                returnInfo.setReturnCode("-1");
                returnInfo.setReturnMsg(commonResponse.getMessage());
                return returnInfo;
            }
        } catch (IllegalArgumentException iae) {
            returnInfo.setReturnCode("-1");
            returnInfo.setReturnMsg("校验失败," + iae.getMessage());
            return returnInfo;
        } catch (Exception e) {
            logger.error("通知CMF失败", e);
            returnInfo.setReturnCode("-1");
            returnInfo.setReturnMsg("cmf处理失败," + e.getMessage());
            return returnInfo;
        }
    }

    @Override
    public FundOutCancelResult fundOutCancelNotice(FundOutCancelRequest request) {
        FundOutCancelResult result = new FundOutCancelResult();

        try {
            // validate
            logger.info("机构通知结果:{}", request);
            Assert.notNull(request, "请求内容为空");
            Assert.hasText(request.getInstOrderNo(),"机构订单号为空");
            Assert.notNull(request.getInstOrderStatus(), "请求订单状态为空");

            InstOrder instOrder = instOrderRepository.loadByNo(request.getInstOrderNo());
            Assert.notNull(instOrder, "机构订单不存在");
            Assert.notNull(instOrder.getInstOrderId(), "cmf处理失败[" + request.getInstOrderNo() + "],机构订单不存在");

            Assert.isTrue(BizType.FUNDOUT.equals(instOrder.getBizType()), "非出款订单不可置结果");
            Assert.isTrue(InstOrderStatus.IN_PROCESS.equals(instOrder.getStatus()),"非流程中订单不可置状态");
            Assert.isTrue(CommunicateStatus.AWAITING.equals(instOrder.getCommunicateStatus()), "非等待指令发送订单不可置结果");
            Assert.notNull(instOrder.getGmtBookingSubmit(), "cmf出场时间为空");
            Assert.isTrue(instOrder.getGmtBookingSubmit().toInstant().minusSeconds(60).isAfter(Instant.now()),
                    "出场订单不可置结果");

            InstOrderResult instResult = convert2InstOrderResult(request, instOrder);
            instResult.getExtension().put(NEED_NOTIFY, YesNoEnum.YES.getCode());

            // 更改通迅状态，避免任务被调度
            int updateCount = instOrderRepository.updateCommunicateStatusWithPreStatus(instOrder, CommunicateStatus.IN_PROCESS, CommunicateStatus.AWAITING);
            Assert.isTrue(updateCount == 1, "当前订单通讯状态不可置结果");
            CommonResponse commonResponse = instResultProcessor.process(instOrder, instResult);
            if (commonResponse.getApplyStatus() == ApplyStatusEnum.SUCCESS) {
                result.setReturnCode("0");
                result.setReturnMsg(commonResponse.getMessage());
            } else {
                result.setReturnCode("-1");
                result.setReturnMsg(commonResponse.getMessage());
            }
        } catch (IllegalArgumentException iae) {
            logger.info("订单校验失败，不可置结果，原因：{}", iae.getMessage());
            result.setReturnCode("-1");
            result.setReturnMsg(iae.getMessage());
        } catch (Throwable e){
            logger.error("通知CMF失败",e);
            result.setReturnCode("-1");
            result.setReturnMsg("cmf处理失败," + e.getMessage());
        }

        return result;

    }

    /**
     * 校验金额判断是否通过
     *
     * @param instResult
     * @param instOrder
     * @return
     */
    private boolean validateAmount(InstOrderResult instResult, InstOrder instOrder) {
        String sourceCode = instResult.getExtension().get(ExtensionKey.SOURCE_CODE.key);
        boolean isNeedValidate = ("COUNTER".equalsIgnoreCase(sourceCode));
        return !isNeedValidate || (instResult.getRealAmount() != null && instResult.getRealAmount().getAmount() != null
                && instResult.getRealAmount().compareTo(instOrder.getAmount()) == 0);
    }

    private InstOrderResult convert2InstOrderResult(FundOutCancelRequest request, InstOrder instOrder){

        InstOrderResult result = new InstOrderResult();
        result.setInstSeqNo(request.getInstSeqNo());
        result.setInstOrderNo(instOrder.getInstOrderNo());
        result.setInstOrderId(instOrder.getInstOrderId());
        result.setBizType(instOrder.getBizType());
        result.setFundChannelCode(instOrder.getFundChannelCode());
        result.setRealAmount(instOrder.getAmount());
        result.setOperateStatus(InstResultOperateStatus.AWAITING);

        switch (request.getInstOrderStatus()){
            case S:
                result.setStatus(InstOrderResultStatus.SUCCESSFUL);
                result.setProcessStatus(InstOrderProcessStatus.SUCCESS);
                break;
            case F:
                result.setStatus(InstOrderResultStatus.FAILURE);
                result.setProcessStatus(InstOrderProcessStatus.SUCCESS);
                break;
            case I:
                result.setStatus(InstOrderResultStatus.IN_PROCESS);
                result.setProcessStatus(InstOrderProcessStatus.AWAITING);
                break;
            default:
                throw new IllegalArgumentException("请求订单状态非法");
        }

        return result;

    }
}
