package com.uaepay.cmf.ext.service.validator.fund;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.exception.DateValidateException;
import com.uaepay.cmf.common.core.engine.util.CommonUtil;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.common.domain.Extension;
import com.uaepay.validate.Validator;
import com.uaepay.validate.exception.ValidationException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <p>资金请求通用校验器</p>
 *
 * <AUTHOR>
 * @version $Id: FundRequestCommonValidator.java, v 0.1 2012-8-18 上午10:47:27 fuyangbiao Exp $
 */
@Service("fundRequestCommonValidator")
public class FundRequestCommonValidator implements Validator, BasicConstant {

    @Override
    public void validate(Object model) throws ValidationException {
        CmfRequest request = (CmfRequest) model;
        try {
            Assert.isTrue(StringUtils.isNotBlank(request.getPaymentSeqNo()), "支付流水号不能为空");
            Assert.notNull(request.getAmount(), "金额不能为空");

            Assert.notNull(request.getAmount(), "金额不能为空");
            Assert.isTrue(request.getAmount().getAmount().compareTo(ZERO) > 0, "支付金额必须大于零");

            Assert.isTrue(StringUtils.isNotBlank(request.getProductCode()), "产品编码不能为空");
            Assert.isTrue(StringUtils.isNotBlank(request.getPaymentCode()), "支付编码不能为空");

            if (!CommonUtil.validateDateBySeqNo(request.getPaymentSeqNo())) {
                throw new DateValidateException("超过3个月订单不可重发");
            }

            if (!isValidPid(request.getExtension())) {
                request.getExtension().remove(ExtensionKey.TOPAY_MERCHANT_ID.key);
            }

        } catch (IllegalArgumentException e) {
            throw new ValidationException(e.getMessage());
        }
    }

    private static boolean isValidPid(Extension extension) {
        if (extension == null) {
            return true;
        }
        String pid = extension.getValue(ExtensionKey.TOPAY_MERCHANT_ID.key);
        return StringUtils.isEmpty(pid) || (pid.startsWith("2") && pid.length() == 12);
    }
}
