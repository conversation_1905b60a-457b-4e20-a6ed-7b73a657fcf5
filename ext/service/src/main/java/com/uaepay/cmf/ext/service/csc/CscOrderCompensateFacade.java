package com.uaepay.cmf.ext.service.csc;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.domainservice.main.process.NotifyEscrowService;
import com.uaepay.cmf.domainservice.main.process.NotifyGrcService;
import com.uaepay.cmf.domainservice.main.process.NotifyPaymentService;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.pub.csc.compensation.facade.CompensationFacade;
import com.uaepay.pub.csc.compensation.facade.request.CompensateSingleRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date EscrowOrderCompensateFacade.java v1.0  2020-10-16 15:55
 */
@Slf4j
@Component
@Service(version = "${spring.application.name}")
public class CscOrderCompensateFacade implements CompensationFacade {

    @Resource
    private NotifyEscrowService notifyEscrowService;
    @Resource
    private NotifyGrcService notifyGrcService;
    @Resource
    private NotifyPaymentService notifyPaymentService;
    @Resource
    private CmfOrderRepository cmfOrderRepository;

    @Override
    public CommonResponse applySingle(CompensateSingleRequest request) {
        try {
            log.info("CompensationFacade.applySingle.request:{}", request);
            if ("escrow".equals(request.getNotifyType())) {
                notifyEscrowService.notifyOrder(request.getDetail().getOrderNo());
            } else if ("grc".equals(request.getNotifyType())) {
                notifyGrcService.notify3dsResult(request.getDetail().getOrderNo());
            } else if ("paymentNotify".equalsIgnoreCase(request.getNotifyType())) {
                CmfOrder cmfOrder = cmfOrderRepository.loadByPaymentSeqNo(request.getDetail().getOrderNo(), null);
                if (cmfOrder != null) {
                    notifyPaymentService.notifyPE(cmfOrder.getOrderSeqNo(), true);
                }
            }

            log.info("CompensationFacade.applySingle.success");
        } catch (Exception e) {
            log.error("CompensationFacade.apply.error", e);
            return CommonResponse.buildFail(ApplyStatusEnum.FAIL, e.getMessage());
        }
        return CommonResponse.buildSuccess();
    }

}
