package com.uaepay.cmf.ext.service.mq.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.domain.CmfCommonResultCode;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.common.domain.OperationEnvironment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <p>交易撤销</p>
 *
 * <AUTHOR>
 * @date 2022/6/28
 */
@Slf4j
@Service
public class VoidTransactionHandler implements BasicConstant {

    @Resource
    private ControlRequestFacade controlRequestFacade;


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(CMF_INNER_VOID_QUEUE),
            exchange = @Exchange(name = CMF_INNER_EXCHANGE),
            key = QUEUE_VOID_TRANSACTION_ROUTING_KEY))
    public void handleMessage(Message message) {
        log.info("VoidTransactionHandler.handleMessage:{}", message);
        CmfControlRequest cancelCmfControlRequest = JSON.parseObject(message.getBody(), CmfControlRequest.class);

        CmfControlResult vtResult = controlRequestFacade.control(cancelCmfControlRequest, new OperationEnvironment());
        log.info("VoidTransactionHandler.handleMessage response:{}", vtResult);

        Set<CmfCommonResultCode> successfulCmfCommonResultSet = Sets.newHashSet(CmfCommonResultCode.SUCCESS,CmfCommonResultCode.REQUEST_SUCCESS,CmfCommonResultCode.IN_PROCESS);
        Assert.notNull(vtResult,"交易撤销返回为null，将重试！");
        Assert.isTrue(successfulCmfCommonResultSet.contains(vtResult.getResultCode()),"交易撤销返回失败，将重试！errorCode="+vtResult.getResultCode());
    }


}
