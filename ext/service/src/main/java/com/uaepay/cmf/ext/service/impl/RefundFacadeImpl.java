package com.uaepay.cmf.ext.service.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.domainservice.main.general.impl.RetryRefundProcessor;
import com.uaepay.cmf.ext.service.common.ValidatorUtil;
import com.uaepay.cmf.service.facade.api.RefundFacade;
import com.uaepay.cmf.service.facade.domain.fundin.RefundQueryRequest;
import com.uaepay.cmf.service.facade.domain.refund.RetryRefundRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.RefundQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * <p>
 * 充退查询
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: RefundFacadeImpl.java, v 0.1 2010-12-24 下午12:26:49 sean won Exp $
 */
@Slf4j
@Service
public class RefundFacadeImpl implements RefundFacade {

    @Resource
    private RetryRefundProcessor retryRefundProcessor;

    @Override
    public RefundQueryResult queryDuration(RefundQueryRequest request) {
        RefundQueryResult result = new RefundQueryResult();
        result.setApplyStatus(ApplyStatusEnum.SUCCESS);
        result.setRefundDuration(Duration.ofSeconds(0));
        return result;
    }

    @Override
    public CommonResponse retryReFund(RetryRefundRequest request) {
        return retryRefundProcessor.process(request);
    }
}
