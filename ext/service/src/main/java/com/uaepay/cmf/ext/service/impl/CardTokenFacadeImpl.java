package com.uaepay.cmf.ext.service.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.YesNoEnum;
import com.uaepay.channel.cards.service.facade.CardBinFacade;
import com.uaepay.channel.cards.service.facade.domain.CardBinVO;
import com.uaepay.channel.cards.service.facade.domain.request.CardBinValidateRequest;
import com.uaepay.channel.cards.service.facade.domain.response.ValidateResult;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.domain.enums.SvaAccountEnum;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository;
import com.uaepay.cmf.ext.service.common.ValidatorUtil;
import com.uaepay.cmf.ext.service.convertor.CardTokenRequestConverter;
import com.uaepay.cmf.fss.ext.integration.escrow.EscrowClient;
import com.uaepay.cmf.fss.ext.integration.ma.MemberClient;
import com.uaepay.cmf.fss.ext.integration.ues.UesClient;
import com.uaepay.cmf.service.facade.api.CardTokenFacade;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateRequest;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateResult;
import com.uaepay.cmf.service.facade.domain.card.CardTokenQueryResult;
import com.uaepay.cmf.service.facade.domain.card.CardTokenUpdateRequest;
import com.uaepay.member.service.base.model.BeneficiaryInfo;
import com.uaepay.member.service.base.model.CardInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenFacadeImpl.java v1.0  2020-03-28 14:59
 */
@Slf4j
@Service
public class CardTokenFacadeImpl implements CardTokenFacade, BasicConstant {

    @Resource
    private CardTokenRepository cardTokenRepository;

    @Resource(name = "memoryCacheClient")
    private CacheClient cacheClient;

    @Resource
    private MemberClient memberClient;
    @Resource
    private EscrowClient escrowClient;
    @Resource
    private UesClient uesClient;
    @Reference
    private CardBinFacade cardBinFacade;

    @Value("${cardToken.info.store.switch:N}")
    private String storeCardInfoSwitch;

    @Override
    public CardTokenCreateResult create(CardTokenCreateRequest request) {
        CardTokenCreateResult result = null;
        log.info("CardTokenFacade.create.request:{}", request);
        try {
            ValidatorUtil.validate(request);

            String cardTokenId = storeCardToken(request);

            if (StringUtils.isNotEmpty(request.getCsc())) {
                // 与前端协商15分钟过期
                cacheClient.put(CacheType.CSC, cardTokenId, request.getCsc(), FIFTEEN_MINUTE_SECONDS);
            }
            result = CardTokenCreateResult.buildSuccess(cardTokenId);
        } catch (Exception e) {
            log.error("CardTokenFacade.create.error", e);
            result = CardTokenCreateResult.buildFail(e.getMessage());
        }
        log.info("CardTokenFacade.create.result:{}", result);
        return result;
    }

    private String storeCardToken(CardTokenCreateRequest request) {
        if (StringUtils.isNotEmpty(request.getSessionId()) && StringUtils.isNumeric(request.getSessionId())) {
            CardToken cardToken = cardTokenRepository.query(request.getSessionId());
            if (cardToken != null) {
                // 匿名支付
                return anonymousPayUpdateToken(cardToken, request);
            }
        }
        CardToken cardToken = buildCardToken(request);

        return cardTokenRepository.store(cardToken);
    }

    private String anonymousPayUpdateToken(CardToken cardToken, CardTokenCreateRequest request) {
        log.info("CardTokenFacade.storeCardToken.anonymousPay");
        cardToken.setIpAddress(request.getIpAddress());
        if(StringUtils.isNotEmpty(request.getDbcr())) {
            cardToken.setDbcr(request.getDbcr());
        }
        if(StringUtils.isNotEmpty(request.getCompanyOrPersonal())) {
            cardToken.setCompanyOrPersonal(request.getCompanyOrPersonal());
        }
        //FIXME: 匿名支付第二次传过来的instCode是MC，是错误的。
//        if(StringUtils.isNotEmpty(request.getInstCode())) {
//            cardToken.setInstCode(request.getInstCode());
//        }
        if(StringUtils.isNotEmpty(request.getResultUrl())) {
            cardToken.setResultUrl(request.getResultUrl());
        }
        if(StringUtils.isNotEmpty(request.getIs3DS())) {
            cardToken.setIs3DS(request.getIs3DS());
        }
        cardTokenRepository.updateSelective(cardToken);
        return cardToken.getCardTokenId();
    }

    private CardToken buildCardToken(CardTokenCreateRequest request) {
        CardToken cardToken = null;
        if (StringUtils.isNotBlank(request.getCardTokenType())) {
            cardToken = CardTokenRequestConverter.convert(request);
        } else {
            CardInfo cardInfo = memberClient.queryCardInfoById(request.getCardId());
            BeneficiaryInfo beneficiaryInfo = memberClient.queryBeneficiaryInfoById(request.getBeneficiaryId());
            SvaAccountEnum svaAccountEnum = escrowClient.querySvaAccount(request.getMemberId());
            cardToken = CardTokenRequestConverter.convert(request, cardInfo, beneficiaryInfo, uesClient, cardBinFacade, svaAccountEnum, YesNoEnum.getByCode(storeCardInfoSwitch));
        }
        return cardToken;
    }

    @Override
    public CommonResponse update(CardTokenUpdateRequest request) {
        CommonResponse result = null;
        try {
            log.info("CardTokenFacade.update.request:{}", request);
            cardTokenRepository.update(CardTokenRequestConverter.convert(request));
            result = CommonResponse.buildSuccess();
        } catch (Exception e) {
            log.error("CardTokenFacade.update.error", e);
            result = CommonResponse.buildFail("FAIL", e.getMessage());
        }
        log.info("CardTokenFacade.update.result:{}", result);
        return result;
    }

    @Override
    public CardTokenQueryResult query(String cardTokenId) {
        CardTokenQueryResult result = null;
        try {
            log.info("CardTokenFacade.query.request:{}", cardTokenId);
            CardToken cardToken = cardTokenRepository.query(cardTokenId);
            result = CardTokenRequestConverter.convert(cardToken);
        } catch (Exception e) {
            log.error("CardTokenFacade.query.error", e);
            result = CardTokenQueryResult.buildFail(e.getMessage());
        }
        log.info("CardTokenFacade.query.result:{}", result);
        return result;
    }
}
