package com.uaepay.cmf.ext.service.validator.service;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResult;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version ValidateService.java 1.0 Created@2018-03-02 14:24 $
 */
public interface ValidateService extends BasicConstant{

    CmfFundResult validate(CmfRequest cmfRequest);

}
