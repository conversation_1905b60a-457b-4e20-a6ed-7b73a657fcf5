package com.uaepay.cmf.ext.service.verify;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.exception.DuplicateRequestException;
import com.uaepay.cmf.common.core.domain.exception.ValidateException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.domainservice.channel.holder.VerifySignHolder;
import com.uaepay.cmf.domainservice.main.convert.CmfFundResultConverter;
import com.uaepay.cmf.domainservice.main.general.impl.VerifySignNonOrderProcessor;
import com.uaepay.cmf.domainservice.main.general.impl.VerifySignProcessor;
import com.uaepay.cmf.domainservice.main.pattern.CardTokenService;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository;
import com.uaepay.cmf.domainservice.main.spi.VerifySignService;
import com.uaepay.cmf.ext.service.common.ValidatorUtil;
import com.uaepay.cmf.fss.ext.integration.util.ReturnResultUtil;
import com.uaepay.cmf.service.facade.api.VerifySignFacade;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignResult;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p>
 * 验证签名请求类
 * </p>
 *
 * <AUTHOR>
 * @date DefaultVerifySignFacade.java v1.0 2019-11-14 16:52
 */
@Service
@Slf4j
@Component
public class DefaultVerifySignFacade implements VerifySignFacade {

    @Resource
    private VerifySignProcessor verifySignProcessor;
    @Resource
    private CmfOrderRepository cmfOrderRepository;
    @Resource
    private InstOrderRepository instOrderRepository;
    @Resource
    private InstOrderResultRepository instOrderResultRepository;
    @Resource
    private CardTokenService cardTokenService;
    @Resource
    private VerifySignService verifySignService;
    @Resource
    private VerifySignNonOrderProcessor verifySignNonOrderProcessor;

    /**
     * 渠道验签方法
     * 调用渠道验证签名 返回渠道的应答方式和结果
     *
     * @param request
     * @return
     */
    @Override
    public CmfFundResult verifySign(VerifySignRequest request) {
        log.info("VerifySignFacade.verifySign.request:{}", request);
        try {

            validateRequest(request);

            InstOrderResult instOrderResult = processVerifySign(request);

            log.info("VerifySignFacade.verifySign.result:{}", instOrderResult);
            return buildCmfResult(instOrderResult);
        } catch (DuplicateRequestException dpe) {
            log.warn("VerifySignFacade.verifySign.duplicateRequest");
            return buildDuplicateResult(request);
        } catch (IllegalArgumentException | ValidateException e) {
            log.warn("VerifySignFacade.verifySign.illegal: {}", e.getMessage());
            return buildResult(CmfFundResultCode.FAILED, e.getMessage());
        } catch (Exception ex) {
            log.error("VerifySignFacade.verifySign.error", ex);
            return buildResult(CmfFundResultCode.UNKNOW_EXCEPTION, ex.getMessage());
        } finally {
            VerifySignHolder.clear();
        }

    }

    @Override
    public VerifySignResult verify(VerifySignRequest request) {
        if (StringUtils.isEmpty(request.getInstOrderNo())){
            return verifySignNonOrderProcessor.process(request);
        }
        return verifySignProcessor.process(request);
    }

    private InstOrderResult processVerifySign(VerifySignRequest request) throws DuplicateRequestException {
        if (request.isAsync()) {
            verifySignService.asyncVerify(request);
            return ReturnResultUtil.buildInProcessResult(request);
        }
        return verifySignService.verify(request);

    }

    private void validateRequest(VerifySignRequest request) {
        ValidatorUtil.validate(request);
        Assert.isTrue(request.getVerifyParam() != null || StringUtils.isNotEmpty(request.getVerifyParamStr()), "One of verify param and param str should not be null!");
    }


    static CmfFundResult buildResult(CmfFundResultCode resultCode, String resultMsg) {
        CmfFundResult fundResult = new CmfFundResult();
        fundResult.setResultCode(resultCode);
        fundResult.setResultMessage(resultMsg);
        return fundResult;
    }


    protected CmfFundResult buildCmfResult(InstOrderResult instResult) {
        Assert.notNull(instResult.getInstOrderNo(), "返回机构订单为空");
        InstOrder instOrder = instOrderRepository.loadByNo(instResult.getInstOrderNo());
        Assert.notNull(instOrder, "机构订单不存在");
        CardToken cardToken = cardTokenService.queryTokenByInstOrderId(instOrder.getInstOrderId());
        CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(instOrder.getCmfSeqNo(), false);
        return CmfFundResultConverter.convert(cmfOrder, instOrder, instResult, cardToken);
    }

    protected CmfFundResult buildDuplicateResult(VerifySignRequest request) {
        InstOrder instOrder = instOrderRepository.loadByNo(request.getInstOrderNo());
        InstOrderResult instResult = instOrderResultRepository.loadRealResultByOrder(instOrder.getInstOrderId());
        instResult.setProcessStatus(InstOrderProcessStatus.SUCCESS);
        CardToken cardToken = cardTokenService.queryTokenByInstOrderId(instOrder.getInstOrderId());
        CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(instOrder.getCmfSeqNo(), false);
        return CmfFundResultConverter.convert(cmfOrder, instOrder, instResult, cardToken);
    }

}
