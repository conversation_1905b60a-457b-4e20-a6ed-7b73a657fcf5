//package com.uaepay.cmf.ext.service.common.redis.config;
//
//import lombok.Data;
//import org.redisson.Redisson;
//import org.redisson.api.RedissonClient;
//import org.redisson.config.Config;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Data
//@Configuration
//@ConfigurationProperties("spring.redis")
//public class RedissonConfig {
//
//    private static final String REDIS_ADDRESS_PREFIX = "redis://";
//
//    private String host;
//
//    private String password;
//
//    private String port;
//
//    @Bean
//    RedissonClient redissonClient(){
//
//        Config config = new Config();
//        config.useSingleServer()
//                .setAddress(REDIS_ADDRESS_PREFIX + host + ":" + port)
//                .setPassword(password);
//
//        return Redisson.create(config);
//    }
//}
