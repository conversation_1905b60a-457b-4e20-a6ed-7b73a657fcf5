package com.uaepay.cmf.ext.service.process.impl;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.exception.AppRuntimeException;
import com.uaepay.cmf.common.core.domain.exception.DuplicateKeyException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator;
import com.uaepay.cmf.common.core.engine.generator.SequenceNameEnum;
import com.uaepay.cmf.common.enums.MonitorItem;
import com.uaepay.cmf.common.monitor.MonitorLog;
import com.uaepay.cmf.domainservice.main.convert.CmfFundResultConverter;
import com.uaepay.cmf.domainservice.main.convert.CmfRequestConverter;
import com.uaepay.cmf.domainservice.main.process.DuplicateResultProcessService;
import com.uaepay.cmf.domainservice.main.process.impl.ChannelCodeMappingService;
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.ext.service.validator.service.ValidateService;
import com.uaepay.cmf.fss.ext.integration.util.OrderUtil;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 资金类通用处理模板. 出/入/退
 * </p>
 *
 * <AUTHOR>
 * @version FundProcessService.java 1.0 Created@2017-04-05 11:49 $
 */
@Slf4j
@Service
public class FundProcessService<Req extends CmfRequest, Resp extends CmfFundResult>
        extends AbstractProcessService<Req, Resp> {

    @Resource
    private ValidateService validateService;

    @Resource
    private DuplicateResultProcessService duplicateResultProcessService;

//    @Resource
//    private DbRouter dbRouter;

    @Resource
    private PrimaryKeyGenerator primaryKeyGenerator;

    @Resource
    private SubmitInstitutionService submitInstitutionService;

    @Resource
    private OrderLoaderService orderLoaderService;

    @Resource
    private ChannelCodeMappingService channelCodeMappingService;

    @Override
    public CmfFundResult process(CmfRequest request) {

        // 1.参数校验
        CmfFundResult result = validateService.validate(request);
        if (result != null) {
            return result;
        }

        // 2.转换
        CmfOrder cmfOrder = buildCmfOrder(request);

        // 3.落地
        try {
            saveCmfOrder(cmfOrder);
        } catch (Exception e) {
            log.error("资金请求处理异常:", e);
            return duplicateResultProcessService.queryDuplicateResult(cmfOrder.getPaymentSeqNo(),
                    cmfOrder.getSettlementId(), cmfOrder.getBizType());
        }

        // 4.提交
        InstOrderResult instResult = submitInstitutionService.submit(cmfOrder);

        // 5.结果处理
        result = buildCmfResult(cmfOrder, instResult);

        log.info("资金请求处理结果:{}", result);

        return result;
    }

    protected CmfFundResult buildCmfResult(CmfOrder cmfOrder, InstOrderResult instResult) {
        CmfOrder dbCmfOrder = cmfOrderRepository.loadByCmfSeqNo(cmfOrder.getOrderSeqNo(), false);
        InstOrder instOrder = instOrderRepository.loadByCmfSeqNoSingle(cmfOrder.getOrderSeqNo());
        CmfFundResult result = CmfFundResultConverter.convert(dbCmfOrder, instOrder, instResult);
        result.setChannelPayNo(orderLoaderService.loadReturnOrderNo(instOrder, instResult));
        result.setFundsChannel(channelCodeMappingService.getNewChannelCode(result.getFundsChannel(), instOrder));
        return result;
    }

    protected CmfOrder buildCmfOrder(CmfRequest request) {
        CmfOrder cmfOrder = CmfRequestConverter.convert(request);

        // 设置流水号
        generateCmfSeqNo(cmfOrder);

        if (OrderUtil.isRefund(cmfOrder)) {
            CmfOrder origCmfOrder =
                    cmfOrderRepository.loadByPaymentSeqNo(cmfOrder.getOrgiPaymentSeqNo(), cmfOrder.getOrgiSettlementId());
            CmfRequestConverter.convertFromOld(cmfOrder, origCmfOrder);
        }

        return cmfOrder;
    }

    private void generateCmfSeqNo(CmfOrder cmfOrder) {
        // 设置订单号
        String cmfSeqNo = primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER);
        // 订单是否走分表
//        if (dbRouter.isShardingMode(cmfOrder.getMerchantId(), cmfOrder.getPaymentSeqNo())) {
//            cmfSeqNo = DbRouter.gen4Sharding(cmfSeqNo, cmfOrder.getPaymentSeqNo());
//        }
        cmfOrder.setOrderSeqNo(cmfSeqNo);
    }

    private String saveCmfOrder(final CmfOrder cmfOrder) {
        return cmfTransactionTimeoutTemplate.execute(status -> {
            try {
                return cmfOrderRepository.store(cmfOrder);
            } catch (DuplicateKeyException e) {
                monitorService.logMonitorEvent(new MonitorLog(cmfOrder.getPaymentSeqNo(),
                        MonitorItem.DUPLIATE_REQUEST_EXCEPTION, "重复订单请求" + cmfOrder.getPaymentSeqNo(), e));
                throw new AppRuntimeException("重复订单请求" + cmfOrder.getPaymentSeqNo());
            }
        });
    }

}
