package com.uaepay.cmf.ext.service.validator.control;

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService;
import com.uaepay.cmf.ext.service.common.CmfRequestUtil;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.common.util.DateUtil;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.validate.Validator;
import com.uaepay.validate.exception.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version VoidTransactionValidator.java 1.0
 */
@Slf4j
@Service("voidTransactionValidator")
public class VoidTransactionValidator implements Validator {

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private OrderLoaderService orderLoaderService;

    @Override
    public void validate(Object model) throws ValidationException {
        CmfControlRequest controlRequest = (CmfControlRequest) model;
        try {
            String orgiPaymentSeqNo = controlRequest.getExtension().getValue(ExtensionKey.ORGI_FUNDIN_ORDER_NO.key);
            if(StringUtils.isNotEmpty(orgiPaymentSeqNo)){
                InstBaseOrder preOrder = orderLoaderService.loadPreOrder(RequestType.REFUND.name(), orgiPaymentSeqNo, null, null);
                controlRequest.setPreRequestNo(preOrder.getInstOrderNo());
            }

            Assert.notNull(controlRequest, "请求不可为空");
            InstOrder instOrder = instOrderRepository.loadByNo(controlRequest.getPreRequestNo());
            Assert.notNull(instOrder, "机构订单不存在");
            if (BizType.FUNDOUT == instOrder.getBizType()) {
                Assert.isTrue(!DateUtil.isBeforeNow(instOrder.getGmtBookingSubmit()), "已超过发送时间，交易不可撤销");
                Assert.isTrue(instOrder.getArchiveBatchId() == null || instOrder.getArchiveBatchId() == 0L, "订单已打批，不可撤销");
                Assert.isTrue(instOrder.getCommunicateStatus() == CommunicateStatus.AWAITING, "订单已发送渠道，不可撤销");
            }

            if (CmfRequestUtil.isPosFiserv(controlRequest)){
                log.info("fiserv cancel :{}",controlRequest.getRequestNo());
                instOrderRepository.updateInstOrderStatus(instOrder,InstOrderStatus.CANCEL);
                return;
            }
            if (instOrder.getStatus() == InstOrderStatus.IN_PROCESS) {
                // 更新instOrder状态为撤销状态
                boolean updateCancelStatus = instOrderRepository.updateInstOrderStatus(instOrder, InstOrderStatus.CANCEL);
                Assert.isTrue(updateCancelStatus, "更新cancelStatus失败");
            }



            Assert.isTrue(instOrder.getStatus() == InstOrderStatus.CANCEL, "机构订单不可撤销，请确认");

        } catch (Exception e) {
            throw new ValidationException(e.getMessage());
        }
    }
}
