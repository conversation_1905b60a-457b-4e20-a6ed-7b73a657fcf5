package com.uaepay.cmf.ext.service.counter;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.domain.response.PageResponse;
import com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO;
import com.uaepay.cmf.common.core.dal.dataobject.ChannelCodeMappingDO;
import com.uaepay.cmf.service.facade.counter.ChannelCodeMappingFacade;
import com.uaepay.cmf.service.facade.domain.counter.channelcode.*;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class DefaultChannelCodeMappingFacade implements ChannelCodeMappingFacade {

    @Resource
    private ChannelCodeMappingDAO channelCodeMappingDAO;

    @Override
    public PageResponse<ChannelCodeMappingPageResponse> pageQuery(ChannelCodeMappingPageRequest request) {
        // Calculate offset
        int offset = (request.getCurrentPage() - 1) * request.getPageSize();

        // Query data
        List<ChannelCodeMappingDO> records = channelCodeMappingDAO.pageQuery(
                request.getOldChannelCode(),
                request.getNewChannelCode(),
                request.getStatus(),
                offset,
                request.getPageSize()
        );

        // Query total count
        int total = channelCodeMappingDAO.pageCount(
                request.getOldChannelCode(),
                request.getNewChannelCode(),
                request.getStatus()
        );

        // Convert to response
        List<ChannelCodeMappingPageResponse> responseList = new ArrayList<>();
        for (ChannelCodeMappingDO record : records) {
            ChannelCodeMappingPageResponse response = new ChannelCodeMappingPageResponse();
            BeanUtils.copyProperties(record, response);
            responseList.add(response);
        }

        // Build response
        PageResponse<ChannelCodeMappingPageResponse> queryResponse = new PageResponse<>();
        queryResponse.success();
        queryResponse.setTotalSize(total);
        queryResponse.setDataList(responseList);
        return queryResponse;
    }

    @Override
    public CommonResponse create(CreateChannelCodeMappingRequest request) {
        ChannelCodeMappingDO record = new ChannelCodeMappingDO();
        BeanUtils.copyProperties(request, record);
        channelCodeMappingDAO.insert(record);
        CommonResponse response = new CommonResponse();
        response.success();
        return response;
    }

    @Override
    public CommonResponse update(UpdateChannelCodeMappingRequest request) {
        ChannelCodeMappingDO record = new ChannelCodeMappingDO();
        BeanUtils.copyProperties(request, record);
        channelCodeMappingDAO.updateById(record);
        CommonResponse response = new CommonResponse();
        response.success();
        return response;
    }

    @Override
    public CommonResponse updateStatus(UpdateChannelCodeMappingStatusRequest request) {
        channelCodeMappingDAO.updateStatus(request.getId(), request.getStatus());
        CommonResponse response = new CommonResponse();
        response.success();
        return response;
    }
} 