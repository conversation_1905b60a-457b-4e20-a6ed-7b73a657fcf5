package com.uaepay.cmf.ext.service.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.exception.AppRuntimeException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigureKey;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.domainservice.main.convert.CmfRequestConverter;
import com.uaepay.cmf.domainservice.main.general.impl.*;
import com.uaepay.cmf.domainservice.main.process.impl.DuplicateControlOrderResultProcessService;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.ext.service.convertor.CmfResultConverter;
import com.uaepay.cmf.ext.service.validator.factory.ValidatorFactory;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.domain.CmfCommonResultCode;
import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceRequest;
import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceResult;
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthRequest;
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthResponse;
import com.uaepay.cmf.service.facade.domain.card.RetrieveCardMetadataRequest;
import com.uaepay.cmf.service.facade.domain.card.RetrieveCardMetadataResponse;
import com.uaepay.cmf.service.facade.domain.clear.ClearInfo;
import com.uaepay.cmf.service.facade.domain.clear.ClearInfoQueryRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.cmf.service.facade.domain.control.CmfFileRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfFileResponse;
import com.uaepay.cmf.service.facade.domain.control.config.ChannelConfigQueryRequest;
import com.uaepay.cmf.service.facade.domain.control.config.ChannelConfigQueryResponse;
import com.uaepay.cmf.service.facade.domain.control.pos.PosReversalResponse;
import com.uaepay.cmf.service.facade.domain.control.psp.PspReversalRequest;
import com.uaepay.cmf.service.facade.domain.control.psp.PspReversalResponse;
import com.uaepay.cmf.service.facade.result.ListQueryResult;
import com.uaepay.common.domain.OperationEnvironment;
import com.uaepay.validate.Validator;
import com.uaepay.validate.exception.ValidationException;
import org.apache.dubbo.config.annotation.Service;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 控制请求门面
 * </p>
 *
 * <AUTHOR>
 * @version $Id: DefaultControlRequestFacade.java, v 0.1 2012-8-18 下午3:59:29 fuyangbiao Exp $
 */
@Service
public class DefaultControlRequestFacade implements ControlRequestFacade, BasicConstant, SysConfigureKey {
    private static final Logger logger = LoggerFactory.getLogger(DefaultControlRequestFacade.class);

    @Resource
    private ValidatorFactory validatorFactory;
    @Resource
    private SubmitInstitutionService submitInstitutionService;
    @Resource
    private DownloadFileProcessor downloadFileProcessor;
    @Resource
    private FileMigrateProcessor fileMigrateProcessor;
    @Resource
    private FileImportProcessor fileImportProcessor;
    @Resource
    private AuthProcessor authProcessor;
    @Resource
    private Advance3ds2Processor advance3ds2Processor;
    @Resource
    private ClearInfoProcessor clearInfoProcessor;
    @Resource
    private PspReversalProcessor pspReversalProcessor;
    @Resource
    private ChannelConfigQueryProcessor channelConfigQueryProcessor;

    @Resource
    private DuplicateControlOrderResultProcessService duplicateControlOrderResultProcessService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RetrieveCardMetadataProcessor retrieveCardMetadataProcessor;

    @Override
    public CmfControlResult control(CmfControlRequest request, OperationEnvironment environment) {

        CmfControlResult result;
        try {
            logger.info("[控制类]请求:{}", request);

            //添加幂等操作
            CmfControlResult cmfControlResult = duplicateControlOrderResultProcessService.queryDuplicateResult(request.getRequestNo());
            if(Objects.nonNull(cmfControlResult)){
                return cmfControlResult;
            }

            // 1、验证
            validate(request);

            // 2、转换请求
            InstControlOrder order = CmfRequestConverter.convert(request);

            // 3、提交机构
            InstControlOrderResult orderResult = submitInstitutionService.submit(order);

            result = CmfResultConverter.convert(order, orderResult);

        } catch (ValidationException ve) {
            result = CmfResultConverter.convert(CmfCommonResultCode.VALIDARE_ERROR, ve.getMessage());
        } catch (AppRuntimeException ae) {
            result = CmfResultConverter.convert(CmfCommonResultCode.FAILED,
                    CmfCommonResultCode.UNKNOW_EXCEPTION.getMessage());
        } catch (Exception e) {
            logger.error("控制请求[" + request.getRequestNo() + "]处理异常", e);

            result = CmfResultConverter.convert(CmfCommonResultCode.UNKNOW_EXCEPTION,
                    CmfCommonResultCode.UNKNOW_EXCEPTION.getMessage());
        }
        logger.info("[控制类]响应:{}", result);

        return result;
    }

    private void validate(CmfControlRequest request) throws ValidationException {
        // 1、验证
        Validator validator = validatorFactory.load(request.getRequestType());

        if (validator != null) {
            validator.validate(request);
        }
    }

    @Override
    public CmfAdvanceResult advance(CmfAdvanceRequest request) {
        return advance3ds2Processor.process(request);
    }

    @Override
    public CmfAuthResponse auth(CmfAuthRequest request) {
        return authProcessor.process(request);
    }

    @Override
    public CmfFileResponse processFile(CmfFileRequest request) {
        if(request.getRequestType()== ControlRequestType.DOWNLOAD_STATEMENT) {
            return downloadFileProcessor.process(request);
        }else if(request.getRequestType()== ControlRequestType.FILE_MIGRATE){
            return fileMigrateProcessor.process(request);
        }else if(request.getRequestType() == ControlRequestType.FILE_IMPORT){
            return fileImportProcessor.process(request);
        }
        throw new IllegalArgumentException("Not support the request type");
    }

    @Override
    public ListQueryResult<ClearInfo> queryClearInfo(ClearInfoQueryRequest request) {
        return clearInfoProcessor.process(request);
    }

    @Override
    public PspReversalResponse pspReversal(PspReversalRequest request) {
        PspReversalResponse response = new PspReversalResponse();
        RLock lock = redissonClient.getLock(request.getRequestNo());
        try {
            boolean tryLock = lock.tryLock();
            if (!tryLock) {
                response.setApplyStatus(ApplyStatusEnum.FAIL);
                response.setCode(ErrorCode.WRONG_ORDER_DUPLICATE_PROCESS.getErrorCode());
                response.setMessage(ErrorCode.WRONG_ORDER_DUPLICATE_PROCESS.getErrorMessage());
                return response;
            }
            response = pspReversalProcessor.process(request);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return response;
    }

    @Override
    public ChannelConfigQueryResponse queryChannelConfig(ChannelConfigQueryRequest request) {
        return channelConfigQueryProcessor.process(request);
    }

    @Override
    public RetrieveCardMetadataResponse retrieveCardMetadata(RetrieveCardMetadataRequest request) {

        return retrieveCardMetadataProcessor.process(request);
    }

    @Override
    public PosReversalResponse posReversal(PspReversalRequest request) {
        return null;
    }
}
