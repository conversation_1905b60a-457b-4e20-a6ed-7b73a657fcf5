/**
 *
 */
package com.uaepay.cmf.ext.service.common;

import com.uaepay.cmf.common.core.domain.exception.ValidateException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 校验工具类
 *
 * <AUTHOR>
 * @version $Id: ValidatorUtil.java, v 0.1 2016年4月3日 下午5:51:01 aeky Exp $
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ValidatorUtil {
    private static ValidatorFactory factory = Validation.buildDefaultValidatorFactory();

    /**
     * 验证一个对象的字段值
     *
     * @param <T>   被校验对象
     * @param field 对象字段
     */
    public static <T> void validate(T field) {
        Set<ConstraintViolation<T>> violations = factory.getValidator().validate(field);
        if (!CollectionUtils.isEmpty(violations)) {
            ConstraintViolation<T> violation = violations.iterator().next();
            throw new ValidateException(violation.getMessage());
        }
    }

    /**
     * 校验对象所有字段
     *
     * @param <T>    被校验对象
     * @param fields 对象所有字段
     * @return 错误信息列表
     */
    public static <T> List<String> validateAll(T fields) {
        List<String> errorList = new ArrayList<>();
        Set<ConstraintViolation<T>> violations = factory.getValidator().validate(fields);
        if (!CollectionUtils.isEmpty(violations)) {
            for (ConstraintViolation<T> violation : violations) {
                errorList.add(violation.getPropertyPath() + "-" + violation.getMessage());
                break;
            }
        }
        return errorList;
    }

    /**
     * 校验参数
     *
     * @param <T>   被校验对象
     * @param param 校验参数
     */
    public static <T> void validateParam(T param) {
        List<String> errorList = ValidatorUtil.validateAll(param);
        Assert.isTrue(CollectionUtils.isEmpty(errorList), errorList.toString());
    }

}
