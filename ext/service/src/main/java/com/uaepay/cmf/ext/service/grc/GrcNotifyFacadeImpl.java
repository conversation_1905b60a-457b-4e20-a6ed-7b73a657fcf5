package com.uaepay.cmf.ext.service.grc;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.domainservice.main.process.impl.NotifyGrcServiceImpl;
import com.uaepay.cmf.ext.service.common.ValidatorUtil;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import com.uaepay.cmf.service.facade.domain.grc.Query3dsRequest;
import com.uaepay.cmf.service.facade.domain.grc.Query3dsResponse;
import com.uaepay.cmf.service.facade.grc.GrcNotifyFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date GrcNotifyFacadeImpl.java v1.0  2020-10-16 12:09
 */
@Slf4j
@Service
public class GrcNotifyFacadeImpl implements GrcNotifyFacade {

    @Resource
    private NotifyGrcServiceImpl notifyGrcService;

    @Override
    public CommonResponse notify3dsResult(Notify3dsResult result) {
        try {
            log.info("GrcNotifyFacade.notify3dsResult.result:{}", result);
            ValidatorUtil.validate(result);

            notifyGrcService.saveResultAndNotify(result);

        }catch (IllegalArgumentException ire) {
            log.warn("grcNotify.notify3dsResult.ire:{}", ire.getMessage());
            return CommonResponse.buildFail(ApplyStatusEnum.FAIL, ire.getMessage());
        }catch (Exception e) {
            log.error("GrcNotifyFacade.notify3dsResult.error", e);
            return CommonResponse.buildFail(ApplyStatusEnum.FAIL, e.getMessage());
        }
        return CommonResponse.buildSuccess();
    }

    @Override
    public Query3dsResponse query3dsResult(Query3dsRequest request) {
        try {
            log.info("GrcNotifyFacade.query3dsResult.request:{}", request);
            Assert.isTrue(StringUtils.isNotEmpty(request.getProductOrderNo()) || StringUtils.isNotEmpty(request.getPaymentOrderNo())
                    || StringUtils.isNotEmpty(request.getInstOrderNo()), "查询条件至少有一个不为空");

            Notify3dsResult result = notifyGrcService.query3dsResult(request);

            Query3dsResponse response = (Query3dsResponse) new Query3dsResponse().success();
            response.setResult(result);

            log.info("GrcNotifyFacade.query3dsResult.response:{}", response);
            return response;
        } catch (Exception e) {
            log.error("GrcNotifyFacade.query3dsResult.error", e);
            return (Query3dsResponse) new Query3dsResponse().fail(ApplyStatusEnum.FAIL, e.getMessage());
        }
    }

}
