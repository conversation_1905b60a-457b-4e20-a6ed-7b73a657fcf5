#!/bin/bash

# F5-TTS Demo Script
# 可在任何目录运行的F5-TTS推理脚本

# 设置默认参数
DEFAULT_OUTPUT_DIR="$HOME/f5_tts_output"
DEFAULT_REFERENCE="/Users/<USER>/Downloads/eng.wav"
DEFAULT_TEXT="Ever wondered what happens when a cat and dog really, truly fight?"

# 解析命令行参数
REFERENCE_AUDIO="$DEFAULT_REFERENCE"
SOURCE_TEXT="$DEFAULT_TEXT"
TARGET_TEXT="$DEFAULT_TEXT"
OUTPUT_DIR="$DEFAULT_OUTPUT_DIR"
OUTPUT_FILE="demo_chinese_output.wav"

# 显示帮助信息
show_help() {
    echo "F5-TTS 推理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -r, --reference    参考音频文件路径 (默认: $DEFAULT_REFERENCE)"
    echo "  -s, --source       源文本 (默认: $DEFAULT_TEXT)"
    echo "  -t, --target       目标文本 (默认: $DEFAULT_TEXT)"
    echo "  -o, --output-dir   输出目录 (默认: $DEFAULT_OUTPUT_DIR)"
    echo "  -w, --output-file  输出文件名 (默认: $OUTPUT_FILE)"
    echo "  -h, --help         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  $0 -r /path/to/audio.wav -s \"Hello world\" -t \"Hello world\""
    echo "  $0 -o /custom/output/dir -w my_output.wav"
}

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--reference)
            REFERENCE_AUDIO="$2"
            shift 2
            ;;
        -s|--source)
            SOURCE_TEXT="$2"
            shift 2
            ;;
        -t|--target)
            TARGET_TEXT="$2"
            shift 2
            ;;
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -w|--output-file)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 创建输出目录（如果不存在）
mkdir -p "$OUTPUT_DIR"

# 检查参考音频文件是否存在
if [[ ! -f "$REFERENCE_AUDIO" ]]; then
    echo "错误: 参考音频文件不存在: $REFERENCE_AUDIO"
    exit 1
fi

# 显示运行信息
echo "=================================="
echo "F5-TTS 推理开始"
echo "=================================="
echo "参考音频: $REFERENCE_AUDIO"
echo "源文本: $SOURCE_TEXT"
echo "目标文本: $TARGET_TEXT"
echo "输出目录: $OUTPUT_DIR"
echo "输出文件: $OUTPUT_FILE"
echo "=================================="

# 切换到输出目录并运行命令
cd "$OUTPUT_DIR"

python3.11 -m f5_tts.infer.infer_cli \
    -r "$REFERENCE_AUDIO" \
    -s "$SOURCE_TEXT" \
    -t "$TARGET_TEXT" \
    -o "." \
    -w "$OUTPUT_FILE"

# 检查执行结果
if [[ $? -eq 0 ]]; then
    echo "=================================="
    echo "✅ F5-TTS 推理成功完成！"
    echo "输出文件: $OUTPUT_DIR/$OUTPUT_FILE"
    echo "=================================="
else
    echo "=================================="
    echo "❌ F5-TTS 推理失败！"
    echo "=================================="
    exit 1
fi 