package com.uaepay.schema.cmf.common;

import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

class ResultTest {
    @Mock
    CmfControlResult data;
    @InjectMocks
    Result result;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testOfSuccess() {
        Result<CmfControlResult> result = Result.ofSuccess();
        Assertions.assertEquals(true, result.isSuccess());
    }

    @Test
    void testOfNothing() {
        Result<CmfControlResult> result = Result.ofNothing();
        Assertions.assertEquals(true, result.isSuccess());
    }

    @Test
    void testOfFail() {
        Result<CmfControlResult> result = Result.ofFail();
        Assertions.assertEquals(false, result.isSuccess());
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme