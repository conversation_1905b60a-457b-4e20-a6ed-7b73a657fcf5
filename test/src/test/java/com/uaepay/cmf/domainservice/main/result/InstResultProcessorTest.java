package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.exception.WrongStateException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.limit.LimitService;
import com.uaepay.cmf.domainservice.main.pattern.CardTokenService;
import com.uaepay.cmf.domainservice.main.process.*;
import com.uaepay.cmf.domainservice.main.repository.*;
import com.uaepay.cmf.domainservice.main.validate.ChannelResultValidator;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.common.util.money.Money;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

class InstResultProcessorTest {
    @Mock
    Map<InstOrderResultStatus, InstOrderStatus> instOrderFinalStatusMap;
    @Mock
    Map<InstOrderStatus, CmfOrderStatus> orderFinalStatusMap;
    @Mock
    ControlRequestFacade controlRequestFacade;
    @Mock
    Logger log;
    @Mock
    InstOrderRepository instOrderRepository;
    @Mock
    InstControlOrderRepository controlOrderRepository;
    @Mock
    InstOrderResultRepository instOrderResultRepository;
    @Mock
    InstControlOrderResultRepository controlOrderResultRepository;
    @Mock
    MonitorService monitorService;
    @Mock
    ChannelResultValidator channelResultValidator;
    @Mock
    CmfOrderRepository cmfOrderRepository;
    @Mock
    NotifyPaymentService notifyPaymentService;
    @Mock
    NotifyCashdeskService notifyCashdeskService;
    @Mock
    NotifyEscrowService notifyEscrowService;
    @Mock
    NotifyCounterService notifyCounterService;
    @Mock
    LimitService limitService;
    @Mock
    CardTokenService cardTokenService;
    @Mock
    DuplicateResultProcessService resultProcess;
    @Mock
    ResultCodeService resultCodeService;
    @Mock
    SaveBankReceiveService saveBankReceiveService;
    @Mock
    Map<CommunicateStatus, List<CommunicateStatus>> statusMapping;
    @Mock
    Map<InstOrderStatus, List<InstOrderStatus>> orderStatusMapping;
    @Mock
    Map<InstOrderProcessStatus, CommunicateStatus> targetCommStatusMap;
    @Mock
    Money ZERO_MONEY;
    @Mock
    BigDecimal ZERO;
    @Mock
    Money SPLIT_MIN_AMOUNT;
    @InjectMocks
    InstResultProcessor instResultProcessor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testValidateResult() {
        instResultProcessor.validateResult(new InstOrder(), new InstOrderResult());
    }

    @Test
    void testConvert2InstResp() {
        InstOrderResult result = instResultProcessor.convert2InstResp(new InstOrder(), new ChannelFundResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY));
        Assertions.assertEquals(new InstOrderResult(), result);
    }

    @Test
    void testStoreResult() throws WrongStateException {

        when(instOrderRepository.updateInstOrderStatus(any(), any())).thenReturn(true);
        when(instOrderRepository.updateCommunicateStatusWithPreStatus(any(), any(), any())).thenReturn(1);
        when(instOrderRepository.isCompleteSuccess(any(InstOrder.class))).thenReturn(true);
        when(instOrderResultRepository.storeOrUpdate(any())).thenReturn(Long.valueOf(1));
        when(cmfOrderRepository.loadByCmfSeqNo(anyString(), anyBoolean())).thenReturn(new CmfOrder());
        when(cmfOrderRepository.updateCmfOrderStatus(any(), any())).thenReturn(Boolean.TRUE);
        when(cardTokenService.updateCardToken(any(InstOrder.class), any(InstOrderResult.class))).thenReturn(true);

        InstOrderResult instOrderResult = new InstOrderResult();
        instOrderResult.setProcessStatus(InstOrderProcessStatus.SUCCESS);

        InstOrder newOrder = new InstOrder();
        newOrder.setCommunicateStatus(CommunicateStatus.SENT);

        Assertions.assertDoesNotThrow(()->instResultProcessor.storeResult(newOrder, instOrderResult));
    }

    @Test
    void testLaterProcess() {
        when(controlRequestFacade.control(any(), any())).thenReturn(new CmfControlResult());
        when(instOrderRepository.updateInstOrderStatus(any(), any())).thenReturn(true);
        when(instOrderResultRepository.loadRealResultByOrder(anyLong())).thenReturn(new InstOrderResult());
        when(cmfOrderRepository.loadByCmfSeqNo(anyString(), anyBoolean())).thenReturn(new CmfOrder());
        when(limitService.recordFlow(any())).thenReturn(true);

        instResultProcessor.laterProcess(new InstOrder(), new InstOrderResult());
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme