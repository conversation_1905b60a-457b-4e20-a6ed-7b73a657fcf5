package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.common.util.DateUtil;
import com.uaepay.common.util.money.Money;
import org.joda.time.DateTime;
import org.junit.jupiter.api.*;

import java.math.BigDecimal;

class ChannelResultConverterTest {

    @Test
    @DisplayName("src为null返回null")
    void testConvert() {
        InstOrderResult result = ChannelResultConverter.convert(new InstOrder(), new ChannelFundResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY));
        Assertions.assertNotNull(result);
        InstOrderResult result2 = ChannelResultConverter.convert(new InstOrder(), null);
        Assertions.assertNull(result2);
    }

    @Test
    @DisplayName("src为null返回null")
    void testConvert2() {

        InstOrderResult result = ChannelResultConverter.convert(null, new InstOrder());
        Assertions.assertNull(result);

        InstControlOrderResult controlOrderResult = new InstControlOrderResult();
        controlOrderResult.setApiType(FundChannelApiType.VOID_TRANSACTION);
        InstOrder instOrder = new InstOrder();
        instOrder.setAmount(new Money(BigDecimal.ONE,"AED"));

        // src.amount为null，使用机构单金额
        InstOrderResult result2 = ChannelResultConverter.convert(controlOrderResult, instOrder);
        Assertions.assertEquals(new BigDecimal("1.00"),result2.getRealAmount().getAmount());

        // src.amount有值，取结果金额
        controlOrderResult.setAmount(new Money(BigDecimal.TEN,"AED"));
        InstOrderResult result3 = ChannelResultConverter.convert(controlOrderResult, instOrder);
        Assertions.assertEquals(new BigDecimal("10.00"),result3.getRealAmount().getAmount());

        //撤销成功返回机构单失败
        controlOrderResult.setStatus(InstOrderResultStatus.SUCCESSFUL);
        InstOrderResult result4 = ChannelResultConverter.convert(controlOrderResult, instOrder);
        Assertions.assertEquals(InstOrderResultStatus.FAILURE,result4.getStatus());

        //撤销失败返回机构单处理中
        controlOrderResult.setStatus(InstOrderResultStatus.FAILURE);
        InstOrderResult result5 = ChannelResultConverter.convert(controlOrderResult, instOrder);
        Assertions.assertEquals(InstOrderResultStatus.IN_PROCESS,result5.getStatus());

        //非撤销机构单状态和结果状态一致
        controlOrderResult.setApiType(FundChannelApiType.ADVANCE_3DS2);
        controlOrderResult.setStatus(InstOrderResultStatus.RISK);
        InstOrderResult result6 = ChannelResultConverter.convert(controlOrderResult, instOrder);
        Assertions.assertEquals(InstOrderResultStatus.RISK,result6.getStatus());
    }

    @Test
    void testConvert3() {
        ChannelFundResult channelFundResult = new ChannelFundResult();
        //入参为null，返回null
        InstOrderResult result = ChannelResultConverter.convert(null);
        Assertions.assertEquals(null, result);

        //渠道交易时间==机构清算时间
        channelFundResult.setInstSettleTime(DateTime.now().toDate());
        InstOrderResult result1 = ChannelResultConverter.convert(channelFundResult);
        Assertions.assertEquals(DateUtil.format(channelFundResult.getInstSettleTime(), DateUtil.shortFormat), result1.getExtension().get(ExtensionKey.CHANNEL_TRANS_TIME.key));
        //渠道交易时间==机构处理时间
        channelFundResult.setInstSettleTime(null);
        channelFundResult.setProcessTime(DateTime.now().toDate());
        InstOrderResult result2 = ChannelResultConverter.convert(channelFundResult);
        Assertions.assertEquals(DateUtil.format(channelFundResult.getProcessTime(), DateUtil.shortFormat), result2.getExtension().get(ExtensionKey.CHANNEL_TRANS_TIME.key));
        Assertions.assertEquals(new BigDecimal("0.00"),result2.getRealAmount().getAmount());

        channelFundResult.setRealAmount(new Money(BigDecimal.ONE,"AED"));
        InstOrderResult result3 = ChannelResultConverter.convert(channelFundResult);
        Assertions.assertEquals(new BigDecimal("1.00"),result3.getRealAmount().getAmount());
    }

    @Test
    void testConvert4() {
        InstOrderResult result = ChannelResultConverter.convert(new ChannelFundBatchResult(true, "apiResultCode", "resultMessage", FundChannelApiType.SINGLE_PAY), new InstBatchOrder());
        Assertions.assertNotNull(result);
    }

    @Test
    void testConvert5() {

        InstControlOrderResult result = ChannelResultConverter.convert(null, new InstControlOrder());
        Assertions.assertNull(result);
        InstControlOrderResult result1 = ChannelResultConverter.convert(new InstControlOrderResult(), new InstControlOrder());
        Assertions.assertNotNull(result1);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme