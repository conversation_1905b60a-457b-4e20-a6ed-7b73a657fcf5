package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.convert.ChannelResultConverter;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.schema.cmf.common.Result;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.slf4j.Logger;

import java.util.Optional;

import static org.mockito.Mockito.*;

class InstVoidTxResultLaterProcessorTest {
    @Mock
    InstResultProcessor instResultProcessor;
    @Mock
    InstOrderRepository instOrderRepository;
    @Mock
    Logger log;
    @InjectMocks
    InstVoidTxResultLaterProcessor instVoidTxResultLaterProcessor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testInit() {
        instVoidTxResultLaterProcessor.init();

        Optional<ResultLaterProcessor> resultOpt =  ResultLaterProcessorEnum.getResultLaterProcessor(FundChannelApiType.VOID_TRANSACTION);
        Assert.assertTrue(resultOpt.isPresent());
        Assert.assertEquals(resultOpt.get(), instVoidTxResultLaterProcessor);
    }

    @Test
    @DisplayName("交易状态不为撤销")
    void testProcess() {
        when(instResultProcessor.process(any(InstOrder.class), any(ChannelFundResult.class))).thenReturn(null);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(new InstOrder());

        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setPreInstOrderNo("xxx");
        instControlOrder.setStatus(InstOrderStatus.CANCEL);
        Result<?> result = instVoidTxResultLaterProcessor.process(instControlOrder, new InstControlOrderResult());
        Assert.assertEquals(result.getCode(), Result.Codes.NOTHING.getCode());

        InstOrder preInstOrder = new InstOrder();
        preInstOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        instControlOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(preInstOrder);
        Throwable throwable = Assertions.assertThrows(Exception.class,()->instVoidTxResultLaterProcessor.process(instControlOrder, new InstControlOrderResult()));
        Assertions.assertEquals("交易状态不为撤销",throwable.getMessage());

    }
    @Test
    @DisplayName("被撤销的机构订单状态应该为失败")
    void testProcess2() {
        when(instResultProcessor.process(any(InstOrder.class), any(ChannelFundResult.class))).thenReturn(null);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(new InstOrder());

        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setPreInstOrderNo("xxx");
        instControlOrder.setStatus(InstOrderStatus.SUCCESSFUL);

        InstOrder preInstOrder = new InstOrder();
        preInstOrder.setStatus(InstOrderStatus.CANCEL);

        try (MockedStatic<ChannelResultConverter> mock = Mockito.mockStatic(ChannelResultConverter.class)) {
            InstOrderResult instOrderResult = new InstOrderResult();
            instOrderResult.setStatus(InstOrderResultStatus.SUCCESSFUL);
            mock.when(()-> ChannelResultConverter.convert(any(InstControlOrderResult.class),any(InstOrder.class))).thenReturn(instOrderResult);
            mock.when(()->instOrderRepository.loadByNo(anyString())).thenReturn(preInstOrder);

            InstControlOrderResult controlOrderResult = new InstControlOrderResult();
            Throwable throwable = Assertions.assertThrows(Exception.class,()->instVoidTxResultLaterProcessor.process(instControlOrder, controlOrderResult));

            Assertions.assertEquals("被撤销的机构订单状态应该为失败",throwable.getMessage());
        }

    }


    @Test
    @DisplayName("把前置机构单状态更新为结果状态")
    void testProcess_S() {

        InstOrder instOrder = new InstOrder();
        instOrder.setStatus(InstOrderStatus.CANCEL);
        when(instResultProcessor.process(any(InstOrder.class), any(ChannelFundResult.class))).thenReturn(null);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);

        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        instControlOrder.setPreInstOrderNo("123");

        InstControlOrderResult controlOrderResult = new InstControlOrderResult();
        controlOrderResult.setApiType(FundChannelApiType.VOID_TRANSACTION);
        controlOrderResult.setStatus(InstOrderResultStatus.SUCCESSFUL);
        Result<?> result = instVoidTxResultLaterProcessor.process(instControlOrder,controlOrderResult);
        Assert.assertEquals(result.getCode(), Result.Codes.SUCCESS.getCode());
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme