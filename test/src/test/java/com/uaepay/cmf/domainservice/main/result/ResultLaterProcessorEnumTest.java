package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.cmf.common.enums.FundChannelApiType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.util.Map;
import java.util.Optional;

import static org.mockito.Mockito.*;

class ResultLaterProcessorEnumTest {


    @Test
    void testGetResultLaterProcessor() {
        ResultLaterProcessorEnum resultLaterProcessorEnum = ResultLaterProcessorEnum.ADVANCE_3DS2;
        resultLaterProcessorEnum.register(new Control3Ds2ResultLaterProcessor());

        Optional<ResultLaterProcessor> result = ResultLaterProcessorEnum.getResultLaterProcessor(FundChannelApiType.ADVANCE_3DS2);
        Assertions.assertTrue(result.isPresent());
        Assertions.assertTrue(result.get() instanceof Control3Ds2ResultLaterProcessor);

        Optional<ResultLaterProcessor> result2 = ResultLaterProcessorEnum.getResultLaterProcessor(FundChannelApiType.DEBIT_ADVANCE);
        Assertions.assertFalse(result2.isPresent());

        Optional<ResultLaterProcessor> result3 = ResultLaterProcessorEnum.getResultLaterProcessor(null);
        Assertions.assertFalse(result3.isPresent());
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme