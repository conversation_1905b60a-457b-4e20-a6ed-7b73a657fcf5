package com.uaepay.cmf.domainservice.main.result;

import com.google.common.collect.ImmutableMap;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderType;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.convert.ChannelResultConverter;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.schema.cmf.common.Result;
import org.assertj.core.util.Maps;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.slf4j.Logger;

import java.util.Optional;

import static com.uaepay.cmf.common.core.domain.enums.InstOrderType.CONTROL;
import static com.uaepay.cmf.common.core.domain.enums.InstOrderType.FUND;
import static org.mockito.Mockito.*;

class Control3Ds2ResultLaterProcessorTest {
    @Mock
    InstResultProcessor processor;
    @Mock
    ControlResultProcessor controlResultProcessor;
    @Mock
    InstOrderRepository instOrderRepository;
    @Mock
    InstControlOrderRepository controlOrderRepository;
    @Mock
    Logger log;
    @InjectMocks
    Control3Ds2ResultLaterProcessor control3Ds2ResultLaterProcessor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testInit() {

        control3Ds2ResultLaterProcessor.init();
        Optional<ResultLaterProcessor> resultOpt =  ResultLaterProcessorEnum.getResultLaterProcessor(FundChannelApiType.ADVANCE_3DS2);
        Assert.assertTrue(resultOpt.isPresent());
        Assert.assertEquals(resultOpt.get(), control3Ds2ResultLaterProcessor);
    }


    @Test
    @DisplayName("3ds返回结果处理，修改前置控制单状态=返回结果状态")
    void testProcess() {
        doReturn(null).when(processor).process(any(InstOrder.class ), any(ChannelFundResult.class));
        when(controlResultProcessor.process(any(InstControlOrder.class), any(InstControlOrderResult.class))).thenReturn(null);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(new InstOrder());

        InstControlOrder preInstControlOrder = new InstControlOrder();
        preInstControlOrder.setStatus(InstOrderStatus.IN_PROCESS);
        when(controlOrderRepository.loadByNo(anyString())).thenReturn(preInstControlOrder);

        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        instControlOrder.setExtension(Maps.newHashMap(ExtensionKey.INST_ORDER_TYPE.getKey(), InstOrderType.CONTROL.name()));
        instControlOrder.setPreInstOrderNo("123");

        Assert.assertTrue(preInstControlOrder.getStatus() == InstOrderStatus.IN_PROCESS);
        Result result= control3Ds2ResultLaterProcessor.process(instControlOrder, new InstControlOrderResult());
        Assert.assertEquals(result.getCode(), Result.Codes.SUCCESS.getCode());
    }

    @Test
    @DisplayName("3ds返回结果处理，修改前置机构单状态=返回结果状态")
    void testProcess_instOrder() {
        doReturn(null).when(processor).process(any(InstOrder.class ), any(ChannelFundResult.class));
        when(controlResultProcessor.process(any(InstControlOrder.class), any(InstControlOrderResult.class))).thenReturn(null);

        InstOrder instOrder = new InstOrder();
        instOrder.setStatus(InstOrderStatus.IN_PROCESS);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);

        Assert.assertTrue(instOrder.getStatus() == InstOrderStatus.IN_PROCESS);
        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        instControlOrder.setPreInstOrderNo("123");
        Result result= control3Ds2ResultLaterProcessor.process(instControlOrder, new InstControlOrderResult());
        Assert.assertEquals(result.getCode(), Result.Codes.SUCCESS.getCode());

    }
    @Test
    @DisplayName("机构单交易状态不为处理中")
    void testProcess_instOrder5() {

        InstOrder instOrder = new InstOrder();

        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        instControlOrder.setPreInstOrderNo("123");

        //机构单逻辑 不为处理中
        instOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);

        instControlOrder.setExtension(ImmutableMap.of(ExtensionKey.INST_ORDER_TYPE.getKey(),FUND.name()));
        Throwable throwable = Assertions.assertThrows(Exception.class,()->control3Ds2ResultLaterProcessor.process(instControlOrder, new InstControlOrderResult()));
        Assertions.assertEquals("机构单交易状态不为处理中",throwable.getMessage());

    }

    @Test
    @DisplayName("机构状态与推进状态应该一致")
    void testProcess_instOrder0() {

        InstOrder instOrder = new InstOrder();

        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        instControlOrder.setPreInstOrderNo("123");

        //机构单逻辑 机构单推进状态不一致
        try (MockedStatic<ChannelResultConverter> mock = Mockito.mockStatic(ChannelResultConverter.class)) {
            InstOrderResult instOrderResult = new InstOrderResult();
            instOrderResult.setStatus(InstOrderResultStatus.SUCCESSFUL);
            mock.when(()-> ChannelResultConverter.convert(any(InstControlOrderResult.class),any(InstOrder.class))).thenReturn(instOrderResult);
            instOrder.setStatus(InstOrderStatus.IN_PROCESS);

            mock.when(()->instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);
            instControlOrder.setExtension(ImmutableMap.of(ExtensionKey.INST_ORDER_TYPE.getKey(),FUND.name()));
            Throwable throwable = Assertions.assertThrows(Exception.class,()->control3Ds2ResultLaterProcessor.process(instControlOrder, new InstControlOrderResult()));
            Assertions.assertEquals("机构状态与推进状态应该一致",throwable.getMessage());
        }

    }

    @Test
    @DisplayName("控制单交易状态不为处理中")
    void testProcess_instOrder1() {
        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        instControlOrder.setPreInstOrderNo("123");
        //控制单逻辑 不为处理中
        instControlOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        when(controlOrderRepository.loadByNo(anyString())).thenReturn(instControlOrder);

        instControlOrder.setExtension(ImmutableMap.of(ExtensionKey.INST_ORDER_TYPE.getKey(),CONTROL.name()));
        Throwable throwable = Assertions.assertThrows(Exception.class,()->control3Ds2ResultLaterProcessor.process(instControlOrder, new InstControlOrderResult()));

        Assertions.assertEquals("控制单交易状态不为处理中",throwable.getMessage());
    }

    @Test
    @DisplayName("控制单状态与推进状态应该一致")
    void testProcess_instOrder2() {
        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        instControlOrder.setPreInstOrderNo("123");

        InstControlOrder preInstControlOrder = new InstControlOrder();
        preInstControlOrder.setStatus(InstOrderStatus.IN_PROCESS);

        try (MockedStatic<ChannelResultConverter> mock = Mockito.mockStatic(ChannelResultConverter.class)) {
            InstControlOrderResult controlOrderResult = new InstControlOrderResult();
            controlOrderResult.setStatus(InstOrderResultStatus.SUCCESSFUL);
            mock.when(()-> ChannelResultConverter.convert(any(InstControlOrderResult.class),any(InstControlOrder.class))).thenReturn(controlOrderResult);
            instControlOrder.setStatus(InstOrderStatus.SUCCESSFUL);
            mock.when(()->controlOrderRepository.loadByNo(anyString())).thenReturn(preInstControlOrder);
            instControlOrder.setExtension(ImmutableMap.of(ExtensionKey.INST_ORDER_TYPE.getKey(),CONTROL.name()));
            Throwable throwable = Assertions.assertThrows(Exception.class,()->control3Ds2ResultLaterProcessor.process(instControlOrder, new InstControlOrderResult()));
            Assertions.assertEquals("控制单状态与推进状态应该一致",throwable.getMessage());
        }


    }

    @Test
    @DisplayName("结果不为终态，什么也不做")
    void testProcess_nothing() {
        doReturn(null).when(processor).process(any(InstOrder.class ), any(ChannelFundResult.class));
        when(controlResultProcessor.process(any(InstControlOrder.class), any(InstControlOrderResult.class))).thenReturn(null);

        InstOrder instOrder = new InstOrder();
        instOrder.setStatus(InstOrderStatus.IN_PROCESS);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);

        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setStatus(InstOrderStatus.IN_PROCESS);
        Result result= control3Ds2ResultLaterProcessor.process(instControlOrder, new InstControlOrderResult());
        Assert.assertEquals(result.getCode(), Result.Codes.NOTHING.getCode());

        result= control3Ds2ResultLaterProcessor.process(new InstControlOrder(), new InstControlOrderResult());
        Assert.assertEquals(result.getCode(), Result.Codes.NOTHING.getCode());

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme