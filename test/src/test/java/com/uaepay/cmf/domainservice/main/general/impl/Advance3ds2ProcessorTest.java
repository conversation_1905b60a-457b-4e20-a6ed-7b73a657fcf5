package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderResultRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceRequest;
import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceResult;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;

import static org.mockito.Mockito.*;
class Advance3ds2ProcessorTest extends ApplicationTest {
    @Resource(name = "memoryCacheClient")
    CacheClient cacheClient;
    @Resource
    InstOrderRepository instOrderRepository;
    @Resource
    InstControlOrderRepository instControlOrderRepository;
    @Resource
    InstControlOrderResultRepository instControlOrderResultRepository;
    @Resource
    SubmitInstitutionService submitInstitutionService;
    @InjectMocks
    Advance3ds2Processor advance3ds2Processor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);

        ReflectionTestUtils.setField(advance3ds2Processor,"cacheClient",cacheClient);
        ReflectionTestUtils.setField(advance3ds2Processor,"instOrderRepository",instOrderRepository);
        ReflectionTestUtils.setField(advance3ds2Processor,"instControlOrderRepository",instControlOrderRepository);
        ReflectionTestUtils.setField(advance3ds2Processor,"submitInstitutionService",submitInstitutionService);
        ReflectionTestUtils.setField(advance3ds2Processor,"instControlOrderResultRepository",instControlOrderResultRepository);
    }

    @Test
    void testGetServiceName() {
        String result = advance3ds2Processor.getServiceName();
        Assertions.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    void testBusinessValidate() {
        when(cacheClient.get(any(), anyString())).thenReturn("getResponse");
        when(instOrderRepository.loadByNo(anyString())).thenReturn(new InstOrder());
        when(instControlOrderRepository.loadByNo(anyString())).thenReturn(new InstControlOrder());

        advance3ds2Processor.businessValidate(new CmfAdvanceRequest());
    }

    @Test
    void testCreateResponse() {
        CmfAdvanceResult result = advance3ds2Processor.createResponse();
        Assertions.assertEquals(new CmfAdvanceResult(), result);
    }

    @Test
    void testProcess() {
        advance3ds2Processor.process(getCmfAdvanceRequest(), new CmfAdvanceResult());
    }

    private CmfAdvanceRequest getCmfAdvanceRequest(){

        String json = "{\"clientId\":\"cashdesk-api\",\"extension\":{\"screenWidth\":\"390\",\"screenHeight\":\"844\",\"browser\":\"Mozilla/5.0 (iPhone; CPU iPhone OS 15_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;(null)/3.0.1(iOS,en-CN) iOSMPAdapter/21(mixPMPrompt) Safari/534.50 PayBy/3.2.2 (payby, 3.0.1, ***********, iPhone14,5, en)\",\"timeZone\":\"480\",\"language\":\"zh-CN\",\"VERIFY_PARAM\":\"{\\\"Response\\\":\\\"eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI2MTRhMjljNmZkMGMzMzEwZmUzZWJlNGUiLCJpYXQiOjE2NTgxMTYyNDEsImV4cCI6MTY1ODEyMzQ0MSwianRpIjoiNTY1NGUyZjYtMDgyMS00MDM3LTkwOWMtNWI5ODJhNjllZmVmIiwiUGF5bG9hZCI6eyJBY3Rpb25Db2RlIjoiU1VDQ0VTUyIsIlNlc3Npb25JZCI6ImJlOTZkZjE1LWQ2MDMtNGYzNC05NDIwLThjYTI0ZmU5Yjc0MCIsIkVycm9yTnVtYmVyIjowLCJFcnJvckRlc2NyaXB0aW9uIjoiU3VjY2VzcyJ9fQ.bN0ZpTvJUoOTX4lMrZtDKW4e7qVDBSTZm-Dvr-WpAg8\\\"}\"},\"instOrderToken\":\"9c180a9a154947e59422ec8d8c06f549\"}";
        CmfAdvanceRequest request = null;
        try {
            request = JsonUtil.parseObject(json, CmfAdvanceRequest.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return request;
    }



    @Test
    void testProcess2() {
        CmfAdvanceResult result = advance3ds2Processor.process(new CmfAdvanceRequest());
        Assertions.assertEquals(new CmfAdvanceResult(), result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme