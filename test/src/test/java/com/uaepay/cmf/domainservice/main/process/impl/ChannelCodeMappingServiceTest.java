package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO;
import com.uaepay.cmf.common.core.dal.dataobject.ChannelCodeMappingDO;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class ChannelCodeMappingServiceTest {

    @InjectMocks
    private ChannelCodeMappingService channelCodeMappingService;

    @Mock
    private ChannelCodeMappingDAO channelCodeMappingDAO;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetNewChannelCode_NullInstOrder() {
        // Given
        String oldChannelCode = "OLD_CODE";

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, null);

        // Then
        assertEquals(oldChannelCode, result);
    }

    @Test
    public void testGetNewChannelCode_NoRules() {
        // Given
        String oldChannelCode = "OLD_CODE";
        InstOrder instOrder = createTestInstOrder();
        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Collections.emptyList());

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(oldChannelCode, result);
    }

    @Test
    public void testGetNewChannelCode_MatchOrderType() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String newChannelCode = "NEW_CODE";
        InstOrder instOrder = createTestInstOrder();

        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(oldChannelCode);
        rule.setNewChannelCode(newChannelCode);
        rule.setMatchExpression("orderType == 'I'");
        rule.setStatus("Y");
        rule.setPriority(1);
        rule.setRuleName("Test Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(rule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(newChannelCode, result);
    }

    @Test
    public void testGetNewChannelCode_MatchPayMode() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String newChannelCode = "NEW_CODE";
        InstOrder instOrder = createTestInstOrder();

        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(oldChannelCode);
        rule.setNewChannelCode(newChannelCode);
        rule.setMatchExpression("payMode == 'BALANCE'");
        rule.setStatus("Y");
        rule.setPriority(1);
        rule.setRuleName("Test Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(rule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(newChannelCode, result);
    }

    @Test
    public void testGetNewChannelCode_MatchExtension() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String newChannelCode = "NEW_CODE";
        InstOrder instOrder = createTestInstOrder();

        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(oldChannelCode);
        rule.setNewChannelCode(newChannelCode);
        rule.setMatchExpression("testKey1 == 'testValue'");
        rule.setStatus("Y");
        rule.setPriority(1);
        rule.setRuleName("Test Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(rule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(newChannelCode, result);
    }

    @Test
    public void testGetNewChannelCode_StringContains() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String newChannelCode = "NEW_CODE";
        InstOrder instOrder = createTestInstOrder();
        
        // Add value with "test" substring for contains test
        instOrder.getExtension().put("testKey2", "This contains test substring");

        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(oldChannelCode);
        rule.setNewChannelCode(newChannelCode);
        rule.setMatchExpression("testKey2.contains('test')");
        rule.setStatus("Y");
        rule.setPriority(1);
        rule.setRuleName("Contains Test Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(rule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(newChannelCode, result);
    }
    
    @Test
    public void testGetNewChannelCode_StringStartsWith() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String newChannelCode = "NEW_CODE";
        InstOrder instOrder = createTestInstOrder();
        
        // Add value starting with "TEST_" for startsWith test
        instOrder.getExtension().put("testKey3", "TEST_PREFIX_VALUE");

        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(oldChannelCode);
        rule.setNewChannelCode(newChannelCode);
        rule.setMatchExpression("testKey3.startsWith('TEST_')");
        rule.setStatus("Y");
        rule.setPriority(1);
        rule.setRuleName("StartsWith Test Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(rule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(newChannelCode, result);
    }
    
    @Test
    public void testGetNewChannelCode_StringEndsWith() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String newChannelCode = "NEW_CODE";
        InstOrder instOrder = createTestInstOrder();
        
        // Add value ending with "_SUFFIX" for endsWith test
        instOrder.getExtension().put("testKey4", "VALUE_SUFFIX");

        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(oldChannelCode);
        rule.setNewChannelCode(newChannelCode);
        rule.setMatchExpression("testKey4.endsWith('_SUFFIX')");
        rule.setStatus("Y");
        rule.setPriority(1);
        rule.setRuleName("EndsWith Test Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(rule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(newChannelCode, result);
    }
    
    @Test
    public void testGetNewChannelCode_CombinedStringOperations() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String newChannelCode = "NEW_CODE";
        InstOrder instOrder = createTestInstOrder();
        
        // Add values for combined string operations test
        instOrder.getExtension().put("testKey2", "This contains test substring");
        instOrder.getExtension().put("testKey3", "TEST_PREFIX_VALUE");

        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(oldChannelCode);
        rule.setNewChannelCode(newChannelCode);
        rule.setMatchExpression("testKey2.contains('test') && testKey3.startsWith('TEST_')");
        rule.setStatus("Y");
        rule.setPriority(1);
        rule.setRuleName("Combined String Operations Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(rule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(newChannelCode, result);
    }
    
    @Test
    public void testGetNewChannelCode_MissingVariables() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String newChannelCode = "NEW_CODE";
        InstOrder instOrder = createTestInstOrder();

        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(oldChannelCode);
        rule.setNewChannelCode(newChannelCode);
        // Using a variable 'nonExistentKey' that doesn't exist in the binding
        rule.setMatchExpression("nonExistentKey == null || testKey1 == 'testValue'");
        rule.setStatus("Y");
        rule.setPriority(1);
        rule.setRuleName("Missing Variables Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(rule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(newChannelCode, result);
    }
    
    @Test
    public void testGetNewChannelCode_RulePriority() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String highPriorityChannelCode = "HIGH_PRIORITY_CODE";
        String lowPriorityChannelCode = "LOW_PRIORITY_CODE";
        InstOrder instOrder = createTestInstOrder();

        // Higher priority rule (should be selected)
        ChannelCodeMappingDO highPriorityRule = new ChannelCodeMappingDO();
        highPriorityRule.setOldChannelCode(oldChannelCode);
        highPriorityRule.setNewChannelCode(highPriorityChannelCode);
        highPriorityRule.setMatchExpression("orderType == 'I'");
        highPriorityRule.setStatus("Y");
        highPriorityRule.setPriority(10); // Higher priority
        highPriorityRule.setRuleName("High Priority Rule");

        // Lower priority rule
        ChannelCodeMappingDO lowPriorityRule = new ChannelCodeMappingDO();
        lowPriorityRule.setOldChannelCode(oldChannelCode);
        lowPriorityRule.setNewChannelCode(lowPriorityChannelCode);
        lowPriorityRule.setMatchExpression("payMode == 'BALANCE'");
        lowPriorityRule.setStatus("Y");
        lowPriorityRule.setPriority(5); // Lower priority
        lowPriorityRule.setRuleName("Low Priority Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(lowPriorityRule, highPriorityRule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(highPriorityChannelCode, result);
    }
    
    @Test
    public void testGetNewChannelCode_NullValues() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String newChannelCode = "NEW_CODE";
        InstOrder instOrder = createTestInstOrder();
        instOrder.setPayMode(null); // Set payMode to null

        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(oldChannelCode);
        rule.setNewChannelCode(newChannelCode);
        rule.setMatchExpression("payMode == null");
        rule.setStatus("Y");
        rule.setPriority(1);
        rule.setRuleName("Null Check Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(rule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(newChannelCode, result);
    }

    @Test
    public void testGetNewChannelCode_LogicalOperations() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String newChannelCode = "NEW_CODE";
        InstOrder instOrder = createTestInstOrder();

        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(oldChannelCode);
        rule.setNewChannelCode(newChannelCode);
        rule.setMatchExpression("orderType == 'I' || (payMode != 'CARD' && merchantId != null && merchantId.startsWith('TEST'))");
        rule.setStatus("Y");
        rule.setPriority(1);
        rule.setRuleName("Complex Logical Operations Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(rule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(newChannelCode, result);
    }

    @Test
    public void testGetNewChannelCode_NoMatchingRules() {
        // Given
        String oldChannelCode = "OLD_CODE";
        String newChannelCode = "NEW_CODE";
        InstOrder instOrder = createTestInstOrder();

        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(oldChannelCode);
        rule.setNewChannelCode(newChannelCode);
        rule.setMatchExpression("orderType == 'NOT_MATCHING_TYPE'");
        rule.setStatus("Y");
        rule.setPriority(1);
        rule.setRuleName("Non-Matching Rule");

        when(channelCodeMappingDAO.selectAllValidRules())
            .thenReturn(Arrays.asList(rule));

        // When
        String result = channelCodeMappingService.getNewChannelCode(oldChannelCode, instOrder);

        // Then
        assertEquals(oldChannelCode, result); // Should return original code when no rules match
    }

    private InstOrder createTestInstOrder() {
        InstOrder instOrder = new InstOrder();
        instOrder.setInstCode("TEST_INST");
        instOrder.setBizType(BizType.FUNDIN);
        instOrder.setPayMode(PayMode.BALANCE);
        instOrder.setPaymentCode("TEST_PAYMENT");
        instOrder.setMerchantId("TEST_MERCHANT");
        
        // Set extension fields
        HashMap<String, String> extension = new HashMap<>();
        extension.put("testKey1", "testValue");
        instOrder.setExtension(extension);
        
        return instOrder;
    }
}
