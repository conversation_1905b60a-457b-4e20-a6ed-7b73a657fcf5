package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.limit.LimitService;
import com.uaepay.cmf.domainservice.main.pattern.CardTokenService;
import com.uaepay.cmf.domainservice.main.process.*;
import com.uaepay.cmf.domainservice.main.repository.*;
import com.uaepay.cmf.domainservice.main.validate.ChannelResultValidator;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.common.Result;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.mockito.Mockito.*;

class ControlResultProcessorTest {
    @Mock
    NotifyBindCardInfoService notifyBindCardInfoService;
    @Mock
    AsyncVoidTransactionService asyncVoidTransactionService;
    @Mock
    Logger log;
    @Mock
    InstOrderRepository instOrderRepository;
    @Mock
    InstControlOrderRepository controlOrderRepository;
    @Mock
    InstOrderResultRepository instOrderResultRepository;
    @Mock
    InstControlOrderResultRepository controlOrderResultRepository;
    @Mock
    MonitorService monitorService;
    @Mock
    ChannelResultValidator channelResultValidator;
    @Mock
    CmfOrderRepository cmfOrderRepository;
    @Mock
    NotifyPaymentService notifyPaymentService;
    @Mock
    NotifyCashdeskService notifyCashdeskService;
    @Mock
    NotifyEscrowService notifyEscrowService;
    @Mock
    NotifyCounterService notifyCounterService;
    @Mock
    LimitService limitService;
    @Mock
    CardTokenService cardTokenService;
    @Mock
    DuplicateResultProcessService resultProcess;
    @Mock
    ResultCodeService resultCodeService;
    @Mock
    SaveBankReceiveService saveBankReceiveService;
    @Mock
    Map<CommunicateStatus, List<CommunicateStatus>> statusMapping;
    @Mock
    Map<InstOrderStatus, List<InstOrderStatus>> orderStatusMapping;
    @Mock
    Map<InstOrderProcessStatus, CommunicateStatus> targetCommStatusMap;
    @Mock
    Map<InstOrderResultStatus, InstOrderStatus> instOrderFinalStatusMap;
    @Mock
    Money ZERO_MONEY;
    @Mock
    BigDecimal ZERO;
    @Mock
    Money SPLIT_MIN_AMOUNT;
    @InjectMocks
    ControlResultProcessor controlResultProcessor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testValidateResult() {
        controlResultProcessor.validateResult(new InstControlOrder(), new InstControlOrderResult());
    }

    @Test
    void testConvert2InstResp() {
        InstControlOrderResult result = controlResultProcessor.convert2InstResp(new InstControlOrder(), new ChannelResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY));
        Assertions.assertEquals(new InstControlOrderResult(), result);
    }

    @Test
    void testStoreResult() {
        when(controlOrderRepository.updateCommunicateStatusByIdAndPreStatus(any(), any(), any())).thenReturn(1);
        when(controlOrderRepository.updateInstControlOrderStatus(any(), any())).thenReturn(true);
        InstControlOrderResult instControlOrderResult = new InstControlOrderResult();
        instControlOrderResult.setProcessStatus(InstOrderProcessStatus.SUCCESS);

        InstControlOrder newOrder = new InstControlOrder();
        newOrder.setCommunicateStatus(CommunicateStatus.SENT);

        Assertions.assertDoesNotThrow(()->controlResultProcessor.storeResult(newOrder, instControlOrderResult));
    }

    @Test
    void testLaterProcess() {
        when(asyncVoidTransactionService.processControlVoidTx(any(), any())).thenReturn(new Result());

        Assertions.assertDoesNotThrow(()->controlResultProcessor.laterProcess(new InstControlOrder(), new InstControlOrderResult()));
    }

    @Test
    void testProcess() {
        InstControlOrderResult result = controlResultProcessor.process(new InstControlOrder(), new ChannelResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY));
        Assertions.assertEquals(new InstControlOrderResult(), result);
    }

    @Test
    void testProcess2() {
        CommonResponse result = controlResultProcessor.process(new InstControlOrder(), new InstControlOrderResult());
        Assertions.assertTrue(Objects.nonNull(result));
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme