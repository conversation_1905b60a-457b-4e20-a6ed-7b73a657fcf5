package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.domainservice.channel.limit.LimitService;
import com.uaepay.cmf.domainservice.main.pattern.CardTokenService;
import com.uaepay.cmf.domainservice.main.process.*;
import com.uaepay.cmf.domainservice.main.repository.*;
import com.uaepay.cmf.domainservice.main.validate.ChannelResultValidator;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import com.uaepay.router.service.facade.domain.channel.ChannelApiParamVO;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;
@RunWith(SpringRunner.class)
class ControlResultProcessorSpringTest extends ApplicationTest {
    @Mock
    InstResultProcessor processor;
    @Mock
    NotifyBindCardInfoService notifyBindCardInfoService;
    @Mock
    AsyncVoidTransactionService asyncVoidTransactionService;
    @Mock
    Logger log;
    @Mock
    InstOrderRepository instOrderRepository;
    @Mock
    InstControlOrderRepository controlOrderRepository;
    @Mock
    InstOrderResultRepository instOrderResultRepository;
    @Mock
    InstControlOrderResultRepository controlOrderResultRepository;
    @Mock
    MonitorService monitorService;
    @Mock
    ChannelResultValidator channelResultValidator;
    @Mock
    CmfOrderRepository cmfOrderRepository;
    @Mock
    NotifyPaymentService notifyPaymentService;
    @Mock
    NotifyCashdeskService notifyCashdeskService;
    @Mock
    NotifyEscrowService notifyEscrowService;
    @Mock
    NotifyCounterService notifyCounterService;
    @Mock
    LimitService limitService;
    @Mock
    CardTokenService cardTokenService;
    @Mock
    DuplicateResultProcessService resultProcess;
    @Mock
    ResultCodeService resultCodeService;

    @Resource
    SaveBankReceiveService saveBankReceiveService;

    @Mock
    Map<CommunicateStatus, List<CommunicateStatus>> statusMapping;
    @Mock
    Map<InstOrderStatus, List<InstOrderStatus>> orderStatusMapping;
    @Mock
    Map<InstOrderProcessStatus, CommunicateStatus> targetCommStatusMap;
    @Mock
    Map<InstOrderResultStatus, InstOrderStatus> instOrderFinalStatusMap;
    @Mock
    Money ZERO_MONEY;
    @Mock
    BigDecimal ZERO;
    @Mock
    Money SPLIT_MIN_AMOUNT;
    @InjectMocks
    ControlResultProcessor controlResultProcessor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(controlResultProcessor,"saveBankReceiveService",saveBankReceiveService);
    }

    @Test
    void testValidateResult() {
        controlResultProcessor.validateResult(new InstControlOrder(), new InstControlOrderResult());
    }

    @Test
    void testConvert2InstResp() {
        InstControlOrderResult result = controlResultProcessor.convert2InstResp(new InstControlOrder(), new ChannelResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY));
        Assertions.assertEquals(new InstControlOrderResult(), result);
    }

    @Test
    void testStoreResult() {
        when(controlOrderRepository.updateCommunicateStatusByIdAndPreStatus(any(), any(), any())).thenReturn(1);
        when(controlOrderRepository.updateInstControlOrderStatus(any(), any())).thenReturn(true);
        ChannelHolder.set(getChannelVO());
        controlResultProcessor.storeResult(getInstControlOrder(), getInstControlOrderResult());
    }

    private ChannelVO getChannelVO(){
        ChannelVO vo = new ChannelVO();

        ChannelApiVO apiVO = new ChannelApiVO();
        apiVO.setApiType("AD");
        ChannelApiParamVO apiParamVO = new ChannelApiParamVO();
        apiParamVO.setParamName("instSeqNo");
        apiParamVO.setScene("response");
        apiVO.setParamList(Lists.newArrayList(apiParamVO));

        vo.setChannelApi(apiVO);
        return vo;
    }

    private InstControlOrderResult getInstControlOrderResult(){
        InstControlOrderResult result = new InstControlOrderResult();
        result.setInstOrderNo("T192022071800009021");
        result.setApiResultCode("channel.error");
        result.setApiResultSubCode("3ds.downgrade.rejected.cancel");
        result.setApiType(FundChannelApiType.ADVANCE_3DS2);
        result.setProcessStatus(InstOrderProcessStatus.AWAITING);
        result.setAmount(new Money("200.02","AED"));
        Map<String,String> map = new HashMap<>();
        map.put("authenticationTransactionId","3KodTxGizGUX3EVlAGc0");
        map.put("instSeqNo","6581162450096843603956");
        map.put("instOrderType","FUND");
        result.setExtension(map);

        return result;
    }

    private InstControlOrder getInstControlOrder(){
        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setInstOrderNo("T192022071800009021");
        instControlOrder.setPreInstOrderNo("T192022071800009020");
        instControlOrder.setStatus(InstOrderStatus.IN_PROCESS);
        return instControlOrder;
    }

    @Test
    void testLaterProcess() {

        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setApiType(FundChannelApiType.ADVANCE_3DS2);
        controlResultProcessor.laterProcess(instControlOrder, new InstControlOrderResult());
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme