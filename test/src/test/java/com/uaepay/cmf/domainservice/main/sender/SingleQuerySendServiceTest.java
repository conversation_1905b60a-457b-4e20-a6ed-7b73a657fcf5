package com.uaepay.cmf.domainservice.main.sender;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.domain.query.QueryRequest;
import com.uaepay.cmf.test.base.ApplicationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;



/**
 * <AUTHOR>
 * @date 29/11/2023 13:24
 */
public class SingleQuerySendServiceTest extends ApplicationTest {

    @Autowired
    SingleQuerySendService singleQuerySendService;

    @Test
    public void test(){
        InstOrder order = new InstOrder();
        order.setFundChannelCode("LEAN101");
        singleQuerySendService.send2Bank(new QueryRequest(),order);
    }

    @Test
    public void testOther(){
        InstOrder order = new InstOrder();
        order.setFundChannelCode("CKO101");
        singleQuerySendService.send2Bank(new QueryRequest(),order);
    }
}
