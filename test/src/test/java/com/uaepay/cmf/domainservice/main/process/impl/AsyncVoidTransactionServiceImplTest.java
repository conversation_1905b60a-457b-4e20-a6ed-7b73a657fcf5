package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.common.domain.OperationEnvironment;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.common.Result;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.AmqpTemplate;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;

class AsyncVoidTransactionServiceImplTest {

    @Mock
    ControlRequestFacade controlRequestFacade;
    @Mock
    AmqpAdmin amqpAdmin;
    @Mock
    AmqpTemplate amqpTemplate;
    @Mock
    Logger log;

    @InjectMocks
    AsyncVoidTransactionServiceImpl asyncVoidTransactionServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testInit() {
        asyncVoidTransactionServiceImpl.init();
    }

    @Test
    @DisplayName("只要不是撤销状态，结果返回nothing")
    void testProcessControlVoidTx() {
        InstControlOrder instControlOrder = new InstControlOrder();
        InstControlOrderResult instControlOrderResult = new InstControlOrderResult();
        instControlOrderResult.setStatus(InstOrderResultStatus.IN_PROCESS);
        Result result = asyncVoidTransactionServiceImpl.processControlVoidTx(instControlOrder,instControlOrderResult);
        Assert.assertEquals(result.getCode(),Result.Codes.NOTHING.getCode());

    }


    @Test
    @DisplayName("撤销状态，发送撤销指令消息，结果返回true")
    void testProcessControlVoidTX_success() {
        InstControlOrder instControlOrder = new InstControlOrder();
        instControlOrder.setInstOrderNo("T192022070700008763");
        instControlOrder.setPayMode(PayMode.TOKENPAY);
        instControlOrder.setInstCode("MYBK");
        InstControlOrderResult instControlOrderResult = new InstControlOrderResult();
        instControlOrderResult.setStatus(InstOrderResultStatus.CANCEL);
        Result result = asyncVoidTransactionServiceImpl.processControlVoidTx(instControlOrder,instControlOrderResult);
        Assert.assertEquals(result.getCode(),Result.Codes.SUCCESS.getCode());
    }

    @Test
    @DisplayName("撤销状态，发送撤销指令消息，结果返回false")
    void testProcessControlVoidTX_fail() {
        InstControlOrder instControlOrder = new InstControlOrder();
        InstControlOrderResult instControlOrderResult = new InstControlOrderResult();
        Mockito.doThrow(new AmqpException("ccc")).when(amqpTemplate).convertAndSend(anyString(),anyString(),anyString());
        Mockito.doThrow(new AmqpException("ccc")).when(controlRequestFacade).control(any(CmfControlRequest.class),any(OperationEnvironment.class));
        instControlOrderResult.setStatus(InstOrderResultStatus.CANCEL);
        Result result = asyncVoidTransactionServiceImpl.processControlVoidTx(instControlOrder,instControlOrderResult);
        Assert.assertEquals(result.getCode(),Result.Codes.FAILURE.getCode());
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme