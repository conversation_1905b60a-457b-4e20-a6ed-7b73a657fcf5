package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO;
import com.uaepay.cmf.common.core.dal.dataobject.ChannelCodeMappingDO;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Slf4j
public class ChannelCodeMappingServiceConcurrentTest {

    @InjectMocks
    private ChannelCodeMappingService channelCodeMappingService;

    @Mock
    private ChannelCodeMappingDAO channelCodeMappingDAO;

    private static final int THREAD_COUNT = 10;
    private static final int ITERATION_COUNT = 1000;
    private static final String OLD_CHANNEL_CODE = "OLD_CODE";
    private static final String NEW_CHANNEL_CODE = "NEW_CODE";

    private ExecutorService executorService;
    private CountDownLatch startLatch;
    private CountDownLatch endLatch;
    private CyclicBarrier barrier;
    private AtomicInteger successCount;
    private List<ChannelCodeMappingDO> testRules;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        executorService = Executors.newFixedThreadPool(THREAD_COUNT);
        startLatch = new CountDownLatch(1);
        endLatch = new CountDownLatch(THREAD_COUNT);
        barrier = new CyclicBarrier(THREAD_COUNT);
        successCount = new AtomicInteger(0);
        
        // 准备测试规则
        testRules = Arrays.asList(
            createRule("orderType == 'I'", 1),
            createRule("payMode == 'BALANCE'", 2),
            createRule("orderType == 'I' && payMode == 'BALANCE'", 3)
        );
        
        when(channelCodeMappingDAO.selectByOldChannelCode(anyString()))
            .thenReturn(testRules);
    }

    @Test
    public void testConcurrentExecution() throws InterruptedException {
        log.info("Starting concurrent execution test with {} threads, {} iterations each", THREAD_COUNT, ITERATION_COUNT);
        
        // 创建并启动测试线程
        for (int i = 0; i < THREAD_COUNT; i++) {
            final int threadIndex = i;
            executorService.submit(() -> runTest(threadIndex));
            log.info("Thread {} submitted", threadIndex);
        }

        // 记录开始时间
        long startTime = System.currentTimeMillis();
        log.info("All threads created, starting execution at {}", startTime);
        
        // 同时启动所有线程
        startLatch.countDown();
        log.info("Start signal sent to all threads");
        
        // 等待所有线程完成
        boolean completed = endLatch.await(30, TimeUnit.SECONDS);
        if (!completed) {
            log.warn("Timeout waiting for threads to complete!");
        }
        
        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        // 输出统计信息
        log.info("All threads completed. Total time: {}ms", totalTime);
        log.info("Average time per iteration: {}ms", (double) totalTime / ITERATION_COUNT);
        log.info("Average time per operation: {}ms", (double) totalTime / (THREAD_COUNT * ITERATION_COUNT));
        log.info("Success count: {} out of {}", successCount.get(), THREAD_COUNT * ITERATION_COUNT);
        log.info("Success rate: {}%", (double) successCount.get() / (THREAD_COUNT * ITERATION_COUNT) * 100);
        
        // 验证结果
        assertEquals(THREAD_COUNT * ITERATION_COUNT, successCount.get());
        
        executorService.shutdown();
        log.info("Test completed, executor service shut down");
    }

    private void runTest(int threadIndex) {
        try {
            log.info("Thread {} waiting at barrier", threadIndex);
            barrier.await();
            log.info("Thread {} passed barrier", threadIndex);
            
            startLatch.await();
            log.info("Thread {} started execution", threadIndex);

            int localSuccess = 0;
            long threadStartTime = System.currentTimeMillis();
            long totalMethodTime = 0; // 累计方法调用时间
            
            for (int i = 0; i < ITERATION_COUNT; i++) {
                InstOrder instOrder = createTestInstOrder();
                
                // 记录单次调用开始时间
                long methodStart = System.nanoTime();
                String result = channelCodeMappingService.getNewChannelCode(OLD_CHANNEL_CODE, instOrder);
                long methodTime = System.nanoTime() - methodStart;
                totalMethodTime += methodTime;
                
                if (NEW_CHANNEL_CODE.equals(result)) {
                    localSuccess++;
                    successCount.incrementAndGet();
                }
                
                // 每100次迭代输出一次进度和平均耗时
                if ((i + 1) % 100 == 0) {
                    log.info("Thread {} completed {} iterations, success so far: {}, avg method time: {}ms", 
                        threadIndex, i + 1, localSuccess, 
                        String.format("%.3f", (totalMethodTime / ((i + 1) * 1_000_000.0))));
                    Thread.sleep(ThreadLocalRandom.current().nextInt(1, 5));
                }
            }
            
            long threadEndTime = System.currentTimeMillis();
            double avgMethodTime = totalMethodTime / (ITERATION_COUNT * 1_000_000.0);
            log.info("Thread {} completed all iterations. Total time: {}ms, Success count: {}, Avg method time: {}ms", 
                threadIndex, threadEndTime - threadStartTime, localSuccess, String.format("%.3f", avgMethodTime));
            
        } catch (Exception e) {
            log.error("Thread {} failed: {}", threadIndex, e.getMessage(), e);
        } finally {
            endLatch.countDown();
            log.info("Thread {} finished and counted down", threadIndex);
        }
    }

    private ChannelCodeMappingDO createRule(String expression, int priority) {
        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(OLD_CHANNEL_CODE);
        rule.setNewChannelCode(NEW_CHANNEL_CODE);
        rule.setMatchExpression(expression);
        rule.setStatus("Y");
        rule.setPriority(priority);
        rule.setRuleName("Test Rule " + priority);
        return rule;
    }

    private InstOrder createTestInstOrder() {
        InstOrder instOrder = new InstOrder();
        instOrder.setInstCode("TEST_INST");
        instOrder.setBizType(BizType.FUNDIN);
        instOrder.setPayMode(PayMode.BALANCE);
        instOrder.setPaymentCode("TEST_PAYMENT");
        instOrder.setMerchantId("TEST_MERCHANT");
        
        HashMap<String, String> extension = new HashMap<>();
        extension.put("testKey", "testValue");
        instOrder.setExtension(extension);
        
        return instOrder;
    }

    @Test
    public void testScriptCacheEfficiency() throws InterruptedException {
        log.info("Starting script cache efficiency test");
        long startTime = System.currentTimeMillis();
        AtomicLong totalMethodTime = new AtomicLong(0);
        
        // 创建并启动测试线程
        for (int i = 0; i < THREAD_COUNT; i++) {
            final int threadIndex = i;
            executorService.submit(() -> {
                try {
                    log.info("Cache test thread {} waiting to start", threadIndex);
                    startLatch.await();
                    log.info("Cache test thread {} started", threadIndex);
                    
                    long threadStart = System.currentTimeMillis();
                    InstOrder instOrder = createTestInstOrder();
                    long threadMethodTime = 0;
                    
                    for (int j = 0; j < ITERATION_COUNT; j++) {
                        long methodStart = System.nanoTime();
                        channelCodeMappingService.getNewChannelCode(OLD_CHANNEL_CODE, instOrder);
                        long methodTime = System.nanoTime() - methodStart;
                        threadMethodTime += methodTime;
                        
                        if ((j + 1) % 200 == 0) {
                            log.info("Cache test thread {} completed {} iterations, avg method time: {}ms", 
                                threadIndex, j + 1, String.format("%.3f", (threadMethodTime / ((j + 1) * 1_000_000.0))));
                        }
                    }
                    
                    totalMethodTime.addAndGet(threadMethodTime);
                    long threadEnd = System.currentTimeMillis();
                    log.info("Cache test thread {} completed in {}ms, avg method time: {}ms", 
                        threadIndex, threadEnd - threadStart, 
                        String.format("%.3f", (threadMethodTime / (ITERATION_COUNT * 1_000_000.0))));
                    
                } catch (Exception e) {
                    log.error("Cache test thread {} failed: {}", threadIndex, e.getMessage(), e);
                } finally {
                    endLatch.countDown();
                    log.info("Cache test thread {} counted down", threadIndex);
                }
            });
        }

        startLatch.countDown();
        log.info("All cache test threads started");
        
        boolean completed = endLatch.await(30, TimeUnit.SECONDS);
        if (!completed) {
            log.warn("Timeout waiting for cache test threads!");
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgMethodTime = totalMethodTime.get() / (THREAD_COUNT * ITERATION_COUNT * 1_000_000.0);

        log.info("Cache efficiency test completed. Total time: {}ms", totalTime);
        log.info("Average method time: {}ms", String.format("%.3f", avgMethodTime));
        log.info("Average time per thread: {}ms", (double) totalTime / THREAD_COUNT);
        
        executorService.shutdown();
        log.info("Cache efficiency test finished, executor service shut down");
    }
} 