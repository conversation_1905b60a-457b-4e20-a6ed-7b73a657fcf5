package com.uaepay.cmf.fss.ext.integration.proxy.dubbo;

import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade;
import com.uaepay.cmf.test.service.mock.MockChannelFundFacade;
import com.uaepay.cmf.test.service.util.MockDubboUtil;
import com.uaepay.common.util.money.Money;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.concurrent.ConcurrentHashMap;

class DubboChannelFundRemoteProxyImplTest {
    @Mock
    Logger logger;

    @Mock
    ConcurrentHashMap<String, Object> serviceMap;
    @Mock
    Money ZERO_MONEY;
    @Mock
    BigDecimal ZERO;
    @Mock
    Money SPLIT_MIN_AMOUNT;
    @InjectMocks
    DubboChannelFundRemoteProxyImpl dubboChannelFundRemoteProxyImpl;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(dubboChannelFundRemoteProxyImpl,"applicationConfig", MockDubboUtil.getApplicationConfig("gp195_tpay-nyu",ChannelFundFacade.class,new MockChannelFundFacade()));
        ReflectionTestUtils.setField(dubboChannelFundRemoteProxyImpl,"consumerConfig", MockDubboUtil.getConsumerConfig());
    }

    @Test
    void testApplyFund() {
        ChannelRequest request = new ChannelRequest("NYU101", FundChannelApiType.DEBIT);
        request.setApiUrl("gp195_tpay-nyu");
        ChannelFundResult result = dubboChannelFundRemoteProxyImpl.applyFund(request, 5000L);
        ChannelFundResult result2 = dubboChannelFundRemoteProxyImpl.applyFund(request, null);
        Assertions.assertEquals(FundChannelApiType.DEBIT, result.getApiType());
        Assertions.assertEquals(FundChannelApiType.DEBIT, result2.getApiType());
    }

    @Test
    void testGetTarget() {
        ChannelFundFacade result = dubboChannelFundRemoteProxyImpl.getTarget("gp195_tpay-nyu");
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetTarget2() {
        ChannelFundFacade result2 = dubboChannelFundRemoteProxyImpl.getTarget("gp195_tpay-nyu",null);
        Assertions.assertNotNull(result2);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme