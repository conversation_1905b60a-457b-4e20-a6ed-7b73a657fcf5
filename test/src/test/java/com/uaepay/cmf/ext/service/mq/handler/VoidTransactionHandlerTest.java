package com.uaepay.cmf.ext.service.mq.handler;

import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.domain.CmfCommonResultCode;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.common.domain.Extension;
import com.uaepay.payment.common.v2.enums.PayMode;
import org.junit.jupiter.api.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;

import java.nio.charset.StandardCharsets;

import static com.uaepay.cmf.common.core.domain.enums.ExtensionKey.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

class VoidTransactionHandlerTest {
    @Mock
    ControlRequestFacade controlRequestFacade;

    @InjectMocks
    VoidTransactionHandler voidTransactionHandler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testHandleMessage() throws Exception {
        // 结果码返回null，抛出异常
        CmfControlResult controlResult = new CmfControlResult();
        when(controlRequestFacade.control(any(), any())).thenReturn(controlResult);
        Message message = new Message(JsonUtil.toJsonString(buildCmfControlRequest()).getBytes(StandardCharsets.UTF_8),new MessageProperties());
        Assertions.assertThrows(IllegalArgumentException.class,()->voidTransactionHandler.handleMessage(message));
        // 结果码返回非成功，抛出异常
        controlResult.setResultCode(CmfCommonResultCode.FAILED);
        when(controlRequestFacade.control(any(), any())).thenReturn(controlResult);
        Assertions.assertThrows(IllegalArgumentException.class,()->voidTransactionHandler.handleMessage(message));
        // 结果码返回成功，无异常
        controlResult.setResultCode(CmfCommonResultCode.SUCCESS);
        when(controlRequestFacade.control(any(), any())).thenReturn(controlResult);
        Assertions.assertDoesNotThrow(()->voidTransactionHandler.handleMessage(message));
    }

    private CmfControlRequest buildCmfControlRequest() {
        String preRequestNo="T192022071500009010";
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestNo(ControlRequestType.VOID_TRANSACTION.getCode() + preRequestNo);
        request.setPreRequestNo(preRequestNo);
        request.setRequestType(ControlRequestType.VOID_TRANSACTION);
        request.setPayMode(PayMode.QUICKPAY);
        request.setInstCode("MYBK");
        Extension extension = new Extension();
        extension.add("sourceOrder", "inst");
        extension.add(DBCR.getKey(), "CC");
        extension.add(MEMBER_ID.getKey(), "100000045402");
        extension.add(IS_3DS.getKey(), "Y");
        extension.add(COMPANY_OR_PERSONAL.getKey(), "C");
        request.setExtension(extension);
        return request;
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme