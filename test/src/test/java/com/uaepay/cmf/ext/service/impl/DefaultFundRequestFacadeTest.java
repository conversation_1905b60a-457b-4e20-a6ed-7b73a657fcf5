package com.uaepay.cmf.ext.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.uaepay.cmf.ext.service.process.ProcessService;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.OperationEnvironment;
import com.uaepay.common.util.DateUtil;
import com.uaepay.common.util.money.Money;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static com.uaepay.payment.common.v2.enums.PayMode.TOKENPAY;
import static com.uaepay.schema.cmf.enums.BizType.FUNDIN;
import static com.uaepay.schema.cmf.enums.BizType.FUNDOUT;
import static org.mockito.Mockito.*;

class DefaultFundRequestFacadeTest  extends ApplicationTest {
    @Resource
    ProcessService<CmfRequest, CmfFundResult> fundProcessService;
    @Mock
    Logger log;
    @InjectMocks
    DefaultFundRequestFacade defaultFundRequestFacade;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(defaultFundRequestFacade,"fundProcessService",fundProcessService);
    }

    @Test
    void testApply() {

        CmfRequest request = new CmfRequest();
        request.setPaymentSeqNo(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)+RandomUtil.randomNumbers(7));
        request.setProductCode("********");
        request.setSettlementId(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)+RandomUtil.randomNumbers(7));
        request.setPaymentCode("1001");
        request.setPayMode(TOKENPAY);
        request.setBizType(FUNDOUT);
        request.setInstCode("MYBK");
        request.setMemberId("************");
        request.setAmount(new Money("200.02","AED"));

        Extension extension = new Extension();
        extension.add("COMPANY_OR_PERSONAL","C");
        extension.add("DBCR","CC");
        extension.add("memberId","************");
        extension.add("GATE_ORDER_NO",LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)+RandomUtil.randomNumbers(7));
        extension.add("channelEnv","M");
        extension.add("CARD_ID","118400");
        extension.add("ACCESS_CHANNEL","IOS");
        extension.add("mobileNo","+971-*********");
        extension.add("validDate","P2953594");
        extension.add("expiredDate","P2953594");
        extension.add("accountName","Personal Basic Account");
        extension.add("payerFromPartnerId","************");
        extension.add("BIZ_PRODUCT_CODE","200101");
        extension.add("cardToken","*****************");

        extension.add("paymentOrderNo", LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)+RandomUtil.randomNumbers(7));
        extension.add("payeeId","************");
        extension.add("topayMerchantId","************");
        extension.add("topayMerchantName","regress6");

        request.setExtension(extension);
        CmfFundResult result = defaultFundRequestFacade.apply(request, new OperationEnvironment());
        Assertions.assertEquals(new CmfFundResult(CmfFundResultCode.SUCCESS), result);
    }


    @Test
    public void testFundOutApply(){
        // CmfRequest[requestBatchNo=<null>,paymentSeqNo=20220915FO001036545,settlementId=*****************,productCode=********,paymentCode=3001,payMode=BALANCE,bizType=FUNDOUT,instCode=BBME,memberId=************,amount=AED:1.00,bizTime=Thu Sep 15 06:15:45 GMT+04:00 2022,fundsChannel=<null>,operator=<null>,extension=com.uaepay.common.domain.Extension@6bd38bf4,memo=<null>],extension:GATE_ORDER_NO=311663208144012318,purpose=WithDraw,CARD_TYPE=DC,swiftCode=BBMEAEAD,beneficiaryAddress=********,fundoutGrade=0,cardToken=*****************,ACCOUNT_NAME=********,tradeVoucherNos=131663208141008562,iban=***********************,PT_ID=************,payChannel=14,BANK_CODE=BBME,BIZ_PRODUCT_CODE=230201,BANK_NAME=HSBC Bank Middle East,beneficiaryId=128909,paymentOrderNo=311663208144012318,payeeId=anonymous,orgiSettlementId=null,orgiFundinOrderNo=null,topayMerchantId=************,topayMerchantName=PayBy

        CmfRequest request = new CmfRequest();
        request.setPaymentSeqNo(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)+RandomUtil.randomNumbers(7));
        request.setProductCode("********");
        request.setSettlementId(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)+RandomUtil.randomNumbers(7));
        request.setPaymentCode("1001");
        request.setPayMode(TOKENPAY);
        request.setBizType(FUNDIN);
        request.setInstCode("MYBK");
        request.setMemberId("************");
        request.setAmount(new Money("200.02","AED"));

        Extension extension = new Extension();
        extension.add("COMPANY_OR_PERSONAL","C");
        extension.add("DBCR","CC");
        extension.add("memberId","************");
        extension.add("GATE_ORDER_NO",LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)+RandomUtil.randomNumbers(7));
        extension.add("channelEnv","M");
        extension.add("CARD_ID","118400");
        extension.add("ACCESS_CHANNEL","IOS");
        extension.add("mobileNo","+971-*********");
        extension.add("validDate","P2953594");
        extension.add("expiredDate","P2953594");
        extension.add("accountName","Personal Basic Account");
        extension.add("payerFromPartnerId","************");
        extension.add("BIZ_PRODUCT_CODE","200101");
        extension.add("cardToken","*****************");

        extension.add("paymentOrderNo", LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)+RandomUtil.randomNumbers(7));
        extension.add("payeeId","************");
        extension.add("topayMerchantId","************");
        extension.add("topayMerchantName","regress6");

        request.setExtension(extension);
        CmfFundResult result = defaultFundRequestFacade.apply(request, new OperationEnvironment());
    }

    @Test
    void testRefund() {
        when(fundProcessService.process(any())).thenReturn(new CmfFundResult(CmfFundResultCode.SUCCESS));

        CmfFundResult result = defaultFundRequestFacade.refund(new CmfRequest(), null);
        Assertions.assertEquals(new CmfFundResult(CmfFundResultCode.SUCCESS), result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme