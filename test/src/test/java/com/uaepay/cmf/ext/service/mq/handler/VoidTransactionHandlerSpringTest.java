package com.uaepay.cmf.ext.service.mq.handler;

import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.OperationEnvironment;
import com.uaepay.payment.common.v2.enums.PayMode;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static com.uaepay.cmf.common.core.domain.enums.ExtensionKey.*;
import static com.uaepay.cmf.common.core.domain.enums.ExtensionKey.COMPANY_OR_PERSONAL;

@Slf4j
@RunWith(SpringRunner.class)
class VoidTransactionHandlerSpringTest extends ApplicationTest {
    @Resource
    private ControlRequestFacade controlRequestFacade;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testHandleMessage() {

        CmfControlRequest cmfControlRequest = buildCmfControlRequest();

        //CmfControlResult vtResult = controlRequestFacade.control(cmfControlRequest, new OperationEnvironment());
        //log.info("VoidTransactionHandler.handleMessage response:{}", vtResult);
    }

    private CmfControlRequest buildCmfControlRequest() {
        String preRequestNo="T192022071500009010";
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestNo(ControlRequestType.VOID_TRANSACTION.getCode() + preRequestNo);
        request.setPreRequestNo(preRequestNo);
        request.setRequestType(ControlRequestType.VOID_TRANSACTION);
        request.setPayMode(PayMode.QUICKPAY);
        request.setInstCode("MYBK");
        Extension extension = new Extension();
        extension.add("sourceOrder", "inst");
        extension.add(DBCR.getKey(), "CC");
        extension.add(MEMBER_ID.getKey(), "100000045402");
        extension.add(IS_3DS.getKey(), "Y");
        extension.add(COMPANY_OR_PERSONAL.getKey(), "C");
        request.setExtension(extension);
        return request;
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme