package com.uaepay.cmf.ext.service.validator.control;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.common.util.DateUtil;
import com.uaepay.validate.exception.ValidationException;
import org.joda.time.DateTime;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static com.uaepay.cmf.common.core.domain.enums.CommunicateStatus.AWAITING;
import static com.uaepay.cmf.common.core.domain.enums.CommunicateStatus.SENT;
import static com.uaepay.cmf.common.core.domain.enums.InstOrderStatus.CANCEL;
import static com.uaepay.cmf.common.core.domain.enums.InstOrderStatus.IN_PROCESS;
import static com.uaepay.schema.cmf.enums.BizType.FUNDOUT;
import static org.mockito.Mockito.*;

class VoidTransactionValidatorTest {
    @Mock
    InstOrderRepository instOrderRepository;
    @InjectMocks
    VoidTransactionValidator voidTransactionValidator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    @DisplayName("请求不可为空")
    void testValidate() {
        Throwable throwable = Assertions.assertThrows(ValidationException.class,()->voidTransactionValidator.validate(null));
        Assertions.assertEquals("请求不可为空",throwable.getMessage());
    }

    @Test
    @DisplayName("机构订单不存在")
    void testValidate2() {
        when(instOrderRepository.loadByNo(anyString())).thenReturn(null);
        CmfControlRequest request = new CmfControlRequest();
        request.setPreRequestNo("123");
        Throwable throwable = Assertions.assertThrows(ValidationException.class,()->voidTransactionValidator.validate(request));
        Assertions.assertEquals("机构订单不存在",throwable.getMessage());
    }

    @Test
    @DisplayName("已超过发送时间，交易不可撤销")
    void testValidate3() {

        InstOrder instOrder = new InstOrder();
        instOrder.setBizType(FUNDOUT);
        instOrder.setGmtBookingSubmit(DateUtil.getBeforeDate());
        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);
        CmfControlRequest request = new CmfControlRequest();
        request.setPreRequestNo("123");
        Throwable throwable = Assertions.assertThrows(ValidationException.class,()->voidTransactionValidator.validate(request));
        Assertions.assertEquals("已超过发送时间，交易不可撤销",throwable.getMessage());
    }
    @Test
    @DisplayName("订单已打批，不可撤销")
    void testValidate4() {

        InstOrder instOrder = new InstOrder();
        instOrder.setBizType(FUNDOUT);
        instOrder.setGmtBookingSubmit(DateTime.now().plusSeconds(50).toDate());
        instOrder.setArchiveBatchId(123L);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);
        CmfControlRequest request = new CmfControlRequest();
        request.setPreRequestNo("123");
        Throwable throwable = Assertions.assertThrows(ValidationException.class,()->voidTransactionValidator.validate(request));
        Assertions.assertEquals("订单已打批，不可撤销",throwable.getMessage());
    }


    @Test
    @DisplayName("订单已发送渠道，不可撤销")
    void testValidate5() {

        InstOrder instOrder = new InstOrder();
        instOrder.setBizType(FUNDOUT);
        instOrder.setGmtBookingSubmit(DateTime.now().plusSeconds(50).toDate());
        instOrder.setCommunicateStatus(SENT);

        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);
        CmfControlRequest request = new CmfControlRequest();
        request.setPreRequestNo("123");
        Throwable throwable = Assertions.assertThrows(ValidationException.class,()->voidTransactionValidator.validate(request));
        Assertions.assertEquals("订单已发送渠道，不可撤销",throwable.getMessage());
    }


    @Test
    @DisplayName("将处理中机构单更新为cancelStatus，结果失败")
    void testValidate6() {

        InstOrder instOrder = new InstOrder();
        instOrder.setBizType(FUNDOUT);
        instOrder.setGmtBookingSubmit(DateTime.now().plusSeconds(50).toDate());
        instOrder.setCommunicateStatus(AWAITING);
        instOrder.setStatus(IN_PROCESS);

        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);
        CmfControlRequest request = new CmfControlRequest();
        request.setPreRequestNo("123");

        when(instOrderRepository.updateInstOrderStatus(any(), any())).thenReturn(false);
        Throwable throwable = Assertions.assertThrows(ValidationException.class,()->voidTransactionValidator.validate(request));
        Assertions.assertEquals("更新cancelStatus失败",throwable.getMessage());
    }


    @Test
    @DisplayName("机构订单不为cancel,不可撤销，请确认")
    void testValidate7() {

        InstOrder instOrder = new InstOrder();
        instOrder.setBizType(FUNDOUT);
        instOrder.setGmtBookingSubmit(DateTime.now().plusSeconds(50).toDate());
        instOrder.setCommunicateStatus(AWAITING);
        instOrder.setStatus(IN_PROCESS);

        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);
        CmfControlRequest request = new CmfControlRequest();
        request.setPreRequestNo("123");

        when(instOrderRepository.updateInstOrderStatus(any(), any())).thenReturn(true);
        Throwable throwable = Assertions.assertThrows(ValidationException.class,()->voidTransactionValidator.validate(request));
        Assertions.assertEquals("机构订单不可撤销，请确认",throwable.getMessage());
    }

    @Test
    @DisplayName("验证通过")
    void testValidate8() {

        InstOrder instOrder = new InstOrder();
        instOrder.setBizType(FUNDOUT);
        instOrder.setGmtBookingSubmit(DateTime.now().plusSeconds(50).toDate());
        instOrder.setCommunicateStatus(AWAITING);
        instOrder.setStatus(CANCEL);

        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);
        CmfControlRequest request = new CmfControlRequest();
        request.setPreRequestNo("123");

        when(instOrderRepository.updateInstOrderStatus(any(), any())).thenReturn(true);
        Assertions.assertDoesNotThrow(()->voidTransactionValidator.validate(request));
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme