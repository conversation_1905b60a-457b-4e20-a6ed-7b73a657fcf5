package com.uaepay.cmf.ext.service.impl;

import com.uaepay.cmf.ext.service.counter.DefaultInstOrderProcessFacade;
import com.uaepay.cmf.service.facade.domain.counter.InstOrderVO;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
public class DefaultInstOrderProcessFacadeTest extends ApplicationTest {

    @Resource
    private DefaultInstOrderProcessFacade defaultInstOrderProcessFacade;

    @Test
    public void getInstOrderTest(){
        InstOrderVO instOrder = defaultInstOrderProcessFacade.getInstOrder("TEST1908271600010000");
        Money amount = instOrder.getAmount();
        System.out.println(amount.multiply(amount.getCentFactor()).getAmount().longValue());
    }
}
