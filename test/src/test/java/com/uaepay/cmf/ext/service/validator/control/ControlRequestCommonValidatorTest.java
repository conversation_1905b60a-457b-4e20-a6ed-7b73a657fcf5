package com.uaepay.cmf.ext.service.validator.control;

import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.*;

class ControlRequestCommonValidatorTest {
    @Mock
    Logger logger;
    @Mock
    InstOrderRepository instOrderRepository;
    @Mock
    InstControlOrderRepository instControlOrderRepository;
    @InjectMocks
    ControlRequestCommonValidator controlRequestCommonValidator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("请求号不能为空")
    void testValidate() {
        when(instOrderRepository.loadByNo(anyString())).thenReturn(new InstOrder());
        when(instControlOrderRepository.loadByRequestNo(anyString())).thenReturn(new InstControlOrder());

        CmfControlRequest controlRequest = new CmfControlRequest();
        Throwable throwable = Assertions.assertThrows(Exception.class,()->controlRequestCommonValidator.validate(controlRequest));
        Assertions.assertEquals("请求号不能为空",throwable.getMessage());
    }

    @Test
    @DisplayName("重复请求-请求号重复")
    void testValidate2() {
        when(instOrderRepository.loadByNo(anyString())).thenReturn(new InstOrder());
        when(instControlOrderRepository.loadByRequestNo(anyString())).thenReturn(new InstControlOrder());

        CmfControlRequest controlRequest = new CmfControlRequest();
        controlRequest.setPreRequestNo("123");
        controlRequest.setRequestNo("456");
        Throwable throwable = Assertions.assertThrows(Exception.class,()->controlRequestCommonValidator.validate(controlRequest));
        Assertions.assertEquals("请求号重复",throwable.getMessage());
    }

    @Test
    @DisplayName("原请求号不存在")
    void testValidate3() {
        when(instOrderRepository.loadByNo(anyString())).thenReturn(null);
        when(instControlOrderRepository.loadByRequestNo(anyString())).thenReturn(null);

        CmfControlRequest controlRequest = new CmfControlRequest();
        controlRequest.setPreRequestNo("123");
        controlRequest.setRequestNo("456");
        controlRequest.setRequestType(ControlRequestType.ADVANCE);
        Throwable throwable = Assertions.assertThrows(Exception.class,()->controlRequestCommonValidator.validate(controlRequest));
        Assertions.assertEquals("原请求号不存在",throwable.getMessage());
    }
    @Test
    @DisplayName("原订单已经支付成功，请不要重复支付")
    void testValidate4() {

        InstOrder instOrder = new InstOrder();
        instOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);
        when(instControlOrderRepository.loadByRequestNo(anyString())).thenReturn(null);

        CmfControlRequest controlRequest = new CmfControlRequest();
        controlRequest.setPreRequestNo("123");
        controlRequest.setRequestNo("456");
        controlRequest.setRequestType(ControlRequestType.ADVANCE);

        Throwable throwable = Assertions.assertThrows(Exception.class,()->controlRequestCommonValidator.validate(controlRequest));
        Assertions.assertEquals("原订单已经支付成功，请不要重复支付",throwable.getMessage());
    }


    @Test
    @DisplayName("原订单已失败")
    void testValidate5() {

        InstOrder instOrder = new InstOrder();
        instOrder.setStatus(InstOrderStatus.FAILURE);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);
        when(instControlOrderRepository.loadByRequestNo(anyString())).thenReturn(null);

        CmfControlRequest controlRequest = new CmfControlRequest();
        controlRequest.setPreRequestNo("123");
        controlRequest.setRequestNo("456");
        controlRequest.setRequestType(ControlRequestType.ADVANCE);

        Throwable throwable = Assertions.assertThrows(Exception.class,()->controlRequestCommonValidator.validate(controlRequest));
        Assertions.assertEquals("原订单已失败",throwable.getMessage());
    }
    @Test
    @DisplayName("非重复请求-验证通过")
    void testValidate6() {

        InstOrder instOrder = new InstOrder();
        instOrder.setStatus(InstOrderStatus.IN_PROCESS);
        when(instOrderRepository.loadByNo(anyString())).thenReturn(instOrder);
        when(instControlOrderRepository.loadByRequestNo(anyString())).thenReturn(null);

        CmfControlRequest controlRequest = new CmfControlRequest();
        controlRequest.setPreRequestNo("123");
        controlRequest.setRequestNo("456");
        controlRequest.setRequestType(ControlRequestType.ADVANCE);

        Assertions.assertDoesNotThrow(()->controlRequestCommonValidator.validate(controlRequest));
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme