package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.basis.beacon.service.facade.domain.response.ObjectQueryResponse;
import com.uaepay.cmf.service.facade.domain.counter.InstOrderVO;
import com.uaepay.cmf.service.facade.domain.grc.QueryOrderAllInfoRequest;
import com.uaepay.cmf.service.facade.grc.GrcQueryFacade;
import com.uaepay.cmf.test.base.ApplicationTest;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 10/09/2024 14:25
 */
@DisplayName("grc query")
@RunWith(SpringRunner.class)
public class GrcQueryFacadeTestCase extends ApplicationTest {

    @Resource
    private GrcQueryFacade grcQueryFacade;


    @Test
    public void testQueryOrderAllInfo() {
        QueryOrderAllInfoRequest request = new QueryOrderAllInfoRequest();
        request.setPaymentSeqNo("20250501FI001278899");
        ObjectQueryResponse<InstOrderVO> queryResponse = grcQueryFacade.queryOrderAllInfo(request);
        System.out.println(queryResponse);
    }

}
