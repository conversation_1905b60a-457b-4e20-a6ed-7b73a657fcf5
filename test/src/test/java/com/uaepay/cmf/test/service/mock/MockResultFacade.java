package com.uaepay.cmf.test.service.mock;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.router.service.facade.ResultFacade;
import com.uaepay.router.service.facade.domain.ResultCodeRequest;
import com.uaepay.router.service.facade.domain.ResultCodeResult;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Service;

import java.util.function.Function;

/**
 * <p>结果Mock服务</p>
 *
 * <AUTHOR>
 * @date MockResultFacade.java v1.0  2020-09-08 11:55
 */
@Service
public class MockResultFacade implements ResultFacade {

    @Override
    public ResultCodeResult convertResult(ResultCodeRequest request) {
//        return getResultCode("S", "cmf.success").apply(request);
        if(request.getChannelCode().startsWith("VT")){
            if(!"VT".equals(request.getApiType())) {
                return getResultCode("C", "cmf.in_process").apply(request);
            }else{
                return getResultCode("S", "cmf.trade.reversed").apply(request);
            }
        }
        if (request.getChannelCode().startsWith("FS")){
            if ("PDB".equals(request.getApiType())){
                return getResultCode("H", "cmf.half_success").apply(request);
            }
        }
        if("not_exist".equals(request.getResultCode())){
            return getResultCode("N", "cmf.in_process").apply(request);
        }
        return getResultCode("S", "cmf.success").apply(request);
    }

    private static Function<ResultCodeRequest, ResultCodeResult> getResultCode(String resultCode, String unityResultCode) {
        return (req) -> {
            ResultCodeResult result = new ResultCodeResult();
            result.setApplyStatus(ApplyStatusEnum.SUCCESS);
            result.setResultStatus(StringUtils.isEmpty(resultCode) ? "" : resultCode);
            result.setUnityResultCode(StringUtils.isEmpty(unityResultCode) ? "" : unityResultCode);

            return result;
        };
    }

}
