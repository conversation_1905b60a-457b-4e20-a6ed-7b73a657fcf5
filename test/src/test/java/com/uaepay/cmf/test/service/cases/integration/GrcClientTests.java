package com.uaepay.cmf.test.service.cases.integration;

import com.uaepay.basis.beacon.service.facade.domain.response.ObjectQueryResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.fss.ext.integration.grc.GrcClient;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.grc.connect.api.facade.QueryFacade;
import com.uaepay.grc.connect.api.vo.domain.CheckInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>GrcClientTests</p>
 *
 * <AUTHOR>
 * @version GrcClientTests.java v1.0  2022/9/15 13:25
 */
public class GrcClientTests extends ApplicationTest {


    @Mock
    QueryFacade queryFacade;
    @Resource
    @InjectMocks
    GrcClient grcClient;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCheck() {

        CheckInfo checkInfo = new CheckInfo();
        checkInfo.setMemberId("100000045402");
        checkInfo.setPaymentOrderNo("202265100000045402");
        grcClient.checkResult(Collections.singletonList(checkInfo));

    }

    @Test
    void testEmpty() {
        grcClient.checkResult(Collections.emptyList());
    }

    @Test
    void testCheckFail() {

        ObjectQueryResponse<List<CheckInfo>> response = new ObjectQueryResponse<>();

        response.setApplyStatus(ApplyStatusEnum.FAIL);
        Mockito.when(queryFacade.check(Mockito.any())).thenReturn(response);
        grcClient.checkResult(Collections.singletonList(new CheckInfo()));

    }
}
