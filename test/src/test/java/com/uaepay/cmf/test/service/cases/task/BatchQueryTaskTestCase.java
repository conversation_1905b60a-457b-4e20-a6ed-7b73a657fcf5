package com.uaepay.cmf.test.service.cases.task;

import com.uaepay.cmf.common.core.domain.enums.InstOrderArchiveStatus;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.domainservice.batch.processor.impl.BatchResultProcessorImpl;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.test.base.ApplicationTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BatchQueryTaskTestCase.java v1.0  2020-09-14 13:59
 */
@RunWith(SpringRunner.class)
public class BatchQueryTaskTestCase extends ApplicationTest {

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private BatchResultProcessorImpl batchResultProcessor;

    @Test
    public void testComplete(){
        InstBatchOrder batchOrder = instOrderRepository.loadById(20201114000813235L);
        Assert.isTrue(batchOrder.getStatus() != InstOrderArchiveStatus.RECEIVED, "订单状态未结束");
        Assert.isTrue(instOrderRepository.isBatchComplete(batchOrder), "订单未完全处理完");
    }

    @Test
    public void testProcess(){
        ChannelFundBatchResult channelBatchResult = new ChannelFundBatchResult();
        InstBatchOrder batchResult = instOrderRepository.loadById(20201114000813235L);
        InstBatchResult result = batchResultProcessor.process(batchResult, channelBatchResult, false);
        System.out.println(result);
    }

}
