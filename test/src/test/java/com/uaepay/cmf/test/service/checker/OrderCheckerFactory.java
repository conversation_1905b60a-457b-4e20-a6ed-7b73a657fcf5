package com.uaepay.cmf.test.service.checker;

import org.apache.dubbo.config.annotation.Service;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date OrderCheckerFactory.java v1.0  2020-09-14 17:59
 */
@Service
public class OrderCheckerFactory {

//    public class Checker {
//
//        public Checker(long cancelVoucherNo) {
//            order = cancelOrderMapper.selectByPrimaryKey(cancelVoucherNo);
//        }
//
//        CancelOrder order;
//
//        public Checker exist() {
//            Assertions.assertNotNull(order);
//            return this;
//        }
//
//        public Checker request(CancelTradeRequest request, CancelTradeResponse response) {
//            Assertions.assertEquals(request.getCancelRequestNo(), order.getCancelRequestNo());
//            Assertions.assertEquals(request.getClientId(), order.getClientId());
//            Assertions.assertEquals(request.getCancelAmount(),
//                    order.getCancelAmount().setScale(0, RoundingMode.UNNECESSARY));
//            Assertions.assertEquals(request.getExtension(), order.getExtension());
//            Assertions.assertEquals(response.getUnityResultCode(), order.getUnityResultCode());
//            return this;
//        }
//
//        public Checker cancelSuccess() {
//            Assertions.assertEquals(CancelStatus.SUCCESS, order.getCancelStatus());
//            Assertions.assertNotNull(order.getTransactionId());
//            Assertions.assertNull(order.getUnityResultCode());
//            Assertions.assertNotNull(order.getFinishTime());
//            return this;
//        }
//
//        public Checker cancelFail() {
//            return cancelFail(null);
//        }
//
//        public Checker cancelFail(GpTradeUnityResultCode unityResultCode) {
//            Assertions.assertEquals(CancelStatus.FAIL, order.getCancelStatus());
//            Assertions.assertNull(order.getTransactionId());
//            Assertions.assertEquals(unityResultCode != null ? unityResultCode.getCode() : null,
//                    order.getUnityResultCode());
//            Assertions.assertNotNull(order.getFinishTime());
//            return this;
//        }
//
//        public Checker paying() {
//            Assertions.assertEquals(CancelStatus.PROCESSING, order.getCancelStatus());
//            Assertions.assertNull(order.getTransactionId());
//            Assertions.assertNull(order.getUnityResultCode());
//            Assertions.assertNull(order.getFinishTime());
//            return this;
//        }
//
//    }


}
