package com.uaepay.cmf.test.service.mock;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.member.service.base.model.CardInfo;
import com.uaepay.member.service.base.response.Response;
import com.uaepay.member.service.facade.IMemberCardFacade;
import com.uaepay.member.service.request.card.*;
import com.uaepay.member.service.response.card.*;
import org.apache.dubbo.config.annotation.Service;

/**
 * <p>会员卡管理Mock服务</p>
 *
 * <AUTHOR>
 * @date MockIBankAccountFacade.java v1.0  2020-09-14 13:20
 */
@Service
public class MockIMemberCardFacade implements IMemberCardFacade {
    @Override
    public BindCardResponse bindCard(BindCardRequest bindCardRequest) {
        return null;
    }

    @Override
    public BindCardResponse bindWithdrawalCard(BindWithdrawalCardRequest bindWithdrawalCardRequest) {
        return null;
    }

    @Override
    public QueryWithdrawalCardResponse queryWithdrawalCard(QueryWithdrawalCardRequest queryWithdrawalCardRequest) {
        return null;
    }

    @Override
    public BindCardResponse bindCardByScene(BindCardBySceneRequest bindCardBySceneRequest) {
        return null;
    }

    @Override
    public QueryCardByCardIdResponse queryCardByCardId(QueryCardByCardIdRequest queryCardByCardIdRequest) {
        /**
         * cardCategory=MERCHANT_CUSTOMER_CARD,cardType=DEBIT_CARD,cardAttribute=PERSONAL,isVerified=NO,isSigning=<null>,status=UNACTIVE,payAttribute=payment,certType=<null>,certNo=<null>,certNoTicket=<null>,phoneNo=<null>,phoneNoTicket=<null>,activateDate=<null>,channelCode=<null>,cardValidDate=P3147281,cardOrg=MASTERCARD,createTime=Thu Apr 07 10:35:48 GMT+04:00 2022,updateTime=Thu Apr 07 10:35:48 GMT+04:00 2022,countryCode=AE,outAccountToken=11193830633812414643
         */
        QueryCardByCardIdResponse response= new QueryCardByCardIdResponse();
        CardInfo cardInfo = new CardInfo();
        cardInfo.setMemberId("************");
        cardInfo.setCardNo("************0008");
        cardInfo.setCardNoTicket("P2912982-0008");
        cardInfo.setCardId(106664L);
        cardInfo.setBankCode("TESQ");
        cardInfo.setBankName("Test Bank");
        cardInfo.setBankAccountName("Test******");
        cardInfo.setBankAccountNameTicket("P2934475");
        cardInfo.setCardCategory("MERCHANT_CUSTOMER_CARD");
//        cardInfo.setCardType("");

        response.setCardInfo(cardInfo);
        response.setApplyStatus(ApplyStatusEnum.SUCCESS);
        return response;
    }

    @Override
    public QueryCardByCategoryResponse queryCardByCategory(QueryCardByCategoryRequest queryCardByCategoryRequest) {
        return null;
    }

    @Override
    public QueryCardByCategoryResponse queryCardByCategoryList(QueryCardByCategoryListRequest queryCardByCategoryListRequest) {
        return null;
    }

    @Override
    public QueryCardByConditionResponse queryCardByCondition(QueryCardByConditionRequest queryCardByConditionRequest) {
        return null;
    }

    @Override
    public QueryCardByConditionResponse queryCardByNo(QueryCardByNoRequest queryCardByNoRequest) {
        return null;
    }

    @Override
    public QueryCardByPageResponse queryCardByPage(QueryCardByPageRequest queryCardByPageRequest) {
        return null;
    }

    @Override
    public UpdateCardStatusResponse updateCardStatus(UpdateCardStatusRequest updateCardStatusRequest) {
        return null;
    }

    @Override
    public UnbindCardResponse unbindCard(UnbindCardRequest unbindCardRequest) {
        return null;
    }

    @Override
    public Response activeCard(ActiveCardRequest activeCardRequest) {
        return null;
    }

    @Override
    public Response batchUnbindCard(BatchUnbindCardRequest batchUnbindCardRequest) {
        return null;
    }

    @Override
    public QueryCardByCardTokenResponse queryCardByCardToken(QueryCardByCardTokenRequest queryCardByCardTokenRequest) {
        return null;
    }

    @Override
    public Response updateAddress(UpdateAddressRequest updateAddressRequest) {
        return null;
    }

    @Override
    public Response updateVerifyStatus(UpdateVerifyStatusRequest updateVerifyStatusRequest) {
        return null;
    }

    @Override
    public Response updateCardInfoById(UpdateCardInfoRequest updateCardInfoRequest) {
        return null;
    }

    @Override
    public RefreshCardArchiveDateResponse refreshMerchCustCardArchiveDate(RefreshCardArchiveDateRequest refreshCardArchiveDateRequest) {
        return null;
    }

    @Override
    public ChangeCardBindResponse changeBindCard(ChangeCardBindRequest changeCardBindRequest) {
        return null;
    }

    @Override
    public BindCardResponse bindCardWithMchtCustomer(BindCardWithMchtCustomerRequest bindCardWithMchtCustomerRequest) {
        return null;
    }

    @Override
    public QueryMchtCustomerCardsResponse queryMchtCustomerCards(QueryMchtCustomerCardsRequest queryMchtCustomerCardsRequest) {
        return null;
    }

    @Override
    public Response checkBindCard(CheckBindCardRequest checkBindCardRequest) {
        return null;
    }

    @Override
    public QueryCardNumByCategoryResponse queryCardNumByCategoryList(QueryCardNumByCategoryListRequest queryCardNumByCategoryListRequest) {
        return null;
    }

    @Override
    public Response addCardToken(AddCardTokenRequest addCardTokenRequest) {
        return null;
    }

    @Override
    public QueryCardTokenByCardIdResponse queryCardTokenByCardId(QueryCardTokenByCardIdRequest queryCardTokenByCardIdRequest) {
        return null;
    }
}
