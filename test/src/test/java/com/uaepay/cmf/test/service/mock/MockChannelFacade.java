package com.uaepay.cmf.test.service.mock;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.test.base.TestConstants;
import com.uaepay.common.util.DateUtil;
import com.uaepay.router.service.facade.ChannelFacade;
import com.uaepay.router.service.facade.domain.RouteRequest;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.router.service.facade.domain.channel.ChannelBatchArchiveVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.router.service.facade.domain.channel.InstCurrencyVO;
import com.uaepay.router.service.facade.domain.order.OrderFee;
import com.uaepay.router.service.facade.domain.request.*;
import com.uaepay.router.service.facade.domain.response.PkResponse;
import com.uaepay.router.service.facade.domain.response.QueryResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

/**
 * <p>渠道管理Mock服务</p>
 *
 * <AUTHOR>
 * @date MockChannelFacade.java v1.0  2020-09-08 11:55
 */
@Service
public class MockChannelFacade implements ChannelFacade, TestConstants {


    @Override
    public QueryResponse<OrderFee> calcFee(OrderFeeRequest request) {
        return null;
    }


    @Override
    public PkResponse<String> genOrderNo(GenOrderNoRequest genOrderNoRequest) {
        String orderNo = genRandomId(10);

        PkResponse<String> resp = new PkResponse<>();
        resp.setItem(orderNo);
        resp.setApplyStatus(ApplyStatusEnum.SUCCESS);
        return resp;
    }

    @Override
    public QueryResponse<ChannelVO> getChannelsByApiType(ApiTypeRequest request) {
        QueryResponse<ChannelVO> resp = new QueryResponse<>();
        ChannelVO vo = getRouteApiChannel().apply(request);
        resp.setResults(Arrays.asList(vo));

        return resp;
    }

    @Override
    public PkResponse<ChannelBatchArchiveVO> queryBatchArchive(ArchiveQueryRequest request) {
        PkResponse<ChannelBatchArchiveVO> resp = new PkResponse<>();
        resp.setApplyStatus(ApplyStatusEnum.SUCCESS);
        ChannelBatchArchiveVO item = new ChannelBatchArchiveVO();
        item.setApiCode(request.getApiCode());
        short max = 5;
        item.setMaxItem(max);
        item.setOrderNoRuleId(11L);
        resp.setItem(item);
        return resp;
    }

    @Override
    public QueryResponse<InstCurrencyVO> queryInstCurrency(QueryInstCurrencyRequest request) {
        QueryResponse<InstCurrencyVO> resp = new QueryResponse<InstCurrencyVO>();
        resp.setApplyStatus(ApplyStatusEnum.SUCCESS);
        List<InstCurrencyVO> icList = new ArrayList<>();
        InstCurrencyVO vo = new InstCurrencyVO();
        vo.setCurrencyList(Arrays.asList("USD","AED"));
        vo.setInstCodeList(Arrays.asList("HSBC", "NBAD"));
        icList.add(vo);
        InstCurrencyVO vo2 = new InstCurrencyVO();
        vo2.setCurrencyList(Arrays.asList("CNY","AED"));
        vo2.setInstCodeList(Arrays.asList("CN[A-Z]{4}"));
        icList.add(vo2);
        resp.setResults(icList);
        return resp;
    }

    @Override
    public QueryResponse<ChannelVO> getChannelByChannelType(GetChannelByChannelCodeRequest getChannelByChannelCodeRequest) {
        return null;
    }


    private static Function<ApiTypeRequest, ChannelVO> getRouteApiChannel() {
        return (req) -> {
            ChannelVO channel = new ChannelVO();
            channel.setChannelCode(req.getChannelCode());
            channel.setChannelName(MOCK_CHANNEL_NAME);

            ChannelApiVO api = new ChannelApiVO();
            String apiType = req.getApiTypes().contains("BFP") ? "BFP": req.getApiTypes().get(0);
            api.setApiCode(req.getChannelCode() + "-" + apiType);
            api.setChannelCode(req.getChannelCode());
            api.setApiType(apiType);
            api.setApiMethod(COMMUNICATION_TYPE_DUBBO);
            api.setApiUrl(MOCK_API_URL);
            api.setParamList(new ArrayList<>(0));

            channel.setChannelApi(api);
            channel.setStatus(CHANNEL_AVAILABLE);
            return channel;
        };
    }


    private static Function<RouteRequest, ChannelVO> getChannel() {
        return (req) -> {
            ChannelVO channel = new ChannelVO();
            String whiteChannel = ExtensionKey.WHITE_CHANNEL_CODE.key;
            if (req.getExtension() != null && req.getExtension().containsKey(whiteChannel)) {
                channel.setChannelCode(req.getExtension().get(whiteChannel));
                channel.setChannelName(req.getExtension().get(whiteChannel));
            } else {
                channel.setChannelCode(MOCK_CHANNEL);
                channel.setChannelName(MOCK_CHANNEL_NAME);
            }

            ChannelApiVO api = new ChannelApiVO();
            String apiType = getApiType(req.getRequestType());

            api.setApiCode(MOCK_CHANNEL + "-" + apiType);
            api.setApiType(apiType);
            api.setApiMethod(COMMUNICATION_TYPE_DUBBO);
            api.setApiUrl(null);

            channel.setChannelApi(api);
            channel.setStatus(CHANNEL_AVAILABLE);
            return channel;
        };
    }

    private static String getApiType(String code) {
        RequestType rt = RequestType.getByCode(code);
        if (rt != null) {
            switch (rt) {
                case FUND_OUT:
                    return "SP";
                case FUND_IN:
                    return "DB";
                case REFUND:
                    return "SR";
            }
        }
        return FundChannelApiType.getByCode(code).getCode();
    }

    protected String genRandomId(int length) {
        String id = StringUtils.rightPad(String.valueOf((int) (Math.random() * 1000000)), length, "0");
        return DateUtil.format(new Date(), "yyyyMMdd") + id;
    }

}
