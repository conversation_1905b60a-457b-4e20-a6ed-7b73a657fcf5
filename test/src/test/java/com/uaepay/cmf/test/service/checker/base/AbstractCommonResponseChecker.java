package com.uaepay.cmf.test.service.checker.base;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.base.CodeEnum;
import com.uaepay.basis.beacon.service.facade.enums.base.CodeMessageEnum;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import org.junit.jupiter.api.Assertions;

/**
 * 通用响应校验器抽象
 * 
 * @param <CK>
 *            校验器
 * @param <RST>
 *            要检验的类
 * <AUTHOR>
 */
public abstract class AbstractCommonResponseChecker<CK extends AbstractCommonResponseChecker,
    RST extends CommonResponse> {

    public AbstractCommonResponseChecker(RST response) {
        this.response = response;
    }

    protected RST response;

    public CK success() {
        result(ApplyStatusEnum.SUCCESS, null, null);
        return (CK)this;
    }

    public CK fail(CodeEnum code, String message) {
        result(ApplyStatusEnum.FAIL, code != null ? code.getCode() : null, message, null);
        return (CK)this;
    }

    public CK fail(CodeMessageEnum codeMessageEnum) {
        return fail(codeMessageEnum, codeMessageEnum != null ? codeMessageEnum.getMessage() : null);
    }

    public CK fail(CodeEnum codeEnum, CodeMessageEnum unityResultCodeEnum) {
        return fail(codeEnum, unityResultCodeEnum.getMessage(), unityResultCodeEnum);
    }

    public CK fail(CodeEnum code, String message, CodeEnum unityResultCode) {
        result(ApplyStatusEnum.FAIL, code != null ? code.getCode() : null, message,
            unityResultCode != null ? unityResultCode.getCode() : null);
        return (CK)this;
    }

    public CK error(CodeEnum code, String message) {
        result(ApplyStatusEnum.ERROR, code != null ? code.getCode() : null, message, null);
        return (CK)this;
    }

    public CK error(CodeMessageEnum codeMessageEnum) {
        return error(codeMessageEnum, codeMessageEnum.getMessage());
    }

    private RST result(ApplyStatusEnum applyStatus, String code, String message) {
        Assertions.assertEquals(applyStatus, response.getApplyStatus());
        Assertions.assertEquals(code, response.getCode());
        Assertions.assertEquals(message, response.getMessage());
        return response;
    }

    private RST result(ApplyStatusEnum applyStatus, String code, String message, String unityResultCode) {
        result(applyStatus, code, message);
        Assertions.assertEquals(unityResultCode, response.getUnityResultCode());
        return response;
    }

}
