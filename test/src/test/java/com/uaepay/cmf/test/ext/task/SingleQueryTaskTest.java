package com.uaepay.cmf.test.ext.task;

import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.fss.ext.resend.newtask.SingleQueryTask;
import com.uaepay.cmf.test.base.ApplicationTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date SingleQueryTaskTest6.java v1.0  2020-03-05 16:07
 */
@RunWith(SpringRunner.class)
public class SingleQueryTaskTest extends ApplicationTest {

    @Resource
    private SingleQueryTask singleQueryTask;

    @Test
    public void testSingleQuery(){
        InstOrderResult resp = singleQueryTask.executeSingle(20210917003177069L);
        System.out.println(resp);
    }


}
