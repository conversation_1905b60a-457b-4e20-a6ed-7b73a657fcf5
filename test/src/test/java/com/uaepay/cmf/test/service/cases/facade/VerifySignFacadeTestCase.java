package com.uaepay.cmf.test.service.cases.facade;

import com.alibaba.fastjson.JSONObject;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.service.facade.api.VerifySignFacade;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignResult;
import com.uaepay.cmf.test.base.ApplicationTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date VerifySignFacadeTestCase.java v1.0  2020-09-14 13:58
 */
@RunWith(SpringRunner.class)
public class VerifySignFacadeTestCase extends ApplicationTest {

    @Resource
    private VerifySignFacade verifySignFacade;

    @Test
    public void testVerifySign() {
        String returnUrl = "https://sim-mpaypage.test2pay.com/result?ft=7107c429-44ba-4101-af0c-69203278e4ec";
//        {"apiType":"VS","channelCode":"CREADIT10101","extension":{"NOTIFY_MSG":"{\n\t\"keyEnc\":\"028e42ed15c592d20024eaaa5d614b6513d748a127fa400a294519b20461c6debeefcf0401715b3d3b324371534e699dbfb09458c1bf0403c868c1cdc695ab5c5e724a1eabc688509ceceeb0d21b6e3c08e6c54510f658e769557d118f8c893d9c24a185bff8769585c2af717b5aeecaffa7bd65970c0783031bd15ca97850b903278f5fbd0f5242cc3cd22b3075e1bbdc33329a9f29b97d154f6f484590d9db3b0884ad1b420cd5377f6d591929fc836cb3391f9a8c819555233eb9a99303b7eb7154dc763b6480a92df597dc1972a72cb640baaa9f47d5c02ae20e1396bed9ccf41cc1224855b41da645b750b179d60b1852eb5e832338e3afe2870cc57128\",\n\t\"sign\":\"6ad613c4745c67460a65b301905cd0b3ae8757589221cb4a451b914328e2c8fc03fae0d3fd3129d0808a68de96f237dad4a3a85db8e769aa92d44e11ddea0f98cf1317ad6421cb56b755c93a6a2cf8958e9e4ea132a30eb68732a7f3c3c367fcd268347b02511a252190177c297f70bb50362d9fd9a8b5697119e54061cc30dbe409ea1df6dec1c82e5bfed897a78f5da1a4d74d59549ab79277f0af31735da080b53f95645c503cb629dcc85cee1381129c3daca0149e11883a39efb9503ec03b81b73266fc7abb4cc5e334bfcbf69a3d1100adc0bfea02c880c33996302f42d7fb13304c4814e01ae69354351dafbf11605d76d59008a0a47ae12e1e399368\",\n\t\"contextEnc\":\"2095eff841c8bd23b401d5f3bfe31632adc5f6e0c67d25b88ca2e9a826d92f8db29e6c0604a2519d2723ab9938955ed7c95545942c9c7bcade12b27b3c9317c0a7bda84b8ee782f3a69f522630336690\"\n\t\n}"},"requestNo":"20191121191101"}
        VerifySignRequest request = buildVerifyRequest();
        VerifySignResult resp = verifySignFacade.verify(request);
        Assert.isTrue(resp.getApplyStatus()== ApplyStatusEnum.SUCCESS, "申请失败");
        Assert.isTrue(resp.getExtMap()!=null &&returnUrl.equals(resp.getExtMap().get(ExtensionKey.PAGE_URL_FOR_SIGN.key)), "返回url不正确");
        // 测试重复请求
        VerifySignResult resp2 = verifySignFacade.verify(request);
        Assert.isTrue(resp2.getApplyStatus()== ApplyStatusEnum.SUCCESS, "resp2申请失败");
        Assert.isTrue(resp2.getExtMap()!=null &&returnUrl.equals(resp2.getExtMap().get(ExtensionKey.PAGE_URL_FOR_SIGN.key)), "resp2返回url不正确");
        System.out.println("resp:" + resp);
    }

    @Test
    public void testAuthVerify() {
        VerifySignRequest request = new VerifySignRequest();
        request.setApiType("AV");
        request.setAsync(true);
        request.setCallbackType("page");
        request.setChannelCode("CS104");
        request.setInstOrderNo("T192021121500005830");
        request.setRequestNo("cfdc284d5f6b48ed99099ff4ebcc7026");
        Map<String, String> map = new HashMap<>();
        map.put("MD", "T192021121500005830");
        map.put("PaReq", "eNpVUsluwjAQvfsrol4rxQtr0WCJtY1a9hRabiFxSVic4CQF/r52gNL6NG9m/DzvjcENlRDdmfBzJTgMRJp6a2FFQfNhMQnk4bPrvg/Jaxiv1tvVgD5wGLem4sDhW6g0iiWnNrEZ4BtEmkL5oSczDp5/aDtDzkqE0DrgK0SwF8rpcspInZjDSmXAlxwC6e0FH3vn1dlyhR/KeBevz1avMxoALmoI/DiXmTrzWl3fuwEEudrxMMuSBsbH49FODIftx3vApoIA3wcb5yZKtd5TFPA57W9Xsr+c9qdzd9N+856foiXLItftNQGbDgSBlwnOCKOU0YpFao1yrcFqgIs8Am9vxuDdMLUtYhOixV4yCBLzVuuCdP4v1FJypYT0b1puCIE4JbEUukdb+xtrDffJOy/GYD/Trs3S5OOrUxlONgtndAp27RJlj2vHaR0nTWN70VRwRtopViXVgjQqbMOGCF+Xiq/719G/f/EDwmqyfA==");
        map.put("PaRes", "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");
        request.setVerifyParam(map);
        VerifySignResult resp = verifySignFacade.verify(request);
        System.out.println("resp:" + resp);
    }

    @Test
    public void testDuplicateNotify() {

        VerifySignRequest request = new VerifySignRequest();
        request.setApiType("VS");
        request.setCallbackType("server");
        request.setChannelCode("CS101");
        request.setRequestNo("604560117487");
        request.setInstOrderNo("T192021021000002076");
        Map<String, String> map = new HashMap<>();
        map.put("respCode", "S");
        request.setVerifyParam(map);
        VerifySignResult resp = verifySignFacade.verify(request);
        System.out.println("resp:" + resp);

        resp = verifySignFacade.verify(request);
        System.out.println(resp);

    }

    @Test
    public void testVerifySignNonOrder(){

        VerifySignRequest request = new VerifySignRequest();
        request.setCallbackType("server");
        request.setAsync(false);
        request.setChannelCode("LEAN101");
        request.setApiType("VS");

        JSONObject verifyParam = new JSONObject();
        JSONObject payload = new JSONObject();
        payload.put("intent_id", "88f73a6d-f017-4fc6-85bf-2cc2804c2a4a");
        payload.put("status", "ACCEPTED_BY_BANK");
        payload.put("amount", 10.12);
        payload.put("currency", "AED");
        verifyParam.put("payload", payload);
        request.setVerifyParamStr(verifyParam.toJSONString());

        VerifySignResult result = verifySignFacade.verify(request);
        Assert.notNull(result.getInstOrderNo(), "instOrderNo is null");
        Assert.isTrue("cmf.order_inprocess".equals(result.getUnityResultCode()), "result code is wrong!");
    }

    private VerifySignRequest buildVerifyRequest() {
        VerifySignRequest request = new VerifySignRequest();
        request.setApiType("VS");
        request.setCallbackType("page");
        request.setChannelCode("CKO101");
        request.setInstOrderNo("T462023110100002650");
        request.setRequestNo("cfdc284d5f6b48ed99099ff4ebcc7026");
        Map<String, String> map = new HashMap<>();
        map.put("MD", "T192021121500005830");
        map.put("PaReq", "eNpVUsluwjAQvfsrol4rxQtr0WCJtY1a9hRabiFxSVic4CQF/r52gNL6NG9m/DzvjcENlRDdmfBzJTgMRJp6a2FFQfNhMQnk4bPrvg/Jaxiv1tvVgD5wGLem4sDhW6g0iiWnNrEZ4BtEmkL5oSczDp5/aDtDzkqE0DrgK0SwF8rpcspInZjDSmXAlxwC6e0FH3vn1dlyhR/KeBevz1avMxoALmoI/DiXmTrzWl3fuwEEudrxMMuSBsbH49FODIftx3vApoIA3wcb5yZKtd5TFPA57W9Xsr+c9qdzd9N+856foiXLItftNQGbDgSBlwnOCKOU0YpFao1yrcFqgIs8Am9vxuDdMLUtYhOixV4yCBLzVuuCdP4v1FJypYT0b1puCIE4JbEUukdb+xtrDffJOy/GYD/Trs3S5OOrUxlONgtndAp27RJlj2vHaR0nTWN70VRwRtopViXVgjQqbMOGCF+Xiq/719G/f/EDwmqyfA==");
        map.put("PaRes", "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");
        request.setVerifyParam(map);
        return request;
    }

}
