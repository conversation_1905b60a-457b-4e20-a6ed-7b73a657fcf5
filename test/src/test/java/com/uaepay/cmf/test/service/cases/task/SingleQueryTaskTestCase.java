package com.uaepay.cmf.test.service.cases.task;

import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.fss.ext.resend.newtask.SingleQueryTask;
import com.uaepay.cmf.test.base.ApplicationTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date SingleQueryTaskTestCase.java v1.0  2020-09-14 13:59
 */
@RunWith(SpringRunner.class)
public class SingleQueryTaskTestCase extends ApplicationTest {

    @Resource
    private SingleQueryTask singleQueryTask;

    @Test
    public void testSingleQuery(){
        InstOrderResult resp = singleQueryTask.executeSingle(20200305289909000L);
        System.out.println(resp);
    }



}
