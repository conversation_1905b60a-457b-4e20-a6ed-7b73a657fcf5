package com.uaepay.cmf.test.service.cases.processor;

import com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO;
import com.uaepay.cmf.common.core.dal.dataobject.ChannelCodeMappingDO;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.domainservice.main.process.impl.ChannelCodeMappingService;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Slf4j
@DisplayName("ChannelCodeMappingServiceIntegration")
@RunWith(SpringRunner.class)
public class ChannelCodeMappingServiceIntegrationTest extends ApplicationTest {

    @Autowired
    private ChannelCodeMappingService channelCodeMappingService;

    @Autowired
    private ChannelCodeMappingDAO channelCodeMappingDAO;

    private static final String OLD_CHANNEL_CODE = "TEST_OLD_CODE";
    private static final String NEW_CHANNEL_CODE = "TEST_NEW_CODE";

    @BeforeEach
    public void setup() {
        deleteByOldChannelCode(OLD_CHANNEL_CODE);
        // 创建基础测试规则
        createAndSaveRule("orderType == 'I'", 1, "Basic Rule");
    }

    @Test
    public void testBasicRuleMatching() {
        // Given
        InstOrder instOrder = createTestInstOrder(BizType.FUNDIN, PayMode.BALANCE);

        // When
        long startTime = System.nanoTime();
        String result = channelCodeMappingService.getNewChannelCode(OLD_CHANNEL_CODE, instOrder);
        long endTime = System.nanoTime();

        // Then
        assertEquals(NEW_CHANNEL_CODE, result);
        log.info("Basic rule matching took {}ms", (endTime - startTime) / 1_000_000.0);
    }

    @Test
    public void testMultipleRules() {
        // Given
        createAndSaveRule("payMode == 'BALANCE'", 2, "PayMode Rule");
        createAndSaveRule("orderType == 'I' && payMode == 'BALANCE'", 3, "Complex Rule");

        InstOrder instOrder = createTestInstOrder(BizType.FUNDIN, PayMode.BALANCE);

        // When
        long startTime = System.nanoTime();
        String result = channelCodeMappingService.getNewChannelCode(OLD_CHANNEL_CODE, instOrder);
        long endTime = System.nanoTime();

        // Then
        assertEquals(NEW_CHANNEL_CODE, result);
        log.info("Multiple rules matching took {}ms", (endTime - startTime) / 1_000_000.0);

        // Verify rules are properly stored
        List<ChannelCodeMappingDO> rules = channelCodeMappingDAO.selectByOldChannelCode(OLD_CHANNEL_CODE);
        assertEquals(3, rules.size());
    }

    @Test
    public void testExtensionFieldMatching() {
        // Given
        createAndSaveRule("testKey == 'testValue'", 2, "Extension Rule");
        InstOrder instOrder = createTestInstOrder(BizType.FUNDIN, PayMode.BALANCE);

        // When
        long startTime = System.nanoTime();
        String result = channelCodeMappingService.getNewChannelCode(OLD_CHANNEL_CODE, instOrder);
        long endTime = System.nanoTime();

        // Then
        assertEquals(NEW_CHANNEL_CODE, result);
        log.info("Extension field matching took {}ms", (endTime - startTime) / 1_000_000.0);
    }

    @Test
    public void testRulePriority() {
        deleteByOldChannelCode(OLD_CHANNEL_CODE);
        // Given
        createAndSaveRule("true", 10, "Low Priority Rule");
        createAndSaveRule("orderType == 'I'", 1, "High Priority Rule");

        InstOrder instOrder = createTestInstOrder(BizType.FUNDIN, PayMode.BALANCE);

        // When
        long startTime = System.nanoTime();
        String result = channelCodeMappingService.getNewChannelCode(OLD_CHANNEL_CODE, instOrder);
        long endTime = System.nanoTime();

        // Then
        assertEquals(NEW_CHANNEL_CODE, result);
        log.info("Priority based matching took {}ms", (endTime - startTime) / 1_000_000.0);

        // Verify rules are ordered by priority
        List<ChannelCodeMappingDO> rules = channelCodeMappingDAO.selectByOldChannelCode(OLD_CHANNEL_CODE);
        assertEquals(2, rules.size());
        assertEquals(10, rules.get(0).getPriority());
    }

    @Test
    public void testPerformanceWithLargeDataset() {
        // Given
        for (int i = 0; i < 100; i++) {
            createAndSaveRule("orderType == 'I' && merchantId == 'MERCHANT_" + i + "'",
                    i + 10, "Performance Test Rule " + i);
        }

        InstOrder instOrder = createTestInstOrder(BizType.FUNDIN, PayMode.BALANCE);

        // When
        long startTime = System.nanoTime();
        String result = channelCodeMappingService.getNewChannelCode(OLD_CHANNEL_CODE, instOrder);
        long endTime = System.nanoTime();

        // Then
        assertNotNull(result);
        log.info("Large dataset matching took {}ms", (endTime - startTime) / 1_000_000.0);
    }

    private void createAndSaveRule(String expression, int priority, String ruleName) {
        ChannelCodeMappingDO rule = new ChannelCodeMappingDO();
        rule.setOldChannelCode(OLD_CHANNEL_CODE);
        rule.setNewChannelCode(NEW_CHANNEL_CODE);
        rule.setMatchExpression(expression);
        rule.setStatus("Y");
        rule.setPriority(priority);
        rule.setRuleName(ruleName);
        channelCodeMappingDAO.insert(rule);
    }

    private InstOrder createTestInstOrder(BizType bizType, PayMode payMode) {
        InstOrder instOrder = new InstOrder();
        instOrder.setInstCode("TEST_INST");
        instOrder.setBizType(bizType);
        instOrder.setPayMode(payMode);
        instOrder.setPaymentCode("TEST_PAYMENT");
        instOrder.setMerchantId("TEST_MERCHANT");

        HashMap<String, String> extension = new HashMap<>();
        extension.put("testKey", "testValue");
        instOrder.setExtension(extension);

        return instOrder;
    }

    private Integer deleteByOldChannelCode(String oldChannelCode) {
        return channelCodeMappingDAO.deleteByOldChannelCode(oldChannelCode);
    }

} 