package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolderImpl;
import com.uaepay.cmf.fss.ext.integration.ues.UesClient;
import com.uaepay.cmf.service.facade.api.CacheFacade;
import com.uaepay.cmf.service.facade.domain.cache.*;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateRequest;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateResult;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.cmf.test.base.ShareDataBean;
import com.uaepay.cmf.test.service.builder.CardTokenRequestBuilder;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.core.annotation.Order;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CacheFacadeTestCase.java v1.0  2020-09-14 13:43
 */
@DisplayName("缓存测试")
@RunWith(SpringRunner.class)
public class CacheFacadeTestCase extends ApplicationTest {

    @Resource
    private CacheFacade cacheFacade;

    @Resource
    private UesClient uesClient;

    @Test
    @Order(1)
    @DisplayName("创建缓存记录")
    public void testCreateCardToken() {
        String csc = "193";
        CardTokenCreateRequest cardTokenRequest = CardTokenRequestBuilder.createCardTokenRequest(csc);
        CardTokenCreateResult result = cardTokenFacade.create(cardTokenRequest);
        Assert.isTrue(result.getApplyStatus() == ApplyStatusEnum.SUCCESS, "创建card token失败");
        ShareDataBean.addData(CARD_TOKEN, result.getCardTokenId());
        ShareDataBean.addData(CSC, csc);
    }

    @Test
    @Order(2)
    @DisplayName("查询缓存测试")
    public void testQueryCache() {
        String csc = cacheFacade.getCsc(ShareDataBean.getData(CARD_TOKEN));
        String cscPlain = uesClient.getDataByTicket(csc);
        Assert.isTrue(ShareDataBean.getData(CSC).equals(cscPlain), "csc获取失败");
    }


    @Test
    public void testChannelKey() {
        ImportChannelKeyRequest request = new ImportChannelKeyRequest();
        request.setKeyName("NI_CHANNEL_KEY_ZPK");
        request.setKeyValue("NI_CHANNEL_KEY_ZPK");
        request.setMemo("NI_CHANNEL_KEY_ZPK");
        CommonResponse response = cacheFacade.importChannelKey(request);
        System.out.println(response);

        response = cacheFacade.importChannelKey(request);
        System.out.println(response);

        QueryChannelKeyRequest keyRequest = new QueryChannelKeyRequest();
        keyRequest.setKey("NI_CHANNEL_KEY_ZPK");
        QueryChannelKeyResponse queryChannelKeyResponse = cacheFacade.queryChannelKey(keyRequest);
        System.out.println(queryChannelKeyResponse);

        keyRequest = new QueryChannelKeyRequest();
        keyRequest.setKey("NI_CHANNEL_KEY_ZPK1");
        queryChannelKeyResponse = cacheFacade.queryChannelKey(keyRequest);
        System.out.println(queryChannelKeyResponse);
    }

    @Resource
    private SysConfigurationHolderImpl sysConfigurationHolder;

    @Test
    public void testRefreshCache() {
        sysConfigurationHolder.refreshCache();
    }


    @Test
    public void testAsyncRefreshCache() throws InterruptedException {
        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 调用刷新
        sysConfigurationHolder.refreshCache();

        // 验证立即返回（异步）
        long timeSpent = System.currentTimeMillis() - startTime;
        Assert.isTrue(timeSpent < 100, "Should return immediately");

        // 等待异步操作完成
        Thread.sleep(10000);
    }

    @Test
    public void testQueryAccountBalance() {
        QueryAccountBalanceCacheRequest request = new QueryAccountBalanceCacheRequest();
        request.setAccountNo("*************");
        request.setClientId("router");
        QueryAccountBalanceCacheResponse response = cacheFacade.queryBalanceCache(request);
        System.out.println(response);
    }

}
