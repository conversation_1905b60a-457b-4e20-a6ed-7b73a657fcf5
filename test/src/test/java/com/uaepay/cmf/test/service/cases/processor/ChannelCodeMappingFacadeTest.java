package com.uaepay.cmf.test.service.cases.processor;


import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.domain.response.PageResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.service.facade.counter.ChannelCodeMappingFacade;

import com.uaepay.cmf.service.facade.domain.counter.channelcode.*;
import com.uaepay.cmf.test.base.ApplicationTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@DisplayName("ChannelCodeMappingServiceIntegration")
@RunWith(SpringRunner.class)
public class ChannelCodeMappingFacadeTest extends ApplicationTest {

    @Autowired
    private ChannelCodeMappingFacade channelCodeMappingFacade;

    private static final String OLD_CHANNEL_CODE = "TEST_OLD_CODE_FACADE";
    private static final String NEW_CHANNEL_CODE = "TEST_NEW_CODE_FACADE";

    @Test
    public void test_create() {

        // 创建请求
        CreateChannelCodeMappingRequest request = new CreateChannelCodeMappingRequest();
        request.setOldChannelCode(OLD_CHANNEL_CODE);
        request.setNewChannelCode(NEW_CHANNEL_CODE);
        request.setMatchExpression("orderType == 'I'");
        request.setPriority(1);
        request.setStatus("Y");
        request.setRuleName("Test Rule");

        // 执行测试
        CommonResponse response = channelCodeMappingFacade.create(request);

        // 验证结果
        assertTrue(ApplyStatusEnum.SUCCESS.equals(response.getApplyStatus()));
        log.info("Created mapping successfully");
    }

    @Test
    public void test_update() {



        // 更新请求
        UpdateChannelCodeMappingRequest updateRequest = new UpdateChannelCodeMappingRequest();
        updateRequest.setId(129L);
        updateRequest.setNewChannelCode("UPDATED_CODE");
        updateRequest.setPriority(2);

        // 执行测试
        CommonResponse updateResponse = channelCodeMappingFacade.update(updateRequest);

        // 验证结果
        assertTrue(ApplyStatusEnum.SUCCESS.equals(updateResponse.getApplyStatus()));

    }

    @Test
    public void test_updateStatus() {
        UpdateChannelCodeMappingStatusRequest request = new UpdateChannelCodeMappingStatusRequest();
        request.setId(129L);
        request.setStatus("N");
        // 执行测试
        CommonResponse updateResponse = channelCodeMappingFacade.updateStatus(request);

        // 验证结果
        assertTrue(ApplyStatusEnum.SUCCESS.equals(updateResponse.getApplyStatus()));
    }

    @Test
    public void test_pageQuery() {

        // 创建查询请求
        ChannelCodeMappingPageRequest request = new ChannelCodeMappingPageRequest();
        request.setOldChannelCode(OLD_CHANNEL_CODE);
        request.setPageSize(10);
        request.setCurrentPage(1);

        // 执行测试
        PageResponse<ChannelCodeMappingPageResponse> pageResponse = channelCodeMappingFacade.pageQuery(request);

        // 验证结果
        assertTrue(ApplyStatusEnum.SUCCESS.equals(pageResponse.getApplyStatus()));
        assertNotNull(pageResponse.getDataList());
        assertFalse(pageResponse.getDataList().isEmpty());
        log.info("Found {} records", pageResponse.getDataList().size());
    }

}