package com.uaepay.cmf.test.service.cases.function;

import com.uaepay.cmf.common.core.domain.enums.UesDataTypeEnum;
import com.uaepay.cmf.fss.ext.integration.ues.UesClient;
import com.uaepay.cmf.fss.ext.integration.util.OrderUtil;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.cmf.test.base.ShareDataBean;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.core.annotation.Order;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date UesTestCase.java v1.0  2020-09-14 22:46
 */
@Slf4j
@RunWith(SpringRunner.class)
public class UesTestCase extends ApplicationTest {

    @Resource
    private UesClient uesClient;

//    @Test
//    public void testEncrypt(){
//        String res = uesClient.saveData("123", null);
//        System.out.println(res);
//    }

    public static void main(String[] args) {
        System.out.println(OrderUtil.isEncryptedData("P12345"));
        System.out.println(OrderUtil.isEncryptedData("C12345"));
        System.out.println(OrderUtil.isEncryptedData("P12345,C12345"));
    }

    @Test
    @Order(1)
    public void test(){
        ShareDataBean.addData("token", "");
    }

    @Test
    @Order(2)
    public void test2(){
        System.out.println("tttttt:" + ShareDataBean.getData("token"));
    }

    @Test
    public void testDecryptShort(){
        String result = uesClient.getDataByTicket("P3132617");
        Assert.isTrue("dodododo".equals(result), "解密结果不相等:"+result);
        String resp = uesClient.saveData("dodododo", UesDataTypeEnum.NAME);
        Assert.isTrue(resp.equals("P3132617"), "加密结果不等");
        log.info("testDecryptShort.testPassed");

    }

    @Test
    public void testDecryptLong(){
        String result = uesClient.getDataByTicket("P3132619,P3132619");
        Assert.isTrue("dodododododododododododododododododododododododododododododododododododododododododododododododo".equals(result), "解密结果不相等:"+result);
        log.info("testDecryptLong.testPassed");
    }


}
