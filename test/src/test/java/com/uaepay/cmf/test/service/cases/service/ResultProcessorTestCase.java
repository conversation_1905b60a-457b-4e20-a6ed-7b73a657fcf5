package com.uaepay.cmf.test.service.cases.service;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ResultProcessorTestCase.java v1.0  11/17/20 4:38 PM
 */
@RunWith(SpringRunner.class)
public class ResultProcessorTestCase extends ApplicationTest {

    @Resource
    private InstResultProcessor instResultProcessor;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Test
    public void testResult() {
        InstOrder instOrder = instOrderRepository.loadByNo("T102020111700016698");
        ChannelFundResult cs = buildResult();
        InstOrderResult resp = instResultProcessor.process(instOrder, cs);
        System.out.println(resp);
    }

    private ChannelFundResult buildResult() {
        //apiType=BATCH_QUERY, resultCode=null, instOrderNo=T102020111700016698, instReturnOrderNo=null, apiResultCode=in_process, apiResultSubCode=Success, apiResultMessage=null, apiResultSubMessage=null, extension=null), realAmount=AED:0.02, instSettleTime=null, processTime=null, instUrl=null, fundChannelCode=FAB212)
        ChannelFundResult result = new ChannelFundResult();
        result.setInstOrderNo("T102020111700016698");
        result.setApiResultCode("in_process");
        result.setApiResultSubCode("Success");
        result.setApiType(FundChannelApiType.BATCH_QUERY);
        result.setRealAmount(new Money("0.02", "AED"));
        result.setFundChannelCode("FAB212");
        return result;
    }
}
