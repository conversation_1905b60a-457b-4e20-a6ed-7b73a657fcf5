package com.uaepay.cmf.test.service.builder;

import com.uaepay.cmf.test.base.TestConstants;
import com.uaepay.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BaseBuilder.java v1.0  2020-09-14 23:16
 */
public abstract class BaseBuilder implements TestConstants {

    protected static String genRandomId(int length) {
        String id = StringUtils.rightPad(String.valueOf((int) (Math.random() * 1000000)), length, "0");
        return DateUtil.format(new Date(), "yyyyMMdd") + id;
    }
}
