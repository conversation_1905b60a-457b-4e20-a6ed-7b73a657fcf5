package com.uaepay.cmf.test.service.cases.task;

import com.uaepay.cmf.domainservice.main.process.ChannelBalanceService;
import com.uaepay.cmf.fss.ext.resend.newtask.BatchSendTask;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BatchSendTaskTestCase.java v1.0  2020-09-14 13:59
 */
@RunWith(SpringRunner.class)
public class BatchSendTaskTestCase extends ApplicationTest {

    @Resource
    private BatchSendTask batchSendTask;

    @Resource
    private ChannelBalanceService balanceService;

    @Test
    public void testLoadTask(){
        List<String> tasks = batchSendTask.loadTask(10);
        System.out.println(tasks);
    }

    @Test
    public void executeTask(){
        batchSendTask.executeTask("FAB221");
    }

    @Test
    public void testGatherBalance(){
        Money result = balanceService.gatherAwaitingAmount("FAB221");
        System.out.println(result);
    }

}
