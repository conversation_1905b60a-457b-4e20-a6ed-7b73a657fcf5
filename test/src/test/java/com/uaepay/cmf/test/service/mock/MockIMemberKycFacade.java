package com.uaepay.cmf.test.service.mock;

import com.uaepay.member.service.base.response.Response;
import com.uaepay.member.service.facade.IMemberKycFacade;
import com.uaepay.member.service.request.*;
import com.uaepay.member.service.request.correctinfo.CorrectKycInfoRequest;
import com.uaepay.member.service.request.correctinfo.QueryCorrectKycInfoHisRequest;
import com.uaepay.member.service.request.member.QueryKycInfoRequest;
import com.uaepay.member.service.request.svacard.CheckVerifyRefAuthRequest;
import com.uaepay.member.service.request.svacard.SetVerifyRefAuthRequest;
import com.uaepay.member.service.response.*;
import com.uaepay.member.service.response.correctinfo.QueryCorrectKycInfoHisResponse;
import com.uaepay.member.service.response.svacard.CheckVerifyRefAuthResponse;
import org.apache.dubbo.config.annotation.Service;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date MockIMemberKycFacade.java v1.0
 */
@Service
public class MockIMemberKycFacade implements IMemberKycFacade {
    @Override
    public QueryMemberKycResponse queryMemberKycInfo(QueryMemberKycRequest queryMemberKycRequest) {
        return null;
    }

    @Override
    public QueryKycInfoResponse queryKycInfo(QueryKycInfoRequest queryKycInfoRequest) {
        return null;
    }

    @Override
    public QueryMemberKycResponse queryMemberKycInfoBaseNoCheck(QueryMemberKycRequest queryMemberKycRequest) {
        return null;
    }

    @Override
    public SaveMemberKycAttachResponse saveMemberKycAttach(SaveMemberKycAttachRequest saveMemberKycAttachRequest) {
        return null;
    }

    @Override
    public SaveMemberContractSignInfoResponse saveMemberContractSignInfo(SaveMemberContractSignInfoRequest saveMemberContractSignInfoRequest) {
        return null;
    }

    @Override
    public VerifyMemberKycResponse verifyMemberKyc(VerifyMemberKycRequest verifyMemberKycRequest) {
        return null;
    }

    @Override
    public Response correctKycInfo(CorrectKycInfoRequest correctKycInfoRequest) {
        return null;
    }

    @Override
    public QueryCorrectKycInfoHisResponse queryCorrectKycInfoHis(QueryCorrectKycInfoHisRequest queryCorrectKycInfoHisRequest) {
        return null;
    }

    @Override
    public Response setVerifyRefAuth(SetVerifyRefAuthRequest setVerifyRefAuthRequest) {
        return null;
    }

    @Override
    public CheckVerifyRefAuthResponse checkVerifyRefAuth(CheckVerifyRefAuthRequest checkVerifyRefAuthRequest) {
        return null;
    }

    @Override
    public QueryMemberKycLevelResponse queryMemberKycLevel(QueryMemberKycLevelRequest queryMemberKycLevelRequest) {
        return null;
    }

}
