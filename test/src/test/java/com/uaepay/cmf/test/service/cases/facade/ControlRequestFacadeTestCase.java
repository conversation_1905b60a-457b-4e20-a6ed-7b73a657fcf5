package com.uaepay.cmf.test.service.cases.facade;

import cn.hutool.core.util.RandomUtil;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceRequest;
import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceResult;
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthRequest;
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthResponse;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateRequest;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateResult;
import com.uaepay.cmf.service.facade.domain.card.RetrieveCardMetadataRequest;
import com.uaepay.cmf.service.facade.domain.card.RetrieveCardMetadataResponse;
import com.uaepay.cmf.service.facade.domain.clear.ClearInfo;
import com.uaepay.cmf.service.facade.domain.clear.ClearInfoQueryRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.cmf.service.facade.domain.control.CmfFileRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfFileResponse;
import com.uaepay.cmf.service.facade.domain.control.config.ChannelConfigQueryRequest;
import com.uaepay.cmf.service.facade.domain.control.config.ChannelConfigQueryResponse;
import com.uaepay.cmf.service.facade.domain.control.psp.PspReversalRequest;
import com.uaepay.cmf.service.facade.domain.control.psp.PspReversalResponse;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import com.uaepay.cmf.service.facade.result.ListQueryResult;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.cmf.test.service.builder.CardTokenRequestBuilder;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.OperationEnvironment;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ControlRequestFacadeTestCase.java v1.0  2020-09-14 13:56
 */
@RunWith(SpringRunner.class)
public class ControlRequestFacadeTestCase extends ApplicationTest {

    @Resource
    private ControlRequestFacade controlRequestFacade;


    @Test
    public void testEnbdIbanValidation(){
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestType(ControlRequestType.IBAN_DETAIL_QUERY);
        request.setInstCode("ENBD");
        request.setRequestNo(UUID.randomUUID().toString().replace("-", ""));
        request.setAmount(Money.zero("AED"));
        request.setPayMode(PayMode.QUICKPAY);
        Extension extension = new Extension();
        extension.add("iban", "***********************");
        request.setExtension(extension);

        CmfControlResult result = controlRequestFacade.control(request, new OperationEnvironment());
        System.out.println(result);
    }

    @Test
    public void testControl() {
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestType(ControlRequestType.ADVANCE);
        request.setInstCode("ICBC");
        request.setRequestNo(genRandomId(10));
        request.setPreRequestNo("TEST1909051800147415");
        Extension extension = new Extension();
        extension.add("verificationCode", "123456");
        extension.add("sourceOrder", "inst");
        request.setExtension(extension);


        OperationEnvironment environment = new OperationEnvironment();
        controlRequestFacade.control(request, environment);
        System.out.println();
    }

    @Test
    public void testAdvance(){
        CmfAdvanceRequest request = new CmfAdvanceRequest();
        request.setClientId(CLIENT_ID);
        request.setInstOrderToken("abc");
        CmfAdvanceResult resp = controlRequestFacade.advance(request);
        System.out.println(resp);
        Assert.isTrue(resp.getApplyStatus() == ApplyStatusEnum.FAIL, "申请状态错误");

        CmfRequest request1 = FundRequestFacadeTestCase.buildRequest("20220329FI00000008", MOCK_CHANNEL_3DS2).apply("");
        CmfFundResult resp1 = fundRequestFacade.apply(request1, new OperationEnvironment());
        System.out.println(resp1);
        Assert.isTrue(resp1.getResultCode() == CmfFundResultCode.REQUEST_SUCCESS && StringUtils.isNotEmpty(resp1.getExtension().getValue("instOrderToken")), "申请状态错误");
        request.setInstOrderToken(resp1.getExtension().getValue("instOrderToken"));
        resp = controlRequestFacade.advance(request);
        System.out.println(resp);
        Assert.isTrue(resp.getApplyStatus() == ApplyStatusEnum.SUCCESS, "申请状态不为成功");
    }

    @Test
    public void testVoidTransaction() {
        CmfControlRequest request = buildRequest(ControlRequestType.VOID_TRANSACTION, "2020-11-01", "CS101");
        request.setPreRequestNo("T192021011300001362");

        CmfControlResult result = controlRequestFacade.control(request, new OperationEnvironment());
        System.out.println(result);
    }

    @Test
    public void testPspReversal() {
        PspReversalRequest request = new PspReversalRequest();
        request.setRequestNo("T192021011300001362");
        request.setRequestType(ControlRequestType.CONTROL_VOID_TRANSACTION);
        request.setAmount(new Money("1.2", "AED"));
        request.setInstCode("TAXIPSP");
        request.setPayMode(PayMode.QUICKPAY);
        HashMap<String, String> ext = new HashMap<>();
        ext.put("abc", "123");
        request.setExtension(ext);
        PspReversalResponse result = controlRequestFacade.pspReversal(request);
        System.out.println(result);
        result = controlRequestFacade.pspReversal(request);
        System.out.println(result);
    }

    @Test
    public void testControlVoid() {
        CmfControlRequest request = buildRequest(ControlRequestType.CONTROL_VOID_TRANSACTION, "2022-05-01", "");
        request.setPreRequestNo("11192021011300001362");

        CmfControlResult result = controlRequestFacade.control(request, new OperationEnvironment());
        System.out.println(result);
    }


    @Test
    public void testAuth() {
        CardTokenCreateRequest cardTokenRequest = CardTokenRequestBuilder.createCardTokenRequest("123");
        CardTokenCreateResult result = cardTokenFacade.create(cardTokenRequest);
        CmfAuthRequest request = buildAuthRequest(result.getCardTokenId());
        CmfAuthResponse resp = controlRequestFacade.auth(request);
        System.out.println(resp);
        Assert.isTrue(resp.getApplyStatus() == ApplyStatusEnum.SUCCESS, "验卡请求失败");
    }

    @Test
    public void testAd() {
        CmfAdvanceRequest request = new CmfAdvanceRequest();
        request.setClientId(CLIENT_ID);
        request.setInstOrderToken("8a6a5c1f801544a9bfc8304e49fa0600");
        CmfAdvanceResult resp = controlRequestFacade.advance(request);
        System.out.println(resp);
        Assert.isTrue(resp.getApplyStatus() == ApplyStatusEnum.SUCCESS, "验卡请求失败");
    }


    @Test
    public void testDownloadBill() {
        CmfFileRequest request = new CmfFileRequest();
        request.setChannelCode("FAB211");
        request.setFileDate("20210201");
        request.setRequestNo("1332899");
        request.setClientId("abc");
        request.setRequestType(ControlRequestType.DOWNLOAD_STATEMENT);

        CmfFileResponse result = controlRequestFacade.processFile(request);
        System.out.println(result);
    }

    @Test
    public void testFileImport() {
        CmfFileRequest request = new CmfFileRequest();
        request.setChannelCode("FAB221");
        request.setRequestNo("13328990001");
        request.setClientId("abc");
        request.setRequestType(ControlRequestType.FILE_IMPORT);

        Map<String, String> extMap = new HashMap<>();
        extMap.put("fileExt", "txt");
        extMap.put("fileTag", "abcdefg");
        extMap.put("originalFileName", "oooooo.gg");
        request.setExtension(extMap);

        CmfFileResponse result = controlRequestFacade.processFile(request);
        System.out.println(result);
    }

    @Test
    public void queryClearInfo() {
        ClearInfoQueryRequest request = new ClearInfoQueryRequest();
        request.setClientId(CLIENT_ID);
        ListQueryResult<ClearInfo> ciList = controlRequestFacade.queryClearInfo(request);
        System.out.println(ciList);
        Assert.isTrue(ciList.getList().size() == 3, "返回错误");
        request.setClearNet("swift");
        ciList = controlRequestFacade.queryClearInfo(request);
        System.out.println(ciList);
        Assert.isTrue(ciList.getList().size() == 1, "返回错误");
        request.setClearNet("abc");
        ciList = controlRequestFacade.queryClearInfo(request);
        System.out.println(ciList);

        Assert.isTrue(ciList.getList() == null || ciList.getList().size() == 0, "返回错误");

    }

    private CmfControlRequest buildRequest(ControlRequestType requestType, String startDate, String whiteChannelCode) {
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestNo(genRandomId(10));
        request.setRequestType(requestType);
        request.setPayMode(PayMode.QUICKPAY);
        request.setInstCode("NBAD");
        Extension extension = new Extension();
        extension.add("startDate", startDate);
        extension.add("whiteChannelCode", whiteChannelCode);
        extension.add("sourceOrder", "inst");
        request.setExtension(extension);

        return request;
    }

    private CmfAuthRequest buildAuthRequest(String cardTokenId) {
        CmfAuthRequest request = new CmfAuthRequest();
        request.setCardTokenId(cardTokenId);
        request.setClientId(CLIENT_ID);
        request.setPayMode(PayMode.QUICKPAY);
        request.setInstCode(MOCK_INST);
        request.setRequestNo(genRandomId(17));
        request.setExtension(new HashMap<>());
        request.getExtension().put("whiteChannelCode", MOCK_CHANNEL_3DS2);
        return request;
    }

    @Test
    public void testQueryConfig(){
        ChannelConfigQueryRequest request = new ChannelConfigQueryRequest();
        request.setClientId(CLIENT_ID);
        request.setInstCode("GOOGLEPAY");
        request.setPayMode(PayMode.QUICKPAY);
        request.setRequestNo(genRandomId(10));
        Map<String, String> extMap = new HashMap<>();
        extMap.put("decryptType","GATEWAY_DECRYPT");
        extMap.put("hostApp","botim-pay");
        request.setExtension(extMap);
        ChannelConfigQueryResponse response = controlRequestFacade.queryChannelConfig(request);
        System.out.println(response);
        Assert.isTrue(response.getApplyStatus()==ApplyStatusEnum.SUCCESS, "查询失败");
        Assert.isTrue(response.getExtension().size()>=2, "查询返回结果失败");
        request.setInstCode("XXX");
        response = controlRequestFacade.queryChannelConfig(request);
        System.out.println(response);
        Assert.isTrue(response.getApplyStatus()==ApplyStatusEnum.FAIL && "devicePay.config.notExist".equals(response.getCode()), "查询失败");
        Assert.isTrue(response.getExtension()==null||response.getExtension().size()==0, "查询返回结果失败");
    }


    @Test
    public void testPreAuthComplete(){
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestNo(RandomUtil.randomNumbers(10));
        request.setRequestType(ControlRequestType.ADVANCE);
        request.setExtension(new Extension());
        request.getExtension().add("op_type","preauth-complete");
        request.getExtension().add("sourceOrder","inst");
        request.getExtension().add("payLoad","C3498194");
        request.getExtension().add("orgiFundinOrderNo","20220909FS000000284");
        request.getExtension().add("merchantId","**********");
        request.getExtension().add("terminalId","********");
        request.getExtension().add(ExtensionKey.TRANSACTION_TYPE.getKey(),"reservation");

        controlRequestFacade.control(request,new OperationEnvironment());
    }

    @Test
    public void testRetrieveCardMetadata(){

        RetrieveCardMetadataRequest request = new RetrieveCardMetadataRequest();
        request.setBankCode("ABDI");
        request.setCardNo("P3731920");
        request.setCardType("DC");
        request.setClientId("fundout");
        request.setCountryCode("AE");
        request.setRequestNo(UUID.randomUUID().toString().replace("-", ""));
        RetrieveCardMetadataResponse response = controlRequestFacade.retrieveCardMetadata(request);
        System.out.println(response);
    }

}
