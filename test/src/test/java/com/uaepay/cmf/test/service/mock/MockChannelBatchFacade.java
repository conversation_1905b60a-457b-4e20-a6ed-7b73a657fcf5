package com.uaepay.cmf.test.service.mock;

import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.fss.ext.common.api.ChannelFundBatchFacade;
import org.apache.dubbo.config.annotation.Service;

/**
 * <p>批量渠道mock服务</p>
 *
 * <AUTHOR>
 * @date MockChannelBatchFacade.java v1.0  2020-09-14 13:17
 */
@Service
public class MockChannelBatchFacade implements ChannelFundBatchFacade {
    @Override
    public ChannelFundBatchResult apply(String request) {
        return null;
    }
}
