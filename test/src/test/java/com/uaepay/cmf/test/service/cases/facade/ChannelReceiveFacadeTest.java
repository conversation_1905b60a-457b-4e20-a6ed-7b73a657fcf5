package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelNotifyResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.service.facade.api.ChannelReceiveFacade;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ChannelReceiveFacadeImpl.java v1.0
 */
@RunWith(SpringRunner.class)
@DisplayName("接收通知测试")
public class ChannelReceiveFacadeTest  extends ApplicationTest {

    @Resource
    private ChannelReceiveFacade channelReceiveFacade;
    @Resource
    private InstOrderRepository instOrderRepository;

    @Test
    public void testControlNotify(){
        ChannelFundResult request = new ChannelFundResult();
        /**
         * ChannelFundResult(super=ChannelResult( apiType=CONTROL_VOID_TRANSACTION, resultCode=null,
         * instOrderNo=R342022042300002955, instReturnOrderNo=, apiResultCode=FAILED, apiResultSubCode=REVERSAL_TIME_OUT,
         * apiResultMessage=null, apiResultSubMessage=null, extension=null),
         * realAmount=AED:18.05, instSettleTime=null, processTime=null, instUrl=null, fundChannelCode=NI101)
         */
        request.setApiType(FundChannelApiType.CONTROL_VOID_TRANSACTION);
        request.setInstOrderNo("R342022042300002955");
        request.setApiResultCode("FAILED");
        request.setApiResultSubCode("REVERSAL_TIME_OUT");
        request.setRealAmount(new Money("18.05", "AED"));
        request.setFundChannelCode("NI101");
        ChannelNotifyResult result = channelReceiveFacade.fundNotify(request);
        System.out.println(result);
    }

    @Test
    public void testDebitNotify(){
        ChannelFundResult request = new ChannelFundResult();
        /**
         * ChannelFundResult(super=ChannelResult( apiType=CONTROL_VOID_TRANSACTION, resultCode=null,
         * instOrderNo=R342022042300002955, instReturnOrderNo=, apiResultCode=FAILED, apiResultSubCode=REVERSAL_TIME_OUT,
         * apiResultMessage=null, apiResultSubMessage=null, extension=null),
         * realAmount=AED:18.05, instSettleTime=null, processTime=null, instUrl=null, fundChannelCode=NI101)
         */
        request.setApiType(FundChannelApiType.DEBIT);
        request.setInstOrderNo("T192022061500008327");
        request.setApiResultCode("S");
        request.setApiResultSubCode("SUCCESS");
        request.setRealAmount(new Money("0.11", "AED"));
        request.setFundChannelCode(MOCK_CHANNEL_3DS2);
        Map<String, String> extMap = new HashMap<>();
        extMap.put("extTest", "extVal");
        request.setExtension(MapUtil.mapToJson(extMap));
        ChannelNotifyResult result = channelReceiveFacade.fundNotify(request);
        System.out.println(result);
        InstOrder instOrder = instOrderRepository.loadByNo("T192022061500008327");
        Assert.isTrue(instOrder.getStatus()== InstOrderStatus.SUCCESSFUL
                && "extVal".equals(instOrder.getExtension().get("extTest")), "结果不匹配");
    }
}
