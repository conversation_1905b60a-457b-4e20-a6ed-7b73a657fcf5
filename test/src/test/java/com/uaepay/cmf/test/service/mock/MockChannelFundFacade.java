package com.uaepay.cmf.test.service.mock;

import com.alibaba.fastjson.JSON;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade;
import org.apache.dubbo.config.annotation.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>资金渠道Mock服务</p>
 *
 * <AUTHOR>
 * @date MockChannelFundFacade.java v1.0  2020-09-14 13:15
 */
@Service(group="localMock")
public class MockChannelFundFacade implements ChannelFundFacade {

    @Override
    public ChannelFundResult apply(String req) {
        ChannelFundRequest request = JSON.parseObject(req, ChannelFundRequest.class);


//        ChannelFundResult(super=ChannelResult(super=com.uaepay.cmf.common.domain.ChannelFundResult@3cb328b7
//
//                apiType=DEBIT
//        resultCode=null
//        instOrderNo=100120201030131022221
//        instReturnOrderNo=
//                apiResultCode=PROCEED
//        apiResultSubCode=null
//        apiResultMessage=
//                apiResultSubMessage=null
//        extension={"instTokenId":"P2975909","PAGE_URL":"<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\"><html><head><title>Process Secure Payment</title><meta http-equiv=\"content-type\" content=\"text/html;charset=UTF-8\"><meta name=\"description\" content=\"Process Secure Payment\"><meta name=\"robots\" content=\"noindex\"><style type=\"text/css\">body {font-family:\"Trebuchet MS\",sans-serif; background-color: #FFFFFF; }#msg {border:5px solid #666; background-color:#fff; margin:20px; padding:25px; max-width:40em; -webkit-border-radius: 10px; -khtml-border-radius: 10px; -moz-border-radius: 10px; border-radius: 10px;}#submitButton { text-align: center ; }#footnote {font-size:0.8em;}</style></head><body onload=\"return window.document.echoForm.submit()\"><form name=\"echoForm\" method=\"POST\" action=\"https://secure5.arcot.com/acspage/cap?RID=38689&VAA=B\" accept-charset=\"UTF-8\"><input type=\"hidden\" name=\"PaReq\" value=\"eAFVUlFvmzAQfq/U/4B4rYrPQAiJLq6gabdUWodKt4e9WeYUUAskxtlCf33tlKybn+67O3/+7jvjzbF99X6THpq+W/k8AN+jTvVV021X/o/n++vUvxGXF/hca6J1SeqgSeA3Gga5Ja+pVn4IwHkSphFfxKEvsMieaC9w4hSWMgiRnaG9qlUtOyNQqn2+eRQxLKLZAtkEsSW9WQtwJ03iJLb8cP2YZ+tyAxzZRxk72ZIo5JiPyE4xqv7QGT2KeRojOwM86FdRG7MbloyZfifHoCOjyJCWgepbhsx1IPuUVRycwMFOeWwqwV/Sq/tB1lE/T3jJv/9U9VMxzgsd3q2QuQ6spCERQggcIvBgseSwjK2GUx5l63SJ7G7tQQDWiSmBO/dO9lG16X8hWpe1XcN5mDNCOu76jiydvfA3RvYp+far81UZ6+DbDH7Nv4AqizYaZw9Vvi+T7M9Lnm03VvnU5Bgba1o4A6t4AsgcDZsWaa05Ldtm/vsElxfvU6+4SQ==\"><input type=\"hidden\" name=\"TermUrl\" value=\"https://sim-fcw.test2pay.com/fcw/page/MC101-VS/100120201030131022221/notify.html\"><input type=\"hidden\" name=\"MD\" value=\"\"><noscript><div id=\"msg\"><div id=\"submitButton\"><input type=\"submit\" value=\"Click here to continue\" class=\"button\"></div></div></noscript></form></body></html>\n"})
//        realAmount=AED:0.02
//        instSettleTime=null
//        processTime=null
//        instUrl=null
//        fundChannelCode=MC101)


        ChannelFundResult fundResult = new ChannelFundResult();
        fundResult.setFundChannelCode(request.getFundChannelCode());
        fundResult.setApiType(FundChannelApiType.DEBIT);
        fundResult.setInstOrderNo(request.getInstOrderNo());
        fundResult.setRealAmount(request.getAmount());
        Map<String, String> extMap = new HashMap<>();
        extMap.put("instTokenId", request.getExtension().get("instTokenId"));
        extMap.put("PAGE_URL", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\" \"http://www.w3.org/TR/html4/loose.dtd\"><html><head><title>Process Secure Payment</title><meta http-equiv=\"content-type\" content=\"text/html;charset=UTF-8\"><meta name=\"description\" content=\"Process Secure Payment\"><meta name=\"robots\" content=\"noindex\"><style type=\"text/css\">body {font-family:\"Trebuchet MS\",sans-serif; background-color: #FFFFFF; }#msg {border:5px solid #666; background-color:#fff; margin:20px; padding:25px; max-width:40em; -webkit-border-radius: 10px; -khtml-border-radius: 10px; -moz-border-radius: 10px; border-radius: 10px;}#submitButton { text-align: center ; }#footnote {font-size:0.8em;}</style></head><body onload=\"return window.document.echoForm.submit()\"><form name=\"echoForm\" method=\"POST\" action=\"https://secure5.arcot.com/acspage/cap?RID=38689&VAA=B\" accept-charset=\"UTF-8\"><input type=\"hidden\" name=\"PaReq\" value=\"eAFVUlFvmzAQfq/U/4B4rYrPQAiJLq6gabdUWodKt4e9WeYUUAskxtlCf33tlKybn+67O3/+7jvjzbF99X6THpq+W/k8AN+jTvVV021X/o/n++vUvxGXF/hca6J1SeqgSeA3Gga5Ja+pVn4IwHkSphFfxKEvsMieaC9w4hSWMgiRnaG9qlUtOyNQqn2+eRQxLKLZAtkEsSW9WQtwJ03iJLb8cP2YZ+tyAxzZRxk72ZIo5JiPyE4xqv7QGT2KeRojOwM86FdRG7MbloyZfifHoCOjyJCWgepbhsx1IPuUVRycwMFOeWwqwV/Sq/tB1lE/T3jJv/9U9VMxzgsd3q2QuQ6spCERQggcIvBgseSwjK2GUx5l63SJ7G7tQQDWiSmBO/dO9lG16X8hWpe1XcN5mDNCOu76jiydvfA3RvYp+far81UZ6+DbDH7Nv4AqizYaZw9Vvi+T7M9Lnm03VvnU5Bgba1o4A6t4AsgcDZsWaa05Ldtm/vsElxfvU6+4SQ==\"><input type=\"hidden\" name=\"TermUrl\" value=\"https://sim-fcw.test2pay.com/fcw/page/MC101-VS/100120201030131022221/notify.html\"><input type=\"hidden\" name=\"MD\" value=\"\"><noscript><div id=\"msg\"><div id=\"submitButton\"><input type=\"submit\" value=\"Click here to continue\" class=\"button\"></div></div></noscript></form></body></html>\n");
        fundResult.setExtension(MapUtil.mapToJson(extMap));


        return fundResult;
    }

}
