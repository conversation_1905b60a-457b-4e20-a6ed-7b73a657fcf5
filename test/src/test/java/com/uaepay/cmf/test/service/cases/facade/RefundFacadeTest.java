package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.service.facade.api.RefundFacade;
import com.uaepay.cmf.service.facade.domain.refund.RetryRefundRequest;
import com.uaepay.cmf.test.base.ApplicationTest;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 12/12/2023 15:34
 */
public class RefundFacadeTest  extends ApplicationTest {
    @Reference
    private RefundFacade refundFacade;

    @Test
    public void testRetry(){
        RetryRefundRequest request = new RetryRefundRequest();
        request.setOperator("Test");
        request.setInstOrderNo("R482024011000000600");
        request.setNeedNewInstOrderNo(false);
        CommonResponse response = refundFacade.retryReFund(request);
        System.out.println(response);
    }


    @Test
    public void testRetryNewOrderNo(){
        RetryRefundRequest request = new RetryRefundRequest();
        request.setOperator("Test");
        request.setInstOrderNo("R482023111000000550");
        request.setNeedNewInstOrderNo(true);
        CommonResponse response = refundFacade.retryReFund(request);
        System.out.println(response);
    }
}
