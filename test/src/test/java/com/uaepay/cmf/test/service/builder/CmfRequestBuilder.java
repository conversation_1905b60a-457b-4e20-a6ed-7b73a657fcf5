package com.uaepay.cmf.test.service.builder;

import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;

import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CmfRequestBuilder.java v1.0  2020-09-14 23:15
 */
public class CmfRequestBuilder extends BaseBuilder {

    public static CmfRequest genCmfRequest(String cardTokenId, String amount) {
        CmfRequest request = new CmfRequest();
        request.setInstCode(MOCK_INST);
        request.setPaymentSeqNo(genRandomId(10));
        request.setSettlementId(genRandomId(10));
        request.setProductCode("20040010");
        request.setPaymentCode("1001");
        request.setPayMode(PayMode.QUICKPAY);
        request.setBizType(BizType.FUNDIN);
        request.setMemberId("100000429655");
        request.setAmount(new Money(amount, "AED"));
        request.setBizTime(new Date());
        Extension extension = new Extension();
        extension.add("paymentOrderNo", "312019122721400316");
        extension.add(CARD_TOKEN, cardTokenId);
        request.setExtension(extension);
        return request;
    }
}
