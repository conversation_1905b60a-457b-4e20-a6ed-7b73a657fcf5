package com.uaepay.cmf.test.service.mock;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.escrow.service.facade.VirtualCardFacade;
import com.uaepay.escrow.service.facade.request.CardSvaInfoQueryRequest;
import com.uaepay.escrow.service.facade.request.CardSvaStatusUpdateRequest;
import com.uaepay.escrow.service.facade.request.QueryCardApplyRecordRequest;
import com.uaepay.escrow.service.facade.response.CardSvaInfoExistsResponse;
import com.uaepay.escrow.service.facade.response.CardSvaInfoQueryResponse;
import com.uaepay.escrow.service.facade.response.CustomerStatusUpdateResponse;
import com.uaepay.escrow.service.facade.response.common.ItemQueryResponse;
import org.apache.dubbo.config.annotation.Service;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date MockVirtualCardFacade.java v1.0
 */
@Service
public class MockVirtualCardFacade implements VirtualCardFacade {
    @Override
    public CardSvaInfoQueryResponse svaInfoQuery(CardSvaInfoQueryRequest cardSvaInfoQueryRequest) {
        CardSvaInfoQueryResponse response = new CardSvaInfoQueryResponse();
        response.setSvaStatus("A");
        return response;
    }

    @Override
    public CardSvaInfoExistsResponse isSvaInfoExist(CardSvaInfoQueryRequest cardSvaInfoQueryRequest) {
        return null;
    }

    @Override
    public CommonResponse svaStatusUpdate(CardSvaStatusUpdateRequest cardSvaStatusUpdateRequest) {
        return null;
    }

    @Override
    public CustomerStatusUpdateResponse customerSvaStatusUpdate(CardSvaStatusUpdateRequest cardSvaStatusUpdateRequest) {
        return null;
    }

    @Override
    public ItemQueryResponse<Integer> queryApplyRecord(QueryCardApplyRecordRequest queryCardApplyRecordRequest) {
        return null;
    }
}
