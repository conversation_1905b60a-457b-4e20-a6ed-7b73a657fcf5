package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.NotifyStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.pub.csc.compensation.facade.CompensationFacade;
import com.uaepay.pub.csc.compensation.facade.domain.CompensateDetail;
import com.uaepay.pub.csc.compensation.facade.request.CompensateSingleRequest;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CompensationFacadeTestCase.java v1.0
 */
@DisplayName("补偿测试")
@RunWith(SpringRunner.class)
public class CompensationFacadeTestCase extends ApplicationTest {

    @Resource
    private CompensationFacade compensationFacade;
    @Resource
    private CmfOrderRepository cmfOrderRepository;

    @Test
    public void testCompensationNotify() {
        CmfOrder cmfOrder = orderRepoTestService.buildCmfOrder(CmfOrderStatus.SUCCESSFUL);
        InstOrder instOrder = orderRepoTestService.buildInstOrder(cmfOrder.getOrderSeqNo(), InstOrderStatus.SUCCESSFUL);

        CompensateSingleRequest request = new CompensateSingleRequest();
        request.setNotifyType("escrow");
        CompensateDetail detail = new CompensateDetail();
        detail.setOrderNo(instOrder.getInstOrderNo());
        request.setDetail(detail);
        CommonResponse response = compensationFacade.applySingle(request);
        Assert.isTrue(response.getApplyStatus() == ApplyStatusEnum.SUCCESS, "调用失败");

        request.setNotifyType("grc");
        detail.setOrderNo(instOrder.getInstOrderNo());
        request.setDetail(detail);
        response = compensationFacade.applySingle(request);
        Assert.isTrue(response.getApplyStatus() == ApplyStatusEnum.SUCCESS, "调用失败");

        request.setNotifyType("paymentNotify");
        detail.setOrderNo(cmfOrder.getPaymentSeqNo());
        request.setDetail(detail);
        response = compensationFacade.applySingle(request);
        Assert.isTrue(response.getApplyStatus() == ApplyStatusEnum.SUCCESS, "调用失败");
        CmfOrder resp = cmfOrderRepository.loadByCmfSeqNo(cmfOrder.getOrderSeqNo(), false);
        Assert.isTrue(resp.getPaymentNotifyStatus()== NotifyStatus.SUCCESSFUL, "cmf通知失败");

    }
}
