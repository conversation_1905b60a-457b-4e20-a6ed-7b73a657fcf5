package com.uaepay.cmf.test.service.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.IOException;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/7/28
 */
@Slf4j
public class CaseUtil {

    public static String getCaseFile(String fileName,String fileType){
        String caseStr = null;
        try {
            File caseFile = ResourceUtils.getFile("classpath:" + fileType + "/"+ fileName +"." + fileType);
            caseStr = FileUtils.readFileToString(caseFile,"UTF-8");
        } catch (IOException e) {
            log.error("读取文件失败");
            throw new RuntimeException(e);
        }
        return caseStr;
    }
}
