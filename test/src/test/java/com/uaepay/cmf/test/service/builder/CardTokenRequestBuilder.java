package com.uaepay.cmf.test.service.builder;

import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateRequest;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenRequestBuilder.java v1.0  2020-09-14 18:41
 */
public class CardTokenRequestBuilder {

    public static CardTokenCreateRequest createCardTokenRequest(String csc) {
        CardTokenCreateRequest cardTokenRequest = new CardTokenCreateRequest();
        cardTokenRequest.setSessionId("12345token12345");
        cardTokenRequest.setMemberId("10101010101010");
        cardTokenRequest.setInstCode("MOCK");
        cardTokenRequest.setDbcr("DC");
        cardTokenRequest.setCardType("DC");
        cardTokenRequest.setCardBrand("VISA");
        cardTokenRequest.setCompanyOrPersonal("C");
        cardTokenRequest.setIs3DS("Y");
        cardTokenRequest.setResultUrl("http://www.baidu.com/");
        cardTokenRequest.setNeedCsc("Y");
        cardTokenRequest.setCsc(csc);
        cardTokenRequest.setClientId("testClientId");
        return cardTokenRequest;
    }

    public static CardTokenCreateRequest createAnonymousCreateRequest(String csc) {
        CardTokenCreateRequest cardTokenRequest = new CardTokenCreateRequest();
        cardTokenRequest.setMemberId("anonymous");
        cardTokenRequest.setInstCode("MOCK");
        cardTokenRequest.setCardHolder("Tester");
        cardTokenRequest.setCardId(106664L);
        cardTokenRequest.setCardNo("6666666666666666");
        cardTokenRequest.setNeedCsc("Y");
        cardTokenRequest.setCsc(csc);
        return cardTokenRequest;
    }

    public static CardTokenCreateRequest createAnonymousUpdateRequest(String sessionId) {
        CardTokenCreateRequest cardTokenRequest = new CardTokenCreateRequest();
        cardTokenRequest.setSessionId(sessionId);
        cardTokenRequest.setInstCode("MOCK");
        cardTokenRequest.setDbcr("DC");
        cardTokenRequest.setCompanyOrPersonal("C");
        cardTokenRequest.setIs3DS("Y");
        cardTokenRequest.setIpAddress("*******");
        cardTokenRequest.setResultUrl("http://www.baidu.com/");
        return cardTokenRequest;
    }
}
