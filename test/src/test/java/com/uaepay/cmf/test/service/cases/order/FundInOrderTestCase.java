package com.uaepay.cmf.test.service.cases.order;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateRequest;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateResult;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.cmf.test.base.ShareDataBean;
import com.uaepay.cmf.test.service.builder.CmfRequestBuilder;
import com.uaepay.cmf.test.service.util.EnvUtil;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.core.annotation.Order;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date FundInOrderCase.java v1.0  2020-09-14 13:11
 */
@Slf4j
@DisplayName("入款订单测试")
@RunWith(SpringRunner.class)
public class FundInOrderTestCase extends ApplicationTest {

    @Test
    @Order(0)
    @DisplayName("0 创建card token")
    public void createCardToken() {
        CardTokenCreateRequest cardTokenRequest = createCardTokenRequest();
        CardTokenCreateResult result = cardTokenFacade.create(cardTokenRequest);
        Assert.isTrue(result.getApplyStatus() == ApplyStatusEnum.SUCCESS, "创建card token失败");
        ShareDataBean.addData(CARD_TOKEN, result.getCardTokenId());
    }


    @Test
    @Order(1)
    @DisplayName("1 入款订单测试")
    public void fundIn() {
        CmfRequest request = CmfRequestBuilder.genCmfRequest(ShareDataBean.getData(CARD_TOKEN), "10");
        CmfFundResult result = fundRequestFacade.apply(request, EnvUtil.getEnv());
        Assert.isTrue(result.getResultCode() == CmfFundResultCode.IN_PROCESS, "返回订单状态错误");
        ShareDataBean.addData(INST_ORDER, result.getInstOrderNo());
    }

    /**
     * 同步订单测试
     */
    @Test
    @DisplayName("moto订单测试")
    public void motoFundIn() {
        log.info("3");
    }


    @Test
    @Order(1)
    @DisplayName("重复订单测试")
    public void duplicateOrder() {
        CmfRequest request = createCmfRequest();
        CmfFundResult result = fundRequestFacade.apply(request, EnvUtil.getEnv());
        Assert.isTrue(result.getResultCode() == CmfFundResultCode.IN_PROCESS, "返回订单状态错误");
        CmfFundResult result2 = fundRequestFacade.apply(request, EnvUtil.getEnv());
        Assert.isTrue(result2.getResultCode() == CmfFundResultCode.IN_PROCESS, "返回订单状态错误");
    }


    private CardTokenCreateRequest createCardTokenRequest() {
        CardTokenCreateRequest cardTokenRequest = new CardTokenCreateRequest();
        cardTokenRequest.setSessionId("12345token12345");
        cardTokenRequest.setMemberId("10101010101010");
        cardTokenRequest.setInstCode("MOCK");
        cardTokenRequest.setDbcr("DC");
        cardTokenRequest.setCompanyOrPersonal("C");
        cardTokenRequest.setIs3DS("Y");
        cardTokenRequest.setResultUrl("http://www.baidu.com/");
        cardTokenRequest.setNeedCsc("Y");
        cardTokenRequest.setCsc("123");
        return cardTokenRequest;
    }

    private CmfRequest createCmfRequest() {
        CmfRequest request = new CmfRequest();
        request.setInstCode(MOCK_INST);
        request.setPaymentSeqNo(genRandomId(10));
        request.setSettlementId(genRandomId(10));
        request.setProductCode("20040010");
        request.setPaymentCode("1001");
        request.setPayMode(PayMode.QUICKPAY);
        request.setBizType(BizType.FUNDIN);
        request.setMemberId("100000429655");
        request.setAmount(new Money("10.1", "AED"));
        request.setBizTime(new Date());
        Extension extension = new Extension();
        extension.add("paymentOrderNo", "312019122721400316");
        extension.add(CARD_TOKEN, ShareDataBean.getData(CARD_TOKEN));
        request.setExtension(extension);
        return request;
    }

    private VerifySignRequest createVerifySignRequest() {
        VerifySignRequest request = new VerifySignRequest();
        request.setInstOrderNo(ShareDataBean.getData(INST_ORDER));
        request.setChannelCode(MOCK_CHANNEL);
        request.setApiType("VS");
        request.setCallbackType("PAGE");
        Map<String, String> verifyParam = new HashMap<>();
        verifyParam.put(TARGET_STATUS, "S");
        request.setVerifyParam(verifyParam);
        return request;
    }

}
