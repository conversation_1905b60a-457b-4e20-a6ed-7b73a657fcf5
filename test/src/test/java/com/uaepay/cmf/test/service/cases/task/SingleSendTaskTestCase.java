package com.uaepay.cmf.test.service.cases.task;

import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolderImpl;
import com.uaepay.cmf.fss.ext.resend.newtask.SingleSendTask;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.schema.cmf.enums.BizType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date SingleSendTaskTestCase.java v1.0  2020-09-14 13:59
 */
@RunWith(SpringRunner.class)
public class SingleSendTaskTestCase extends ApplicationTest {

    @Resource
    private SingleSendTask singleSendTask;

    @Resource
    SysConfigurationHolderImpl sysConfigurationHolder;

    @Test
    public void testExecuteTask(){
        sysConfigurationHolder.refreshCache();
        Boolean result = singleSendTask.executeTask(BizType.FUNDOUT);
        System.out.println(result);
    }

}
