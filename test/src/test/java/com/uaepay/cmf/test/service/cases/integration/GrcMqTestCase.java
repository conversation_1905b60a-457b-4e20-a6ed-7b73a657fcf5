package com.uaepay.cmf.test.service.cases.integration;

import com.uaepay.cmf.fss.ext.integration.grc.GrcClient;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import com.uaepay.cmf.test.base.ApplicationTest;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.core.annotation.Order;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CacheFacadeTestCase.java v1.0  2020-09-14 13:43
 */
@DisplayName("MQ测试")
@RunWith(SpringRunner.class)
public class GrcMqTestCase extends ApplicationTest {

    @Resource
    private GrcClient grcClient;

    @Test
    @Order(1)
    @DisplayName("发送mq")
    public void testCreateCardToken() {

        Notify3dsResult result = new Notify3dsResult();
        result.setBankName("123");
        grcClient.send3DsNotify(result);
        System.out.println("send success");
    }

}
