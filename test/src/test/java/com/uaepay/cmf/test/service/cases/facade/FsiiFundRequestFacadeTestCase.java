package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.api.FundRequestFacade;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.cmf.test.service.util.CaseUtil;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.OperationEnvironment;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/8/7
 */
@RunWith(SpringRunner.class)
public class FsiiFundRequestFacadeTestCase  extends ApplicationTest {

    @Resource
    private FundRequestFacade fundRequestFacade;

    @Resource
    private ControlRequestFacade controlRequestFacade;

    @SneakyThrows
    @Test
    public void testFsiiPurchase(){
        CmfRequest request = buildFsiiDebitRequest("20220808FS1000017");
        CmfFundResult cmfFundResult = fundRequestFacade.apply(request,new OperationEnvironment());

        System.out.println(JsonUtil.toJsonString(cmfFundResult));
    }
    private CmfRequest buildFsiiDebitRequest(String paymentSeqNo) {

        CmfRequest request = new CmfRequest();
        request.setInstCode("FISERV");
        request.setPaymentSeqNo(paymentSeqNo);
        request.setProductCode("60040090");
        request.setPaymentCode("4001");
        request.setPayMode(PayMode.QUICKPAY);
        request.setBizType(BizType.FUNDIN);
        request.setMemberId("anonymousMember");
        request.setAmount(new Money("5.00", "AED"));
        request.setBizTime(new Date());
        Extension extension = new Extension();
        extension.add("payLoad", "C3470131");
        extension.add("merchantId", "8116000002");
        extension.add("terminalId", "80905678");
        extension.add("DBCR", "DC");
        request.setExtension(extension);
        return request;
    }
    @SneakyThrows
    @Test
    public void testFsiiRefund(){
        CmfRequest request = buildFsiiRefundRequest("20220826FS000024103");
        CmfFundResult cmfFundResult = fundRequestFacade.refund(request,new OperationEnvironment());

        System.out.println(JsonUtil.toJsonString(cmfFundResult));
    }

    private CmfRequest buildFsiiRefundRequest(String paymentSeqNo) {

        CmfRequest request = new CmfRequest();
        request.setInstCode("FISERV");
        request.setPaymentSeqNo(paymentSeqNo);
        request.setProductCode("60040090");
        request.setPaymentCode("4001");
        request.setPayMode(PayMode.QUICKPAY);
        request.setBizType(BizType.REFUND);
        request.setMemberId("10000042");
        request.setAmount(new Money("36.00", "AED"));
        request.setBizTime(new Date());
        Extension extension = new Extension();
        extension.add("payLoad", "C3482619");
        extension.add("merchantId", "8116000002");
        extension.add("terminalId", "80905679");
        extension.add("orgiFundinOrderNo","20220826FS000024100");
        extension.add("DBCR", "DC");
        request.setExtension(extension);
        return request;
    }

    @SneakyThrows
    @Test
    public void testFsiiCancel(){
        CmfRequest request = buildFsiiCancelRequest("20220808FS0000091");
        CmfFundResult cmfFundResult = fundRequestFacade.refund(request,new OperationEnvironment());

        System.out.println(JsonUtil.toJsonString(cmfFundResult));
    }

    @SneakyThrows
    @Test
    public void testFsiiReverse(){
        CmfControlResult cmfFundResult = controlRequestFacade.control(buildReserve("20220817FS100000663"),new OperationEnvironment());
        System.out.println(JsonUtil.toJsonString(cmfFundResult));
    }

    private CmfControlRequest buildReserve(String paymentSeqNo) {

        CmfControlRequest request = new CmfControlRequest();
        request.setRequestNo(ControlRequestType.VOID_TRANSACTION.getCode()+paymentSeqNo);
        request.setRequestType(ControlRequestType.VOID_TRANSACTION);
        request.setPayMode(PayMode.QUICKPAY);
        request.setInstCode("FISERV");
        request.setAmount(new Money("5.00", "AED"));
        Extension extension = new Extension();
        extension.add("sourceOrder", "inst");
        extension.add("sourceCode","POS_FISERV");
        extension.add("payLoad", "C3470131");
        extension.add("orgiFundinOrderNo","20220817FS100000561");
        extension.add("merchantId", "8116000002");
        extension.add("terminalId", "80905678");
        request.setExtension(extension);

        return request;
    }

    @SneakyThrows
    @Test
    public void testFsiiInitialPreAuth(){
        String paymentSeqNo = "20220808FS1" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("ddHHmm"));
        CmfRequest request = buildFsiiInitialPreAuth(paymentSeqNo);
        CmfFundResult cmfFundResult = fundRequestFacade.apply(request,new OperationEnvironment());
        System.out.println(JsonUtil.toJsonString(cmfFundResult));
    }

    private CmfRequest buildFsiiInitialPreAuth(String paymentSeqNo) {

        CmfRequest request = new CmfRequest();
        request.setInstCode("FISERV");
        request.setPaymentSeqNo(paymentSeqNo);
        request.setProductCode("60040090");
        request.setPaymentCode("4001");
        request.setPayMode(PayMode.QUICKPAY);
        request.setBizType(BizType.FUNDIN);
        request.setMemberId("anonymousMember");
        request.setAmount(new Money("300.1", "AED"));
        request.setBizTime(new Date());
        request.setSettlementId("20221010045538637");
        Extension extension = new Extension();
        extension.add("sourceCode","POS_FISERV");
        extension.add("preAuth","Y");
        extension.add("payLoad", "C3482033");
        extension.add("merchantId", "8116000002");
        extension.add("terminalId", "80905678");
        extension.add("DBCR", "DC");
        request.setExtension(extension);
        return request;
    }


    @SneakyThrows
    @Test
    public void testFsiiReservePreAuth(){
        CmfControlResult cmfFundResult = controlRequestFacade.control(buildReserve("20220817FS100000666"),new OperationEnvironment());
        System.out.println(JsonUtil.toJsonString(cmfFundResult));
    }


    @SneakyThrows
    @Test
    public void testFsiiUpdatePreAuth(){
        CmfControlResult cmfFundResult = controlRequestFacade.control(buildUpdatePreAuth("20220808FS1101719"),new OperationEnvironment());
        System.out.println(JsonUtil.toJsonString(cmfFundResult));
    }

    @SneakyThrows
    @Test
    public void testFsiiPreAuthVoid(){
        CmfControlResult cmfFundResult = controlRequestFacade.control(buildPreAuthVoid("20220808FS1102242"),new OperationEnvironment());
        System.out.println(JsonUtil.toJsonString(cmfFundResult));
    }


    private CmfControlRequest buildPreAuthVoid(String orderNo){
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestNo(ControlRequestType.PREAUTH_VOID.getCode()+System.currentTimeMillis());
        request.setRequestType(ControlRequestType.PREAUTH_VOID);
        request.setPayMode(PayMode.QUICKPAY);
        request.setInstCode("FISERV");
        request.setAmount(new Money("300.1", "AED"));
        Extension extension = new Extension();
        extension.add("sourceCode","POS_FISERV");
        extension.add("sourceOrder", "inst");
        extension.add("payLoad", "C3482033");
        extension.add("orgiFundinOrderNo",orderNo);
        extension.add("merchantId", "000008116000002");
        extension.add("terminalId", "80905679");
        request.setExtension(extension);
        return request;
    }


    private CmfControlRequest buildUpdatePreAuth(String seqNo){
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestNo(ControlRequestType.PREAUTH_UPDATE.getCode()+seqNo);
        request.setRequestType(ControlRequestType.PREAUTH_UPDATE);
        request.setPayMode(PayMode.QUICKPAY);
        request.setInstCode("FISERV");
        request.setAmount(new Money("300.1", "AED"));
        Extension extension = new Extension();
        extension.add("sourceCode","POS_FISERV");
        extension.add("sourceOrder", "inst");
        extension.add("payLoad", "C3482033");
        extension.add("orgiFundinOrderNo","20220808FS1101719");
        extension.add("merchantId", "000008116000002");
        extension.add("terminalId", "80905679");
        request.setExtension(extension);
        return request;
    }


    @SneakyThrows
    @Test
    public void testFsiiCompletePreAuth(){
        CmfControlResult cmfFundResult = controlRequestFacade.control(buildCompletePreAuth("20220808FS1101719"),new OperationEnvironment());
        System.out.println(JsonUtil.toJsonString(cmfFundResult));
    }
    private CmfControlRequest buildCompletePreAuth(String seqNo){
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestNo(ControlRequestType.PREAUTH_COMPLETE.getCode()+seqNo);
        request.setRequestType(ControlRequestType.PREAUTH_COMPLETE);
        request.setPayMode(PayMode.QUICKPAY);
        request.setInstCode("FISERV");
        request.setAmount(new Money("200.1", "AED"));
        Extension extension = new Extension();
        extension.add("sourceOrder", "inst");
        extension.add("sourceCode","POS_FISERV");
        extension.add("payLoad", "C3482033");
        extension.add("orgiFundinOrderNo",seqNo);
        extension.add("merchantId", "8116000002");
        extension.add("terminalId", "80905679");
        request.setExtension(extension);
        return request;
    }

    @SneakyThrows
    @Test
    public void testFsiiInquiryPreAuth(){
        CmfControlResult cmfFundResult = controlRequestFacade.control(buildQueryPreAuth(),new OperationEnvironment());
        System.out.println(JsonUtil.toJsonString(cmfFundResult));
    }








    private CmfControlRequest buildQueryPreAuth(){
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestNo(ControlRequestType.ADVANCE_QUERY.getCode()+"D99239121213");
        request.setRequestType(ControlRequestType.ADVANCE_QUERY);
        request.setPayMode(PayMode.QUICKPAY);
        request.setInstCode("FISERV");
        request.setAmount(new Money("300.1", "AED"));
        Extension extension = new Extension();
        extension.add("sourceCode","POS_FISERV");
        extension.add("sourceOrder", "inst");
        extension.add("payLoad", "C3467039");
        extension.add("orgiFundinOrderNo","20220808FS0000088");
        extension.add("merchantId", "000008116000002");
        extension.add("terminalId", "80905679");
        request.setExtension(extension);
        return request;
    }





    private CmfRequest buildFsiiCancelRequest(String paymentSeqNo) {

        CmfRequest request = new CmfRequest();
        request.setInstCode("FISERV");
        request.setPaymentSeqNo(paymentSeqNo);
        request.setProductCode("60040090");
        request.setPaymentCode("4001");
        request.setPayMode(PayMode.QUICKPAY);
        request.setBizType(BizType.REFUND);
        request.setMemberId("10000042");
        request.setAmount(new Money("300.1", "AED"));
        request.setBizTime(new Date());
        Extension extension = new Extension();
        extension.add("sourceCode","POS_FISERV");
        extension.add("payLoad", "C3467039");
        extension.add("orgiFundinOrderNo","20220808FS0000091");
        extension.add("merchantId", "000008116000002");
        extension.add("terminalId", "80905679");
        extension.add("DBCR", "DC");
        request.setExtension(extension);
        return request;
    }

}
