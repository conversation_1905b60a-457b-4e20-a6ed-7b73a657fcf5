package com.uaepay.cmf.test.service.cases.integration;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.fss.ext.integration.common.AbstractJmsSender;
import com.uaepay.cmf.fss.ext.integration.enums.MQActionEnum;
import com.uaepay.mq.core.MQService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.jms.UncategorizedJmsException;
import org.springframework.jms.ResourceAllocationException;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test case for AbstractJmsSender
 *
 * <AUTHOR>
 */
public class AbstractJmsSenderTest {

    // Concrete implementation of AbstractJmsSender for testing
    private static class TestJmsSender extends AbstractJmsSender {
        public BaseResult testSend(Object content, MQActionEnum queueName) {
            return send(content, queueName);
        }
    }

    @InjectMocks
    private TestJmsSender testJmsSender;

    @Mock
    private MQService mqService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // Set default configuration
        ReflectionTestUtils.setField(testJmsSender, "retryIntervalMs", 10L); // Use short interval for tests
        ReflectionTestUtils.setField(testJmsSender, "retryCount", 2);
    }

    @Test
    public void testSendSuccess() throws Exception {
        // Arrange
        String testContent = "Test Message";
        MQActionEnum testQueue = MQActionEnum.FUNDS_RESULT_QUEUE;
        
        // Act
        BaseResult result = testJmsSender.testSend(testContent, testQueue);
        
        // Assert
        assertTrue("Message send should succeed", result.isSuccess());
        verify(mqService, times(1)).sendMessage(any());
    }

    @Test
    public void testSendRetrySuccess() throws Exception {
        // Arrange
        String testContent = "Test Message";
        MQActionEnum testQueue = MQActionEnum.FUNDS_RESULT_QUEUE;
        
        // First call fails, second succeeds
        doThrow(UncategorizedJmsException.class)
            .doNothing()
            .when(mqService).sendMessage(any());
        
        // Act
        BaseResult result = testJmsSender.testSend(testContent, testQueue);
        
        // Assert
        assertTrue("Message send should succeed after retry", result.isSuccess());
        verify(mqService, times(2)).sendMessage(any());
    }
    
    @Test
    public void testSendRetryFailure() throws Exception {
        // Arrange
        String testContent = "Test Message";
        MQActionEnum testQueue = MQActionEnum.FUNDS_RESULT_QUEUE;
        
        // All attempts fail
        doThrow(UncategorizedJmsException.class)
            .when(mqService).sendMessage(any());
        
        // Act
        BaseResult result = testJmsSender.testSend(testContent, testQueue);
        
        // Assert
        assertFalse("Message send should fail after all retries", result.isSuccess());
        verify(mqService, times(3)).sendMessage(any()); // Initial + 2 retries
    }
    
    @Test
    public void testSendWithResourceAllocationException() throws Exception {
        // Arrange
        String testContent = "Test Message";
        MQActionEnum testQueue = MQActionEnum.FUNDS_RESULT_QUEUE;
        
        // First call fails with ResourceAllocationException, second succeeds
        doThrow(ResourceAllocationException.class)
            .doNothing()
            .when(mqService).sendMessage(any());
        
        // Act
        BaseResult result = testJmsSender.testSend(testContent, testQueue);
        
        // Assert
        assertTrue("Message send should succeed after retry", result.isSuccess());
        verify(mqService, times(2)).sendMessage(any());
    }
    
    @Test
    public void testSendWithGenericException() throws Exception {
        // Arrange
        String testContent = "Test Message";
        MQActionEnum testQueue = MQActionEnum.FUNDS_RESULT_QUEUE;
        
        // Call fails with generic exception
        doThrow(RuntimeException.class)
            .when(mqService).sendMessage(any());
        
        // Act
        BaseResult result = testJmsSender.testSend(testContent, testQueue);
        
        // Assert
        assertFalse("Message send should fail with generic exception", result.isSuccess());
        verify(mqService, times(1)).sendMessage(any()); // No retry for generic exceptions
    }
    
    @Test
    public void testConfigurableRetryCount() throws Exception {
        // Arrange
        String testContent = "Test Message";
        MQActionEnum testQueue = MQActionEnum.FUNDS_RESULT_QUEUE;
        
        // Configure a higher retry count
        ReflectionTestUtils.setField(testJmsSender, "retryCount", 5);
        
        // All attempts fail
        doThrow(UncategorizedJmsException.class)
            .when(mqService).sendMessage(any());
        
        // Act
        BaseResult result = testJmsSender.testSend(testContent, testQueue);
        
        // Assert
        assertFalse("Message send should fail after all retries", result.isSuccess());
        verify(mqService, times(6)).sendMessage(any()); // Initial + 5 retries
    }
} 