package com.uaepay.cmf.test.service.mock;

import com.uaepay.acs.service.facade.language.LanguageFacade;
import com.uaepay.acs.service.facade.language.domain.LanguageConfigDTO;
import com.uaepay.acs.service.facade.language.request.LanguageConfigQueryRequest;
import com.uaepay.acs.service.facade.language.request.LanguageConfigQueryRequestV2;
import com.uaepay.basis.beacon.service.facade.domain.response.ObjectQueryResponse;
import org.apache.dubbo.config.annotation.Service;

@Service
public class MockLanguageFacade implements LanguageFacade {

    @Override
    public ObjectQueryResponse<LanguageConfigDTO> queryConfig(LanguageConfigQueryRequest languageConfigQueryRequest) {
        return null;
    }

    @Override
    public ObjectQueryResponse<LanguageConfigDTO> queryConfigV2(LanguageConfigQueryRequestV2 languageConfigQueryRequestV2) {
        return null;
    }
}
