package com.uaepay.cmf.test.service.cases.order;

import com.uaepay.cmf.domainservice.channel.router.ChannelService;
import com.uaepay.cmf.test.base.ApplicationTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ChannelRouteTestCase.java v1.0  2020-09-14 13:27
 */
@DisplayName("渠道路由测试用例")
@RunWith(SpringRunner.class)
public class ChannelRouteTestCase extends ApplicationTest {

    @Resource
    private ChannelService channelService;

    @Test
    public void test3ds(){
        boolean status = channelService.is3ds2Channel(MOCK_CHANNEL_3DS2);
        Assert.isTrue(status, "返回失败");
        status = channelService.is3ds2Channel(MOCK_CHANNEL_3DS2);
        Assert.isTrue(status, "返回失败");
    }

}
