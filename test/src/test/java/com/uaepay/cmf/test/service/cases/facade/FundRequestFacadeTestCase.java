package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.api.FundRequestFacade;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import com.uaepay.cmf.test.TestException;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.cmf.test.service.util.EnvUtil;
import com.uaepay.cmf.test.service.util.TestUtils;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.OperationEnvironment;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Random;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * <AUTHOR> magicjoey
 * @Classname FundRequestFacadeTest
 * @Description TODO
 * @Date 2019/10/22 6:23 PM
 */
@RunWith(SpringRunner.class)
public class FundRequestFacadeTestCase extends ApplicationTest {

    @Resource
    private FundRequestFacade fundRequestFacade;

    @Resource
    private ControlRequestFacade controlRequestFacade;

    private static String cancelTpduData = "600065000002007020058000C00206165175548369205998020000000000011100000830007100152136303130303031373132303930303030303030302020200150820219808407A0000000041010950500008080019A032208039C01005F2A0207845F3401019F02060000000111009F03060000000000009F090200029F10120110A04001220000000000000000000000FF9F1A0207849F1E0830303030303930359F260888267F6DA3503BDC9F2701809F3303E000E89F34033F00019F3501229F360200149F37048138A62B9F6E0707840000303000000630303038333000400038474E303030303030303030303030303030303030303030303030303030303030303030303030";



    @Test
    public void testRefundToFundout(){
        CmfRequest request = new CmfRequest();
        request.setInstCode("LEAN");
        request.setPayMode(PayMode.TOKENPAY);
        request.setPaymentSeqNo(genRandomId(10));
        request.setSettlementId(genRandomId(10));
        request.setAmount(new Money("1", "AED"));
        request.setBizType(BizType.REFUND);
        // request.setPaymentSeqNo("20191104RI2836539227");
        // request.setSettlementId("201911041102199227");
        request.setBizTime(new Date());
        request.setPaymentCode("1001");
        request.setProductCode("60040040");
        request.setMemberId("100000348328");

        Extension extension = new Extension();
        extension.add("ORGI_FUNDIN_ORDER_NO", "20231017FI001124008");
        extension.add("orgiSettlementId", "20231017003842157");
        extension.add("orgiFundinOrderNo", "20231017FI001124008");
        extension.add("topayMerchantId", "200000000888");
        request.setExtension(extension);
        CmfFundResult refund = fundRequestFacade.refund(request, new OperationEnvironment());
        System.out.println(refund);
    }

    @Test
    public void test4Reversed() {
        CmfRequest request = buildRequest("20210208FI" + genRandomId(8), "VT101").apply("");
        CmfFundResult fundResult = fundRequestFacade.apply(request, EnvUtil.getEnv());
        Assert.isTrue(fundResult.getResultCode() == CmfFundResultCode.FAILED
                && "cmf.trade.reversed".equals(fundResult.getExtension().getValue("unityResultCode")), "撤销不成功");

    }

    @Test
    public void testReversed4Success() {
        // 测试撤销已成功订单
        CmfRequest request = buildRequest("20210208FI" + genRandomId(8), "TEST101").apply("");
        CmfFundResult fundResult = fundRequestFacade.apply(request, EnvUtil.getEnv());
        Assert.isTrue(fundResult.getResultCode() == CmfFundResultCode.SUCCESS, "交易不成功");
        CmfControlRequest controlRequest = buildVoidRequest(ControlRequestType.VOID_TRANSACTION).apply(fundResult);
        CmfControlResult controlResult = controlRequestFacade.control(controlRequest, EnvUtil.getEnv());
        Assert.isTrue(controlResult.getResultCode().equals("VALIDARE_ERROR")
                && "机构订单不可撤销，请确认".equals(controlResult.getReturnMessage()), "撤销不成功");
    }

    private Function<CmfFundResult, CmfControlRequest> buildVoidRequest(ControlRequestType requestType) {
        return (req) -> {
            CmfControlRequest request = new CmfControlRequest();
            request.setRequestNo(genRandomId(10));
            request.setRequestType(requestType);
            request.setPayMode(PayMode.QUICKPAY);
            request.setInstCode("NBAD");
            request.setPreRequestNo(req.getInstOrderNo());
            Extension extension = new Extension();
            extension.add("sourceOrder", "inst");
            request.setExtension(extension);

            return request;
        };
    }


    @Test
    public void test4Normal() {
        CmfRequest request = buildRequest("20210208FI0000002");
        CmfFundResult fundResult = fundRequestFacade.apply(request, EnvUtil.getEnv());
        Assert.isTrue(fundResult.getResultCode() == CmfFundResultCode.SUCCESS, "撤销状态错误");
    }


    @Test
    public void testFab() {
        /**
         * fundRequest.req:CmfRequest[requestBatchNo=<null>,paymentSeqNo=20191111FI000605966,settlementId=20191111002648546,
         * productCode=********,paymentCode=1001,payMode=QUICKPAY,bizType=FUNDIN,instCode=credit001,memberId=100000429655,
         * amount=6499.00,currencyCode=AED,bizTime=Mon Nov 11 14:08:40 CST 2019,fundsChannel=<null>,operator=<null>,
         * extension:COMPANY_OR_PERSONAL=C,DBCR=CC,GATE_ORDER_NO=960ab6e2aad94537af314df1b0678779,idType=IC,
         * CARD_ID=346338,pid=************,ACCESS_CHANNEL=IOS,cardNo=***5705,idNo=*******,BIZ_PRODUCT_CODE=200101,
         * maCardType=2,paymentOrderNo=960ab6e2aad94537af314df1b0678779,payeeId=************,orgiSettlementId=null,
         * orgiFundinOrderNo=null,topayMerchantId=************,topayMerchantName=测试公司,
         */

//        for (String no : new String[]{"20210208FI100905027", "20210208FI100905028", "20210208FI100905029", "20210208FI100905030"}) {
        CmfRequest request = buildRequest("20210208FI100905030");

        OperationEnvironment environment = new OperationEnvironment();
        CmfFundResult fundResult = fundRequestFacade.apply(request, environment);
        System.out.println("fundResult:" + fundResult);
//        }
    }


    @Test
    public void testTQPay() {
        /**
         * fundRequest.req:CmfRequest[requestBatchNo=<null>,paymentSeqNo=20191111FI000605966,settlementId=20191111002648546,
         * productCode=********,paymentCode=1001,payMode=QUICKPAY,bizType=FUNDIN,instCode=credit001,memberId=100000429655,
         * amount=6499.00,currencyCode=AED,bizTime=Mon Nov 11 14:08:40 CST 2019,fundsChannel=<null>,operator=<null>,
         * extension:COMPANY_OR_PERSONAL=C,DBCR=CC,GATE_ORDER_NO=960ab6e2aad94537af314df1b0678779,idType=IC,
         * CARD_ID=346338,pid=************,ACCESS_CHANNEL=IOS,cardNo=***5705,idNo=*******,BIZ_PRODUCT_CODE=200101,
         * maCardType=2,paymentOrderNo=960ab6e2aad94537af314df1b0678779,payeeId=************,orgiSettlementId=null,
         * orgiFundinOrderNo=null,topayMerchantId=************,topayMerchantName=测试公司,
         */

        CmfRequest request = buildRequest("20211101FI00000025");

        OperationEnvironment environment = new OperationEnvironment();
        CmfFundResult fundResult = fundRequestFacade.apply(request, environment);
        System.out.println("fundResult:" + fundResult);
    }

    @Test
    public void testKlip(){
        CmfRequest request = buildRequest("20220808FI001028923");
        request.setInstCode("KLIP");
        request.setPaymentCode("1001");
        Extension extension = request.getExtension();
        extension.add("city","Abu Dhabi");
        extension.add("countryCode","UAE");
        extension.add("mid","202204070002");
        extension.add("terminalId","22080401");
        extension.add("mcc","9211");
        extension.add("merchantName","wwwwwww");
        CmfFundResult result = fundRequestFacade.apply(request, new OperationEnvironment());
        System.out.println(result);

    }

    @Test
    public void testLean(){
        String paymentOrderNo = LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE) + "FI"
                + LocalTime.now().format(DateTimeFormatter.ISO_TIME).replaceAll(":|/+", "");
        CmfRequest request = buildRequest(paymentOrderNo);
        request.setInstCode("LEAN");
        request.setPaymentCode("1001");
        request.setAmount(new Money(new BigDecimal("119.30"), "AED"));
        Extension extension = new Extension();
        extension.add("payment_destination_id", "41473865-e7a8-41a7-b4f4-d2cbd1352b07");
        extension.add("customer_id", "9ae18ef1-c075-42c5-8841-90a49d5c39c3");
        request.setExtension(extension);

        CmfFundResult result = fundRequestFacade.apply(request, new OperationEnvironment());
        System.out.println(result);
    }

    @Test
    public void test43ds2() {
        // 测试3ds2.0
        CmfRequest request = buildRequest("20220221FI" + genRandomId(8), MOCK_CHANNEL_3DS2).apply("");
        CmfFundResult fundResult = fundRequestFacade.apply(request, EnvUtil.getEnv());
        Extension ext = fundResult.getExtension();
        Assert.isTrue(fundResult.getResultCode() == CmfFundResultCode.REQUEST_SUCCESS
                && StringUtils.isNotEmpty(TestUtils.getExtVal(ext,INST_ORDER_TOKEN)) && StringUtils.isNotEmpty(TestUtils.getExtVal(ext,"PAGE_URL"))
                && TestUtils.getExtVal(ext, "PAGE_URL").startsWith("http"));

    }

    public static Function<String, CmfRequest> buildRequest(String paymentSeqNo, String whiteChannelCode) {
        return (req) -> {
            CmfRequest request = new CmfRequest();
            request.setInstCode("TEST");
            request.setPaymentSeqNo(paymentSeqNo);
            request.setSettlementId(paymentSeqNo.replace("FI", ""));
            request.setProductCode("********");
            request.setPaymentCode("1001");
            request.setPayMode(PayMode.QUICKPAY);
            request.setBizType(BizType.FUNDIN);
            request.setMemberId("100000429655");
            request.setAmount(new Money("3000.1", "AED"));
            request.setBizTime(new Date());
            Extension extension = new Extension();
            extension.add("COMPANY_OR_PERSONAL", "C");
            extension.add("DBCR", "DC");
            extension.add("WHITE_CHANNEL_CODE", whiteChannelCode);
            request.setExtension(extension);

            return request;
        };
    }


    private CmfRequest buildRequest(String paymentSeqNo) {
        CmfRequest request = new CmfRequest();
        request.setInstCode("ICBC");
        request.setPaymentSeqNo(paymentSeqNo);
        request.setSettlementId(paymentSeqNo.replace("FI", ""));
        request.setProductCode("********");
        request.setPaymentCode("1001");
        request.setPayMode(PayMode.QUICKPAY);
        request.setBizType(BizType.FUNDIN);
        request.setMemberId("100000429655");
        request.setAmount(new Money("3000.1", "AED"));
        request.setBizTime(new Date());
        Extension extension = new Extension();
        extension.add("COMPANY_OR_PERSONAL", "C");
        extension.add("DBCR", "DC");
        extension.add("title", "JohnDoe And Co.");
        extension.add("cc_first_name", "P70260");
        extension.add("cc_last_name", "P70259");
        extension.add("cc_phone_number", "00971");
        extension.add("payerIp", "***********");
        extension.add("paymentOrderNo", "312019122721400316");
        extension.add("email", "<EMAIL>");
        extension.add("products_per_title", "MobilePhone || Charger || Camera");
        extension.add("unit_price", "12.123 || 21.345 || 35.678");
        extension.add("quantity", "2 || 3 || 1");
        extension.add("discount", "0.00");
        extension.add("other_charges", "0.00");
        extension.add("billing_address", "Flat 3021 Manama Bahrain");
        extension.add("state", "Manama");
        extension.add("city", "Manama");
        extension.add("postal_code", "12345");
        extension.add("country", "BHR");
        extension.add("shipping_first_name", "P70260");
        extension.add("state_shipping", "manama");
        extension.add("shipping_last_name", "P70259");
        extension.add("address_shipping", "Manama");
        extension.add("city_shipping", "Manama");
        extension.add("postal_code_shipping", "1234");
        extension.add("country_shipping", "BHR");
        extension.add("reference_no", "122333");
        extension.add("payerIp", "************");
        extension.add("is_existing_customer", "FALSE");
        extension.add("cardToken", "*****************");
        request.setExtension(extension);
        return request;
    }

    @Test
    public void testTap() {
        String suffix = "11";
        CmfRequest request = new CmfRequest();
//        request.setCallbackServerUrl("http://sim.intra.test2pay.com/fcw/server/TAP102-VS/notify.html");
//        request.setCallbackPageUrl("http://sim.intra.test2pay.com/fcw/page/TAP102-VS/notify.html");
        request.setInstCode("TAP");
        request.setPaymentSeqNo("20212021FI1000000" + suffix);
        request.setSettlementId("***************" + suffix);
        request.setProductCode("********");
        request.setPaymentCode("1001");
        request.setPayMode(PayMode.NETBANK);
        request.setBizType(BizType.FUNDIN);
        request.setMemberId("***********");
        request.setAmount(new Money("0.1", "AED"));
        request.setBizTime(new Date());
        Extension extension = new Extension();
        extension.add("COMPANY_OR_PERSONAL", "C");
        extension.add("DBCR", "DC");
        extension.add("paymentOrderNo", "****************" + suffix);
//        extension.add("email", "<EMAIL>");
        extension.add("mobileNo", "*********");
//        extension.add("name", "qian");
        extension.add("cardToken", "*****************");
        // auth_Ho495820211435i2F41602608
        request.setExtension(extension);
        System.out.println(request);
        CmfFundResult resp = fundRequestFacade.apply(request, new OperationEnvironment());
        System.out.println(resp);
    }

    @Test
    public void testCredit() {
        /**
         * fundRequest.req:CmfRequest[requestBatchNo=<null>,paymentSeqNo=20191111FI000605966,settlementId=20191111002648546,
         * productCode=********,paymentCode=1001,payMode=QUICKPAY,bizType=FUNDIN,instCode=credit001,memberId=100000429655,
         * amount=6499.00,currencyCode=AED,bizTime=Mon Nov 11 14:08:40 CST 2019,fundsChannel=<null>,operator=<null>,
         * extension:COMPANY_OR_PERSONAL=C,DBCR=CC,GATE_ORDER_NO=960ab6e2aad94537af314df1b0678779,idType=IC,
         * CARD_ID=346338,pid=************,ACCESS_CHANNEL=IOS,cardNo=***5705,idNo=*******,BIZ_PRODUCT_CODE=200101,
         * maCardType=2,paymentOrderNo=960ab6e2aad94537af314df1b0678779,payeeId=************,orgiSettlementId=null,
         * orgiFundinOrderNo=null,topayMerchantId=************,topayMerchantName=测试公司,
         */

        CmfRequest request = new CmfRequest();
        request.setInstCode("CMB");
        request.setPaymentSeqNo("20191111FI100905972");
        request.setSettlementId("20191111002648543");
        request.setProductCode("********");
        request.setPaymentCode("1001");
        request.setPayMode(PayMode.QUICKPAY);
        request.setBizType(BizType.FUNDIN);
        request.setMemberId("100000429655");
        request.setAmount(new Money("6499.00", "FRF"));
        request.setBizTime(new Date());
        Extension extension = new Extension();
        extension.add("COMPANY_OR_PERSONAL", "C");
        extension.add("DBCR", "DC");
        extension.add("GATE_ORDER_NO", "960ab6e2aad94537af314df1b0678779");
        extension.add("idType", "IC");
        extension.add("CARD_ID", "346338");
        extension.add("pid", "************");
        extension.add("ACCESS_CHANNEL", "IOS");
        extension.add("cardNo", "***5705");
        extension.add("idNo", "*******");
        extension.add("BIZ_PRODUCT_CODE", "200101");
        extension.add("maCardType", "2");
        extension.add("paymentOrderNo", "960ab6e2aad94537af314df1b0678779");
        extension.add("payeeId", "************");
        extension.add("topayMerchantId", "************");
        extension.add("topayMerchantName", "测试公司");
        request.setExtension(extension);
        OperationEnvironment environment = new OperationEnvironment();
        CmfFundResult fundResult = fundRequestFacade.apply(request, environment);
        System.out.println("fundResult:" + fundResult);
    }


    @Test
    public void testFundOut() {
        /**
         * fundRequest.req:CmfRequest[requestBatchNo=<null>,paymentSeqNo=20191111FI000605966,settlementId=20191111002648546,
         * productCode=********,paymentCode=1001,payMode=QUICKPAY,bizType=FUNDIN,instCode=credit001,memberId=100000429655,
         * amount=6499.00,currencyCode=AED,bizTime=Mon Nov 11 14:08:40 CST 2019,fundsChannel=<null>,operator=<null>,
         * extension:COMPANY_OR_PERSONAL=C,DBCR=CC,GATE_ORDER_NO=960ab6e2aad94537af314df1b0678779,idType=IC,
         * CARD_ID=346338,pid=************,ACCESS_CHANNEL=IOS,cardNo=***5705,idNo=*******,BIZ_PRODUCT_CODE=200101,
         * maCardType=2,paymentOrderNo=960ab6e2aad94537af314df1b0678779,payeeId=************,orgiSettlementId=null,
         * orgiFundinOrderNo=null,topayMerchantId=************,topayMerchantName=测试公司,
         */


        OperationEnvironment environment = new OperationEnvironment();
        for (int i = 0; i < 5; i++) {
            CmfRequest request = buildRequest();

            CmfFundResult fundResult = fundRequestFacade.apply(request, environment);
            System.out.println("fundResult:" + fundResult);
        }
    }

    CmfRequest buildRequest() {
        CmfRequest request = new CmfRequest();
        request.setInstCode("FAB");
        request.setPaymentSeqNo(genRandomId(10));
        request.setSettlementId(genRandomId(10));
        request.setProductCode("********");
        request.setPaymentCode("1001");
        request.setPayMode(PayMode.BALANCE);
        request.setBizType(BizType.FUNDOUT);
        request.setMemberId("100000429655");
        request.setAmount(new Money("12.31", "AED"));
        request.setBizTime(new Date());
        Extension extension = new Extension();
        extension.add("COMPANY_OR_PERSONAL", "C");
        extension.add("DBCR", "DC");
        extension.add("GATE_ORDER_NO", "960ab6e2aad94537af314df1b0678779");
        extension.add("idType", "IC");
        extension.add("CARD_ID", "346338");
        extension.add("pid", "************");
        extension.add("ACCESS_CHANNEL", "IOS");
        extension.add("cardType", "DC");
        extension.add("iban", "***********************");
        extension.add("bankName", "First Abu Dhabi Bank PJSC");
        extension.add("swiftCode", "NBADAEAAXXX");
        extension.add("accountName", "JIE SUN");
        extension.add("BIZ_PRODUCT_CODE", "200101");
        extension.add("maCardType", "2");
        extension.add("paymentOrderNo", "960ab6e2aad94537af314df1b0678779");
        extension.add("payeeId", "************");
        extension.add("topayMerchantId", "************");
        extension.add("topayMerchantName", "测试公司");
        request.setExtension(extension);
        return request;
    }


    @Test
    public void testApply() {
        CmfRequest request = new CmfRequest();
        request.setInstCode("ICBC");
        request.setPayMode(PayMode.QUICKPAY);
        request.setAmount(new Money("10.10", "AED"));
        Extension extension = new Extension();
        extension.add("verificationCode", "123456");
        extension.add("sourceOrder", "inst");
        request.setExtension(extension);

        OperationEnvironment environment = new OperationEnvironment();
        CmfFundResult fundResult = fundRequestFacade.apply(request, environment);
        System.out.println("fundResult:" + fundResult);
    }

    @Test
    public void testAllowRefund() {
        CmfRequest request = new CmfRequest();
        request.setInstCode("ICBC");
        request.setPayMode(PayMode.QUICKPAY);
        request.setPaymentSeqNo(genRandomId(10));
        request.setSettlementId(genRandomId(10));
        request.setAmount(new Money("1", "AED"));
        request.setBizType(BizType.REFUND);
        // request.setPaymentSeqNo("20191104RI2836539227");
        // request.setSettlementId("201911041102199227");
        request.setBizTime(new Date());
        request.setPaymentCode("1001");
        request.setProductCode("********");
        request.setMemberId("100000000123");

        Extension extension = new Extension();
        extension.add("ORGI_FUNDIN_ORDER_NO", "20191104FI2836538094");
        extension.add("orgiSettlementId", "201911041102188093");
        extension.add("orgiFundinOrderNo", "20191104FI2836538094");
        request.setExtension(extension);

        testRefund(request, (fundResult) -> "原订单不可退款".equals(fundResult.getResultMessage())
                && fundResult.getResultCode() == CmfFundResultCode.FAILED, "退款校验异常，请检查代码");
    }

    public void testRefund(CmfRequest request, Predicate<CmfFundResult> predicate, String msg) {
        System.out.println("cmfRequest:" + request);
        CmfFundResult fundResult = fundRequestFacade.refund(request, new OperationEnvironment());
        System.out.println("fundResult:" + fundResult);
        if (!predicate.test(fundResult)) {
            throw new TestException(msg);
        } else {
            System.out.println("本次任务执行通过");
        }
    }

    @Test
    public void testMC() {
        CmfRequest request = buildMCRequest();

        OperationEnvironment environment = new OperationEnvironment();
        CmfFundResult fundResult = fundRequestFacade.apply(request, environment);
        System.out.println("fundResult:" + fundResult);
    }

    private CmfRequest buildMCRequest() {
        CmfRequest request = new CmfRequest();
        request.setPaymentSeqNo(genRandomId(10));
        request.setSettlementId(genRandomId(10));
        request.setProductCode("********");
        request.setPaymentCode("1001");
        request.setPayMode(PayMode.NETBANK);
        request.setBizType(BizType.FUNDIN);
        request.setMemberId("************");
        request.setAmount(new Money("0.1", "AED"));
        request.setBizTime(new Date());
        Extension extension = new Extension();
        extension.add("GATE_ORDER_NO", "960ab6e2aad94537af314df1b0678779");
        extension.add("pid", "************");
        extension.add("ACCESS_CHANNEL", "IOS");
        extension.add("BIZ_PRODUCT_CODE", "200101");
        extension.add("maCardType", "2");
        extension.add("paymentOrderNo", "960ab6e2aad94537af314df1b0678779");
        extension.add("memberId", "************");
        extension.add("topayMerchantId", "************");
        extension.add("topayMerchantName", "测试公司");
        extension.add("purpose", "Payment Test");
        extension.add("cardToken", "*****************");
        request.setExtension(extension);
        return request;
    }

}
