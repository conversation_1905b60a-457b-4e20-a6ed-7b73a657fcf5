package com.uaepay.cmf.test.service.mock;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.channel.cards.service.facade.BankFacade;
import com.uaepay.channel.cards.service.facade.domain.BankVO;
import com.uaepay.channel.cards.service.facade.domain.request.BankQueryRequest;
import com.uaepay.channel.cards.service.facade.domain.request.CreateRequest;
import com.uaepay.channel.cards.service.facade.domain.request.PkQueryRequest;
import com.uaepay.channel.cards.service.facade.domain.request.UpdateRequest;
import com.uaepay.channel.cards.service.facade.domain.response.CreateResult;
import com.uaepay.channel.cards.service.facade.domain.response.OperationResult;
import com.uaepay.channel.cards.service.facade.domain.response.PkQueryResult;
import com.uaepay.channel.cards.service.facade.domain.response.QueryResult;
import org.apache.dubbo.config.annotation.Service;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date MockBankFacade.java v1.0  2020-10-20 11:19
 */
@Service
public class MockBankFacade implements BankFacade {
    @Override
    public QueryResult<BankVO> queryByCondition(BankQueryRequest bankQueryRequest) {
        return null;
    }

    @Override
    public PkQueryResult<BankVO> queryByBankCode(PkQueryRequest<String> pkQueryRequest) {
        PkQueryResult<BankVO> result = new PkQueryResult<>();
        BankVO bank = new BankVO();
        if("NBAD".equals(pkQueryRequest.getPk())){
            bank.setCountryCode("AE");
            bank.setBankCode("NBAD");
            bank.setBankName("First Abu Bank");
        }
        result.setApplyStatus(ApplyStatusEnum.SUCCESS);
        result.setItem(bank);

        return result;
    }

    @Override
    public CreateResult create(CreateRequest<BankVO> createRequest) {
        return null;
    }

    @Override
    public OperationResult update(UpdateRequest<BankVO> updateRequest) {
        return null;
    }
}
