package com.uaepay.cmf.test.service.cases.function;

import com.uaepay.cmf.domainservice.batch.processor.BatchOrderArchiveService;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import org.junit.jupiter.api.Test;
import org.springframework.data.annotation.ReadOnlyProperty;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>InstOrderRepositoryTests</p>
 *
 * <AUTHOR>
 * @version InstOrderRepositoryTests.java v1.0  2022/9/19 11:31
 */
public class InstOrderRepositoryTests extends ApplicationTest {

    @Resource
    private BatchOrderArchiveService batchOrderArchiveService;


    @Resource
    private InstOrderRepository instOrderRepository;

    @Test
    void testArchive() {
        List<Long> ids = new ArrayList<>();
        ids.add(20220913000447076L);
        ids.add(20220913000447077L);
        batchOrderArchiveService.archiveOrder(new ChannelApiVO());

    }

    @Test
    void testQueryByIds() {
        instOrderRepository.loadInstOrderListByIds(Arrays.asList(1L, 2L));
    }

    @Test
    void testQueryByEmptyIds() {
        instOrderRepository.loadInstOrderListByIds(new ArrayList<>());
    }
}