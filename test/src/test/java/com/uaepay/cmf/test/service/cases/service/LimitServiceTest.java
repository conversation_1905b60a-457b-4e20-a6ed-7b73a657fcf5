package com.uaepay.cmf.test.service.cases.service;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.domainservice.channel.limit.LimitService;
import com.uaepay.cmf.domainservice.channel.limit.impl.LimitServiceImpl;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.integration.config.TransformConfig;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date LimitServiceTest.java v1.0
 */
@RunWith(SpringRunner.class)
public class LimitServiceTest extends ApplicationTest {

    @Resource
    private LimitServiceImpl limitService;

    @Value("${escrow.notify.transform.versionTag}")
    private String versionTag;

    @Resource
    private TransformConfig transformConfig;

    /**
     * 将versionTag设置为Y之后成立
     */
    @Test
    public void testLimitVersion2(){

        CmfOrder cmfOrder = new CmfOrder();
        cmfOrder.setOrderSeqNo("20220405000345911");
        cmfOrder.setGmtCreate(new Date());
        cmfOrder.setBizType(BizType.FUNDIN);
        cmfOrder.getExtension().put("bizProductCode", "230101");
        cmfOrder.getExtension().put("memberId", "1020340234");
        cmfOrder.getExtension().put("svaStatus", "N");
        cmfOrder.setAmount(new Money("4888.34", "AED"));
        boolean res = limitService.validateLimit(cmfOrder);
        if (versionTag.equals("Y")){
            Assert.isTrue(res, "除260291，260202之外均不走限额");
        }else {
            Assert.isTrue(!res, "版本1：除230101以外走限额，校验KYC");
        }
        cmfOrder.getExtension().put("bizProductCode", "260291");
        res = limitService.validateLimit(cmfOrder);
        Assert.isTrue(!res, "金融类进行KYC校验");
        cmfOrder.getExtension().put("bizProductCode", "260202");
        res = limitService.validateLimit(cmfOrder);
        Assert.isTrue(!res, "金融类进行KYC校验");
        cmfOrder.getExtension().put("bizProductCode", "230204");
        cmfOrder.getExtension().put("svaStatus", "V");
        res = limitService.validateLimit(cmfOrder);
        Assert.isTrue(!res, "汇款类进行KYC校验");

    }

    @Test
    public void testLimit() {
        CmfOrder cmfOrder = new CmfOrder();
        cmfOrder.setOrderSeqNo("20220405000345911");
        cmfOrder.setGmtCreate(new Date());
        cmfOrder.setBizType(BizType.FUNDIN);
        cmfOrder.getExtension().put("bizProductCode", "230202");
        cmfOrder.getExtension().put("memberId", "1020340234");
        cmfOrder.getExtension().put("svaStatus", "Y");
        cmfOrder.setAmount(new Money("4888.34", "AED"));
        boolean res = limitService.validateLimit(cmfOrder);
        System.out.println(res);
        Assert.isTrue(!res, "校验失败");
        cmfOrder.getExtension().put("svaStatus", "N");
        res = limitService.validateLimit(cmfOrder);
        Assert.isTrue(!res, "校验失败");
        cmfOrder.setBizType(BizType.FUNDOUT);
        res = limitService.validateLimit(cmfOrder);
        Assert.isTrue(res, "校验失败");
    }

    @Test
    public void testValidateLimitRate() {
        CmfOrder cmfOrder = new CmfOrder();
        cmfOrder.setOrderSeqNo("20220405000345911");
        cmfOrder.setPaymentSeqNo("20220620FI000985876");
        cmfOrder.setSettlementId("20220620003595276");
        cmfOrder.setProductCode("60040040");
        cmfOrder.setPaymentCode("1001");
        cmfOrder.setPayMode(PayMode.TOKENPAY);
        cmfOrder.setGmtCreate(new Date());
        cmfOrder.setBizType(BizType.FUNDIN);
        cmfOrder.setInstCode("BBME");
        cmfOrder.setMemberId("100000326743");
        cmfOrder.setBizTime(new Date());
        cmfOrder.getExtension().put("companyOrPersonal", "C");
        cmfOrder.getExtension().put("dbcr", "DC");
        cmfOrder.getExtension().put("gateOrderNo", "311655715555490581");
        cmfOrder.getExtension().put("accessChannel", "ANDROID");
        cmfOrder.getExtension().put("cardId", "115624");
        cmfOrder.getExtension().put("bizProductCode", "260291");
        cmfOrder.getExtension().put("svaStatus", "Y");
        cmfOrder.setAmount(new Money("2.08", "AED"));
        for (int i = 0; i < 10000; i++) {
            boolean res = limitService.validateLimit(cmfOrder);
            System.out.println(res);
        }

    }

    private static final Map<String, Integer> RATIO_MAP = new HashMap<String, Integer>() {{
        put("230101", 100);
        put("260291", 10);
    }};
    static final Integer TEST_TIMES = 10000;

    @Test
    public void testRatio() {
        String bizCode = "203101";
        for (Map.Entry<String, Integer> entry : RATIO_MAP.entrySet()) {
            int pass = 0;
            for (int i = 0; i < TEST_TIMES; i++) {
                boolean resp = limitService.validateRatio(transformConfig, entry.getKey());
                if (resp) {
                    pass++;
                }
            }
            System.out.println(pass);
            Double passRate = (double) pass / Double.valueOf(TEST_TIMES) * 100;
            Assert.isTrue((entry.getValue() == 100 && (int) pass == 100) || (entry.getValue() * 0.5 <= passRate && entry.getValue() * 1.5 >= passRate), String.format("通过率出错:%s_%s", entry.getValue(), passRate));
        }
    }

    @Resource
    private InstOrderRepository instOrderRepository;

    @Test
    public void testRecordFlow2() {
        InstOrder instOrder = instOrderRepository.loadByNo("T91202204140018303");
        boolean result = limitService.recordFlow(instOrder);
        Assert.isTrue(result, "failed");
    }

    @Test
    public void testRecordFlow() {
        InstOrder instOrder = new InstOrder();
        instOrder.setStatus(InstOrderStatus.FAILURE);
        instOrder.setBizType(BizType.FUNDIN);
        instOrder.setFundChannelCode("MC101");
        instOrder.setGmtCreate(new Date());
        instOrder.setAmount(new Money("25.34", "AED"));
        instOrder.getExtension().put(ExtensionKey.PAYMENT_ORDER_NO.getKey(), genRandomId(10));
        instOrder.getExtension().put("bizProductCode", "230202");
        instOrder.getExtension().put("memberId", "100000338200");
        instOrder.getExtension().put("svaStatus", "Y");
        boolean status = limitService.recordFlow(instOrder);
        Assert.isTrue(!status, "校验失败");
        instOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        status = limitService.recordFlow(instOrder);
        Assert.isTrue(status, "校验失败");
        instOrder.setAmount(new Money("20005.34", "AED"));
        status = limitService.recordFlow(instOrder);
        Assert.isTrue(status, "校验失败");
        instOrder.setAmount(new Money("20005.34", "AED"));
        status = limitService.recordFlow(instOrder);
        Assert.isTrue(status, "校验失败");
        CmfOrder cmfOrder = new CmfOrder();
        cmfOrder.setOrderSeqNo(genRandomId(11));
        cmfOrder.setGmtCreate(new Date());
        cmfOrder.setBizType(BizType.FUNDIN);
        cmfOrder.setAmount(new Money("5000", "AED"));
        cmfOrder.getExtension().put(ExtensionKey.PAYMENT_ORDER_NO.getKey(), genRandomId(10));
        cmfOrder.getExtension().put("bizProductCode", "230202");
        cmfOrder.getExtension().put("memberId", "100000338200");
        cmfOrder.getExtension().put("svaStatus", "Y");
        boolean res = limitService.validateLimit(cmfOrder);
        Assert.isTrue(!res, "校验失败");

        // 测试退款流量
        instOrder.setBizType(BizType.REFUND);
        instOrder.setAmount(new Money("20055.34", "AED"));
        status = limitService.recordFlow(instOrder);
        Assert.isTrue(status, "校验失败");

        res = limitService.validateLimit(cmfOrder);
        Assert.isTrue(res, "校验失败");


    }


}
