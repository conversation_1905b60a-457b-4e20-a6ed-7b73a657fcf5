package com.uaepay.cmf.test.ext.task;

import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolderImpl;
import com.uaepay.cmf.fss.ext.resend.newtask.SingleSendTask;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.schema.cmf.enums.BizType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
public class SingleSendTaskTest extends ApplicationTest {
    @Resource
    private SingleSendTask singleSendTask;

    @Resource
    SysConfigurationHolderImpl sysConfigurationHolder;

    @Test
    public void testExecuteTask(){
        sysConfigurationHolder.refreshCache();
        Boolean result = singleSendTask.executeTask(BizType.FUNDOUT);
        System.out.println(result);
    }
}
