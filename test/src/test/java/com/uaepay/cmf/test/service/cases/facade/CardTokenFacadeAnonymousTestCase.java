package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository;
import com.uaepay.cmf.fss.ext.integration.cashdesk.CashdeskClient;
import com.uaepay.cmf.service.facade.api.CardTokenFacade;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateRequest;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateResult;
import com.uaepay.cmf.service.facade.domain.card.CardTokenQueryResult;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.cmf.test.base.ShareDataBean;
import com.uaepay.cmf.test.service.builder.CardTokenRequestBuilder;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.core.annotation.Order;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenFacadeTestCase.java v1.0  2020-09-14 13:55
 */
@DisplayName("卡token测试")
@RunWith(SpringRunner.class)
public class CardTokenFacadeAnonymousTestCase extends ApplicationTest {

    @Resource
    private CardTokenFacade cardTokenFacade;

    @Resource
    private CashdeskClient cashdeskClient;

    @Resource
    private CardTokenRepository cardTokenRepository;

    @Test
    @Order(1)
    @DisplayName("1.创建token记录")
    public void testCreate() {
        String csc = "177";
        CardTokenCreateRequest cardTokenRequest = CardTokenRequestBuilder.createAnonymousCreateRequest(csc);
        CardTokenCreateResult result = cardTokenFacade.create(cardTokenRequest);
        Assert.isTrue(result.getApplyStatus() == ApplyStatusEnum.SUCCESS, "创建card token失败");
        ShareDataBean.addData(CARD_TOKEN, result.getCardTokenId());
        ShareDataBean.addData(CSC, csc);
        CardTokenCreateRequest updateRequest = CardTokenRequestBuilder.createAnonymousUpdateRequest(result.getCardTokenId());
        CardTokenCreateResult updateResult = cardTokenFacade.create(updateRequest);
        Assert.isTrue(updateResult.getApplyStatus() == ApplyStatusEnum.SUCCESS, "更新card token失败");
        ShareDataBean.addData(CARD_TOKEN, updateResult.getCardTokenId());

    }

    @Test
    @Order(2)
    @DisplayName("2.更新token记录")
    public void testUpdate() {
        String cardTokenId = "20210112100032070"; // ShareDataBean.getData(CARD_TOKEN)
        CardTokenCreateRequest cardTokenRequest = CardTokenRequestBuilder.createAnonymousUpdateRequest(cardTokenId);
        CardTokenCreateResult result = cardTokenFacade.create(cardTokenRequest);
        Assert.isTrue(result.getApplyStatus() == ApplyStatusEnum.SUCCESS, "更新card token失败");
        ShareDataBean.addData(CARD_TOKEN, result.getCardTokenId());

    }

    @Test
    @Order(3)
    @DisplayName("3.查询token记录")
    public void testQuery() {
        CardTokenQueryResult result = cardTokenFacade.query(ShareDataBean.getData(CARD_TOKEN));
        System.out.println(result);
        Assert.isTrue(result.getApplyStatus() == ApplyStatusEnum.SUCCESS, "查询card token失败");
    }

//    @Test
//    public void testNotify() {
//        BindCardRequest request = new BindCardRequest();
//        request.setCardBrand("VISA");
//        request.setCardNo("1234xxxx123132");
//        cashdeskClient.sendBindCardRequest(request);
//    }
//
//    @Test
//    public void queryCardToken() {
//        CardToken cardToken = cardTokenRepository.queryByInstOrderId(20200330000297504L);
//        System.out.println(cardToken);
//    }


}
