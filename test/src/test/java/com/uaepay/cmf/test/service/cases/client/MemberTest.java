package com.uaepay.cmf.test.service.cases.client;

import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.member.service.facade.IMemberCardFacade;
import com.uaepay.member.service.request.card.QueryCardByCardIdRequest;
import com.uaepay.member.service.response.card.QueryCardByCardIdResponse;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date MemberTest.java v1.0
 */
@RunWith(SpringRunner.class)
public class MemberTest extends ApplicationTest {

    @Reference
    private IMemberCardFacade iMemberCardFacade;

    @Test
    public void test() {
        QueryCardByCardIdRequest request = new QueryCardByCardIdRequest();
        request.setCardId(79103L);
        request.setQueryDisabledFlag(false);
        QueryCardByCardIdResponse resp = iMemberCardFacade.queryCardByCardId(request);
        System.out.println(resp);
    }

}
