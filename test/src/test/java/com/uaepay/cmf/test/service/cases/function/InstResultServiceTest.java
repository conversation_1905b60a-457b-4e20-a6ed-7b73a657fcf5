package com.uaepay.cmf.test.service.cases.function;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.result.ControlResultProcessor;
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date InstResultServiceTest.java v1.0  2020-04-07 17:04
 */
@RunWith(SpringRunner.class)
public class InstResultServiceTest extends ApplicationTest {

    @Resource
    private InstResultProcessor instResultProcessor;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private ControlResultProcessor controlResultProcessor;

    @Test
    public void testResult() {
        String instOrderNo = "10020200406155304009";
        InstOrder instOrder = instOrderRepository.loadByNo(instOrderNo);

        ChannelFundResult channelFundResult = buildResult();
        InstOrderResult instOrderResult = instResultProcessor.process(instOrder,
                channelFundResult);
        System.out.println(instOrderResult);
    }

    /**
     * result=ChannelFundBatchResult(super=ChannelFundResult(super=ChannelResult(super=com.uaepay.cmf.common.domain.ChannelFundBatchResult@1b49322c,
     * apiType=BATCH_QUERY, resultCode=null, instOrderNo=10120200406155400040, instReturnOrderNo=null, apiResultCode=in_process, apiResultSubCode=null,
     * apiResultMessage=null, apiResultSubMessage=null, extension=null), realAmount=AED:0.02, instSettleTime=null, processTime=null, instUrl=null,
     * fundChannelCode=FAB212), archiveBatchId=20200406000058893, fundResultList=
     * [ChannelFundResult(super=ChannelResult(super=com.uaepay.cmf.common.domain.ChannelFundResult@2776bb96,
     * apiType=BATCH_QUERY, resultCode=null, instOrderNo=10020200406155304009, instReturnOrderNo=null, apiResultCode=in_process,
     * apiResultSubCode=Success, apiResultMessage=null, apiResultSubMessage=null, extension=null), realAmount=AED:0.02,
     * instSettleTime=null, processTime=null, instUrl=null, fundChannelCode=FAB212)])
     *
     * @return
     */
    private ChannelFundResult buildResult() {
        ChannelFundResult result = new ChannelFundResult();
        result.setApiType(FundChannelApiType.BATCH_QUERY);
        result.setInstOrderNo("10020200406155304009");
        result.setApiResultCode("in_process");
        result.setApiResultSubCode("Success");
        result.setRealAmount(new Money("0.02", "AED"));
        result.setFundChannelCode("FAB212");
        return result;
    }

}
