package com.uaepay.cmf.test.service.cases.integration;

import com.uaepay.cmf.fss.ext.integration.escrow.EscrowClient;
import com.uaepay.cmf.service.facade.domain.query.SimpleOrder;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.core.annotation.Order;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Random;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CacheFacadeTestCase.java v1.0  2020-09-14 13:43
 */
@DisplayName("MQ测试")
@RunWith(SpringRunner.class)
public class EscrowMqTestCase extends ApplicationTest {

    @Resource
    private EscrowClient escrowClient;

    @Test
    @Order(1)
    @DisplayName("发送mq")
    public void testSendOrder() {

        for (int i = 0; i < 10; i++) {
            SimpleOrder order = new SimpleOrder();
            order.setInstOrderNo(genRandomId(17));
            order.setBizProductCode("123456");
            order.setPaymentOrderNo(genRandomId(17));
            order.setProductOrderNo(genRandomId(17));
            order.setStatus("S");
            order.setFundChannelCode("MC101");
            order.setAmount(new Money("" + new Random().nextInt(4), "AED").divide(100));
            order.setGmtSubmit(new Date());
            order.setBizType("I");
            order.setMemberId("10" + genRandomId(8));

            escrowClient.sendOrder(order);
            System.out.println("send success");
        }
    }

}
