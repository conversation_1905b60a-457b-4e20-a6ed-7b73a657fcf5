package com.uaepay.cmf.test.service.cases.service;

import com.alibaba.fastjson.JSONObject;
import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade;
import com.uaepay.cmf.fss.ext.integration.proxy.dubbo.DubboChannelFundRemoteProxyImpl;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date DubboTest.java v1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
public class DubboTest extends ApplicationTest {

    @Resource
    private DubboChannelFundRemoteProxyImpl dubboChannelFundRemoteProxy;

    @Test
    public void testTarget() {
        ChannelFundFacade channelFundFacade = dubboChannelFundRemoteProxy.getTarget("localMock");
        Assert.notNull(channelFundFacade, "channelFacade为空");
        ChannelFundRequest request = new ChannelFundRequest();
        request.setFundChannelCode(MOCK_CHANNEL_3DS2);
        request.setAmount(new Money("10.0", "AED"));
        request.setInstOrderNo(genRandomId(10));
        request.setApiType(FundChannelApiType.DEBIT);
        ChannelFundResult result = channelFundFacade.apply(JSONObject.toJSONString(request));
        log.info("channelFundResult:{}", result);
        Assert.isTrue(StringUtils.equals(result.getInstOrderNo(), request.getInstOrderNo()), "返回有误");
    }

}
