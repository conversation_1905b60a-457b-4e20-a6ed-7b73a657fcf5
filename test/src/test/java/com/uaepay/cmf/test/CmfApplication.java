package com.uaepay.cmf.test;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;

/**
 * <p>
 * pns main
 * </p>
 *
 * DataSourceAutoConfiguration:数据源走了jndi所以无需开启
 * DubboApplicationContextInitializer,ZipkinBackwardsCompatibilityAutoConfiguration：
 * 和始化applicationContext.xml有冲突，导致某些bean过早初始化IOC注入为空
 * 
 * <AUTHOR>
 * @version 1.0: CsaApplication, v 0.1 2019-05-17 11:00 Hewj Exp $
 */

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@ImportResource("classpath:META-INF/spring/applicationContext-test.xml")
public class CmfApplication {

}
