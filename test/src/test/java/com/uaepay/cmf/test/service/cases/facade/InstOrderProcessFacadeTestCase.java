package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.service.facade.counter.InstOrderProcessFacade;
import com.uaepay.cmf.service.facade.domain.counter.InstOrderVO;
import com.uaepay.cmf.service.facade.domain.counter.QueryOrderResult;
import com.uaepay.cmf.service.facade.domain.counter.QueryRefundRequest;
import com.uaepay.cmf.service.facade.domain.counter.QueryRefundResponse;
import com.uaepay.cmf.service.facade.domain.fundout.FundOutRetryRequest;
import com.uaepay.cmf.service.facade.domain.fundout.UpdateBookingTimeRequest;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date InstOrderProcessFacadeTestCase.java v1.0  2020-09-14 13:58
 */
@RunWith(SpringRunner.class)
public class InstOrderProcessFacadeTestCase extends ApplicationTest {

    @Resource
    private InstOrderProcessFacade instOrderProcessFacade;

    @Test
    public void testProcess() {
        InstOrderVO resp = instOrderProcessFacade.getInstOrder("TEST2001180900290006");
        System.out.println(resp);

    }

    @Test
    public void queryInstOrder() {
        String orderNo = "T91202011130000347";
        QueryOrderResult result = instOrderProcessFacade.queryInstOrderResult(orderNo);
        System.out.println(result);
    }

    @Test
    public void updateBookingTime(){
        UpdateBookingTimeRequest request = new UpdateBookingTimeRequest();
        request.setGmtBookingSubmit(DateUtil.addDays(new Date(), 1));
        request.setInstOrderNo("T102021091500023659");
        request.setClientId("test");
        CommonResponse resp = instOrderProcessFacade.updateGmtBooking(request);
        Assert.isTrue(resp.getApplyStatus()== ApplyStatusEnum.SUCCESS, "请求失败");
        // 校验失败的情况

    }

    @Test
    public void fundoutRetry(){
        FundOutRetryRequest request = new FundOutRetryRequest();
        request.setArchiveBatchId(20210915003152299L);
        request.setGmtRetry(DateUtil.addHours(new Date(), 1));
        request.setClientId(CLIENT_ID);
        CommonResponse resp = instOrderProcessFacade.retry(request);
        Assert.isTrue(resp.getApplyStatus()== ApplyStatusEnum.SUCCESS, "请求失败");

        // 校验失败的情况
    }

    @Test
    public void testQueryRefundListByInstOrderNo(){
        QueryRefundRequest request = new QueryRefundRequest();
        request.setInstOrderNo("T91202408230049377");
        QueryRefundResponse response = instOrderProcessFacade.queryRefundListByInstOrderNo(request);
        System.out.println(response);
    }
}
