package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.basis.beacon.service.facade.domain.response.PageResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.service.facade.api.OrderQueryFacade;
import com.uaepay.cmf.service.facade.domain.query.BatchOrderQueryRequest;
import com.uaepay.cmf.service.facade.domain.query.OrderPageQueryRequest;
import com.uaepay.cmf.service.facade.domain.query.PkQueryRequest;
import com.uaepay.cmf.service.facade.domain.query.SimpleOrder;
import com.uaepay.cmf.service.facade.domain.query.order.BatchOrderVO;
import com.uaepay.cmf.service.facade.domain.query.order.ControlOrderVO;
import com.uaepay.cmf.service.facade.result.PkQueryResult;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date OrderQueryFacadeTestCase.java v1.0  2020-09-14 13:58
 */
@RunWith(SpringRunner.class)
public class OrderQueryFacadeTestCase extends ApplicationTest {

    @Resource
    private OrderQueryFacade orderQueryFacade;

    @Test
    public void testQuery() {
        int pageNo = 1;

        while (true) {
            PageResponse<SimpleOrder> resp = orderQueryFacade.queryOrdersByPage(buildRequest(PAGE_SIZE, pageNo, "MC", "S", "20200920000000", "20200929000000"));

            Assert.isTrue(resp.getApplyStatus() == ApplyStatusEnum.SUCCESS, "查询失败");
            System.out.println("times:" + pageNo + ", resp:" + resp);
            if(pageNo > 99 || CollectionUtils.isEmpty(resp.getDataList())) break;
            pageNo++;
        }
    }

    @Test
    public void testQueryBatch(){
        BatchOrderQueryRequest request = new BatchOrderQueryRequest();
        request.setPageNum(1);
        request.setPageSize(100);
        request.setChannelCode("FAB221");
        PageResponse<BatchOrderVO> resp = orderQueryFacade.queryBatchOrder(request);
        System.out.println(resp);
        Assert.isTrue(resp.getApplyStatus()==ApplyStatusEnum.SUCCESS, "请求失败");
        System.out.println(resp.getDataList().size());
    }

    @Test
    public void testQueryControl(){
        PkQueryRequest<String> request = new PkQueryRequest<>();
        request.setPk("2021120699067700000000000");
        request.setClientId(CLIENT_ID);
        PkQueryResult<ControlOrderVO> resp = orderQueryFacade.queryControlOrder(request);
        System.out.println(resp);
    }

    private static OrderPageQueryRequest buildRequest(Integer pageSize, Integer pageNo, String instCode, String status, String gmtStart, String gmtEnd) {
        OrderPageQueryRequest request = new OrderPageQueryRequest();
        request.setPageSize(pageSize);
        request.setPageNum(pageNo);
        request.setInstCode(instCode);
        request.setStatus(status);
        request.setGmtStart(DateUtil.parseDateLongFormat(gmtStart));
        request.setGmtEnd(DateUtil.parseDateLongFormat(gmtEnd));

        return request;
    }

}
