package com.uaepay.cmf.test.service.cases.order;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.service.facade.api.CardTokenFacade;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.api.FundRequestFacade;
import com.uaepay.cmf.service.facade.domain.CmfCommonResultCode;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateRequest;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateResult;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.cmf.test.base.ShareDataBean;
import com.uaepay.cmf.test.service.util.EnvUtil;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.OperationEnvironment;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.core.annotation.Order;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date FundInOrderCase.java v1.0  2020-09-14 13:11
 */
@DisplayName("出款订单测试")
@RunWith(SpringRunner.class)
public class FundOutOrderTestCase extends ApplicationTest {

    @Resource
    private CardTokenFacade cardTokenFacade;

    @Resource
    private FundRequestFacade fundRequestFacade;

    @Resource
    private ControlRequestFacade controlRequestFacade;

    @Resource
    private InstOrderRepository repository;

    @Test
    @Order(0)
    @DisplayName("0 创建card token")
    public void createCardToken() {
        CardTokenCreateRequest cardTokenRequest = createCardTokenRequest();
        CardTokenCreateResult result = cardTokenFacade.create(cardTokenRequest);
        Assert.isTrue(result.getApplyStatus() == ApplyStatusEnum.SUCCESS, "创建card token失败");
        ShareDataBean.addData(CARD_TOKEN, result.getCardTokenId());
    }

    @Test
    @Order(1)
    @DisplayName("1.出款订单同步测试")
    public void fundOut() {
        String cardTokenId = ShareDataBean.getData(CARD_TOKEN);
        CmfRequest request = buildRequst(BizType.FUNDOUT, PayMode.BALANCE, new Money("10", "AED"), cardTokenId);
        CmfFundResult result = fundRequestFacade.apply(request, new OperationEnvironment());
        System.out.println(result);
        ShareDataBean.addData(INST_ORDER, result.getInstOrderNo());

    }

    @Test
    @Order(3)
    @DisplayName("3.出款订单交易撤销")
    public void voidFo() {
        // 正常撤销
        fundOut();
        CmfControlRequest request = buildControlRequest();
        CmfControlResult resp = controlRequestFacade.control(request, EnvUtil.getEnv());
        Assert.isTrue(resp.getResultCode() == CmfCommonResultCode.SUCCESS, "撤销状态不为成功");
        InstOrder instOrder = repository.loadByNo(ShareDataBean.getData(INST_ORDER));
        Assert.isTrue(instOrder.getStatus() == InstOrderStatus.FAILURE, "订单状态不为失败");
        // 撤销一笔已发送渠道的订单
        fundOut();
        request = buildControlRequest();
        instOrder = repository.loadByNo(ShareDataBean.getData(INST_ORDER));
        repository.updateCommunicateStatusWithPreStatus(instOrder, CommunicateStatus.SENT, instOrder.getCommunicateStatus());
        resp = controlRequestFacade.control(request, EnvUtil.getEnv());
        Assert.isTrue(resp.getResultCode() == CmfCommonResultCode.VALIDARE_ERROR, "撤销状态不为校验失败");

        // 撤销一笔预计提交时间已过当前时间，但还没有发送的交易
        fundOut();
        request = buildControlRequest();
        instOrder = repository.loadByNo(ShareDataBean.getData(INST_ORDER));
        repository.updateBookingSubmit(instOrder, new Date());
        resp = controlRequestFacade.control(request, EnvUtil.getEnv());
        Assert.isTrue(resp.getResultCode() == CmfCommonResultCode.VALIDARE_ERROR, "撤销状态不为校验失败");


    }

    private CmfControlRequest buildControlRequest() {
        CmfControlRequest request = new CmfControlRequest();
        request.setPreRequestNo(ShareDataBean.getData(INST_ORDER));
        request.setRequestNo(genRandomId(17));
        request.setRequestType(ControlRequestType.VOID_TRANSACTION);
        Extension ext = new Extension();
        ext.add("sourceOrder", "inst");
        request.setExtension(ext);
        return request;
    }

//    @Test
//    @Order(2)
//    @DisplayName("2.出款dingsh任务发送测试")
//    public void fundOutSend() {
//
//    }
//
//    @Test
//    @DisplayName("出款同步发送")
//    public void fundOutSync() {
//
//    }
//
//    @Test
//    @DisplayName("出款拆单测试")
//    public void fundOutSplit() {
//
//    }

    private CardTokenCreateRequest createCardTokenRequest() {
//        CardTokenFacade.create.request:CardTokenCreateRequest{sessionId='null'
//            cardId=25301
//            memberId='100000045402'
//            instCode='MC'
//            dbcr='DC'
//            companyOrPersonal='C'
//            cardNo='null'
//            cardHolder='null'
//            is3DS='Y'
//            ipAddress='null'
//            iban='***********************'
//            resultUrl='https://sim-mpaypage.test2pay.com/result?ft=28ca273f-b82a-47ee-aa8b-73cb4cd4cb4a'
//            needCsc='null'
//            extension='null'}


        CardTokenCreateRequest cardTokenRequest = new CardTokenCreateRequest();
//        cardTokenRequest.setCardId(25301L);
        cardTokenRequest.setMemberId("100000045402");
        cardTokenRequest.setInstCode("TESQ");
        cardTokenRequest.setDbcr("DC");
        cardTokenRequest.setCompanyOrPersonal("C");
        cardTokenRequest.setIban("***********************");
        cardTokenRequest.setResultUrl("https://sim-mpaypage.test2pay.com/result?ft=28ca273f-b82a-47ee-aa8b-73cb4cd4cb4a");
        return cardTokenRequest;
    }

    private static CmfRequest buildRequst(BizType bizType, PayMode payMode, Money amount, String cardToken) {
        CmfRequest request = new CmfRequest();
        request.setSettlementId(genRandomId(10));
        request.setPaymentSeqNo(genRandomId(10));
        request.setProductCode("60030010");
        request.setPaymentCode("3001");
        request.setBizType(bizType);
        request.setPayMode(payMode);
        request.setInstCode("NBAD");
        request.setMemberId("100000053503");
        request.setAmount(amount);
        Extension extension = new Extension();
        extension.add("cardToken", cardToken);
        extension.add("CARD_TYPE", "DC");
        extension.add("BIZ_PRODUCT_CODE", "220401");
        extension.add("paymentOrderNo", "311991604915466835");
        extension.add("payeeId", "anonymous");
        extension.add("iban", "***********************");
        extension.add("topayMerchantId", "200000000777");
        request.setExtension(extension);
        return request;
    }

}
