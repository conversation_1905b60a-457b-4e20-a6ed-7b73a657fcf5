package com.uaepay.cmf.test.service.cases.processor;

import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstFundinOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.convert.InstControlOrderConverter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.process.SaveBankReceiveService;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.result.ControlResultProcessor;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ControlProcessorTestCase.java v1.0
 */
@Slf4j
@DisplayName("汇款订单测试")
@RunWith(SpringRunner.class)
public class ControlProcessorTestCase extends ApplicationTest {

    @Resource
    private ControlResultProcessor controlResultProcessor;
    @Resource
    private InstControlOrderRepository controlOrderRepository;
    @Resource
    private InstOrderRepository instOrderRepository;
    @Resource
    private ChannelApiRouter apiRouter;
    @Resource
    private SaveBankReceiveService saveBankReceiveService;

    @Test
    @DisplayName("1.测试推进成功")
    public void testAdSuccess() {
        try (ChannelCarrier carrier = apiRouter.route(RouteUtil.getParam(MOCK_CHANNEL_3DS2, FundChannelApiType.ADVANCE_3DS2))) {
            InstOrder instOrder = buildInstOrder();
            InstControlOrder controlOrder = buildControlOrder(instOrder);
            ChannelFundResult result = buildChannelFundResult(controlOrder, "success");
            InstControlOrderResult res = controlResultProcessor.process(controlOrder, result);
            System.out.println("ControlResult:" + res);
            controlOrder = controlOrderRepository.loadByNo(controlOrder.getInstOrderNo());
            Assert.isTrue(controlOrder.getStatus() == InstOrderStatus.SUCCESSFUL, "执行出错");
            instOrder = instOrderRepository.loadByNo(instOrder.getInstOrderNo());
            Assert.isTrue(instOrder.getStatus() == InstOrderStatus.SUCCESSFUL, "执行出错");
            String id = instOrder.getExtension().get("authenticationTransactionId");
            Assert.isTrue("PVQC2LR9ysIquCCSjm31".equals(id), "transId不等");
        } catch (Exception e) {
            log.error("testAdSuccess.error", e);
        }
    }

    @Test
    @DisplayName("2.测试推进失败")
    public void testAdFail() {
        InstOrder instOrder = buildInstOrder();
        InstControlOrder controlOrder = buildControlOrder(instOrder);
        ChannelFundResult result = buildChannelFundResult(controlOrder, "fail");
        InstControlOrderResult res = controlResultProcessor.process(controlOrder, result);
        System.out.println("ControlResult:" + res);
        controlOrder = controlOrderRepository.loadByNo(controlOrder.getInstOrderNo());
        Assert.isTrue(controlOrder.getStatus() == InstOrderStatus.FAILURE, "执行出错");
        instOrder = instOrderRepository.loadByNo(instOrder.getInstOrderNo());
        Assert.isTrue(instOrder.getStatus() == InstOrderStatus.FAILURE, "执行出错");
    }

    @Test
    @DisplayName("3.测试推进处理中")
    public void testAdInProcess() {
        InstOrder instOrder = buildInstOrder();
        InstControlOrder controlOrder = buildControlOrder(instOrder);
        ChannelFundResult result = buildChannelFundResult(controlOrder, "in_process");
        InstControlOrderResult res = controlResultProcessor.process(controlOrder, result);
        System.out.println("ControlResult:" + res);
        controlOrder = controlOrderRepository.loadByNo(controlOrder.getInstOrderNo());
        Assert.isTrue(controlOrder.getStatus() == InstOrderStatus.IN_PROCESS, "执行出错");
        instOrder = instOrderRepository.loadByNo(instOrder.getInstOrderNo());
        Assert.isTrue(instOrder.getStatus() == InstOrderStatus.IN_PROCESS, "执行出错");
    }

    @Test
    @DisplayName("4.测试推进异常")
    public void testAdError() {

        InstOrder instOrder = buildInstOrder();
        InstControlOrder controlOrder = buildControlOrder(instOrder);
        instOrderRepository.updateInstOrderStatus(instOrder, InstOrderStatus.SUCCESSFUL);
        ChannelFundResult result = buildChannelFundResult(controlOrder, "fail");
        try {
            InstControlOrderResult res = controlResultProcessor.process(controlOrder, result);
            System.out.println("ControlResult:" + res);
        } catch (IllegalArgumentException ae) {
            logger.warn("ControlResult.error", ae.getMessage());
        }
        controlOrder = controlOrderRepository.loadByNo(controlOrder.getInstOrderNo());
        Assert.isTrue(controlOrder.getStatus() == InstOrderStatus.FAILURE, "执行出错");
        instOrder = instOrderRepository.loadByNo(instOrder.getInstOrderNo());
        Assert.isTrue(instOrder.getStatus() == InstOrderStatus.SUCCESSFUL, "执行出错");
    }

    @Test
    @DisplayName("5.测试3ds2-0元绑卡流程")
    public void test0BindCardAd() {
        try (ChannelCarrier carrier = apiRouter.route(RouteUtil.getParam(MOCK_CHANNEL_3DS2, FundChannelApiType.ADVANCE_3DS2))) {
            InstControlOrder preOrder = buildControlOrder();
            InstControlOrder controlOrder = buildControlOrder(preOrder);
            ChannelFundResult result = buildChannelFundResult(controlOrder, "success");
            InstControlOrderResult res = controlResultProcessor.process(controlOrder, result);
            System.out.println("ControlResult:" + res);
            controlOrder = controlOrderRepository.loadByNo(controlOrder.getInstOrderNo());
            Assert.isTrue(controlOrder.getStatus() == InstOrderStatus.SUCCESSFUL, "执行出错");
            preOrder = controlOrderRepository.loadByNo(preOrder.getInstOrderNo());
            Assert.isTrue(preOrder.getStatus() == InstOrderStatus.SUCCESSFUL, "执行出错");
            String id = preOrder.getExtension().get("authenticationTransactionId");
            Assert.isTrue("PVQC2LR9ysIquCCSjm31".equals(id), "transId不等");
        } catch (Exception e) {
            log.error("test0BindCardAd.error", e);
        }
    }

    @Test
    @DisplayName("6.保存扩展参数测试")
    public void testReceiveData() {
        try (ChannelCarrier carrier = apiRouter.route(RouteUtil.getParam(MOCK_CHANNEL_3DS2, FundChannelApiType.ADVANCE_3DS2))) {
            InstControlOrder preOrder = buildControlOrder();
            InstControlOrder controlOrder = buildControlOrder(preOrder);
            InstControlOrderResult controlResult = buildControlResult(controlOrder);
            saveBankReceiveService.saveExtension(controlOrder, controlResult, carrier.getChannel());
            controlOrder = controlOrderRepository.loadByNo(controlOrder.getInstOrderNo());
            Assert.isTrue("extVall".equals(controlOrder.getExtension().get("extTest")), "保存失败!");

        } catch (Exception e) {
            log.error("testReceiveData.error", e);
        }
    }

    private InstControlOrderResult buildControlResult(InstControlOrder controlOrder) {
        InstControlOrderResult result = new InstControlOrderResult();
        BeanUtils.copyProperties(controlOrder, result);
        Map<String, String> extMap = new HashMap<>();
        extMap.put("extTest", "extVall");
        result.setExtension(extMap);
        return result;
    }


    private InstOrder buildInstOrder() {
        InstOrder order = new InstFundinOrder();
        order.setInstOrderType(InstOrderType.FUND);
        order.setBizType(BizType.FUNDIN);
        order.setStatus(InstOrderStatus.IN_PROCESS);
        order.setGmtBookingSubmit(new Date());
        order.setFundChannelCode(MOCK_CHANNEL_3DS2);
        order.setApiType(FundChannelApiType.DEBIT);
        order.setArchiveBatchId(0L);
        order.setPayMode(PayMode.QUICKPAY);
        order.setInstOrderNo("L" + genRandomId(17));
        order.setGmtCreate(new Date());
        order.setGmtModified(new Date());
        order.setInstCode(MOCK_INST);
        order.setCommunicateStatus(CommunicateStatus.SENT);
        order.setCommunicateType(InstOrderCommunicateType.SINGLE);
        order.setCmfSeqNo("20220402000409689");
        order.setProductCode("600001");
        order.setPaymentCode("123");
        instOrderRepository.store(order);
        return order;
    }

    private InstControlOrder buildControlOrder() {
        InstControlOrder order = new InstControlOrder();
        order.setInstOrderType(InstOrderType.FUND);
        order.setStatus(InstOrderStatus.IN_PROCESS);
        order.setFundChannelCode(MOCK_CHANNEL_3DS2);
        order.setApiType(FundChannelApiType.AUTH);
        order.setPayMode(PayMode.QUICKPAY);
        order.setInstOrderNo("L" + genRandomId(17));
        order.setRequestNo(genRandomId(17));
        order.setGmtCreate(new Date());
        order.setGmtModified(new Date());
        order.setInstCode(MOCK_INST);
        order.setRequestType(ControlRequestType.AUTH);
        order.setFlag(OrderFlag.DEFAULT);
        order.setStatus(InstOrderStatus.IN_PROCESS);
        order.setNotifyStatus(NotifyStatus.AWAITING);
        order.setCommunicateStatus(CommunicateStatus.SENT);
        order.setProductCode("600001");
        Map<String, String> extMap = new HashMap<>();
        extMap.put("testSaveExt", "saveVal");
        order.setExtension(extMap);
        controlOrderRepository.store(order);
        return order;
    }

    private InstControlOrder buildControlOrder(InstOrder instOrder) {
        InstControlOrder order = InstControlOrderConverter.convert(instOrder, ControlRequestType.ADVANCE, FundChannelApiType.ADVANCE_3DS2);
        order.setFlag(OrderFlag.DEFAULT);
        order.setRequestNo(UUID.randomUUID().toString().replace("-", ""));
        order.setNotifyStatus(NotifyStatus.NOT_NOTIFY);
        order.setCommunicateStatus(CommunicateStatus.SENT);
        order.setStatus(InstOrderStatus.IN_PROCESS);
        controlOrderRepository.store(order);
        return order;
    }

    private InstControlOrder buildControlOrder(InstControlOrder instOrder) {
        InstControlOrder order = InstControlOrderConverter.convert(instOrder, ControlRequestType.ADVANCE, FundChannelApiType.ADVANCE_3DS2);
        order.setFlag(OrderFlag.DEFAULT);
        order.setRequestNo(UUID.randomUUID().toString().replace("-", ""));
        order.setNotifyStatus(NotifyStatus.NOT_NOTIFY);
        order.setCommunicateStatus(CommunicateStatus.AWAITING);
        order.setStatus(InstOrderStatus.IN_PROCESS);
        order.setInstOrderNo("L" + genRandomId(17));
        controlOrderRepository.store(order);
        return order;
    }


    private ChannelFundResult buildChannelFundResult(InstControlOrder controlOrder, String resultCode) {
        ChannelFundResult result = new ChannelFundResult();
        result.setFundChannelCode(controlOrder.getFundChannelCode());
        result.setRealAmount(controlOrder.getAmount());
        result.setApiType(controlOrder.getApiType());
        result.setApiResultCode(resultCode);
        result.setApiResultSubCode("sub_code");
        result.setApiResultMessage("mock");
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("authenticationTransactionId", "PVQC2LR9ysIquCCSjm31");
        result.setExtension(MapUtil.mapToJson(dataMap));
        return result;
    }


}
