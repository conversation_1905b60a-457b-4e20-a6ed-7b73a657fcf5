package com.uaepay.cmf.test.service.cases.asserts;

import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import com.uaepay.unittest.facade.mocker.testtool.checker.base.AbstractCommonResponseChecker;
import org.junit.jupiter.api.Assertions;
import org.springframework.stereotype.Service;

@Service
public class RemittanceR<PERSON>ponse<PERSON>he<PERSON> extends Assertions {

    public Checker create(CmfFundResult response) {
        return new Checker(response);

    }

    public static class Checker extends AbstractCommonResponseChecker<Checker,CmfFundResult> {


        public Checker(CmfFundResult response) {
            super(response);
        }

        public Checker checkStatus(CmfFundResultCode cmfFundResultCode) {
            assertEquals(cmfFundResultCode, response.getResultCode());
            return this;
        }
    }
}
