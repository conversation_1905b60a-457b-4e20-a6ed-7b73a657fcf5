package com.uaepay.cmf.test.service.mock;

import com.uaepay.grc.common.bean.Decission;
import com.uaepay.grc.common.error.ErrorMsg;
import com.uaepay.grc.cps.api.LimitQuotaFrequencyStub;
import com.uaepay.grc.cps.api.vo.LimitQuotaAndTimesRequest;
import com.uaepay.grc.cps.api.vo.MobileRechargeLimitRequest;
import com.uaepay.rm.unbreakable.Result;
import org.apache.dubbo.config.annotation.Service;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date MockLimitQuotaFrequencyStub.java v1.0
 */
@Service
public class MockLimitQuotaFrequencyStub implements LimitQuotaFrequencyStub {
    @Override
    public Result<ErrorMsg, Decission> limitQuotaAndTimes(LimitQuotaAndTimesRequest limitQuotaAndTimesRequest) {
        return null;
    }

    @Override
    public Result<ErrorMsg, Decission> mobileRechargeLimitQuotaAndTimes(MobileRechargeLimitRequest mobileRechargeLimitRequest) {
        return null;
    }
}
