package com.uaepay.cmf.test.service.util;

import org.apache.dubbo.config.*;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/7/31
 */
public class MockDubboUtil {


    public static <T> ApplicationConfig getApplicationConfig(String group,Class<T> interfaceClass,Object ref) {
        ApplicationConfig applicationConfig = new ApplicationConfig("cache-test");
        RegistryConfig registryConfig = new RegistryConfig("N/A");
        applicationConfig.setRegistry(registryConfig);
        ProtocolConfig protocolConfig = new ProtocolConfig("injvm");
        ServiceConfig<T> service = new ServiceConfig<>();
        service.setApplication(applicationConfig);
        service.setRegistry(registryConfig);
        service.setProtocol(protocolConfig);
        service.setGroup(group);
        service.setInterface(interfaceClass.getName());
        service.setRef((T)ref);
        service.export();
        return applicationConfig;
    }

    public static ConsumerConfig getConsumerConfig() {
        ConsumerConfig consumerConfig = new ConsumerConfig();
        consumerConfig.setTimeout(10);
        return consumerConfig;
    }
}
