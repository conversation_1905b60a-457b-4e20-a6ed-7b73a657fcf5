package com.uaepay.cmf.test.service.mock;

import com.uaepay.member.service.facade.IBeneficiaryFacade;
import com.uaepay.member.service.request.beneficiary.*;
import com.uaepay.member.service.response.beneficiary.*;
import org.apache.dubbo.config.annotation.Service;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date MockIBeneficiaryFacade.java v1.0
 */
@Service
public class MockIBeneficiaryFacade implements IBeneficiaryFacade {
    @Override
    public AddBeneficiaryResponse addBeneficiary(AddBeneficiaryRequest addBeneficiaryRequest) {
        return null;
    }

    @Override
    public QueryBeneficiaryAccountResponse queryBeneficiaryAccount(QueryBeneficiaryAccountRequest queryBeneficiaryAccountRequest) {
        return null;
    }


    @Override
    public QueryByIdResponse queryById(QueryByIdRequest queryByIdRequest) {
        return null;
    }

    @Override
    public QueryByAccountNoResponse queryByAccountNo(QueryByAccountNoRequest queryByAccountNoRequest) {
        return null;
    }

    @Override
    public QueryByPageResponse queryByPage(QueryByPageRequest queryByPageRequest) {
        return null;
    }

    @Override
    public RemoveBeneficiaryResponse removeBeneficiary(RemoveBeneficiaryRequest removeBeneficiaryRequest) {
        return null;
    }
}
