package com.uaepay.cmf.test.service.cases.base;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.exception.DuplicateKeyException;
import com.uaepay.cmf.common.core.domain.institution.InstFundinOrder;
import com.uaepay.cmf.common.core.domain.institution.InstFundoutOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstRefundOrder;
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator;
import com.uaepay.cmf.common.core.engine.generator.SequenceNameEnum;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.cmf.test.base.TestConstants;
import com.uaepay.cmf.test.service.builder.BaseBuilder;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.router.service.facade.domain.RouteRequest;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date OrderRepoTestService.java v1.0
 */
@Service
public class OrderRepoTestService extends BaseBuilder {

    @Resource
    private CmfOrderRepository cmfOrderRepository;
    @Resource
    private InstOrderRepository instOrderRepository;
    @Resource
    private InstOrderResultRepository instOrderResultRepository;
    @Resource
    private PrimaryKeyGenerator primaryKeyGenerator;

    public CmfOrder buildCmfOrder(CmfOrderStatus status) {
        CmfOrder cmfOrder = new CmfOrder();
        cmfOrder.setOrderSeqNo(primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER));
        cmfOrder.setBizType(BizType.FUNDIN);
        cmfOrder.setAmount(new Money("1.01", "AED"));
        cmfOrder.setPaymentSeqNo(genRandomId(17));
        cmfOrder.setSettlementId(genRandomId(17));
        cmfOrder.setGmtCreate(new Date());
        cmfOrder.setInstCode(MOCK_INST);
        cmfOrder.setPayMode(PayMode.QUICKPAY);
        cmfOrder.setRequestType(RequestType.FUND_IN);
        cmfOrder.setStatus(status);
        cmfOrder.setConfirmStatus(CmfOrderConfirmStatus.PASS);
        cmfOrder.setBizTime(new Date());
        cmfOrder.setGmtModified(new Date());
        cmfOrder.setProductCode("6001010");
        cmfOrder.setPaymentCode("1");
        cmfOrder.getExtension().put(ExtensionKey.PAYMENT_ORDER_NO.getKey(), genRandomId(10));
        cmfOrder.getExtension().put("bizProductCode", "230202");
        cmfOrder.getExtension().put("memberId", "100000338200");
        cmfOrder.getExtension().put("svaStatus", "Y");
        try {
            cmfOrderRepository.store(cmfOrder);
        } catch (DuplicateKeyException e) {
            return cmfOrderRepository.loadByPaymentSeqNo(cmfOrder.getPaymentSeqNo(), cmfOrder.getSettlementId());
        }
        return cmfOrder;
    }


    public InstOrder buildInstOrder(String cmfSeqNo, InstOrderStatus status) {
        InstOrder order = new InstFundinOrder();
        order.setInstOrderType(InstOrderType.FUND);
        order.setBizType(BizType.FUNDIN);
        order.setStatus(status);
        order.setAmount(new Money("1.01", "AED"));
        order.setGmtBookingSubmit(new Date());
        order.setFundChannelCode(MOCK_CHANNEL_3DS2);
        order.setApiType(FundChannelApiType.DEBIT);
        order.setArchiveBatchId(0L);
        order.setPayMode(PayMode.QUICKPAY);
        order.setInstOrderNo("L" + genRandomId(17));
        order.setGmtCreate(new Date());
        order.setGmtModified(new Date());
        order.setInstCode(MOCK_INST);
        order.setCmfSeqNo(cmfSeqNo);
        order.setCommunicateStatus(status == InstOrderStatus.SUCCESSFUL ? CommunicateStatus.RECEIVED : CommunicateStatus.SENT);
        order.setCommunicateType(InstOrderCommunicateType.SINGLE);
        order.setProductCode("600001");
        order.setPaymentCode("123");
        instOrderRepository.store(order);
        return order;
    }


    Map<InstOrderStatus, CmfOrderStatus> statusMap = new HashMap<InstOrderStatus, CmfOrderStatus>() {{
        put(InstOrderStatus.IN_PROCESS, CmfOrderStatus.IN_PROCESS);
        put(InstOrderStatus.SUCCESSFUL, CmfOrderStatus.SUCCESSFUL);
        put(InstOrderStatus.FAILURE, CmfOrderStatus.FAILURE);
        put(InstOrderStatus.RISK, CmfOrderStatus.IN_PROCESS);
        put(InstOrderStatus.CANCEL, CmfOrderStatus.CANCEL);
    }};

    public Function<ApplicationTest.InitOrder, CmfOrder> getCmfOrder() {
        return (io) -> {
            CmfOrder cmfOrder = new CmfOrder();
            cmfOrder.setOrderSeqNo(primaryKeyGenerator.generateKey(SequenceNameEnum.CMF_ORDER));
            cmfOrder.setBizType(BizType.FUNDIN);
            cmfOrder.setAmount(new Money(io.getAmount(), "AED"));
            cmfOrder.setPaymentSeqNo(genRandomId(17));
            cmfOrder.setSettlementId(genRandomId(17));
            cmfOrder.setGmtCreate(new Date());
            cmfOrder.setInstCode(StringUtils.isEmpty(io.getInst()) ? MOCK_INST : io.getInst());
            cmfOrder.setPayMode(PayMode.QUICKPAY);
            cmfOrder.setRequestType(RequestType.FUND_IN);
            cmfOrder.setStatus(statusMap.get(io.getStatus()));
            cmfOrder.setConfirmStatus(CmfOrderConfirmStatus.PASS);
            cmfOrder.setBizTime(new Date());
            cmfOrder.setGmtModified(new Date());
            cmfOrder.setProductCode("6001010");
            cmfOrder.setPaymentCode("1");
            cmfOrder.getExtension().put(ExtensionKey.PAYMENT_ORDER_NO.getKey(), genRandomId(10));
            cmfOrder.getExtension().put("bizProductCode", "230202");
            cmfOrder.getExtension().put("memberId", "100000338200");
            cmfOrder.getExtension().put("svaStatus", "Y");
            if (io.getExtMap() != null) {
                cmfOrder.getExtension().putAll(io.getExtMap());
            }
            try {
                cmfOrderRepository.store(cmfOrder);
            } catch (DuplicateKeyException e) {
                return cmfOrderRepository.loadByPaymentSeqNo(cmfOrder.getPaymentSeqNo(), cmfOrder.getSettlementId());
            }
            return cmfOrder;
        };
    }


    public Function<ApplicationTest.InitOrder, InstOrder> getInstOrder() {
        return (io) -> {
            InstOrder order = null;
            if (io.getBizType() == BizType.FUNDOUT) {
                order = new InstFundoutOrder();
            } else if (io.getBizType() == BizType.REFUND) {
                order = new InstRefundOrder();
            } else {
                order = new InstFundinOrder();
            }
            order.setInstOrderType(InstOrderType.FUND);
            order.setBizType(io.getBizType());
            order.setStatus(io.getStatus());
            order.setAmount(new Money(io.getAmount(), "AED"));
            order.setGmtBookingSubmit(new Date());
            order.setFundChannelCode(StringUtils.isNotEmpty(io.getChannelCode()) ? io.getChannelCode() : MOCK_CHANNEL_3DS2);
            order.setApiType(FundChannelApiType.DEBIT);
            order.setArchiveBatchId(0L);
            order.setPayMode(PayMode.QUICKPAY);
            order.setInstOrderNo(StringUtils.isNotEmpty(io.getInstOrderNo()) ? io.getInstOrderNo() : "L" + genRandomId(17));
            order.setGmtCreate(new Date());
            order.setGmtModified(new Date());
            order.setInstCode(StringUtils.isEmpty(io.getInst()) ? MOCK_INST : io.getInst());
            order.setCmfSeqNo(io.getCmfOrder().getOrderSeqNo());
            order.setCommunicateStatus(io.getStatus() == InstOrderStatus.SUCCESSFUL ? CommunicateStatus.RECEIVED : CommunicateStatus.SENT);
            order.setCommunicateType(InstOrderCommunicateType.SINGLE);
            if (io.getIsAdvance() != null) {
                order.setIsAdvance(io.getIsAdvance());
            }
            order.setProductCode("600001");
            order.setPaymentCode("123");
            if (io.getExtMap() != null) {
                order.getExtension().putAll(io.getExtMap());
            }

            instOrderRepository.store(order);
            return order;
        };


    }
}
