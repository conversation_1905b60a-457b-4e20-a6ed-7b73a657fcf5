
package com.uaepay.cmf.test.base;

import com.uaepay.common.util.money.Money;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

public class ShareDataBean {

    public static final AtomicBoolean GP_OPERATE = new AtomicBoolean(true);
    public static final AtomicBoolean Cut_OPERATE = new AtomicBoolean(true);
    public static final AtomicBoolean GP_SETTLE_OPERATE = new AtomicBoolean(true);
    public static final AtomicBoolean GP_CACEL_OPERATE = new AtomicBoolean(true);
    public static final AtomicBoolean CUT_OPERATE_EXCEPTION = new AtomicBoolean(false);
    public static final AtomicBoolean CUT_OPERATE_MODIFID_AMOUNT_EXCEPTION = new AtomicBoolean(false);
    public static long FREEZE_STATUS = 1L;
    public static boolean CSS_SERVICE_SUCC = true;

    private static final AtomicInteger queues = new AtomicInteger();

    /**
     * 初始化数据池
     */
    private final static Map<String, String> INIT_DATA_POOL = new ConcurrentHashMap<>();


    /**
     * 添加数据
     *
     * @param data
     */
    public static void addData(String key,String data) {
        INIT_DATA_POOL.put(key, data);
    }

    public static String getData(String key){
        return INIT_DATA_POOL.get(key);
    }

    public static void removeInitDataList(int index) {
        INIT_DATA_POOL.remove(index);
    }

    public static final String CURRENCY = "AED";

    /**
     * 交易金额
     */
    public static Money tradeAmount = new Money("1", "AED");

    public static void setCssServiceExcetion() {
        CSS_SERVICE_SUCC = false;
    }

}