package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.cmf.service.facade.counter.QueryChannelAccountBalanceFacade;
import com.uaepay.cmf.service.facade.domain.counter.QueryAccountBalanceRequest;
import com.uaepay.cmf.service.facade.domain.counter.QueryAccountBalanceResponse;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 10/09/2024 14:25
 */
@DisplayName("query channel account balance")
@RunWith(SpringRunner.class)
public class QueryChannelAccountBalanceFacadeTestCase extends ApplicationTest {

    @Resource
    private QueryChannelAccountBalanceFacade queryChannelAccountBalanceFacade;


    @Test
    public void testQueryAccountBalance(){
        QueryAccountBalanceRequest request = new QueryAccountBalanceRequest();
        request.setChannelCode("ENBD201");
        request.setInstCode("ENBD");
        request.setAccountType("ACCOUNT_NUMBER");
        request.setThreshold(new Money("100000","AED"));
        request.setClientId("test");
        QueryAccountBalanceResponse response = queryChannelAccountBalanceFacade.queryAccountBalance(request);
        System.out.println(response);
    }

}
