package com.uaepay.cmf.test.service.mock;

import com.uaepay.ues.crypto.model.EncryptType;
import com.uaepay.ues.ctx.params.EncryptParameter;
import com.uaepay.ues.model.EncryptData;
import com.uaepay.ues.model.UesResult;
import com.uaepay.ues.services.UesRemoteService;
import org.apache.dubbo.config.annotation.Service;

import java.util.HashMap;
import java.util.List;

@Service
public class MockUesRemoteService implements UesRemoteService {

    @Override
    public UesResult saveData(String s, String s1, String s2, EncryptType encryptType) {
        return null;
    }

    @Override
    public UesResult saveDataWithSummary(String s, String s1, String s2, EncryptType encryptType, int i, int i1) {
        return null;
    }

    @Override
    public UesResult saveTempData(String s, String s1, String s2, EncryptType encryptType, int i, int i1) {
        return null;
    }

    @Override
    public UesResult getDataByTicket(String s) {
        return null;
    }

    @Override
    public UesResult getCert() {
        return null;
    }

    @Override
    public List<UesResult> saveDataBatch(List<EncryptData> list) {
        return null;
    }

    @Override
    public List<UesResult> saveDataWithSummaryBatch(List<EncryptData> list, int i, int i1) {
        return null;
    }

    @Override
    public List<UesResult> getDataByBatchTickets(List<String> list) {
        return null;
    }

    @Override
    public HashMap<String, UesResult> getDataByBatchTicketsReturnMap(List<String> list) {
        return null;
    }

    @Override
    public UesResult saveDataByParam(EncryptParameter encryptParameter) {
        return null;
    }

    @Override
    public List<UesResult> saveDatasByParams(List<EncryptParameter> list) {
        return null;
    }

    @Override
    public UesResult getDataByTicket(String s, String s1) {
        return null;
    }

    @Override
    public List<UesResult> getDataByBatchTickets(List<String> list, String s) {
        return null;
    }

    @Override
    public HashMap<String, UesResult> getDataByBatchTicketsReturnMap(List<String> list, String s) {
        return null;
    }
}
