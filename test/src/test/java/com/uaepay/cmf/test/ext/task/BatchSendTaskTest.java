package com.uaepay.cmf.test.ext.task;

import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.domainservice.main.process.ChannelBalanceService;
import com.uaepay.cmf.fss.ext.resend.newtask.BatchQueryTask;
import com.uaepay.cmf.fss.ext.resend.newtask.BatchSendTask;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
public class BatchSendTaskTest extends ApplicationTest {
    @Resource
    private BatchSendTask batchSendTask;

    @Resource
    private BatchQueryTask batchQueryTask;

    @Resource
    private ChannelBalanceService balanceService;

    @Test
    public void testLoadTask(){
        List<String> tasks = batchSendTask.loadTask(10);
        System.out.println(tasks);
    }

    @Test
    public void executeTask(){
       boolean status = batchSendTask.executeTask("FAB213");
       Assert.isTrue(status, "执行发送银行任务失败");
    }

    @Test
    public void executeBatchQuery(){
        InstBatchResult result = batchQueryTask.executeSingle(20210917003177069L);
        System.out.println(result);
    }

    @Test
    public void testGatherBalance(){
        Money result = balanceService.gatherAwaitingAmount("FAB211");
        System.out.println(result);
    }
}
