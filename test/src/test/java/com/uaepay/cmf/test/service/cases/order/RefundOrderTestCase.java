package com.uaepay.cmf.test.service.cases.order;

import com.uaepay.cmf.test.base.ApplicationTest;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.core.annotation.Order;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date RefundOrderCase.java v1.0  2020-09-14 13:05
 */
@RunWith(SpringRunner.class)
@DisplayName("退款订单")
public class RefundOrderTestCase extends ApplicationTest {

    @Test
    @Order(0)
    @DisplayName("0 创建card token")
    public void createCardToken(){

    }

    @Test
    @Order(1)
    @DisplayName("1 入款订单测试")
    public void fundIn(){

    }

    @Test
    @Order(2)
    @DisplayName("2 验证签名")
    public void verifySign(){

    }

    @Test
    @Order(3)
    @DisplayName("3 退款")
    public void refund(){

    }

    @Test
    @DisplayName("退款转人工")
    public void transfer2Manual(){

    }

}
