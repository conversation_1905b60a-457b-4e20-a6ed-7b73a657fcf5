package com.uaepay.cmf.test.service.mock;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.test.base.TestConstants;
import com.uaepay.common.util.DateUtil;
import com.uaepay.router.service.facade.RouteFacade;
import com.uaepay.router.service.facade.domain.RouteOnboardingRequest;
import com.uaepay.router.service.facade.domain.RouteRequest;
import com.uaepay.router.service.facade.domain.RouteResponse;
import com.uaepay.router.service.facade.domain.channel.ChannelApiParamVO;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.router.service.facade.domain.channel.ChannelExtVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.router.service.facade.domain.order.OrderInfo;
import com.uaepay.router.service.facade.domain.request.RouteApiRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

/**
 * <p>路由Mock服务</p>
 *
 * <AUTHOR>
 * @date MockRouteFacade.java v1.0  2020-09-08 11:02
 */
@Slf4j
@Service
public class MockRouteFacade implements RouteFacade, TestConstants {

    @Override
    public RouteResponse<ChannelVO> routeOnboarding(RouteOnboardingRequest routeOnboardingRequest) {
        return null;
    }

    @Override
    public RouteResponse<ChannelVO> preRoute(RouteRequest routeRequest) {
        return null;
    }

    @Override
    public RouteResponse<ChannelVO> route(RouteRequest request) {
        RouteResponse<ChannelVO> response = new RouteResponse<>();
        String currency = request.getAmount() != null ? request.getAmount().getCurrency() : "AED";
        // 若金额不为美元或AED则路由失败
        if (!"USD".equals(currency) && !"AED".equals(currency)) {
            return response;
        }

        ChannelVO channel = getChannel().apply(request);
        response.setChannel(channel);
        response.setOrderInfo(genOrderInfo(request.getBizType()));
        response.setApplyStatus(ApplyStatusEnum.SUCCESS);
        return response;
    }

    @Override
    public RouteResponse<ChannelVO> routeApi(RouteApiRequest request) {
        ChannelVO channel = getRouteApiChannel().apply(request);
        RouteResponse<ChannelVO> routeResp = new RouteResponse<>();
        routeResp.setChannel(channel);
        routeResp.setOrderInfo(request.isGenOrderInfo() ? genOrderInfo("I") : null);
        routeResp.setApplyStatus(ApplyStatusEnum.SUCCESS);
        return routeResp;
    }

    private static OrderInfo genOrderInfo(String bizType) {
        OrderInfo orderInfo = new OrderInfo();
        Date sendTime = new Date();
        String sendType = "S";

        if ("O".equals(bizType)) {
            sendTime = DateUtil.addHours(sendTime, 1);
            sendType = "A";
        }
        orderInfo.setGmtBooking(sendTime);
        orderInfo.setSendType(sendType);
        orderInfo.setOrderNo(genRandomId(10));
        return orderInfo;
    }

    private static Function<RouteApiRequest, ChannelVO> getRouteApiChannel() {
        return (req) -> {
            ChannelVO channel = new ChannelVO();
            channel.setChannelCode(req.getChannelCode());
            channel.setChannelName(MOCK_CHANNEL_NAME);

            ChannelApiVO api = new ChannelApiVO();
            api.setApiCode(req.getChannelCode() + "-" + req.getApiType());
            ArrayList<ChannelApiParamVO> paramList = new ArrayList<>(0);
            if(FundChannelApiType.ADVANCE_3DS2.getCode().equals(req.getApiType())){
                ChannelApiParamVO vo = new ChannelApiParamVO();
                vo.setApiCode("DS320-AD");
                vo.setScene("response");
                vo.setGmtCreate(new Date());

                vo.setParamName("authenticationTransactionId");
                paramList.add(vo);
                ChannelApiParamVO vo2 = new ChannelApiParamVO();
                vo2.setApiCode("DS320-AD");
                vo2.setScene("response");
                vo2.setGmtCreate(new Date());

                vo2.setParamName("extTest");
                paramList.add(vo2);
            }
            if(FundChannelApiType.DEBIT.getCode().equals(req.getApiType())){
                ChannelApiParamVO vo3 = new ChannelApiParamVO();
                vo3.setApiCode("DS320-DB");
                vo3.setScene("response");
                vo3.setGmtCreate(new Date());

                vo3.setParamName("extTest");
                paramList.add(vo3);
            }
            if(FundChannelApiType.ADVANCE_3DS2.getCode().equals(req.getApiType())&&!"DS320".equals(req.getChannelCode())){
                channel.setChannelApi(null);
            }else {
            api.setApiType(req.getApiType());
            api.setApiMethod(COMMUNICATION_TYPE_DUBBO);
            api.setApiUrl(null);
            api.setParamList(paramList);
            channel.setChannelApi(api);
            }


            channel.setStatus(CHANNEL_AVAILABLE);

            List<ChannelExtVO> extList = new ArrayList<>();
            extList.add(getExt("maxResendTimes", "50"));
            channel.setExtList(extList);
            return channel;
        };
    }

    private static ChannelExtVO getExt(String attrKey, String attrVal){
        ChannelExtVO extVo = new ChannelExtVO();
        extVo.setAttrKey(attrKey);
        extVo.setAttrValue(attrVal);
        return extVo;
    }


    private static Function<RouteRequest, ChannelVO> getChannel() {
        return (req) -> {
            ChannelVO channel = new ChannelVO();
            String whiteChannel = ExtensionKey.WHITE_CHANNEL_CODE.key;
            if (req.getExtension() != null && req.getExtension().containsKey(whiteChannel)) {
                channel.setChannelCode(req.getExtension().get(whiteChannel));
                channel.setChannelName(req.getExtension().get(whiteChannel));
            }else if("AT".equals(req.getRequestType())){
                channel.setChannelCode("CS104");
                channel.setChannelName("CyberSource Channel");
            } else {
                channel.setChannelCode(MOCK_CHANNEL);
                channel.setChannelName(MOCK_CHANNEL_NAME);
            }

            ChannelApiVO api = new ChannelApiVO();
            String apiType = getApiType(req.getRequestType());

            api.setApiCode(MOCK_CHANNEL + "-" + apiType);
            api.setApiType(apiType);
            api.setApiMethod(COMMUNICATION_TYPE_DUBBO);
            api.setApiUrl(null);

            channel.setChannelApi(api);
            channel.setStatus(CHANNEL_AVAILABLE);
            return channel;
        };
    }

    private static String getApiType(String code) {
        RequestType rt = RequestType.getByCode(code);
        if (rt != null) {
            switch (rt) {
                case FUND_OUT:
                    return "SP";
                case FUND_IN:
                    return "DB";
                case REFUND:
                    return "SR";
            }
        }
        return FundChannelApiType.getByCode(code).getCode();
    }

    protected static String genRandomId(int length) {
        String id = StringUtils.rightPad(String.valueOf((int) (Math.random() * 1000000)), length, "0");
        return DateUtil.format(new Date(), "yyyyMMdd") + id;
    }

}
