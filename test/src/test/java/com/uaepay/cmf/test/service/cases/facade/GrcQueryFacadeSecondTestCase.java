package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.basis.beacon.service.facade.domain.response.ObjectQueryResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository;
import com.uaepay.cmf.ext.service.grc.GrcQueryFacadeImpl;
import com.uaepay.cmf.service.facade.domain.counter.InstOrderVO;
import com.uaepay.cmf.service.facade.domain.grc.QueryOrderAllInfoRequest;
import com.uaepay.schema.cmf.enums.BizType;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 10/09/2024 14:25
 */
public class GrcQueryFacadeSecondTestCase {

    @InjectMocks
    private GrcQueryFacadeImpl grcQueryFacadeImpl;

    @Mock
    private CmfOrderRepository cmfOrderRepository;

    @Mock
    private InstOrderRepository instOrderRepository;

    @Mock
    private InstOrderResultRepository instOrderResultRepository;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryOrderAllInfo_NotFound() {
        // Setup
        QueryOrderAllInfoRequest request = new QueryOrderAllInfoRequest();
        request.setPaymentSeqNo("NON_EXISTENT_PAYMENT_SEQ_NO");

        when(cmfOrderRepository.loadByPaymentSeqNo(anyString(), any())).thenReturn(null);

        // Execute
        ObjectQueryResponse<InstOrderVO> response = grcQueryFacadeImpl.queryOrderAllInfo(request);

        // Assert
        assertNotNull(response);
        assertEquals(ApplyStatusEnum.SUCCESS, response.getApplyStatus());
        assertEquals(ErrorCode.ORDER_NOT_FOUND.getErrorCode(), response.getCode());
        assertNull(response.getResult());
    }

    @Test
    public void testQueryOrderAllInfo_NullRequest() {
        // Execute
        ObjectQueryResponse<InstOrderVO> response = grcQueryFacadeImpl.queryOrderAllInfo(null);

        // Assert
        assertNotNull(response);
        assertEquals(ApplyStatusEnum.SUCCESS, response.getApplyStatus());
        assertEquals(ErrorCode.ORDER_NOT_FOUND.getErrorCode(), response.getCode());
        assertNull(response.getResult());
    }

    @Test
    public void testQueryOrderAllInfo_Error() {
        // Setup
        QueryOrderAllInfoRequest request = new QueryOrderAllInfoRequest();
        request.setPaymentSeqNo("PAYMENT_SEQ_NO_ERROR");

        when(cmfOrderRepository.loadByPaymentSeqNo(anyString(), any())).thenThrow(new RuntimeException("Test Exception"));

        // Execute
        ObjectQueryResponse<InstOrderVO> response = grcQueryFacadeImpl.queryOrderAllInfo(request);

        // Assert
        assertNotNull(response);
        assertEquals(ApplyStatusEnum.ERROR, response.getApplyStatus());
        assertEquals(ErrorCode.CMF_SYSTEM_ERROR.getErrorCode(), response.getCode());
    }

    @Test
    public void testQueryOrderAllInfo_Success() {
        // Setup
        QueryOrderAllInfoRequest request = new QueryOrderAllInfoRequest();
        request.setPaymentSeqNo("PAYMENT_SEQ_NO_SUCCESS");

        CmfOrder cmfOrder = new CmfOrder();
        cmfOrder.setOrderSeqNo("ORDER_SEQ_NO");
        cmfOrder.setPaymentSeqNo("PAYMENT_SEQ_NO_SUCCESS");

        InstOrder instOrder = createMockInstOrder();
        List<InstOrder> instOrderList = Collections.singletonList(instOrder);

        when(cmfOrderRepository.loadByPaymentSeqNo(anyString(), any())).thenReturn(cmfOrder);
        when(instOrderRepository.loadByCmfSeqNo(anyString())).thenReturn(instOrderList);
        when(cmfOrderRepository.loadByCmfSeqNo(anyString(), anyBoolean())).thenReturn(cmfOrder);
        when(instOrderResultRepository.getAllResult(anyLong())).thenReturn(Collections.singletonList(createMockInstOrderResult()));

        // Set the keyMappingConfigAlias value using reflection
        ReflectionTestUtils.setField(grcQueryFacadeImpl, "keyMappingConfigAlias",
                "{\"CKO\":{\"auth_code\":\"authCode\",\"response_summary\":\"acquirerMessage\"},\"MPGS\":{\"authorizationCode\":\"authCode\",\"eci\":\"resEci\"}}");

        // Initialize the cache field by calling init method
        ReflectionTestUtils.invokeMethod(grcQueryFacadeImpl, "init");

        // Execute
        ObjectQueryResponse<InstOrderVO> response = grcQueryFacadeImpl.queryOrderAllInfo(request);

        // Assert
        assertNotNull(response);
        assertEquals(ApplyStatusEnum.SUCCESS, response.getApplyStatus());
        assertNotNull(response.getResult());
        InstOrderVO vo = response.getResult();
        assertEquals("PAYMENT_SEQ_NO_SUCCESS", vo.getPaymentSeqNo());
    }

    @Test
    public void testNormalizeChannelFields() throws Exception {
        // Use reflection to access the private method
        Method normalizeMethod = GrcQueryFacadeImpl.class.getDeclaredMethod("normalizeChannelFields",
                Map.class, Map.class, String.class);
        normalizeMethod.setAccessible(true);

        // Set the keyMappingConfigAlias value using reflection
        ReflectionTestUtils.setField(grcQueryFacadeImpl, "keyMappingConfigAlias",
                "{\"CKO\":{\"auth_code\":\"authCode\",\"response_summary\":\"acquirerMessage\"},\"MPGS\":{\"authorizationCode\":\"authCode\",\"eci\":\"resEci\"}}");

        // Initialize the cache field by calling init method
        ReflectionTestUtils.invokeMethod(grcQueryFacadeImpl, "init");

        // Test 1: Null or empty inputs
        Map<String, String> result1 = (Map<String, String>) normalizeMethod.invoke(grcQueryFacadeImpl, null, null, "CKO");
        assertTrue(result1.isEmpty());

        Map<String, String> result2 = (Map<String, String>) normalizeMethod.invoke(grcQueryFacadeImpl, new HashMap<>(), null, "CKO");
        assertTrue(result2.isEmpty());

        Map<String, String> result3 = (Map<String, String>) normalizeMethod.invoke(grcQueryFacadeImpl,
                new HashMap<String, String>() {{
                    put("auth_code", "123456");
                }},
                null,
                null);
        assertTrue(result3.isEmpty());

        // Test 2: CKO channel mapping
        Map<String, String> ckoExtMap = new HashMap<>();
        ckoExtMap.put("auth_code", "123456");
        ckoExtMap.put("response_summary", "Approved");
        ckoExtMap.put("other_field", "value");

        Map<String, String> orderExtMap = new HashMap<>();

        Map<String, String> result4 = (Map<String, String>) normalizeMethod.invoke(
                grcQueryFacadeImpl, ckoExtMap, orderExtMap, "CKO101");

        // Checking that the normalized map contains the right mappings
        assertFalse(result4.isEmpty());
        assertEquals("123456", result4.get("authCode"));
        assertEquals("Approved", result4.get("acquirerMessage"));

        // Make sure original map is not changed (values not removed)
        assertTrue(ckoExtMap.containsKey("auth_code"));
        assertTrue(ckoExtMap.containsKey("response_summary"));

        // Test 3: MPGS channel mapping
        Map<String, String> mpgsExtMap = new HashMap<>();
        mpgsExtMap.put("authorizationCode", "654321");
        mpgsExtMap.put("eci", "05");
        mpgsExtMap.put("other_field", "other_value");

        Map<String, String> result5 = (Map<String, String>) normalizeMethod.invoke(
                grcQueryFacadeImpl, mpgsExtMap, orderExtMap, "MPGS");

        assertFalse(result5.isEmpty());
        assertEquals("654321", result5.get("authCode"));
        assertEquals("05", result5.get("resEci"));

        // Test 4: No matching channel
        Map<String, String> unknownExtMap = new HashMap<>();
        unknownExtMap.put("auth_code", "123456");

        Map<String, String> result6 = (Map<String, String>) normalizeMethod.invoke(
                grcQueryFacadeImpl, unknownExtMap, orderExtMap, "UNKNOWN");

        assertTrue(result6.isEmpty());
        assertTrue(unknownExtMap.containsKey("auth_code"));

        // Test 5: Channel with prefix matching
        Map<String, String> mpgsExtMapWithPrefix = new HashMap<>();
        mpgsExtMapWithPrefix.put("authorizationCode", "654321");

        Map<String, String> result7 = (Map<String, String>) normalizeMethod.invoke(
                grcQueryFacadeImpl, mpgsExtMapWithPrefix, orderExtMap, "MPGS_XYZ");

        assertFalse(result7.isEmpty());
        assertEquals("654321", result7.get("authCode"));
    }

    @Test
    public void testCollectResultExtensions() throws Exception {
        // Use reflection to access the private method
        Method collectMethod = GrcQueryFacadeImpl.class.getDeclaredMethod("collectResultExtensions", List.class);
        collectMethod.setAccessible(true);

        // Test empty list
        Map<String, String> result1 = (Map<String, String>) collectMethod.invoke(grcQueryFacadeImpl, Collections.emptyList());
        assertTrue(result1.isEmpty());

        // Test valid list
        InstOrderResult result = createMockInstOrderResult();
        Map<String, String> result3 = (Map<String, String>) collectMethod.invoke(
                grcQueryFacadeImpl, Collections.singletonList(result));

        assertFalse(result3.isEmpty());
        assertEquals("123456", result3.get("auth_code"));
        assertEquals("Approved", result3.get("response_summary"));
    }

    @Test
    public void testConvertToVO_NullInstOrder() {
        InstOrderVO vo = grcQueryFacadeImpl.convertToVO(null);
        assertNull(vo);
    }

    @Test
    public void testConvertToVO_WithoutResults() {
        // Setup
        InstOrder instOrder = createMockInstOrder();
        CmfOrder cmfOrder = new CmfOrder();
        cmfOrder.setPaymentSeqNo("TEST_PAYMENT_SEQ_NO");

        when(cmfOrderRepository.loadByCmfSeqNo(anyString(), anyBoolean())).thenReturn(cmfOrder);
        when(instOrderResultRepository.getAllResult(anyLong())).thenReturn(Collections.emptyList());

        // Execute
        InstOrderVO vo = grcQueryFacadeImpl.convertToVO(instOrder);

        // Assert
        assertNotNull(vo);
        assertEquals("TEST_PAYMENT_SEQ_NO", vo.getPaymentSeqNo());
        assertEquals("TEST_GATE_ORDER_NO", vo.getGateOrderNo());
        assertEquals("S", vo.getInstStatus());
        assertFalse(vo.isCanManualChange());
    }

    @Test
    public void testConvertToVO_WithResults() {
        // Setup
        InstOrder instOrder = createMockInstOrder();
        CmfOrder cmfOrder = new CmfOrder();
        cmfOrder.setPaymentSeqNo("TEST_PAYMENT_SEQ_NO");

        InstOrderResult instOrderResult = createMockInstOrderResult();

        when(cmfOrderRepository.loadByCmfSeqNo(anyString(), anyBoolean())).thenReturn(cmfOrder);
        when(instOrderResultRepository.getAllResult(anyLong())).thenReturn(Collections.singletonList(instOrderResult));

        // Set the keyMappingConfigAlias value using reflection
        ReflectionTestUtils.setField(grcQueryFacadeImpl, "keyMappingConfigAlias",
                "{\"CKO\":{\"auth_code\":\"authCode\",\"response_summary\":\"acquirerMessage\"},\"MPGS\":{\"authorizationCode\":\"authCode\",\"eci\":\"resEci\"}}");

        // Initialize the cache field by calling init method
        ReflectionTestUtils.invokeMethod(grcQueryFacadeImpl, "init");

        // Execute
        InstOrderVO vo = grcQueryFacadeImpl.convertToVO(instOrder);

        // Assert
        assertNotNull(vo);
        assertEquals("TEST_PAYMENT_SEQ_NO", vo.getPaymentSeqNo());
        assertEquals("TEST_MEMO", vo.getMemo());
        assertNotNull(vo.getExtension());
    }

    @Test
    public void testGetExtensionValue() throws Exception {
        // Use reflection to access the private method
        Method getExtensionValueMethod = GrcQueryFacadeImpl.class.getDeclaredMethod(
                "getExtensionValue", Map.class, String.class);
        getExtensionValueMethod.setAccessible(true);

        // Test null map
        String result1 = (String) getExtensionValueMethod.invoke(grcQueryFacadeImpl, null, "key");
        assertNull(result1);

        // Test null key
        Map<String, String> testMap = new HashMap<>();
        testMap.put("key", "value");
        String result2 = (String) getExtensionValueMethod.invoke(grcQueryFacadeImpl, testMap, null);
        assertNull(result2);

        // Test valid key
        String result3 = (String) getExtensionValueMethod.invoke(grcQueryFacadeImpl, testMap, "key");
        assertEquals("value", result3);

        // Test missing key
        String result4 = (String) getExtensionValueMethod.invoke(grcQueryFacadeImpl, testMap, "missing");
        assertNull(result4);
    }

    // Helper methods to create test objects

    private InstOrder createMockInstOrder() {
        InstOrder instOrder = new InstOrder();
        instOrder.setInstOrderId(1L);
        instOrder.setCmfSeqNo("TEST_CMF_SEQ_NO");
        instOrder.setStatus(InstOrderStatus.SUCCESSFUL);
        instOrder.setBizType(BizType.FUNDIN);
        instOrder.setArchiveBatchId(1L);
        instOrder.setFundChannelCode("CKO");

        Map<String, String> extension = new HashMap<>();
        extension.put(ExtensionKey.GATE_ORDER_NO.key, "TEST_GATE_ORDER_NO");
        extension.put("auth_code", "123456");
        instOrder.setExtension(extension);

        return instOrder;
    }

    private InstOrderResult createMockInstOrderResult() {
        InstOrderResult result = new InstOrderResult();
        result.setResultId(1L);
        result.setMemo("TEST_MEMO");
        result.setApiResultCode("00");
        result.setApiResultSubCode("SUCCESS");

        Map<String, String> extension = new HashMap<>();
        extension.put("auth_code", "123456");
        extension.put("response_summary", "Approved");
        result.setExtension(extension);

        return result;
    }
}
