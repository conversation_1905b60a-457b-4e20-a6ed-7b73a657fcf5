package com.uaepay.cmf.test.service.cases.order;

import com.uaepay.cmf.common.core.domain.institution.InstFundoutOrder;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.service.facade.domain.CmfRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.cmf.test.service.cases.asserts.RemittanceResponseChecker;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.OperationEnvironment;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.core.annotation.Order;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date RemittanceTestCase.java v1.0
 */
@DisplayName("汇款订单测试")
@RunWith(SpringRunner.class)
public class RemittanceTestCase extends ApplicationTest {

    @Resource
    private RemittanceResponseChecker remittanceResponseChecker;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Test
    @Order(1)
    @DisplayName("1.正常汇款订单测试")
    public void remittanceTest() {
        Money sourceAmt = new Money("367", "AED");
        Money targetAmt = new Money("100", "USD");
        String beneficiaryAddress = "P3147162,P3147163";
        String intermediaryBank = "NBADAEAAXXX";
        CmfRequest request = buildRequst(sourceAmt, targetAmt, beneficiaryAddress, intermediaryBank);
        CmfFundResult result = fundRequestFacade.apply(request, new OperationEnvironment());
        remittanceResponseChecker.create(result).checkStatus(CmfFundResultCode.SUCCESS);
        Assert.isTrue(result.getAmount().equals(sourceAmt), "源金额不一致");
        InstFundoutOrder instOrder = (InstFundoutOrder) instOrderRepository.loadByNo(result.getInstOrderNo());
        Assert.isTrue(instOrder.getAmount().equals(targetAmt), "目标金额不一致");
        Assert.isTrue(beneficiaryAddress.equals(instOrder.getBeneficiaryAddress()), "地址不一致");
        Assert.isTrue(intermediaryBank.equals(instOrder.getIntermediaryBank()), "中间行不一致");
    }

    @Test
    @Order(2)
    @DisplayName("2.汇款订单测试-不支持的币种")
    public void currencyTest() {
        CmfRequest request = buildRequst(new Money("519.99", "AED"), new Money("100", "GBP"));
        CmfFundResult result = fundRequestFacade.apply(request, new OperationEnvironment());
        remittanceResponseChecker.create(result).checkStatus(CmfFundResultCode.FAILED);
    }

    @Test
    @Order(3)
    @DisplayName("3.汇款订单测试-汇款参数缺失")
    public void parameterTest() {
        CmfRequest request = buildRequst(new Money("367", "AED"), null);
        CmfFundResult result = fundRequestFacade.apply(request, new OperationEnvironment());
        remittanceResponseChecker.create(result).checkStatus(CmfFundResultCode.REQUEST_SUCCESS);
        Assert.isTrue("目标金额或币种不能为空!".equals(result.getResultMessage()), "返回结果信息不符预期");
    }

    private static CmfRequest buildRequst(Money amount, Money targetAmount) {
        return buildRequst(amount, targetAmount, null, null);
    }

    private static CmfRequest buildRequst(Money amount, Money targetAmount, String beneficiaryAddress, String intermediaryBank) {
        CmfRequest request = new CmfRequest();
        request.setSettlementId(genRandomId(10));
        request.setPaymentSeqNo(genRandomId(10));
        request.setProductCode("********");
        request.setPaymentCode("3001");
        request.setBizType(BizType.FUNDOUT);
        request.setPayMode(PayMode.BALANCE);
        request.setInstCode("NBAD");
        request.setMemberId("************");
        request.setAmount(amount);
        Extension extension = new Extension();
        extension.add("CARD_TYPE", "DC");
        extension.add("BIZ_PRODUCT_CODE", "220401");
        extension.add("paymentOrderNo", genRandomId(20));
        extension.add("payeeId", "anonymous");
        extension.add("iban", "P3146871-0003");
        extension.add("topayMerchantId", "************");
        extension.add("swiftCode", "NBADAEAAXXX");
        if(StringUtils.isNotEmpty(beneficiaryAddress)) {
            extension.add("beneficiaryAddress", beneficiaryAddress);
        }
        if(StringUtils.isNotEmpty(intermediaryBank)) {
            extension.add("intermediaryBank", intermediaryBank);
        }
        // 内部出款类型
        extension.add("innerFundoutType", "remittance");
        if (targetAmount != null) {
            extension.add("targetAmount", targetAmount.getAmount().toString());
            extension.add("targetCurrency", targetAmount.getCurrency());
        }

        request.setExtension(extension);
        return request;
    }
}
