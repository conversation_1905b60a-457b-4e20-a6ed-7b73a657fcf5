package com.uaepay.cmf.test.service.cases.service;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.exception.RouteChannelException;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.router.ApiRouteParam;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.general.impl.APlusMerchantRegisterQueryProcessor;
import com.uaepay.cmf.domainservice.main.process.NotifyPaymentService;
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterQueryRequest;
import com.uaepay.cmf.test.base.ApplicationTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date NotifyPaymentServiceTest.java v1.0  2020-11-05 16:40
 */
@RunWith(SpringRunner.class)
public class NotifyPaymentServiceTest extends ApplicationTest {

    @Resource
    private ChannelApiRouter channelApiRouter;

    @Resource
    NotifyPaymentService notifyPaymentService;
    @Resource
    private APlusMerchantRegisterQueryProcessor aPlusMerchantRegisterQueryProcessor;

    @Test
    public void testNotify() {

        try (ChannelCarrier carrier = channelApiRouter.route(ApiRouteParam.builder().channelCode("TEST101").apiType("VS").build())) {
            CmfOrder cmfOrder = new CmfOrder();
            cmfOrder.setOrderSeqNo("20201105000345911");
            cmfOrder.setGmtCreate(new Date());
            InstOrderResult result = new InstOrderResult();

            result.setProcessStatus(InstOrderProcessStatus.SUCCESS);

            // 通知payment结果
            notifyPaymentService.notifyResult(cmfOrder, result);
        } catch (RouteChannelException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testQueryMerchant(){
        APlusMerchantRegisterQueryRequest request = new APlusMerchantRegisterQueryRequest();
        request.setClientId(CLIENT_ID);
        request.setPreRequestNo("A20230814000004");
        aPlusMerchantRegisterQueryProcessor.process(request);
    }
}
