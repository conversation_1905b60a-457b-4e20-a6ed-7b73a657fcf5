package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository;
import com.uaepay.cmf.fss.ext.integration.cashdesk.CashdeskClient;
import com.uaepay.cmf.service.facade.api.CardTokenFacade;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateRequest;
import com.uaepay.cmf.service.facade.domain.card.CardTokenCreateResult;
import com.uaepay.cmf.service.facade.domain.card.CardTokenQueryResult;
import com.uaepay.cmf.service.facade.domain.card.CardTokenUpdateRequest;
import com.uaepay.cmf.service.facade.result.BindCardRequest;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.cmf.test.base.ShareDataBean;
import com.uaepay.cmf.test.service.builder.CardTokenRequestBuilder;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.core.annotation.Order;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenFacadeTestCase.java v1.0  2020-09-14 13:55
 */
@DisplayName("卡token测试")
@RunWith(SpringRunner.class)
public class CardTokenFacadeTestCase extends ApplicationTest {

    @Resource
    private CardTokenFacade cardTokenFacade;

    @Resource
    private CashdeskClient cashdeskClient;

    @Resource
    private CardTokenRepository cardTokenRepository;

    @Test
    @Order(1)
    @DisplayName("1.创建token记录")
    public void testCreate() {
        String csc = "177";
        CardTokenCreateRequest cardTokenRequest = CardTokenRequestBuilder.createCardTokenRequest(csc);
        CardTokenCreateResult result = cardTokenFacade.create(cardTokenRequest);
        Assert.isTrue(result.getApplyStatus() == ApplyStatusEnum.SUCCESS, "创建card token失败");
        ShareDataBean.addData(CARD_TOKEN, result.getCardTokenId());
        ShareDataBean.addData(CSC, csc);
    }

    @Test
    @Order(2)
    @DisplayName("2.查询token记录")
    public void testQuery() {
//        ShareDataBean.getData(CARD_TOKEN)
        CardTokenQueryResult result = cardTokenFacade.query("20220328100065717");
        System.out.println(result);
        Assert.isTrue(result.getApplyStatus() == ApplyStatusEnum.SUCCESS, "查询card token失败");
    }

    @Test
    @Order(3)
    @DisplayName("3.更新token记录")
    public void testUpdate() {
        CardTokenUpdateRequest request = new CardTokenUpdateRequest();
        request.setSessionId("abcTest");
        request.setCardId(111L);
        request.setInstCode("MC");
        request.setMemberId("1001");
        request.setDbcr("CC");
        request.setCompanyOrPersonal("C");
        request.setCardTokenId("20200328100000001");
        CommonResponse resp = cardTokenFacade.update(request);
        System.out.println(resp);
    }

    @Test
    public void testCreateTokenLong(){
        // 测试长姓名
        String cardHolder = "P3132619,P3132619";
        CardTokenCreateRequest cardTokenRequest = CardTokenRequestBuilder.createCardTokenRequest("");
        cardTokenRequest.setCardHolder(cardHolder);
        CardTokenCreateResult result = cardTokenFacade.create(cardTokenRequest);
        System.out.println(result);
        CardToken dbcardToken = cardTokenRepository.query(result.getCardTokenId());
        Assert.isTrue(cardHolder.equals(dbcardToken.getCardHolder()), "用户名不等:"+dbcardToken.getCardHolder());
    }

    @Test
    public void testNotify() {
        BindCardRequest request = new BindCardRequest();
        request.setCardBrand("VISA");
        request.setCardNo("1234xxxx123132");
        request.setClientId("cashierii");
        cashdeskClient.sendBindCardRequest(request);
    }

    @Test
    public void queryCardToken() {
        CardToken cardToken = cardTokenRepository.queryByInstOrderId(20200330000297504L);
        System.out.println(cardToken);
    }


}
