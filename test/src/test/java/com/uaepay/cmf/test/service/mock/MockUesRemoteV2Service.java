package com.uaepay.cmf.test.service.mock;

import com.uaepay.ues.ctx.params.EncryptParameterV2;
import com.uaepay.ues.model.DataTypeConfig;
import com.uaepay.ues.model.UesResult;
import com.uaepay.ues.services.UesRemoteV2Service;
import org.apache.dubbo.config.annotation.Service;

import java.util.List;
import java.util.Map;

@Service
public class MockUesRemoteV2Service implements UesRemoteV2Service {

    @Override
    public UesResult getCert() {
        return null;
    }

    @Override
    public UesResult saveDataByParam(EncryptParameterV2 encryptParameterV2) {
        return null;
    }

    @Override
    public UesResult queryTicket(EncryptParameterV2 encryptParameterV2) {
        return null;
    }

    @Override
    public List<UesResult> saveDatasByParams(List<EncryptParameterV2> list) {
        return null;
    }

    @Override
    public UesResult getDataByTicket(String s, String s1) {
        return null;
    }

    @Override
    public Map<String, UesResult> getDataByBatchTicketsReturnMap(List<String> list, String s) {
        return null;
    }

    @Override
    public DataTypeConfig queryDataType(String s, String s1) {
        return null;
    }

    @Override
    public List<DataTypeConfig> queryDataTypeList(String s) {
        return null;
    }

    @Override
    public boolean deleteTempEncryptData(String s, String s1) {
        return false;
    }
}
