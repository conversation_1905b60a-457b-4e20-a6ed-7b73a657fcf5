package com.uaepay.cmf.test.base;


import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.IsAdvance;
import com.uaepay.cmf.fss.ext.integration.ues.UesClient;
import com.uaepay.cmf.service.facade.api.CardTokenFacade;
import com.uaepay.cmf.service.facade.api.FundRequestFacade;
import com.uaepay.cmf.service.facade.api.VerifySignFacade;
import com.uaepay.cmf.test.CmfApplication;
import com.uaepay.cmf.test.service.cases.base.OrderRepoTestService;
import com.uaepay.common.util.DateUtil;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>作为spring cloud的测试基类</p>
 *
 * <AUTHOR> Yun
 * @version v 0.1 2019/7/8 19:15
 */

@ContextConfiguration
@SpringBootTest(classes = CmfApplication.class, properties = "spring.profiles.active:dev,unittest,manual")
//由于是Web项目，Junit需要模拟ServletContext，因此我们需要给我们的测试类加上@WebAppConfiguration。
@WebAppConfiguration
public class ApplicationTest implements TestConstants {
    protected static Logger logger = LoggerFactory.getLogger(ApplicationTest.class);

    @Resource
    protected FundRequestFacade fundRequestFacade;
    @Resource
    protected CardTokenFacade cardTokenFacade;
    @Resource
    protected VerifySignFacade verifySignFacade;
    @Resource
    protected OrderRepoTestService orderRepoTestService;
    @Resource
    protected UesClient uesClient;

    protected static String genRandomId(int length) {
        String id = StringUtils.rightPad(String.valueOf((int) (Math.random() * 1000000)), length, "0");
        return DateUtil.format(new Date(), "yyyyMMdd") + id;
    }

    protected void initOrder(List<InitOrder> orderList){
        for(InitOrder io : orderList) {
            CmfOrder cmfOrder = orderRepoTestService.getCmfOrder().apply(io);
            io.setCmfOrder(cmfOrder);
            orderRepoTestService.getInstOrder().apply(io);
        }

    }


    @Data
    @Builder
    public static class InitOrder{

        private String instOrderNo;

        private String inst;

        private BizType bizType = BizType.FUNDIN;

        private String amount;

        private InstOrderStatus status = InstOrderStatus.IN_PROCESS;

        private IsAdvance isAdvance;

        private String channelCode;

        private Map<String,String> extMap;

        private CmfOrder cmfOrder;
    }


}
