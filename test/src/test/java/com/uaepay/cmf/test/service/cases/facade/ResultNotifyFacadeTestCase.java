package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.cmf.common.core.domain.enums.IsAdvance;
import com.uaepay.cmf.common.domain.base.InstOrderResult;
import com.uaepay.cmf.common.domain.base.ReturnInfo;
import com.uaepay.cmf.service.facade.api.ResultNotifyFacade;
import com.uaepay.cmf.service.facade.domain.fundschannel.OrderResult;
import com.uaepay.cmf.test.TestException;
import com.uaepay.cmf.test.base.ApplicationTest;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.InstOrderStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ResultNotifyFacade.java v1.0  2020-09-14 13:58
 */
@Slf4j
@RunWith(SpringRunner.class)
public class ResultNotifyFacadeTestCase extends ApplicationTest {

    @Resource
    private ResultNotifyFacade resultNotifyFacade;

    Map<String, Triple<InitOrder, InstOrderStatus, Predicate<ReturnInfo>>> orderMap = new HashMap<String, Triple<InitOrder, InstOrderStatus, Predicate<ReturnInfo>>>() {{
        String orderNo = "L" + genRandomId(17);
        put(orderNo, Triple.of(InitOrder.builder().instOrderNo(orderNo).amount("5555555552.00").bizType(BizType.FUNDIN).status(com.uaepay.cmf.common.core.domain.enums.InstOrderStatus.IN_PROCESS).build(), InstOrderStatus.S, (resp) -> "0".equals(resp.getReturnCode()) && "处理成功".equals(resp.getReturnMsg())));
        orderNo = "L" + genRandomId(17);
        put(orderNo, Triple.of(InitOrder.builder().instOrderNo(orderNo).amount("1.01").bizType(BizType.FUNDIN).status(com.uaepay.cmf.common.core.domain.enums.InstOrderStatus.IN_PROCESS).isAdvance(IsAdvance.YES).build(), InstOrderStatus.S, (resp) -> "-1".equals(resp.getReturnCode()) && "cmf处理失败,未推进订单不可置结果".equals(resp.getReturnMsg())));
        orderNo = "L" + genRandomId(17);
        put(orderNo, Triple.of(InitOrder.builder().instOrderNo(orderNo).amount("8.00").bizType(BizType.FUNDIN).status(com.uaepay.cmf.common.core.domain.enums.InstOrderStatus.IN_PROCESS).build(), InstOrderStatus.S, (resp) -> "0".equals(resp.getReturnCode()) && "处理成功".equals(resp.getReturnMsg())));
        put(orderNo+"R", Triple.of(InitOrder.builder().instOrderNo(orderNo+"R").amount("8.00").bizType(BizType.FUNDIN).status(com.uaepay.cmf.common.core.domain.enums.InstOrderStatus.IN_PROCESS).build(), InstOrderStatus.S, (resp) -> "0".equals(resp.getReturnCode()) && resp.getReturnMsg().startsWith("[渠道回调通知]重复通知,机构订单号")));
        orderNo = "L" + genRandomId(17);
        put(orderNo, Triple.of(InitOrder.builder().instOrderNo(orderNo).amount("12000.00").bizType(BizType.FUNDIN).status(com.uaepay.cmf.common.core.domain.enums.InstOrderStatus.SUCCESSFUL).build(), InstOrderStatus.F, (resp) -> "-1".equals(resp.getReturnCode()) && resp.getReturnMsg().startsWith("[渠道回调通知]重复通知且状态不一致,机构订单号")));
        orderNo = "L" + genRandomId(17);
        put(orderNo, Triple.of(InitOrder.builder().instOrderNo(orderNo).amount("1.00").bizType(BizType.FUNDIN).status(com.uaepay.cmf.common.core.domain.enums.InstOrderStatus.IN_PROCESS).build(), InstOrderStatus.S, (resp) -> "-1".equals(resp.getReturnCode()) && resp.getReturnMsg().startsWith("订单金额校验异常")));
    }};


    @Test
    public void testNotify() {
        initOrder(orderMap.values().stream().map(Triple::getLeft).filter(i->!i.getInstOrderNo().endsWith("R")).collect(Collectors.toList()));
        log.info("OrderMap:{}", orderMap);
        for (Map.Entry<String, Triple<InitOrder, InstOrderStatus, Predicate<ReturnInfo>>> entry : orderMap.entrySet()) {
            InitOrder order = entry.getValue().getLeft();
            OrderResult orderResult = buildOrderResult(order.getInstOrderNo(), "1.00".equals(order.getAmount()) ? "10.00" : order.getAmount(), "AED", entry.getValue().getMiddle());
            testNotify(orderResult, entry.getValue().getRight(), entry.getKey() + "通知失败:" + orderResult);
        }
    }

    private OrderResult buildOrderResult(String instOrderNo, String amountStr, String currency, InstOrderStatus status) {
        OrderResult orderResult = new OrderResult();
        InstOrderResult instOrderResult = new InstOrderResult();
        instOrderResult.setOrderType(BizType.FUNDIN);
        instOrderResult.setInstOrderNo(instOrderNo.endsWith("R")?instOrderNo.substring(0,instOrderNo.length()-1):instOrderNo);
        instOrderResult.setInstOrderStatus(status);
        Money money = new Money(amountStr, currency);
        instOrderResult.setRealAmount(money);
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setReturnCode("0000");
        returnInfo.setReturnMsg("测试notify接口");
        instOrderResult.setReturnInfo(returnInfo);
        orderResult.setOrderResult(instOrderResult);
        return orderResult;
    }

    public void testNotify(OrderResult orderResult, Predicate<ReturnInfo> predicate, String msg) {
        System.out.println("orderResult:" + orderResult);
        ReturnInfo returnInfo = resultNotifyFacade.notify(orderResult);
        System.out.println("returnInfo:" + returnInfo);
        if (!predicate.test(returnInfo)) {
            throw new TestException(msg);
        } else {
            log.info("本次任务执行通过");
        }
    }

}
