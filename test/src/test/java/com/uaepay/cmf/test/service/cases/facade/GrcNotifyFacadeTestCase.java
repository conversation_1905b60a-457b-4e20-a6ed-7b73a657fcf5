package com.uaepay.cmf.test.service.cases.facade;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import com.uaepay.cmf.service.facade.grc.GrcNotifyFacade;
import com.uaepay.cmf.test.base.ApplicationTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date GrcNotifyFacadeTestCase.java v1.0  2020-10-29 17:10
 */
@RunWith(SpringRunner.class)
public class GrcNotifyFacadeTestCase extends ApplicationTest {

    @Resource
    private GrcNotifyFacade grcNotifyFacade;

    @Test
    public void notifyTest(){
        Notify3dsResult notifyResult = new Notify3dsResult();
        /**
         * Notify3dsResult[resultId=<null>,tradeOrderNo=<null>,paymentOrderNo=<null>,channelCode=MC101,instOrderNo=100120201029164525941,
         * gmtCmfRequest=<null>,gmtBankRequest=<null>,gmtBankResponse=Thu Oct 29 16:46:03 GMT+04:00 2020,cardHolder=<null>,cardNo=<null>,
         * cardNoMask=428688xxxxxx6767,bankCode=BBME,bankName=HSBC BANK MIDDLE EAST,cardType=DC,cardBrand=VISA,cardExpire=********,eci=05,
         * identityResult=Y,identityResultDesc=认证成功,extMap=<null>]
         */
        notifyResult.setChannelCode("MC101");
        notifyResult.setInstOrderNo("100120201029164525941");
        notifyResult.setGmtBankResponse(new Date());
        notifyResult.setCardNoMask("428688xxxxxx6767");
        notifyResult.setBankCode("BBME");
        notifyResult.setBankName("HSBC BANK MIDDLE EAST");
        notifyResult.setCardType("DC");
        notifyResult.setCardBrand("VISA");
        notifyResult.setCardExpire("********");
        notifyResult.setEci("05");
        notifyResult.setIdentityResult("Y");
        notifyResult.setIdentityResultDesc("认证成功");

        CommonResponse resp = grcNotifyFacade.notify3dsResult(notifyResult);

        Assert.isTrue(resp.getApplyStatus()== ApplyStatusEnum.SUCCESS, "3ds结果保存请求失败");
    }

}
