<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd"
       default-autowire="byName">

    <!-- sim -->
    <bean id="cmfDataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName">
            <value>com.mysql.jdbc.Driver</value>
        </property>
        <property name="url">
            <!--  sim -->
            <value><![CDATA[******************************************]]></value>
            <!--  dev -->
<!--            <value><![CDATA[*****************************************]]></value>-->
        </property>
        <property name="username">
            <value>cmfuser</value>
        </property>
        <property name="password">
            <!--  sim -->
            <value>cmf_UaR1stXSDpxCnP</value>
            <!--  dev -->
<!--            <value>cmf_aGlmLok7F6E2bv</value>-->
        </property>
    </bean>
    <bean id="csCmfDataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName">
            <value>com.mysql.jdbc.Driver</value>
        </property>
        <property name="url">
            <value><![CDATA[******************************************]]></value>
        </property>
        <property name="username">
            <value>cmfuser</value>
        </property>
        <property name="password">
            <value>cmf_UaR1stXSDpxCnP</value>
        </property>
    </bean>
    <bean id="cmfDataSourceHistory" class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName">
            <value>com.mysql.jdbc.Driver</value>
        </property>
        <property name="url">
            <value><![CDATA[*****************************************]]></value>
        </property>
        <property name="username">
            <value>cmfuser</value>
        </property>
        <property name="password">
            <value>cmf_aGlmLok7F6E2bv</value>
        </property>
    </bean>
    <bean id="csCmfDataSourceHistory" class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName">
            <value>com.mysql.jdbc.Driver</value>
        </property>
        <property name="url">
            <value><![CDATA[******************************************]]></value>
        </property>
        <property name="username">
            <value>cmfuser</value>
        </property>
        <property name="password">
            <value>cmf_UaR1stXSDpxCnP</value>
        </property>
    </bean>

</beans>
