spring:
  main:
    lazy-initialization: true
  application:
    name: gp002_cmf
#  profiles:
#    active: dev,unittest


  cloud:
    config:
      uri: ${CONFIG_ADDRESS:http://config.test2pay.com}
      fail-fast: true
      retry:
        initial-interval: 2000
        max-interval: 10000
        multiplier: 2
        max-attempts: 10
      username: ${CONFIG_USER:admin}
      password: ${CONFIG_PASSWORD:admin}
      profile: dev


## 修改tomcat端口（替换8080）
#server:
#  port: 0
#
## 修改dubbo端口（替换20880）
#dubbo:
#  protocol:
#    port: -1
#  #  consumer:
#  #    scope: local
#  #    injvm: true
#  scan:
#    base-packages: ..., com.uaepay.cmf.test.service.mock
#    #, com.uaepay.cmf.ext.service
#  reference:
#    com:
#      uaepay:
#        cmf:
#          fss:
#            ext:
#              common:
#                api:
#                  ChannelFundFacade:
#                    injvm: true
#        router:
#          service:
#            facade:
#              RouteFacade:
#                injvm: true
#              ChannelFacade:
#                injvm: true
#              ResultFacade:
#                injvm: true
#        channel:
#          cards:
#            service:
#              facade:
#                BankFacade:
#                  injvm: true
#dubbo.reference.com.uaepay.grc.cps.api.LimitQuotaFrequencyStub.injvm: true
#dubbo.reference.com.uaepay.grc.connect.api.facade.QueryFacade.injvm: true

datasource:
  cmf:
    url: ******************************************************
    username: cmfuser
    password: cmf_aGlmLok7F6E2bv

escrow:
  notify:
    transform:
      versionTag: Y
      channels: MC101,MC104,MC105,FAB101,FAB102,FAB103,TEST104
      topUpBizCode: 230101
      consumeBizCode: 200000
      amountRangeBegin: 1
      amountRangeEnd: 5000
      transformBizCodes: 260291,260202
      ratioBizCodeMap:
        260202: 10
        260291: 10
        230101: 100
    remittance:
      channels: MC101,MC102,MC103,MC104,MC105,MC111,MC112,MC113,FS101,FS102,FS103,CS104,CS105,CS106
      topUpBizCode: 230101
      consumeBizCode: 200000
      transformBizCodes: 230204
