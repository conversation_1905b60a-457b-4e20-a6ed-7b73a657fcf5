<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:caaa.001.001.06" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <AccptrAuthstnReq>
        <Hdr>
            <MsgFctn>FAUQ</MsgFctn>
            <PrtcolVrsn>6.0</PrtcolVrsn>
            <XchgId>305</XchgId>
            <CreDtTm>2022-08-07T19:09:32.1+04:00</CreDtTm>
            <InitgPty>
                <Id>3325325</Id>
                <Tp>OPOI</Tp>
                <ShrtNm>Shop123456</ShrtNm>
            </InitgPty>
        </Hdr>
        <AuthstnReq>
            <Envt>
                <Acqrr>
                    <Id>
                        <Id>1</Id>
                    </Id>
                    <ParamsVrsn>20220620100125</ParamsVrsn>
                </Acqrr>
                <Mrchnt>
                    <Id>
                        <Id>8116000002</Id>
                    </Id>
                    <CmonNm>PayBy test</CmonNm>
                </Mrchnt>
                <POI>
                    <Id>
                        <Id>Shop123456</Id>
                        <Tp>OPOI</Tp>
                        <ShrtNm>Shop123456</ShrtNm>
                    </Id>
                    <GrpId>**********</GrpId>
                    <Cpblties>
                        <CardRdngCpblties>CICC</CardRdngCpblties>
                        <CardRdngCpblties>PHYS</CardRdngCpblties>
                        <CardRdngCpblties>MGST</CardRdngCpblties>
                        <CardRdngCpblties>ECTL</CardRdngCpblties>
                        <CrdhldrVrfctnCpblties>NPIN</CrdhldrVrfctnCpblties>
                        <CrdhldrVrfctnCpblties>FCPN</CrdhldrVrfctnCpblties>
                        <CrdhldrVrfctnCpblties>FEPN</CrdhldrVrfctnCpblties>
                        <CrdhldrVrfctnCpblties>MNSG</CrdhldrVrfctnCpblties>
                        <CrdhldrVrfctnCpblties>NOVF</CrdhldrVrfctnCpblties>
                        <CrdhldrVrfctnCpblties>OTHR</CrdhldrVrfctnCpblties>
                        <ApprvlCdLngth>6</ApprvlCdLngth>
                        <OnLineCpblties>ONLN</OnLineCpblties>
                        <MsgCpblties>
                            <Dstn>CDSP</Dstn>
                            <AvlblFrmt>TEXT</AvlblFrmt>
                            <NbOfLines>4</NbOfLines>
                            <LineWidth>32</LineWidth>
                            <AvlblLang>en</AvlblLang>
                        </MsgCpblties>
                        <MsgCpblties>
                            <Dstn>CRCP</Dstn>
                            <AvlblFrmt>TEXT</AvlblFrmt>
                            <NbOfLines>4</NbOfLines>
                            <LineWidth>32</LineWidth>
                            <AvlblLang>en</AvlblLang>
                        </MsgCpblties>
                    </Cpblties>
                    <Cmpnt>
                        <Tp>TERM</Tp>
                        <Id>
                            <PrvdrId>SUNMI P2</PrvdrId>
                            <Id>T6900</Id>
                            <SrlNb>PB16208N60078</SrlNb>
                        </Id>
                    </Cmpnt>
                    <Cmpnt>
                        <Tp>APLI</Tp>
                        <Id>
                            <PrvdrId>PayBy</PrvdrId>
                            <Id>PayBy Payment Application</Id>
                        </Id>
                        <Sts>
                            <VrsnNb>2.0.8</VrsnNb>
                        </Sts>
                        <StdCmplc>
                            <Id>PayBy-IS</Id>
                            <Vrsn>4.0</Vrsn>
                            <Issr>PayBy</Issr>
                        </StdCmplc>
                        <Assmnt>
                            <Tp>CERT</Tp>
                            <Assgnr>OSCAR</Assgnr>
                            <Nb>ToComplete(giveByPayCert)</Nb>
                        </Assmnt>
                    </Cmpnt>
                </POI>
                <Card>
                    <PrtctdCardData>
                        <CnttTp>EVLP</CnttTp>
                        <EnvlpdData>
                            <Rcpt>
                                <KEK>
                                    <KEKId>
                                        <KeyId>A2DataTestKey</KeyId>
                                        <KeyVrsn>2016051911</KeyVrsn>
                                        <DerivtnId>AQIDBAU=</DerivtnId>
                                    </KEKId>
                                    <KeyNcrptnAlgo>
                                        <Algo>DKP9</Algo>
                                    </KeyNcrptnAlgo>
                                    <NcrptdKey>CQkJCAgIAwMD</NcrptdKey>
                                </KEK>
                            </Rcpt>
                            <NcrptdCntt>
                                <CnttTp>DATA</CnttTp>
                                <CnttNcrptnAlgo>
                                    <Algo>E3DC</Algo>
                                </CnttNcrptnAlgo>
                                <NcrptdData>AQIDBAU=</NcrptdData>
                            </NcrptdCntt>
                        </EnvlpdData>
                    </PrtctdCardData>
                    <IssrBIN>67999989</IssrBIN>
                    <CardCtryCd>784</CardCtryCd>
                    <CardCcyCd>784</CardCcyCd>
                    <CardPdctPrfl>E</CardPdctPrfl>
                    <CardBrnd>E</CardBrnd>
                </Card>
                <Crdhldr>
                    <Lang>en</Lang>
                </Crdhldr>
            </Envt>
            <Cntxt>
                <PmtCntxt>
                    <CardPres>True</CardPres>
                    <CrdhldrPres>True</CrdhldrPres>
                    <AttndncCntxt>ATTD</AttndncCntxt>
                    <CardDataNtryMd>ECTL</CardDataNtryMd>
                </PmtCntxt>
                <SaleCntxt>
                    <SaleId>80905678</SaleId>
                </SaleCntxt>
            </Cntxt>
            <Tx>
                <TxCaptr>false</TxCaptr>
                <TxTp>CRDP</TxTp>
                <MrchntCtgyCd>5499</MrchntCtgyCd>
                <CstmrCnsnt>True</CstmrCnsnt>
                <CardPrgrmmApld>MAESTRO</CardPrgrmmApld>
                <TxId>
                    <TxDtTm>2022-08-07T19:09:32.1+04:00</TxDtTm>
                    <TxRef>1659884972685</TxRef>
                </TxId>
                <RcncltnId>1</RcncltnId>
                <TxDtls>
                    <Ccy>AED</Ccy>
                    <TtlAmt>0.2</TtlAmt>
                    <AmtQlfr>ACTL</AmtQlfr>
                    <OnLineRsn>ICCF</OnLineRsn>
                    <SaleItm>
                        <PdctCd>41</PdctCd>
                        <PdctAmt>0.2</PdctAmt>
                    </SaleItm>
                    <ICCRltdData>AQIDBAUGCAkK</ICCRltdData>
                </TxDtls>
                <AddtlTxData>/Card/CardProductProfile/0023</AddtlTxData>
            </Tx>
        </AuthstnReq>
        <SctyTrlr>
            <CnttTp>AUTH</CnttTp>
            <AuthntcdData>
                <Rcpt>
                    <KEK>
                        <KEKId>
                            <KeyId>A2MacTestKey</KeyId>
                            <KeyVrsn>2016051911</KeyVrsn>
                            <DerivtnId>CQgDCR0nKgk=</DerivtnId>
                        </KEKId>
                        <KeyNcrptnAlgo>
                            <Algo>DKP9</Algo>
                        </KeyNcrptnAlgo>
                        <NcrptdKey>AwQJCg==</NcrptdKey>
                    </KEK>
                </Rcpt>
                <MACAlgo>
                    <Algo>MCCS</Algo>
                </MACAlgo>
                <NcpsltdCntt>
                    <CnttTp>DATA</CnttTp>
                </NcpsltdCntt>
                <MAC>CQAIAwEDAgQ=</MAC>
            </AuthntcdData>
        </SctyTrlr>
    </AccptrAuthstnReq>
</Document>