package com.uaepay.cmf.ext.service.validator.control

import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder
import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.common.enums.ControlRequestType
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest
import com.uaepay.common.domain.Extension
import com.uaepay.common.util.money.Money
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 *
 * <p>PreAuthRequestCommonValidatorTest</p>
 *
 * <AUTHOR>
 * @version PreAuthRequestCommonValidatorTest.java v1.0  2022/10/20 10:04
 */
class PreAuthRequestCommonValidatorTest extends Specification {
    @Mock
    OrderLoaderService orderLoaderService
    @Mock
    InstOrderRepository instOrderRepository
    @Mock
    Logger log
    @InjectMocks
    PreAuthRequestCommonValidator preAuthRequestCommonValidator

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "validate #v_name"() {
        given:
        when(instOrderRepository.loadByNo(anyString())).thenReturn(v_inst_order)
        when(orderLoaderService.loadPreOrder(anyString(), anyString(), any(), any())).thenReturn(v_pre_order)

        when:

        CmfControlRequest request = new CmfControlRequest(requestNo: v_request_no,
                preRequestNo: "requestNo",
                amount: v_amount,
                requestType: v_request_type,
                extension: new Extension())
        request.getExtension().add("orgiFundinOrderNo", v_orgi_key)
        preAuthRequestCommonValidator.validate(request)
        throw new Exception("Success")

        then:
        def ex = thrown(Exception)
        expectedResult == ex.message

        where:
        v_name                     || v_amount                || v_request_type                      || v_inst_order                                                                           || v_pre_order                                || v_request_no           || expectedResult                            || v_orgi_key
        "Empty Request"            || new Money("100", "AED") || ControlRequestType.PREAUTH_UPDATE   || null                                                                                   || null                                       || null                   || "请求号不能为空"                          || "1001"
        "None Exists Request"      || new Money("100", "AED") || ControlRequestType.PREAUTH_COMPLETE || null                                                                                   || null                                       || "D2011908677770099566" || "original fund in order no is non-exists" || "1002"
        "PreAuth Status INIT"      || new Money("100", "AED") || ControlRequestType.PREAUTH_UPDATE   || new InstOrder(status: InstOrderStatus.IN_PROCESS)                                      || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || "原订单状态不正确"                        || "1002"
        "PreAuth Status PROCESS"   || new Money("100", "AED") || ControlRequestType.PREAUTH_COMPLETE || new InstOrder(status: InstOrderStatus.IN_PROCESS)                                      || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || "原订单状态不正确"                        || "1002"
        "PreAuth Void"             || new Money("100", "AED") || ControlRequestType.PREAUTH_VOID     || new InstOrder(status: InstOrderStatus.IN_PROCESS)                                      || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || "原订单状态不正确"                        || "1002"
        "PreAuth Void Amount Diff" || new Money("100", "AED") || ControlRequestType.PREAUTH_VOID     || new InstOrder(status: InstOrderStatus.HALF_SUCCESSFUL, amount: new Money("30", "AED")) || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || "撤销金额与原订单金额必须一致"            || "1002"
        "PreAuth Void Amount Diff" || null                    || ControlRequestType.PREAUTH_VOID     || new InstOrder(status: InstOrderStatus.HALF_SUCCESSFUL, amount: new Money("30", "AED")) || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || "Success"                                 || "1002"

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme