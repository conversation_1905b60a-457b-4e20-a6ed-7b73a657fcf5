package com.uaepay.cmf.ext.service.mcr

import com.uaepay.cmf.common.domain.ChannelControlResult
import com.uaepay.cmf.common.domain.ChannelFundResult
import com.uaepay.cmf.common.enums.ControlRequestType
import com.uaepay.cmf.common.enums.FundChannelApiType
import com.uaepay.cmf.common.util.CommonUtils
import com.uaepay.cmf.domainservice.main.general.impl.APlusMerchantRegisterProcessor
import com.uaepay.cmf.domainservice.main.general.impl.APlusMerchantRegisterQueryProcessor
import com.uaepay.cmf.domainservice.main.sender.ControlOrderSendService
import com.uaepay.cmf.domainservice.main.sender.InstOrderSendService
import com.uaepay.cmf.domainservice.main.spi.FundOutRejectService
import com.uaepay.cmf.domainservice.main.spi.impl.DefaultSubmitInstitutionService
import com.uaepay.cmf.ext.service.impl.DefaultFundRequestFacade
import com.uaepay.cmf.ext.service.process.impl.FundProcessService
import com.uaepay.cmf.service.facade.api.RegisterFacade
import com.uaepay.cmf.service.facade.domain.CmfRequest
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterQueryRequest
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterRequest
import com.uaepay.cmf.test.CmfApplication
import com.uaepay.common.domain.Extension
import com.uaepay.common.util.money.Money
import com.uaepay.payment.common.v2.enums.PayMode
import com.uaepay.schema.cmf.enums.BizType
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.web.WebAppConfiguration
import spock.lang.Specification
import spock.lang.Unroll

import javax.annotation.Resource
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

import static org.mockito.Mockito.when

/**
 *
 * <p>预授权测试用例</p>
 *
 * <AUTHOR>
 * @version MerchantRegisterSpringTest.java v1.0  2022/10/17 11:40
 */
@ContextConfiguration
@SpringBootTest(classes = CmfApplication.class, properties = "spring.profiles.active=dev,unittest,manual")
@WebAppConfiguration
class MerchantRegisterSpringTest extends Specification {


    @Resource
    @InjectMocks
    FundProcessService fundProcessService

    @Mock
    FundOutRejectService fundOutRejectService;

    @Resource
    @InjectMocks
    DefaultSubmitInstitutionService submitInstitutionService

    @Mock
    InstOrderSendService instOrderSendService

    @Mock
    ControlOrderSendService controlOrderSendService

    @InjectMocks
    @Resource
    DefaultFundRequestFacade fundRequestFacade

    @InjectMocks
    @Resource
    APlusMerchantRegisterProcessor aPlusMerchantRegisterProcessor;
    @Resource
    APlusMerchantRegisterQueryProcessor aPlusMerchantRegisterQueryProcessor;

    def setup() {
        MockitoAnnotations.openMocks(this)
    }


    @Unroll("registerFacade where name=#v_name")
    def "registerFacade.register"() {
        given:
        def cmfRequest = buildCmfRequest()

        if (v_requestNo != null) {
            cmfRequest.setPaymentSeqNo(v_requestNo)
        }

        def request = new APlusMerchantRegisterRequest(requestNo: "********",
                merchantId: "20000",
                "merchantName":"fsdfds",
                "merchantMcc":"1111",
                "registrationNo":"fdsfds",
                "merchantAddress":"fdsfds",
                "storeId":"12342",
                "storeName":"fsfsdfs",
                "storeMcc":"2222",
                "storeAddress":"fsfds")

        def response = new ChannelFundResult();

        when(instOrderSendService.send(Mockito.any())).thenReturn(response)
        when(fundOutRejectService.getRejectAccountType(Mockito.any())).thenReturn(Mockito.any())

        when:
        def result = aPlusMerchantRegisterProcessor.process(request)

        then:
        v_result_code == result.getCode()
        v_result_message == result.getMessage()

        where:
        v_name || v_requestNo || v_api_result_code || v_api_result_sub_code      || v_result_code || v_result_message
        "Pass" || null        || "S"               || "reservation_initial.APPR" || "U" || "UNKNOWN"

    }


    @Unroll("registerQuery where name=#v_name")
    def "registerQuery.query"() {
        given:
        def cmfRequest = buildCmfRequest()

        if (v_requestNo != null) {
            cmfRequest.setPaymentSeqNo(v_requestNo)
        }

        def request = new APlusMerchantRegisterQueryRequest(preRequestNo: "********")

        def response = new ChannelFundResult();

        when(instOrderSendService.send(Mockito.any())).thenReturn(response)
        when(fundOutRejectService.getRejectAccountType(Mockito.any())).thenReturn(Mockito.any())

        when:
        def result = aPlusMerchantRegisterQueryProcessor.process(request)

        then:
        v_result_code == result.getCode()
        v_result_message == result.getMessage()

        where:
        v_name || v_requestNo || v_api_result_code || v_api_result_sub_code      || v_result_code || v_result_message
        "Pass" || null        || "S"               || "reservation_initial.APPR" || "U" || "UNKNOWN"

    }



    private CmfRequest buildCmfRequest() {
        CmfRequest request = new CmfRequest()
        request.setInstCode("FISERV")
        request.setPaymentSeqNo(LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE) + 'FS' + System.currentTimeMillis())
        request.setProductCode("********")
        request.setPaymentCode("4001")
        request.setPayMode(PayMode.QUICKPAY)
        request.setBizType(BizType.FUNDIN)
        request.setMemberId("anonymousMember")
        request.setAmount(new Money("100", "AED"))
        request.setBizTime(new Date())
        Extension extension = new Extension()
        extension.add("sourceCode", "POS_FISERV")
        extension.add("preAuth", "Y")
        extension.add("payLoad", "C3482033")
        extension.add("merchantId", "8116000002")
        extension.add("terminalId", "80905678")
        request.setExtension(extension)
        return request
    }


    CmfControlRequest buildControlRequest() {
        CmfControlRequest request = new CmfControlRequest()
        request.setRequestNo("D" + System.currentTimeMillis())
        request.setRequestType(ControlRequestType.PREAUTH_UPDATE)
        request.setPayMode(PayMode.QUICKPAY)
        request.setInstCode("FISERV")
        request.setAmount(new Money("100", "AED"))
        Extension extension = new Extension()
        extension.add("sourceCode", "POS_FISERV")
        extension.add("sourceOrder", "inst")
        extension.add("payLoad", "C3467039")
        extension.add("merchantId", "000008116000002")
        extension.add("terminalId", "80905679")
        request.setExtension(extension)
        return request;
    }

}
