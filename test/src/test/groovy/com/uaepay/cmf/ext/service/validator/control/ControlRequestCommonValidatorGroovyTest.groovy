package com.uaepay.cmf.ext.service.validator.control

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder
import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.common.enums.ControlRequestType
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest
import com.uaepay.common.domain.Extension
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

class ControlRequestCommonValidatorGroovyTest extends Specification {
    @Mock
    Logger logger
    @Mock
    InstOrderRepository instOrderRepository
    @Mock
    InstControlOrderRepository instControlOrderRepository
    @Mock
    OrderLoaderService orderLoaderService
    @InjectMocks
    ControlRequestCommonValidator controlRequestCommonValidator

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "validate where name=#v_name"() {
        given:
        when(instOrderRepository.loadByNo(anyString())).thenReturn(v_inst_order)
        //正常情况下返回null
        when(instControlOrderRepository.loadByRequestNo("noneExistRequestNo")).thenReturn(null)
        //已存在订单,返回订单
        when(instControlOrderRepository.loadByRequestNo("existRequestNo")).thenReturn(new InstControlOrder())

        when(orderLoaderService.loadPreOrder(anyString(), anyString(), any(), any())).thenReturn(v_base_inst_orderNo)

        when:
        CmfControlRequest request = new CmfControlRequest(requestNo: v_requestNo,
                preRequestNo: "requestNo",
                requestType: v_request_type,
                extension: new Extension())
        println("instcontrolorder = " + instControlOrderRepository.loadByRequestNo("requestNo"))
        request.getExtension().add(ExtensionKey.ORGI_FUNDIN_ORDER_NO.key, v_orgi_key)
        println("key = " + request.getExtension().getValue(ExtensionKey.ORGI_FUNDIN_ORDER_NO.key))
        controlRequestCommonValidator.validate(request)
        throw new Exception("")
        then:
        def ex = thrown(Exception)
        v_exception == ex.getMessage()

        where:
        v_name                      || v_requestNo          || v_inst_order                                      || v_base_inst_orderNo                           || v_request_type                   || v_orgi_key || v_exception
        "Origin Order Failure"      || "noneExistRequestNo" || new InstOrder(status: InstOrderStatus.FAILURE)    || new InstBaseOrder(instOrderNo: "instOrderNo") || ControlRequestType.ADVANCE       || "1000"     || "原订单已失败"
        "Dont repeat pay "          || "noneExistRequestNo" || new InstOrder(status: InstOrderStatus.SUCCESSFUL) || new InstBaseOrder(instOrderNo: "instOrderNo") || ControlRequestType.ADVANCE       || "1000"     || "原订单已经支付成功，请不要重复支付"
        "request No not exist"      || "noneExistRequestNo" || null                                              || new InstBaseOrder(instOrderNo: "instOrderNo") || ControlRequestType.ADVANCE       || "1000"     || "原请求号不存在"
        "Pass"                      || "noneExistRequestNo" || new InstOrder(status: InstOrderStatus.IN_PROCESS) || new InstBaseOrder(instOrderNo: "instOrderNo") || ControlRequestType.ADVANCE       || "1000"     || ""
        "Pass Not Advance"          || "noneExistRequestNo" || new InstOrder(status: InstOrderStatus.IN_PROCESS) || new InstBaseOrder(instOrderNo: "instOrderNo") || ControlRequestType.ADVANCE_QUERY || "1000"     || ""
        "Pass Not Advance"          || "noneExistRequestNo" || new InstOrder(status: InstOrderStatus.IN_PROCESS) || new InstBaseOrder(instOrderNo: "instOrderNo") || ControlRequestType.ADVANCE_QUERY || null       || ""
        "request No. repeat"        || "existRequestNo"     || null                                              || new InstBaseOrder(instOrderNo: "instOrderNo") || ControlRequestType.ADVANCE       || "1000"     || "请求号重复"
        "request No. can't be null" || null                 || null                                              || null                                          || ControlRequestType.ADVANCE       || "1000"     || "请求号不能为空"
        "NonePre InstOrder"         || "noneExistRequestNo" || new InstOrder(status: InstOrderStatus.IN_PROCESS) || null                                          || ControlRequestType.ADVANCE       || "1000"     || null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme