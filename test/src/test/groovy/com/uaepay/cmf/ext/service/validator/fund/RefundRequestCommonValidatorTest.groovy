package com.uaepay.cmf.ext.service.validator.fund

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder
import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService
import com.uaepay.cmf.service.facade.domain.CmfRequest
import com.uaepay.common.domain.Extension
import com.uaepay.common.util.money.Money
import com.uaepay.schema.cmf.enums.BizType
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 *
 * <p>RefundRequestCommonValidatorTest</p>
 *
 * <AUTHOR>
 * @version RefundRequestCommonValidatorTest.java v1.0  2022/10/20 16:21
 */
class RefundRequestCommonValidatorTest extends Specification {
    @Mock
    Logger logger
    @Mock
    OrderLoaderService orderLoaderService
    @Mock
    InstOrderRepository instOrderRepository
    @Mock
    Money ZERO_MONEY
    @Mock
    BigDecimal ZERO
    @Mock
    Money SPLIT_MIN_AMOUNT
    @InjectMocks
    RefundRequestCommonValidator refundRequestCommonValidator

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "validate where model=#model"() {
        given:
        when(orderLoaderService.loadPreOrder(any(), any(), any(), any())).thenReturn(preOrder)
        when(instOrderRepository.getHasRefundAmount(any(), any())).thenReturn(refundAmount)

        when:
        request.setProductCode("101011")
        request.setPaymentCode("refund")
        request.getExtension().add("orgiFundinOrderNo", origiFundinOrderNo)
        request.getExtension().add("orgiSettlementId", origiFundinOrderNo)
        refundRequestCommonValidator.validate(request)

        then:
        def ex = thrown(Exception)
        expectedResult == ex.message

        where:
        model                          || refundAmount          || request                                                                                        || origiFundinOrderNo || preOrder                                                                        || instOrder       || expectedResult
        "Original Order non Exists"    || new Money("0", "AED") || new CmfRequest(extension: new Extension(), paymentSeqNo: "111", amount: new Money("1", "AED")) || "existsOrderNo"    || null                                                                            || new InstOrder() || "未找到原入款订单"
        "Original Order Cannot Refund" || new Money("0", "AED") || new CmfRequest(extension: new Extension(), paymentSeqNo: "111", amount: new Money("1", "AED")) || "existsOrderNo"    || new InstOrder(bizType: BizType.FUNDOUT)                                         || new InstOrder() || "原订单不可退款"
        "Original Order Cannot Refund" || new Money("0", "AED") || new CmfRequest(extension: new Extension(), paymentSeqNo: "111", amount: new Money("1", "AED")) || "existsOrderNo"    || new InstOrder(bizType: BizType.FUNDIN, status: InstOrderStatus.RISK)            || new InstOrder() || "原订单不可退款"
        "Original Order Cannot Refund" || new Money("0", "AED") || new CmfRequest(extension: new Extension(), paymentSeqNo: "111", amount: new Money("1", "AED")) || "existsOrderNo"    || new InstOrder(bizType: BizType.FUNDIN, status: InstOrderStatus.SUCCESSFUL)      || new InstOrder() || "原订单不可退款"
        "Original Order Cannot Refund" || new Money("1", "AED") || new CmfRequest(extension: new Extension(), paymentSeqNo: "111", amount: new Money("1", "AED")) || "existsOrderNo"    || new InstOrder(bizType: BizType.FUNDIN, status: InstOrderStatus.HALF_SUCCESSFUL) || new InstOrder() || "原订单不可退款"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme