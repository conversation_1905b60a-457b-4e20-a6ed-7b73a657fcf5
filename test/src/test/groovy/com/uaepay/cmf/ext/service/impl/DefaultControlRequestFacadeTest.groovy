package com.uaepay.cmf.ext.service.impl

import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus
import com.uaepay.cmf.common.core.domain.exception.AppRuntimeException
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult
import com.uaepay.cmf.common.enums.ControlRequestType
import com.uaepay.cmf.common.enums.FundChannelApiType
import com.uaepay.cmf.common.util.CommonUtils
import com.uaepay.cmf.domainservice.main.general.impl.*
import com.uaepay.cmf.domainservice.main.process.impl.DuplicateControlOrderResultProcessService
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService
import com.uaepay.cmf.ext.service.validator.factory.ValidatorFactory
import com.uaepay.cmf.service.facade.domain.CmfCommonResultCode
import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceRequest
import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceResult
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthRequest
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthResponse
import com.uaepay.cmf.service.facade.domain.clear.ClearInfo
import com.uaepay.cmf.service.facade.domain.clear.ClearInfoQueryRequest
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult
import com.uaepay.cmf.service.facade.domain.control.CmfFileRequest
import com.uaepay.cmf.service.facade.domain.control.CmfFileResponse
import com.uaepay.cmf.service.facade.domain.control.config.ChannelConfigQueryRequest
import com.uaepay.cmf.service.facade.domain.control.config.ChannelConfigQueryResponse
import com.uaepay.cmf.service.facade.domain.control.psp.PspReversalRequest
import com.uaepay.cmf.service.facade.domain.control.psp.PspReversalResponse
import com.uaepay.cmf.service.facade.result.ListQueryResult
import com.uaepay.common.util.money.Money
import com.uaepay.validate.exception.ValidationException
import org.mockito.ArgumentMatchers
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.doReturn
import static org.mockito.Mockito.when

/**
 *
 * <p>DefaultControlRequestFacadeTest</p>
 *
 * <AUTHOR>
 * @version DefaultControlRequestFacadeTest.java v1.0  2022/10/20 11:58
 */
class DefaultControlRequestFacadeTest extends Specification {
    @Mock
    Logger logger
    @Mock
    ValidatorFactory validatorFactory
    @Mock
    SubmitInstitutionService submitInstitutionService
    @Mock
    DownloadFileProcessor downloadFileProcessor
    @Mock
    FileMigrateProcessor fileMigrateProcessor
    @Mock
    FileImportProcessor fileImportProcessor
    @Mock
    AuthProcessor authProcessor
    @Mock
    Advance3ds2Processor advance3ds2Processor
    @Mock
    ClearInfoProcessor clearInfoProcessor
    @Mock
    PspReversalProcessor pspReversalProcessor
    @Mock
    ChannelConfigQueryProcessor channelConfigQueryProcessor
    @Mock
    DuplicateControlOrderResultProcessService duplicateControlOrderResultProcessService
    @Mock
    Money ZERO_MONEY
    @Mock
    BigDecimal ZERO
    @Mock
    Money SPLIT_MIN_AMOUNT
    @InjectMocks
    DefaultControlRequestFacade defaultControlRequestFacade

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "control #v_name"() {
        given:

        doReturn(null).when(validatorFactory).load(ArgumentMatchers.any() as ControlRequestType);
        when(submitInstitutionService.submit(ArgumentMatchers.any() as InstControlOrder)).thenReturn(v_controlResult)
        when(duplicateControlOrderResultProcessService.queryDuplicateResult(anyString())).thenReturn(duplicate_result)

        when:
        def result = defaultControlRequestFacade.control(request, CommonUtils.defaultOpEnv())
        then:
        result.resultCode.name() == expectedResultCode

        where:
        v_name || request                                                                                || duplicate_result       || v_controlResult                                                      || expectedResultCode
        "Pass" || new CmfControlRequest(requestNo: "requestNo", requestType: ControlRequestType.ADVANCE) || null                   || new InstControlOrderResult(status: InstOrderResultStatus.SUCCESSFUL) || "SUCCESS"
        "Pass" || new CmfControlRequest(requestNo: "requestNo", requestType: ControlRequestType.ADVANCE) || new CmfControlResult() || new InstControlOrderResult(status: InstOrderResultStatus.SUCCESSFUL) || "SUCCESS"
        "Pass" || new CmfControlRequest(requestNo: "requestNo", requestType: ControlRequestType.ADVANCE) || null                   || new InstControlOrderResult(status: InstOrderResultStatus.SUCCESSFUL) || "SUCCESS"
    }

    @Unroll
    def "control exception #v_name"() {
        given:

        doReturn(null).when(validatorFactory).load(ArgumentMatchers.any() as ControlRequestType);
        when(duplicateControlOrderResultProcessService.queryDuplicateResult(anyString())).thenReturn(null)
        when(submitInstitutionService.submit(ArgumentMatchers.any() as InstControlOrder)).thenAnswer(new Answer<Object>() {
            @Override
            Object answer(InvocationOnMock invocation) throws Throwable {
                def order = invocation.getArgument(0, InstControlOrder.class)

                if (order.getApiType() == FundChannelApiType.PREAUTH_UPDATE) {
                    throw new ValidationException("校验失败")
                }
                if (order.getApiType() == FundChannelApiType.PREAUTH_COMPLETE) {
                    throw new AppRuntimeException("处理失败")
                }

                if (order.getApiType() == FundChannelApiType.PREAUTH_VOID) {
                    throw new Exception("系统异常")
                }
                return new InstControlOrderResult(status: InstOrderResultStatus.SUCCESSFUL)
            }
        })

        when:
        def result = defaultControlRequestFacade.control(request, CommonUtils.defaultOpEnv())

        then:
        result.resultCode == expectedResultCode

        where:
        v_name                || request                                                                                         || expectedResultCode
        "ValidateException"   || new CmfControlRequest(requestNo: "requestNo", requestType: ControlRequestType.PREAUTH_UPDATE)   || CmfCommonResultCode.VALIDARE_ERROR
        "AppRuntimeException" || new CmfControlRequest(requestNo: "requestNo", requestType: ControlRequestType.PREAUTH_COMPLETE) || CmfCommonResultCode.FAILED
        "Exception"           || new CmfControlRequest(requestNo: "requestNo", requestType: ControlRequestType.PREAUTH_VOID)     || CmfCommonResultCode.UNKNOW_EXCEPTION
    }

    @Unroll
    def "advance where request=#request then expect: #expectedResult"() {
        given:
        when(downloadFileProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileMigrateProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileImportProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(authProcessor.process(any())).thenReturn(new CmfAuthResponse())
        when(advance3ds2Processor.process(any())).thenReturn(new CmfAdvanceResult())
        when(clearInfoProcessor.process(any())).thenReturn(new ListQueryResult<ClearInfo>())
        when(pspReversalProcessor.process(any())).thenReturn(new PspReversalResponse())
        when(channelConfigQueryProcessor.process(any())).thenReturn(new ChannelConfigQueryResponse())

        expect:
        defaultControlRequestFacade.advance(request) == expectedResult

        where:
        request                 || expectedResult
        new CmfAdvanceRequest() || new CmfAdvanceResult()
    }

    @Unroll
    def "auth where request=#request then expect: #expectedResult"() {
        given:
        when(downloadFileProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileMigrateProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileImportProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(authProcessor.process(any())).thenReturn(new CmfAuthResponse())
        when(advance3ds2Processor.process(any())).thenReturn(new CmfAdvanceResult())
        when(clearInfoProcessor.process(any())).thenReturn(new ListQueryResult<ClearInfo>())
        when(pspReversalProcessor.process(any())).thenReturn(new PspReversalResponse())
        when(channelConfigQueryProcessor.process(any())).thenReturn(new ChannelConfigQueryResponse())

        expect:
        defaultControlRequestFacade.auth(request) == expectedResult

        where:
        request              || expectedResult
        new CmfAuthRequest() || new CmfAuthResponse()
    }

    @Unroll
    def "process File where request=#request then expect: #expectedResult"() {
        given:
        when(downloadFileProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileMigrateProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileImportProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(authProcessor.process(any())).thenReturn(new CmfAuthResponse())
        when(advance3ds2Processor.process(any())).thenReturn(new CmfAdvanceResult())
        when(clearInfoProcessor.process(any())).thenReturn(new ListQueryResult<ClearInfo>())
        when(pspReversalProcessor.process(any())).thenReturn(new PspReversalResponse())
        when(channelConfigQueryProcessor.process(any())).thenReturn(new ChannelConfigQueryResponse())

        expect:
        defaultControlRequestFacade.processFile(request) == expectedResult

        where:
        request                                                     || expectedResult
        new CmfFileRequest(requestType: ControlRequestType.ADVANCE) || new CmfFileResponse()
    }

    @Unroll
    def "query Clear Info where request=#request then expect: #expectedResult"() {
        given:
        when(downloadFileProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileMigrateProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileImportProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(authProcessor.process(any())).thenReturn(new CmfAuthResponse())
        when(advance3ds2Processor.process(any())).thenReturn(new CmfAdvanceResult())
        when(clearInfoProcessor.process(any())).thenReturn(new ListQueryResult<ClearInfo>())
        when(pspReversalProcessor.process(any())).thenReturn(new PspReversalResponse())
        when(channelConfigQueryProcessor.process(any())).thenReturn(new ChannelConfigQueryResponse())

        expect:
        defaultControlRequestFacade.queryClearInfo(request) == expectedResult

        where:
        request                     || expectedResult
        new ClearInfoQueryRequest() || new ListQueryResult<ClearInfo>()
    }

    @Unroll
    def "psp Reversal where request=#request then expect: #expectedResult"() {
        given:
        when(downloadFileProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileMigrateProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileImportProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(authProcessor.process(any())).thenReturn(new CmfAuthResponse())
        when(advance3ds2Processor.process(any())).thenReturn(new CmfAdvanceResult())
        when(clearInfoProcessor.process(any())).thenReturn(new ListQueryResult<ClearInfo>())
        when(pspReversalProcessor.process(any())).thenReturn(new PspReversalResponse())
        when(channelConfigQueryProcessor.process(any())).thenReturn(new ChannelConfigQueryResponse())

        expect:
        defaultControlRequestFacade.pspReversal(request) == expectedResult

        where:
        request                  || expectedResult
        new PspReversalRequest() || new PspReversalResponse()
    }

    @Unroll
    def "query Channel Config where request=#request then expect: #expectedResult"() {
        given:
        when(downloadFileProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileMigrateProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(fileImportProcessor.process(any())).thenReturn(new CmfFileResponse())
        when(authProcessor.process(any())).thenReturn(new CmfAuthResponse())
        when(advance3ds2Processor.process(any())).thenReturn(new CmfAdvanceResult())
        when(clearInfoProcessor.process(any())).thenReturn(new ListQueryResult<ClearInfo>())
        when(pspReversalProcessor.process(any())).thenReturn(new PspReversalResponse())
        when(channelConfigQueryProcessor.process(any())).thenReturn(new ChannelConfigQueryResponse())

        expect:
        defaultControlRequestFacade.queryChannelConfig(request) == expectedResult

        where:
        request                         || expectedResult
        new ChannelConfigQueryRequest() || new ChannelConfigQueryResponse()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme