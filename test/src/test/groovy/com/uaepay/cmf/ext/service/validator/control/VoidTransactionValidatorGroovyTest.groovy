package com.uaepay.cmf.ext.service.validator.control

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder
import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.common.enums.ControlRequestType
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest
import com.uaepay.common.domain.Extension
import com.uaepay.common.util.DateUtil
import com.uaepay.common.util.money.Money
import com.uaepay.schema.cmf.enums.BizType
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 *
 * <p>VoidTransactionValidatorTest</p>
 *
 * <AUTHOR>
 * @version VoidTransactionValidatorTest.java v1.0  2022/10/20 10:57
 */
class VoidTransactionValidatorGroovyTest extends Specification {
    @Mock
    InstOrderRepository instOrderRepository
    @Mock
    OrderLoaderService orderLoaderService
    @Mock
    Logger log
    @InjectMocks
    VoidTransactionValidator voidTransactionValidator

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "validate #v_name"() {
        given:
        when(instOrderRepository.loadByNo(anyString())).thenReturn(v_inst_order)
        when(orderLoaderService.loadPreOrder(anyString(), anyString(), any(), any())).thenReturn(v_pre_order)
        when(instOrderRepository.updateInstOrderStatus(any(), any())).thenReturn(true)

        when:
        CmfControlRequest request = new CmfControlRequest(requestNo: v_request_no,
                preRequestNo: "requestNo",
                requestType: ControlRequestType.VOID_TRANSACTION,
                extension: new Extension())
        request.getExtension().add("orgiFundinOrderNo", v_orgi_key)

        if (v_pos) {
            request.getExtension().add("sourceCode", "POS_FISERV")
        }
        voidTransactionValidator.validate(request)
        throw new Exception("")
        then:
        def ex = thrown(Exception)
        expectedResult == ex.message


        where:
        v_name                 || v_pos || v_inst_order                                                                       || v_pre_order                                || v_request_no           || expectedResult                || v_orgi_key
        "Empty Inst Order"     || false || null                                                                               || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || "机构订单不存在"              || "1002"
        "FundOut Error Date"   || false || new InstOrder(bizType: BizType.FUNDOUT, gmtBookingSubmit: DateUtil.beforeDate)     || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || "已超过发送时间，交易不可撤销" || "1002"
        "FundOut Archived"     || false || new InstOrder(bizType: BizType.FUNDOUT, archiveBatchId: 1l)                        || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || "订单已打批，不可撤销"         || "1002"
        "FundOut Send Success" || false || new InstOrder(bizType: BizType.FUNDOUT, communicateStatus: CommunicateStatus.SENT) || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || "订单已发送渠道，不可撤销"     || "1002"
        "FundIn Fiserv Pos"    || true  || new InstOrder(bizType: BizType.FUNDIN, communicateStatus: CommunicateStatus.SENT)  || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || ""                            || "1002"
        "FundIn Process"       || false || new InstOrder(bizType: BizType.FUNDIN, status: InstOrderStatus.IN_PROCESS)         || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || "机构订单不可撤销，请确认"     || "1002"
        "FundIn Cancel"        || false || new InstOrder(bizType: BizType.FUNDIN, status: InstOrderStatus.CANCEL)             || new InstBaseOrder(instOrderNo: "20220101") || "D2011908677770099566" || ""                            || "1002"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme