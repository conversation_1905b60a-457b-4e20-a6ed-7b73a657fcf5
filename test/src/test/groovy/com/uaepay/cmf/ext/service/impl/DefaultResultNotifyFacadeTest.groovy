package com.uaepay.cmf.ext.service.impl


import com.uaepay.cmf.service.facade.api.FundRequestFacade
import com.uaepay.cmf.service.facade.domain.CmfRequest
import com.uaepay.cmf.service.facade.domain.fundout.FundOutCancelRequest
import com.uaepay.cmf.test.CmfApplication
import com.uaepay.common.domain.OperationEnvironment
import com.uaepay.common.util.money.Money
import com.uaepay.payment.common.v2.enums.PayMode
import com.uaepay.schema.cmf.enums.BizType
import com.uaepay.schema.cmf.enums.InstOrderStatus
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.ImportResource
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.context.junit4.SpringRunner
import spock.lang.Specification
import spock.lang.Unroll

import javax.annotation.Resource

@RunWith(SpringRunner.class)
@ExtendWith(SpringExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringBootTest(classes = CmfApplication.class, properties = "spring.profiles.active:dev,test",
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ImportResource("classpath:META-INF/spring/applicationContext-test.xml")
class DefaultResultNotifyFacadeTest extends Specification {
    @Mock
    Logger logger
    @Mock
    Money ZERO_MONEY
    @Mock
    BigDecimal ZERO
    @Mock
    Money SPLIT_MIN_AMOUNT
    @Resource
    DefaultResultNotifyFacade defaultResultNotifyFacade
    @Resource
    FundRequestFacade fundRequestFacade

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "fund Out Notify where result=#result then expect: #expectedResult"() {

        given:
        CmfRequest request = new CmfRequest(
                paymentSeqNo: paymentSeqNo,
                settlementId: "20221227058367011",
                productCode: "60030010",
                paymentCode: "3001",
                payMode: PayMode.BALANCE,
                bizType: BizType.FUNDOUT,
                instCode: "ADCB",
                memberId: "200004681933",
                amount: new Money(new BigDecimal("20.00"), "AED", ),
                bizTime: new Date())
        def result = fundRequestFacade.apply(request, new OperationEnvironment())

        FundOutCancelRequest fundOutCancelRequest = new FundOutCancelRequest()
        fundOutCancelRequest.setInstOrderNo(result.getInstOrderNo())
        fundOutCancelRequest.setInstOrderStatus(InstOrderStatus.F)
        fundOutCancelRequest.setInstSeqNo(paymentSeqNo)

        expect:
        def fundOutCancelResult =  defaultResultNotifyFacade.fundOutCancelNotice(fundOutCancelRequest)
        fundOutCancelResult.getReturnCode() == expectedCode

        where:
        paymentSeqNo          || expectedCode
        "20221227FO022755617" || 0
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme