package com.uaepay.cmf.ext.service.process.impl

import com.uaepay.cmf.common.core.dal.util.DbRouter
import com.uaepay.cmf.common.core.domain.CmfOrder
import com.uaepay.cmf.common.core.domain.exception.AppRuntimeException
import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator
import com.uaepay.cmf.domainservice.main.process.DuplicateResultProcessService
import com.uaepay.cmf.domainservice.main.process.MonitorService
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService
import com.uaepay.cmf.ext.service.validator.service.ValidateService
import com.uaepay.cmf.service.facade.domain.CmfRequest
import com.uaepay.cmf.service.facade.result.CmfFundResult
import com.uaepay.common.domain.Extension
import com.uaepay.common.util.money.Money
import com.uaepay.payment.common.v2.enums.PayMode
import com.uaepay.schema.cmf.enums.BizType
import org.mockito.ArgumentMatchers
import org.slf4j.Logger
import org.springframework.dao.DuplicateKeyException
import org.springframework.transaction.support.TransactionTemplate
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

import static org.mockito.Mockito.*

/**
 *
 * <p>FundProcessServiceTest</p>
 *
 * <AUTHOR>
 * @version FundProcessServiceTest.java v1.0  2022/10/21 13:09
 */
class FundProcessServiceTest extends Specification {
    @Mock
    ValidateService validateService
    @Mock
    DuplicateResultProcessService duplicateResultProcessService
    @Mock
    DbRouter dbRouter
    @Mock
    PrimaryKeyGenerator primaryKeyGenerator
    @Mock
    SubmitInstitutionService submitInstitutionService
    @Mock
    OrderLoaderService orderLoaderService
    @Mock
    Logger log
    @Mock
    CmfOrderRepository cmfOrderRepository
    @Mock
    InstOrderRepository instOrderRepository
    @Mock
    MonitorService monitorService
    @Mock
    TransactionTemplate cmfTransactionTimeoutTemplate
    @InjectMocks
    FundProcessService fundProcessService

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "process where request=#name"() {
        given:
        when(validateService.validate(any())).thenReturn(cmfResult)
        when(duplicateResultProcessService.queryDuplicateResult(any(), any(), any())).thenReturn(new CmfFundResult(channelPayNo: "channelPayNo"))
        when(dbRouter.isShardingMode(anyString(), anyString())).thenReturn(true)
        when(primaryKeyGenerator.generateKey(any())).thenReturn("generateKeyResponse")
        when(submitInstitutionService.submit(any(CmfOrder.class))).thenReturn(new InstOrderResult())
        when(orderLoaderService.loadReturnOrderNo(any(), any())).thenReturn("loadReturnOrderNoResponse")
        when(cmfOrderRepository.store(any())).thenReturn("storeResponse")
        when(cmfOrderRepository.loadByPaymentSeqNo(any(), any())).thenReturn(new CmfOrder())
        when(cmfOrderRepository.loadByCmfSeqNo(any(), anyBoolean())).thenReturn(new CmfOrder())
        when(instOrderRepository.loadByCmfSeqNoSingle(any())).thenReturn(new InstOrder())

        expect:
        CmfRequest request = buildCmfRequest()
        fundProcessService.process(request) != null

        where:
        name || cmfResult                                       || expectedResult
        "1"  || new CmfFundResult(channelPayNo: "channelPayNo") || new CmfFundResult(channelPayNo: "channelPayNo")
        "2"  || null                                            || new CmfFundResult(channelPayNo: "channelPayNo")
    }


    @Unroll
    def "process  Exception where request=#name"() {
        given:
        when(validateService.validate(any())).thenReturn(cmfResult)
        when(duplicateResultProcessService.queryDuplicateResult(any(), any(), any())).thenReturn(new CmfFundResult(channelPayNo: "channelPayNo"))
        when(dbRouter.isShardingMode(anyString(), anyString())).thenReturn(true)
        when(primaryKeyGenerator.generateKey(any())).thenReturn("generateKeyResponse")
        when(submitInstitutionService.submit(any(CmfOrder.class))).thenReturn(new InstOrderResult())
        when(orderLoaderService.loadReturnOrderNo(any(), any())).thenReturn("loadReturnOrderNoResponse")
        when(cmfOrderRepository.store(any())).thenThrow(new DuplicateKeyException("Duplicate Key"))
        when(cmfOrderRepository.loadByPaymentSeqNo(any(), any())).thenReturn(new CmfOrder())
        when(cmfOrderRepository.loadByCmfSeqNo(any(), anyBoolean())).thenReturn(new CmfOrder())
        when(instOrderRepository.loadByCmfSeqNoSingle(any())).thenReturn(new InstOrder())
        when(cmfTransactionTimeoutTemplate.execute(any())).thenThrow(new AppRuntimeException("重复订单请求"))


        expect:
        CmfRequest request = buildCmfRequest()
        fundProcessService.process(request) != null

        where:
        name || cmfResult || expectedResult
        "2"  || null      || new CmfFundResult(channelPayNo: "channelPayNo")
    }

    CmfRequest buildCmfRequest() {
        CmfRequest request = new CmfRequest()
        request.setInstCode("FISERV")
        request.setPaymentSeqNo(LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE) + 'FS' + System.currentTimeMillis())
        request.setProductCode("60040090")
        request.setPaymentCode("4001")
        request.setPayMode(PayMode.QUICKPAY)
        request.setBizType(BizType.FUNDIN)
        request.setMemberId("anonymousMember")
        request.setAmount(new Money("100", "AED"))
        request.setBizTime(new Date())
        Extension extension = new Extension()
        extension.add("sourceCode", "POS_FISERV")
        extension.add("preAuth", "Y")
        extension.add("payLoad", "C3482033")
        extension.add("merchantId", "8116000002")
        extension.add("terminalId", "80905678")
        request.setExtension(extension)
        return request
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme