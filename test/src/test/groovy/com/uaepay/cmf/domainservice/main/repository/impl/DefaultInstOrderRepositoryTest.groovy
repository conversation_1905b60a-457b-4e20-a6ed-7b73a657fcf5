package com.uaepay.cmf.domainservice.main.repository.impl

import com.uaepay.cmf.common.core.dal.daointerface.InstBatchOrderDAO
import com.uaepay.cmf.common.core.dal.daointerface.InstOrderDAO
import com.uaepay.cmf.common.core.dal.dataobject.InstBatchOrderDO
import com.uaepay.cmf.common.core.dal.dataobject.InstOrderDO
import com.uaepay.cmf.common.core.dal.dataobject.UniqueOrderDO
import com.uaepay.cmf.common.core.domain.CmfOrder
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus
import com.uaepay.cmf.common.core.domain.enums.InstOrderArchiveStatus
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus
import com.uaepay.cmf.common.core.domain.enums.IsAdvance
import com.uaepay.cmf.common.core.domain.enums.OrderFlag
import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator
import com.uaepay.cmf.domainservice.main.factory.BizProcessorFactory
import com.uaepay.cmf.domainservice.main.process.biz.impl.FundOutBizProcessor
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository
import com.uaepay.cmf.service.facade.domain.query.BatchOrderQueryRequest
import com.uaepay.cmf.service.facade.enums.ArchiveStatusEnum
import com.uaepay.common.util.money.Money
import com.uaepay.schema.cmf.enums.BizType
import com.uaepay.schema.cmf.enums.YesNo
import org.slf4j.Logger
import org.springframework.transaction.support.TransactionTemplate
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 *
 * <p>DefaultInstOrderRepositoryTest</p>
 *
 * <AUTHOR>
 * @version DefaultInstOrderRepositoryTest.java v1.0  2022/10/20 18:02
 */
class DefaultInstOrderRepositoryTest extends Specification {
    @Mock
    Logger logger
    @Mock
    InstOrderDAO instOrderDAO
    @Mock
    InstBatchOrderDAO instBatchOrderDAO
    @Mock
    PrimaryKeyGenerator primaryKeyGenerator
    @Mock
    TransactionTemplate cmfTransactionTemplate
    @Mock
    CmfOrderRepository cmfOrderRepository
    @Mock
    CardTokenRepository cardTokenRepository
    @Mock
    BizProcessorFactory bizProcessorFactory
    @Mock
    Money ZERO_MONEY
    @Mock
    BigDecimal ZERO
    @Mock
    Money SPLIT_MIN_AMOUNT
    @InjectMocks
    DefaultInstOrderRepository defaultInstOrderRepository

    def setup() {
        MockitoAnnotations.openMocks(this)
    }


    @Unroll
    def "loadInstOrderListByIds"() {
        given:
        when(instOrderDAO.loadInstOrderIdListByIds(any())).thenReturn(orderList)
        when:
        def result = defaultInstOrderRepository.loadInstOrderListByIds(idList)
        then:
        result.size() == expectedResult
        where:
        idList | orderList           || expectedResult
        []     | [new InstOrderDO()] || 0
        [1]    | [new InstOrderDO()] || 1
    }

    @Unroll
    def "updateTemplateBatchId"() {
        given:
        when(instOrderDAO.countArchivePages(anyLong(), anyLong(), anyString(), anyString(), any(), anyInt())).thenReturn(page)
        when(instOrderDAO.queryInstOrderList4ArchivePage(anyLong(), anyLong(), anyString(), anyString(), any(), anyInt())).thenReturn(idList)
        when(cmfTransactionTemplate.execute(any())).thenReturn(idList.size())
        when:
        defaultInstOrderRepository.updateTempBatchId(archiveTemplateId, apiCode, templateBatchId, hours, bookingTime)
        then:
        expectedResult == true
        where:
        hours | idList | page | bookingTime                                                        | apiCode   | archiveTemplateId | templateBatchId || expectedResult
        1l    | [1l]   | 1    | new GregorianCalendar(2022, Calendar.OCTOBER, 20, 18, 2).getTime() | "apiCode" | 1l                | 1l              || true
        1l    | []     | 1    | new GregorianCalendar(2022, Calendar.OCTOBER, 20, 18, 2).getTime() | "apiCode" | 1l                | 1l              || true
    }


    @Unroll
    def "updateInstOrderStatus"() {
        given:
        when(instOrderDAO.updateStatusById(anyLong(), anyString(), anyString())).thenReturn(1)
        when:
        def result = defaultInstOrderRepository.updateInstOrderStatus(instOrder, targetStatus)
        then:
        assert result == expectedResult
        where:
        instOrder                                                               | targetStatus                    || expectedResult
        new InstOrder(status: InstOrderStatus.IN_PROCESS, instOrderId: 1L)      | InstOrderStatus.IN_PROCESS      || false
        new InstOrder(status: InstOrderStatus.IN_PROCESS, instOrderId: 1L)      | InstOrderStatus.HALF_SUCCESSFUL || true
        new InstOrder(status: InstOrderStatus.HALF_SUCCESSFUL, instOrderId: 1L) | InstOrderStatus.SUCCESSFUL      || true
        new InstOrder(status: InstOrderStatus.CANCEL, instOrderId: 1L)          | InstOrderStatus.FAILURE         || true
        new InstOrder(status: InstOrderStatus.FAILURE, instOrderId: 1L)         | InstOrderStatus.FAILURE         || false

    }


    @Unroll
    def "updateInstOrderStatusNoCheck"() {
        given:
        when(instOrderDAO.updateStatusById(anyLong(), anyString(), anyString())).thenReturn(1)
        when:
        def result = defaultInstOrderRepository.updateInstOrderStatusNoCheck(instOrder, targetStatus)
        then:
        assert result == expectedResult
        where:
        instOrder                                                               | targetStatus                    || expectedResult
        new InstOrder(status: InstOrderStatus.IN_PROCESS, instOrderId: 1L)      | InstOrderStatus.IN_PROCESS      || true
        new InstOrder(status: InstOrderStatus.IN_PROCESS, instOrderId: 1L)      | InstOrderStatus.HALF_SUCCESSFUL || true
        new InstOrder(status: InstOrderStatus.HALF_SUCCESSFUL, instOrderId: 1L) | InstOrderStatus.SUCCESSFUL      || true
        new InstOrder(status: InstOrderStatus.CANCEL, instOrderId: 1L)          | InstOrderStatus.FAILURE         || true
        new InstOrder(status: InstOrderStatus.FAILURE, instOrderId: 1L)         | InstOrderStatus.FAILURE         || true

    }


    @Unroll
    def "load Inst Order List4 Archive Page where hours=#hours and bookingTime=#bookingTime and apiCode=#apiCode and archiveTemplateId=#archiveTemplateId and communicateType=#communicateType then expect: #expectedResult"() {
        given:
        when(instOrderDAO.queryInstOrderList4ArchivePage(anyLong(), anyLong(), anyString(), anyString(), any(), anyInt())).thenReturn([1l])

        expect:
        defaultInstOrderRepository.loadInstOrderList4ArchivePage(archiveTemplateId, apiCode, hours, bookingTime, communicateType) == expectedResult

        where:
        hours | bookingTime                                                        | apiCode   | archiveTemplateId | communicateType   || expectedResult
        1l    | new GregorianCalendar(2022, Calendar.OCTOBER, 20, 18, 2).getTime() | "apiCode" | 1l                | "communicateType" || [1l]
    }

    @Unroll
    def "get Archive Pages where hours=#hours and bookingTime=#bookingTime and apiCode=#apiCode and archiveTemplateId=#archiveTemplateId and communicateType=#communicateType then expect: #expectedResult"() {
        given:
        when(instOrderDAO.countArchivePages(anyLong(), anyLong(), anyString(), anyString(), any(), anyInt())).thenReturn(0)

        expect:
        defaultInstOrderRepository.getArchivePages(archiveTemplateId, apiCode, hours, bookingTime, communicateType) == expectedResult

        where:
        hours | bookingTime                                                        | apiCode   | archiveTemplateId | communicateType   || expectedResult
        1l    | new GregorianCalendar(2022, Calendar.OCTOBER, 20, 18, 2).getTime() | "apiCode" | 1l                | "communicateType" || 0
    }


    @Unroll
    def "update Batch Id2 Default where tempBatchId=#tempBatchId then expect: #expectedResult"() {
        given:
        when(instOrderDAO.updateBatchId2Default(anyLong(), anyLong())).thenReturn(0)

        expect:
        defaultInstOrderRepository.updateBatchId2Default(tempBatchId) == expectedResult

        where:
        tempBatchId || expectedResult
        1l          || 0
    }


    @Unroll
    def "update Batch Order Status where archiveBatchId=#archiveBatchId and archiveStatus=#archiveStatus and preStatus=#preStatus then expect: #expectedResult"() {
        given:
        when(instBatchOrderDAO.updateStatusByIdAndPreStatus(anyString(), anyString(), anyLong())).thenReturn(0)

        expect:
        defaultInstOrderRepository.updateBatchOrderStatus(archiveBatchId, archiveStatus, preStatus) == expectedResult

        where:
        archiveBatchId | archiveStatus                   | preStatus                       || expectedResult
        1l             | InstOrderArchiveStatus.AWAITING | InstOrderArchiveStatus.AWAITING || 0
    }

    @Unroll
    def "update Communicate Status By Inst Orders where instOrderList=#instOrderList and preStatus=#preStatus and targetStatus=#targetStatus then expect: #expectedResult"() {
        given:
        when(instOrderDAO.updateCommunicateStatusBatchByIds(any(), anyString(), anyString())).thenReturn(0)

        expect:
        defaultInstOrderRepository.updateCommunicateStatusByInstOrders(instOrderList, targetStatus, preStatus) == expectedResult

        where:
        instOrderList     | preStatus                  | targetStatus               || expectedResult
        [new InstOrder()] | CommunicateStatus.AWAITING | CommunicateStatus.AWAITING || 0
    }

    @Unroll
    def "update Batch Id List By Temp Batch Id where archiveBatchId=#archiveBatchId and tempBatchId=#tempBatchId and instOrderIdList=#instOrderIdList then expect: #expectedResult"() {
        given:
        when(instOrderDAO.updateBatchIdListByTempBatchId(anyLong(), anyLong(), any())).thenReturn(0)

        expect:
        defaultInstOrderRepository.updateBatchIdListByTempBatchId(tempBatchId, archiveBatchId, instOrderIdList) == expectedResult

        where:
        archiveBatchId | tempBatchId | instOrderIdList || expectedResult
        1l             | 1l          | [1l]            || 0
    }

    @Unroll
    def "update Memo By Id where instOrderId=#instOrderId and memo=#memo then expect: #expectedResult"() {
        given:
        when(instOrderDAO.updateMemoById(anyString(), anyLong())).thenReturn(0)

        expect:
        defaultInstOrderRepository.updateMemoById(memo, instOrderId) == expectedResult

        where:
        instOrderId | memo   || expectedResult
        1l          | "memo" || 0
    }


    @Unroll
    def "update Flag With Order Id And Pre Flag where flag=#flag and instOrderId=#instOrderId and preFlag=#preFlag then expect: #expectedResult"() {
        given:
        when(instOrderDAO.updateFlagWithOrderIdAndPreFlag(anyLong(), anyString(), anyString())).thenReturn(0)

        expect:
        defaultInstOrderRepository.updateFlagWithOrderIdAndPreFlag(instOrderId, flag, preFlag) == expectedResult

        where:
        flag              | instOrderId | preFlag           || expectedResult
        OrderFlag.DEFAULT | 1l          | OrderFlag.DEFAULT || 0
    }

    @Unroll
    def "update Status By Id where instOrderId=#instOrderId and preStatus=#preStatus and targetStatus=#targetStatus then expect: #expectedResult"() {
        given:
        when(instOrderDAO.updateStatusById(anyLong(), anyString(), anyString())).thenReturn(0)

        expect:
        defaultInstOrderRepository.updateStatusById(instOrderId, targetStatus, preStatus) == expectedResult

        where:
        instOrderId | preStatus                  | targetStatus               || expectedResult
        0l          | InstOrderStatus.IN_PROCESS | InstOrderStatus.IN_PROCESS || 0
    }

    @Unroll
    def "update Amount And Extension where amount=#amount and extension=#extension and instOrderId=#instOrderId then expect: #expectedResult"() {
        given:
        when(instOrderDAO.updateAmountAndExtension(anyLong(), any(), anyString())).thenReturn(0)

        expect:
        defaultInstOrderRepository.updateAmountAndExtension(instOrderId, amount, extension) == expectedResult

        where:
        amount | extension   | instOrderId || expectedResult
        null   | "extension" | 0l          || 0
    }

    @Unroll
    def "update Communicate Status By Id where communicateStatus=#communicateStatus and instOrderId=#instOrderId then expect: #expectedResult"() {
        given:
        when(instOrderDAO.updateCommunicateStatusById(anyString(), anyLong())).thenReturn(0)

        expect:
        defaultInstOrderRepository.updateCommunicateStatusById(communicateStatus, instOrderId) == expectedResult

        where:
        communicateStatus          | instOrderId || expectedResult
        CommunicateStatus.AWAITING | 0l          || 0
    }

    @Unroll
    def "sum Amount For Query Result where orderType=#orderType and endDate=#endDate and communicateStatusList=#communicateStatusList and channelCodeList=#channelCodeList and startDate=#startDate then expect: #expectedResult"() {
        given:
        when(instOrderDAO.sumAmountForQueryResult(any(), anyString(), any(), any(), any())).thenReturn(null)

        expect:
        defaultInstOrderRepository.sumAmountForQueryResult(communicateStatusList, orderType, channelCodeList, startDate, endDate) == expectedResult

        where:
        orderType   | endDate                                                            | communicateStatusList | channelCodeList | startDate                                                          || expectedResult
        "orderType" | new GregorianCalendar(2022, Calendar.OCTOBER, 20, 18, 2).getTime() | ["String"]            | ["String"]      | new GregorianCalendar(2022, Calendar.OCTOBER, 20, 18, 2).getTime() || null
    }

    @Unroll
    def "update Advance Status With Pre Status where instOrderId=#instOrderId and advanceStatus=#advanceStatus and preStatus=#preStatus then expect: #expectedResult"() {
        given:
        when(instOrderDAO.updateAdvanceStatusWithPreStatus(anyString(), anyLong(), anyString())).thenReturn(0)

        expect:
        defaultInstOrderRepository.updateAdvanceStatusWithPreStatus(advanceStatus, instOrderId, preStatus) == expectedResult

        where:
        instOrderId | advanceStatus | preStatus     || expectedResult
        1l          | IsAdvance.YES | IsAdvance.YES || 0
    }


    @Unroll
    def "load Single Order4 Query where bizType=#bizType and startTime=#startTime and maxSize=#maxSize and endTime=#endTime then expect: #expectedResult"() {
        given:
        when(instOrderDAO.loadSingleOrder4Query(any(), any(), anyInt(), anyString(), anyString())).thenReturn([1l])

        expect:
        defaultInstOrderRepository.loadSingleOrder4Query(startTime, endTime, maxSize, bizType) == expectedResult

        where:
        bizType        | startTime                                                          | maxSize | endTime                                                            || expectedResult
        BizType.FUNDIN | new GregorianCalendar(2022, Calendar.OCTOBER, 20, 18, 2).getTime() | 0       | new GregorianCalendar(2022, Calendar.OCTOBER, 20, 18, 2).getTime() || [1l] as Set<Long>
    }


    @Unroll
    def "update Communicate Status By Archive Batch Id where archiveBatchId=#archiveBatchId and targetStatus=#targetStatus then expect: #expectedResult"() {
        given:
        when(instOrderDAO.updateCommunicateStatusBatchByIds(any(), anyString(), anyString())).thenReturn(0)
        when(instOrderDAO.loadByArchiveBatchId(anyLong())).thenReturn([new InstOrderDO()])

        expect:
        defaultInstOrderRepository.updateCommunicateStatusByArchiveBatchId(archiveBatchId, targetStatus) == expectedResult

        where:
        archiveBatchId | targetStatus               || expectedResult
        0l             | CommunicateStatus.AWAITING || 0
    }

    @Unroll
    def "query Migrate Date List where channelCode=#channelCode then expect: #expectedResult"() {
        given:
        when(instOrderDAO.queryFOFinishedDateList(anyString())).thenReturn(["String"])

        expect:
        defaultInstOrderRepository.queryMigrateDateList(channelCode) == expectedResult

        where:
        channelCode   || expectedResult
        "channelCode" || ["String"]
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme