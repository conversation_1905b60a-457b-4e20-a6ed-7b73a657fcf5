package com.uaepay.cmf.domainservice.main.process.impl

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum
import com.uaepay.cmf.common.core.domain.enums.ErrorCode
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult
import com.uaepay.cmf.common.enums.FundChannelApiType
import com.uaepay.cmf.fss.ext.integration.router.RouterClient
import com.uaepay.common.util.money.Money
import com.uaepay.router.service.facade.domain.ResultCodeResult
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 *
 * <p>DefaultResultCodeServiceTest</p>
 *
 * <AUTHOR>
 * @version DefaultResultCodeServiceTest.java v1.0  2022/10/21 14:03
 */
class DefaultResultCodeServiceTest extends Specification {
    @Mock
    RouterClient routerClient
    @Mock
    Logger log
    @Mock
    Money ZERO_MONEY
    @Mock
    BigDecimal ZERO
    @Mock
    Money SPLIT_MIN_AMOUNT
    @InjectMocks
    DefaultResultCodeService defaultResultCodeService

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "fill Result Status where baseResult=#baseResult"() {
        given:
        when(routerClient.parseResult(any())).thenReturn(codeResult)

        expect:
        defaultResultCodeService.fillResultStatus(baseResult)
        assert expectedResult == true
        if (baseResult != null) {
            assert baseResult.getStatus() == status
            assert baseResult.getProcessStatus() == processStatus
        }

        where:
        baseResult                                                     || processStatus                           || status                                || codeResult                                                                                                                          || expectedResult
        null                                                           || null                                    || InstOrderResultStatus.IN_PROCESS      || new ResultCodeResult(applyStatus: ApplyStatusEnum.SUCCESS)                                                                          || true
        new InstBaseResult(apiType: FundChannelApiType.PREAUTH_UPDATE) || InstOrderProcessStatus.SUCCESS          || InstOrderResultStatus.HALF_SUCCESSFUL || new ResultCodeResult(applyStatus: ApplyStatusEnum.SUCCESS, resultStatus: status.code)                                               || true
        new InstBaseResult(apiType: FundChannelApiType.PREAUTH_UPDATE) || InstOrderProcessStatus.SUCCESS          || InstOrderResultStatus.SUCCESSFUL      || new ResultCodeResult(applyStatus: ApplyStatusEnum.SUCCESS, resultStatus: status.code)                                               || true
        new InstBaseResult(apiType: FundChannelApiType.PREAUTH_UPDATE) || InstOrderProcessStatus.SUCCESS          || InstOrderResultStatus.FAILURE         || new ResultCodeResult(applyStatus: ApplyStatusEnum.SUCCESS, resultStatus: status.code, unityResultCode: ErrorCode.SUCCESS.errorCode) || true
        new InstBaseResult(apiType: FundChannelApiType.PREAUTH_UPDATE) || InstOrderProcessStatus.SUBMIT_INST_FAIL || InstOrderResultStatus.NONEXISTS       || new ResultCodeResult(applyStatus: ApplyStatusEnum.SUCCESS, resultStatus: status.code, unityResultCode: ErrorCode.SUCCESS.errorCode) || true
        new InstBaseResult(apiType: FundChannelApiType.PREAUTH_UPDATE) || InstOrderProcessStatus.AWAITING         || InstOrderResultStatus.RISK            || new ResultCodeResult(applyStatus: ApplyStatusEnum.SUCCESS, resultStatus: status.code, unityResultCode: ErrorCode.SUCCESS.errorCode) || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme