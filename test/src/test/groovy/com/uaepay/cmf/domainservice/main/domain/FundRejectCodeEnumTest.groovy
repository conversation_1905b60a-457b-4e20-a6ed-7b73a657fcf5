package com.uaepay.cmf.domainservice.main.domain

import spock.lang.*

/**
 *
 * <p>FundRejectCodeEnumTest</p>
 *
 * <AUTHOR>
 * @version FundRejectCodeEnumTest.java v1.0  2022/10/21 11:40
 */
class FundRejectCodeEnumTest extends Specification {
    //Field CHARGE_BACK of type FundRejectCodeEnum - was not mocked since <PERSON><PERSON><PERSON> doesn't mock enums

    @Unroll
    def "getDescription"() {
        expect:
        type.resultCode == code
        type.description == message

        where:

        type                           || code                                  || message
        FundRejectCodeEnum.CHARGE_BACK || "CHARGE_BACK"                         || "CHARGE_BACK"
        H2hRejectAccountEnum.PAYIT     || H2hRejectAccountEnum.PAYIT.resultCode || H2hRejectAccountEnum.PAYIT.description
        H2hRejectAccountEnum.RATIBI     || H2hRejectAccountEnum.RATIBI.resultCode || H2hRejectAccountEnum.RATIBI.description
    }
}

//Generated with love by <PERSON><PERSON>e :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme