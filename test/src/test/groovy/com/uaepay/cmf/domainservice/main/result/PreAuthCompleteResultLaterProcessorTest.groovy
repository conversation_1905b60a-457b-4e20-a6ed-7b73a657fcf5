package com.uaepay.cmf.domainservice.main.result

import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult
import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository
import com.uaepay.common.util.money.Money
import com.uaepay.schema.cmf.common.Result
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import javax.annotation.Resource

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.doNothing
import static org.mockito.Mockito.when

//@SpringBootTest(classes = com.uaepay.cmf.test.CmfApplication)
class PreAuthCompleteResultLaterProcessorTest extends Specification {
    @Mock
    InstOrderRepository instOrderRepository
    @Mock
    Logger log

    @Mock
    InstResultProcessor instResultProcessor;

    @InjectMocks
    PreAuthCompleteResultLaterProcessor preAuthCompleteResultLaterProcessor

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "init"() {
        expect:
        preAuthCompleteResultLaterProcessor.init()
        assert expectedResult == false //todo - validate something

        where:
        expectedResult << true
    }

    @Unroll
    def "process #name"() {
        given:
        when(instOrderRepository.loadByNo(anyString())).thenReturn(new InstOrder(extension: [("test"): "test"], amount: new Money(100, "AED"), instOrderNo: "instOrderNo", instOrderId: 10000L))
        when(instOrderRepository.updateAmountAndExtension(anyLong(), any(), anyString())).thenReturn(0)
        doNothing().when(instResultProcessor).updateOrderStatusAndExt(any(), any())
        doNothing().when(instResultProcessor).updateCmfOrderStatus(any())

        when:
        Result result = preAuthCompleteResultLaterProcessor.process(instControlOrder, instControlResult)

        then:
        result == expectedResult

        where:
        name       | instControlResult                                                                                   | instControlOrder                                                                                                  || expectedResult
        "Nothing1" | new InstControlOrderResult()                                                                        | new InstControlOrder()                                                                                            || Result.ofNothing()
        "Nothing2" | new InstControlOrderResult(status: InstOrderResultStatus.SUCCESSFUL, amount: new Money(100, "AED")) | new InstControlOrder(preInstOrderNo: "preInstOrderNo", instOrderNo: "instOrderNo", amount: new Money(100, "AED")) || Result.ofNothing()
        "Success1" | new InstControlOrderResult(status: InstOrderResultStatus.SUCCESSFUL, amount: new Money(100, "AED")) | new InstControlOrder(preInstOrderNo: "preInstOrderNo", instOrderNo: "instOrderNo", amount: new Money(101, "AED")) || Result.ofSuccess()

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme