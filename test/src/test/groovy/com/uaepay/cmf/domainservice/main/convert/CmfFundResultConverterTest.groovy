package com.uaepay.cmf.domainservice.main.convert

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum
import com.uaepay.cmf.common.core.domain.CmfOrder
import com.uaepay.cmf.common.core.domain.constants.BasicConstant
import com.uaepay.cmf.common.core.domain.enums.*
import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult
import com.uaepay.cmf.common.core.domain.vo.CardToken
import com.uaepay.cmf.service.facade.result.CmfFundResult
import com.uaepay.cmf.service.facade.result.CmfFundResultCode
import com.uaepay.schema.cmf.enums.BizType
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

class CmfFundResultConverterTest extends Specification {
    @Mock
    Logger logger
    @Mock
    Map<ApplyStatusEnum, List<CmfFundResultCode>> applyStatusMapping
    @InjectMocks
    CmfFundResultConverter cmfFundResultConverter

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "convert where instOrder=#instOrder and cmfOrder=#cmfOrder and from=#from then expect: #expectedResult"() {
        expect:
        CmfFundResultConverter.convert(cmfOrder, instOrder, from) == expectedResult

        where:
        instOrder       | cmfOrder       | from                  || expectedResult
        new InstOrder() | new CmfOrder() | new InstOrderResult() || new CmfFundResult(extension: null, fundsChannel: "fundsChannel", instOrderNo: "instOrderNo", instPayTime: new GregorianCalendar(2022, Calendar.OCTOBER, 14, 10, 14).getTime(), amount: null, success: true, resultMessage: "resultMessage")
    }

    @Unroll
    def "convert 2 #name"() {
        when:
        CmfOrder cmfOrder = new CmfOrder(status: cmfOrder_status, paymentSeqNo: cmfOrder_paymentSeqNo)
        InstOrder instOrder = fundChannelCode == null ? null : new InstOrder(fundChannelCode: fundChannelCode, instOrderNo: instOrderNo, riskStatus: riskStatus)
        InstOrderResult from = new InstOrderResult(status: from_status, processStatus: from_processStatus, instSeqNo: from_instSeqNo, extension: from_extension, bizType: from_bizType, instResultCode: from_instResultCode)
        CardToken cardToken = cardToken_resultUrl == null ? null : new CardToken(resultUrl: cardToken_resultUrl)

        CmfFundResult result = cmfFundResultConverter.convert(cmfOrder, instOrder, from, cardToken)

        then:
        result.getResultCode() == expected_resultCode
        result.getFundsChannel() == expected_fundsChannel
        result.getInstOrderNo() == expected_instOrderNo
        result.getApplyStatus() == expected_applyStatus
        result.isSuccess() == expected_success


        where:
        name                                  | fundChannelCode | instOrderNo   | riskStatus                  | cmfOrder_status           | cmfOrder_paymentSeqNo | from_status                           | from_processStatus                      | from_instResultCode       | from_instSeqNo | from_extension                                                                                                                            | from_bizType    | cardToken_resultUrl || expected_resultCode               | expected_fundsChannel | expected_instOrderNo | expected_success | expected_applyStatus
//        "NotNullPoint" | new InstOrder(fundChannelCode: "MC101", instOrderNo: "instOrderNo", riskStatus: OrderRiskStatus.IN_PROCESS) | new CmfOrder(status: CmfOrderStatus.SUCCESSFUL, paymentSeqNo: "paymentSeqNo") | new InstOrderResult(status: InstOrderResultStatus.CANCEL, processStatus: InstOrderProcessStatus.AWAITING, instSeqNo: "instSeqNo", extension: [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"])                          | new CardToken(resultUrl: "resultURL") || new CmfFundResult(extension: null, fundsChannel: "fundsChannel", instOrderNo: "instOrderNo", instPayTime: new GregorianCalendar(2022, Calendar.OCTOBER, 14, 10, 14).getTime(), amount: null, success: true, resultMessage: "resultMessage")
        "FUNDIN & From UNKNOW_EXCEPTION"      | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.SUCCESSFUL | "paymentSeqNo"        | InstOrderResultStatus.CANCEL          | InstOrderProcessStatus.UNKNOW_EXCEPTION | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.IN_PROCESS      | null                  | "instOrderNo"        | false            | ApplyStatusEnum.ERROR
        "FUNDIN & From SUCCESS"               | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.SUCCESSFUL | "paymentSeqNo"        | InstOrderResultStatus.CANCEL          | InstOrderProcessStatus.SUCCESS          | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.REQUEST_SUCCESS | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.ERROR
        "FUNDIN & From AWAITING"              | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.SUCCESSFUL | "paymentSeqNo"        | InstOrderResultStatus.CANCEL          | InstOrderProcessStatus.AWAITING         | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.REQUEST_SUCCESS | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.ERROR
        "FUNDIN & From AWAITING NuuInstOrder" | null            | null          | null                        | CmfOrderStatus.SUCCESSFUL | "paymentSeqNo"        | InstOrderResultStatus.CANCEL          | InstOrderProcessStatus.AWAITING         | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.REQUEST_SUCCESS | null                  | null                 | false            | ApplyStatusEnum.ERROR
        "FUNDIN & From S & IO S"              | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.SUCCESSFUL | "paymentSeqNo"        | InstOrderResultStatus.SUCCESSFUL      | InstOrderProcessStatus.SUCCESS          | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.SUCCESS         | "MC101"               | "instOrderNo"        | true             | ApplyStatusEnum.SUCCESS
        "FUNDIN & From HALF_SUCCESSFULE"      | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.SUCCESSFUL | "paymentSeqNo"        | InstOrderResultStatus.HALF_SUCCESSFUL | InstOrderProcessStatus.SUCCESS          | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.SUCCESS         | "MC101"               | "instOrderNo"        | true             | ApplyStatusEnum.SUCCESS
        "FUNDIN & From RISK"                  | "MC101"         | "instOrderNo" | OrderRiskStatus.FAILED_SEND | CmfOrderStatus.SUCCESSFUL | "paymentSeqNo"        | InstOrderResultStatus.RISK            | InstOrderProcessStatus.SUCCESS          | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.REQUEST_SUCCESS | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.ERROR
        "FUNDIN & From CANCEL  "              | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.CANCEL     | "paymentSeqNo"        | InstOrderResultStatus.FAILURE         | InstOrderProcessStatus.SUCCESS          | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.FAILED          | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.FAIL
        "FUNDIN & From FAILURE1"              | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.FAILURE    | "paymentSeqNo"        | InstOrderResultStatus.FAILURE         | InstOrderProcessStatus.SUCCESS          | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.FAILED          | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.FAIL
        "FUNDIN & From FAILURE2"              | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.CANCEL     | "paymentSeqNo"        | InstOrderResultStatus.FAILURE         | InstOrderProcessStatus.SUCCESS          | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.FAILED          | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.FAIL
        "FUNDIN & From S  CO INPROCESS"       | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.IN_PROCESS | "paymentSeqNo"        | InstOrderResultStatus.SUCCESSFUL      | InstOrderProcessStatus.SUCCESS          | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad", (ExtensionKey.PAYMENT_SEQ_NO.key): "paymentNo"] | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.REQUEST_SUCCESS | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.ERROR
        "FUNDIN & From S  CO INPROCESS2"      | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.FAILURE    | "paymentSeqNo"        | InstOrderResultStatus.SUCCESSFUL      | InstOrderProcessStatus.SUCCESS          | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad", (ExtensionKey.PAYMENT_SEQ_NO.key): "paymentNo"] | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.REQUEST_SUCCESS | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.ERROR
        "FUNDIN & From FAILURE3"              | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.CANCEL     | "paymentSeqNo"        | InstOrderResultStatus.FAILURE         | InstOrderProcessStatus.FAILURE          | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.FAILED          | null                  | "instOrderNo"        | false            | ApplyStatusEnum.FAIL
        "FUNDIN & From FAILURE4"              | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.CANCEL     | "paymentSeqNo"        | InstOrderResultStatus.FAILURE         | InstOrderProcessStatus.SUBMIT_CMF_FAIL  | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.FAILED          | null                  | "instOrderNo"        | false            | ApplyStatusEnum.FAIL
        "FUNDIN & From FAILURE5"              | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.CANCEL     | "paymentSeqNo"        | InstOrderResultStatus.IN_PROCESS      | InstOrderProcessStatus.FAILURE          | null                      | "instSeqNo"    | [(ExtensionKey.PAGE_URL_FOR_SIGN.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                 | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.IN_PROCESS      | null                  | "instOrderNo"        | false            | ApplyStatusEnum.ERROR
        "FUNDIN & From SUCCESS"               | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.IN_PROCESS | "paymentSeqNo"        | InstOrderResultStatus.CANCEL          | InstOrderProcessStatus.SUCCESS          | BasicConstant.SUCCESS_MSG | "instSeqNo"    | [(ExtensionKey.BANK_FORM_KEY.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                     | BizType.FUNDIN  | "resultURL"         || CmfFundResultCode.REQUEST_SUCCESS | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.ERROR
        "FUNDIN & From SUCCESS"               | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.SUCCESSFUL | "paymentSeqNo"        | InstOrderResultStatus.CANCEL          | InstOrderProcessStatus.SUCCESS          | BasicConstant.SUCCESS_MSG | "instSeqNo"    | [(ExtensionKey.BANK_FORM_KEY.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                     | BizType.FUNDIN  | null                || CmfFundResultCode.REQUEST_SUCCESS | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.ERROR

        "REFUND & From SUCCESS"               | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.SUCCESSFUL | "paymentSeqNo"        | InstOrderResultStatus.CANCEL          | InstOrderProcessStatus.SUCCESS          | null                      | "instSeqNo"    | [(ExtensionKey.BANK_FORM_KEY.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                     | BizType.REFUND  | "resultURL"         || CmfFundResultCode.IN_PROCESS      | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.ERROR
        "FUNDOUT & From SUCCESS"              | "MC101"         | "instOrderNo" | OrderRiskStatus.IN_PROCESS  | CmfOrderStatus.SUCCESSFUL | "paymentSeqNo"        | InstOrderResultStatus.CANCEL          | InstOrderProcessStatus.SUCCESS          | null                      | "instSeqNo"    | [(ExtensionKey.BANK_FORM_KEY.key): "signForm", (ExtensionKey.PAYLOAD.key): "payLoad"]                                                     | BizType.FUNDOUT | "resultURL"         || CmfFundResultCode.REQUEST_SUCCESS | "MC101"               | "instOrderNo"        | false            | ApplyStatusEnum.ERROR

    }

    @Unroll
    def "convert 3 where cmfOrder=#cmfOrder then expect: #expectedResult"() {
        expect:
        CmfFundResultConverter.convert(cmfOrder) == expectedResult

        where:
        cmfOrder       || expectedResult
        new CmfOrder() || new CmfFundResult(channelPayNo: "channelPayNo", amount: null, success: true)
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme