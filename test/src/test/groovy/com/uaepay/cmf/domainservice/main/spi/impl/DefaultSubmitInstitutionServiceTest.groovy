package com.uaepay.cmf.domainservice.main.spi.impl

import com.uaepay.cmf.common.core.domain.CmfOrder
import com.uaepay.cmf.common.core.domain.enums.ErrorCode
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey
import com.uaepay.cmf.common.core.domain.exception.RouteChannelException
import com.uaepay.cmf.common.core.domain.institution.*
import com.uaepay.cmf.common.domain.ChannelFundResult
import com.uaepay.cmf.common.domain.ChannelResult
import com.uaepay.cmf.common.enums.ApiParamScene
import com.uaepay.cmf.common.enums.ControlRequestType
import com.uaepay.cmf.common.enums.FundChannelApiType
import com.uaepay.cmf.common.enums.RequestType
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter
import com.uaepay.cmf.domainservice.channel.router.impl.CmfOrderChannelRouter
import com.uaepay.cmf.domainservice.channel.router.impl.ControlOrderChannelRouter
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier
import com.uaepay.cmf.domainservice.main.domain.H2hRejectAccountEnum
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository
import com.uaepay.cmf.domainservice.main.result.ControlResultProcessor
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor
import com.uaepay.cmf.domainservice.main.sender.ControlOrderSendService
import com.uaepay.cmf.domainservice.main.sender.InstOrderSendService
import com.uaepay.cmf.domainservice.main.spi.FundOutRejectService
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService
import com.uaepay.cmf.domainservice.main.split.SplitOrderService
import com.uaepay.common.util.money.Money
import com.uaepay.gateway.cgs.app.facade.enums.ApiType
import com.uaepay.router.service.facade.domain.channel.ChannelApiParamVO
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO
import com.uaepay.router.service.facade.domain.channel.ChannelVO
import com.uaepay.router.service.facade.domain.order.OrderInfo
import com.uaepay.schema.cmf.enums.BizType
import com.uaepay.schema.cmf.enums.YesNo
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

class DefaultSubmitInstitutionServiceTest extends Specification {
    @Mock
    OrderLoaderService orderLoaderService
    @Mock
    CmfOrderRepository cmfOrderRepository
    @Mock
    InstOrderRepository instOrderRepository
    @Mock
    InstOrderResultRepository instOrderResultRepository
    @Mock
    InstControlOrderRepository instControlOrderRepository
    @Mock
    SplitOrderService splitOrderService
    @Mock
    CmfOrderChannelRouter channelRouter
    @Mock
    ControlOrderChannelRouter controlRouter
    @Mock
    ChannelApiRouter apiRouter
    @Mock
    InstOrderSendService instOrderSendService
    @Mock
    ControlOrderSendService controlOrderSendService
    @Mock
    InstResultProcessor instResultProcessor
    @Mock
    ControlResultProcessor controlResultProcessor
    @Mock
    FundOutRejectService fundOutRejectService
    @Mock
    Logger log
    @Mock
    Money ZERO_MONEY
    @Mock
    BigDecimal ZERO
    @Mock
    Money SPLIT_MIN_AMOUNT
    @InjectMocks
    DefaultSubmitInstitutionService defaultSubmitInstitutionService

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "submit cmfOrder=#name"() {
        given:
        when(orderLoaderService.loadPreOrder(anyString(), anyString(), anyString(), anyString())).thenReturn(new InstBaseOrder())
        when(cmfOrderRepository.updateCmfOrderStatus(any(), any())).thenReturn(Boolean.TRUE)
        when(instOrderResultRepository.loadRealResultByOrder(anyLong())).thenReturn(new InstOrderResult())
        when(splitOrderService.orderComplement(any(), any(), any(), any())).thenReturn([new InstOrder()])
        if (channelException == 1) {
            when(channelRouter.route(any())).thenReturn(new ChannelCarrier(new ChannelVO(channelApi: new ChannelApiVO()), new OrderInfo()))
        } else if(channelException == 2){
            when(channelRouter.route(any())).thenThrow(new RouteChannelException(ErrorCode.CMF_SYSTEM_ERROR,"ERROR_MSG"))
        }else{
            when(channelRouter.route(any())).thenThrow(new RuntimeException())
        }
        when(controlRouter.route(any())).thenReturn(null)
        when(apiRouter.route(any())).thenReturn(null)
        when(instOrderSendService.send(any())).thenReturn(new ChannelFundResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY))
        when(controlOrderSendService.send(any())).thenReturn(new ChannelResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY))
        when(instResultProcessor.process(any(), (ChannelResult) any())).thenReturn(new InstOrderResult())
        when(controlResultProcessor.process(any(), (ChannelResult) any())).thenReturn(new InstControlOrderResult())
        when(fundOutRejectService.getRejectAccountType(any())).thenReturn(rejectAccountType)
        when(fundOutRejectService.processRejectAccountRequest(any(), any())).thenReturn(new InstOrderResult())
        when(fundOutRejectService.processRejectRequest(any(), any())).thenReturn(new InstOrderResult())
        when(fundOutRejectService.grcCheck(any())).thenReturn([null])

        when:
        defaultSubmitInstitutionService.submit(cmfOrder)

        then:
        "" == ""

        where:
        name                    | cmfOrder                                                                                                                        | rejectAccountType          | channelException | grcCheck          || expectedResult
        "RouteChannelException" | new CmfOrder(requestType: RequestType.REFUND, orgiPaymentSeqNo: "orgipsn", orgiSettlementId: "settid", bizType: BizType.FUNDIN) | H2hRejectAccountEnum.PAYIT | 1             | [new InstOrder()] || new InstOrderResult()
        "1"                     | new CmfOrder(requestType: RequestType.REFUND, orgiPaymentSeqNo: "orgipsn", orgiSettlementId: "settid", bizType: BizType.FUNDIN) | H2hRejectAccountEnum.PAYIT | 2            | [new InstOrder()] || new InstOrderResult()
        "2"                     | new CmfOrder(requestType: RequestType.REFUND, orgiPaymentSeqNo: "orgipsn", orgiSettlementId: "settid", bizType: BizType.FUNDIN) | null                       | 3            | null              || new InstOrderResult()
    }

    @Unroll
    def "submit 2 where instOrder=#instOrder and apiType=#apiType then expect: #expectedResult"() {
        given:
        when(channelRouter.route(any())).thenReturn(null)
        when(controlRouter.route(any())).thenReturn(null)
        when(apiRouter.route(any())).thenReturn(null)
        when(instOrderSendService.send(any())).thenReturn(new ChannelFundResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY))
        when(controlOrderSendService.send(any())).thenReturn(new ChannelResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY))
        when(instResultProcessor.process(any(), (ChannelResult) any())).thenReturn(new InstOrderResult())
        when(controlResultProcessor.process(any(), (ChannelResult) any())).thenReturn(new InstControlOrderResult())

        expect:
        defaultSubmitInstitutionService.submit(instOrder, apiType) == expectedResult

        where:
        instOrder       | apiType                       || expectedResult
        new InstOrder() | FundChannelApiType.SINGLE_PAY || new InstOrderResult()
    }

    @Unroll
    def "submit 3 where order=#order then expect: #expectedResult"() {
        given:
        when(orderLoaderService.loadPreOrder(any(), any(), any(), any())).thenReturn(new InstBaseOrder(instSeqNo: "seqNo",extension: [("test"):"test"]))
        when(channelRouter.route(any())).thenReturn(new ChannelCarrier(new ChannelVO(channelApi: new ChannelApiVO(apiType: ApiType.AUTHED)),new OrderInfo()))
        when(controlRouter.route(any())).thenReturn(new ChannelCarrier(new ChannelVO(channelApi: new ChannelApiVO(apiType: ApiType.AUTHED,paramList:[new ChannelApiParamVO(scene: ApiParamScene.REQUEST_CHANNEL.getCode(),isOrigin: YesNo.YES.getCode())] )),new OrderInfo()))
        when(apiRouter.route(any())).thenReturn(null)
        when(instOrderSendService.send(any())).thenReturn(new ChannelFundResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY))
        when(controlOrderSendService.send(any())).thenReturn(new ChannelResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY))
        when(instResultProcessor.process(any(), (ChannelResult) any())).thenReturn(new InstOrderResult())
        when(controlResultProcessor.process(any(), (ChannelResult) any())).thenReturn(new InstControlOrderResult())

        expect:
        defaultSubmitInstitutionService.submit(order) == expectedResult

        where:
        order                  || expectedResult
        new InstControlOrder(requestType: ControlRequestType.AUTH,preRequestNo: "preReqNo",preSettlementId: "preSettId",extension: [(ExtensionKey.SOURCE_ORDER):"order"]) || new InstControlOrderResult()
    }

    @Unroll
    def "submit 4 where controlOrder=#controlOrder and apiType=#apiType then expect: #expectedResult"() {
        given:
        when(channelRouter.route(any())).thenReturn(null)
        when(controlRouter.route(any())).thenReturn(null)
        when(apiRouter.route(any())).thenReturn(null)
        when(instOrderSendService.send(any())).thenReturn(new ChannelFundResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY))
        when(controlOrderSendService.send(any())).thenReturn(new ChannelResult(true, "apiResultCode", "apiResultMessage", FundChannelApiType.SINGLE_PAY))
        when(instResultProcessor.process(any(), (ChannelResult) any())).thenReturn(new InstOrderResult())
        when(controlResultProcessor.process(any(), (ChannelResult) any())).thenReturn(new InstControlOrderResult())

        expect:
        defaultSubmitInstitutionService.submit(controlOrder, apiType) == expectedResult

        where:
        controlOrder           | apiType                       || expectedResult
        new InstControlOrder() | FundChannelApiType.SINGLE_PAY || new InstControlOrderResult()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme