package com.uaepay.cmf.domainservice.main.process.impl

import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult
import com.uaepay.cmf.common.enums.FundChannelApiType
import com.uaepay.cmf.domainservice.main.process.MonitorService
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderResultRepository
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult
import com.uaepay.common.util.money.Money
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.Mockito.when

class DuplicateControlOrderResultProcessServiceTest extends Specification {
    @Mock
    Logger logger
    @Mock
    MonitorService monitorService
    @Mock
    InstControlOrderResultRepository instControlOrderResultRepository
    @Mock
    InstControlOrderRepository instControlOrderRepository
    @Mock
    CmfOrderRepository cmfOrderRepository
    @Mock
    Logger log
    @InjectMocks
    DuplicateControlOrderResultProcessService duplicateControlOrderResultProcessService

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "query Duplicate Result where requestNo=#requestNo then expect: #expectedResult"() {
        given:
        when(instControlOrderRepository.loadByRequestNo("requestNo")).thenReturn(new InstControlOrder(instOrderNo: "instOrderNo"))
        when(instControlOrderRepository.loadByRequestNo("controlNull")).thenReturn(new InstControlOrder(instOrderNo: "controlNull"))
        when(instControlOrderRepository.loadByRequestNo("exception")).thenReturn(new InstControlOrder(instOrderNo: "exception"))
        when(instControlOrderRepository.loadByRequestNo("null")).thenReturn(null)

        when(instControlOrderResultRepository.loadByInstOrderNo("instOrderNo")).thenReturn(new InstControlOrderResult(instOrderNo: "instOrderNo", apiType: FundChannelApiType.AUTH, amount: new Money(10, "AED"), status: InstOrderResultStatus.IN_PROCESS, instResultCode: "resultCode"))
        when(instControlOrderResultRepository.loadByInstOrderNo("controlNull")).thenReturn(null)
        when(instControlOrderResultRepository.loadByInstOrderNo("exception")).thenReturn(new InstControlOrderResult())

        when:
        println(instControlOrderRepository.loadByRequestNo("requestNo").getInstOrderNo())
        CmfControlResult result = duplicateControlOrderResultProcessService.queryDuplicateResult(requestNo)

        then:
        if (result != null) {
            result.getInstResultCode() == expectedResult.getResultCode()
        }

        where:
        requestNo     || expectedResult
        "requestNo"   || new CmfControlResult(instResultCode: "resultCode")
        "null"        || null
        "exception"   || null
        "controlNull" || null

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme