package com.uaepay.cmf.domainservice.channel.impl

import com.uaepay.cmf.common.core.domain.CmfOrder
import com.uaepay.cmf.common.core.domain.enums.TokenTypeEnum
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder
import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.common.core.domain.ma.MemberChannelToken
import com.uaepay.cmf.common.core.domain.vo.CardToken
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder
import com.uaepay.cmf.domainservice.main.pattern.CardTokenService
import com.uaepay.cmf.fss.ext.integration.config.TransformConfig
import com.uaepay.cmf.fss.ext.integration.ma.MemberClient
import com.uaepay.common.util.money.Money
import com.uaepay.payment.common.v2.enums.PayMode
import com.uaepay.schema.cmf.enums.CompanyOrPersonal
import com.uaepay.schema.cmf.enums.Dbcr
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 *
 * <p>ConfigurationServiceImplTest</p>
 *
 * <AUTHOR>
 * @version ConfigurationServiceImplTest.java v1.0  2022/10/20 17:50
 */
class ConfigurationServiceImplTest extends Specification {
    @Mock
    Logger logger
    @Mock
    SysConfigurationHolder sysConfigurationHolder
    @Mock
    CardTokenService cardTokenService
    @Mock
    MemberClient memberClient
    @Mock
    TransformConfig transformConfig
    @Mock
    Money ZERO_MONEY
    @Mock
    BigDecimal ZERO
    @Mock
    Money SPLIT_MIN_AMOUNT
    @InjectMocks
    ConfigurationServiceImpl configurationServiceImpl

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "before Route where cmfOrder=#cmfOrder"() {
        given:
        when(sysConfigurationHolder.loadConfigureOrDefault(anyString(), anyString())).thenReturn("loadConfigureOrDefaultResponse")
        when(cardTokenService.queryToken(any())).thenReturn(new CardToken(dbcr: Dbcr.DC.name(), companyOrPersonal: CompanyOrPersonal.PERSONAL))
        when(memberClient.queryTokens(anyLong())).thenReturn([new MemberChannelToken()])

        expect:
        configurationServiceImpl.beforeRoute(cmfOrder)
        assert expectedResult == true //todo - validate something

        where:
        cmfOrder       || expectedResult
        new CmfOrder() || true
    }

    @Unroll
    def "before Route 2 where controlOrder=#controlOrder"() {
        given:
        when(sysConfigurationHolder.loadConfigureOrDefault(anyString(), anyString())).thenReturn("loadConfigureOrDefaultResponse")
        when(cardTokenService.queryToken(anyString())).thenReturn(new CardToken())
        when(memberClient.queryTokens(anyLong())).thenReturn([new MemberChannelToken()])

        expect:
        configurationServiceImpl.beforeRoute(controlOrder)
        assert expectedResult == false //todo - validate something

        where:
        controlOrder           || expectedResult
        new InstControlOrder() || true
    }

    def "queryCardToken:#payMode"() {
        given:
        when(cardTokenService.queryToken(anyString())).thenReturn(cardToken)
        when(memberClient.queryTokens(anyLong())).thenReturn(channelToken)
        when(sysConfigurationHolder.loadConfigureOrDefault(anyString(), anyString())).thenReturn("")

        when:
        configurationServiceImpl.queryCardToken(payMode, new CmfOrder(extension: extension))
        then:
        expectedResult == true
        where:
        payMode          || channelToken               || cardToken       || extension              || expectedResult
        PayMode.QUICKPAY || [new MemberChannelToken()] || new CardToken() || ["cardToken": "token"] || true
        PayMode.BALANCE  || [new MemberChannelToken()] || new CardToken() || ["cardToken": "token"] || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme