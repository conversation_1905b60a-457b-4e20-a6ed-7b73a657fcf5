package com.uaepay.cmf.domainservice.batch.processor

import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator
import com.uaepay.cmf.domainservice.batch.result.ArchiveDetail
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository
import com.uaepay.cmf.domainservice.main.spi.FundOutRejectService
import com.uaepay.cmf.fss.ext.integration.router.RouterClient
import com.uaepay.common.util.money.Money
import com.uaepay.grc.connect.api.vo.domain.CheckInfo
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO
import com.uaepay.router.service.facade.domain.channel.ChannelBatchArchiveVO
import org.junit.platform.commons.util.CollectionUtils
import org.slf4j.Logger
import org.springframework.transaction.support.TransactionTemplate
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 *
 * <p>BatchOrderArchiveServiceImplTest</p>
 *
 * <AUTHOR>
 * @version BatchOrderArchiveServiceImplTest.java v1.0  2022/10/20 16:05
 */
class BatchOrderArchiveServiceImplTest extends Specification {
    @Mock
    Logger logger
    @Mock
    InstOrderRepository instOrderRepository
    @Mock
    TransactionTemplate cmfTransactionTemplate
    @Mock
    PrimaryKeyGenerator primaryKeyGenerator
    @Mock
    RouterClient routerClient
    @Mock
    FundOutRejectService fundOutRejectService
    @Mock
    Money ZERO_MONEY
    @Mock
    BigDecimal ZERO
    @Mock
    Money SPLIT_MIN_AMOUNT
    @InjectMocks
    BatchOrderArchiveServiceImpl batchOrderArchiveServiceImpl

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll

    def "archive Order where apiVO=#apiVO then expect: #expectedResult"() {
        given:
        when(instOrderRepository.getInstOrderListByAichiveBatchId(anyLong())).thenReturn([new InstOrder()])
        when(instOrderRepository.loadInstOrderIdListByBatchIdAndCurrency(anyLong(), anyString())).thenReturn([1l])
        when(instOrderRepository.loadOrderCurrencyListByBatchId(anyLong())).thenReturn(["String"])
        when(instOrderRepository.updateBatchIdListByTempBatchId(anyLong(), anyLong(), any())).thenReturn(1)
        when(instOrderRepository.loadInstOrderList4ArchivePage(any(), any(), any(), any(), any())).thenReturn(instList)
        when(instOrderRepository.getArchivePages(anyLong(), any(), anyLong(), any(), anyString())).thenReturn(archivePages)
        when(instOrderRepository.updateBatchByInstOrderId(anyLong(), any())).thenReturn(1)
        when(instOrderRepository.updateBatchId2Default(anyLong())).thenReturn(1)
        when(instOrderRepository.loadInstOrderListByIds(any())).thenReturn([new InstOrder()])
        when(primaryKeyGenerator.generateKey(any())).thenReturn("202210210000001")
        when(routerClient.genOrderNo(anyString(), any(), anyBoolean())).thenReturn("genOrderNoResponse")
        when(routerClient.queryChannelArchive(any())).thenReturn(archiveVO)
        when(fundOutRejectService.grcCheck(any())).thenReturn([])
        when:
        def result = batchOrderArchiveServiceImpl.archiveOrder(apiVO)
        if (result != null) {
            throw new Exception("Success")
        }
        then:
        def ex = thrown(Exception)
        ex.message == expectedResult
        where:
        apiVO              || instList              || archivePages || archiveVO                                            || expectedResult
        new ChannelApiVO() || Arrays.asList(1L, 2L) || 2            || new ChannelBatchArchiveVO(archiveId: 1L, maxItem: 0) || "最大笔数设置不能小于0"
        new ChannelApiVO() || Arrays.asList(1L, 2L) || 2            || new ChannelBatchArchiveVO(archiveId: 1L, maxItem: 1) || "笔数不一致"
        new ChannelApiVO() || Arrays.asList(1L, 2L) || 1            || new ChannelBatchArchiveVO(archiveId: 1L, maxItem: 1) || "Success"
        new ChannelApiVO() || []                    || 1            || new ChannelBatchArchiveVO(archiveId: 1L, maxItem: 1) || "笔数不一致"
        new ChannelApiVO() || Arrays.asList(1L, 2L) || 2            || null                                                 || "Success"
    }

    @Unroll
    def "filter Order where instOrderIdList=#instOrderIdList"() {
        given:
        when(instOrderRepository.loadInstOrderListByIds(any())).thenReturn(instOrders)
        when(fundOutRejectService.grcCheck(any())).thenReturn(grcCheckResults)

        expect:
        batchOrderArchiveServiceImpl.filterOrder(instOrderIdList)
        expectedResult == true

        where:
        name              || instOrderIdList || instOrders        || grcCheckResults                || expectedResult
        "pass"            || [1l]            || [new InstOrder()] || [new CheckInfo(memberId: "1")] || true
        "instOrder Empty" || [1L]            || []                || []                             || true
        "grc Check Empty" || [1L]            || [new InstOrder()] || []                             || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme