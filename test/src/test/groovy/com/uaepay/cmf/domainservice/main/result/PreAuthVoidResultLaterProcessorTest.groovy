package com.uaepay.cmf.domainservice.main.result

import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult
import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult
import com.uaepay.cmf.domainservice.main.convert.ChannelResultConverter
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository
import com.uaepay.schema.cmf.common.Result
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

class PreAuthVoidResultLaterProcessorTest extends Specification {
    @Mock
    InstOrderRepository instOrderRepository
    @Mock
    InstResultProcessor instResultProcessor
    @Mock
    Logger log
    @InjectMocks
    PreAuthVoidResultLaterProcessor preAuthVoidResultLaterProcessor

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "init"() {
        expect:
        preAuthVoidResultLaterProcessor.init()
        assert expectedResult == false //todo - validate something

        where:
        expectedResult << true
    }

    @Unroll
    def "process #name"() {
        given:
        when(instOrderRepository.loadByNo(anyString())).thenReturn(new InstOrder())
        when(instOrderRepository.updateInstOrderStatus(any(), any())).thenReturn(true)

        when:
        Result result
        try (var ms2 = Mockito.mockStatic(ChannelResultConverter.class)) {
            when(ChannelResultConverter.convert((InstControlOrderResult) any(), (InstOrder) any())).thenReturn(new InstOrderResult(status: instOrderResult_Status))
            result = preAuthVoidResultLaterProcessor.process(instControlOrder, instControlResult)
        }

        throw ("")
        then:
        def ex = thrown(Exception)
        if (!"".equals(ex.getMessage())) {
            ex.getMessage() == errorMsg
        } else {
            expectedResult == result
        }

        where:
        name                | instControlResult                                                    | instControlOrder                                      | instOrderResult_Status || expectedResult     | errorMsg
        "CancelStatusError" | new InstControlOrderResult(status: InstOrderResultStatus.SUCCESSFUL) | new InstControlOrder(preInstOrderNo: "preInstOrderNo") | InstOrderResultStatus.SUCCESSFUL|| new Result<?>()    | "撤销状态有误"
        "Nothing1"          | new InstControlOrderResult(status: InstOrderResultStatus.IN_PROCESS) | new InstControlOrder(preInstOrderNo: "preInstOrderNo") |InstOrderResultStatus.FAILURE|| Result.ofNothing() | ""
        "Success"           | new InstControlOrderResult(status: InstOrderResultStatus.SUCCESSFUL) | new InstControlOrder(preInstOrderNo: "preInstOrderNo") |InstOrderResultStatus.FAILURE|| Result.ofSuccess() | ""

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme