package com.uaepay.cmf.domainservice.channel.router.impl

import com.uaepay.cmf.common.core.domain.CmfOrder
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder
import com.uaepay.cmf.domainservice.channel.limit.LimitService
import com.uaepay.cmf.domainservice.channel.router.ConfigurationService
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService
import com.uaepay.cmf.fss.ext.integration.router.RouterClient
import com.uaepay.common.util.money.Money
import com.uaepay.router.service.facade.domain.RouteResponse
import com.uaepay.router.service.facade.domain.channel.ChannelVO
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class CmfOrderChannelRouterTest extends Specification {
    @Mock
    RouterClient routerClient
    @Mock
    OrderLoaderService orderLoaderService
    @Mock
    LimitService limitService
    @Mock
    ConfigurationService configurationService
    @Mock
    Money ZERO_MONEY
    @Mock
    BigDecimal ZERO
    @Mock
    Money SPLIT_MIN_AMOUNT
    @InjectMocks
    CmfOrderChannelRouter cmfOrderChannelRouter

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "before Route where cmfOrder=#cmfOrder"() {
        given:
        when(limitService.validateLimit(any())).thenReturn(true)

        expect:
        cmfOrderChannelRouter.beforeRoute(cmfOrder)
        assert expectedResult == false //todo - validate something

        where:
        cmfOrder       || expectedResult
        new CmfOrder() || true
    }

    @Unroll
    def "route Custom where cmfOrder=#cmfOrder then expect: #expectedResult"() {
        given:
        when(routerClient.route(any())).thenReturn(new RouteResponse<ChannelVO>())
        when(routerClient.route(any())).thenReturn(new RouteResponse<ChannelVO>())
        when(orderLoaderService.loadPreOrder(anyString(), anyString(), anyString(), anyString())).thenReturn(new InstBaseOrder())

        expect:
        cmfOrderChannelRouter.routeCustom(cmfOrder) == expectedResult

        where:
        cmfOrder       || expectedResult
        new CmfOrder() || new RouteResponse<ChannelVO>()
    }

    @Unroll
    def "route where request=#request then expect: #expectedResult"() {
        expect:
        cmfOrderChannelRouter.route(request) == expectedResult

        where:
        request        || expectedResult
        new CmfOrder() || null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme