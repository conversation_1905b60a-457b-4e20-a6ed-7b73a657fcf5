package com.uaepay.cmf.domainservice.main.process.impl

import com.uaepay.cmf.common.core.domain.CmfOrder
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus
import com.uaepay.cmf.common.core.domain.institution.InstOrder
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult
import com.uaepay.cmf.domainservice.main.process.MonitorService
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService
import com.uaepay.cmf.fss.ext.integration.payment.PaymentClient
import com.uaepay.common.util.DateUtil
import com.uaepay.common.util.money.Money
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 *
 * <p>DefaultNotifyPaymentServiceTest</p>
 *
 * <AUTHOR>
 * @version DefaultNotifyPaymentServiceTest.java v1.0  2022/10/21 11:48
 */
class DefaultNotifyPaymentServiceTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CmfOrderRepository cmfOrderRepository
    @Mock
    InstOrderRepository instOrderRepository
    @Mock
    InstOrderResultRepository instOrderResultRepository
    @Mock
    MonitorService monitorService
    @Mock
    PaymentClient paymentClient
    @Mock
    OrderLoaderService orderLoaderService
    @Mock
    Money ZERO_MONEY
    @Mock
    BigDecimal ZERO
    @Mock
    Money SPLIT_MIN_AMOUNT
    @InjectMocks
    DefaultNotifyPaymentService defaultNotifyPaymentService

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "notify Result #name"() {
        given:
        when(cmfOrderRepository.loadByCmfSeqNo(anyString(), anyBoolean())).thenReturn(new CmfOrder())
        when(cmfOrderRepository.updatePaymentNotifyStatusById(any(), anyString())).thenReturn(0)
        when(cmfOrderRepository.storeNotifyLog(any())).thenReturn(1l)
        when(instOrderRepository.loadByCmfSeqNo(anyString())).thenReturn([new InstOrder()])
        when(instOrderRepository.loadByCmfSeqNoSingle(anyString())).thenReturn(new InstOrder())
        when(instOrderResultRepository.loadRealResultByOrder(anyLong())).thenReturn(new InstOrderResult())
        when(paymentClient.sendInstCmfResult(any())).thenReturn(null)
        when(orderLoaderService.loadReturnOrderNo(any(), any())).thenReturn("loadReturnOrderNoResponse")


        when:
        def instResult = new InstOrderResult(processStatus: processStatus)
        instResult.setExtension(extension as HashMap<String, String>)
        defaultNotifyPaymentService.notifyResult(cmfOrder, instResult)

        then:
        assert expectedResult == true

        where:
        name             | processStatus                  | extension           | cmfOrder                                                      || expectedResult
        "Extension NULL" | InstOrderProcessStatus.SUCCESS | null                | new CmfOrder(gmtCreate: DateUtil.addSeconds(new Date(), -20)) || true
        "NeedNotify Yes" | InstOrderProcessStatus.SUCCESS | ["needNotify": "Y"] | new CmfOrder(gmtCreate: DateUtil.addSeconds(new Date(), -20)) || true
        "NeedNotify NO"  | InstOrderProcessStatus.SUCCESS | ["needNotify": "N"] | new CmfOrder(gmtCreate: DateUtil.addSeconds(new Date(), -20)) || true
        "NeedNotify NO"  | InstOrderProcessStatus.SUCCESS | ["needNotify": "N"] | new CmfOrder(gmtCreate: DateUtil.addSeconds(new Date(), -5)) || true
        "NeedNotify NO"  | InstOrderProcessStatus.SUCCESS | ["notify": "N"] | new CmfOrder(gmtCreate: DateUtil.addSeconds(new Date(), -5)) || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme