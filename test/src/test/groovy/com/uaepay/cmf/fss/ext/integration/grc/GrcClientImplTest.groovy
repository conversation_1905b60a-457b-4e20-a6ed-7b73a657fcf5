package com.uaepay.cmf.fss.ext.integration.grc

import com.uaepay.basis.beacon.service.facade.domain.response.ObjectQueryResponse
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum
import com.uaepay.cmf.common.core.domain.vo.GrcLimitCarrier
import com.uaepay.cmf.fss.ext.integration.amqp.MqTemplate
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult
import com.uaepay.common.util.money.Money
import com.uaepay.grc.connect.api.facade.QueryFacade
import com.uaepay.grc.connect.api.vo.domain.CheckInfo
import com.uaepay.grc.cps.api.LimitQuotaFrequencyStub
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 *
 * <p>GrcClientImplTest</p>
 *
 * <AUTHOR>
 * @version GrcClientImplTest.java v1.0  2022/10/20 17:04
 */
class GrcClientImplTest extends Specification {
    @Mock
    MqTemplate<Notify3dsResult> mqTemplate
    @Mock
    LimitQuotaFrequencyStub limitQuotaFrequencyStub
    @Mock
    QueryFacade queryFacade
    @Mock
    Logger log
    @Mock
    Money ZERO_MONEY
    @Mock
    BigDecimal ZERO
    @Mock
    Money SPLIT_MIN_AMOUNT
    @InjectMocks
    GrcClientImpl grcClientImpl

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "check Result where checkInfos=#checkInfos then expect: #expectedResult"() {

        given:
        when(queryFacade.check(any())).thenReturn(checkResponse)
        when:
        def result = grcClientImpl.checkResult(checkInfos)

        then:
        result.size() == expectedResult.size()

        where:
        checkInfos                     || checkResponse                                                                                         || expectedResult
        [new CheckInfo(memberId: "1")] || new ObjectQueryResponse(applyStatus: ApplyStatusEnum.ERROR)                                           || []
        [new CheckInfo(memberId: "1")] || new ObjectQueryResponse(applyStatus: ApplyStatusEnum.SUCCESS)                                         || []
        [new CheckInfo(memberId: "1")] || new ObjectQueryResponse(applyStatus: ApplyStatusEnum.SUCCESS, result: [new CheckInfo(memberId: "1")]) || [new CheckInfo()]
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme