package com.uaepay.cmf.common.core.engine.cache.impl

import com.uaepay.cmf.common.core.engine.cache.CacheLockService
import com.uaepay.cmf.test.CmfApplication
import org.slf4j.Logger
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.web.WebAppConfiguration
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import javax.annotation.Resource
import java.time.Duration

import static org.mockito.Mockito.*

/**
 *
 * <p>CacheLockServiceImplTest</p>
 *
 * <AUTHOR>
 * @version CacheLockServiceImplTest.java v1.0  2022/11/21 11:57
 */
@ContextConfiguration
@SpringBootTest(classes = CmfApplication.class, properties = "spring.profiles.active=dev,unittest,manual")
@WebAppConfiguration
class CacheLockServiceImplTest extends Specification {

    @Resource
    CacheLockService cacheLockService

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "lock where lockTime=#lockTime and lockKey=#lockKey then expect: #expectedResult"() {
        given:
        when:
        boolean result = cacheLockService.lock(lockKey, lockTime)
        then:
        assert result == expectedResult

        where:
        lockTime               | lockKey   || expectedResult
        Duration.ofSeconds(10) | "lockKey" || true
        Duration.ofSeconds(10) | null      || false
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme