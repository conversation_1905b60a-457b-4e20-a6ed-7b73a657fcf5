package com.uaepay.cmf.common.enums

import spock.lang.*

/**
 *
 * <p>ControlRequestTypeTest</p>
 *
 * <AUTHOR>
 * @version ControlRequestTypeTest.java v1.0  2022/10/20 09:54
 */
class ControlRequestTypeTest extends Specification {

    @Unroll
    def "get By Code where code=#code then expect: #expectedResult"() {
        expect:
        ControlRequestType.getByCode(code) == expectedResult

        where:
        code  || expectedResult
        "A"   || ControlRequestType.ADVANCE
        "PAU" || ControlRequestType.PREAUTH_UPDATE
        "PAC" || ControlRequestType.PREAUTH_COMPLETE
    }

    @Unroll
    def "is Pre Auth"() {
        expect:
        v_type.isPreAuth() == expectedResult

        where:
        v_name             || v_type                              || expectedResult
        "ADVANCE"          || ControlRequestType.ADVANCE          || false
        "PREAUTH_UPDATE"   || ControlRequestType.PREAUTH_UPDATE   || true
        "PREAUTH_COMPLETE" || ControlRequestType.PREAUTH_COMPLETE || true
    }

    @Unroll
    def "update Original"() {
        expect:
        v_type.updateOriginal() == expectedResult

        where:
        v_name             || v_type                              || expectedResult
        "ADVANCE"          || ControlRequestType.ADVANCE          || false
        "PREAUTH_UPDATE"   || ControlRequestType.PREAUTH_UPDATE   || true
        "PREAUTH_COMPLETE" || ControlRequestType.PREAUTH_COMPLETE || true
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme