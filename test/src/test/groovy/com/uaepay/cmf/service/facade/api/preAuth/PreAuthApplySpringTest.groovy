package com.uaepay.cmf.service.facade.api.preAuth

import com.uaepay.cmf.common.domain.ChannelControlResult
import com.uaepay.cmf.common.domain.ChannelFundResult
import com.uaepay.cmf.common.enums.ControlRequestType
import com.uaepay.cmf.common.enums.FundChannelApiType
import com.uaepay.cmf.common.util.CommonUtils
import com.uaepay.cmf.domainservice.main.sender.ControlOrderSendService
import com.uaepay.cmf.domainservice.main.sender.InstOrderSendService
import com.uaepay.cmf.domainservice.main.spi.FundOutRejectService
import com.uaepay.cmf.domainservice.main.spi.impl.DefaultSubmitInstitutionService
import com.uaepay.cmf.ext.service.impl.DefaultControlRequestFacade
import com.uaepay.cmf.ext.service.impl.DefaultFundRequestFacade
import com.uaepay.cmf.ext.service.process.impl.FundProcessService
import com.uaepay.cmf.service.facade.domain.CmfRequest
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest
import com.uaepay.cmf.test.CmfApplication
import com.uaepay.common.domain.Extension
import com.uaepay.common.util.money.Money
import com.uaepay.payment.common.v2.enums.PayMode
import com.uaepay.schema.cmf.enums.BizType
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.web.WebAppConfiguration
import spock.lang.Specification
import spock.lang.Unroll

import javax.annotation.Resource
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

import static org.mockito.Mockito.when

/**
 *
 * <p>预授权测试用例</p>
 *
 * <AUTHOR>
 * @version PreAuthRequestApiTest.java v1.0  2022/10/17 11:40
 */
@ContextConfiguration
@SpringBootTest(classes = CmfApplication.class, properties = "spring.profiles.active=dev,unittest,manual")
@WebAppConfiguration
class PreAuthApplySpringTest extends Specification {


    @Resource
    @InjectMocks
    FundProcessService fundProcessService

    @Mock
    FundOutRejectService fundOutRejectService;

    @Resource
    @InjectMocks
    DefaultSubmitInstitutionService submitInstitutionService

    @Mock
    InstOrderSendService instOrderSendService

    @Mock
    ControlOrderSendService controlOrderSendService

    @InjectMocks
    @Resource
    DefaultFundRequestFacade fundRequestFacade

    @InjectMocks
    @Resource
    DefaultControlRequestFacade controlRequestFacade

    def setup() {
        MockitoAnnotations.openMocks(this)
    }


    @Unroll("preAuthApply where name=#v_name")
    def "preAuthApply"() {
        given:
        def cmfRequest = buildCmfRequest()

        if (v_requestNo != null) {
            cmfRequest.setPaymentSeqNo(v_requestNo)
        }

        def channelFundResult = new ChannelFundResult(fundChannelCode: "FS105",
                apiType: FundChannelApiType.PRE_DEBIT,
                apiResultCode: v_api_result_code,
                apiResultSubCode: v_api_result_sub_code,
                realAmount: cmfRequest.getAmount())

        when(instOrderSendService.send(Mockito.any())).thenReturn(channelFundResult)
        when(fundOutRejectService.getRejectAccountType(Mockito.any())).thenReturn(Mockito.any())

        when:
        def result = fundRequestFacade.apply(cmfRequest, CommonUtils.defaultOpEnv())

        then:
        v_result_code == result.resultCode.name()

        where:
        v_name || v_requestNo || v_api_result_code || v_api_result_sub_code      || v_result_code
        "Pass" || null        || "S"               || "reservation_initial.APPR" || "SUCCESS"

    }


    @Unroll("preAuthUpdate:#v_name")
    def "preAuthUpdate"() {
        given:

        // --先造一笔申请成功的订单
        def cmfRequest = buildCmfRequest()
        cmfRequest.setAmount(new Money(v_apply_amount, "AED"))

        def channelFundResult = new ChannelFundResult(fundChannelCode: "FS105",
                apiType: FundChannelApiType.PRE_DEBIT,
                apiResultCode: "S",
                apiResultSubCode: "reservation_initial.APPR",
                realAmount: cmfRequest.getAmount())

        when(fundOutRejectService.getRejectAccountType(Mockito.any())).thenReturn(null)
        when(instOrderSendService.send(Mockito.any())).thenReturn(channelFundResult)

        fundRequestFacade.apply(cmfRequest, CommonUtils.defaultOpEnv())
        // --申请成功
        println("=========>>>>> cmfRequest" + cmfRequest.getPaymentSeqNo())
        // --组建控制单-更新
        def controlRequest = buildControlRequest()
        controlRequest.setAmount(new Money(v_amount, "AED"))

        def controlResult = new ChannelControlResult(fundChannelCode: "FS105",
                apiType: FundChannelApiType.PREAUTH_UPDATE,
                apiResultCode: v_api_result_code,
                apiResultSubCode: v_api_result_sub_code)
        when(controlOrderSendService.send(Mockito.any())).thenReturn(controlResult)

        controlRequest.getExtension().add("orgiFundinOrderNo", cmfRequest.getPaymentSeqNo())

        when:
        def result = controlRequestFacade.control(controlRequest, CommonUtils.defaultOpEnv())

        then:
        result.resultCode.name() == v_result_code

        where:
        v_name              || v_apply_amount || v_amount || v_requestNo || v_api_result_code || v_api_result_sub_code     || v_result_code
        "PreAuth Increase"  || "100"          || "120"    || null        || "S"               || "reservation_update.APPR" || "SUCCESS"
        "PreAuth Decrease"  || "120"          || "80"     || null        || "S"               || "reservation_update.APPR" || "SUCCESS"
        "PreAuth No Change" || "100"          || "100"    || null        || "S"               || "reservation_update.APPR" || "SUCCESS"
    }


    @Unroll("preAuthComplete:#v_name")
    def "preAuthComplete"() {
        given:

        // --先造一笔申请成功的订单
        def cmfRequest = buildCmfRequest()
        cmfRequest.setAmount(new Money(v_apply_amount, "AED"))

        def channelFundResult = new ChannelFundResult(fundChannelCode: "FS105",
                apiType: FundChannelApiType.PRE_DEBIT,
                apiResultCode: "S",
                apiResultSubCode: "reservation_complete.APPR",
                realAmount: cmfRequest.getAmount())

        when(fundOutRejectService.getRejectAccountType(Mockito.any())).thenReturn(null)
        when(instOrderSendService.send(Mockito.any())).thenReturn(channelFundResult)

        fundRequestFacade.apply(cmfRequest, CommonUtils.defaultOpEnv())
        // --申请成功
        println("=========>>>>> cmfRequest==>>" + cmfRequest.getPaymentSeqNo())
        // --组建控制单-更新
        def controlRequest = buildControlRequest()
        controlRequest.setAmount(new Money(v_amount, "AED"))
        controlRequest.setRequestType(ControlRequestType.PREAUTH_COMPLETE)

        def controlResult = new ChannelControlResult(fundChannelCode: "FS105",
                apiType: FundChannelApiType.PREAUTH_COMPLETE,
                apiResultCode: v_api_result_code,
                apiResultSubCode: v_api_result_sub_code)
        when(controlOrderSendService.send(Mockito.any())).thenReturn(controlResult)

        controlRequest.getExtension().add("orgiFundinOrderNo", cmfRequest.getPaymentSeqNo())

        when:
        def result = controlRequestFacade.control(controlRequest, CommonUtils.defaultOpEnv())

        then:
        result.resultCode.name() == v_result_code

        where:
        v_name                 || v_apply_amount || v_amount || v_requestNo || v_api_result_code || v_api_result_sub_code     || v_result_code
        "Complete Diff Amount" || "100"          || "80"     || null        || "S"               || "reservation_complete.APPR" || "SUCCESS"
        "Complete Same Amount" || "100"          || "100"    || null        || "S"               || "reservation_complete.APPR" || "SUCCESS"
    }


    private CmfRequest buildCmfRequest() {
        CmfRequest request = new CmfRequest()
        request.setInstCode("FISERV")
        request.setPaymentSeqNo(LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE) + 'FS' + System.currentTimeMillis())
        request.setProductCode("60040090")
        request.setPaymentCode("4001")
        request.setPayMode(PayMode.QUICKPAY)
        request.setBizType(BizType.FUNDIN)
        request.setMemberId("anonymousMember")
        request.setAmount(new Money("100", "AED"))
        request.setBizTime(new Date())
        Extension extension = new Extension()
        extension.add("sourceCode", "POS_FISERV")
        extension.add("preAuth", "Y")
        extension.add("payLoad", "C3482033")
        extension.add("merchantId", "8116000002")
        extension.add("terminalId", "80905678")
        request.setExtension(extension)
        return request
    }


    CmfControlRequest buildControlRequest() {
        CmfControlRequest request = new CmfControlRequest()
        request.setRequestNo("D" + System.currentTimeMillis())
        request.setRequestType(ControlRequestType.PREAUTH_UPDATE)
        request.setPayMode(PayMode.QUICKPAY)
        request.setInstCode("FISERV")
        request.setAmount(new Money("100", "AED"))
        Extension extension = new Extension()
        extension.add("sourceCode", "POS_FISERV")
        extension.add("sourceOrder", "inst")
        extension.add("payLoad", "C3467039")
        extension.add("merchantId", "000008116000002")
        extension.add("terminalId", "80905679")
        request.setExtension(extension)
        return request;
    }

}
