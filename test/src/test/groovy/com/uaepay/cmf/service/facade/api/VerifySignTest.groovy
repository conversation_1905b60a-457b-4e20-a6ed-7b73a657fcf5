package com.uaepay.cmf.service.facade.api;

import com.uaepay.cmf.common.domain.ChannelControlResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.common.util.CommonUtils;
import com.uaepay.cmf.domainservice.main.general.impl.VerifySignProcessor
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import com.uaepay.cmf.test.CmfApplication;
import com.uaepay.common.util.money.Money;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.web.WebAppConfiguration;
import spock.lang.Specification;
import spock.lang.Unroll;

import javax.annotation.Resource;

import static org.mockito.Mockito.when;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version: VerifySignTest.class v1.0
 */
@ContextConfiguration
@SpringBootTest(classes = CmfApplication.class, properties = "spring.profiles.active=dev,unittest,manual")
@WebAppConfiguration
public class VerifySignTest extends Specification {

    @Resource
    VerifySignProcessor verifySignProcessor;

    @Unroll("verifySignProcessor:#v_name")
    def "verifySignProcessor"() {

        when:
        def result = verifySignProcessor.process(request)

        then:
        result.headerMap != null

        where:
        v_name              || request
        "PreAuth Increase"  || new VerifySignRequest(channelCode: "APLUS101", apiType:"VS", instOrderNo:"T202310270000000824", callbackType: "server")
    }

}
