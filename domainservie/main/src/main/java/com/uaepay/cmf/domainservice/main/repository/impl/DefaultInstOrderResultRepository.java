package com.uaepay.cmf.domainservice.main.repository.impl;

import com.uaepay.basic.cobarclient.support.utils.CollectionUtils;
import com.uaepay.cmf.common.core.dal.daointerface.InstOrderResultDAO;
import com.uaepay.cmf.common.core.dal.dataobject.InstOrderResultDO;
import com.uaepay.cmf.common.core.dal.util.DbRouter;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator;
import com.uaepay.cmf.common.core.engine.generator.SequenceNameEnum;
import com.uaepay.cmf.domainservice.main.convert.InstOrderResultConverter;
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>机构订单结果仓储默认实现</p>
 *
 * <AUTHOR>
 * @version $Id: DefaultInstOrderResultRepository.java, v 0.1 2012-8-3 上午10:13:34 fuyangbiao Exp $
 */
@Repository("instOrderResultRepository")
@Slf4j
public class DefaultInstOrderResultRepository implements InstOrderResultRepository {

    /**
     * 机构订单结果DAO
     */
    @Resource
    private InstOrderResultDAO instOrderResultDAO;

    @Resource
    private PrimaryKeyGenerator primaryKeyGenerator;

    @Override
    public InstOrderResult load(Long resultId, boolean isLock) {
        InstOrderResultDO resultDO = null;
        if (isLock) {
            resultDO = instOrderResultDAO.lockedById(resultId);
        } else {
            resultDO = instOrderResultDAO.loadById(resultId);
        }
        InstOrderResult result = InstOrderResultConverter.convert(resultDO);

        return result;
    }

    @Override
    public InstOrderResult loadRealResultByOrder(Long orderId) {
        List<InstOrderResultDO> results = instOrderResultDAO.loadByOrder(orderId);
        if (results == null || results.isEmpty()) {
            return null;
        }

        List<InstOrderResult> orders = InstOrderResultConverter.convert(results);

        if (orders != null && orders.size() > 0) {

            //R_C:渠道结果会有多条，所以从结果中随机取一条的逻辑会有问题
            InstOrderResult result = getRealOperateResult(orders);
            return result;
        } else {
            return null;
        }
    }

    /**
     * 获取真实处理的结果
     * 1.默认返回第一条
     * 2.当存在操作状态为成功的,直接返回
     * 3.优先返回状态为最终结果的数据
     *
     * @param results
     * @return
     */
    private InstOrderResult getRealOperateResult(List<InstOrderResult> results) {
        List<InstOrderResult> finalList = results.stream().filter(this::isLastStatus).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(finalList)) {
            return finalList.get(0);
        } else {
            for (InstOrderResult result : results) {
                if (StringUtils.isNotEmpty(result.getInstSeqNo())) {
                    return result;
                }
            }
        }
        return null;
    }

    /**
     * 判断结果状态是否为最终结果
     *
     * @param result
     * @return
     */
    private boolean isLastStatus(InstOrderResult result) {
        return InstOrderResultStatus.FAILURE.equals(result.getStatus())
                || InstOrderResultStatus.SUCCESSFUL.equals(result.getStatus());
    }

    @Override
    public Long storeOrUpdate(InstOrderResult result) {
        try {
            // 若instOrderId + apiResultCode + apiResultSubCode 已存在，则更新
            List<InstOrderResultDO> instOrderResults = instOrderResultDAO.loadByInstOrderIdAndResult(result.getInstOrderId(), result.getApiType() == null ? null : result.getApiType().getCode(), result.getApiResultCode(), result.getApiResultSubCode());
            // 记录已存在，则更新
            if (CollectionUtils.isNotEmpty(instOrderResults)) {
                InstOrderResultDO dbResult = instOrderResults.get(0);
                int rows = instOrderResultDAO.updateResultById(InstOrderResultConverter.convert(result, dbResult), dbResult.getResultId());
                putResultTime(result);
                return dbResult.getResultId();
            }
            return store(result);
        } catch (Throwable e) {
            log.error("InstOrderResultRepository.storeOrUpdate failed! result:{}", result, e);
        }
        return null;
    }

    @Override
    public Long store(InstOrderResult result) {
        InstOrderResultDO resultDO = InstOrderResultConverter.convert(result);
        String resultId = DbRouter.gen4Sharding(
                primaryKeyGenerator.generateKey(SequenceNameEnum.INST_RESULT), resultDO.getInstOrderId()
                        + "");
        resultDO.setResultId(Long.valueOf(resultId));
        // 存机构订单结果表
        instOrderResultDAO.insert(resultDO);
        result.setResultId(resultDO.getResultId());
        putResultTime(result);
        return result.getResultId();
    }

    private void putResultTime(InstOrderResult result) {
        if (result.getGmtCreate() == null) {
            result.setGmtCreate(new Date());
        }
        if (result.getGmtModified() == null) {
            result.setGmtModified(new Date());
        }
    }

    private InstOrderResult createManualInstOrderResult(InstOrder instOrder,
                                                        InstOrderResultStatus instOrderResultStatus,
                                                        String memo) {
        InstOrderResult instOrderResult = new InstOrderResult();
        instOrderResult.setInstOrderId(instOrder.getInstOrderId());
        instOrderResult.setBizType(instOrder.getBizType());
        instOrderResult.setRealAmount(instOrder.getAmount());
        instOrderResult.setStatus(instOrderResultStatus);
        instOrderResult.setMemo("强制" + memo);
        instOrderResult.setOperateStatus(InstResultOperateStatus.AWAITING);
        instOrderResult.setFundChannelCode(instOrder.getFundChannelCode());
        return instOrderResult;
    }

    /**
     * 人工生成结果订单
     *
     * @param instOrder
     * @param memo
     * @param instOrderResultStatus
     * @return
     */
    @Override
    public InstOrderResult convertInstOrder(InstOrder instOrder, String memo,
                                            InstOrderResultStatus instOrderResultStatus) {
        InstOrderResult instOrderResult = createManualInstOrderResult(instOrder,
                InstOrderResultStatus.SUCCESSFUL, memo);
        // this.storeOrUpdate(instOrderResult);
        return instOrderResult;
    }

    @Override
    public void updateOperateStatusByResultIds(InstResultOperateStatus to, List<Long> ids,
                                               InstResultOperateStatus from) {
        instOrderResultDAO.updateOperateStatusByResultIds(to.getCode(), ids, from.getCode());
    }

    @Override
    public InstOrderResult getLastResult(Long instOrderId) {
        List<InstOrderResultDO> list = instOrderResultDAO.listByInstOrderId(instOrderId);
        for (InstOrderResultDO instOrderResultDO : list) {
            InstOrderResult result = InstOrderResultConverter.convert(instOrderResultDO);
            /** 单个 */
            if (result.getOperateStatus() != InstResultOperateStatus.FAILURE) {
                return result;
            }
        }
        return null;
    }

    @Override
    public List<InstOrderResult> getAllResult(Long instOrderId) {
        List<InstOrderResultDO> list = instOrderResultDAO.listByInstOrderId(instOrderId);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<InstOrderResult> resultList = InstOrderResultConverter.convert(list);
        return resultList;
    }

    @Override
    public int updateRiskFlagById(RiskFlag riskFlag, Long resultId) {
        return instOrderResultDAO.updateRiskFlagById(riskFlag.getCode(), resultId);
    }

    @Override
    public int updateOperateStatusById(InstResultOperateStatus operateStatus, Long resultId) {
        return instOrderResultDAO.updateOperateStatusById(operateStatus.getCode(), resultId);
    }

}
