package com.uaepay.cmf.domainservice.main.repository;

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.OrderFlag;
import com.uaepay.cmf.common.core.domain.exception.DuplicateKeyException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.enums.FundChannelApiType;

import java.util.Date;
import java.util.List;

/**
 * <p>机构控制订单仓储</p>
 *
 * <AUTHOR>
 * @version $Id: InstControlOrderRepository.java, v 0.1 2012-8-20 上午10:20:50 fuyangbiao Exp $
 */
public interface InstControlOrderRepository {

    /**
     * 保存机构控制订单
     *
     * @param order
     * @throws DuplicateKeyException
     */
    void store(InstControlOrder order);

    /**
     * 根据请求号获取机构控制订单
     *
     * @param requestNo
     * @return
     */
    InstControlOrder loadByRequestNo(String requestNo);


    /**
     * 查询最近的一条 控制订单
     * @param requestNo
     * @param apiType
     * @return
     */
    InstControlOrder loadLatestByRequestNoAndApiType(String requestNo, String apiType);

    /**
     * 更新控制订单状态
     *
     * @param orderId
     * @param targetStatus
     * @param preStatus
     * @return
     */
    int updateStatusById(long orderId, InstOrderStatus targetStatus, InstOrderStatus preStatus);

    /**
     * 已经机构订单装载控制订单数据
     *
     * @param instOrderNo
     * @return
     */
    InstControlOrder loadByNo(String instOrderNo);

    /**
     * 装载控制订单数据
     *
     * @param orderId
     * @return
     */
    InstControlOrder loadWithOrderId(long orderId);

    /**
     * 根据原状态修改现在状态
     *
     * @param controlOrder
     * @param communicateStatus
     * @param preStatus
     * @return
     */
    int updateCommunicateStatusByIdAndPreStatus(InstControlOrder controlOrder, CommunicateStatus communicateStatus, CommunicateStatus preStatus);

    /**
     * 更新控制订单最终结果
     *
     * @param instControlOrder
     * @param targetStatus
     * @return
     */
    boolean updateInstControlOrderStatus(InstControlOrder instControlOrder, InstOrderStatus targetStatus);

    /**
     * 更新扩展参数
     *
     * @param extension
     * @param requestNo
     * @return
     */
    int updateExtensionByRequestNo(String extension, String requestNo);

    /**
     * 获取订单列表供查询用
     *
     * @param apiType
     * @param batchSize
     * @return
     */
    List<Long> loadControlOrder4Query(FundChannelApiType apiType, Date startDate, Date endDate, int batchSize);

    /**
     * 修改查询时间
     *
     * @param retryTimes
     * @param retryDateTime
     * @param orderId
     * @return
     */
    int updateRetryInfoById(int retryTimes, Date retryDateTime, long orderId);

    /**
     * 更新锁定状态
     *
     * @param orderId
     * @param flag
     * @param preFlag
     * @return
     */
    int updateFlagWithOrderIdAndPreFlag(Long orderId, OrderFlag flag, OrderFlag preFlag);
}
