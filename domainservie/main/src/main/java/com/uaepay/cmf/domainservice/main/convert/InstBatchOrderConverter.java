package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.dal.dataobject.InstBatchOrderDO;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.InstOrderArchiveStatus;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

/**
 * <p>
 * 机构订单转换器
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: InstOrderConverter.java, v 0.1 2010-12-22 下午03:55:47 sean won
 * Exp $
 */
public class InstBatchOrderConverter implements BasicConstant {

    private InstBatchOrderConverter() {

    }

    /**
     * DO-->机构批量订单
     *
     * @param batchDO
     * @return
     */
    public static InstBatchOrder convert(InstBatchOrderDO batchDO) {
        if (batchDO == null) {
            return null;
        }
        InstBatchOrder order = new InstBatchOrder();
        BeanUtils.copyProperties(batchDO, order);
        order.setBizType(BizType.getByCode(batchDO.getOrderType()));
        order.setIsLocked(YesNo.getByCode(batchDO.getIsLocked()));
        order.setStatus(InstOrderArchiveStatus.getByCode(batchDO.getStatus()));
        order.setApiCode(batchDO.getFundChannelApi());
        order.setInstBatchNo(batchDO.getBatchOrderNo());
        order.setPayMode(PayMode.getByCode(batchDO.getPayMode()));
        order.setCheckFlag(YesNo.getByCode(batchDO.getCheckFlag()));
        order.setAmount(batchDO.getTotalAmount());
        if (batchDO.getFundChannelApi() != null) {
            //设置接口类型
            String[] apiArray = batchDO.getFundChannelApi().split(SPLIT_TAG);
            if (apiArray.length == 2) {
                order.setApiType(FundChannelApiType.getByCode(apiArray[1]));
            }
        }
        order.setExtension(CommonConverter.convertFromDb(batchDO.getExtension()));
        return order;
    }

    /**
     * 机构批量订单-->DO
     *
     * @param batch
     * @return
     */
    public static InstBatchOrderDO convert(InstBatchOrder batch) {
        if (batch == null) {
            return null;
        }
        InstBatchOrderDO orderDO = new InstBatchOrderDO();
        BeanUtils.copyProperties(batch, orderDO);
        if (batch.getBizType() != null) {
            orderDO.setOrderType(batch.getBizType().getCode());
        }
        if (batch.getIsLocked() != null) {
            orderDO.setIsLocked(batch.getIsLocked().getCode());
        }
        if (batch.getStatus() != null) {
            orderDO.setStatus(batch.getStatus().getCode());
        }
        orderDO.setBatchOrderNo(batch.getInstBatchNo());
        if (batch.getPayMode() != null) {
            orderDO.setPayMode(batch.getPayMode().getCode());
        }
        if (batch.getBizType() != null) {
            orderDO.setOrderType(batch.getBizType().getCode());
        }
        orderDO.setFundChannelApi(batch.getApiCode());
        orderDO.setTotalAmount(batch.getAmount());

        if (batch.getCheckFlag() != null) {
            orderDO.setCheckFlag(batch.getCheckFlag().getCode());
        }
        if (batch.getExtension() != null) {
            orderDO.setExtension(CommonConverter.convertToDb(batch.getExtension()));
        }
        return orderDO;
    }
}
