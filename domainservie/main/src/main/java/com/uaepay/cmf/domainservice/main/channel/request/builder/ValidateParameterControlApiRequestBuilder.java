package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.domain.ChannelControlRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import static com.uaepay.cmf.common.enums.FundChannelApiType.VALIDATE_PARAMETER;

/**
 * <p>Apple</p>
 *
 * <AUTHOR>
 * @date ControlApiRequestBuilder.java v1.0  2020-09-09 18:05
 */
@Service
public class ValidateParameterControlApiRequestBuilder extends AbstractApiRequestBuilder<InstControlOrder, ChannelControlRequest> {

    private static final String APPLY_PAY_INST_CODE = "APPLEPAY";
    private static final String EXT_KEY_CODE_DISPLAY_NAME = "displayName";

    @Override
    protected void buildCustomParam(InstControlOrder order, ChannelControlRequest request) {
        BeanUtils.copyProperties(order, request);
        request.setTargetInstCode(order.getInstCode());
        request.setInstOrderSubmitTime(order.getGmtCreate());
        if (FundChannelApiType.VALIDATE_PARAMETER.equals(request.getApiType()) && APPLY_PAY_INST_CODE.equals(order.getInstCode())) {
            String displayName = request.getExtension() == null ? "" : request.getExtension().get(EXT_KEY_CODE_DISPLAY_NAME);
            Boolean changeDisplayName = StringUtils.isNotBlank(displayName) && displayName.length() > 64;
            request.getExtension().put(EXT_KEY_CODE_DISPLAY_NAME, changeDisplayName ? StringUtils.substring(displayName, 0, 64) : displayName);
        }
    }

    @Override
    public ChannelControlRequest buildReq() {
        return new ChannelControlRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(VALIDATE_PARAMETER);
    }
}
