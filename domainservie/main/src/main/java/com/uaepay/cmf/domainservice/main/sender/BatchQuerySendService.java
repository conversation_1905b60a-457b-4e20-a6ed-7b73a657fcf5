package com.uaepay.cmf.domainservice.main.sender;

import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.domain.query.BatchQueryItemRequest;
import org.springframework.stereotype.Service;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BatchQuerySendService.java v1.0  2020-09-12 00:55
 */
@Service
public class BatchQuerySendService extends AbstractChannelSendService<InstBatchOrder, BatchQueryItemRequest, ChannelFundBatchResult> {

    @Override
    protected void updateOrderCommunicateStatus2Send(InstBatchOrder order) {
        // do nothing
    }

    @Override
    protected ChannelFundBatchResult send2Bank(BatchQueryItemRequest request, InstBatchOrder order) {
        return channelSenderFactory.applyBatchQuery(request);
    }

    @Override
    protected ChannelFundBatchResult buildResp() {
        return new ChannelFundBatchResult();
    }

}
