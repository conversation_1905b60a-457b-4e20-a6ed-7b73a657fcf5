package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.vo.EscrowTransformOrder;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date TransformOrderConverter.java v1.0
 */
public class TransformOrderConverter implements BasicConstant {

    private static final String SPLIT_TYPE = "split";

    private TransformOrderConverter(){

    }

    public static EscrowTransformOrder convert(InstOrder instOrder){
        EscrowTransformOrder order = new EscrowTransformOrder();
        order.setOrderNo(instOrder.getInstOrderNo());
        order.setAmount(instOrder.getAmount());
        order.setBizProductCode(instOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.getKey()));
        order.setMemberId(instOrder.getExtension().get(ExtensionKey.MEMBER_ID.getKey()));
        order.setTransTime(instOrder.getGmtCreate());
        order.setTransStatus(instOrder.getStatus().getCode());
        order.setTransType(instOrder.getBizType().getCode());
        order.setReportType(SPLIT_TYPE);
        order.setMemo("Split Order");
        return order;
    }

}
