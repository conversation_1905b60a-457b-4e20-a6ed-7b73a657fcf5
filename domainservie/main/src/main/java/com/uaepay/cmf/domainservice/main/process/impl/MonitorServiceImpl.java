package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.enums.MonitorType;
import com.uaepay.cmf.common.monitor.MonitorLog;
import com.uaepay.cmf.domainservice.main.process.MonitorService;
import com.uaepay.cmf.domainservice.main.repository.MonitorLogRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>监控服务</p>
 *
 * <AUTHOR> won
 * @version $Id: MonitorServiceImpl.java, v 0.1 2011-7-20 下午03:12:47 sean won Exp $
 */
@Service
public class MonitorServiceImpl implements MonitorService {
    Logger logger = LoggerFactory.getLogger(MonitorServiceImpl.class);

    @Resource
    private MonitorLogRepository monitorLogRepository;

//    @Resource
//    private TaskLockService lockService;

    @Override
    public void logMonitorEvent(MonitorLog monitorLog) {
        try {
            //1. 保存监控日志
            monitorLogRepository.store(monitorLog);
            //2. 发送到监控系统
            List<MonitorLog> logs = new ArrayList<>();
            logs.add(monitorLog);
            //systemMonitor.sendMonitorEmail(logs);
        } catch (Exception e) {
            //为保证主流程正常，记录监控日志不外抛异常.
            logger.error("监控异常. monitorLog=" + monitorLog, e);
        }
    }

    @Override
    public void alertMonitorEvent(MonitorLog monitorLog, MonitorType monitorType) {

        if (monitorType == null || monitorLog == null) {
            return;
        }

        try {
            boolean lockStatus;
            //1:鉴于可能会有批量报警，会对mns造成压力且大部分报警无效，需要做时间间隔控制
//            Lock lock = new Lock(monitorType.getMonitorKey(), monitorType.getMessage(), LockType.EXCLUSION, monitorType.getMonitorInterval());
//            try {
//                lockStatus = lockService.lock(lock);
//            } catch (Exception e) {
//                logger.error(monitorType.getMonitorKey(), e);
//                return;
//            }
//            if (!lockStatus) {
//                return;
//            }

            //2:发送短信

            //3:记录MonitorLog，Task异步发送邮件
            this.logMonitorEvent(monitorLog);

        } catch (Exception e) {
            logger.error("监控异常. monitorLog=" + monitorLog, e);
        }


    }
}
