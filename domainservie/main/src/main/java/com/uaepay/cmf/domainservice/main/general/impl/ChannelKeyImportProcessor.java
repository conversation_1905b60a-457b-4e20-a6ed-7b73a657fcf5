package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.service.facade.domain.cache.ImportChannelKeyRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/03/2024 09:43
 */
@Slf4j
@Component
public class ChannelKeyImportProcessor extends GeneralProcessorTemplate<ImportChannelKeyRequest, CommonResponse> {

    @Autowired
    private SysConfigurationHolder sysConfigurationHolder;

    @Override
    protected String getServiceName() {
        return "IMPORT_CHANNEL_KEY";
    }

    @Override
    protected CommonResponse createResponse() {
        return new CommonResponse();
    }

    @Override
    protected void process(ImportChannelKeyRequest request, CommonResponse commonResponse) {
        sysConfigurationHolder.insert(request.getKeyName(), request.getKeyValue(), request.getMemo());
        commonResponse.setApplyStatus(ApplyStatusEnum.SUCCESS);
    }
}
