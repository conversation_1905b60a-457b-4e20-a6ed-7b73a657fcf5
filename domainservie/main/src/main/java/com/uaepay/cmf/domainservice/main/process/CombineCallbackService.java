package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.common.enums.FundChannelApiType;

/**
 * <p>组装CMF渠道交易结果回调地址</p>
 *
 * <AUTHOR> won
 * @version $Id: CombineCallbackService.java, v 0.1 2012-2-8 下午05:05:52 sean won Exp $
 */
public interface CombineCallbackService {

    /**
     * 组装CMF渠道交易结果回调地址
     *
     * @param channelCode 资金渠道
     * @param type 后端回调URL     apiType-页面回调URL
     * @return
     */
    String getCallBackUrl(String channelCode, String type, FundChannelApiType apiType);

}
