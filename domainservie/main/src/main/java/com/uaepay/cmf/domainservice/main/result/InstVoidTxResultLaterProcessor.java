package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.domainservice.main.convert.ChannelResultConverter;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.schema.cmf.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>机构单撤销结果处理</p>
 *
 * <AUTHOR>
 * @date 2022/6/29
 */
@Slf4j
@Component
public class InstVoidTxResultLaterProcessor implements ResultLaterProcessor {

    @Resource
    private InstResultProcessor instResultProcessor;

    @Resource
    protected InstOrderRepository instOrderRepository;

    @PostConstruct
    public void init() {
        ResultLaterProcessorEnum.VOID_TRANSACTION.register(this);
    }


    @Override
    public Result<?> process(InstControlOrder instControlOrder, InstControlOrderResult instControlResult) {

        if (!Objects.equals(instControlOrder.getStatus(), InstOrderStatus.SUCCESSFUL)) {
            log.info("交易撤销失败将重试");
            return Result.ofNothing();
        }

        InstOrder instOrder = instOrderRepository.loadByNo(instControlOrder.getPreInstOrderNo());
        Assert.isTrue(instOrder.getStatus()==InstOrderStatus.CANCEL, "交易状态不为撤销");
        InstOrderResult instResult = ChannelResultConverter.convert(instControlResult, instOrder);
        Assert.isTrue(instResult.getStatus()== InstOrderResultStatus.FAILURE, "被撤销的机构订单状态应该为失败");
        instResultProcessor.process(instOrder, instResult);

        return Result.ofSuccess();
    }
}
