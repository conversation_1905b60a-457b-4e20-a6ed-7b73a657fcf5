package com.uaepay.cmf.domainservice.main.sender;

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderArchiveStatus;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BatchOrderSendService.java v1.0  2020-09-12 00:53
 */
@Service
public class BatchOrderSendService extends AbstractChannelSendService<InstBatchOrder, ChannelFundRequest, ChannelFundBatchResult> {


    @Override
    @Transactional(rollbackFor = {Throwable.class})
    protected void updateOrderCommunicateStatus2Send(InstBatchOrder order) {
        // 1.更新批量订单状态
        int batchCount = instOrderRepository.updateBatchOrderStatus(order.getArchiveBatchId(),
                InstOrderArchiveStatus.IN_PROCESS, InstOrderArchiveStatus.AWAITING);

        // 批次更新失败则无需更新逐笔状态
        Assert.isTrue(batchCount == 1, "批量订单发送状态更新失败,该订单已执行过:" + order.getArchiveBatchId());
        order.setStatus(InstOrderArchiveStatus.IN_PROCESS);

        // 2. 更新逐笔订单的发送状态
        if (CollectionUtils.isNotEmpty(order.getInstOrderList())) {
            boolean exStatus = cmfTransactionTemplate.execute(status -> {

                int itemsCount = instOrderRepository.updateCommunicateStatusByInstOrders(
                        order.getInstOrderList(), CommunicateStatus.IN_PROCESS, CommunicateStatus.AWAITING);

                boolean modSuccess = itemsCount == order.getInstOrderList().size();

                // 未更新成功则回滚，批次订单停留在I状态，单笔订单为A
                if (!modSuccess) {
                    status.setRollbackOnly();
                }
                return modSuccess;
            });
            Assert.isTrue(exStatus, "批量订单的机构订单笔数更新不相等");
        }
    }

    @Override
    protected ChannelFundBatchResult send2Bank(ChannelFundRequest request, InstBatchOrder order) {
        return channelSenderFactory.applyBatch(request);
    }

    @Override
    protected ChannelFundBatchResult buildResp() {
        return new ChannelFundBatchResult();
    }

}
