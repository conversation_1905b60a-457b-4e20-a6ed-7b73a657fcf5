package com.uaepay.cmf.domainservice.main.repository;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;

import java.util.List;

/**
 * <p>机构控制订单结果仓储</p>
 *
 * <AUTHOR>
 * @version $Id: InstControlOrderResultRepository.java, v 0.1 2012-8-20 上午10:51:32 fuyangbiao Exp $
 */
public interface InstControlOrderResultRepository {

    /**
     * 保存订单结果
     *
     * @param result
     */
    void storeOrUpdate(InstControlOrderResult result);

    /**
     * 根据控制订单ID获取订单结果
     *
     * @param orderId
     * @return
     */
    List<InstControlOrderResult> loadByOrderId(Long orderId);


    /**
     * 根据控制单号获取订单结果
     * @param instOrderNo
     * @return
     */
    InstControlOrderResult loadByInstOrderNo(String instOrderNo);

}
