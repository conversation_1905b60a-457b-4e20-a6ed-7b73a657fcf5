package com.uaepay.cmf.domainservice.main.spi;

import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.common.core.domain.vo.VerifyResponseContent;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import org.apache.commons.lang3.tuple.Pair;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version: IdempotencyService.class v1.0
 */
public interface IdempotencyService {
    Pair<InstBaseResult, VerifyResponseContent> processIdempotent(VerifySignRequest request, InstBaseOrder baseOrder);
}
