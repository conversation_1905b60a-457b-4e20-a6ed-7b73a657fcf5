package com.uaepay.cmf.domainservice.main.spi;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.domainservice.main.domain.FundRejectReason;
import com.uaepay.cmf.domainservice.main.domain.H2hRejectAccountEnum;
import com.uaepay.grc.connect.api.vo.domain.CheckInfo;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date FundOutRejectService.java v1.0  2020-11-11 21:56
 */
public interface FundOutRejectService {

    H2hRejectAccountEnum getRejectAccountType(List<InstOrder> instOrderList);

    InstOrderResult processRejectAccountRequest(List<InstOrder> instOrderList, H2hRejectAccountEnum accountType);


    /**
     * 获取拒绝原因
     * @param instOrder instOrder
     *
     * @return 拒绝原因
     */
    FundRejectReason  getRejectReason(InstOrder instOrder);


    /**
     * 机构订单列表
     *
     * @param instOrder 机构订单
     * @param reason 拒绝原因
     * @return 机构订单结果
     */
    InstOrderResult processRejectRequest(InstOrder instOrder, FundRejectReason reason);

    /**
     * 批量拒绝
     * @param instOrders 机构订单
     * @param reason 原因
     */
    void batchRejectWithdrawOrder(List<InstOrder> instOrders, FundRejectReason reason);

    /**
     * 风控检查
     * @param instOrderList 机构订单列表
     * @return 检查信息
     */
    List<CheckInfo> grcCheck(List<InstOrder> instOrderList);

    /**
     * 是否为提现订单
     * @param instOrder 机构订单
     * @return 是否提现订单
     */
    boolean isWithdrawOrder(InstOrder instOrder);
}
