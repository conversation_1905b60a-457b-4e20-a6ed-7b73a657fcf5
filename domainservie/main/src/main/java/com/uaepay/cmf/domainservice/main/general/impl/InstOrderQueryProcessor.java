package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.PageResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.exception.CmfBizException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.domainservice.main.convert.InstOrderConverter;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.service.facade.domain.query.InstOrderQueryRequest;
import com.uaepay.cmf.service.facade.domain.query.SimpleOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date InstOrderQueryProcessor.java v1.0
 */
@Service
public class InstOrderQueryProcessor extends GeneralProcessorTemplate<InstOrderQueryRequest, PageResponse<SimpleOrder>> {

    @Resource
    private InstOrderRepository instOrderRepository;

    @Override
    protected String getServiceName() {
        return "InstOrderQueryProcessor";
    }

    @Override
    protected PageResponse<SimpleOrder> createResponse() {
        PageResponse<SimpleOrder> response = new PageResponse<>();
        response.setApplyStatus(ApplyStatusEnum.SUCCESS);
        return response;
    }

    @Override
    protected void process(InstOrderQueryRequest request, PageResponse<SimpleOrder> response) throws CmfBizException {
        List<SimpleOrder> simpleOrderList = new ArrayList<>(request.getInstOrderNoList().size());
        for(String instOrderNo:request.getInstOrderNoList()){
            InstOrder instOrder = instOrderRepository.loadByNo(instOrderNo);
            simpleOrderList.add(InstOrderConverter.transfer(instOrder));
        }
        response.setDataList(simpleOrderList);
    }
}
