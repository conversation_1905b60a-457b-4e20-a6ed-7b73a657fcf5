package com.uaepay.cmf.domainservice.main.sender;

import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.query.QueryRequest;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date InstOrderQuerySendService.java v1.0  2020-09-12 00:54
 */
@Service
@Slf4j
public class SingleQuerySendService extends AbstractChannelSendService<InstOrder, QueryRequest, ChannelFundResult> {

    @Value("${cmf.single.query.dynamic:{\"LEAN101\":\"60000\"}}")
    private String singleQueryDynamic;

    @Override
    protected void updateOrderCommunicateStatus2Send(InstOrder order) {
        // do nothing
    }

    @Override
    protected ChannelFundResult send2Bank(QueryRequest request, InstOrder order) {
        Long timeout = null;
        try {
            if (StringUtils.isNotBlank(singleQueryDynamic)) {
                Map<String, String> map = JsonUtil.parseMap(singleQueryDynamic);
                String timeoutString = map.get(order.getFundChannelCode());
                timeout = StringUtils.isNotBlank(timeoutString) ? Long.valueOf(timeoutString) : null;
            }
        } catch (Exception e) {
            log.warn("SingleQuerySendService.singleQueryDynamic.warn,", e);
        }

        return channelSenderFactory.applyFund(request, timeout);
    }

    @Override
    protected ChannelFundResult buildResp() {
        return new ChannelFundResult();
    }

}
