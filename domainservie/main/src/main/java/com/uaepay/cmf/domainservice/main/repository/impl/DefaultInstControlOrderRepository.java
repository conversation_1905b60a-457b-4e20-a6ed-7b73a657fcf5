package com.uaepay.cmf.domainservice.main.repository.impl;

import com.uaepay.cmf.common.core.dal.daointerface.ControlOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.ControlOrderDO;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.OrderFlag;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.ma.MemberChannelToken;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator;
import com.uaepay.cmf.common.core.engine.generator.SequenceNameEnum;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.TokenHolder;
import com.uaepay.cmf.domainservice.main.convert.InstControlOrderConverter;
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.schema.cmf.enums.BizType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * <p>机构控制订单仓储默认实现</p>
 * </p>
 *
 * <AUTHOR>
 * @version $Id: DefaultInstControlOrderRepository.java, v 0.1 2012-8-20 上午10:23:33 fuyangbiao Exp $
 * 上午10:23:33 fuyangbiao Exp $
 */
@Repository("instControlOrderRepository")
public class DefaultInstControlOrderRepository implements InstControlOrderRepository {
    private Logger logger = LoggerFactory
            .getLogger(DefaultInstControlOrderRepository.class);
    @Resource
    private ControlOrderDAO controlOrderDAO;

    @Resource
    private TransactionTemplate cmfTransactionTemplate;

    @Resource
    private CardTokenRepository cardTokenRepository;

    @Resource
    private PrimaryKeyGenerator primaryKeyGenerator;

    @Override
    public void store(InstControlOrder order) {
        order.setOrderId(Long.valueOf(primaryKeyGenerator
                .generateKey(SequenceNameEnum.CONTROL_ORDER)));
        order.setGmtCreate(new Date());
        order.setGmtModified(new Date());
        order.setGmtNextRetry(new Date());
        ControlOrderDO orderDO = InstControlOrderConverter.convert(order);
        long orderId = controlOrderDAO.insert(orderDO);
        order.setOrderId(orderId);
        // 回写机构订单id
        updateCardToken(order);
    }

    private void updateCardToken(InstControlOrder controlOrder) {
        Map<String, Object> holderMap = TokenHolder.get();
        if (holderMap == null || !holderMap.containsKey(ExtensionKey.CARD_TOKEN.key)) {
            return;
        }
        CardToken cardToken = (CardToken) holderMap.get(ExtensionKey.CARD_TOKEN.key);
        Assert.isTrue(cardToken.getInstOrderId() == null, "Card token has been used!");
        String instTokenId = null;
        if (holderMap.containsKey(ExtensionKey.MEMBER_TOKENS.key)) {
            List<MemberChannelToken> memberTokens = (List<MemberChannelToken>) holderMap.get(ExtensionKey.MEMBER_TOKENS.key);
            for (MemberChannelToken token : memberTokens) {
                if (controlOrder.getFundChannelCode().equals(token.getFundChannelCode())) {
                    instTokenId = token.getToken();
                }
            }
        }
        cardTokenRepository.updateInstInfo(controlOrder.getOrderId(), instTokenId, cardToken);
        holderMap.put(ExtensionKey.CARD_TOKEN.key, cardToken);
        TokenHolder.set(holderMap);
    }

    @Override
    public InstControlOrder loadByRequestNo(String requestNo) {
        ControlOrderDO orderDO = controlOrderDAO.loadWithRequestNo(requestNo);
        if (orderDO == null) {
            return null;
        }

        return InstControlOrderConverter.convert(orderDO);
    }

    @Override
    public InstControlOrder loadLatestByRequestNoAndApiType(String requestNo, String apiType) {
        ControlOrderDO orderDO =controlOrderDAO.loadWithRequestNoAndApiType(requestNo, apiType);
        if (orderDO == null) {
            return null;
        }

        return InstControlOrderConverter.convert(orderDO);
    }

    @Override
    public InstControlOrder loadWithOrderId(long orderId) {
        ControlOrderDO orderDO = controlOrderDAO.loadWithOrderId(orderId);
        if (orderDO == null) {
            return null;
        }

        return InstControlOrderConverter.convert(orderDO);
    }

    @Override
    public int updateStatusById(long orderId, InstOrderStatus targetStatus, InstOrderStatus preStatus) {
        return controlOrderDAO.updateStatusById(orderId, targetStatus.getCode(), preStatus.getCode());
    }

    @Override
    public InstControlOrder loadByNo(String instOrderNo) {
        ControlOrderDO orderDO = controlOrderDAO.loadWithInstOrderNo(instOrderNo);
        if (orderDO == null) {
            return null;
        }
        return InstControlOrderConverter.convert(orderDO);
    }

    @Override
    public int updateCommunicateStatusByIdAndPreStatus(InstControlOrder controlOrder,
                                                       CommunicateStatus targetStatus,
                                                       CommunicateStatus preStatus) {
        int modCount = controlOrderDAO.updateCommunicateStatusByIdAndPreStatus(
                controlOrder.getOrderId(), targetStatus.getCode(), preStatus.getCode());
        if (modCount > 0) {
            controlOrder.setCommunicateStatus(targetStatus);
        }
        return modCount;
    }

    /**
     * 更新控制订单状态
     *
     * @param controlOrder
     * @return
     */
    @Override
    public boolean updateInstControlOrderStatus(final InstControlOrder controlOrder, InstOrderStatus targetStatus) {
        // 不是最终状态,不更新
        if (InstOrderStatus.IN_PROCESS == targetStatus) {
            return false;
        }
        // 原订单是处理中,更新订单状态
        // 如果原订单不为处理中并且与需要更新的状态不一致,则异常
        if (InstOrderStatus.IN_PROCESS == controlOrder.getStatus()) {
            int rows = updateStatusById(controlOrder.getOrderId(), targetStatus, controlOrder.getStatus());
            Assert.isTrue(rows==1, "控制订单更新失败");
            controlOrder.setStatus(targetStatus);
            return true;
        }
        return false;
    }

    @Override
    public int updateExtensionByRequestNo(String extension, String requestNo) {

        return controlOrderDAO.updateExtensionByRequestNo(extension, requestNo);
    }

    @Override
    public List<Long> loadControlOrder4Query(FundChannelApiType apiType, Date startDate,
                                             Date endDate, int batchSize) {
        return controlOrderDAO.loadControlOrder4Query(apiType.getCode(), startDate, endDate,
                batchSize);
    }

    @Override
    public int updateRetryInfoById(int retryTimes, Date retryDateTime, long orderId) {
        return controlOrderDAO.updateRetryInfoById(retryTimes, retryDateTime, orderId);
    }

    @Override
    public int updateFlagWithOrderIdAndPreFlag(Long orderId, OrderFlag flag, OrderFlag preFlag) {
        return controlOrderDAO.updateFlagWithOrderIdAndPreFlag(orderId, flag.getCode(), preFlag.getCode());
    }
}
