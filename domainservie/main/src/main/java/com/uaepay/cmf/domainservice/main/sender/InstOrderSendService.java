package com.uaepay.cmf.domainservice.main.sender;

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.exception.WrongOrderResultException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade;
import com.uaepay.cmf.fss.ext.integration.util.InstCovertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>机构订单渠道发送</p>
 *
 * <AUTHOR>
 * @date InstChannelSendService.java v1.0  2020-09-09 15:52
 */
@Slf4j
@Service
public class InstOrderSendService extends AbstractChannelSendService<InstOrder, ChannelFundRequest, ChannelFundResult> {


    @Override
    protected ChannelFundResult send2Bank(ChannelFundRequest request, InstOrder order) {
        return this.applyFund(order, request);
    }


    private ChannelFundResult applyFund(InstOrder instOrder, ChannelFundRequest request) {
        ChannelFundResult result;
        if (this.isRouteAgent(instOrder.getPayMode(), instOrder.getApiType())) {
            ChannelFundFacade agent =
                    this.getSpecialAgent(instOrder.getPayMode(), instOrder.getApiType());
            result = agent.apply(InstCovertUtil.convert(request));
        } else {
            // 使用默认超时时间
            result = channelSenderFactory.applyFund(request, null);
            bankFormService.processInstResult(instOrder, result);
        }
        return result;
    }

    /**
     * 更新交互状态为发送中
     *
     * @param instOrder
     */
    @Override
    public void updateOrderCommunicateStatus2Send(InstOrder instOrder) {
        if (instOrder.getCommunicateStatus() == CommunicateStatus.IN_PROCESS) {
            throw new WrongOrderResultException(ErrorCode.WRONG_ORDER_DUPLICATE_PROCESS);
        }
        if (instOrder.getInstOrderId() == null){
            return ;
        }
        // 发送前更新发送状态 A -> I ,用于避免重复发送
        int count = instOrderRepository.updateCommunicateStatusWithPreStatus(instOrder,
                CommunicateStatus.IN_PROCESS, instOrder.getCommunicateStatus());

        // 校验更新记录数
        assertStatusIsUpdated(count);

    }


    @Override
    protected ChannelFundResult buildResp() {
        return new ChannelFundResult();
    }


}
