package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.common.util.money.Money;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;

import java.util.List;
import java.util.Map;

/**
 * <p>渠道余额接口</p>
 *
 * <AUTHOR>
 * @version $Id: ChannelBalanceService.java, v 0.1 2013-7-15 下午2:11:54 liumaoli Exp $
 */
public interface ChannelBalanceService extends BasicConstant {

    /**
     * 查询余额结果
     *
     * @param fundChannel
     * @return
     * @throws CommunicateException
     */
    ChannelFundResult queryBalanceResult(ChannelVO fundChannel);

    /**
     * 统计渠道待出款金额
     *
     * @param channelCode
     * @return
     */
    Money gatherAwaitingAmount(String channelCode);

    /**
     * 查询余额
     *
     * @param channel
     * @param extension
     * @return
     * @throws CommunicateException
     */
    ChannelResult queryBalance(ChannelVO channel, Map<String, String> extension);

    /**
     * 组装需要统计所有渠道的渠道编号
     *
     * @param channelCode
     * @return
     */
    List<String> combineFundChannel(String channelCode);

    /**
     * 余额校验报警
     *
     * @param balance            该渠道目前可用余额（人行托管户余额）
     * @param mappingBalance     映射给该渠道的可用余额
     * @param sumAmount          该渠道当前待出款总额
     * @param channelDescription 渠道名
     */
    void warn(Money balance, Money mappingBalance, Money sumAmount, String channelDescription);

    /**
     * 校验余额汇总,内部拼装相关联渠道
     *
     * @param fundChannelCode
     * @return
     */
    Boolean checkBalance(String fundChannelCode, Boolean needNotify);


    List<String> filterBalance(List<String> channelCodeList);

    /**
     * 查询余额结果
     *
     * @param fundChannel
     * @return
     * @throws CommunicateException
     */
    ChannelFundResult queryBalanceResult(ChannelVO fundChannel, Map<String, String> extension, String instOrderNo);

}
