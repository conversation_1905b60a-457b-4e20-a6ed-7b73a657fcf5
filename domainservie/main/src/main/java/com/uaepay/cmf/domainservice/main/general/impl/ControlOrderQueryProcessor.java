package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.exception.CmfBizException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.domainservice.main.convert.InstControlOrderConverter;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.service.facade.domain.query.PkQueryRequest;
import com.uaepay.cmf.service.facade.domain.query.order.ControlOrderVO;
import com.uaepay.cmf.service.facade.result.PkQueryResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ControlOrderQueryProcessor.java v1.0
 */
@Service
public class ControlOrderQueryProcessor extends GeneralProcessorTemplate<PkQueryRequest<String>, PkQueryResult<ControlOrderVO>> {

    @Resource
    private InstControlOrderRepository instControlOrderRepository;

    @Override
    protected String getServiceName() {
        return "ControlOrderQueryProcessor";
    }

    @Override
    protected PkQueryResult<ControlOrderVO> createResponse() {
        return new PkQueryResult<>();
    }

    @Override
    protected void process(PkQueryRequest<String> request, PkQueryResult<ControlOrderVO> result) {
        InstControlOrder controlOrder = instControlOrderRepository.loadByRequestNo(request.getPk());
        if (controlOrder == null) {
            throw new CmfBizException(ErrorCode.ORDER_NOT_FOUND);
        }
        result.setItem(InstControlOrderConverter.convert2VO(controlOrder));
        result.setApplyStatus(ApplyStatusEnum.SUCCESS);

    }
}
