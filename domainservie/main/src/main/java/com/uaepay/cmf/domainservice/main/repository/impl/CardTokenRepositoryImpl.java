package com.uaepay.cmf.domainservice.main.repository.impl;

import com.uaepay.cmf.common.core.dal.daointerface.CardTokenDAO;
import com.uaepay.cmf.common.core.dal.dataobject.CardTokenDO;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator;
import com.uaepay.cmf.common.core.engine.generator.SequenceNameEnum;
import com.uaepay.cmf.domainservice.main.convert.CardTokenConverter;
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenRepositoryImpl.java v1.0  2020-03-28 15:50
 */
@Slf4j
@Repository
public class CardTokenRepositoryImpl implements CardTokenRepository {

    @Resource
    private CardTokenDAO cardTokenDAO;

    @Resource
    private PrimaryKeyGenerator primaryKeyGenerator;

    @Override
    public String store(CardToken cardToken) {

        cardToken.setCardTokenId(primaryKeyGenerator.generateKey(SequenceNameEnum.CARD_TOKEN));
        return cardTokenDAO.insert(CardTokenConverter.convert(cardToken));
    }

    @Override
    public int update(CardToken cardToken) {
        return cardTokenDAO.updateByPrimaryKey(CardTokenConverter.convert(cardToken));
    }

    @Override
    public int updateInstInfo(Long instOrderId, String instTokenId, CardToken cardToken) {
        int rows = cardTokenDAO.updateInstInfo(instOrderId, instTokenId, cardToken.getCardTokenId());
        Assert.isTrue(rows==1, "Card token更新失败");
        cardToken.setInstOrderId(instOrderId);
        cardToken.setInstTokenId(instTokenId);
        return rows;
    }

    @Override
    public int updateSelective(CardToken cardToken) {
        return cardTokenDAO.updateSelective(CardTokenConverter.convert(cardToken));
    }

    @Override
    public CardToken query(String cardTokenId) {
        if (StringUtils.isEmpty(cardTokenId)) {
            return null;
        }
        return CardTokenConverter.convert(cardTokenDAO.selectByPrimaryKey(cardTokenId));
    }

    @Override
    public CardToken queryByInstOrderId(Long instOrderId) {
        if (instOrderId == null) {
            return null;
        }
        return CardTokenConverter.convert(cardTokenDAO.selectByInstOrderId(instOrderId));
    }

    @Override
    public CardToken queryByOrder(InstBaseOrder baseOrder) {
        CardTokenDO cardTokenDO = null;
        if(baseOrder instanceof InstOrder){
            cardTokenDO = cardTokenDAO.selectByInstOrderId(((InstOrder) baseOrder).getInstOrderId());
        }else if(baseOrder instanceof InstControlOrder){
            cardTokenDO = cardTokenDAO.selectByInstOrderId(((InstControlOrder) baseOrder).getOrderId());
        }
        return CardTokenConverter.convert(cardTokenDO);
    }

}
