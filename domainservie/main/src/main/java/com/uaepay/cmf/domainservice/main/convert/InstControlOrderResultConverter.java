package com.uaepay.cmf.domainservice.main.convert;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthResponse;
import com.uaepay.cmf.service.facade.domain.card.RetrieveCardMetadataResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import com.uaepay.biz.common.util.PropertyConverter;
import com.uaepay.cmf.common.core.dal.dataobject.ControlOrderResultDO;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.common.enums.FundChannelApiType;

/**
 * <p>机构控制订单结果转换器</p>
 *
 * <AUTHOR>
 * @version $Id: InstControlOrderResultConverter.java, v 0.1 2012-8-20 上午10:58:27 fuyangbiao Exp $
 */
@Slf4j
public class InstControlOrderResultConverter {

    /**
     * DO转换为DOMAIN
     *
     * @param resultDO
     * @return
     */
    public static InstControlOrderResult convert(ControlOrderResultDO resultDO) {
        if (resultDO == null) {
            return null;
        }
        InstControlOrderResult result = new InstControlOrderResult();
        BeanUtils.copyProperties(resultDO, result);
        result.setOrderId(resultDO.getControlOrderId());
        result.setApiResultSubCode(resultDO.getApiSubResultCode());
        // 扩展字段格式统一
        try {
            result.setExtension(CommonConverter.convertFromDb(resultDO.getExtension()));
        } catch (Exception e) {
            result.setExtension(PropertyConverter.convertToMap(resultDO.getExtension()));
        }
        result.setApiType(FundChannelApiType.getByCode(resultDO.getApiType()));
        result.setStatus(InstOrderResultStatus.getByCode(resultDO.getStatus()));
        return result;
    }

    /**
     * DOMAIN转换为DO
     *
     * @param result
     * @return
     */
    public static ControlOrderResultDO convert(InstControlOrderResult result) {
        if (result == null) {
            return null;
        }
        ControlOrderResultDO resultDO = new ControlOrderResultDO();
        BeanUtils.copyProperties(result, resultDO);
        resultDO.setControlOrderId(result.getOrderId());
        resultDO.setApiSubResultCode(result.getApiResultSubCode());
        if (result.getExtension() != null) {
            Map<String, String> extMap = new HashMap<>(result.getExtension());
            // 去除PAGE_URL大字段
            extMap.remove(ExtensionKey.PAGE_URL.name());
            extMap.remove(ExtensionKey.APPLE_WEB_SIGNATURE.getKey());
            // 扩展字段格式统一
            try {
                resultDO.setExtension(CommonConverter.convertToDb(extMap));
            } catch (Exception e) {
                resultDO.setExtension(PropertyConverter.convertFromMap(extMap));
            }
        }
        if (result.getApiType() != null) {
            resultDO.setApiType(result.getApiType().getCode());
        }
        if (result.getStatus() != null) {
            resultDO.setStatus(result.getStatus().getCode());
        }

        return resultDO;
    }

    public static List<InstControlOrderResult> convertList(List<ControlOrderResultDO> resultDOList) {
        if (CollectionUtils.isEmpty(resultDOList)) {
            return null;
        }
        List<InstControlOrderResult> instControlOrderResultList = new ArrayList<>(
                resultDOList.size());
        for (ControlOrderResultDO controlResult : resultDOList) {
            instControlOrderResultList.add(convert(controlResult));
        }
        return instControlOrderResultList;
    }

    public static void convert(InstControlOrderResult resp, CmfAuthResponse authResponse) {
        BeanUtils.copyProperties(resp, authResponse);
        authResponse.setUnityResultCode(resp.getInstResultCode());
        authResponse.setMessage(resp.getResultMessage());
        authResponse.setDataMap(resp.getExtension());
        authResponse.setApplyStatus(resp.getStatus() == InstOrderResultStatus.FAILURE ? ApplyStatusEnum.FAIL : ApplyStatusEnum.SUCCESS);
    }

    public static void convert(InstControlOrderResult resp, RetrieveCardMetadataResponse retrieveCardMetadataResponse) {
        retrieveCardMetadataResponse.setUnityResultCode(resp.getInstResultCode());
        if(resp.getStatus() != InstOrderResultStatus.SUCCESSFUL) {
            retrieveCardMetadataResponse.setApplyStatus(ApplyStatusEnum.FAIL);
            retrieveCardMetadataResponse.setMessage(resp.getResultMessage());
            return;

        }

        retrieveCardMetadataResponse.setApplyStatus(ApplyStatusEnum.SUCCESS);
        try {
            String cardMetadataJson = resp.getExtension().get(ExtensionKey.CARD_METADATA.getKey());
            
            // Use JsonUtil to parse the JSON string
            Map<String, Object> cardMetadataMap = JsonUtil.parseObject(cardMetadataJson, Map.class);
            
            // Set basic fields
            retrieveCardMetadataResponse.setBin((String) cardMetadataMap.get("bin"));
            retrieveCardMetadataResponse.setScheme((String) cardMetadataMap.get("scheme"));
            retrieveCardMetadataResponse.setCardType((String) cardMetadataMap.get("card_type"));
            retrieveCardMetadataResponse.setCardCategory((String) cardMetadataMap.get("card_category"));
            retrieveCardMetadataResponse.setCurrency((String) cardMetadataMap.get("currency"));
            retrieveCardMetadataResponse.setIssuer((String) cardMetadataMap.get("issuer"));
            retrieveCardMetadataResponse.setIssuerCountry((String) cardMetadataMap.get("issuer_country"));
            retrieveCardMetadataResponse.setIssuerCountryName((String) cardMetadataMap.get("issuer_country_name"));
            retrieveCardMetadataResponse.setProductId((String) cardMetadataMap.get("product_id"));
            retrieveCardMetadataResponse.setProductType((String) cardMetadataMap.get("product_type"));
            
            // Handle card_payouts nested object
            if (cardMetadataMap.containsKey("card_payouts")) {
                Map<String, String> payouts = (Map<String, String>) cardMetadataMap.get("card_payouts");
                retrieveCardMetadataResponse.setDomesticNonMoneyTransfer(payouts.get("domestic_non_money_transfer"));
                retrieveCardMetadataResponse.setCrossBorderNonMoneyTransfer(payouts.get("cross_border_non_money_transfer"));
                retrieveCardMetadataResponse.setDomesticGambling(payouts.get("domestic_gambling"));
                retrieveCardMetadataResponse.setCrossBorderGambling(payouts.get("cross_border_gambling"));
                retrieveCardMetadataResponse.setDomesticMoneyTransfer(payouts.get("domestic_money_transfer"));
                retrieveCardMetadataResponse.setCrossBorderMoneyTransfer(payouts.get("cross_border_money_transfer"));
            }
            
        } catch (Exception e) {
            log.warn("Failed to parse card metadata", e);
            retrieveCardMetadataResponse.setApplyStatus(ApplyStatusEnum.FAIL);
            retrieveCardMetadataResponse.setMessage("Failed to parse card metadata");
        }
    }
}
