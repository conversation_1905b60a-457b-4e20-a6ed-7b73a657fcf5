package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderType;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.domainservice.main.convert.ChannelResultConverter;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.schema.cmf.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>推进3ds2.0结果处理</p>
 *
 * <AUTHOR>
 * @date 2022/6/29
 */
@Slf4j
@Component
public class Control3Ds2ResultLaterProcessor implements ResultLaterProcessor {
    @Resource
    private InstResultProcessor processor;

    @Resource
    private ControlResultProcessor controlResultProcessor;

    @Resource
    protected InstOrderRepository instOrderRepository;

    @Resource
    protected InstControlOrderRepository controlOrderRepository;

    @PostConstruct
    public void init() {
        ResultLaterProcessorEnum.ADVANCE_3DS2.register(this);
    }

    @Override
    public Result<?> process(InstControlOrder instControlOrder, InstControlOrderResult instControlResult) {
        log.info("3ds结果处理-控制单:{},控制单结果:{}",instControlOrder,instControlResult);
        if(Objects.isNull(instControlOrder.getStatus()) || !instControlOrder.getStatus().isFinalStatus()){
            return Result.ofNothing();
        }

        InstOrderType orderType = InstOrderType.getByName(instControlOrder.getExtension().get(ExtensionKey.INST_ORDER_TYPE.getKey()));
        if(orderType==InstOrderType.CONTROL){
            proceedControl4AD(instControlOrder, instControlResult);
        }else {
            proceedInst4AD(instControlOrder, instControlResult);
        }

        return Result.ofSuccess();
    }

    private void proceedInst4AD(InstControlOrder instControlOrder, InstControlOrderResult instControlResult) {
        log.info("3ds结果处理机构单");
        InstOrder instOrder = instOrderRepository.loadByNo(instControlOrder.getPreInstOrderNo());
        Assert.isTrue(instOrder.getStatus() == InstOrderStatus.IN_PROCESS, "机构单交易状态不为处理中");
        InstOrderResult instResult = ChannelResultConverter.convert(instControlResult, instOrder);
        Assert.isTrue(instResult.getStatus() == instControlResult.getStatus(), "机构状态与推进状态应该一致");
        processor.process(instOrder, instResult);
    }

    private void proceedControl4AD(InstControlOrder order, InstControlOrderResult instControlResult) {
        log.info("3ds结果处理控制单");
        InstControlOrder preControlOrder = controlOrderRepository.loadByNo(order.getPreInstOrderNo());
        Assert.isTrue(preControlOrder.getStatus() == InstOrderStatus.IN_PROCESS, "控制单交易状态不为处理中");
        InstControlOrderResult preControlResult = ChannelResultConverter.convert(instControlResult, preControlOrder);
        Assert.isTrue(preControlResult.getStatus() == instControlResult.getStatus(), "控制单状态与推进状态应该一致");
        controlResultProcessor.process(preControlOrder, preControlResult);
    }
}
