package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.domain.base.BankCardInfo;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.fss.ext.common.domain.authenticate.AuthenticateRequest;
import com.uaepay.schema.cmf.enums.CardType;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date AuthApiRequestBuilder.java v1.0  2020-09-09 17:58
 */
@Service
public class AuthApiRequestBuilder extends AbstractApiRequestBuilder<InstControlOrder, AuthenticateRequest> {

    @Override
    protected void buildCustomParam(InstControlOrder order, AuthenticateRequest request) {
        BeanUtils.copyProperties(order, request);

        BankCardInfo cardInfo = new BankCardInfo();

        Map<String, String> extMap = order.getExtension();
        cardInfo.setCardNo(extMap.get(ExtensionKey.CARD_NO.key));
        cardInfo.setExpiredDate(extMap.get(ExtensionKey.EXPIRED_DATE.key));
        cardInfo.setCardType(CardType.getByCode(extMap.get(
                ExtensionKey.CARD_TYPE.key)));
        cardInfo.setName(extMap.get(ExtensionKey.ACCOUNT_NAME.key));
        request.setBankCardInfo(cardInfo);

        request.setMobilePhoneNo(extMap.get(ExtensionKey.MOBILENO.key));
        request.setExtension(extMap);

        putInstOrderToken(order);
    }

    @Override
    public AuthenticateRequest buildReq() {
        return new AuthenticateRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(FundChannelApiType.AUTH);
    }
}
