package com.uaepay.cmf.domainservice.main.process.impl;

import com.alibaba.fastjson.JSON;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.*;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.enums.EciStatusEnum;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.router.ChannelService;
import com.uaepay.cmf.domainservice.main.process.NotifyCashdeskService;
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository;
import com.uaepay.cmf.domainservice.main.repository.Notify3dsResultRepository;
import com.uaepay.cmf.fss.ext.integration.cashdesk.CashdeskClient;
import com.uaepay.cmf.fss.ext.integration.ues.UesClient;
import com.uaepay.cmf.fss.ext.integration.util.OrderUtil;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import com.uaepay.cmf.service.facade.result.BindCardRequest;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.YesNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date NotifyCashdeskServiceImpl.java v1.0  2020-03-26 15:30
 */
@Slf4j
@Service("notifyCashdeskService")
public class NotifyCashdeskServiceImpl implements NotifyCashdeskService {

    @Resource
    private CashdeskClient cashdeskClient;
    @Resource
    private CardTokenRepository cardTokenRepository;
    @Resource
    private Notify3dsResultRepository resultRepository;
    @Resource
    private UesClient uesClient;
    @Resource
    private ChannelService channelService;

    private static final String CATEGORY_TOKEN = "TOKEN";
    private static final String CATEGORY_QPAY = "COMMON";
    private static final String CATEGORY_BALANCE = "IBAN";

    private static final String ECI_STATUS = "eciStatus";
    private static final String ECI_STATUS_DOWNGRADE = "D";

    @Override
    public void notifyBindCard(InstOrder instOrder, InstOrderResult instResult) {
        if (instOrder.getStatus() != InstOrderStatus.SUCCESSFUL) {
            return;
        }
        CardToken cardToken = cardTokenRepository.queryByInstOrderId(instOrder.getInstOrderId());
        // 首次绑卡 或 发生签约 均通知收银台
        if (cardToken == null
                || (cardToken.getFirstBind() != YesNo.YES && StringUtils.isEmpty(instResult.getExtension().get(SIGN_TRANSACTION_ID)))) {
            log.info("cardToken.needNotNotify!");
            return;
        }
        BindCardRequest request = this.convert2BindCardRequest(instOrder, cardToken, instResult);
        cashdeskClient.sendBindCardRequest(request);
    }

    @Override
    public void notifyBindCard(InstControlOrder controlOrder, InstControlOrderResult result) {
        if (controlOrder.getStatus() != InstOrderStatus.SUCCESSFUL || controlOrder.getApiType() != FundChannelApiType.AUTH) {
            return;
        }
        CardToken cardToken = cardTokenRepository.queryByInstOrderId(controlOrder.getOrderId());
        if (cardToken == null || cardToken.getFirstBind() != YesNo.YES ) {
            log.info("cardToken.needNotNotify!");
            return;
        }
        BindCardRequest request = this.convert2BindCardRequest(controlOrder, cardToken, result);
        cashdeskClient.sendBindCardRequest(request);
    }

    private BindCardRequest convert2BindCardRequest(InstBaseOrder instOrder, CardToken cardToken, InstBaseResult instResult) {
        BindCardRequest request = new BindCardRequest();
        BeanUtils.copyProperties(cardToken, request);
        request.setChannelCode(instOrder.getFundChannelCode());
        request.setInstOrderNo(instOrder.getInstOrderNo());
        request.setPaymentOrderNo(instOrder.getExtension().get(ExtensionKey.PAYMENT_ORDER_NO.key));
        if (StringUtils.isNotEmpty(cardToken.getCardHolder())) {
            request.setCardHolder(uesDecrypt(cardToken.getCardHolder()));
        }
        if (StringUtils.isNotEmpty(cardToken.getCardNo())) {
            request.setCardNo(uesDecrypt(cardToken.getCardNo()));
        }
        if (StringUtils.isNotEmpty(cardToken.getIban())) {
            request.setIban(uesDecrypt(cardToken.getIban()));
        }
        if (StringUtils.isNotEmpty(cardToken.getCardAccountNo())) {
            request.setCardAccountNo(uesDecrypt(cardToken.getCardAccountNo()));
        }
        if (StringUtils.isNotEmpty(cardToken.getCardExpired())) {
            request.setExpiredDate(uesDecrypt(cardToken.getCardExpired()));
        }
        PayMode payMode = instOrder.getPayMode();
        String category = null;
        if (payMode == PayMode.TOKENPAY) {
            category = CATEGORY_TOKEN;
        } else if (payMode == PayMode.QUICKPAY) {
            category = CATEGORY_QPAY;
        } else if (payMode == PayMode.BALANCE) {
            category = CATEGORY_BALANCE;
        } else {
            throw new IllegalArgumentException("不支持该类型!");
        }
        request.setCardCategory(category);
        if (StringUtils.isEmpty(request.getCardType())) {
            request.setCardType(cardToken.getDbcr());
        }
        String eciStatus = getEciStatus(instOrder, instResult);
        // 若3ds2渠道降级，则转为N，以便cashdesk有不同处理
        request.setEciStatus(StringUtils.equals(eciStatus, ECI_STATUS_DOWNGRADE) && channelService.is3ds2Channel(instOrder.getFundChannelCode()) ? EciStatusEnum.NO_SHOW.getStatus() : eciStatus);
        if (StringUtils.isNotEmpty(cardToken.getExtension())){
            String clientId = MapUtil.jsonToMap(cardToken.getExtension()).get("clientId");
            request.setClientId(clientId);
        }
        // 补充渠道扩展字段
        Map<String, String> extension = new HashMap<>();
        if (StringUtils.isNotBlank(request.getExtension()) && JSON.isValidObject(request.getExtension())){
            extension = MapUtil.jsonToMap(request.getExtension());
        }
        extension.putAll(instResult.getExtension());
        request.setExtension(MapUtil.mapToJson(extension));
        return request;
    }

    private String uesDecrypt(String data) {
        if (!OrderUtil.isEncryptedData(data)) {
            return data;
        }
        return uesClient.getDataByTicket(data);
    }

    private String getEciStatus(InstBaseOrder instOrder, InstBaseResult instResult) {
        // 机构结果返回eciStatus不为成功不通知
        if (instResult != null && instResult.getExtension().containsKey(ECI_STATUS)) {
            log.info("getEciStatus.value:{}", instResult.getExtension().get(ECI_STATUS));
            return instResult.getExtension().get(ECI_STATUS);
        }
        Notify3dsResult notifyResult = resultRepository.queryBy3dsResult(instOrder.getInstOrderNo());
        if (notifyResult == null) {
            return EMPTY_STRING;
        }
        EciStatusEnum eciStatus = EciStatusEnum.getByCode(notifyResult.getEci());

        return eciStatus != null ? eciStatus.getStatus() : EMPTY_STRING;
    }

}

