package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.cmf.common.enums.FundChannelApiType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
@Slf4j
@Getter
@AllArgsConstructor
public enum ResultLaterProcessorEnum {

    ADVANCE_3DS2("AD", "推进3ds2.0"),
    VOID_TRANSACTION("VT", "交易撤销"),
    ADVANCE("DBA", "推进"),
    PREAUTH_UPDATE("PAU","预授权权更新"),
    PREAUTH_COMPLETE("PAC","预授权权完成"),
    PREAUTH_VOID("PAV","预授权撤销"),
    CONTROL_VOID_TRANSACTION("CV", "控制类交易撤销")
    ;

    /**
     * 代码
     */
    private final String code;
    /**
     * 信息
     */
    private final String message;



    public static Optional<ResultLaterProcessor> getResultLaterProcessor(FundChannelApiType fundChannelApiType) {

        if(Objects.isNull(fundChannelApiType)){
            return Optional.empty();
        }

        for(ResultLaterProcessorEnum resultLaterProcessorEnum : ResultLaterProcessorEnum.values()){
            if(Objects.equals(resultLaterProcessorEnum.getCode(), fundChannelApiType.getCode())){
                ResultLaterProcessor resultLaterProcessor = apiTypeResultProcessorMap.get(resultLaterProcessorEnum);
                log.info("结果处理器:{}",resultLaterProcessorEnum.name());
                return Optional.ofNullable(resultLaterProcessor);
            }
        }



        return Optional.empty();

    }

    public void register(ResultLaterProcessor resultLaterProcessor) {
        apiTypeResultProcessorMap.put(this, resultLaterProcessor);
    }

    private static final Map<ResultLaterProcessorEnum, ResultLaterProcessor> apiTypeResultProcessorMap = new HashMap<>();

}
