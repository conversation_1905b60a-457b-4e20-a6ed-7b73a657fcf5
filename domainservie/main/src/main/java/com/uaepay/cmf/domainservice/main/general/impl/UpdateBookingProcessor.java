package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.exception.CmfBizException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.util.validate.Validate;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.service.facade.domain.fundout.UpdateBookingTimeRequest;
import com.uaepay.schema.cmf.enums.BizType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date UpdateBookingProcessor.java v1.0
 */
@Service
public class UpdateBookingProcessor extends GeneralProcessorTemplate<UpdateBookingTimeRequest, CommonResponse> {

    @Resource
    private InstOrderRepository instOrderRepository;

    @Override
    protected String getServiceName() {
        return "UpdateBookingProcessor";
    }

    @Override
    protected CommonResponse createResponse() {
        return CommonResponse.buildSuccess();
    }

    @Override
    protected void businessValidate(UpdateBookingTimeRequest request) {
        //
        InstOrder instOrder = instOrderRepository.loadByNo(request.getInstOrderNo());
        Validate.assertTrue("未找到机构订单", instOrder != null);
        Validate.assertTrue("不为出款订单", instOrder.getBizType() == BizType.FUNDOUT);
        Validate.assertTrue("订单已发送银行", instOrder.getCommunicateStatus()== CommunicateStatus.AWAITING&& instOrder.getStatus()== InstOrderStatus.IN_PROCESS);
        Validate.assertTrue("已超过发送时间", instOrder.getGmtBookingSubmit().after(new Date()));
    }

    @Override
    protected void process(UpdateBookingTimeRequest request, CommonResponse response) throws CmfBizException {
        InstOrder instOrder = instOrderRepository.loadByNo(request.getInstOrderNo());
        instOrderRepository.updateBookingSubmit(instOrder, request.getGmtBookingSubmit());
    }
}
