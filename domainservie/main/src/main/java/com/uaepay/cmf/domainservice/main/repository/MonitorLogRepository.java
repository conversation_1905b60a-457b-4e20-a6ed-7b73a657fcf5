package com.uaepay.cmf.domainservice.main.repository;

import java.util.List;

import com.uaepay.cmf.common.enums.MonitorLogStatus;
import com.uaepay.cmf.common.monitor.MonitorLog;

/**
 *
 * <p>监控日志仓储</p>
 *
 * <AUTHOR> won
 * @version $Id: MonitorLogRepository.java, v 0.1 2011-7-20 上午10:30:15 sean won Exp $
 */
public interface MonitorLogRepository {

    /**
     * 根据ID加载领域对象
     * @param logId
     * @param isLock
     * @return
     */
    MonitorLog load(Long logId);

    /**
     * 保存
     * @param log
     * @return id
     */
    Long store(MonitorLog log);

    /**
     * 根据状态加载,锁记录
     *
     * @param rownum
     * @param stauts
     * @return
     */
    List<MonitorLog> loadByStatus(Integer rownum, MonitorLogStatus stauts);

    /**
     * 更新状态.
     *
     * @param logIdlist
     * @param newStatus
     * @param oldStatus
     * @param memo
     * @return
     */
    int updateStatusWithPreStatus(List<String> logIdlist, MonitorLogStatus newStatus, MonitorLogStatus oldStatus, String memo);
}
