package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.service.facade.domain.cache.QueryAccountBalanceCacheRequest;
import com.uaepay.cmf.service.facade.domain.cache.QueryAccountBalanceCacheResponse;
import com.uaepay.common.util.money.Money;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 02/09/2024 15:06
 */
@Slf4j
@Component
public class QueryAccountBalanceCacheProcessor extends GeneralProcessorTemplate<QueryAccountBalanceCacheRequest, QueryAccountBalanceCacheResponse> {

    @Resource(name = "memoryCacheClient")
    private CacheClient cacheClient;

    @Override
    protected String getServiceName() {
        return "QUERY_ACCOUNT_BALANCE";
    }

    @Override
    protected QueryAccountBalanceCacheResponse createResponse() {
        return new QueryAccountBalanceCacheResponse();
    }

    @Override
    protected void process(QueryAccountBalanceCacheRequest request, QueryAccountBalanceCacheResponse queryAccountBalanceCacheResponse) {
        Object o = cacheClient.get(CacheType.ACCOUNT_BALANCE, request.getAccountNo());
        if (o == null) {
            return;
        }
        Map<String, Money> result = (Map<String, Money>) o;
        queryAccountBalanceCacheResponse.setThreshold(result.get(THRESHOLD));
        queryAccountBalanceCacheResponse.setBalance(result.get(ACCOUNT_BALANCE));
        queryAccountBalanceCacheResponse.setApplyStatus(ApplyStatusEnum.SUCCESS);
    }
}
