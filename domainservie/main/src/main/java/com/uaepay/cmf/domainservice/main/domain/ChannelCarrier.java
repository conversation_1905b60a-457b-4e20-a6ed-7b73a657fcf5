package com.uaepay.cmf.domainservice.main.domain;

import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.router.service.facade.domain.order.OrderInfo;
import lombok.Builder;
import lombok.Data;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ChannelCarrier.java v1.0  2020-09-12 16:27
 */
@Data
@Builder
public class ChannelCarrier implements AutoCloseable {

    private ChannelVO channel;

    private OrderInfo orderInfo;

    @Override
    public void close() throws Exception {
        // 清除渠道线程变量
        ChannelHolder.clear();
    }
}
