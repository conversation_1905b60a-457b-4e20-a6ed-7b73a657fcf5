package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstResultOperateStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.Map;

/**
 * <p>渠道结果转换类</p>
 *
 * <AUTHOR> Liu
 * @version $Id: ChannelResultConverter.java, v 0.1 2012-8-14 下午6:16:58 liumaoli Exp $
 */
public class ChannelResultConverter {

    /**
     * 渠道结果转换
     *
     * @param src
     * @return
     */
    public static InstOrderResult convert(InstOrder instOrder, ChannelFundResult src) {
        if (src == null) {
            return null;
        }
        InstOrderResult result = convert(src);
        result.setInstOrderId(instOrder.getInstOrderId());
        result.setInstOrderNo(instOrder.getInstOrderNo());
        result.setFundChannelCode(instOrder.getFundChannelCode());
        result.setBizType(instOrder.getBizType());
        return result;
    }

    /**
     * 依据控制类结果构造机构订单结果
     *
     * @param src
     * @return
     */
    public static InstOrderResult convert(InstControlOrderResult src, InstOrder instOrder) {
        if (src == null) {
            return null;
        }
        InstOrderResult result = new InstOrderResult();
        result.setBizType(instOrder.getBizType());
        result.setOperateStatus(InstResultOperateStatus.AWAITING);
        result.setApiType(src.getApiType());
        result.setFundChannelCode(instOrder.getFundChannelCode());

        result.setInstSeqNo(src.getExtension().get(ExtensionKey.INST_SEQ_NO.key));
        if(src.getAmount()==null && src.getApiType() == FundChannelApiType.VOID_TRANSACTION){
            result.setRealAmount(instOrder.getAmount());
        }else {
            result.setRealAmount(src.getAmount());
        }
        result.setInstOrderId(instOrder.getInstOrderId());
        result.setInstOrderNo(instOrder.getInstOrderNo());
        result.setProcessStatus(src.getProcessStatus());
        // 控制订单交易撤销成功，机构订单则失败
        if (src.getApiType() == FundChannelApiType.VOID_TRANSACTION||src.getApiType()==FundChannelApiType.PREAUTH_VOID) {
            if (src.getStatus() == InstOrderResultStatus.SUCCESSFUL) {
                result.setStatus(InstOrderResultStatus.FAILURE);
            } else {
                result.setStatus(InstOrderResultStatus.IN_PROCESS);
            }
        } else {
            result.setStatus(src.getStatus());
        }


        //修改原订单结果返回码以及备注信息
        result.setInstResultCode(src.getInstResultCode());
        //保存扩展信息
        result.setExtension(src.getExtension());
        return result;
    }

    /**
     * 渠道结果转换,用于批量转换
     *
     * @param src
     * @return
     */
    public static InstOrderResult convert(ChannelFundResult src) {
        if (src == null) {
            return null;
        }
        InstOrderResult result = new InstOrderResult();
        BeanUtils.copyProperties(src, result);
        result.setInstSeqNo(src.getInstReturnOrderNo()); //银行生成的流水号
        result.setOperateStatus(InstResultOperateStatus.AWAITING);
        result.setInstResultCode(src.getResultCode());
        result.setApiResultCode(src.getApiResultCode());
        result.setApiResultSubCode(src.getApiResultSubCode());
        result.setApiType(src.getApiType());

        Date channelProcessTime = new Date();
        if (src.getInstSettleTime() != null) {
            channelProcessTime = src.getInstSettleTime();
        } else if (src.getProcessTime() != null) {
            channelProcessTime = src.getProcessTime();
        }
        result.getExtension().put(ExtensionKey.CHANNEL_TRANS_TIME.key,
                DateUtil.format(channelProcessTime, DateUtil.shortFormat));

        if (src.getRealAmount() != null) {
            result.setRealAmount(src.getRealAmount());
        }

        //渠道接口变更 20130221
        Map<String, String> extMap = MapUtil.jsonToMap(src.getExtension());
        result.getExtension().putAll(extMap);
        result.getExtension().put(ExtensionKey.RESULT_SUB_MESSAGE.getKey(), src.getApiResultSubMessage());
        String msg = StringUtils.isNotEmpty(src.getApiResultMessage()) ? src.getApiResultMessage() : src.getResultMessage();
        if (StringUtils.isNotBlank(msg) && msg.length() > 96) {
            msg = StringUtils.substring(msg, 0, 96);
        }
        result.setMemo(msg);

        return result;
    }


    /**
     * 批量结果转换，仅用于获取状态
     *
     * @param channelBatchResult
     * @param instBatchOrder
     * @return
     */
    public static InstOrderResult convert(ChannelFundBatchResult channelBatchResult, InstBatchOrder instBatchOrder) {
        if (channelBatchResult == null || instBatchOrder == null) {
            return null;
        }
        InstOrderResult instOrderResult = new InstOrderResult();
        instOrderResult.setInstOrderNo(channelBatchResult.getInstOrderNo());
        instOrderResult.setInstSeqNo(channelBatchResult.getInstReturnOrderNo()); //银行生成的流水号
        instOrderResult.setOperateStatus(InstResultOperateStatus.AWAITING);
        instOrderResult.setInstResultCode(channelBatchResult.getResultCode());
        instOrderResult.setApiResultCode(channelBatchResult.getApiResultCode());
        instOrderResult.setApiResultSubCode(channelBatchResult.getApiResultSubCode());
        instOrderResult.setApiType(channelBatchResult.getApiType());
        instOrderResult.setFundChannelCode(instBatchOrder.getFundChannelCode());

        Date channelProcessTime = new Date();
        if (channelBatchResult.getInstSettleTime() != null) {
            channelProcessTime = channelBatchResult.getInstSettleTime();
        } else if (channelBatchResult.getProcessTime() != null) {
            channelProcessTime = channelBatchResult.getProcessTime();
        }
        instOrderResult.getExtension().put(ExtensionKey.CHANNEL_TRANS_TIME.key,
                DateUtil.format(channelProcessTime, DateUtil.shortFormat));

        instOrderResult.setRealAmount(channelBatchResult.getRealAmount());
        instOrderResult.setInstOrderId(instBatchOrder.getArchiveBatchId());
        Map<String, String> extMap = MapUtil.jsonToMap(channelBatchResult.getExtension());
        instOrderResult.getExtension().putAll(extMap);
        String msg = channelBatchResult.getResultMessage();
        if (StringUtils.isNotBlank(msg) && msg.length() > 128) {
            msg = StringUtils.substring(channelBatchResult.getResultMessage(), 0, 96);
        }
        instOrderResult.setMemo(msg);

        return instOrderResult;
    }


    public static InstControlOrderResult convert(InstControlOrderResult src, InstControlOrder controlOrder) {
        if (src == null) {
            return null;
        }
        InstControlOrderResult result = new InstControlOrderResult();
        result.setApiType(src.getApiType());

        result.setAmount(controlOrder.getAmount());
        result.setOrderId(controlOrder.getOrderId());
        result.setInstOrderNo(controlOrder.getInstOrderNo());
        result.setProcessStatus(src.getProcessStatus());
        // 控制订单交易撤销成功，机构订单则失败
        result.setStatus(src.getStatus());

        //修改原订单结果返回码以及备注信息
        result.setInstResultCode(src.getInstResultCode());
        //保存扩展信息
        result.setExtension(src.getExtension());
        return result;
    }
}
