package com.uaepay.cmf.domainservice.main.repository;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.CmfRequest;
import com.uaepay.cmf.common.core.domain.PaymentNotifyLog;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderConfirmStatus;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.NotifyStatus;
import com.uaepay.cmf.common.core.domain.exception.DuplicateKeyException;
import com.uaepay.cmf.service.facade.domain.query.OrderNoQueryRequest;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * CMF订单仓储
 * </p>
 *
 * <AUTHOR>
 * @version $Id: CmfOrderRepository.java, v 0.1 2012-8-2 下午2:45:57 fuyangbiao
 * Exp $
 */
public interface CmfOrderRepository {

    /**
     * 保存cmf订单数据
     *
     * @param order
     * @return
     */
    String store(CmfOrder order) throws DuplicateKeyException;

    /**
     * 通过支付流水号加载
     *
     * @param paymentSeqNo
     * @return
     */
    CmfOrder loadByPaymentSeqNo(String paymentSeqNo, String settlementId);

    /**
     * 通过流水号加载
     *
     * @param cmfSeqNo
     * @param isLock   是否加锁
     * @return
     */
    CmfOrder loadByCmfSeqNo(String cmfSeqNo, boolean isLock);

    /**
     * 更新cmf订单状态
     *
     * @param cmfOrder
     * @param preStatus
     * @return
     */
    Boolean updateCmfOrderStatus(final CmfOrder cmfOrder, final CmfOrderStatus preStatus);

    /**
     * 更新支付结果通知状态
     *
     * @param paymentNotifyStatus
     * @param cmfSeqNo
     * @return
     */
    int updatePaymentNotifyStatusById(NotifyStatus paymentNotifyStatus, String cmfSeqNo);

    /**
     * 根据支付流水判断是否允许存储CMF订单.
     *
     * @param request
     * @return
     */
    boolean canStore(CmfRequest request, StringBuilder logAccum, Long startMillis) throws DuplicateKeyException;

    /**
     * 更新审核状态
     *
     * @param confirmStatus
     * @param cmfSeqNo
     * @return
     */
    int updateConfirmStatusById(CmfOrderConfirmStatus confirmStatus, String cmfSeqNo);

    int updateStatusAndConfirmStatusById(CmfOrderStatus cmfOrderStatus, CmfOrderStatus preStatus, CmfOrderConfirmStatus confirmStatus, String cmfSeqNo);

    /**
     * 保存通知.
     *
     * @param log
     * @return id
     */
    Long storeNotifyLog(final PaymentNotifyLog log);

    /**
     * 通过原始settlementId和时间加载cmf订单列表
     */
    List<CmfOrder> loadByOrgiSettlementId(String orgiSettlementId, Date dateStart, List<Long> ignoreInstOrderIds);

    /**
     * 根据支付订单号查询交易流水
     * @param request
     */
    CmfOrder loadByPaymentOrderNo(OrderNoQueryRequest request);
}
