package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.service.facade.domain.CmfCommonResultCode;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import org.apache.commons.lang3.StringUtils;
import com.uaepay.schema.cmf.enums.BizType;

/**
 * <p>CMF结果转换器</p>
 * <AUTHOR>
 * @version $Id: CmfResultConverter.java, v 0.1 2012-8-17 下午5:27:04 fuyangbiao Exp $
 */
public class CmfResultConverter implements BasicConstant {

    /**
     * 根据控制订单转换为结果
     * @param order
     * @param orderResult
     * @return
     */
    public static CmfControlResult convert(InstControlOrder order,
                                           InstControlOrderResult orderResult) {
        CmfControlResult result = new CmfControlResult();
        result.setFundsChannel(order.getFundChannelCode());
        result.setRequestNo(order.getRequestNo());
        result.setInstOrderNo(order.getInstOrderNo());
        result.setInstResultCode(orderResult.getInstResultCode());
        result.setReturnMessage(orderResult.getResultMessage());
        result.setExtension(CommonConverter.convertExtension(orderResult.getExtension()));
        result.setResultCode(convertResultCode(order.getStatus()));
        return result;
    }

    public static CmfCommonResultCode convertResultCode(InstOrderStatus instOrderStatus) {
        if (instOrderStatus == null) {
            return null;
        }
        switch (instOrderStatus) {
            case SUCCESSFUL:
                return CmfCommonResultCode.SUCCESS;
            case FAILURE:
                return CmfCommonResultCode.FAILED;
            default:
                return CmfCommonResultCode.IN_PROCESS;
        }
    }

    /**
     * 根据结果码和信息组装
     * @param resultCode
     * @param resultMessage
     * @return
     */
    public static CmfControlResult convert(CmfCommonResultCode resultCode, String resultMessage) {
        CmfControlResult result = new CmfControlResult();
        if (resultCode == null) {
            resultCode = CmfCommonResultCode.UNKNOW_EXCEPTION;
        }
        result.setResultCode(resultCode);
        result.setReturnMessage(resultMessage);

        return result;
    }

    /**
     * 处理失败的结果.
     *
     * @param failure
     * @param bizType
     * @param e
     * @return
     */
    public static CmfFundResult fail(InstOrderResult instResult, InstOrderProcessStatus failure,
                                     BizType bizType, Throwable e) {
        CmfFundResult result = new CmfFundResult();
        result.setSuccess(false);
        CmfFundResultCode resultCode = (instResult != null && InstOrderResultStatus.FAILURE
            .equals(instResult.getStatus())) ? CmfFundResultCode.FAILED
            : CmfFundResultCode.IN_PROCESS;
        result.setResultCode(resultCode);
        if (isLegalMessage(e)) {
            result.setResultMessage(e.getMessage());
        } else {
            result.setResultMessage(SYSTEM_BUSY);
        }
        return result;
    }

    private static boolean isLegalMessage(Throwable e) {
        return e != null && StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().length() < 32;
    }

    /**
     * 创建一个请求处理结果.
     *
     * @param resultCode
     * @param resultMessage
     * @return
     */
    public static CmfFundResult buildFundResult(CmfFundResultCode resultCode, String resultMessage) {
        CmfFundResult result = new CmfFundResult();
        result.setResultCode(resultCode);
        result.setResultMessage(resultMessage);

        boolean isSuccess = (CmfFundResultCode.SUCCESS == resultCode || CmfFundResultCode.REQUEST_SUCCESS == resultCode);
        result.setSuccess(isSuccess);

        return result;
    }

}
