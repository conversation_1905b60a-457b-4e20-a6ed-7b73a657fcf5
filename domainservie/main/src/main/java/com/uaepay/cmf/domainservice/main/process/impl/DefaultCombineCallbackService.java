package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.process.CombineCallbackService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class DefaultCombineCallbackService implements BasicConstant, CombineCallbackService {

    /**
     * 银行回调页面地址
     */
    @Value("${fcw.address}/page/")
    private String pageUrl;
    /**
     * 银行回调后台地址
     */
    @Value("${fcw.address}/server/")
    private String serverUrl;

    @Override
    public String getCallBackUrl(String channelCode, String type, FundChannelApiType apiType) {
        String apiCode = channelCode + SPLIT_TAG + apiType.getCode();
        if (PAGE_URL.equals(type)) {
            return pageUrl + apiCode + POSFIX_HTML;
        }
        return serverUrl + apiCode + POSFIX_HTML;
    }
}
