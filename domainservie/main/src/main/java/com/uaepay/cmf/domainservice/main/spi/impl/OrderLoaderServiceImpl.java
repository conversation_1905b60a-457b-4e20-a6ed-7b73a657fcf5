package com.uaepay.cmf.domainservice.main.spi.impl;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.InstOrderType;
import com.uaepay.cmf.common.core.domain.enums.ReturnResultNoEnum;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstRefundOrder;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * .
 * </p>
 *
 * <AUTHOR>
 * @version OrderLoaderServiceImpl.java 1.0 Created@2017-12-13 11:20 $
 */
@Service
public class OrderLoaderServiceImpl implements OrderLoaderService {

    private static final Map<String, InstOrderType> TYPE_MAP = new HashMap<>();

    static {
        TYPE_MAP.put(RequestType.REFUND.name(), InstOrderType.FUND);
        TYPE_MAP.put(ControlRequestType.ADVANCE.name(), InstOrderType.FUND);
        TYPE_MAP.put(ControlRequestType.VOID_TRANSACTION.name(), InstOrderType.FUND);
        TYPE_MAP.put(ControlRequestType.PREAUTH_UPDATE.name(), InstOrderType.FUND);
        TYPE_MAP.put(ControlRequestType.PREAUTH_COMPLETE.name(), InstOrderType.FUND);
        TYPE_MAP.put(ControlRequestType.PREAUTH_VOID.name(), InstOrderType.FUND);
        TYPE_MAP.put(ControlRequestType.AUTHENTICATE_ADVANCE.name(), InstOrderType.CONTROL);
    }

    @Resource
    protected InstOrderRepository instOrderRepository;

    @Resource
    protected CmfOrderRepository cmfOrderRepository;

    @Resource
    protected InstControlOrderRepository instControlOrderRepository;

    @Value("${cmf.returnOrder.channelConfig:KIOSK101^instSeq|}")
    private String returnOrderChannelConfig;

    /**
     * 获取原订单信息
     *
     * @param requestNo
     * @return
     */
    @Override
    public InstBaseOrder loadPreOrder(String requestType, String requestNo, String settlementId, String sourceOrder) {
        if (!TYPE_MAP.containsKey(requestType) || StringUtils.isEmpty(requestNo)) {
            return null;
        }
        InstOrderType orderType = TYPE_MAP.get(requestType);

        // 资金类
        if (orderType == InstOrderType.FUND) {
            if (SOURCE_ORDER_INST.equals(sourceOrder)) {
                return instOrderRepository.loadByNo(requestNo);
            } else {
                CmfOrder origCmfOrder = cmfOrderRepository.loadByPaymentSeqNo(requestNo, settlementId);
                if (origCmfOrder == null) {
                    return null;
                }
                return instOrderRepository.loadByCmfSeqNo(origCmfOrder.getOrderSeqNo()).get(0);
            }
        } else {
            return instControlOrderRepository.loadByRequestNo(requestNo);
        }
    }

    @Override
    public String loadReturnOrderNo(InstOrder instOrder, InstOrderResult result) {
        if (instOrder != null && YesNo.YES.equals(instOrder.getIsSplit())) {
            //拆分的用cmf订单号返回
            return instOrder.getCmfSeqNo();
        }

        if (result != null && instOrder != null) {
            ReturnResultNoEnum returnResultNoEnum = getReturnNoType(instOrder.getFundChannelCode());
            if (returnResultNoEnum != null) {
                switch (returnResultNoEnum) {
                    case INST_SEQ_NO:
                        return result.getInstSeqNo();
                    case ORGI_ORDER_NO:
                        if (instOrder instanceof InstRefundOrder) {
                            return ((InstRefundOrder) instOrder).getFundinOrderNo();
                        }
                        break;
                    default:
                        // do nothing
                }
            }
        }
        // 默认返回机构订单号
        return instOrder != null ? instOrder.getInstOrderNo() : EMPTY_STRING;
    }

    private ReturnResultNoEnum getReturnNoType(String channelCode) {
        if (StringUtils.isEmpty(returnOrderChannelConfig) || StringUtils.isEmpty(channelCode)) {
            return null;
        }
        String[] configs = returnOrderChannelConfig.split("\\|");
        for (String config : configs) {
            String[] itemConfig = config.split("\\^");
            if (itemConfig.length < 2) {
                continue;
            }
            if (channelCode.equals(itemConfig[0])) {
                return ReturnResultNoEnum.getByCode(itemConfig[1]);
            }

        }
        return null;
    }

    public static Map<String, InstOrderType> getTypeMap() {
        return TYPE_MAP;
    }

}
