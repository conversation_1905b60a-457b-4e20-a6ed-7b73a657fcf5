package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ManualRefundType;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;

/**
 * 
 * <p>退款处理</p>
 * <AUTHOR>
 * @version $Id: RefundProcessService.java, v 0.1 2012-8-10 上午11:19:41 liumaoli Exp $
 */
public interface RefundProcessService extends BasicConstant{

	/**
	 * 处理冲退失败订单业务
	 * @param instOrder
	 * @param refundType
	 */
    void processRefund(InstOrder instOrder, ManualRefundType refundType);
	
	
	/**
     * 处理冲退失败订单业务
     * @param instOrder
     * @param result
     */
    void processRefund(InstOrder instOrder, InstOrderResult result);

}
