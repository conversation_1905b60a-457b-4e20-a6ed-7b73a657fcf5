package com.uaepay.cmf.domainservice.main.convert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.uaepay.cmf.common.core.dal.dataobject.InstOrderResultDO;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.institution.*;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import org.apache.commons.lang3.StringUtils;
import com.uaepay.schema.cmf.enums.BizType;
import org.springframework.beans.BeanUtils;

/**
 * <p>机构订单结果转换器</p>
 *
 * <AUTHOR>
 * @version $Id: InstOrderResultConverter.java, v 0.1 2012-8-9 下午1:12:29 fuyangbiao Exp $
 */
public class InstOrderResultConverter {

    /**
     * 构建待处理机构结果
     *
     * @param instOrder
     * @return
     */
    public static InstOrderResult buildAwaiting(InstOrder instOrder) {
        InstOrderResult result = new InstOrderResult();
        result.setFundChannelCode(instOrder.getFundChannelCode());
        result.setApiType(instOrder.getApiType());
        result.setProcessStatus(InstOrderProcessStatus.AWAITING);
        result.setBizType(instOrder.getBizType());
        result.setInstOrderNo(instOrder.getInstOrderNo());
        result.setMemo("受理中，请等待.");


        return result;
    }

    /**
     * DO list -->机构订单结果LIST
     *
     * @param results
     * @return
     */
    public static List<InstOrderResult> convert(List<InstOrderResultDO> results) {
        InstOrderResult order = null;
        List<InstOrderResult> orders = new ArrayList<>();

        for (InstOrderResultDO resultDO : results) {
            order = convert(resultDO);
            orders.add(order);
        }

        return orders;
    }

    /**
     * DO-->机构订单结果
     *
     * @param resultDO
     * @return
     */
    public static InstOrderResult convert(InstOrderResultDO resultDO) {
        InstOrderResult order = new InstOrderResult();
        BeanUtils.copyProperties(resultDO, order);

        order.setBizType(BizType.getByCode(resultDO.getOrderType()));
        order.setInstOrderNo(resultDO.getOrgiInstOrderNo());
        order.setStatus(InstOrderResultStatus.getByCode(resultDO.getInstStatus()));
        order.setOperateStatus(InstResultOperateStatus.getByCode(resultDO.getOperateStatus()));
        if (StringUtils.isNotEmpty(resultDO.getExtension())) {
            order.setExtension(CommonConverter.convertFromDb(resultDO.getExtension()));
        }

        if (!StringUtils.isBlank(resultDO.getApiType())) {
            order.setApiType(FundChannelApiType.getByCode(resultDO.getApiType()));
        }
        if (!StringUtils.isBlank(resultDO.getRiskFlag())) {
            order.setRiskFlag(RiskFlag.getByCode(resultDO.getRiskFlag()));
        }
        return order;
    }

    /**
     * 机构订单结果-->DO
     *
     * @param result
     * @return
     */
    public static InstOrderResultDO convert(InstOrderResult result) {
        InstOrderResultDO orderDO = new InstOrderResultDO();
        BeanUtils.copyProperties(result, orderDO);
        if (result.getBizType() != null) {
            orderDO.setOrderType(result.getBizType().getCode());
        }
        orderDO.setOrgiInstOrderNo(result.getInstOrderNo());
        if (result.getStatus() != null) {
            orderDO.setInstStatus(result.getStatus().getCode());
        }
        //保存前面64位字符,防止保存数据库字符超长
        if (result.getMemo() != null && result.getMemo().length() > 64) {
            orderDO.setMemo(result.getMemo().substring(0, 64));
        } else {
            orderDO.setMemo(result.getMemo());
        }
        if (result.getOperateStatus() != null) {
            orderDO.setOperateStatus(result.getOperateStatus().getCode());
        }
        if (result.getExtension() != null) {
            Map<String, String> extMap = new HashMap<>(result.getExtension());
            // 去除PAGE_URL大字段
            extMap.remove(ExtensionKey.PAGE_URL.name());
            orderDO.setExtension(CommonConverter.convertToDb(extMap));
        }
        if (result.getApiType() != null) {
            orderDO.setApiType(result.getApiType().getCode());
        }
        if (result.getRiskFlag() != null) {
            orderDO.setRiskFlag(result.getRiskFlag().getCode());
        }
        orderDO.setReturnTimes(0);

        return orderDO;
    }

    public static void convert(InstOrder from, InstOrderResult to) {
        to.setInstOrderId(from.getInstOrderId());
        to.setInstOrderNo(from.getInstOrderNo());
        to.setFundChannelCode(from.getFundChannelCode());
        to.setBizType(from.getBizType());
    }


    public static InstOrderResultDO convert(InstOrderResult result, InstOrderResultDO dbResult) {
        InstOrderResultDO resultDO = convert(result);
        if (dbResult.getReturnTimes() != null) {
            resultDO.setReturnTimes(dbResult.getReturnTimes() + 1);
        }
        return resultDO;
    }
}
