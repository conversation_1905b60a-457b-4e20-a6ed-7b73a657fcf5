package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderType;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.common.enums.ApiParamScene;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.process.SaveBankReceiveService;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.router.service.facade.domain.channel.ChannelApiParamVO;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>签约时需要保存银行返回的信息，签约 推进时重新从原签约信息中获取</p>
 *
 * <AUTHOR>
 * @version $Id: SaveBankReceiveServiceImpl.java, v 0.1 2014-11-4 下午7:10:18 Administrator Exp $
 */
@Service("saveBankReceiveService")
public class SaveBankReceiveServiceImpl implements SaveBankReceiveService {

    private static final Logger logger = LoggerFactory.getLogger(SaveBankReceiveServiceImpl.class);

    @Resource()
    InstControlOrderRepository instControlOrderRepository;

    @Resource()
    InstOrderRepository instOrderRepository;

    @Override
    public void saveExtension(InstControlOrder controlOrder, InstControlOrderResult result,
                              ChannelVO channel) {

        if (channel == null) {
            return;
        }
        logger.info("channel apicode:{}", channel.getChannelApi().getApiCode());
        ChannelApiVO api = channel.getChannelApi();

        if (CollectionUtils.isNotEmpty(api.getParamList()) && result.getExtension() != null) {
            boolean needStore = false;
            InstOrderType orderType = getOrderType(controlOrder);
            InstOrder instOrder = orderType == InstOrderType.FUND ? instOrderRepository.loadByNo(controlOrder.getPreInstOrderNo()) : null;
            // 按照接口定义保存数据
            FundChannelApiType apiType = FundChannelApiType.getByCode(api.getApiType());
            if (apiType == null) {
                return;
            }
            for (ChannelApiParamVO apiParam : api.getParamList()) {
                if (ApiParamScene.CHANNEL_RETURN.getCode().equals(apiParam.getScene())
                        && StringUtils.isNotEmpty(result.getExtension().get(apiParam.getParamName()))) {
                    needStore = true;
                    if (isControl(orderType, apiType)) {
                        controlOrder.getExtension().put(apiParam.getParamName(),
                                result.getExtension().get(apiParam.getParamName()));
                    }
                    if (isFund(orderType, instOrder, apiType)) {
                        instOrder.getExtension().put(apiParam.getParamName(),
                                result.getExtension().get(apiParam.getParamName()));
                    }
                }
            }
            // 如果有需要保存,则更新扩展信息
            if (needStore) {
                if (orderType == InstOrderType.CONTROL) {
                    instControlOrderRepository.updateExtensionByRequestNo(
                            CommonConverter.convertToDb(controlOrder.getExtension()),
                            controlOrder.getRequestNo());
                } else if (orderType == InstOrderType.FUND && instOrder != null) {
                    instOrderRepository.storeExtension(instOrder);
                }
            }
        }
    }

    private boolean isControl(InstOrderType orderType, FundChannelApiType apiType) {
        return orderType == InstOrderType.CONTROL && (apiType == FundChannelApiType.AUTH
                || apiType == FundChannelApiType.ADVANCE_3DS2);
    }

    private boolean isFund(InstOrderType orderType, InstOrder instOrder, FundChannelApiType apiType) {
        return orderType == InstOrderType.FUND && (apiType == FundChannelApiType.DEBIT_ADVANCE
                || apiType == FundChannelApiType.ADVANCE_3DS2) && instOrder != null;
    }

    private InstOrderType getOrderType(InstControlOrder controlOrder) {
        return controlOrder.getExtension() != null && InstOrderType.CONTROL.name().equalsIgnoreCase(controlOrder.getExtension().get(ExtensionKey.INST_ORDER_TYPE.key))
                ? InstOrderType.CONTROL : InstOrderType.FUND;
    }

}
