package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.enums.IsAdvance;
import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.core.domain.exception.RouteChannelException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.process.DistributeQueryService;
import com.uaepay.cmf.domainservice.main.sender.BatchQuerySendService;
import com.uaepay.cmf.domainservice.main.sender.ControlOrderSendService;
import com.uaepay.cmf.domainservice.main.sender.SingleQuerySendService;
import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * 统一分发查询接口实现
 *
 * <AUTHOR> won
 * @version $Id: DistributeQueryServiceImpl.java, v 0.1 2011-3-22 下午05:28:10
 * sean won Exp $
 */
@Slf4j
@Service
public class DistributeQueryServiceImpl implements DistributeQueryService {

    @Resource
    private ControlOrderSendService controlOrderSendService;
    @Resource
    private SingleQuerySendService singleQuerySendService;
    @Resource
    private BatchQuerySendService batchQuerySendService;
    @Resource
    private ChannelApiRouter channelApiRouter;

    @Override
    public ChannelFundResult queryResult(InstOrder instOrder) {
        try (ChannelCarrier carrier = routeQueryChannel(instOrder)) {
            Assert.isTrue(instOrder.getIsAdvance() == IsAdvance.NO, "推进订单不支持查询");

            if (carrier == null || carrier.getChannel() == null) {
                return null;
            }

            ChannelFundResult channelFundResult = singleQuerySendService.send(instOrder);

            log.info("DistributeQuery.queryResult-Res{}", channelFundResult);

            return channelFundResult;
        } catch (Exception e) {
            log.warn("DistributeQuery.queryResult.e:{}", e.getMessage());
            return null;
        }
    }

    private ChannelCarrier routeQueryChannel(InstOrder instOrder) throws RouteChannelException {
        FundChannelApiType apiType = instOrder.getBizType() == BizType.REFUND ? FundChannelApiType.SINGLE_REFUND_QUERY : FundChannelApiType.SINGLE_QUERY;
        instOrder.setApiType(apiType);
        if (ChannelUtil.isManualRefund(instOrder.getApiType())) {
            return null;
        }
        return channelApiRouter.route(RouteUtil.getParam(instOrder.getFundChannelCode(),
                apiType));
    }

    @Override
    public ChannelResult queryControlResult(InstControlOrder instControlOrder) {
        ChannelResult channelResult = null;

        // 目前只能查这个
        try (ChannelCarrier carrier = channelApiRouter.route(RouteUtil.getParam(instControlOrder.getFundChannelCode(),
                FundChannelApiType.DEBIT_ADVANCE_QUERY))) {
            channelResult = controlOrderSendService.send(instControlOrder);
            if (channelResult != null) {
                log.info("查询结果,机构订单id: {},结果:{}", instControlOrder.getOrderId(), channelResult);
            }
            return channelResult;
        } catch (Exception e) {
            log.error("DistributeQueryService.queryControlResult.error", e);
            return null;
        }
    }

    /**
     * 批量逐笔补单
     *
     * @param instBatchOrder
     * @return
     * @throws CommunicateException
     */
    @Override
    public ChannelFundBatchResult queryBatchItemResult(InstBatchOrder instBatchOrder) {

        try {
            ChannelVO channel = ChannelHolder.get();
            if (channel == null || channel.getChannelApi() == null) {
                log.info("find no channel info:{}", instBatchOrder.getArchiveBatchId());
                return null;
            }
            log.info("BatchItemQueryRequest：{}", instBatchOrder.getInstBatchNo());

            ChannelFundBatchResult bulkQueryResult = batchQuerySendService.send(instBatchOrder);

            log.info("BatchItemQueryResult：{}", bulkQueryResult.getInstOrderNo());

            return bulkQueryResult;
        } catch (Exception e) {
            log.error("DistributeQueryService.queryBatchItemResult.error", e);
            return null;
        }
    }

}