package com.uaepay.cmf.domainservice.channel.impl;

import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.FundChannelManager;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date FundChannelManagerImpl.java v1.0  2020-09-08 00:27
 */
@Service
public class FundChannelManagerImpl implements FundChannelManager {

    @Resource
    private RouterClient routerClient;

    @Override
    public List<ChannelVO> getFundChannel(String channelCode, FundChannelApiType... apiTypes) {
        List<String> apiTypesList = new ArrayList<>();
        for (FundChannelApiType apiType : apiTypes) {
            apiTypesList.add(apiType.getCode());
        }
        return routerClient.getChannelsByApiTypes(channelCode, apiTypesList);
    }

    @Override
    public List<ChannelVO> loadByApiType(FundChannelApiType apiType) {
        return loadByApiTypes(apiType);
    }

    @Override
    public List<ChannelVO> loadByApiTypes(FundChannelApiType... apiTypes) {
        return getFundChannel(null , apiTypes);
    }
}
