package com.uaepay.cmf.domainservice.main.spi.impl;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.exception.RouteChannelException;
import com.uaepay.cmf.common.core.domain.institution.*;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.core.engine.util.PropertyValueUtil;
import com.uaepay.cmf.common.core.engine.util.collection.CollectionUtil;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.ApiParamScene;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.TokenHolder;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.channel.router.impl.CmfOrderChannelRouter;
import com.uaepay.cmf.domainservice.channel.router.impl.ControlOrderChannelRouter;
import com.uaepay.cmf.domainservice.main.convert.InstOrderConverter;
import com.uaepay.cmf.domainservice.main.convert.InstOrderResultConverter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.domain.FundRejectCodeEnum;
import com.uaepay.cmf.domainservice.main.domain.H2hRejectAccountEnum;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository;
import com.uaepay.cmf.domainservice.main.result.ControlResultProcessor;
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor;
import com.uaepay.cmf.domainservice.main.sender.ControlOrderSendService;
import com.uaepay.cmf.domainservice.main.sender.InstOrderSendService;
import com.uaepay.cmf.domainservice.main.spi.FundOutRejectService;
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.domainservice.main.split.SplitOrderService;
import com.uaepay.cmf.fss.ext.integration.util.ReturnResultUtil;
import com.uaepay.cmf.service.facade.domain.CmfCommonResultCode;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import com.uaepay.common.util.money.Money;
import com.uaepay.router.service.facade.domain.channel.ChannelApiParamVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.router.service.facade.domain.order.OrderInfo;
import com.uaepay.schema.cmf.enums.YesNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>提交机构服务默认实现</p>
 *
 * <AUTHOR>
 * @version $Id: DefaultSubmitInstitutionService.java, v 0.1 2012-8-3 下午1:53:10 fuyangbiao Exp $
 */
@Slf4j
@Service("submitInstitutionService")
public class DefaultSubmitInstitutionService implements SubmitInstitutionService {
    @Resource
    protected OrderLoaderService orderLoaderService;
    @Resource
    protected CmfOrderRepository cmfOrderRepository;
    @Resource
    protected InstOrderRepository instOrderRepository;
    @Resource
    protected InstOrderResultRepository instOrderResultRepository;
    @Resource
    protected InstControlOrderRepository instControlOrderRepository;
    @Resource
    private SplitOrderService splitOrderService;
    @Resource
    private CmfOrderChannelRouter channelRouter;
    @Resource
    private ControlOrderChannelRouter controlRouter;
    @Resource
    private ChannelApiRouter apiRouter;
    @Resource
    private InstOrderSendService instOrderSendService;
    @Resource
    private ControlOrderSendService controlOrderSendService;
    @Resource
    private InstResultProcessor instResultProcessor;
    @Resource
    private ControlResultProcessor controlResultProcessor;
    @Resource
    private FundOutRejectService fundOutRejectService;


    @Override
    public InstOrderResult submit(CmfOrder cmfOrder) {
        log.debug("提交CMF订单请求: {}", cmfOrder);
        // 1、获取资金渠道
        // WARNING: 这里有个深坑，如果在这部分代码逻辑及后续代码中调用了channelRouter.route，那么就会改变ChannelHolder中的值，
        // 会影响渠道参数逻辑，切忌切忌
        try (ChannelCarrier carrier = channelRouter.route(cmfOrder)) {

            // 2、组装机构订单
            InstBaseOrder preOrder = orderLoaderService.loadPreOrder(cmfOrder.getRequestType()
                    .name(), cmfOrder.getOrgiPaymentSeqNo(), cmfOrder.getOrgiSettlementId(), null);

            InstBaseResult preResult = preOrder == null ? null : instOrderResultRepository.loadRealResultByOrder(((InstOrder) preOrder).getInstOrderId());
            InstOrder instOrder = InstOrderConverter.convert(cmfOrder, preOrder, preResult,
                    carrier.getChannel(), carrier.getOrderInfo());

            // 3、订单完善并仓储
            List<InstOrder> instOrderList = splitOrderService.orderComplement(cmfOrder, instOrder,
                    preOrder, carrier.getChannel());

            instOrderRepository.storeInstOrder(instOrderList);

            // 校验账户类型，拒绝不支持的账户
            H2hRejectAccountEnum rejectAccountType = fundOutRejectService.getRejectAccountType(instOrderList);
            if (rejectAccountType != null) {
                return fundOutRejectService.processRejectAccountRequest(instOrderList, rejectAccountType);
            }

            // 风控检查
            if (CollectionUtil.isNotEmpty(fundOutRejectService.grcCheck(instOrderList))){
                return fundOutRejectService.processRejectRequest(instOrderList.get(0), FundRejectCodeEnum.CHARGE_BACK);
            }


            // 异步订单-等待task处理
            if (instOrder.getSendType().isAsync()) {
                return InstOrderResultConverter.buildAwaiting(instOrder);
            }

            ChannelFundResult channelFundResult = instOrderSendService.send(instOrder);
            // 结果推进
            return instResultProcessor.process(instOrder, channelFundResult);
        } catch (RouteChannelException re) {
            //1. cmfOrder直接跃迁为失败
            cmfOrder.setStatus(CmfOrderStatus.FAILURE);
            cmfOrderRepository.updateCmfOrderStatus(cmfOrder, CmfOrderStatus.IN_PROCESS);
            //2. 路由失败结果返回
            return ReturnResultUtil.routeFail(cmfOrder, re);
        } catch (Exception e) {
            log.error("SubmitInstitutionService.submit.error", e);
            return ReturnResultUtil.buildInProcess(cmfOrder, e.getMessage());
        } finally {
            TokenHolder.clear();
        }
    }

    @Override
    public InstOrderResult submit(InstOrder instOrder, FundChannelApiType apiType) {
        // 1. 渠道路由
        try (ChannelCarrier carrier = apiRouter.route(RouteUtil.getParam(instOrder.getFundChannelCode(), apiType))) {
            // 2.发送银行
            ChannelFundResult channelFundResult = instOrderSendService.send(instOrder);
            // 3.处理结果
            return instResultProcessor.process(instOrder, channelFundResult);
        } catch (Exception e) {
            log.error("SubmitInstitutionService.submit.error", e);
            // do something
            return ReturnResultUtil.buildInProcess(instOrder, e.getMessage());
        }
    }

    @Override
    public InstControlOrderResult submit(InstControlOrder order) {
        log.debug("提交CMF控制订单请求:{} ", order);

        // 1、获取资金渠道
        try (ChannelCarrier carrier = controlRouter.route(order)) {
            InstBaseOrder preOrder = orderLoaderService.loadPreOrder(order.getRequestType().name(),
                    order.getPreRequestNo(), order.getPreSettlementId(),
                    order.getExtension().get(ExtensionKey.SOURCE_ORDER.key));

            controlOrderComplement(order, preOrder, carrier.getChannel(), carrier.getOrderInfo());

            // 2、订单仓储
            order.setStatus(InstOrderStatus.IN_PROCESS);
            instControlOrderRepository.store(order);

            ChannelResult result = controlOrderSendService.send(order);

            // 结果推进
            return controlResultProcessor.process(order, result);
        } catch (RouteChannelException e) {
            log.warn("订单路由失败:{}", e.getMessage());
            return ReturnResultUtil.routeFail(CmfCommonResultCode.CHANNEL_NOT_ACCESS, e);
        } catch (Exception e) {
            log.error("SubmitInstitutionService.submit.error", e);
            return ReturnResultUtil.buildInProcess(order, e.getMessage());
        } finally {
            TokenHolder.clear();
        }
    }

    @Override
    public InstControlOrderResult submit(InstControlOrder controlOrder, FundChannelApiType apiType) {
        // 1. 渠道路由
        try (ChannelCarrier carrier = apiRouter.route(RouteUtil.getParam(controlOrder.getFundChannelCode(), apiType))) {
            // 2.发送银行
            ChannelResult result = controlOrderSendService.send(controlOrder);
            // 3.处理结果
            return controlResultProcessor.process(controlOrder, result);
        } catch (Exception e) {
            log.error("SubmitInstitutionService.submit.error", e);
            // do something
            return ReturnResultUtil.buildInProcess(controlOrder, e.getMessage());
        }
    }

    @Override
    public InstBaseResult submit(VerifySignRequest request, FundChannelApiType apiType) {

        try (ChannelCarrier carrier = apiRouter.route(RouteUtil.getParam(request.getChannelCode(), apiType))){
            if (FundChannelApiType.isFund(apiType)){

                InstOrder instOrder = new InstOrder();
                instOrder.setApiType(apiType);
                instOrder.setFundChannelCode(carrier.getChannel().getChannelCode());
                ChannelFundResult result = instOrderSendService.send(instOrder);

                if (StringUtils.isEmpty(result.getInstOrderNo())){
                    throw new Exception("instOrderNo is null." + result.getApiResultMessage());
                }
                instOrder = instOrderRepository.loadByNo(result.getInstOrderNo());
                return instResultProcessor.process(instOrder, result);
            } else {
                InstControlOrder instControlOrder = new InstControlOrder();
                instControlOrder.setApiType(apiType);
                instControlOrder.setFundChannelCode(carrier.getChannel().getChannelCode());
                ChannelResult result = controlOrderSendService.send(instControlOrder);

                if (StringUtils.isEmpty(result.getInstOrderNo())){
                    throw new Exception("instOrderNo is null." + result.getApiResultMessage());
                }
                instControlOrder = instControlOrderRepository.loadByNo(result.getInstOrderNo());
                return controlResultProcessor.process(instControlOrder, result);
            }
        } catch (Exception e){
            log.error("SubmitInstitutionService.submit.error", e);

            InstBaseResult result = new InstBaseResult();
            Map<String, String> extension = new HashMap<>();
            result.setResultMessage(e.getMessage());
            result.setStatus(InstOrderResultStatus.IN_PROCESS);
            result.setProcessStatus(InstOrderProcessStatus.UNKNOW_EXCEPTION);
            result.setExtension(extension);
            return result;
        }
    }

    /**
     * 控制订单信息补足
     *
     * @param order
     * @param
     * @param
     */
    private void controlOrderComplement(InstControlOrder order, InstBaseOrder preOrder,
                                        ChannelVO channel, OrderInfo orderInfo) {
        order.setInstCode(order.getInstCode());
        order.setFundChannelCode(channel.getChannelCode());
        order.setApiType(FundChannelApiType.getByCode(channel.getChannelApi().getApiType()));
        order.setInstOrderNo(orderInfo.getOrderNo());
        if (preOrder != null) {
            if(StringUtils.isNotEmpty(preOrder.getInstSeqNo())){
                order.getExtension().put(ExtensionKey.INST_SEQ_NO.key, preOrder.getInstSeqNo());
            }
            order.setPayMode(preOrder.getPayMode());
            order.setPreInstOrderNo(preOrder.getInstOrderNo());
            order.setFundChannelCode(preOrder.getFundChannelCode());
            //需要更新订单的使用新订单金额
            Money amount = order.isUpdatePreOrder() && order.getAmount()!=null?order.getAmount():preOrder.getAmount();
            order.setAmount(amount);
            order.setInstCode(preOrder.getInstCode());
            // 拷贝扩展信息
            if (!CollectionUtils.isEmpty(preOrder.getExtension())) {
                for (ChannelApiParamVO apiParam : channel.getChannelApi().getParamList()) {
                    if (ApiParamScene.REQUEST_CHANNEL.getCode().equals(apiParam.getScene())
                            && YesNo.YES.getCode().equals(apiParam.getIsOrigin())
                            && StringUtils.isNotEmpty(getExtVal(preOrder, apiParam.getParamName()))) {
                        order.getExtension().put(apiParam.getParamName(),
                                getExtVal(preOrder, apiParam.getParamName()));
                    }
                }
            }
        }

    }

    private static String getExtVal(InstBaseOrder preOrder, String paramName) {
        if (preOrder instanceof InstFundoutOrder) {
            for (InstOrderExtensionMapping mapping : InstOrderExtensionMapping
                    .getMappingSet(InstFundoutOrder.class)) {
                if (paramName.equals(mapping.getExtensionKey())) {
                    return (String) PropertyValueUtil.getValue(preOrder, mapping);
                }

            }
        }
        return preOrder.getExtension().get(paramName);
    }

}
