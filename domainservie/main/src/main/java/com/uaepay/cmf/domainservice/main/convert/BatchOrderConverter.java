package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.service.facade.domain.query.order.BatchOrderVO;
import org.springframework.beans.BeanUtils;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BatchOrderConverter.java v1.0
 */
public class BatchOrderConverter {

    private BatchOrderConverter() {

    }

    public static BatchOrderVO convert(InstBatchOrder bo) {
        if (bo == null) {
            return null;
        }
        BatchOrderVO vo = new BatchOrderVO();
        BeanUtils.copyProperties(bo, vo);
        if (bo.getStatus() != null) {
            vo.setStatus(bo.getStatus().getCode());
        }
        vo.setChannelCode(bo.getFundChannelCode());
        if (bo.getExtension() != null) {
            vo.setFileExt(bo.getExtension().get("fileExt"));
            vo.setFileTag(bo.getExtension().get("fileTag"));
            vo.setFileName(bo.getExtension().get("fileName"));
            vo.setExtension(MapUtil.mapToJson(bo.getExtension()));
        }
        return vo;
    }
}
