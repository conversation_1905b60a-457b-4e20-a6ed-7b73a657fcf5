package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.domain.fundin.ebank.EBankChannelVerifyRequest;
import com.uaepay.cmf.common.enums.CallBackType;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.VerifySignHolder;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static com.uaepay.cmf.common.enums.FundChannelApiType.MERCHANT_REGISTER_VERIFY;
import static com.uaepay.cmf.common.enums.FundChannelApiType.VERIFY_SIGN;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date VerifySignRequestBuilder.java v1.0  2020-09-12 11:23
 */
@Service
public class RegistrationVerifySignRequestBuilder extends AbstractApiRequestBuilder<InstControlOrder, EBankChannelVerifyRequest> {

    @Override
    protected void buildCustomParam(InstControlOrder order, EBankChannelVerifyRequest request) {
        VerifySignRequest vsRequest = VerifySignHolder.get();
        request.setCallBackType(CallBackType.getByCode(vsRequest.getCallbackType()));
        request.setResponseData(vsRequest.getVerifyParam());
        request.setResponseQueryString(vsRequest.getVerifyParamStr());
        request.setHeaderMap(vsRequest.getHeaderMap());
        if(request.getExtension()==null){
            request.setExtension(new HashMap<>());
        }
        request.getExtension().put(REQUEST_NO, order.getRequestNo());
    }

    @Override
    public EBankChannelVerifyRequest buildReq() {
        return new EBankChannelVerifyRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(MERCHANT_REGISTER_VERIFY);
    }
}
