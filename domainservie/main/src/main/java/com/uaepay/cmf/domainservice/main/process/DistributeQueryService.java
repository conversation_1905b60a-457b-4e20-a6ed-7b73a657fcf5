package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.core.domain.exception.RouteChannelException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelResult;

/**
 * 统一分发查询接口
 *
 * <AUTHOR> won
 * @version $Id: DistributeQueryService.java, v 0.1 2011-3-22 下午05:23:20 sean won Exp $
 */
public interface DistributeQueryService {

    /**
     * 查询控制类结果
     *
     * @param instControlOrder
     * @return
     * @throws CommunicateException
     * @throws RouteChannelException
     */
    ChannelResult queryControlResult(InstControlOrder instControlOrder) throws RouteChannelException;

    /**
     * 查询结果 1. 查询是否存在有未比对的结果,若存在直接装载 2. 查询是否存在单笔查询接口,若存在,调用单笔查询接口查询结果 3. 存在多笔记录,如果都是成功,处理中,返回成功结果 4.
     * 存在多笔记录,如果都是失败,处理中,返回失败结果 5. 存在多笔记录,如果都是处理中,随机获取处理中结果 6. 如果既存在成功,又存在失败,报警
     *
     * @param instOrder
     * @return
     */
    ChannelFundResult queryResult(InstOrder instOrder);

    /**
     * 逐笔查询批次结果
     *
     * @param instBatchOrder
     * @return
     * @throws CommunicateException
     * @throws RouteChannelException
     */
    ChannelFundBatchResult queryBatchItemResult(InstBatchOrder instBatchOrder);

}
