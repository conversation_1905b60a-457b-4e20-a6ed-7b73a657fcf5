package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <p>PreAuthUpdateResultLaterProcessor</p>
 *
 * <AUTHOR>
 * @version PreAuthUpdateResultLaterProcessor.java v1.0  2022/10/10 11:18
 */

@Slf4j
@Component
public class PreAuthUpdateResultLaterProcessor implements ResultLaterProcessor {


    @Resource
    protected InstOrderRepository instOrderRepository;

    @PostConstruct
    public void init() {
        ResultLaterProcessorEnum.PREAUTH_UPDATE.register(this);
    }

    @Override
    public Result<?> process(InstControlOrder instControlOrder, InstControlOrderResult instControlResult) {
        if (InstOrderResultStatus.SUCCESSFUL != instControlResult.getStatus()) {
            return Result.ofNothing();
        }
        log.info("预授权订单更新处理-控制单:{},控制单结果:{}", instControlOrder, instControlResult);

        InstOrder preInstOrder = instOrderRepository.loadByNo(instControlOrder.getPreInstOrderNo());

        preInstOrder.getExtension().put("updateRelationControlOrder", instControlOrder.getInstOrderNo());

        //最终更新金额为 controlOrder的金额
        Money updateAmount = instControlOrder.getAmount();
        if (updateAmount.equals(preInstOrder.getAmount())){
            log.warn("预授权金额未变化 不更新...");
            return Result.ofNothing();
        }

        String extensionStr = JsonUtil.mapToString(preInstOrder.getExtension());
        log.info("预授权订单更新处理,修改原机构单金额,机构单号:{},原金额:{},更新后金额:{}", instControlOrder.getPreInstOrderNo(), preInstOrder.getAmount(), updateAmount);

        instOrderRepository.updateAmountAndExtension(preInstOrder.getInstOrderId(), updateAmount, extensionStr);

        return Result.ofSuccess();
    }
}
