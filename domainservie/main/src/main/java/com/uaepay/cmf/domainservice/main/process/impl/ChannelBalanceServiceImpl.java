package com.uaepay.cmf.domainservice.main.process.impl;

import com.alibaba.fastjson.JSON;
import com.uaepay.cmf.common.core.domain.config.SysConfiguration;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.core.engine.util.collection.CollectionUtil;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigureKey;
import com.uaepay.cmf.common.domain.ChannelCommonResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.ApiMethodEnum;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.process.ChannelBalanceService;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.integration.factory.ChannelSenderFactory;
import com.uaepay.common.util.DateUtil;
import com.uaepay.common.util.money.Money;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 渠道余额服务实现
 * </p>
 *
 * <AUTHOR> Liu
 * @version $Id: ChannelBalanceServiceImpl.java, v 0.1 2013-7-15 下午4:07:21 liumaoli Exp $
 */
@Slf4j
@Service
public class ChannelBalanceServiceImpl implements ChannelBalanceService, SysConfigureKey {

    protected static final Logger logger = LoggerFactory.getLogger(ChannelBalanceServiceImpl.class);

    @Resource
    SysConfigurationHolder sysConfigurationHolder;

    @Resource
    private ChannelApiRouter channelApiRouter;

    @Resource
    private ChannelSenderFactory channelSenderFactory;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Override
    public ChannelFundResult queryBalanceResult(ChannelVO fundChannel) {
        ChannelRequest channelRequest = getChannelRequest(fundChannel);
        ChannelCommonResult channelCommonResult = channelSenderFactory.applyManager(channelRequest);
        return JSON.parseObject(channelCommonResult.getResultJason(), ChannelFundResult.class);
    }

    private static ChannelRequest getChannelRequest(ChannelVO fundChannel) {
        ChannelRequest channelRequest = new ChannelRequest();
        channelRequest.setApiType(FundChannelApiType.QUERY_BALANCE);
        channelRequest.setApiUrl(fundChannel.getChannelApi().getApiUrl());
        channelRequest.setFundChannelCode(fundChannel.getChannelCode());
        channelRequest.setInstCode(fundChannel.getInstCode());
        channelRequest.setApiMethod(ApiMethodEnum.getByCode(fundChannel.getChannelApi().getApiMethod()));
        return channelRequest;
    }

    @Override
    public List<String> combineFundChannel(String channelCode) {
        List<String> fundChannels = new ArrayList<>();
        if (StringUtils.isEmpty(channelCode)) {
            return fundChannels;
        }
        Map<String, String> dataMap = MapUtil.jsonToMap(sysConfigurationHolder.loadConfigureOrDefault(QUERY_BALANCE_CHANNEL_REFLECT, "{}"));
        fundChannels.add(channelCode);
        for (Map.Entry<String, String> entry : dataMap.entrySet()) {
            if (channelCode.equals(entry.getValue())) {
                fundChannels.add(entry.getKey());
            }
        }
        return fundChannels;
    }

    @Override
    public void warn(Money balance, Money mappingBalance, Money sumAmount, String channelDescription) {
    }

    @Override
    public Boolean checkBalance(String fundChannelCode, Boolean needNotify) {
        try (ChannelCarrier carrier = channelApiRouter.route(RouteUtil.getParam(fundChannelCode, FundChannelApiType.QUERY_BALANCE))) {
            if (carrier.getChannel() == null || carrier.getChannel().getChannelApi() == null) {
                log.info("Channel-{}-余额查询接口为空", fundChannelCode);
                return false;
            }
            return checkBalance(carrier.getChannel(), combineFundChannel(fundChannelCode), needNotify);
        } catch (Exception e) {
            logger.warn(fundChannelCode + "出款金额预警,无查询接口,直接通过");
            return false;
        }
    }

    private Money getBuffer() {
        SysConfiguration conf = sysConfigurationHolder.getConfiguration(SysConfigureKey.AMOUNT_CHECK_BUFFER);
        if (null != conf && StringUtils.isNotEmpty(conf.getAttrValue())) {
            return new Money(conf.getAttrValue(), DEFAULT_CURRENCY);
        } else {
            return new Money(ZERO_MONEY_STRING, DEFAULT_CURRENCY);
        }
    }

    /**
     * 检查余额
     *
     * @param channel
     * @param fundChannelCodes
     * @param needNotify
     * @return
     */
    private Boolean checkBalance(ChannelVO channel, List<String> fundChannelCodes, Boolean needNotify) {
        try {
            // 查询渠道账户余额
            ChannelFundResult channelResult = queryBalanceResult(channel);
            Money balance = channelResult.getRealAmount();
            if (balance == null) {
                logger.info("{}.checkBalance.fail:{}", channel.getChannelCode(), balance);
                return false;
            }

            Map<String, String> extMap = MapUtil.jsonToMap(channelResult.getExtension());

            // 检查余额查询
            Money sumAmount = gatherAwaitingAmount(channel.getChannelCode());

            logger.info("{},channelList:{}-balance:{},-sumAmount:{}", channel.getChannelCode(), fundChannelCodes,
                    balance, sumAmount);

            boolean moneyShortage = balance.compareTo(sumAmount) < 0;
            // 余额不足渠道控制，余额不足之后则渠道都走异步；余额足够之后则继续同步
            putBalanceInsufficientChannels(fundChannelCodes, moneyShortage);

            // 余额不足或需要通知,发短信通知
            if (moneyShortage || needNotify) {
                warn(sumBalance(balance, extMap), balance, sumAmount, channel.getChannelName());
                return false;
            }
            return true;
        } catch (Exception e) {
            logger.error("[余额查询失败]:" + fundChannelCodes.toString(), e);
//            mnsNotifyClient.sendMsg(channel.getFundChannelCode() + "查询出款余额出错", NotifyProtocol.SNS, "");
            return false;
        }
    }

    private void putBalanceInsufficientChannels(List<String> fundChannelCodes, boolean moneyShortage) {
        if (CollectionUtils.isEmpty(fundChannelCodes)) {
            return;
        }
        String sysConfig = sysConfigurationHolder.loadConfigureOrDefault(BALANCE_INSUFFICIENT_CHANNELS, "");
        Set<String> fundChannelSet = new HashSet<>(Arrays.asList(sysConfig.split(CHAR_COMMA)));
        boolean modified;
        if (!moneyShortage) {
            modified = fundChannelSet.removeAll(fundChannelCodes);
        } else {
            modified = fundChannelSet.addAll(fundChannelCodes);
        }
        String newSysConfig = CollectionUtil.join(fundChannelSet, CHAR_COMMA);
        if (modified) {
            logger.info("balanceConfig.changed,origin-{},new-{}", sysConfig, newSysConfig);
            sysConfigurationHolder.update(BALANCE_INSUFFICIENT_CHANNELS, newSysConfig);
        }
    }

    private Money sumBalance(Money balance, Map<String, String> extMap) {
        if (MapUtils.isNotEmpty(extMap)) {
            String transferBalance = extMap.get(ExtensionKey.TRANSFER_BALANCE.key);
            if (NumberUtils.isNumber(transferBalance)) {
                return new Money(transferBalance, DEFAULT_CURRENCY).add(balance);
            }
        }
        return balance;
    }

    @Override
    public Money gatherAwaitingAmount(final String channelCode) {
        List<String> communicateStatusList = new ArrayList<>(Arrays.asList(CommunicateStatus.AWAITING.getCode(), CommunicateStatus.SENT.getCode(), CommunicateStatus.FAILURE.getCode()));
        List<String> fundChannelCodeList = combineFundChannel(channelCode);

        Date startDate = DateUtil.addHours(new Date(), -24);
        Date endDate = new Date();
        Money awaitingAmount = instOrderRepository.sumAmountForQueryResult(communicateStatusList,
                BizType.FUNDOUT.getCode(), fundChannelCodeList, startDate, endDate);

        logger.info("BALANCE_QUERY_RESULT:[" + startDate + "," + endDate + "],金额:" + awaitingAmount);
        Money buffer = getBuffer();
        logger.info("BALANCE_QUERY_RESULT_SUM:channel:{},裕度:{},总金额{}", channelCode, buffer,
                awaitingAmount);
        return awaitingAmount.add(buffer);
    }

    @Override
    public ChannelResult queryBalance(ChannelVO channel, Map<String, String> extension) {
        ChannelRequest channelRequest = new ChannelRequest();
        channelRequest.setApiType(FundChannelApiType.getByCode(channel.getChannelApi().getApiType()));
        channelRequest.setApiMethod(ApiMethodEnum.getByCode(channel.getChannelApi().getApiMethod()));
        channelRequest.setApiUrl(channel.getChannelApi().getApiUrl());
        channelRequest.setFundChannelCode(channel.getChannelCode());
        channelRequest.setInstCode(channel.getInstCode());
        channelRequest.setExtension(extension);
        ChannelCommonResult channelCommonResult = channelSenderFactory.applyManager(channelRequest);
        return JSON.parseObject(channelCommonResult.getResultJason(), ChannelResult.class);
    }

    @Override
    public List<String> filterBalance(List<String> channelList) {
        if (CollectionUtils.isEmpty(channelList)) {
            return new ArrayList<>(0);
        }
        Map<String, Set<String>> configMap = queryBalanceConfig(channelList);
        Set<String> channelSet = new HashSet<>();
        for (String channelCode : configMap.keySet()) {
            if (checkBalance(channelCode, false)) {
                channelSet.addAll(configMap.get(channelCode));
            } else {
                logger.info("channel.balance.filterList:{}", configMap.get(channelCode));
            }
        }
        return new ArrayList<>(channelSet);
    }

    @Override
    public ChannelFundResult queryBalanceResult(ChannelVO fundChannel, Map<String, String> extension, String instOrderNo) {
        ChannelRequest channelRequest = getChannelRequest(fundChannel);
        channelRequest.setInstOrderNo(instOrderNo);
        channelRequest.setExtension(extension);
        ChannelCommonResult channelCommonResult = channelSenderFactory.applyManager(channelRequest);
        return JSON.parseObject(channelCommonResult.getResultJason(), ChannelFundResult.class);
    }

    /**
     * 余额渠道配置
     *
     * @return
     */
    protected Map<String, Set<String>> queryBalanceConfig(List<String> channelList) {
        String channelReflect = sysConfigurationHolder.loadConfigureOrDefault(SysConfigureKey.QUERY_BALANCE_CHANNEL_REFLECT, "{}");
        Map<String, String> dataMap = MapUtil.jsonToMap(channelReflect);
        Map<String, Set<String>> resultMap = new HashMap<>(channelList.size());
        for (String channel : channelList) {
            resultMap.put(channel, new HashSet<>(Collections.singletonList(channel)));
        }
        for (String key : dataMap.keySet()) {
            resultMap.remove(key);
            if (!resultMap.containsKey(dataMap.get(key))) {
                resultMap.put(dataMap.get(key), new HashSet<>());
            }
            resultMap.get(dataMap.get(key)).add(key);
        }
        return resultMap;
    }

}
