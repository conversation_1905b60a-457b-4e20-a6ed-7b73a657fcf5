package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.ClearNetEnum;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.spi.ClearNetService;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.cmf.service.facade.domain.clear.ClearInfo;
import com.uaepay.cmf.service.facade.domain.clear.ClearInfoQueryRequest;
import com.uaepay.cmf.service.facade.result.ListQueryResult;
import com.uaepay.router.service.facade.domain.channel.InstCurrencyVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ClearInfoProcessor.java v1.0
 */
@Service
public class ClearInfoProcessor extends GeneralProcessorTemplate<ClearInfoQueryRequest, ListQueryResult<ClearInfo>> {

    @Resource
    private RouterClient routerClient;
    @Resource
    private ClearNetService clearNetService;

    @Override
    protected String getServiceName() {
        return "ClearInfoProcessor";
    }

    @Override
    protected ListQueryResult<ClearInfo> createResponse() {
        return new ListQueryResult<>();
    }

    @Override
    protected void process(ClearInfoQueryRequest request, ListQueryResult<ClearInfo> result) {
        result.setApplyStatus(ApplyStatusEnum.SUCCESS);
        ClearNetEnum clearNet = ClearNetEnum.getByCode(request.getClearNet());
        if (!StringUtils.isEmpty(request.getClearNet()) && clearNet == null) {
            return;
        }
        List<InstCurrencyVO> instCurrencies = routerClient.queryInstCurrency(FundChannelApiType.getFundoutTypes().stream().map(FundChannelApiType::getCode).collect(Collectors.toList()));
        result.setList(clearNetService.convert(instCurrencies, clearNet));
    }
}
