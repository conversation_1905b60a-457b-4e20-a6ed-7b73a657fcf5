package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.ManualRefundType;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.router.ApiRouteParam;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.process.RefundProcessService;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>退款处理实现</p>
 *
 * <AUTHOR> Liu
 * @version $Id: RefundProcessServiceImpl.java, v 0.1 2012-8-10 上午11:19:57 liumaoli Exp $
 */
@Service
public class RefundProcessServiceImpl implements RefundProcessService {
    private Logger logger = LoggerFactory.getLogger(RefundProcessServiceImpl.class);

    @Resource
    private ChannelApiRouter apiRouter;

    @Resource
    private InstOrderRepository instOrderRepository;


    @Override
    public void processRefund(InstOrder instOrder, InstOrderResult result) {
        if (!canTransfer(instOrder)) {
            return;
        }

        //结果不为空时,更新备注信息为失败,由后续定时任务转人工
        if (result != null && InstOrderResultStatus.FAILURE.equals(result.getStatus())) {
            String memo = ManualRefundType.AUTO_REFUND_FAILED.getCode();
            if (!StringUtils.isEmpty(result.getMemo())) {
                memo = memo + ManualRefundType.SPLIT_CHARACTER + result.getMemo();
            }
            result.setMemo(memo);

            instOrder.setMemo(result.getMemo());
            instOrderRepository.updateMemoById(instOrder.getMemo(), instOrder.getInstOrderId());
            result.setProcessStatus(InstOrderProcessStatus.AWAITING);
            result.setStatus(InstOrderResultStatus.IN_PROCESS);
        }
    }

    @Override
    public void processRefund(InstOrder instOrder, ManualRefundType refundType) {
        switch (refundType) {
            case REFUND_NON_RETURN_RESULT:
                //如果类型是超过固定时限未返回结果,更新memo信息,直接转人工
                instOrder.setMemo(ManualRefundType.REFUND_NON_RETURN_RESULT.getCode());
                instOrderRepository.updateMemoById(instOrder.getMemo(), instOrder.getInstOrderId());
                transfer2Manual(instOrder);
                break;
            case OTHERS:
            case AUTO_REFUND_FAILED:
                //其它,退款失败,直接转人工
                transfer2Manual(instOrder);
                break;
            default:
                break;
        }
    }

    /**
     * 转人工操作,修改API
     *
     * @param instOrder
     */
    private void transfer2Manual(InstOrder instOrder) {
        ChannelVO channel = null;
        try (ChannelCarrier carrier = apiRouter.route(ApiRouteParam.builder().channelCode(instOrder.getFundChannelCode())
                .apiType(instOrder.getApiType().getCode()).build())) {
            channel = carrier.getChannel();
        } catch (Exception e) {
            logger.warn("渠道路由失败");
            return;
        }

        ChannelApiVO manualRefundApi = buildManualRefundApi(channel);
        if (manualRefundApi == null) {
            logger.info("order.notSupport.manualRefund:{}", instOrder.getInstOrderNo());
            return;
        }

        instOrder.setFundChannelCode(channel.getChannelCode());
        instOrderRepository.updateChannelInfoById(manualRefundApi.getChannelCode(),
                manualRefundApi.getApiCode(), instOrder.getInstOrderId());
    }

    private ChannelApiVO buildManualRefundApi(ChannelVO channel) {
        return null;
    }

    private boolean canTransfer(InstOrder instOrder) {
        return BizType.REFUND.equals(instOrder.getBizType())
                && FundChannelApiType.SINGLE_REFUND.equals(instOrder
                .getApiType());
        // TODO: 待完成
        //&& instOrder.getFundChannel().isManualRefundSupported();
    }

}
