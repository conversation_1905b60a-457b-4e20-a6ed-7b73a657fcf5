package com.uaepay.cmf.domainservice.main.repository.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.uaepay.basis.sequenceutil.IDGen;
import com.uaepay.cmf.common.core.dal.daointerface.MonitorLogDAO;
import com.uaepay.cmf.common.core.dal.dataobject.MonitorLogDO;
import com.uaepay.cmf.common.core.dal.util.SeqConstant;
import com.uaepay.cmf.common.enums.MonitorLogStatus;
import com.uaepay.cmf.common.monitor.MonitorLog;
import com.uaepay.cmf.domainservice.main.convert.MonitorLogConverter;
import com.uaepay.cmf.domainservice.main.repository.MonitorLogRepository;
import org.springframework.stereotype.Repository;

/**
 *
 * <p>
 * 监控日志仓储
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: MonitorLogRepositoryImpl.java, v 0.1 2011-7-20 上午10:31:09 sean won Exp $
 */
@Repository
public class DefaultMonitorLogRepository implements MonitorLogRepository {

    /** 监控日志DAO */
    @Resource
    private MonitorLogDAO monitorLogDAO;

    @Resource(name = "idGen")
    private IDGen idGen;

    @Override
    public MonitorLog load(Long logId) {
        MonitorLogDO dbLog = monitorLogDAO.loadByLogId(logId);
        return MonitorLogConverter.convert(dbLog);
    }

    @Override
    public List<MonitorLog> loadByStatus(Integer rownum, MonitorLogStatus stauts) {
        List<MonitorLogDO> logs = monitorLogDAO.loadByStatus(stauts.getCode(), rownum);
        if (null == logs || logs.isEmpty()) {
            return null;
        }

        List<MonitorLog> monitorLogs = new ArrayList<>();
        for (MonitorLogDO log : logs) {
            monitorLogs.add(MonitorLogConverter.convert(log));
        }

        return monitorLogs;
    }

    @Override
    public Long store(MonitorLog log) {

        MonitorLogDO dataObject = MonitorLogConverter.convert(log);
        dataObject.setLogId(idGen.get(SeqConstant.SEQ_MONITOR_LOG));

        return monitorLogDAO.insert(dataObject);
    }

    @Override
    public int updateStatusWithPreStatus(List<String> logIdlist, MonitorLogStatus newStatus, MonitorLogStatus oldStatus,
        String memo) {
        return monitorLogDAO.updateStatusWithPreStatus(logIdlist, newStatus.getCode(), oldStatus.getCode(), memo);
    }
}
