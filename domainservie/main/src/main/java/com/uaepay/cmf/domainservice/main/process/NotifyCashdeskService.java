package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date NotifyCashdeskService.java v1.0  2020-03-26 15:21
 */
public interface NotifyCashdeskService extends BasicConstant {

    /**
     * 通知绑卡
     * @param instOrder
     */
    void notifyBindCard(InstOrder instOrder, InstOrderResult result);

    /**
     * 通知绑卡
     * @param controlOrder
     * @param result
     */
    void notifyBindCard(InstControlOrder controlOrder, InstControlOrderResult result);

}
