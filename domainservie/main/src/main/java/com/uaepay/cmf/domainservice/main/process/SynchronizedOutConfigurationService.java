package com.uaepay.cmf.domainservice.main.process;

import java.util.List;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;

/**
 *
 * <p>异步处理服务</p>
 * <AUTHOR>
 * @version $Id: SynchronizedOutConfigurationService.java, v 0.1 2014-11-6 下午7:14:21 Administrator Exp $
 */
public interface SynchronizedOutConfigurationService {


    /**
     * 判断订单是否为异步
     * @param fundChannel
     * @param instOrderList
     * @return
     */
    boolean isAsynchronousOrder(ChannelVO fundChannel, List<InstOrder> instOrderList);

    /**
     *
     * @param fundChannel
     * @param instOrder
     * @return
     */
    boolean isAsynchronousOrder(ChannelVO fundChannel, InstOrder instOrder);
}
