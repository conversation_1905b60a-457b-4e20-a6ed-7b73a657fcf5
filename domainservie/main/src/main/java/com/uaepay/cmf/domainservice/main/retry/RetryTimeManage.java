package com.uaepay.cmf.domainservice.main.retry;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version RetryTimeManage.java 1.0 Created@2017-12-28 14:23 $
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface RetryTimeManage {
}
