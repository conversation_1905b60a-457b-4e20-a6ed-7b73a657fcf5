package com.uaepay.cmf.domainservice.main.process.buffer;

import com.uaepay.common.util.DateUtil;
import lombok.ToString;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * 渠道订单-时间缓存. 用于标记查询时间范围
 * </p>
 *
 * <AUTHOR>
 * @version ChannelOrderHourCache.java 1.0 @2016/1/6 17:57 $
 */
@ToString(callSuper = true)
public class ChannelOrderHourCache {

    private static ChannelOrderHourCache channelAmountHourCache = new ChannelOrderHourCache();

    /**
     * 缓存
     */
    private Map<String, Set<String>> hourNeedNotCheckMap = new ConcurrentHashMap<>();

    // 时间精确到小时
    private static final String DATE_FORMAT = "yyyyMMddHH";

    private ChannelOrderHourCache() {
        //不允许实例化
    }

    // 单例
    public static ChannelOrderHourCache getInstance() {
        return channelAmountHourCache;
    }

    public Set<String> getByKey(String key) {
        return hourNeedNotCheckMap.get(key);
    }

    public boolean exists(String key, String hour) {
        return key != null && hourNeedNotCheckMap.containsKey(key)
                && hourNeedNotCheckMap.get(key).contains(hour);
    }

    /**
     * 获取查询时间范围
     * 格式如下: 间隔为固定一个小时，最后一条记录为小时到当前时间
     * [2016-01-20 15:00:00,2016-01-20 15:17:08]
     * [2016-01-20 14:00:00,2016-01-20 15:00:00]
     * [2016-01-20 13:00:00,2016-01-20 14:00:00]
     * [2016-01-20 12:00:00,2016-01-20 13:00:00]
     *
     * @param key
     * @param maxHour
     * @return
     */
    public List<Date[]> getQueryTimeRange(String key, int maxHour) {
        List<Date[]> dateList = new ArrayList<>();
        if (maxHour <= 0) {
            return dateList;
        }
        Calendar calendar = Calendar.getInstance();
        Date lastDate = parseDateHour(DateUtil.format(calendar.getTime(), DATE_FORMAT));
        dateList.add(new Date[]{lastDate, new Date()});
        for (int i = 0; i < maxHour; i++) {
            calendar.add(Calendar.HOUR, -1);
            String dateHour = DateUtil.format(calendar.getTime(), DATE_FORMAT);
            Date tempDate = parseDateHour(dateHour);
            // 以开始时间作为值判断是否需要
            dateList.add(new Date[]{tempDate, lastDate});
            lastDate = tempDate;
        }
        return dateList;
    }


    private Date parseDateHour(String dateHour) {
        try {
            return DateUtil.parseDateNoTime(dateHour, DATE_FORMAT);
        } catch (ParseException e) {
            return null;
        }
    }

}