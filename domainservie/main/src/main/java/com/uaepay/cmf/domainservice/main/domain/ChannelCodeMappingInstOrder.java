package com.uaepay.cmf.domainservice.main.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 10/01/2025 15:46
 */
@Data
public class ChannelCodeMappingInstOrder {
    private String instCode;
    private String orderType;
    private String currency;
    private BigDecimal amount;
    private String productCode;
    private String payMode;
    private String paymentCode;
    private String merchantId;
    private Map<String, String> ext = new HashMap<>();
}
