package com.uaepay.cmf.domainservice.channel.limit.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.YesNoEnum;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ChannelInfoExtKey;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.SvaAccountEnum;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.vo.GrcLimitCarrier;
import com.uaepay.cmf.common.core.domain.vo.TransformInfo;
import com.uaepay.cmf.domainservice.channel.limit.LimitService;
import com.uaepay.cmf.domainservice.channel.router.ConfigurationService;
import com.uaepay.cmf.fss.ext.integration.config.ITransformConfig;
import com.uaepay.cmf.fss.ext.integration.config.RemittanceConfig;
import com.uaepay.cmf.fss.ext.integration.config.TransformConfig;
import com.uaepay.cmf.fss.ext.integration.escrow.EscrowClient;
import com.uaepay.cmf.fss.ext.integration.grc.GrcClient;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date LimitServiceImpl.java v1.0
 */
@Slf4j
@Service
public class LimitServiceImpl implements LimitService {

    @Resource
    private GrcClient grcClient;
    @Resource
    private TransformConfig transformConfig;
    @Resource
    private RemittanceConfig remittanceConfig;
    @Resource
    private ConfigurationService configurationService;
    @Resource
    private EscrowClient escrowClient;

    /**
     * 限额校验
     *
     * @param cmfOrder
     */
    @Override
    public boolean validateLimit(CmfOrder cmfOrder) {
        // 非入款交易不做限额校验
        if (cmfOrder.getBizType() != BizType.FUNDIN) {
            return true;
        }
        GrcLimitCarrier carrier = toCarrier(cmfOrder);
        /**
         * 校验金融类拆单
         */
        boolean validateTransform = validateTransform(transformConfig, cmfOrder, carrier);
        /**
         * 校验汇款拆单
         */
        boolean validateRemittance = validateTransform(remittanceConfig, cmfOrder, carrier);
        return validateTransform && validateRemittance;
    }

    /**
     * @param iTransformConfig
     * @param carrier
     * @return
     */
    private boolean validateTransform(ITransformConfig iTransformConfig, CmfOrder cmfOrder, GrcLimitCarrier carrier) {
        // 判定是否为拆单产品码
        TransformInfo info = TransformInfo.builder()
                .bizProductCode(carrier.getBizProductCode())
                .dbcr(cmfOrder.getExtension().get(ExtensionKey.DBCR.getKey()))
                .cardType(cmfOrder.getExtension().get(ExtensionKey.CARD_TYPE_UNDERLINE.getKey()))
                .merchantId(cmfOrder.getExtension().get(ExtensionKey.TOPAY_MERCHANT_ID.getKey()))
                .build();
        log.info("LimitServiceImpl.validateTransform-transformInfo:{}", info);
        if (!iTransformConfig.isTransformTarget(info)) {
            return true;
        }
        // 校验充值和支付的限额
        boolean validatePass = false;
        try {
            if (StringUtils.isNotEmpty(carrier.getMemberId())
                    && !BasicConstant.ANONYMOUS_MEMBER_ID.equals(carrier.getMemberId())
                    && YesNo.YES.getCode().equals(cmfOrder.getExtension().get(ExtensionKey.TRANSFORM_ACCOUNT_ACCESS.getKey()))
                    && checkPass(iTransformConfig, carrier)) {
                validatePass = true;
            }
        } catch (Exception e) {
            // 异常当做校验失败处理
            log.error("LimitService.validateLimit.error:", e);
        }
        // 加入blackList
        if (!validatePass) {
            cmfOrder.getExtension().put(ExtensionKey.BLACK_CHANNEL_LIST.key, mergeChannels(cmfOrder.getExtension().get(ExtensionKey.BLACK_CHANNEL_LIST.key), iTransformConfig.getChannels()));
            cmfOrder.getExtension().put(ExtensionKey.UNITY_RESULT_CODE.key, ErrorCode.ROUTER_ERROR_TRANSFORM_AMOUNT_EXCEED.getErrorCode());
        }
        return validatePass;
    }

    private String mergeChannels(String oldChannel, String newChannels) {
        if (StringUtils.isEmpty(oldChannel) || oldChannel.equals(newChannels)) {
            return newChannels;
        }
        return oldChannel + BasicConstant.CHAR_COMMA + newChannels;
    }

    private boolean checkPass(ITransformConfig iTransformConfig, GrcLimitCarrier carrier) {
        if (!checkSvaAccountPass(iTransformConfig, carrier)) {
            log.info("LimitService.checkPass.notPass:hasNoSva");
            return false;
        }
        if (SvaAccountEnum.VIP != carrier.getSvaAccount() && !iTransformConfig.isAmountInRange(carrier.getAmount())) {
            log.info("LimitService.checkPass.notPass:amountNotInRange");
            return false;
        }
        if (!validateRatio(iTransformConfig, carrier.getBizProductCode())) {
            log.info("LimitService.checkPass.notPass:validateRatioNotPass");
            return false;
        }
        if (!grcClient.validateLimit(toTopUp(iTransformConfig, carrier))) {
            log.info("LimitService.checkPass.notPass:topUpLimitNotPass");
            return false;
        }
        if (!grcClient.validateLimit(toConsume(iTransformConfig, carrier))) {
            log.info("LimitService.checkPass.notPass:consumeLimitNotPass");
            return false;
        }
        return true;
    }

    private boolean checkSvaAccountPass(ITransformConfig iTransformConfig, GrcLimitCarrier carrier) {

        // 根据memberId获取sva账户的状态
        SvaAccountEnum svaAccount = escrowClient.querySvaAccount(carrier.getMemberId());

        if(svaAccount == SvaAccountEnum.HAS_SVA){
            return true;
        }
        return iTransformConfig.isVipPass() && svaAccount == SvaAccountEnum.VIP;
    }

    public boolean validateRatio(ITransformConfig iTransformConfig, String bizPCode) {
        // 未配置则通过
        if (iTransformConfig.getRatioBizCodeMap()==null || !iTransformConfig.getRatioBizCodeMap().containsKey(bizPCode)) {
            return true;
        }
        int random = (int) (Math.random() * 100);
        boolean pass = random <= iTransformConfig.getRatioBizCodeMap().get(bizPCode);
        if (!pass) {
            log.info("LimitService.validateLimit.validateRatio:{},{},{}", random, iTransformConfig.getRatioBizCodeMap().get(bizPCode), pass);
        }
        return pass;
    }


    /**
     * 仅记录特定渠道(fab)非充值类的流量
     *
     * @param instOrder
     */
    @Override
    public boolean recordFlow(InstOrder instOrder) {
        if (!configurationService.isSvaTransformOrder(instOrder)) {
            return false;
        }
        GrcLimitCarrier carrier = toCarrier(instOrder);
        TransformInfo info = TransformInfo.builder()
                .fundChannelCode(instOrder.getFundChannelCode())
                .bizProductCode(instOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.getKey()))
                .merchantId(instOrder.getExtension().get(ExtensionKey.TOPAY_MERCHANT_ID.getKey()))
                .dbcr(instOrder.getExtension().get(ExtensionKey.DBCR.getKey()))
                .cardType(instOrder.getExtension().get(ExtensionKey.CARD_TYPE_UNDERLINE.getKey()))
                .build();
        ITransformConfig iTransformConfig = transformConfig.isTransform(info) ? transformConfig : remittanceConfig;
        try {
            // todo: 需要重试和幂等
            grcClient.validateLimit(toTopUp(iTransformConfig, carrier));
            grcClient.validateLimit(toConsume(iTransformConfig, carrier));
            return true;
        } catch (Exception e) {
            log.error("LimitService.recordFlow.error:", e);
            return false;
        }
    }


    private GrcLimitCarrier toTopUp(ITransformConfig iTransformConfig, GrcLimitCarrier carrier) {
        GrcLimitCarrier topUpCarrier = new GrcLimitCarrier();
        BeanUtils.copyProperties(carrier, topUpCarrier);
        topUpCarrier.setBizProductCode(iTransformConfig.getTopUpBizCode());
        topUpCarrier.setTopUp(true);
        return topUpCarrier;
    }

    private GrcLimitCarrier toConsume(ITransformConfig iTransformConfig, GrcLimitCarrier carrier) {
        GrcLimitCarrier consumeCarrier = new GrcLimitCarrier();
        BeanUtils.copyProperties(carrier, consumeCarrier);
        consumeCarrier.setBizProductCode(iTransformConfig.getConsumeBizCode());
        return consumeCarrier;
    }


    private GrcLimitCarrier toCarrier(CmfOrder cmfOrder) {
        GrcLimitCarrier carrier = new GrcLimitCarrier();
        BeanUtils.copyProperties(cmfOrder, carrier);
        carrier.setBizProductCode(cmfOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.getKey()));
        carrier.setPaymentOrderNo(cmfOrder.getExtension().get(ExtensionKey.PAYMENT_ORDER_NO.getKey()));
        carrier.setMemberId(cmfOrder.getExtension().get(ExtensionKey.MEMBER_ID.getKey()));
        carrier.setSvaAccount(SvaAccountEnum.getByCode(cmfOrder.getExtension().get(ExtensionKey.SVA_STATUS.getKey())));
        return carrier;
    }

    private GrcLimitCarrier toCarrier(InstOrder instOrder) {
        GrcLimitCarrier carrier = new GrcLimitCarrier();
        BeanUtils.copyProperties(instOrder, carrier);
        carrier.setAfterPay(true);
        carrier.setBizProductCode(instOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.getKey()));
        carrier.setPaymentOrderNo(instOrder.getExtension().get(ExtensionKey.PAYMENT_ORDER_NO.getKey()));
        carrier.setMemberId(instOrder.getExtension().get(ExtensionKey.MEMBER_ID.getKey()));
        carrier.setSvaAccount(SvaAccountEnum.getByCode(instOrder.getExtension().get(ExtensionKey.SVA_STATUS.getKey())));
        if (carrier.getGmtCreate() == null) {
            carrier.setGmtCreate(new Date());
        }
        return carrier;
    }

}
