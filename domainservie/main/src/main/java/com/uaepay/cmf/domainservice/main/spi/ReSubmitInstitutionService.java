package com.uaepay.cmf.domainservice.main.spi;

import java.util.List;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.common.core.domain.exception.RouteChannelException;

/**
 * 
 * <p>废除原订单,重新路由后产生新订单</p>
 * <AUTHOR>
 * @version $Id: ReSubmitInstitutionService.java, v 0.1 2013-8-13 上午10:41:40 liumaoli Exp $
 */
public interface ReSubmitInstitutionService {

    /**
     * 重路由提交
     * @param orderNos 订单号列表
     * @param fundChannelCode 资金渠道编码
     * @return
     * @throws RouteChannelException
     */
    BaseResult reSubmit(List<String> orderNos, String fundChannelCode);

}
