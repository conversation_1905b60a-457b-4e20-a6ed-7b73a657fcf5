package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.enums.ChannelRequestExtensionMapping;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.engine.util.PropertyValueUtil;
import com.uaepay.cmf.common.domain.fundin.ebank.EBankChannelFundRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.schema.cmf.enums.AccessChannel;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>网银参数构建器</p>
 *
 * <AUTHOR>
 * @date NetBankApiRequestBuilder.java v1.0  2020-09-09 16:25
 */
@Service
public class NetBankApiRequestBuilder extends AbstractApiRequestBuilder<InstOrder, EBankChannelFundRequest> {

    @Override
    protected void buildCustomParam(InstOrder order, EBankChannelFundRequest request) {
        request.setPayMode(order.getPayMode());
        request.setAccessChannel(AccessChannel
                .getByCode(order.getExtension()
                        .get(ExtensionKey.ACCESS_CHANNEL.key)));
        request.setOrderDate(new Date());
        //设置会员, 商户信息
        for (ChannelRequestExtensionMapping mapping : ChannelRequestExtensionMapping
                .getMappingSet(EBankChannelFundRequest.class)) {
            PropertyValueUtil.setValue(request, mapping, order.getExtension()
                    .get(mapping.getExtensionKey()));
        }
    }

    @Override
    public EBankChannelFundRequest buildReq() {
        return new EBankChannelFundRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(FundChannelApiType.SIGN);
    }
}
