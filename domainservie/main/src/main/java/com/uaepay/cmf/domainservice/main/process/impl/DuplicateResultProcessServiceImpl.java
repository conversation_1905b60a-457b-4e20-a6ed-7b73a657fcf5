package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.exception.WrongOrderResultException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.enums.MonitorItem;
import com.uaepay.cmf.common.monitor.MonitorLog;
import com.uaepay.cmf.domainservice.main.convert.CmfFundResultConverter;
import com.uaepay.cmf.domainservice.main.convert.CmfResultConverter;
import com.uaepay.cmf.domainservice.main.process.DuplicateResultProcessService;
import com.uaepay.cmf.domainservice.main.process.MonitorService;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository;
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>处理结果，包括实时调用的结果，包括实时返回，或者异步回调</p>
 * <p>
 * 1. 核对返回的结果，金额是否匹配，账户信息是否一致等
 * 2. 调用状态模块更新状态
 *
 * <AUTHOR> won
 * @version $Id: ResultProcessImpl.java, v 0.1 2011-1-5 下午05:29:39 sean won Exp $
 */
@Slf4j
@Service("duplicateResultProcessService")
public class DuplicateResultProcessServiceImpl implements DuplicateResultProcessService {

    private static final Logger logger = LoggerFactory
            .getLogger(DuplicateResultProcessServiceImpl.class);

    @Resource
    private MonitorService monitorService;

    @Resource
    InstOrderResultRepository instOrderResultRepository;

    @Resource
    InstOrderRepository instOrderRepository;

    @Resource
    protected CmfOrderRepository cmfOrderRepository;

    @Resource
    private OrderLoaderService orderLoaderService;


    @Override
    public void validateNotProcessed(Long instOrderId) {
        InstOrder dbInstOrder = instOrderRepository.loadById(instOrderId, false);
        if (dbInstOrder == null) {
            return;
        }
        // 状态已处理
        if (CommunicateStatus.RECEIVED == dbInstOrder.getCommunicateStatus()
                && dbInstOrder.getStatus().isFinalStatus()) {
            log.info("DuplicateResultProcessService.validateNotProcessed.duplicate:{}", instOrderId);
            throw new WrongOrderResultException(ErrorCode.WRONG_ORDER_DUPLICATE_PROCESS);
        }
    }

    @Override
    public boolean checkNotProcessed(InstOrder instOrder) {
        if (instOrder == null) {
            return true;
        }
        InstOrder dbInstOrder = instOrderRepository.loadById(instOrder.getInstOrderId(), false);
        return CommunicateStatus.RECEIVED.equals(dbInstOrder.getCommunicateStatus())
                && (InstOrderStatus.SUCCESSFUL.equals(dbInstOrder.getStatus()) || InstOrderStatus.FAILURE
                .equals(dbInstOrder.getStatus()));
    }

    @Override
    public boolean duplicateRequestProcess(InstOrder dbInstOrder, InstOrderResult comingResult) {
        //不是重复请求
        if (checkNotProcessed(dbInstOrder) || comingResult == null) {
            return true;
        }

        //结果.  理论上，CommunicateStatus为"R数据已返回"，dbResult就有数据。
        InstOrderResult dbResult = instOrderResultRepository.loadRealResultByOrder(dbInstOrder
                .getInstOrderId());
        if (dbResult == null || dbResult.getStatus() == comingResult.getStatus()) {
            logger.warn("[重复请求处理]: dbResult:" + dbResult + ", 当前请求的result:" + comingResult);
            return true;
        } else {

            //重复请求，并且状态不一致，发送给监控.
            logger.warn("[重复请求处理，两次的状态不一致]: dbResult:" + dbResult + ", 当前请求的result:" + comingResult);

            String monitorMessage = buildDuplicateRequestWarning(dbResult, comingResult);
            monitorService.logMonitorEvent(new MonitorLog(dbInstOrder.getInstOrderNo(),
                    MonitorItem.STATUS_NOT_CONSISTENT, monitorMessage));
            return false;
        }
    }

    /**
     * 重复订单结果查询.
     *
     * @return
     */
    @Override
    public CmfFundResult queryDuplicateResult(String paymentSeqNo, String settlementId,
                                              BizType bizType) {
        //机构订单
        try {
            CmfOrder cmfOrder = cmfOrderRepository.loadByPaymentSeqNo(paymentSeqNo, settlementId);
            if (null == cmfOrder) {
                logger.warn("无法找到重复订单:" + paymentSeqNo);
                return CmfResultConverter.fail(null, InstOrderProcessStatus.FAILURE, bizType, null);
            }
            //机构订单结果
            List<InstOrder> instOrderList = instOrderRepository.loadByCmfSeqNo(cmfOrder.getOrderSeqNo());

            //如果没有机构订单,依据cmf订单返回
            if (CollectionUtils.isEmpty(instOrderList)) {
                return CmfFundResultConverter.convert(cmfOrder);
            }

            InstOrder instOrder = instOrderList.get(0);
            if (null == instOrder) {
                return CmfResultConverter.fail(null, InstOrderProcessStatus.FAILURE,
                        cmfOrder.getBizType(), null);
            }
            InstOrderResult instResult = instOrderResultRepository.loadRealResultByOrder(instOrder
                    .getInstOrderId());
            if (null == instResult) {
                return CmfResultConverter.fail(null, InstOrderProcessStatus.FAILURE, bizType, null);
            }
            instResult.setProcessStatus(InstOrderProcessStatus.SUCCESS);
            CmfFundResult result = CmfFundResultConverter.convert(cmfOrder, instOrder, instResult);
            result.setChannelPayNo(orderLoaderService.loadReturnOrderNo(instOrder, instResult));
            return result;
        } catch (Exception e) {
            logger.error("[订单查询]处理异常", e);
            return CmfResultConverter.fail(null, InstOrderProcessStatus.FAILURE, bizType, e);
        }
    }

    /**
     * 组装重复请求消息，发送给监控.
     *
     * @param dbResult
     * @param comingResult
     * @return
     */
    private String buildDuplicateRequestWarning(InstOrderResult dbResult,
                                                InstOrderResult comingResult) {
        StringBuilder msg = new StringBuilder();
        msg.append("重复回调CMF:{instOrderNo:" + dbResult.getInstOrderNo());
        msg.append(",orderType:" + dbResult.getBizType().getMessage());
        msg.append(",amount:" + dbResult.getRealAmount().getAmount());
        msg.append(",channelOrderNo:" + comingResult.getInstSeqNo());
        msg.append(",instOrderResult.status:" + dbResult.getStatus().getMessage());
        msg.append(",新请求instOrderResult.status:" + comingResult.getStatus().getMessage() + "}");
        return msg.toString();
    }
}
