package com.uaepay.cmf.domainservice.channel.router;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.TokenTypeEnum;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.payment.common.v2.enums.PayMode;

import java.util.Map;

/**
 * <p>系统配置服务</p>
 *
 * <AUTHOR>
 * @version $Id: ConfigurationService.java, v 0.1 2014-11-27 下午3:44:47 Administrator Exp $
 */
public interface ConfigurationService extends BasicConstant {


    /**
     * @param cmfOrder
     */
    void beforeRoute(CmfOrder cmfOrder);


    void beforeRoute(InstControlOrder controlOrder);

    TokenTypeEnum queryCardToken(PayMode payMode, Map<String, String> extMap);

    /**
     * 是否为拆单订单
     * @param instOrder
     * @return
     */
    boolean isSvaTransformOrder(InstOrder instOrder);
}
