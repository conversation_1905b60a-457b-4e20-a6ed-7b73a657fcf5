package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.institution.InstRefundOrder;
import com.uaepay.cmf.common.domain.query.QueryRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.common.util.DateUtil;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.uaepay.cmf.common.enums.FundChannelApiType.SINGLE_REFUND_QUERY;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date RefundQueryRequestBuilder.java v1.0  2020-09-09 18:24
 */
@Service
public class RefundQueryRequestBuilder extends AbstractApiRequestBuilder<InstRefundOrder, QueryRequest> {
    @Override
    protected void buildCustomParam(InstRefundOrder order, QueryRequest request) {
        request.setQueryTime(new Date());
        request.setInstOrderSubmitTime(order.getGmtBookingSubmit());
        request.setOriginalInstOrderNo(order.getFundinOrderNo());
        request.setOriginalInstOrderAmount(order.getFundinRealAmount());
        request.setOriginalInstSeqNo(order.getFundinInstSeqNo());
        request.setOriginalInstOrderSettleTime(DateUtil
                .parseDateLongFormat(order.getFundinDate()));
        request.setOriginalInstOrderSubmitTime(DateUtil
                .parseDateLongFormat(order.getFundinDate()));
    }

    @Override
    public QueryRequest buildReq() {
        return new QueryRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(SINGLE_REFUND_QUERY);
    }
}
