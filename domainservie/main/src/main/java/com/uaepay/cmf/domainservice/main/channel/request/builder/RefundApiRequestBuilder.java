package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.enums.ChannelRequestExtensionMapping;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstRefundOrder;
import com.uaepay.cmf.common.core.engine.util.PropertyValueUtil;
import com.uaepay.cmf.common.domain.refund.RefundRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.common.util.DateUtil;
import com.uaepay.schema.cmf.enums.YesNo;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <p>退款参数构建器</p>
 *
 * <AUTHOR>
 * @date RefundApiRequestBuilder.java v1.0  2020-09-09 16:52
 */
@Service
public class RefundApiRequestBuilder extends AbstractApiRequestBuilder<InstRefundOrder, RefundRequest> {
    @Override
    protected void buildCustomParam(InstRefundOrder order, RefundRequest request) {
        request.setOriginalOrderAmount(order.getFundinRealAmount());
        request.setOrignalInstOrderNo(order.getFundinOrderNo());
        request.setOrignalInstSeqNo(order.getFundinInstSeqNo());
        request.setOrignalOrderSettleTime(DateUtil.parseDateLongFormat(order.getFundinDate()));
        request.setOrignalOrderSubmitTime(DateUtil.parseDateLongFormat(order.getFundinDate()));
        // 是否为撤销
        request.setCancel(YesNo.YES.getCode().equals(order.getExtension().get(ExtensionKey.IS_CANCEL.key)));
        //设置机构和请求号信息
        for (ChannelRequestExtensionMapping mapping : ChannelRequestExtensionMapping
                .getMappingSet(RefundRequest.class)) {
            PropertyValueUtil.setValue(request, mapping,
                    order.getExtension().get(mapping.getExtensionKey()));
        }
    }

    @Override
    public RefundRequest buildReq() {
        return new RefundRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(FundChannelApiType.SINGLE_REFUND, FundChannelApiType.MANUAL_REFUND);
    }
}
