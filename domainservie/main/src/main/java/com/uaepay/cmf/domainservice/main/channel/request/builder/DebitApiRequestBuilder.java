package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderType;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.router.ChannelService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>入款参数构建器</p>
 *
 * <AUTHOR>
 * @date DebitApiRequestBuilder.java v1.0  2020-09-09 16:35
 */
@Service
public class DebitApiRequestBuilder extends AbstractApiRequestBuilder<InstOrder, ChannelFundRequest> {

    @Override
    protected void buildCustomParam(InstOrder order, ChannelFundRequest request) {
        putInstOrderToken(order);
    }


    @Override
    public ChannelFundRequest buildReq() {
        return new ChannelFundRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(FundChannelApiType.DEBIT, FundChannelApiType.PRE_DEBIT);
    }
}
