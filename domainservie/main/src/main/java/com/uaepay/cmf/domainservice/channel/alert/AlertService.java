package com.uaepay.cmf.domainservice.channel.alert;

import com.uaepay.cmf.common.core.domain.enums.MonitorType;
import com.uaepay.cmf.common.monitor.MonitorLog;


/**
 *
 * <p>短信&报警</p>
 *
 * <AUTHOR>
 * @version $Id: AlertService.java, v 0.1 2015-10-28 下午03:06:14 sean won Exp $
 */
public interface AlertService {

    /**
     * 报警+监控
     * @param log
     */
    void alertMonitorEvent(MonitorLog log, MonitorType monitorType);

}
