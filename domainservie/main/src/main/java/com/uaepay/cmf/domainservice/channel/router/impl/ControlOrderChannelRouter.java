package com.uaepay.cmf.domainservice.channel.router.impl;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.router.service.facade.domain.RouteRequest;
import com.uaepay.router.service.facade.domain.RouteResponse;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ControlOrderChannelRouter.java v1.0  2020-09-08 16:14
 */
@Service
public class ControlOrderChannelRouter extends AbstractChannelRouter<InstControlOrder> {

    @Resource
    private RouterClient routerClient;

    @Resource
    private OrderLoaderService orderLoaderService;

    @Override
    protected void beforeRoute(InstControlOrder controlOrder) {
        configurationService.beforeRoute(controlOrder);
    }

    @Override
    protected RouteResponse<ChannelVO> routeCustom(InstControlOrder controlOrder) {
        // 白名单路由
        String channel = getRouteChannel(controlOrder);
        if (StringUtils.isNotEmpty(channel)) {
            return routerClient.route(RouteUtil.getParam(channel, controlOrder.getApiType(), true));
        }
        RouteRequest req = convertRequest(controlOrder);
        return routerClient.route(req);
    }

    private String getRouteChannel(InstControlOrder order) {
        InstBaseOrder preOrder = orderLoaderService.loadPreOrder(order.getRequestType().name(),
                order.getPreRequestNo(), order.getPreSettlementId(),
                order.getExtension().get(ExtensionKey.SOURCE_ORDER.key));
        if (preOrder != null) {
            return preOrder.getFundChannelCode();
        }
        String whiteChannel = order.getExtension().get(ExtensionKey.WHITE_CHANNEL_CODE.key);
        return directRequestType(order.getRequestType()) ? whiteChannel : null;
    }

    private boolean directRequestType(ControlRequestType requestType) {
        return requestType == ControlRequestType.DOWNLOAD_STATEMENT
                || requestType == ControlRequestType.VOID_TRANSACTION
                || requestType == ControlRequestType.QUERY_BALANCE
                || requestType == ControlRequestType.NOTIFY
                || requestType == ControlRequestType.ADVANCE;
    }

    private RouteRequest convertRequest(InstControlOrder order) {

        RouteRequest request = new RouteRequest();
        BeanUtils.copyProperties(order, request);
        request.setClientId(CLIENT_ID);
        request.setBizProductCode(order.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.key));
        request.setBizType(FundChannelApiType.RETRIEVE_CARD_METADATA.equals(order.getApiType()) ?
                BizType.FUNDOUT.getCode() : BizType.FUNDIN.getCode());
        request.setCompanyOrPersonal(order.getExtension().get(ExtensionKey.COMPANY_OR_PERSONAL.key));
        request.setDbcr(order.getExtension().get(ExtensionKey.DBCR.key));
        if (order.getPayMode() != null) {
            request.setPayMode(order.getPayMode().getCode());
        }
        request.setRequestNo(StringUtils.isNotEmpty(order.getInstOrderNo()) ? order.getInstOrderNo() : order.getRequestNo());
        request.setRequestType(order.getApiType().getCode());
        request.setWhiteChannel(order.getExtension().get(ExtensionKey.WHITE_CHANNEL_CODE.key));
        return request;
    }
}
