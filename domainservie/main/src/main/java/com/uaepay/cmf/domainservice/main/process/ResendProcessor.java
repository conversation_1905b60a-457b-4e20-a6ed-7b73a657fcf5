package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;

/**
 * <p>补单流程[一级].</p>
 * <AUTHOR> won
 * @version $Id: ResendProcessor.java, v 0.1 2011-4-11 下午03:18:52 sean won Exp $
 */
public interface ResendProcessor extends BasicConstant {
    /**
     * 单笔补单.
     * @param instOrder
     * @return
     */
    InstOrderResult process(InstOrder instOrder);

    /**
     * 单笔补单.
     * @param instOrder
     * @return
     */
    InstControlOrderResult process(InstControlOrder instOrder);


}
