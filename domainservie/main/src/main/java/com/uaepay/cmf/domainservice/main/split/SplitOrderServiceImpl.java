package com.uaepay.cmf.domainservice.main.split;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.InstOrderCommunicateType;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.enums.ApiParamScene;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.common.util.money.Money;
import com.uaepay.router.service.facade.domain.channel.ChannelApiParamVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version SplitOrderServiceImpl.java 1.0 Created@2018-03-21 11:14 $
 */
@Service
public class SplitOrderServiceImpl implements SplitOrderService {
    private Logger logger = LoggerFactory
            .getLogger(SplitOrderServiceImpl.class);

    @Resource
    private RouterClient routerClient;

    /**
     * 订单补全/拆分
     *
     * @param instOrder
     * @return
     */
    @Override
    public List<InstOrder> orderComplement(CmfOrder cmfOrder, InstOrder instOrder,
                                           InstBaseOrder preOrder, ChannelVO channel) {
        // 1、批量通讯则设置归档模板
        if (instOrder.getCommunicateType() == InstOrderCommunicateType.BATCH) {
            instOrder.setArchiveBatchId(0L);
        }

        if (preOrder != null && !CollectionUtils.isEmpty(preOrder.getExtension())) {
            for (ChannelApiParamVO apiParam : channel.getChannelApi().getParamList()) {
                if (ApiParamScene.REQUEST_CHANNEL.getCode().equals(apiParam.getScene())
                        && YesNo.YES == YesNo.getByCode(apiParam.getIsOrigin())
                        && StringUtils.isNotEmpty(preOrder.getExtension().get(apiParam.getParamName()))) {
                    instOrder.getExtension().put(apiParam.getParamName(),
                            preOrder.getExtension().get(apiParam.getParamName()));
                }
            }
        }

        // 2、拆分（出款且金额大于限额）
        List<InstOrder> instOrderList = splitOrders(cmfOrder, instOrder);

        // 未被拆分直接使用原始订单
        if (CollectionUtils.isEmpty(instOrderList)) {
            return Collections.singletonList(instOrder);
        }

        // 3、设置提交机构订单号
        for (InstOrder tempOrder : instOrderList) {
            tempOrder.setInstOrderNo(routerClient.genOrderNo(channel.getChannelCode(), FundChannelApiType.getByCode(channel.getChannelApi().getApiType())));
        }

        return instOrderList;
    }

    private List<InstOrder> splitOrders(CmfOrder cmfOrder, InstOrder instOrder) {
        List<InstOrder> instOrderList = new ArrayList<>();
        if (instOrder.getBizType() != BizType.FUNDOUT) {
            return instOrderList;
        }

        // 获取渠道API最大限额
        Money maxAmountLimit = new Money("0", "AED");

//                (instOrder.getFundChannelApi().getAmountLimit() != null) ? instOrder
//                .getFundChannelApi().getAmountLimit().getMaxAmount() : null;

        // 根据限额拆分订单
        if (maxAmountLimit != null && maxAmountLimit.greaterThan(ZERO_MONEY)
                && instOrder.getAmount().greaterThan(maxAmountLimit)) {
            Money remainingAmount = new Money(instOrder.getAmount().toString(), instOrder.getAmount().getCurrency());
            while (remainingAmount.greaterThan(maxAmountLimit)) {
                Money splitOrderAmount = calculateSplitAmount(maxAmountLimit, remainingAmount);

                instOrderList.add(buildSplitOrder(cmfOrder, instOrder, splitOrderAmount));

                remainingAmount.subtractFrom(splitOrderAmount);
            }
            if (remainingAmount.greaterThan(ZERO_MONEY)) {
                instOrderList.add(buildSplitOrder(cmfOrder, instOrder, remainingAmount));
            }

            logger.info("cmfSeqNo{} 被拆分为{}笔订单", cmfOrder.getOrderSeqNo(), instOrderList.size());
        }
        return instOrderList;
    }

    private static Money calculateSplitAmount(Money maxAmountLimit, Money remainingAmount) {
        // 拆分订单金额上限
        if (maxAmountLimit.compareTo(SPLIT_MIN_AMOUNT) < 0) {
            return maxAmountLimit;
        }
        if (SPLIT_MIN_AMOUNT.greaterThan(remainingAmount.subtract(maxAmountLimit))) {
            return SPLIT_MIN_AMOUNT;
        }
        return maxAmountLimit;
    }

    /**
     * 组件拆分订单
     *
     * @param preInstOrder
     * @param amount
     * @return
     */
    private InstOrder buildSplitOrder(CmfOrder cmfOrder, InstOrder preInstOrder, Money amount) {
        try {
            InstOrder newOrder = preInstOrder.clone();
            newOrder.setAmount(amount);
            newOrder.setIsSplit(YesNo.YES);
            newOrder.setCmfSeqNo(cmfOrder.getOrderSeqNo());

            return newOrder;
        } catch (CloneNotSupportedException e) {
            throw new IllegalArgumentException("[" + preInstOrder.getCmfSeqNo() + "]克隆对象异常", e);
        }
    }

}
