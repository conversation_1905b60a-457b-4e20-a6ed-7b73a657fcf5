package com.uaepay.cmf.domainservice.main.retry.converter;

import java.util.HashMap;
import java.util.Map;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ChannelInfoExtKey;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.domainservice.main.retry.OrderRetryRequest;
import com.uaepay.cmf.domainservice.main.retry.OrderRetryType;
import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import org.apache.commons.lang3.StringUtils;
import com.uaepay.payment.common.v2.enums.PayMode;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version OrderRetryConverter.java 1.0 Created@2017-12-29 10:18 $
 */
public class OrderRetryConverter implements BasicConstant {

    private static final Map<String, String> defaultQueryConfig = new HashMap<>();

    private static final String FAST_QUERY_CONFIG = "10^2|30^5|99^15";
    private static final String MIDDLE_QUERY_CONFIG = "10^5|30^10|99^20";
    private static final String SLOW_QUERY_CONFIG = "5^5|10^15|99^30";

    static {
        defaultQueryConfig.put(PayMode.BALANCE.getCode(), MIDDLE_QUERY_CONFIG);
        defaultQueryConfig.put(PayMode.TRUSTCOLLECT.getCode(), FAST_QUERY_CONFIG);
        defaultQueryConfig.put(PayMode.QUICKPAY.getCode(), FAST_QUERY_CONFIG);
        defaultQueryConfig.put(PayMode.NETBANK.getCode(), SLOW_QUERY_CONFIG);
        defaultQueryConfig.put(DEFAULT, SLOW_QUERY_CONFIG);
    }

    private OrderRetryConverter() {

    }

    public static OrderRetryRequest convert(InstOrder instOrder) {
        OrderRetryRequest req = new OrderRetryRequest();
        req.setRetryTimes(instOrder.getRetryTimes() + 1);
        req.setRetryDateTime(instOrder.getGmtNextRetry());
        req.setFundChannelCode(instOrder.getFundChannelCode());
        req.setOrderId(instOrder.getInstOrderId());
        req.setRetryType(OrderRetryType.INST);
        if (instOrder.getFundChannelApi() != null) {
            req.setFundChannelApiType(instOrder.getApiType());
        }
        req.setRetryTimeConfig(getQueryConfig(ChannelHolder.get(), instOrder.getPayMode()));
        return req;
    }

    public static OrderRetryRequest convert(InstControlOrder controlOrder) {
        OrderRetryRequest req = new OrderRetryRequest();
        Integer retryTimes = controlOrder.getRetryTimes() == null ? 0 : controlOrder
                .getRetryTimes();
        req.setRetryTimes(retryTimes + 1);
        req.setRetryDateTime(controlOrder.getGmtNextRetry());
        req.setFundChannelCode(controlOrder.getFundChannelCode());
        req.setOrderId(controlOrder.getOrderId());
        req.setRetryType(OrderRetryType.CONTROL);
        if (controlOrder.getApiType() != null) {
            req.setFundChannelApiType(controlOrder.getApiType());
        }
        req.setRetryTimeConfig(getQueryConfig(ChannelHolder.get(),
                controlOrder.getPayMode()));
        return req;
    }

    public static OrderRetryRequest convert(InstBatchOrder batchOrder) {
        OrderRetryRequest req = new OrderRetryRequest();
        req.setRetryTimes(batchOrder.getQueryTimes() + 1);
        req.setRetryDateTime(batchOrder.getGmtNextRetry());
        req.setFundChannelCode(batchOrder.getFundChannelCode());
        req.setOrderId(batchOrder.getArchiveBatchId());
        req.setRetryType(OrderRetryType.BATCH);
        req.setFundChannelApiType(batchOrder.getApiType());
        req.setRetryTimeConfig(getQueryConfig(ChannelHolder.get(), batchOrder.getPayMode()));
        return req;
    }

    private static String getQueryConfig(ChannelVO channel, PayMode payMode) {
        // TODO: 待修改获取方式
        String queryConfig = ChannelUtil.getExtVal(channel.getExtList(), ChannelInfoExtKey.QUERY_CONFIG);
        if (StringUtils.isNotEmpty(queryConfig)) {
            return queryConfig;
        }
        return payMode != null && defaultQueryConfig.containsKey(payMode.getCode()) ? defaultQueryConfig
                .get(payMode.getCode()) : defaultQueryConfig.get(DEFAULT);
    }

}
