package com.uaepay.cmf.domainservice.main.agent;

import com.alibaba.fastjson.JSON;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.exception.ChannelValidateFailException;
import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.core.util.form.BankFormUtil;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.fundin.ebank.EBankChannelFundRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade;
import com.uaepay.cmf.fss.ext.integration.factory.ChannelSenderFactory;
import com.uaepay.common.util.money.Money;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

//import com.uaepay.mns.service.notify.enums.NotifyProtocol;

/**
 * <p>
 * Ebank渠道支付代理
 * </p>
 * <p>
 * 调用Ebank相关支付渠道先由此代理完成附加操作
 *
 * <AUTHOR> won
 * @version $Id: EBankChannelFundAgent.java, v 0.1 2011-10-2 上午11:31:52 sean won Exp $
 */
@Service("ebankChannelFundAgent")
public class EBankChannelFundAgent implements ChannelFundFacade, BasicConstant {
    private static final Logger logger = LoggerFactory.getLogger(EBankChannelFundAgent.class);

    private static final String EBANK_SEND_SUCC = "0";

    @Resource
    private ChannelSenderFactory channelSenderFactory;

    @Resource
    private BankFormUtil bankFormUtil;


    @Override
    public ChannelFundResult apply(String request) {
        logger.info("EBank支付请求:{}", request);

        EBankChannelFundRequest ebankRequest = JSON.parseObject(request, EBankChannelFundRequest.class);
        try {
            // 处理请求
            return process(ebankRequest);
        } catch (Exception e) {

            // 完整性破坏报警
            if (e instanceof ChannelValidateFailException) {
                // 存在风险
//                mnsNotifyClient.sendMsg(e.getMessage(), NotifyProtocol.SNS, "");
//                mnsNotifyClient.sendMsg(e.getMessage(), NotifyProtocol.MAIL, "");
            }

            logger.error("[" + ebankRequest.getInstOrderNo() + "]EBank请求异常", e);
            return buildFailFundResult(e.getMessage(), ebankRequest.getAmount());
        }
    }

    /**
     * 处理请求
     *
     * @param ebankRequest
     * @return
     */
    private ChannelFundResult process(EBankChannelFundRequest ebankRequest) {

        ChannelFundResult result = null;
        try {
            result = send(ebankRequest);
        } catch (CommunicateException e) {
            logger.error("[" + ebankRequest.getInstOrderNo() + "]发送渠道请求异常,重试", e);
            try {
                result = send(ebankRequest);
            } catch (CommunicateException e1) {
                logger.error("[" + ebankRequest.getInstOrderNo() + "]发送渠道请求二次异常,返回失败", e);
                result = buildFailFundResult(e.getMessage(), ebankRequest.getAmount());
            }
        }
        return result;
    }

    private ChannelFundResult send(EBankChannelFundRequest ebankRequest) {

        ChannelFundResult result = channelSenderFactory.applyFund(ebankRequest, null);

        // 临时解决方案
        if (StringUtils.isBlank(result.getExtension())) {
            throw new ChannelValidateFailException("渠道返回结果完整性校验失败,扩展字段为空,订单号:" + ebankRequest.getInstOrderNo());
        }

        Map<String, String> extMap = MapUtil.jsonToMap(result.getExtension());

        Money orgAmount = ebankRequest.getAmount();
        Money publicAmount = result.getRealAmount();

        String orgInstOrderNo = ebankRequest.getInstOrderNo();
        String publicInstOrderNo = result.getInstOrderNo();
        if (!StringUtils.equals(orgInstOrderNo, publicInstOrderNo) || orgAmount.compareTo(publicAmount) != 0) {
            logger.error(
                    "渠道返回结果完整性校验失败,原始订单号:" + orgInstOrderNo + ",主订单号" + publicInstOrderNo
                            + ",原始金额" + orgAmount + ",主订单金额" + publicAmount);
            throw new ChannelValidateFailException("渠道返回结果完整性校验失败,订单号:" + orgInstOrderNo);
        }

        extMap.put(ExtensionKey.FC_CODE.key, ebankRequest.getFundChannelCode());

        if (!result.isSuccess()) {
            return result;
//            return buildFailFundResult(logPrefix + "失败", ebankRequest.getAmount());
        } else {
//            result.setApiResultCode(EBANK_SEND_SUCC);
            result.setRealAmount(ebankRequest.getAmount());
            result.setApiType(FundChannelApiType.SIGN);
            result.setExtension(MapUtil.mapToJson(extMap));
            String bankForm = bankFormUtil.buildSignForm(result);
            extMap.put(ExtensionKey.PAGE_URL_FOR_SIGN.key, bankForm);
        }

        result.setExtension(MapUtil.mapToJson(extMap));

        return result;
    }

    /**
     * 将结果代码组装成渠道支付结果
     *
     * @param resultMsg
     * @return
     */
    private ChannelFundResult buildFailFundResult(String resultMsg, Money amount) {
        ChannelFundResult result = new ChannelFundResult(false, "-1", resultMsg);
        result.setApiResultCode("-1");
        result.setRealAmount(amount);
        result.setApiType(FundChannelApiType.SIGN);
        return result;
    }

}
