package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.channel.cards.service.facade.domain.BankVO;
import com.uaepay.cmf.domainservice.main.process.NotifyGrcService;
import com.uaepay.cmf.domainservice.main.repository.Notify3dsResultRepository;
import com.uaepay.cmf.fss.ext.integration.cards.CardsClient;
import com.uaepay.cmf.fss.ext.integration.grc.GrcClient;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import com.uaepay.cmf.service.facade.domain.grc.Query3dsRequest;
import com.uaepay.schema.cmf.enums.YesNo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date NotifyGrcServiceImpl.java v1.0  2020-10-16 15:36
 */
@Slf4j
@Service
public class NotifyGrcServiceImpl implements NotifyGrcService {

    @Resource
    private Notify3dsResultRepository notify3dsResultRepository;

    @Resource
    private GrcClient grcClient;

    @Value("${grc.3ds.notify.switch:N}")
    private String grc3dsNotifySwitch;

    @Resource
    private CardsClient cardsClient;

    @Override
    public void saveResultAndNotify(Notify3dsResult result) {
        long resultId = notify3dsResultRepository.store(result);
        if (!YesNo.YES.getCode().equals(grc3dsNotifySwitch)) {
            return;
        }
        grcClient.send3DsNotify(query3dsResult(resultId));
    }

    @Override
    public void notify3dsResult(String orderNo) {
        if (!YesNo.YES.getCode().equals(grc3dsNotifySwitch)) {
            return;
        }
        Notify3dsResult result = notify3dsResultRepository.queryBy3dsResult(orderNo);
        if (result != null) {
            grcClient.send3DsNotify(result);
        }
    }

    @Override
    public Notify3dsResult query3dsResult(Long resultId) {
        Notify3dsResult result = notify3dsResultRepository.queryBy3dsResultId(resultId);
        queryBankName(result);
        return result;
    }

    @Override
    public Notify3dsResult query3dsResult(Query3dsRequest request) {
        Notify3dsResult result = notify3dsResultRepository.queryBy3dsResult(request);
        queryBankName(result);

        return result;
    }

    private void queryBankName(Notify3dsResult result) {
        if (result == null) {
            return;
        }
        BankVO bank = cardsClient.queryByBankCode(result.getBankCode());
        if (bank != null) {
            result.setBankName(bank.getBankName());
        }
    }
}
