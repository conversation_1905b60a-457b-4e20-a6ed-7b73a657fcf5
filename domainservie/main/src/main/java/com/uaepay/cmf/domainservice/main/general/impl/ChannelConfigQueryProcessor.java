package com.uaepay.cmf.domainservice.main.general.impl;

import com.google.common.base.Joiner;
import com.uaepay.acs.service.facade.legacy.common.MerchantSetting;
import com.uaepay.cmf.common.core.domain.exception.ValidateException;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.fss.ext.integration.acs.AcsClient;
import com.uaepay.cmf.service.facade.domain.control.config.ChannelConfigQueryRequest;
import com.uaepay.cmf.service.facade.domain.control.config.ChannelConfigQueryResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ChannelConfigQueryProcessor.java v1.0
 */
@Service
public class ChannelConfigQueryProcessor extends GeneralProcessorTemplate<ChannelConfigQueryRequest, ChannelConfigQueryResponse> {

    @Resource
    private AcsClient acsClient;

    @Override
    protected String getServiceName() {
        return "ChannelConfigQueryProcessor";
    }

    @Override
    protected ChannelConfigQueryResponse createResponse() {
        ChannelConfigQueryResponse response = new ChannelConfigQueryResponse();
        response.success();
        return response;
    }

    @Override
    protected void commonValidate(ChannelConfigQueryRequest request) throws ValidateException {
        super.commonValidate(request);
        Assert.notNull(request.getExtension(), "extension为空");
        Map<String, String> extension = request.getExtension();
        String decryptType = extension.get(DECRYPT_TYPE);
        String hostApp = extension.get(HOST_APP);
        Assert.notNull(decryptType, "extension-decryptType为空");
        Assert.notNull(hostApp, "extension-hostApp为空");
    }

    @Override
    protected void process(ChannelConfigQueryRequest request, ChannelConfigQueryResponse response) {

        Map<String, String> extension = request.getExtension();
        String decryptType = extension.get(DECRYPT_TYPE).toUpperCase();
        String hostApp = extension.get(HOST_APP).toUpperCase();

        String paramKey = Joiner.on(SPLIT_TAG).join(request.getInstCode(), decryptType, hostApp);
        MerchantSetting merchantSetting = acsClient.getMerchantSetting(DEVICE_PAYMENT, paramKey);
        Map<String, String> paramValMap = convertVal2Map(merchantSetting);
        if (paramValMap.size() == 0) {
            response.fail(CONFIG_NOT_EXIST_CODE, CONFIG_NOT_EXIST_MSG);
            return;
        }
        response.setExtension(paramValMap);

    }

    private Map<String, String> convertVal2Map(MerchantSetting merchantSetting) {
        return MapUtil.safeJsonToMap(merchantSetting == null ? null : merchantSetting.getParamValue());
    }

    private static final String DEVICE_PAYMENT = "devicePayment";
    private static final String DECRYPT_TYPE = "decryptType";
    private static final String HOST_APP = "hostApp";
    private static final String CONFIG_NOT_EXIST_CODE = "devicePay.config.notExist";
    private static final String CONFIG_NOT_EXIST_MSG = "devicePay渠道配置缺失";

}
