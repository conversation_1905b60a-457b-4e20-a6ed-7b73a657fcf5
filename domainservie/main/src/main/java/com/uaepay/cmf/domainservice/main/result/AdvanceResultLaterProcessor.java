package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

import static com.uaepay.cmf.common.core.domain.enums.ExtensionKey.TRANSACTION_TYPE;

/**
 * <p>推进结果处理</p>
 *
 * <AUTHOR>
 * @date 2022/6/29
 */
@Slf4j
@Component
public class AdvanceResultLaterProcessor implements ResultLaterProcessor {

    @Resource
    protected InstOrderRepository instOrderRepository;


    @PostConstruct
    public void init() {
        ResultLaterProcessorEnum.ADVANCE.register(this);
    }

    @Override
    public Result<?> process(InstControlOrder instControlOrder, InstControlOrderResult instControlResult) {
        log.info("推进结果处理-控制单:{},控制单结果:{}", instControlOrder, instControlResult);
        if (InstOrderResultStatus.SUCCESSFUL != instControlResult.getStatus()
                || notPreAuthorizeUpdate(instControlResult)) {
            return Result.ofNothing();
        }

        InstOrder preInstOrder = instOrderRepository.loadByNo(instControlOrder.getPreInstOrderNo());

        preInstOrder.getExtension().put("updateRelationControlOrder", instControlOrder.getInstOrderNo());

        //最终更新金额为 controlOrder的金额
        Money updateAmount = instControlOrder.getAmount();
        String extensionStr = JsonUtil.mapToString(preInstOrder.getExtension());
        log.info("预授权更新结果处理,修改原机构单金额,机构单号:{},原金额:{},更新后金额:{}", instControlOrder.getPreInstOrderNo(), preInstOrder.getAmount(), updateAmount);
        int count = instOrderRepository.updateAmountAndExtension(preInstOrder.getInstOrderId(), updateAmount, extensionStr);

        if (count == 1) {
            return Result.ofSuccess();
        }

        return Result.ofSuccess();
    }

    private boolean notPreAuthorizeUpdate(InstControlOrderResult instControlResult) {
        String transactionType = instControlResult.getExtension().getOrDefault(TRANSACTION_TYPE.getKey(), "");
        return !Objects.equals("reservation_update", transactionType);
    }


}
