package com.uaepay.cmf.domainservice.main.retry;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version OrderRetryType.java 1.0 Created@2017-12-28 15:58 $
 */
public enum OrderRetryType {
    // 订单重试类型
    INST("I", "机构订单"),
    CONTROL("C","控制订单"),
    BATCH("B", "批量订单");

    private String code;
    private String description;

    OrderRetryType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
    
}
