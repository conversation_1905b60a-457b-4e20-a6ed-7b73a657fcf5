package com.uaepay.cmf.domainservice.channel.router.impl;

import com.uaepay.cmf.common.core.domain.router.ApiRouteParam;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.router.service.facade.domain.RouteResponse;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>渠道路由服务</p>
 *
 * <AUTHOR>
 * @date ChannelApiRouter.java v1.0  2020-09-08 12:06
 */
@Service
public class ChannelApiRouter extends AbstractChannelRouter<ApiRouteParam> {

    @Resource
    private RouterClient routerClient;

    @Override
    protected void beforeRoute(ApiRouteParam request) {
        // do nothing
    }

    @Override
    protected RouteResponse<ChannelVO> routeCustom(ApiRouteParam request) {
        return routerClient.route(request);
    }
}
