package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.dal.daointerface.InstOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.UniqueOrderDO;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;

import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.process.ResendProcessor;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.cmf.service.facade.domain.refund.RetryRefundRequest;
import com.uaepay.common.util.DateUtil;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 11/12/2023 15:01
 */
@Service("retryRefundProcessor")
public class RetryRefundProcessor extends GeneralProcessorTemplate<RetryRefundRequest, CommonResponse> {

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private CmfOrderRepository cmfOrderRepository;

    @Resource
    protected SysConfigurationHolder sysConfigurationHolder;

    @Resource
    private RouterClient routerClient;

    @Resource
    private ResendProcessor resendProcessor;

    @Resource
    private ChannelApiRouter channelApiRouter;

    @Resource
    private InstOrderDAO instOrderDAO;

    private static final String RETRY_NAME = "retryName";
    private static final String OLD_INST_ORDER_NO = "oldInstOrderNo";

    public static final ThreadLocal<InstOrder> threadLocal = new ThreadLocal<>();

    @Override
    protected String getServiceName() {
        return "RetryRefundProcessor";
    }

    @Override
    protected CommonResponse createResponse() {
        return new CommonResponse();
    }

    protected void businessValidate(RetryRefundRequest req) {
        InstOrder instOrder = instOrderRepository.loadByNo(req.getInstOrderNo());

        assertNotNull(instOrder, "instOrder not exist");
        assertIsTrue(BizType.REFUND.equals(instOrder.getBizType()), "instOrder bizType not REFUND");
        assertIsTrue(InstOrderStatus.IN_PROCESS.equals(instOrder.getStatus()), "instOrder status not I");
        assertIsTrue(canSendToChannel(instOrder), "refund total amount can't be more than the original order amount");
        threadLocal.set(instOrder);
    }

    @Override
    protected void process(RetryRefundRequest req, CommonResponse commonResponse) {
        InstOrder instOrder = threadLocal.get();
        try (ChannelCarrier carrier = channelApiRouter.route(RouteUtil.getParam(instOrder.getFundChannelCode(),
                instOrder.getApiType()))) {

            InstOrder sendOrder = instOrderRepository.loadById(instOrder.getInstOrderId(), false);
            Map<String, String> extension = sendOrder.getExtension();
            extension.put(RETRY_NAME, req.getOperator());
            if (req.getNeedNewInstOrderNo()) {
                extension.put(OLD_INST_ORDER_NO, sendOrder.getInstOrderNo());
                String newInstOrderNo = routerClient.genOrderNo(instOrder.getFundChannelCode(), instOrder.getApiType());
                UniqueOrderDO uniqueOrderDO = instOrderDAO.loadUniqueOrderByNo(sendOrder.getInstOrderNo());
                uniqueOrderDO.setInstOrderNo(newInstOrderNo);
                instOrderDAO.updateUniqueOrder(uniqueOrderDO);
                sendOrder.setInstOrderNo(newInstOrderNo);
            }
            sendOrder.setExtension(extension);

            //将订单置为待发送,
            //2024-01-31 要解决lean 退款查询不到结果数据的问题，把 GMT_BOOKING_SUBMIT 的时间在mapper文件里面也改成当前时间
            int count = instOrderRepository.updateRetryDataWithPreStatus(sendOrder,
                    CommunicateStatus.AWAITING, instOrder.getCommunicateStatus());

            if (count == 0) {
                logger.info("QueryResultProcessor.updateStatus.Fail");
                commonResponse.setApplyStatus(ApplyStatusEnum.FAIL);
                return;
            }
            InstOrderResult result = resendProcessor.process(sendOrder);
            convertResult(result, commonResponse);
        } catch (Exception e) {
            logger.error("RetryRefundProcessor.error:", e);
            commonResponse.setApplyStatus(ApplyStatusEnum.ERROR);
        } finally {
            threadLocal.remove();
        }
    }

    private void convertResult(InstOrderResult result, CommonResponse commonResponse) {
        commonResponse.setApplyStatus(ApplyStatusEnum.SUCCESS);
        commonResponse.setCode(result.getProcessStatus().getCode());
        commonResponse.setUnityResultCode(result.getStatus().getCode());
    }

    /**
     * 以下方法从 SingleSendTask copy过来
     * 增加了金额校验
     *
     * @param instOrder
     * @return
     */
    private boolean canSendToChannel(InstOrder instOrder) {

        // 根据instOrderId获取cmf订单，获取ORGI_SETTLEMENT_ID
        CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(instOrder.getCmfSeqNo(), false);

        // 理论上退款订单的cmfOrder和orgiSettlementId不为空，出现为空的，直接发到渠道
        if (cmfOrder == null || StringUtils.isBlank(cmfOrder.getOrgiSettlementId())) {
            return true;
        }

        CmfOrder oriCmfOrder = cmfOrderRepository.loadByPaymentSeqNo(cmfOrder.getOrgiPaymentSeqNo(), cmfOrder.getOrgiSettlementId());
        Assert.isTrue(CmfOrderStatus.SUCCESSFUL.equals(oriCmfOrder.getStatus()), "original order not success");

        // 通过orgiSettlementId加载处理中的cmf订单，排除自己
        List<Long> ignoreInstIds = new ArrayList<>();
        ignoreInstIds.add(instOrder.getInstOrderId());
        List<CmfOrder> cmfOrders = cmfOrderRepository.loadByOrgiSettlementId(cmfOrder.getOrgiSettlementId(),
                DateUtil.getDayBegin(cmfOrder.getGmtCreate()), ignoreInstIds);

        // 如果不存在相同原始入款订单的部分退款订单，则返回true
        if (CollectionUtils.isEmpty(cmfOrders)) {
            return oriCmfOrder.getAmount().compareTo(instOrder.getAmount()) >= 0;
        }

        Money totalRefund = instOrder.getAmount();
        // 存在其他订单，总金额相加，和原订单金额比较
        for (CmfOrder o : cmfOrders) {
            totalRefund = totalRefund.add(o.getAmount());
        }

        return oriCmfOrder.getAmount().compareTo(totalRefund) >= 0;
    }

    /**
     * 鉴于上游的退款订单不允许失败，不用再判断原订单的其他退款订单是否有效
     * @param cmfSeqNo
     * @return
     */
    @Deprecated
    private boolean checkInstOrder(String cmfSeqNo) {

        if (cmfSeqNo == null) {
            return true;
        }

        // 加载机构订单
        List<InstOrder> instOrderList = instOrderRepository.loadByCmfSeqNo(cmfSeqNo);
        if (instOrderList == null) {
            return true;
        }

        instOrderList = instOrderList.stream().filter(v -> StringUtils.isNotBlank(v.getInstOrderNo())).collect(Collectors.toList());

        Assert.isTrue(instOrderList.size() == 1, "记录关联不正确");

        InstOrder instOrder = instOrderList.get(0);

        // 被检查的其他部分退款订单状态不为I，跳过
        if (instOrder.getStatus() != InstOrderStatus.IN_PROCESS) {
            return true;
        }

        // 尚未发送，跳过
        if (instOrder.getCommunicateStatus() == CommunicateStatus.AWAITING) {
            return true;
        }

        // 已发送，状态为处理中，但发送时间已超过阈值的，跳过
        int minutesIgnore = sysConfigurationHolder.loadConfigureOrDefault(REFUND_ORDER_IGNORE_MINUTES,
                DEFAULT_REFUND_ORDER_IGNORE_MINUTES);
        if (DateUtil.addMinutes(instOrder.getGmtCreate(), minutesIgnore).before(new Date())) {
            return true;
        }

        // 所有可以发送条件都不满足，跳出
        return false;
    }
}
