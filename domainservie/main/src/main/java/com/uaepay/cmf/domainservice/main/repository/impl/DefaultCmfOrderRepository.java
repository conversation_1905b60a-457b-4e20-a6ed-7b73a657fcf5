package com.uaepay.cmf.domainservice.main.repository.impl;

import com.uaepay.cmf.common.core.dal.daointerface.CmfOrderDAO;
import com.uaepay.cmf.common.core.dal.daointerface.CmfRequestDAO;
import com.uaepay.cmf.common.core.dal.daointerface.PaymentNotifyLogDAO;
import com.uaepay.cmf.common.core.dal.dataobject.CmfOrderDO;
import com.uaepay.cmf.common.core.dal.dataobject.CmfRequestDO;
import com.uaepay.cmf.common.core.dal.dataobject.PaymentNotifyLogDO;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.CmfRequest;
import com.uaepay.cmf.common.core.domain.PaymentNotifyLog;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderConfirmStatus;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.NotifyStatus;
import com.uaepay.cmf.common.core.domain.exception.AppRuntimeException;
import com.uaepay.cmf.common.core.domain.exception.DuplicateKeyException;
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator;
import com.uaepay.cmf.common.core.engine.generator.SequenceNameEnum;
import com.uaepay.cmf.common.core.util.log.LogUtil;
import com.uaepay.cmf.domainservice.main.convert.CmfOrderConverter;
import com.uaepay.cmf.domainservice.main.convert.CmfRequestConverter;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.service.facade.domain.query.OrderNoQueryRequest;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>cmf订单仓储默认实现</p>
 *
 * <AUTHOR>
 * @version $Id: DefaultCmfOrderRepository.java, v 0.1 2012-8-3 上午10:11:19 fuyangbiao Exp $
 */
@Repository("cmfOrderRepository")
public class DefaultCmfOrderRepository implements CmfOrderRepository {
    private static final Logger logger = LoggerFactory
            .getLogger(DefaultCmfOrderRepository.class);

    private static final String defaultSettlementId = "0";
    @Resource
    private CmfOrderDAO cmfOrderDAO;
    @Resource
    private CmfRequestDAO requestDAO;
    @Resource
    private PaymentNotifyLogDAO paymentNotifyLogDAO;
    @Resource
    private PrimaryKeyGenerator primaryKeyGenerator;
    /**
     * 事务模板
     */
    @Resource
    private TransactionTemplate cmfTransactionTemplate;

    @Value("${cmf.sharding.switchDate:20991230}")
    private String switchDate;
    @Value("${cmf.sharding.closeNewDb:N}")
    private String closeNewDb;

    @Override
    public String store(CmfOrder order) throws DuplicateKeyException {
        CmfRequest req = convertCmfRequest(order);
        long startMillis = System.currentTimeMillis();
        StringBuilder logAccum = new StringBuilder();
        logAccum.append("cmfOrder.storeOrUpdate.costTime:--");
        // 判断是否重复提交
        if (!canStore(req, logAccum, startMillis)) {
            throw new DuplicateKeyException("支付流水号重复");
        }
        cmfOrderDAO.insert(CmfOrderConverter.convert(order));
        logAccum.append("],cmfOrder.insert.cost:[")
                .append(System.currentTimeMillis() - startMillis);
        LogUtil.info(logAccum.toString(), startMillis, System.currentTimeMillis());
        return order.getOrderSeqNo();
    }

    private CmfRequest convertCmfRequest(CmfOrder order) {
        CmfRequest req = new CmfRequest();
        req.setPaymentSeqNo(order.getPaymentSeqNo());
        req.setSettlementId(getSettlementId(order.getSettlementId()));
        return req;
    }

    /**
     * 更新cmf订单状态
     *
     * @param cmfOrder
     */
    @Override
    public Boolean updateCmfOrderStatus(final CmfOrder cmfOrder, final CmfOrderStatus preStatus) {
        //不是最终状态,不更新状态,只更新审核状态
        if (preStatus.equals(cmfOrder.getStatus())) {
            updateConfirmStatusById(cmfOrder.getConfirmStatus(), cmfOrder.getOrderSeqNo());
            return true;
        }
        return cmfTransactionTemplate.execute(status -> {
            CmfOrder dbCmfOrder = loadByCmfSeqNo(cmfOrder.getOrderSeqNo(), true);

            if (preStatus.equals(dbCmfOrder.getStatus())) {
                return updateStatusAndConfirmStatusById(cmfOrder.getStatus(), preStatus,
                        cmfOrder.getConfirmStatus(), cmfOrder.getOrderSeqNo()) > 0;
            } else if (!dbCmfOrder.getStatus().equals(cmfOrder.getStatus())) {
                logger.error("cmf订单[" + cmfOrder.getPaymentSeqNo() + "]状态跃迁异常,原状态:"
                        + dbCmfOrder.getStatus() + ",新状态" + cmfOrder.getStatus());
                throw new AppRuntimeException("支付流水号[" + cmfOrder.getPaymentSeqNo() + "],原状态:"
                        + dbCmfOrder.getStatus() + ",新状态"
                        + cmfOrder.getStatus());
            }
            return false;
        });
    }

    @Override
    public CmfOrder loadByPaymentSeqNo(String paymentSeqNo, String settlementId) {
        //去全表扫描问题
        if (StringUtils.isEmpty(paymentSeqNo)) {
            return null;
        }
        CmfOrderDO cmfOrderDO = cmfOrderDAO.loadByPaymentNo(paymentSeqNo, settlementId);
        return CmfOrderConverter.convert(cmfOrderDO);
    }

    @Override
    public CmfOrder loadByCmfSeqNo(String cmfSeqNo, boolean isLock) {
        CmfOrderDO cmfOrderDO = isLock ? cmfOrderDAO.lockedById(cmfSeqNo) : cmfOrderDAO
                .loadById(cmfSeqNo);
        return CmfOrderConverter.convert(cmfOrderDO);
    }

    @Override
    public Long storeNotifyLog(PaymentNotifyLog log) {
        PaymentNotifyLogDO logDO = new PaymentNotifyLogDO();
        logDO.setNotifyLogId(Long.valueOf(primaryKeyGenerator
                .generateKey(SequenceNameEnum.PAYMENT_NOTIFY_LOG)));
        logDO.setChannelSeqNo(log.getChannelSeqNo());
        logDO.setNotifyResult(log.getNotifyResult().getCode());
        if (log.getMemo() != null && !"".equals(log.getMemo()) && log.getMemo().length() > 64) {
            logDO.setMemo(log.getMemo().substring(0, 64));
        }
        paymentNotifyLogDAO.insert(logDO);
        log.setNotifyLogId(logDO.getNotifyLogId());
        return log.getNotifyLogId();
    }

    @Override
    public int updatePaymentNotifyStatusById(NotifyStatus paymentNotifyStatus, String cmfSeqNo) {
        return cmfOrderDAO.updatePaymentNotifyStatusById(paymentNotifyStatus.getCode(), cmfSeqNo);
    }

    @Override
    public boolean canStore(CmfRequest request, StringBuilder logAccum, Long startMillis)
            throws DuplicateKeyException {
        CmfRequestDO reqdo = CmfRequestConverter.convertToRequestDO(request);

        try {

            insert2OldDb(reqdo);
            logAccum.append("oldCmfRequest.insert.cost:[").append(
                    System.currentTimeMillis() - startMillis);
            if (!YesNo.YES.getCode().equals(closeNewDb)) {
                requestDAO.insert(reqdo);
            }
            logAccum.append("], cmfRequest.insert.cost:[").append(
                    System.currentTimeMillis() - startMillis);
            return true;
        } catch (DataIntegrityViolationException e) {
            // 支付流水重复
            CmfRequestDO dbdo = requestDAO.loadById(request.getPaymentSeqNo(),
                    request.getSettlementId());
            CmfRequest dbreq = CmfRequestConverter.convertToRequest(dbdo);

            // 如果能重复则锁定
            if (dbreq.isCanRetry()) {
                requestDAO.updateStatusById("N", request.getPaymentSeqNo(),
                        request.getSettlementId());
                return true;
            }
            throw new DuplicateKeyException("支付流水号重复");
        }
    }

    private void insert2OldDb(CmfRequestDO cmfRequestDO) {
        // 判断支付时间在切换时间之前,则需要校验
        //DbRouter.isNewOrder(cmfRequestDO.getPaymentSeqNo()) 不再校验是否新订单
//        if (CommonUtil.isPaymentBeforeDate(cmfRequestDO.getPaymentSeqNo(), switchDate)) {
        requestDAO.insert2OldDb(cmfRequestDO);
//        }
    }

    @Override
    public int updateConfirmStatusById(CmfOrderConfirmStatus confirmStatus, String cmfSeqNo) {
        return cmfOrderDAO.updateConfirmStatusById(confirmStatus.getCode(), cmfSeqNo);
    }

    @Override
    public int updateStatusAndConfirmStatusById(CmfOrderStatus cmfOrderStatus,
                                                CmfOrderStatus preStatus,
                                                CmfOrderConfirmStatus confirmStatus, String cmfSeqNo) {
        return cmfOrderDAO.updateStatusAndConfirmStatusById(cmfOrderStatus.getCode(),
                preStatus.getCode(), confirmStatus.getCode(), cmfSeqNo);
    }

    /**
     * 依据settlementId 转换. 为空的时候返回默认值 ,否则直接返回
     *
     * @param settlementId
     * @return
     */
    private String getSettlementId(String settlementId) {
        return StringUtils.isEmpty(settlementId) ? defaultSettlementId : settlementId;
    }

    public void setSwitchDate(String switchDate) {
        this.switchDate = switchDate;
    }

    public void setCloseNewDb(String closeNewDb) {
        this.closeNewDb = closeNewDb;
    }

    @Override
    public List<CmfOrder> loadByOrgiSettlementId(
            String orgiSettlementId, Date dateStart, List<Long> ignoreInstOrderIds
    ) {

        if (orgiSettlementId == null || dateStart == null) {
            //避免全表扫描
            return null;
        }

        return CmfOrderConverter.convert(cmfOrderDAO.loadByOrgiSettlementId(orgiSettlementId,
                dateStart,
                ignoreInstOrderIds));
    }

    @Override
    public CmfOrder loadByPaymentOrderNo(OrderNoQueryRequest request) {
        return CmfOrderConverter.convert(cmfOrderDAO.loadByPaymentOrderNo(request.getPaymentOrderNo(), request.getGmtStart(), request.getGmtEnd()));
    }
}
