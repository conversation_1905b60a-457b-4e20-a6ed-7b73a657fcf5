package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.fss.ext.common.domain.authenticate.ChannelRetrieveCardMetadataRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date AuthApiRequestBuilder.java v1.0  2020-09-09 17:58
 */
@Service
public class RetrieveCardMetadataRequestBuilder extends AbstractApiRequestBuilder<InstControlOrder, ChannelRetrieveCardMetadataRequest> {

    @Override
    protected void buildCustomParam(InstControlOrder order, ChannelRetrieveCardMetadataRequest request) {
        BeanUtils.copyProperties(order, request);
        request.setCardNo(order.getExtension().get(ExtensionKey.CARD_NO.key));
    }

    @Override
    public ChannelRetrieveCardMetadataRequest buildReq() {
        return new ChannelRetrieveCardMetadataRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(FundChannelApiType.RETRIEVE_CARD_METADATA);
    }
}
