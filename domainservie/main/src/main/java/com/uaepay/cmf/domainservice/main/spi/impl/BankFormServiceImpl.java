package com.uaepay.cmf.domainservice.main.spi.impl;

import com.uaepay.cmf.common.core.domain.config.SysConfiguration;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.exception.ChannelValidateFailException;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.vo.VerifyResponseContent;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigureKey;
import com.uaepay.cmf.common.domain.ChannelControlResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.domainservice.channel.holder.VerifySignHolder;
import com.uaepay.cmf.domainservice.channel.router.ChannelService;
import com.uaepay.cmf.domainservice.main.spi.BankFormService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version BankFormServiceImpl.java 1.0 Created@2017-02-07 11:05 $
 */
@Service
public class BankFormServiceImpl implements BankFormService, BasicConstant {
    private Logger logger = LoggerFactory
            .getLogger(BankFormServiceImpl.class);

    @Resource
    private SysConfigurationHolder sysConfigurationHolder;
    @Resource
    private ChannelService channelService;
    @Resource(name = "memoryCacheClient")
    private CacheClient cacheClient;

    @Override
    public void processControlResult(InstControlOrder instControlOrder, ChannelResult channelResult) {
        processResponseAndHeader(channelResult);
        // 缓存3ds2.0渠道form
        cache3ds2Form(instControlOrder, channelResult);
        if (!(channelResult instanceof ChannelControlResult)) {
            return;
        }
        ChannelControlResult result = (ChannelControlResult) channelResult;
        if (StringUtils.isEmpty(result.getInstUrl())) {
            return;
        }
        //TODO:控制类网银订单客户端数据篡改的风险校验

        if (StringUtils.isBlank(result.getExtension())) {
            throw new ChannelValidateFailException("渠道返回结果完整性校验失败,扩展字段为空,订单号:"
                    + result.getInstOrderNo());
        }
        processCommon(channelResult, instControlOrder.getInstOrderNo());

    }


    @Override
    public void processInstResult(InstOrder instOrder, ChannelFundResult result) {
        processResponseAndHeader(result);
        // 缓存3ds2.0渠道form
        cache3ds2Form(instOrder, result);
        if (StringUtils.isEmpty(result.getInstUrl())) {
            return;
        }
        processCommon(result, instOrder.getInstOrderNo());
    }

    private void cache3ds2Form(InstBaseOrder instOrder, ChannelResult result) {
        if (channelService.is3ds2Channel(instOrder.getFundChannelCode())) {
            Map<String, String> extMap = MapUtil.jsonToMap(result.getExtension());
            if (extMap != null && extMap.containsKey(ExtensionKey.FORM_3DS2.getKey()) && extMap.containsKey(ExtensionKey.INST_ORDER_TOKEN.getKey())) {
                cacheClient.put(CacheType.FORM, extMap.get(ExtensionKey.INST_ORDER_TOKEN.getKey()), extMap.get(ExtensionKey.FORM_3DS2.getKey()), 900);
                extMap.remove(ExtensionKey.INST_ORDER_TOKEN.getKey());
                extMap.remove(ExtensionKey.FORM_3DS2.getKey());
                result.setExtension(MapUtil.mapToJson(extMap));
            }
        }
    }

    private void processCommon(ChannelResult result, String instOrderNo) {

        Map<String, String> extMap = MapUtil.jsonToMap(result.getExtension());

        String fundChannelCode = "";
        String instUrl = "";
        if (result instanceof ChannelFundResult) {
            ChannelFundResult fundResult = (ChannelFundResult) result;
            fundChannelCode = fundResult.getFundChannelCode();
            instUrl = fundResult.getInstUrl();
        } else if (result instanceof ChannelControlResult) {
            ChannelControlResult controlResult = (ChannelControlResult) result;
            fundChannelCode = controlResult.getFundChannelCode();
            instUrl = controlResult.getInstUrl();
        } else {
            return;
        }

        //为了Transfomer使用.
        extMap.put(ExtensionKey.FC_CODE.key, fundChannelCode);

        String bankForm = "";

        if (!result.isSuccess()) {
            logger.info("表单生成失败");
            return;
        } else {
//            result.setApiResultCode(EBANK_SEND_SUCC);
            result.setExtension(MapUtil.mapToJson(extMap));
            // 判断是否需要表单组装,不需要则直接使用渠道返回URL
            bankForm = needSignForm(fundChannelCode) ? buildSignForm(instUrl, result.getExtension())
                    : instUrl;
        }
        extMap.put(ExtensionKey.PAGE_URL_FOR_SIGN.key, bankForm);

        result.setExtension(MapUtil.mapToJson(extMap));
    }


    private void processResponseAndHeader(ChannelResult result) {
        Map<String, String> extMap = MapUtil.jsonToMap(result.getExtension());
        if (extMap == null) {
            return;
        }
        if (extMap.get(HEADER_MAP) != null || StringUtils.isNotEmpty(extMap.get(RESPONSE_DATE))) {
            Map<String, String> headerMap = StringUtils.isNotEmpty(extMap.get(HEADER_MAP)) ? MapUtil.jsonToMap(extMap.get(HEADER_MAP)) : null;
            String responseData = extMap.get(RESPONSE_DATE);
            VerifySignHolder.set(VerifyResponseContent.builder().responseContent(responseData).headerMap(headerMap).build());
            extMap.remove(HEADER_MAP);
            extMap.remove(RESPONSE_DATE);
            result.setExtension(MapUtil.mapToJson(extMap));
        }
    }

    /**
     * 是否需要signForm
     *
     * @return
     */
    private boolean needSignForm(String fundChannelCode) {
        if (StringUtils.isEmpty(fundChannelCode)) {
            return true;
        }
        SysConfiguration config = sysConfigurationHolder
                .getConfiguration(SysConfigureKey.SIGN_FORM_ONLY_URL);
        if (null == config || StringUtils.isEmpty(config.getAttrValue())) {
            return true;
        }

        String[] fundChannelCodes = config.getAttrValue().split(CHAR_COMMA);
        for (String fcCode : fundChannelCodes) {
            if (fundChannelCode.equals(fcCode)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 组装form/Html信息.
     *
     * @param instUrl
     * @return
     */
    private String buildSignForm(String instUrl, String extension) {
        StringBuilder accum = new StringBuilder();
        Map<String, String> extMap = StringUtils.isNotBlank(extension) ? MapUtil
                .jsonToMap(extension) : null;
        accum.append("<form id='frmBankID' name='frmBankName' method='post'")
                .append(buildEnctypeField(extMap)).append(" action='").append(instUrl).append("'>");
        if (extMap != null) {

            for (Map.Entry<String, String> entry : extMap.entrySet()) {
                if (ExtensionKey.FC_CODE.key.equals(entry.getKey())) {
                    continue;
                }

                if (ExtensionKey.INST_AMOUNT.key.equals(entry.getKey())) {
                    continue;
                }

                if (ExtensionKey.INST_ORDER_NO.key.equals(entry.getKey())) {
                    continue;
                }

                if (ExtensionKey.EBANK_CHARSET.key.equals(entry.getKey())) {
                    continue;
                }

                accum.append("<input type='hidden' name='");
                accum.append(entry.getKey());
                accum.append("'  value='");
                accum.append(entry.getValue());
                accum.append("' />");
            }
        }
        accum.append("</form>");
        logger.debug("银行表单跳转,form信息【{}】", accum.toString());
        return accum.toString();
    }

    private static String buildEnctypeField(Map<String, String> extMap) {
        if (extMap == null || !extMap.containsKey(ENCTYPE)) {
            return EMPTY_STRING;
        }
        return new StringBuilder().append(" ").append(ENCTYPE).append(CHAR_EQUAL)
                .append(extMap.get(ENCTYPE)).toString();
    }

}
