package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.cmf.common.core.domain.exception.CmfBizException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.domainservice.main.convert.CmfRequestConverter;
import com.uaepay.cmf.domainservice.main.convert.InstControlOrderResultConverter;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthRequest;
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date AuthProcessor.java v1.0
 */
@Service
public class AuthProcessor extends GeneralProcessorTemplate<CmfAuthRequest, CmfAuthResponse> {

    @Resource
    private SubmitInstitutionService submitInstitutionService;

    @Override
    protected String getServiceName() {
        return "AuthProcessor";
    }

    @Override
    protected CmfAuthResponse createResponse() {
        return new CmfAuthResponse();
    }

    @Override
    protected void process(CmfAuthRequest request, CmfAuthResponse authResponse) throws CmfBizException {
        InstControlOrder controlOrder = CmfRequestConverter.createAuthOrder(request);
        InstControlOrderResult resp = submitInstitutionService.submit(controlOrder);
        InstControlOrderResultConverter.convert(resp, authResponse);
    }

}
