package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.common.core.domain.enums.MonitorType;
import com.uaepay.cmf.common.monitor.MonitorLog;



/**
 *
 * <p>监控服务</p>
 *
 * <AUTHOR> won
 * @version $Id: MonitorService.java, v 0.1 2011-7-20 下午03:06:14 sean won Exp $
 */
public interface MonitorService {

    /**
     * 监控.
     *
     * @param log
     */
    void logMonitorEvent(MonitorLog log);

    /**
     * 报警+监控
     * @param log
     */
    void alertMonitorEvent(MonitorLog log, MonitorType monitorType);

}
