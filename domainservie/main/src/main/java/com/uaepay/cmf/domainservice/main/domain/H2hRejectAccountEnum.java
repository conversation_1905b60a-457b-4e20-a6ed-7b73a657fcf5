package com.uaepay.cmf.domainservice.main.domain;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date H2hRejectAccountEnum.java v1.0  1/7/21 7:35 PM
 */
public enum H2hRejectAccountEnum implements FundRejectReason {
    // AExx035245xxxxxxxxxxxxx格式
    PAYIT("payIt", "^AE\\d{2}035245\\d{13}$", "PayIt account is not supported!", "payIt.account.notSupport"),
    // AExx035533xxxxxxxxxxxxx格式
    RATIBI("ratibi", "^AE\\d{2}035533\\d{13}$", "Ratibi account is not supported!", "ratibi.account.notSupport");

    private String code;

    private String rule;

    private String description;

    private String resultCode;

    H2hRejectAccountEnum(String code, String rule, String description, String resultCode) {
        this.code = code;
        this.rule = rule;
        this.description = description;
        this.resultCode = resultCode;
    }

    public String getCode() {
        return code;
    }

    public String getRule() {
        return rule;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getResultCode() {
        return resultCode;
    }

    /**
     * 获取账户类型
     *
     * @param iban
     * @return
     */
    public static H2hRejectAccountEnum getRejectAccountType(String iban) {
        if (StringUtils.isEmpty(iban) || iban.length() < 10) {
            return null;
        }
        for (H2hRejectAccountEnum item : values()) {
            if (Pattern.matches(item.getRule(), iban)) {
                return item;
            }
        }
        return null;
    }

}
