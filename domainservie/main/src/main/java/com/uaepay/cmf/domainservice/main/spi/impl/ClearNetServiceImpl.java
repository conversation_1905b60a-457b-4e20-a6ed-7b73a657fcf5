package com.uaepay.cmf.domainservice.main.spi.impl;

import com.uaepay.channel.cards.service.facade.domain.BankVO;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ClearNetEnum;
import com.uaepay.cmf.domainservice.main.spi.ClearNetService;
import com.uaepay.cmf.fss.ext.integration.cards.CardsClient;
import com.uaepay.cmf.service.facade.domain.clear.ClearInfo;
import com.uaepay.router.service.facade.domain.channel.InstCurrencyVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ClearNetServiceImpl.java v1.0
 */
@Service
public class ClearNetServiceImpl implements ClearNetService, BasicConstant {

    @Resource
    private CardsClient cardsClient;

    @Override
    public List<ClearInfo> convert(List<InstCurrencyVO> instCurrencies, ClearNetEnum clearNet) {
        Map<String, Set<String>> countryCurrencyMap = new HashMap<>(30);
        Set<String> localCurrencySet = new HashSet<>(10);
        for (InstCurrencyVO ic : instCurrencies) {
            for (String instCode : ic.getInstCodeList()) {
                // 如果是规则类型instCode
                String countryCode = "";
                if (instCode.contains("{4}")) {
                    countryCode = instCode.substring(0, 2);
                } else {
                    BankVO bank = cardsClient.queryByBankCode(instCode);
                    if (bank != null && StringUtils.isNotEmpty(bank.getCountryCode())) {
                        countryCode = bank.getCountryCode();
                    }
                }
                if (StringUtils.isEmpty(countryCode)) {
                    continue;
                }
                if (DEFAULT_COUNTRY_CODE.equals(countryCode)) {
                    localCurrencySet.addAll(ic.getCurrencyList());
                    continue;
                }
                if (!countryCurrencyMap.containsKey(countryCode)) {
                    countryCurrencyMap.put(countryCode, new HashSet<>());
                }
                countryCurrencyMap.get(countryCode).addAll(ic.getCurrencyList());
            }
        }
        List<ClearInfo> clearInfoList = new ArrayList<>();
        if (clearNet == null || clearNet == ClearNetEnum.LOCAL) {
            clearInfoList.add(new ClearInfo(ClearNetEnum.LOCAL.getCode(), DEFAULT_COUNTRY_CODE, new ArrayList<>(localCurrencySet)));
        }
        for (String countryCode : countryCurrencyMap.keySet()) {
            if (clearNet == null || clearNet == ClearNetEnum.SWIFT) {
                clearInfoList.add(new ClearInfo(ClearNetEnum.SWIFT.getCode(), countryCode, new ArrayList<>(countryCurrencyMap.get(countryCode))));
            }
            if (clearNet == null || clearNet == ClearNetEnum.FEDWIRE) {
                clearInfoList.add(new ClearInfo(ClearNetEnum.FEDWIRE.getCode(), countryCode, new ArrayList<>(countryCurrencyMap.get(countryCode))));
            }
        }
        return clearInfoList;
    }
}
