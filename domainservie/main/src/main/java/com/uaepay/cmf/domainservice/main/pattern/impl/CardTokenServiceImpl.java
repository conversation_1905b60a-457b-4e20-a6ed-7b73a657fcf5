package com.uaepay.cmf.domainservice.main.pattern.impl;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigureKey;
import com.uaepay.cmf.domainservice.main.pattern.CardTokenService;
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version CardTokenServiceImpl.java 1.0 Created@2017-09-28 10:59 $
 */
@Component("cardTokenService")
public class CardTokenServiceImpl implements CardTokenService, SysConfigureKey {

    @Resource
    private CardTokenRepository cardTokenRepository;

    @Override
    public CardToken queryToken(String cardTokenId) {
        if(StringUtils.isEmpty(cardTokenId)){
            return null;
        }
        return cardTokenRepository.query(cardTokenId);
    }

    @Override
    public CardToken queryTokenByInstOrderId(Long instOrderId) {
        return cardTokenRepository.queryByInstOrderId(instOrderId);
    }

    @Override
    public boolean updateCardToken(InstOrder instOrder, InstOrderResult instOrderResult) {
        if (instOrderResult.getExtension() == null) {
            return false;
        }
        CardToken cardToken = cardTokenRepository.queryByInstOrderId(instOrder.getInstOrderId());
        if (cardToken == null) {
            return false;
        }
        String cardNo = instOrderResult.getExtension().get("cardNo");
        String countryCode = instOrderResult.getExtension().get("countryCode");
        String cardHolder = instOrderResult.getExtension().get("cardHolder");
        String cardBrand = instOrderResult.getExtension().get("cardBrand");
        String instTokenId = instOrderResult.getExtension().get("instTokenId");
        String cardType = instOrderResult.getExtension().get("cardType");
        String cardExpired = instOrderResult.getExtension().get("cardExpired");
        String issueBank = instOrderResult.getExtension().get("issueBank");
        String issueBankName = instOrderResult.getExtension().get("issueBankName");

        modifyCardToken(cardToken, cardNo, countryCode, cardHolder, cardBrand, instTokenId, cardType, cardExpired, issueBank, issueBankName);

        cardTokenRepository.update(cardToken);
        return true;
    }


    @Override
    public boolean updateCardToken(InstOrder instOrder, Notify3dsResult notify3dsResult) {
        if (notify3dsResult == null) {
            return false;
        }
        CardToken cardToken = cardTokenRepository.queryByInstOrderId(instOrder.getInstOrderId());
        if (cardToken == null) {
            return false;
        }
        String cardNo = StringUtils.isNotEmpty(notify3dsResult.getCardNo()) ? notify3dsResult.getCardNo() : notify3dsResult.getCardNoMask();
        String countryCode = notify3dsResult.getCountryCode();
        String cardHolder = notify3dsResult.getCardHolder();
        String cardBrand = notify3dsResult.getCardBrand();
        String cardType = notify3dsResult.getCardType();
        String cardExpired = notify3dsResult.getCardExpire();
        String issueBank = notify3dsResult.getBankCode();
        String issueBankName = notify3dsResult.getBankName();

        modifyCardToken(cardToken, cardNo, countryCode, cardHolder, cardBrand, null, cardType, cardExpired, issueBank, issueBankName);


        cardTokenRepository.update(cardToken);
        return true;
    }

    private void modifyCardToken(CardToken cardToken, String cardNo, String countryCode, String cardHolder, String cardBrand, String instTokenId, String cardType, String cardExpired, String issueBank, String issueBankName) {
        if (StringUtils.isNotEmpty(cardNo) && StringUtils.isEmpty(cardToken.getCardNo())) {
            cardToken.setCardNo(cardNo);
        }
        if (StringUtils.isNotEmpty(countryCode) && StringUtils.isEmpty(cardToken.getCountryCode())) {
            cardToken.setCountryCode(countryCode);
        }
        if (StringUtils.isNotEmpty(cardHolder) && StringUtils.isEmpty(cardToken.getCardHolder())) {
            cardToken.setCardHolder(cardHolder);
        }
        if (StringUtils.isNotEmpty(cardBrand) && StringUtils.isEmpty(cardToken.getCardBrand())) {
            cardToken.setCardBrand(cardBrand);
        }
        if (StringUtils.isNotEmpty(instTokenId) && StringUtils.isEmpty(cardToken.getInstTokenId())) {
            cardToken.setInstTokenId(instTokenId);
        }
        if (StringUtils.isNotEmpty(cardType) && StringUtils.isEmpty(cardToken.getCardType())) {
            cardToken.setCardType(cardType);
        }
        if (StringUtils.isNotEmpty(issueBank) && StringUtils.isEmpty(cardToken.getIssueBank()) && issueBank.length() <= 4) {
            cardToken.setIssueBank(issueBank);
        }

        if (StringUtils.isNotEmpty(cardExpired) && StringUtils.isEmpty(cardToken.getCardExpired())) {
            cardToken.setCardExpired(cardExpired);
        }

        if (StringUtils.isNotEmpty(issueBankName) && StringUtils.isEmpty(cardToken.getIssueBankName())) {
            cardToken.setIssueBankName(issueBankName);
        }
    }


}
