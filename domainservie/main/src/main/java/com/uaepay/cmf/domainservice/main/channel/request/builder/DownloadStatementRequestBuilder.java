package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.enums.ChannelInfoExtKey;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.domain.file.ChannelFileRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import static com.uaepay.cmf.common.enums.FundChannelApiType.DOWNLOAD_STATEMENT;
import static com.uaepay.cmf.common.enums.FundChannelApiType.FILE_MIGRATE;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date DownloadStatementRequestBuilder.java v1.0  1/4/21 2:07 PM
 */
@Service
public class DownloadStatementRequestBuilder extends AbstractApiRequestBuilder<InstControlOrder, ChannelFileRequest> {


    @Override
    protected void buildCustomParam(InstControlOrder order, ChannelFileRequest request) {
        BeanUtils.copyProperties(order, request);
        request.setTargetInstCode(order.getInstCode());
        request.setInstOrderSubmitTime(order.getGmtCreate());
        request.setFileDate(order.getExtension().get("fileDate"));
        request.setFileNamePattern(order.getExtension().get("fileNamePattern"));
        request.setFileType(order.getExtension().get("fileType"));
        ChannelVO channel = ChannelHolder.get();
        String payoutAccount = ChannelUtil.getExtVal(channel.getExtList(), ChannelInfoExtKey.PAYOUT_ACCOUNT);
        request.setPayoutAccount(payoutAccount);
    }

    @Override
    public ChannelFileRequest buildReq() {
        return new ChannelFileRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(DOWNLOAD_STATEMENT, FILE_MIGRATE);
    }
}
