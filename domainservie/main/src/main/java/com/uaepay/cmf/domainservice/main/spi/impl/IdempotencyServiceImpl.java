package com.uaepay.cmf.domainservice.main.spi.impl;

import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.core.domain.vo.VerifyResponseContent;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.TokenHolder;
import com.uaepay.cmf.domainservice.channel.holder.VerifySignHolder;
import com.uaepay.cmf.domainservice.channel.router.ConfigurationService;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.result.ControlResultProcessor;
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor;
import com.uaepay.cmf.domainservice.main.sender.ControlOrderSendService;
import com.uaepay.cmf.domainservice.main.sender.InstOrderSendService;
import com.uaepay.cmf.domainservice.main.spi.IdempotencyService;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version: IdempotencyServiceImpl.class v1.0
 */
@Slf4j
@Service
public class IdempotencyServiceImpl implements IdempotencyService {

    @Resource
    private ConfigurationService configurationService;
    @Resource
    private ChannelApiRouter channelApiRouter;
    @Resource
    private InstOrderSendService instOrderSendService;
    @Resource
    private ControlOrderSendService controlOrderSendService;
    @Resource
    private InstResultProcessor instResultProcessor;
    @Resource
    private ControlResultProcessor controlResultProcessor;
    @Value("${cmf.idempotency.verifySign.channels:APLUS101}")
    private String idempotencyVerifySignChannels;

    @Override
    public Pair<InstBaseResult, VerifyResponseContent> processIdempotent(VerifySignRequest request, InstBaseOrder baseOrder) {

        // 是否需要幂等返回
        if (!idempotencyVerifySignChannels.contains(request.getChannelCode())) {
            return null;
        }
        VerifySignHolder.set(request);
        FundChannelApiType apiType = FundChannelApiType.getByCode(request.getApiType());
        configurationService.queryCardToken(baseOrder.getPayMode(), baseOrder.getExtension());
        try (ChannelCarrier carrier = channelApiRouter.route(RouteUtil.getParam(baseOrder.getFundChannelCode(), apiType))) {
            InstBaseResult result = send2Bank(request, baseOrder);
            return Pair.of(result, VerifySignHolder.getResult());
        } catch (Exception e) {
            log.error("SubmitInstitutionService.submit.error", e);
            // do something
            return null;
        } finally {
            VerifySignHolder.clear();
            TokenHolder.clear();
        }
    }

    private InstBaseResult send2Bank(VerifySignRequest request, InstBaseOrder baseOrder) {
        if (baseOrder instanceof InstOrder) {
            InstOrder instOrder = (InstOrder) baseOrder;
            // 2.发送银行
            ChannelFundResult channelFundResult = instOrderSendService.send(instOrder);
            // 3.处理结果
            return instResultProcessor.convert2InstResp(instOrder, channelFundResult);
        } else if (baseOrder instanceof InstControlOrder) {
            InstControlOrder controlOrder = (InstControlOrder) baseOrder;
            // 2.发送银行
            ChannelResult controlResult = controlOrderSendService.send(controlOrder);
            // 3.处理结果
            return controlResultProcessor.convert2InstResp(controlOrder, controlResult);
        }
        throw new IllegalArgumentException("baseOrder type error");
    }
}
