package com.uaepay.cmf.domainservice.main.retry;

import java.util.Date;

import javax.annotation.Resource;

import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version ControlOrderRetryService.java 1.0 Created@2017-12-28 16:04 $
 */
public class ControlOrderRetryService extends AbstractOrderRetryService {

    @Resource
    private InstControlOrderRepository instControlOrderRepository;

    @Override
    int updateRetryInfoById(int retryTimes, Date retryDateTime, Long orderId) {
        return instControlOrderRepository.updateRetryInfoById(retryTimes, retryDateTime, orderId);
    }
}
