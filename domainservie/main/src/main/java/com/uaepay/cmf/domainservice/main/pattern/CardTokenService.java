package com.uaepay.cmf.domainservice.main.pattern;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version CardTokenService.java 1.0 Created@2017-10-13 10:59 $
 */
public interface CardTokenService {

    CardToken queryToken(String cardToken);

    CardToken queryTokenByInstOrderId(Long instOrderId);

    boolean updateCardToken(InstOrder instOrder, InstOrderResult instOrderResult);

    boolean updateCardToken(InstOrder instOrder, Notify3dsResult notify3dsResult);
}
