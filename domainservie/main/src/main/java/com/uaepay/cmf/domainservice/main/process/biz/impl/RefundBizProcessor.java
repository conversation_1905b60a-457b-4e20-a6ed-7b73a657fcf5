package com.uaepay.cmf.domainservice.main.process.biz.impl;

import com.uaepay.cmf.common.core.dal.daointerface.RefundOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.InstOrderDO;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstRefundOrder;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.domainservice.main.convert.InstOrderConverter;
import com.uaepay.schema.cmf.enums.BizType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>退款业务处理器</p>
 *
 * <AUTHOR>
 * @date RefundBizProcessor.java v1.0  2020-09-06 17:05
 */
@Service
public class RefundBizProcessor extends AbstractBizProcessor {

    @Resource
    private RefundOrderDAO refundOrderDAO;

    @Override
    public InstOrder convertDO2Dto(InstOrderDO instOrderDO) {
        return InstOrderConverter.convert(instOrderDO,
                refundOrderDAO.loadById(instOrderDO.getInstOrderId()));
    }

    @Override
    public void insertSubOrder(InstOrder instOrder) {
        refundOrderDAO.insert(InstOrderConverter.convert((InstRefundOrder) instOrder));
    }

    @Override
    public void deleteSubOrder(Long instOrderId) {
        refundOrderDAO.delete(instOrderId);
    }

    @Override
    public BizType getBizType() {
        return BizType.REFUND;
    }

    @Override
    public RequestType getRequestType() {
        return RequestType.REFUND;
    }

}
