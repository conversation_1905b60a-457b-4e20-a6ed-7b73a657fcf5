package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.domain.fundin.ebank.EBankChannelVerifyRequest;
import com.uaepay.cmf.common.enums.CallBackType;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.VerifySignHolder;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import static com.uaepay.cmf.common.enums.FundChannelApiType.AUTH_VERIFY;
import static com.uaepay.cmf.common.enums.FundChannelApiType.VERIFY_SIGN;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date VerifySignRequestBuilder.java v1.0  2020-09-12 11:23
 */
@Service
public class VerifySignRequestBuilder extends AbstractApiRequestBuilder<InstOrder, EBankChannelVerifyRequest> {

    @Override
    protected void buildCustomParam(InstOrder order, EBankChannelVerifyRequest request) {
        VerifySignRequest vsRequest = VerifySignHolder.get();
        request.setCallBackType(CallBackType.getByCode(vsRequest.getCallbackType()));
        request.setResponseData(vsRequest.getVerifyParam());
        request.setResponseQueryString(vsRequest.getVerifyParamStr());
        request.setHeaderMap(vsRequest.getHeaderMap());
    }

    @Override
    public EBankChannelVerifyRequest buildReq() {
        return new EBankChannelVerifyRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(VERIFY_SIGN);
    }
}
