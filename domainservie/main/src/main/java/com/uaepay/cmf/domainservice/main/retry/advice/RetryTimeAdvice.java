package com.uaepay.cmf.domainservice.main.retry.advice;

import java.util.Map;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.util.log.LogUtil;
import com.uaepay.cmf.domainservice.main.retry.OrderRetryRequest;
import com.uaepay.cmf.domainservice.main.retry.OrderRetryService;
import com.uaepay.cmf.domainservice.main.retry.converter.OrderRetryConverter;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version RetryTimeAdvice.java 1.0 Created@2017-12-28 14:25 $
 */
@Aspect
public class RetryTimeAdvice {
    private Logger                         logger = LoggerFactory.getLogger(RetryTimeAdvice.class);

    private Map<String, OrderRetryService> orderRetryServiceMap;

    @Pointcut("@annotation(com.uaepay.cmf.domainservice.main.retry.RetryTimeManage)")
    void retryTimeManage() {
    }

    @Before("retryTimeManage()")
    public void beforeRetryTimeManage(JoinPoint pjp) {
        try {
            logger.debug("retryTimeAdvice.beforeRetryTimeManage");
            long startTime = System.currentTimeMillis();

            //取第一个参数
            Object firstParam = getFirstParam(pjp.getArgs());

            if (firstParam != null) {

                OrderRetryRequest request = convertReq(firstParam);
                if (request == null || !request.isValid()) {
                    return;
                }
                OrderRetryService orderRetryService = orderRetryServiceMap.get(request
                    .getRetryType().name());

                orderRetryService.updateRetryTime(request);
            }
            LogUtil.info("retryTime.invokeAdvice.Consume", startTime, System.currentTimeMillis());
        } catch (Exception e) {
            logger.error("retryTime.advise.error", e);
        }
    }

    private OrderRetryRequest convertReq(Object firstParam) {
        if (firstParam instanceof InstOrder) {
            return OrderRetryConverter.convert((InstOrder) firstParam);
        } else if (firstParam instanceof InstControlOrder) {
            return OrderRetryConverter.convert((InstControlOrder) firstParam);
        } else if (firstParam instanceof InstBatchOrder) {
            return OrderRetryConverter.convert((InstBatchOrder) firstParam);
        }
        return null;
    }

    private Object getFirstParam(Object[] args) {
        return (args != null && args.length >= 1) ? args[0] : null;
    }

    public void setOrderRetryServiceMap(Map<String, OrderRetryService> orderRetryServiceMap) {
        this.orderRetryServiceMap = orderRetryServiceMap;
    }
}
