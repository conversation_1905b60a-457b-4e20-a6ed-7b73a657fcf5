package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderType;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstCommonOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.common.enums.ApiMethodEnum;
import com.uaepay.cmf.common.enums.ApiParamScene;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.domainservice.channel.holder.TokenHolder;
import com.uaepay.cmf.domainservice.channel.router.ChannelService;
import com.uaepay.cmf.domainservice.main.factory.ApiRequestBuilderFactory;
import com.uaepay.cmf.domainservice.main.process.CombineCallbackService;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.router.service.facade.domain.channel.ChannelApiParamVO;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>抽象接口构建器</p>
 *
 * <AUTHOR>
 * @date AbstractApiRequestBuilder.java v1.0  2020-09-09 16:17
 */
@Slf4j
public abstract class AbstractApiRequestBuilder<Order extends InstCommonOrder, Req extends ChannelRequest> implements BasicConstant {

    @Resource
    protected CombineCallbackService callbackService;
    @Resource
    private ApiRequestBuilderFactory apiRequestBuilderFactory;
    @Resource
    private ChannelService channelService;
    @Resource(name = "memoryCacheClient")
    private CacheClient cacheClient;

    @PostConstruct
    void register() {
        apiRequestBuilderFactory.put(this.getApiTypes(), this);
    }

    /**
     * 构建订单 - 模板方法
     *
     * @param order
     * @return
     */
    public Req build(Order order) {
        // 创建对象
        Req req = buildReq();

        // 组装公共参数
        buildCommonReq(order, req);

        // 组装接口个性参数
        buildCustomParam(order, req);

        return req;
    }

    protected abstract void buildCustomParam(Order order, Req req);

    public abstract Req buildReq();


    // 获取接口类型
    public abstract List<FundChannelApiType> getApiTypes();

    private void buildCommonReq(Order order, Req request) {

        // 构建公共参数
        BeanUtils.copyProperties(order, request);

        if (request instanceof ChannelFundRequest) {
            if (order instanceof InstBaseOrder) {
                ((ChannelFundRequest) request).setAmount(((InstBaseOrder) order).getAmount());
                //资金渠道编码
                ((ChannelFundRequest) request).setTargetInstCode(((InstBaseOrder) order).getInstCode());
            }
            if (order instanceof InstOrder) {
                ((ChannelFundRequest) request).setInstOrderSubmitTime(((InstOrder) order).getGmtBookingSubmit());
            }
        }

        ChannelApiVO api = ChannelHolder.get().getChannelApi();
        request.setApiUrl(api.getApiUrl());
        request.setApiType(FundChannelApiType.getByCode(api.getApiType()));

        ApiMethodEnum apiMethod = ApiMethodEnum.getByCode(api.getApiMethod());
        request.setApiMethod(apiMethod == null ? ApiMethodEnum.DUBBO : apiMethod);

        request.setFundProviderCode(ChannelHolder.get().getFundProviderCode());
        //获取扩展信息
        request.getExtension()
                .putAll(filterExtKey(order.getExtension(), api.getParamList()));

        request.setCallbackServerUrl(callbackService.getCallBackUrl(order.getFundChannelCode(),
                SERVER_URL, getVerifyApiType(order)));
        request.setCallbackPageUrl(callbackService.getCallBackUrl(order.getFundChannelCode(),
                PAGE_URL, getVerifyApiType(order)));


        CardToken cardToken = getCardToken();
        buildCardToken(request, cardToken);
    }

    private FundChannelApiType getVerifyApiType(Order order) {
        return (order.getApiType() == FundChannelApiType.AUTH || isInstControl(order)) ? FundChannelApiType.AUTH_VERIFY : FundChannelApiType.VERIFY_SIGN;
    }

    private boolean isInstControl(Order order) {
        return order.getExtension() != null && order.getExtension().containsKey(ExtensionKey.INST_ORDER_TYPE.getKey()) && InstOrderType.getByName(order.getExtension().get(ExtensionKey.INST_ORDER_TYPE.getKey())) == InstOrderType.CONTROL;
    }

    private void buildCardToken(Req request, CardToken cardToken) {
        if (cardToken == null) {
            return;
        }
        request.getExtension().put(ExtensionKey.CARD_TOKEN_ID.key, cardToken.getCardTokenId());
        if (StringUtils.isNotEmpty(cardToken.getSessionId())) {
            request.getExtension().put(ExtensionKey.ORG_TOKEN.key, cardToken.getSessionId());
        }
        if (StringUtils.isNotEmpty(cardToken.getInstTokenId())) {
            request.getExtension().put(ExtensionKey.INST_TOKEN_ID.key, cardToken.getInstTokenId());
        }

        if (StringUtils.isNotEmpty(cardToken.getCardType())) {
            request.getExtension().put(MA_CARD_TYPE, cardToken.getCardType());
        }

        if (StringUtils.isNotEmpty(cardToken.getExtension())) {
            request.getExtension().putAll(MapUtil.jsonToMap(cardToken.getExtension()));
        }
    }

    protected CardToken getCardToken() {
        Map<String, Object> dataMap = TokenHolder.get();
        if (dataMap == null) {
            return null;
        }
        return (CardToken) dataMap.get(ExtensionKey.CARD_TOKEN.key);
    }

    protected void putInstOrderToken(InstBaseOrder order) {
        if (channelService.is3ds2Channel(order.getFundChannelCode())) {
            String instOrderToken = UUID.randomUUID().toString().replace("-", "");
            Map<String, String> tokenMap = new HashMap<>();
            tokenMap.put(ExtensionKey.INST_ORDER_NO.key, order.getInstOrderNo());
            tokenMap.put(ExtensionKey.INST_ORDER_TYPE.key, order instanceof InstOrder ? InstOrderType.FUND.name() : InstOrderType.CONTROL.name());
            cacheClient.put(CacheType.INST_ORDER_TOKEN, instOrderToken, tokenMap, THIRTY_MINUTE_SECONDS);
            order.getExtension().put(CacheType.INST_ORDER_TOKEN.getCode(), instOrderToken);
        }
    }


    protected static Map<String, String> filterExtKey(Map<String, String> orgiMap,
                                                      List<ChannelApiParamVO> filterList) {
        Map<String, String> targetMap = new ConcurrentHashMap<>();

        if (CollectionUtils.isEmpty(filterList)) {
            return targetMap;
        }
        for (Iterator<ChannelApiParamVO> iterator = filterList.iterator(); iterator.hasNext(); ) {
            ChannelApiParamVO apiParam = iterator.next();
            if (apiParam.isAvailable()
                    && ApiParamScene.isChannelRequest(apiParam.getScene())) {
                if (orgiMap.containsKey(apiParam.getParamName())) {
                    if (null != orgiMap.get(apiParam.getParamName())) {
                        targetMap
                                .put(apiParam.getParamName(), orgiMap.get(apiParam.getParamName()));
                    }
                }
            }
        }
        return targetMap;
    }

    private static final String MA_CARD_TYPE = "maCardType";

}
