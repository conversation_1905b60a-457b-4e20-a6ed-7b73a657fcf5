package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.constants.MerchantConstant;
import com.uaepay.cmf.common.core.dal.dataobject.CmfRequestDO;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.CmfRequest;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.fss.ext.integration.util.OrderUtil;
import com.uaepay.cmf.service.facade.domain.auth.CmfAuthRequest;
import com.uaepay.cmf.service.facade.domain.card.RetrieveCardMetadataRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfFileRequest;
import com.uaepay.cmf.service.facade.domain.control.psp.PspReversalRequest;
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterRequest;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.Kvp;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.uaepay.cmf.common.core.domain.enums.ExtensionKey.*;

/**
 * <p>cmf实体表转换对象</p>
 *
 * <AUTHOR> Liu
 * @version $Id: CmfRequestConverter.java, v 0.1 2012-8-17 上午11:02:56 liumaoli Exp $
 */
public class CmfRequestConverter implements BasicConstant, MerchantConstant {

    /**
     * 控制请求类型与接口类型MAP
     */
    private static final Map<ControlRequestType, FundChannelApiType> apiTypeMap = new HashMap<>();

    //初始化
    static {
        apiTypeMap.put(ControlRequestType.AUTH, FundChannelApiType.AUTH);
        apiTypeMap.put(ControlRequestType.AUTHENTICATE_ADVANCE, FundChannelApiType.AUTH_ADVANCE);
        apiTypeMap.put(ControlRequestType.ADVANCE, FundChannelApiType.DEBIT_ADVANCE);
        apiTypeMap.put(ControlRequestType.ADVANCE_QUERY, FundChannelApiType.DEBIT_ADVANCE_QUERY);
        apiTypeMap.put(ControlRequestType.QUERY_BALANCE, FundChannelApiType.QUERY_BALANCE);
        apiTypeMap.put(ControlRequestType.VOID_TRANSACTION, FundChannelApiType.VOID_TRANSACTION);
        apiTypeMap.put(ControlRequestType.PREAUTH_UPDATE, FundChannelApiType.PREAUTH_UPDATE);
        apiTypeMap.put(ControlRequestType.PREAUTH_COMPLETE, FundChannelApiType.PREAUTH_COMPLETE);
        apiTypeMap.put(ControlRequestType.PREAUTH_VOID, FundChannelApiType.PREAUTH_VOID);
        apiTypeMap.put(ControlRequestType.REVERSAL, FundChannelApiType.REVERSAL);
        apiTypeMap.put(ControlRequestType.DOWNLOAD_STATEMENT, FundChannelApiType.DOWNLOAD_STATEMENT);
        apiTypeMap.put(ControlRequestType.FILE_IMPORT, FundChannelApiType.FILE_IMPORT);
        apiTypeMap.put(ControlRequestType.CONTROL_VOID_TRANSACTION, FundChannelApiType.CONTROL_VOID_TRANSACTION);
        apiTypeMap.put(ControlRequestType.VALIDATE_PARAMETER, FundChannelApiType.VALIDATE_PARAMETER);
        apiTypeMap.put(ControlRequestType.IBAN_DETAIL_QUERY, FundChannelApiType.IBAN_DETAIL_QUERY);
    }

    private CmfRequestConverter() {
    }

    /**
     * 请求转换为CMF订单
     *
     * @param request
     * @return
     */
    public static CmfOrder convert(com.uaepay.cmf.service.facade.domain.CmfRequest request) {
        if (request == null) {
            return null;
        }

        CmfOrder order = new CmfOrder();

        order.setStatus(CmfOrderStatus.IN_PROCESS);
        order.setConfirmStatus(CmfOrderConfirmStatus.PASS);

        //1. 转换基本属性.
        convertBaseFields(request, order);
        copyExtension(request, order);

        //2. 转换不同业务类型的基本属性.
        switch (order.getBizType()) {
            case FUNDIN: {
                order.setRequestType(RequestType.FUND_IN);
                convertFundinFields(request, order);
                break;
            }
            case REFUND: {
                order.setRequestType(RequestType.REFUND);
                convertReFundFields(request, order);
                break;
            }
            case FUNDOUT: {
                order.setRequestType(RequestType.FUND_OUT);
                convertFundoutFields(request, order);
                break;
            }
            default:
                throw new IllegalArgumentException("不支持的业务类型");
        }
        // 设置商户信息
        String merchantId = order.getExtension().get(ExtensionKey.TOPAY_MERCHANT_ID.key);
        if (StringUtils.isNotEmpty(merchantId)) {
            order.setMerchantId(merchantId);
        }
        // 设置用户id信息
        if (StringUtils.isNotEmpty(order.getMemberId())
                && order.getExtension() != null && order.getExtension().get(ExtensionKey.MEMBER_ID.getKey()) == null) {
            order.getExtension().put(ExtensionKey.MEMBER_ID.getKey(), order.getMemberId());
        }
        return order;
    }

    /**
     * 控制请求转换为控制订单
     *
     * @param request
     * @return
     */
    public static InstControlOrder convert(CmfControlRequest request) {
        InstControlOrder order = initControlOrder();
        BeanUtils.copyProperties(request, order);
        order.setApiType(apiTypeMap.get(request.getRequestType()));
        order.setExtension(CommonConverter.convertMap(request.getExtension()));
        if (StringUtils.isNotEmpty(request.getPreSettlementId())) {
            order.getExtension().put(ExtensionKey.ORGI_SETTLEMENT_ID.key,
                    request.getPreSettlementId());
        }
        if (request.getExtension() != null
                && StringUtils.isEmpty(request.getExtension().getValue(TARGET_INST))) {
            request.getExtension().add(TARGET_INST, request.getInstCode());
        }
        //预授权更新和完成类型需要更新原订单
        if (ControlRequestType.PREAUTH_COMPLETE == request.getRequestType()
                || ControlRequestType.PREAUTH_UPDATE == request.getRequestType()) {
            order.setUpdatePreOrder(true);
        }

        order.setMerchantId(order.getExtension().get(ExtensionKey.TOPAY_MERCHANT_ID.key));
        return order;
    }

    private static InstControlOrder initControlOrder() {
        InstControlOrder order = new InstControlOrder();
        order.setFlag(OrderFlag.DEFAULT);
        order.setGmtNextRetry(new Date());
        order.setStatus(InstOrderStatus.IN_PROCESS);
        order.setRetryTimes(DEFAULT_RETRY_TIMES);
        order.setCommunicateStatus(CommunicateStatus.AWAITING);
        return order;
    }

    public static InstControlOrder convert(CmfFileRequest request, FundChannelApiType apiType) {
        InstControlOrder order = initControlOrder();
        BeanUtils.copyProperties(request, order);
        order.setApiType(apiType);
        order.setPayMode(PayMode.QUICKPAY);
        order.setRequestType(request.getRequestType());
        order.setFundChannelCode(request.getChannelCode());

        Map<String, String> extMap = request.getExtension() == null ? new HashMap<>(5) : request.getExtension();
        if (StringUtils.isNotEmpty(request.getFileDate())) {
            extMap.put("fileDate", request.getFileDate());
        }
        if (StringUtils.isNotEmpty(request.getFileType())) {
            extMap.put("fileType", request.getFileType());
        }
        if (StringUtils.isNotEmpty(request.getFileNamePattern())) {
            extMap.put("fileNamePattern", request.getFileNamePattern());
        }

        order.setExtension(extMap);

        order.setMerchantId(order.getExtension().get(ExtensionKey.TOPAY_MERCHANT_ID.key));

        return order;
    }

    public static InstControlOrder convert(PspReversalRequest request, FundChannelApiType apiType) {
        InstControlOrder order = initControlOrder();
        BeanUtils.copyProperties(request, order);
        order.setApiType(apiType);
        Map<String, String> extMap = request.getExtension() == null ? new HashMap<>(5) : request.getExtension();
        order.setExtension(extMap);
        return order;
    }

    /**
     * 复制入款的特殊字段.
     *
     * @param request
     * @param order
     */
    private static void convertFundinFields(com.uaepay.cmf.service.facade.domain.CmfRequest request, CmfOrder order) {
        //充退不须提交审核
        order.setConfirmStatus(CmfOrderConfirmStatus.NOT_NEED);

        //手机卡入款，需要用到productCode
        if (!StringUtils.isEmpty(request.getProductCode())) {
            order.getExtension().put(ExtensionKey.PRODUCT_CODE.key, request.getProductCode());
        }
    }

    /**
     * 复制充退的特殊字段.
     *
     * @param request
     * @param order
     */
    private static void convertReFundFields(com.uaepay.cmf.service.facade.domain.CmfRequest request, CmfOrder order) {
        //充退不须提交审核
        order.setConfirmStatus(CmfOrderConfirmStatus.NOT_NEED);
        //充退时，memberId需要使用入款订单的memberId，在instOrder扩展属性里会设置上.
        order.setOrgiPaymentSeqNo(order.getExtension().get(ExtensionKey.ORGI_FUNDIN_ORDER_NO.key));
        if (StringUtils.isNotEmpty(order.getExtension().get(ExtensionKey.ORGI_SETTLEMENT_ID.key))
                && !"null".equals(order.getExtension().get(ExtensionKey.ORGI_SETTLEMENT_ID.key))) {
            order
                    .setOrgiSettlementId(order.getExtension().get(ExtensionKey.ORGI_SETTLEMENT_ID.key));
        }
    }

    /**
     * 复制出款的特殊字段.
     *
     * @param request
     * @param order
     */
    private static void convertFundoutFields(com.uaepay.cmf.service.facade.domain.CmfRequest request, CmfOrder order) {
        if (!StringUtils.isEmpty(request.getProductCode())) {
            order.getExtension().put(ExtensionKey.PRODUCT_CODE.key, request.getProductCode());
        }
        if (StringUtils.isNotBlank(request.getMemberId()) && StringUtils.isEmpty(order.getExtension().get(ExtensionKey.MEMBER_ID.key))) {
            order.getExtension().put(ExtensionKey.MEMBER_ID.key, request.getMemberId());
        } else {
            OrderUtil
                    .putExtIfEmpty(order.getExtension(), ExtensionKey.MEMBER_ID, ExtensionKey.PAYEE_ID);
        }
    }

    /**
     * 复制基本字段信息.
     *
     * @param request
     * @param order
     */
    private static void convertBaseFields(com.uaepay.cmf.service.facade.domain.CmfRequest request, CmfOrder order) {
        BeanUtils.copyProperties(request, order);

        //渠道编号：存入到扩展属性中；
        order.setFundChannelCode(request.getFundsChannel());
        order.setGmtCreate(new Date());

        //设置统一支付凭证号
        String paymentOrderNo = request.getExtension().getValue(ExtensionKey.PAYMENT_ORDER_NO.key);
        order.setPaymentVoucherNo(paymentOrderNo == null ? "" : paymentOrderNo);
    }

    public static void convertFromOld(CmfOrder cmfOrder, CmfOrder origCmfOrder) {
        if (cmfOrder == null || origCmfOrder == null) {
            return;
        }
        if (cmfOrder.getPayMode() == null) {
            cmfOrder.setPayMode(origCmfOrder.getPayMode());
        }
    }

    /**
     * 拷贝所有request的扩展信息给cmfOrder.
     *
     * @param from
     * @param to
     */
    private static void copyExtension(com.uaepay.cmf.service.facade.domain.CmfRequest from, CmfOrder to) {
        if (from.getExtension() != null && from.getExtension().getEntryList() != null) {
            for (Kvp kvp : from.getExtension().getEntryList()) {
                to.getExtension().put(CommonConverter.convertKey(kvp.getKey()), kvp.getValue());
            }
        }
        if (from.getExtension() != null
                && StringUtils.isEmpty(from.getExtension().getValue(TARGET_INST))) {
            to.getExtension().put(TARGET_INST, from.getInstCode());
        }
    }

    public static Map<ControlRequestType, FundChannelApiType> getApiTypeMap() {
        return apiTypeMap;
    }

    /**
     * CmfRequest转换为实体.
     *
     * @param request
     * @return
     */
    public static CmfRequestDO convertToRequestDO(CmfRequest request) {
        if (request == null) {
            return null;
        }
        CmfRequestDO reqdo = new CmfRequestDO();
        BeanUtils.copyProperties(request, reqdo);
        reqdo.setCanRetry((request.isCanRetry() ? YesNo.YES : YesNo.NO).getCode());
        return reqdo;
    }

    /**
     * 实体转换为CmfRequest.
     *
     * @param reqdo
     * @return
     */
    public static CmfRequest convertToRequest(CmfRequestDO reqdo) {
        if (reqdo == null) {
            return null;
        }
        CmfRequest req = new CmfRequest();
        BeanUtils.copyProperties(reqdo, req);
        req.setCanRetry(YesNo.YES.getCode().equalsIgnoreCase(reqdo.getCanRetry()));
        return req;
    }

    public static CmfControlRequest convertInst2Control(InstOrder order, ControlRequestType requestType) {
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestNo(requestType.getCode() + order.getInstOrderNo());
        request.setPreRequestNo(order.getInstOrderNo());
        request.setRequestType(requestType);
        request.setPayMode(order.getPayMode());
        request.setInstCode(order.getInstCode());
        Extension extension = new Extension();
        extension.add("sourceOrder", "inst");
        if (StringUtils.isNotBlank(order.getExtension().get(ExtensionKey.MERCHANT_ONBOARDING_ID.key))) {
            extension.add(ExtensionKey.MERCHANT_ONBOARDING_ID.key, order.getExtension().get(ExtensionKey.MERCHANT_ONBOARDING_ID.key));
        } else {
            extension.add(ExtensionKey.PAYEE_ID.key, order.getExtension().get(ExtensionKey.PAYEE_ID.key));
        }
        request.setExtension(extension);
        return request;
    }

    public static InstControlOrder createAuthOrder(CmfAuthRequest request) {
        InstControlOrder order = initControlOrder();
        BeanUtils.copyProperties(request, order);
        order.setApiType(FundChannelApiType.AUTH);
        order.setRequestType(ControlRequestType.AUTH);

        Map<String, String> extMap = request.getExtension() == null ? new HashMap<>(5) : request.getExtension();

        extMap.put(ExtensionKey.CARD_TOKEN.getKey(), request.getCardTokenId());
        if (request.getExtension() != null) {
            extMap.putAll(request.getExtension());
        }
        order.setExtension(extMap);
        order.setMerchantId(order.getExtension().get(ExtensionKey.TOPAY_MERCHANT_ID.key));

        return order;
    }

    public static VerifySignRequest convertInst2VerifySign(InstOrder order) {
        VerifySignRequest request = new VerifySignRequest();
        request.setChannelCode(order.getFundChannelCode());
        request.setApiType(FundChannelApiType.VERIFY_SIGN.getCode());
        request.setInstOrderNo(order.getInstOrderNo());
        request.setCallbackType("server");
        request.setAsync(false);
        return request;
    }

    public static InstControlOrder convert(APlusMerchantRegisterRequest request) {
        InstControlOrder order = initControlOrder();
        BeanUtils.copyProperties(request, order);
        order.setApiType(FundChannelApiType.MERCHANT_REGISTER);
        order.setPayMode(PayMode.QUICKPAY);
        order.setRequestType(ControlRequestType.MERCHANT_REGISTER);
        order.setInstCode(APLUS);

        Map<String, String> extMap = new HashMap<>();
        if (request.getExtension() != null) {
            extMap.putAll(request.getExtension());
        }
        extMap.put(MERCHANT_ID, request.getMerchantId());
        extMap.put(MERCHANT_NAME, request.getMerchantName());
        extMap.put(MERCHANT_MCC, request.getMerchantMcc());
        extMap.put(REGISTRATION_NO, request.getRegistrationNo());
        extMap.put(MERCHANT_ADDRESS, request.getMerchantAddress());
        extMap.put(STORE_ID, request.getStoreId());
        extMap.put(STORE_NAME, request.getStoreName());
        extMap.put(STORE_MCC, request.getStoreMcc());
        extMap.put(STORE_ADDRESS, request.getStoreAddress());
        extMap.put(WEBSITE_URL, request.getWebsiteUrl());

        order.setExtension(extMap);
        order.setMerchantId(request.getMerchantId());
        return order;
    }


    public static InstControlOrder createRetrieveCardOrder(RetrieveCardMetadataRequest request) {
        InstControlOrder order = initControlOrder();
        order.setRequestNo(request.getRequestNo());
        order.setApiType(FundChannelApiType.RETRIEVE_CARD_METADATA);
        order.setRequestType(ControlRequestType.RETRIEVE_CARD_METADATA);
        order.setPayMode(PayMode.BALANCE);
        order.setInstCode(request.getBankCode());

        Map<String, String> extMap = new HashMap<>();
        extMap.put(CARD_TYPE.getKey(), request.getCardType());
        extMap.put(COUNTRY_CODE.getKey(), request.getCountryCode());
        extMap.put(CARD_NO.getKey(), request.getCardNo());

        order.setExtension(extMap);
        return order;
    }
}
