package com.uaepay.cmf.domainservice.main.process.biz;

import com.uaepay.cmf.common.core.dal.dataobject.InstOrderDO;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.schema.cmf.enums.BizType;

/**
 * <p>业务处理器</p>
 *
 * <AUTHOR>
 * @date BizProcessor.java v1.0  2020-09-06 00:47
 */
public interface BizProcessor {

    /**
     * 转换机构订单DO对象为Dto对象
     * @param instOrderDO
     */
    InstOrder convertDO2Dto(InstOrderDO instOrderDO);

    /**
     * 插入子表
     * @param instOrder
     */
    void insertSubOrder(InstOrder instOrder);

    /**
     * 删除子订单
     * @param instOrderId
     */
    void deleteSubOrder(Long instOrderId);

    /**
     * 获取业务类型
     *
     * @return
     */
    BizType getBizType();

    /**
     * 获取请求类型
     *
     * @return
     */
    RequestType getRequestType();

}
