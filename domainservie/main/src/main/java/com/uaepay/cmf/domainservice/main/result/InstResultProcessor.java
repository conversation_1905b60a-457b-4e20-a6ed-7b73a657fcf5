package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.exception.WrongStateException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.ApiParamScene;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.common.enums.MonitorItem;
import com.uaepay.cmf.common.monitor.MonitorLog;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.domainservice.channel.router.ConfigurationService;
import com.uaepay.cmf.domainservice.main.convert.ChannelResultConverter;
import com.uaepay.cmf.domainservice.main.convert.CmfRequestConverter;
import com.uaepay.cmf.fss.ext.integration.ues.UesClient;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.api.VerifySignFacade;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.common.domain.OperationEnvironment;
import com.uaepay.router.service.facade.domain.channel.ChannelApiParamVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.YesNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date InstResultProcessor.java v1.0  2020-09-12 10:24
 */
@Slf4j
@Service
public class InstResultProcessor extends AbstractResultProcessor<InstOrder, ChannelFundResult, InstOrderResult> {

    private static Map<InstOrderResultStatus, InstOrderStatus> instOrderFinalStatusMap = new HashMap<>(4);

    private static Map<InstOrderStatus, CmfOrderStatus> orderFinalStatusMap = new HashMap<>(4);

    static {
        instOrderFinalStatusMap.put(InstOrderResultStatus.SUCCESSFUL, InstOrderStatus.SUCCESSFUL);
        instOrderFinalStatusMap.put(InstOrderResultStatus.FAILURE, InstOrderStatus.FAILURE);
        instOrderFinalStatusMap.put(InstOrderResultStatus.HALF_SUCCESSFUL, InstOrderStatus.HALF_SUCCESSFUL);
        instOrderFinalStatusMap.put(InstOrderResultStatus.CANCEL, InstOrderStatus.CANCEL);
        instOrderFinalStatusMap.put(InstOrderResultStatus.REDIRECT_VERIFY, InstOrderStatus.REDIRECT_VERIFY);

        orderFinalStatusMap.put(InstOrderStatus.SUCCESSFUL, CmfOrderStatus.SUCCESSFUL);
        orderFinalStatusMap.put(InstOrderStatus.FAILURE, CmfOrderStatus.FAILURE);
    }

    @Resource
    private ControlRequestFacade controlRequestFacade;

    @Resource
    private VerifySignFacade verifySignFacade;

    @Resource
    private UesClient uesClient;

    @Resource
    private ConfigurationService configurationService;

    @Override
    protected void validateResult(InstOrder order, InstOrderResult instResult) {
        // 1. 结果重复性校验
        resultProcess.validateNotProcessed(order.getInstOrderId());

        // 2. 返回金额校验
        channelResultValidator.validateAmount(order, instResult);
    }

    @Override
    public InstOrderResult convert2InstResp(InstOrder order, ChannelFundResult fundResult) {
        return ChannelResultConverter.convert(order, fundResult);
    }

    @Override
    @Transactional(rollbackFor = {Throwable.class})
    protected void storeResult(InstOrder order, InstOrderResult instResult) {
        log.info("instOrder:{},instResult:{}", order, instResult);
        // 1.更新订单发送状态
        updateOrderCommunicateStatus(order, instResult);

        // 2. 保存或更新结果
        instOrderResultRepository.storeOrUpdate(instResult);

        // 3. 更新订单token
        cardTokenService.updateCardToken(order, instResult);

        // 4. 更新机构订单状态，保存扩展信息
        updateOrderStatusAndExt(order, instResult);

        // 5. 更新cmf订单状态
        updateCmfOrderStatus(order);
    }

    @Override
    protected void laterProcess(InstOrder order, InstOrderResult result) {
        // MQ发送结果通知Payment
        CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(order.getCmfSeqNo(), false);
        // 通知payment结果
        notifyPaymentService.notifyResult(cmfOrder, result);
        // 通知收银台绑卡
        notifyCashdeskService.notifyBindCard(order, result);
        // 通知counter退款订单
        notifyCounterService.notifyRefundOrder(order);
        // 流量记录
        limitService.recordFlow(order);
        // 特殊状态处理
        specialStatusProcess(order, result);
        redirectVerifyStatusProcess(order, result);
    }

    private void redirectVerifyStatusProcess(InstOrder order, InstOrderResult result){

        if (result == null || result.getStatus() != InstOrderResultStatus.REDIRECT_VERIFY){
            return;
        }

        verifySignFacade.verify(CmfRequestConverter.convertInst2VerifySign(order));
        InstOrderResult dbResult = instOrderResultRepository.loadRealResultByOrder(order.getInstOrderId());
        if (dbResult != null && dbResult.getStatus() != null && dbResult.getStatus().isFinalResult()) {
            BeanUtils.copyProperties(dbResult, result);
            result.setProcessStatus(InstOrderProcessStatus.SUCCESS);
        }
    }

    // 暂时先放在这里
    private void specialStatusProcess(InstOrder order, InstOrderResult result) {
        if (result == null || result.getStatus() != InstOrderResultStatus.CANCEL) {
            log.info("instOrder--{},need not cancel", order.getInstOrderNo());
            return;
        }
        log.info("instOrder-{}-need-cancel", order.getInstOrderNo());

        boolean updateCancelStatus;
        switch (order.getStatus()) {
            case IN_PROCESS:
                updateCancelStatus = instOrderRepository.updateInstOrderStatus(order, InstOrderStatus.CANCEL);
                break;
            case CANCEL:
                //如果已经更新为CANCEL状态则直接走下面步骤
                updateCancelStatus = true;
                break;
            default:
                log.warn("instOrder--{},status:{} not match", order.getInstOrderNo(), order.getStatus());
                return;
        }
        // 更新instOrder状态为撤销状态
        Assert.isTrue(updateCancelStatus, "更新cancelStatus失败");
        CmfControlResult vtResult = controlRequestFacade.control(CmfRequestConverter.convertInst2Control(order, ControlRequestType.VOID_TRANSACTION), new OperationEnvironment());
        log.info("specialStatusProcess-vtResult-{}", vtResult);
        InstOrderResult dbResult = instOrderResultRepository.loadRealResultByOrder(order.getInstOrderId());
        if (dbResult != null && dbResult.getStatus() != null && dbResult.getStatus().isFinalResult()) {
            BeanUtils.copyProperties(dbResult, result);
            result.setProcessStatus(result.getStatus() != null && result.getStatus().isFinalResult() ? InstOrderProcessStatus.SUCCESS : InstOrderProcessStatus.AWAITING);
        }
    }


    private void updateOrderCommunicateStatus(InstOrder order, InstOrderResult instResult) {
        CommunicateStatus targetStatus = targetCommStatusMap.get(instResult.getProcessStatus());
        Assert.notNull(targetStatus, "未找到目标发送状态");
        int modCount = 0;
        if (statusMapping.get(targetStatus).contains(order.getCommunicateStatus())) {
            modCount = instOrderRepository.updateCommunicateStatusWithPreStatus(order, targetStatus,
                    order.getCommunicateStatus());
        }
        Assert.isTrue(modCount == 1, "订单发送状态更新失败");
    }

    protected void updateOrderStatusAndExt(InstOrder order, InstOrderResult instResult) {
        // 订单状态跃迁
        InstOrderStatus orderStatus = instOrderFinalStatusMap.get(instResult.getStatus());
        if (orderStatus != null && orderStatusMapping.get(orderStatus).contains(order.getStatus())) {
            boolean resultProcess = instOrderRepository.updateInstOrderStatus(order, orderStatus);
            Assert.isTrue(resultProcess, "机构订单状态更新失败");
        }
        saveExtension(order, instResult);
    }


    public void updateCmfOrderStatus(InstOrder order) {
        CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(order.getCmfSeqNo(), false);

        // 对于拆分订单需要判断是否所有订单都成功
        boolean isCompleteSuccess = false;
        try {
            if (instOrderRepository.isCompleteSuccess(order)) {
                isCompleteSuccess = true;
            }
        } catch (WrongStateException e) {
            log.error("cmf订单[" + order.getInstOrderNo() + "]拆分多笔状态不一致", e);
            monitorService.logMonitorEvent(new MonitorLog(order.getInstOrderNo(),
                    MonitorItem.STATUS_NOT_CONSISTENT_MULTI, "拆分多次状态不一致" + order.getInstOrderNo()));
            return;
        }

        if (isCompleteSuccess) {
            CmfOrderStatus cmfOrderStatus = orderFinalStatusMap.get(order.getStatus());
            if (cmfOrderStatus == null) {
                return;
            }
            cmfOrder.setStatus(cmfOrderStatus);
            try {
                cmfOrderRepository.updateCmfOrderStatus(cmfOrder, CmfOrderStatus.IN_PROCESS);
            } catch (Exception e) {
                monitorService.logMonitorEvent(new MonitorLog(order.getInstOrderNo(),
                        MonitorItem.STATE_TRASICTION_EXCEPTION, "状态跃迁异常" + order.getInstOrderNo(), e));
            }
        }
    }

    private void saveExtension(InstOrder instOrder, InstOrderResult instOrderResult) {

        // 拆单标志
        if (instOrder.getStatus() == InstOrderStatus.SUCCESSFUL
                && YesNo.YES.getCode().equals(instOrder.getExtension().get(ExtensionKey.TRANSFORM_ACCOUNT_ACCESS.getKey()))
                && configurationService.isSvaTransformOrder(instOrder)){
            instOrder.getExtension().put(ExtensionKey.TRANSFORM_FLAG.getKey(), YesNo.YES.getCode());
            instOrderResult.getExtension().put(ExtensionKey.TRANSFORM_FLAG.getKey(), YesNo.YES.getCode());
            instOrderRepository.storeExtension(instOrder);
        }

        ChannelVO channel = ChannelHolder.get();
        if (channel != null && channel.getChannelApi() != null
                && instOrderResult.getExtension() != null) {
            boolean needStore = false;
            if (StringUtils.isNotEmpty(instOrderResult.getInstSeqNo())) {
                instOrder.getExtension().put(ExtensionKey.INST_SEQ_NO.key,
                        instOrderResult.getInstSeqNo());
                needStore = true;
            }
            // 按照接口定义保存数据
            if (CollectionUtils.isNotEmpty(channel.getChannelApi().getParamList())) {
                for (ChannelApiParamVO apiParam : channel.getChannelApi().getParamList()) {
                    if (ApiParamScene.CHANNEL_RETURN.getCode().equals(apiParam.getScene())
                            && StringUtils.isNotEmpty(instOrderResult.getExtension().get(apiParam.getParamName()))) {
                        instOrder.getExtension().put(apiParam.getParamName(),
                                instOrderResult.getExtension().get(apiParam.getParamName()));
                        needStore = true;
                    }
                }
            }
            // 如果有需要保存,则更新扩展信息
            if (needStore) {
                instOrderRepository.storeExtension(instOrder);
            }
        }
    }
}
