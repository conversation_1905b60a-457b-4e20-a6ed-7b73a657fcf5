package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderType;
import com.uaepay.cmf.common.core.domain.exception.DuplicateRequestException;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.domain.vo.VerifyResponseContent;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.fundin.ebank.EBankChannelVerifyResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.VerifySignHolder;
import com.uaepay.cmf.domainservice.channel.router.impl.ChannelApiRouter;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.IdempotencyService;
import com.uaepay.cmf.domainservice.main.spi.VerifySignService;
import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.cmf.fss.ext.integration.util.ReturnResultUtil;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date VerifySignProcessor.java v1.0
 */
@Slf4j
@Service
public class VerifySignProcessor extends GeneralProcessorTemplate<VerifySignRequest, VerifySignResult> {

    @Resource
    private VerifySignService verifySignService;
    @Resource
    private InstOrderRepository instOrderRepository;
    @Resource
    private InstControlOrderRepository instControlOrderRepository;
    @Resource
    private CardTokenRepository cardTokenRepository;
    @Resource
    private ChannelApiRouter apiRouter;
    @Resource
    private IdempotencyService idempotencyService;

    @Override
    protected String getServiceName() {
        return "VerifySignProcessor";
    }

    @Override
    protected void businessValidate(VerifySignRequest request) throws DuplicateRequestException {
        InstOrderType instOrderType = FundChannelApiType.getByCode(request.getApiType()) == FundChannelApiType.VERIFY_SIGN ?
                InstOrderType.FUND : InstOrderType.CONTROL;
        Assert.notNull(request.getInstOrderNo(), "机构订单号不可为空");
        InstBaseOrder instOrder = instOrderType == InstOrderType.FUND ? instOrderRepository.loadByNo(request.getInstOrderNo()) :
                instControlOrderRepository.loadByNo(request.getInstOrderNo());
        Assert.notNull(instOrder, instOrderType + "订单不存在");
        if (instOrder.getStatus() == InstOrderStatus.SUCCESSFUL || instOrder.getStatus() == InstOrderStatus.FAILURE) {
            throw new DuplicateRequestException(ErrorCode.WRONG_ORDER_DUPLICATE_PROCESS);
        }
    }

    @Override
    protected void resolveDuplicateResponse(VerifySignRequest request, VerifySignResult verifyResult, String message) {
        InstOrderType instOrderType = FundChannelApiType.getByCode(request.getApiType()) == FundChannelApiType.VERIFY_SIGN ?
                InstOrderType.FUND : InstOrderType.CONTROL;
        InstBaseOrder baseOrder = instOrderType == InstOrderType.FUND ? instOrderRepository.loadByNo(request.getInstOrderNo()) :
                instControlOrderRepository.loadByNo(request.getInstOrderNo());
        Pair<InstBaseResult, VerifyResponseContent> resp = idempotencyService.processIdempotent(request, baseOrder);
        buildVerifySignResult(baseOrder, verifyResult, resp);
    }



    @Override
    protected VerifySignResult createResponse() {
        return new VerifySignResult();
    }

    @Override
    protected void process(VerifySignRequest request, VerifySignResult verifyResult) {

        Pair<InstBaseResult, VerifyResponseContent> resp = null;
        InstOrderType instOrderType = FundChannelApiType.getByCode(request.getApiType()) == FundChannelApiType.VERIFY_SIGN ?
                InstOrderType.FUND : InstOrderType.CONTROL;
        InstBaseOrder baseOrder = instOrderType == InstOrderType.FUND ? instOrderRepository.loadByNo(request.getInstOrderNo()) :
                instControlOrderRepository.loadByNo(request.getInstOrderNo());

        if (request.isAsync()) {
            verifySignService.asyncVerifyReq(request, baseOrder);
        } else {
            resp = verifySignService.verifyReq(request, baseOrder);
        }
        buildVerifySignResult(baseOrder, verifyResult, resp);

        // 如果card token中没有拿到PAGE_URL，尝试从请求体中获取
        if (StringUtils.isEmpty(verifyResult.getExtMap().get(ExtensionKey.PAGE_URL_FOR_SIGN.getKey()))){
            verifyResult.getExtMap().put(ExtensionKey.PAGE_URL_FOR_SIGN.getKey(), request.getExtension(ExtensionKey.RETURN_URL.getKey()));
        }

        if (StringUtils.isEmpty(verifyResult.getExtMap().get(ExtensionKey.PAGE_URL_FOR_SIGN.getKey()))){
            verifyResult.getExtMap().put(ExtensionKey.PAGE_URL_FOR_SIGN.getKey(), baseOrder.getExtension().get(ExtensionKey.RETURN_URL.getKey()));
        }
        log.info("VerifySignProcessor.verifySign.result:{}", verifyResult);
    }

    private void buildVerifySignResult(InstBaseOrder baseOrder, VerifySignResult verifyResult, Pair<InstBaseResult, VerifyResponseContent> resp) {
        verifyResult.setApplyStatus(ApplyStatusEnum.SUCCESS);
        verifyResult.setChannelCode(baseOrder.getFundChannelCode());
        verifyResult.setInstOrderNo(baseOrder.getInstOrderNo());

        if (resp != null) {
            verifyResult.setCode(ApplyStatusEnum.SUCCESS.getCode());
            verifyResult.setMessage(ApplyStatusEnum.SUCCESS.getMessage());
            verifyResult.setUnityResultCode(resp.getLeft().getInstResultCode());
            verifyResult.setExtMap(resp.getLeft().getExtension());
            if(resp.getRight()!=null){
                verifyResult.setResponseContent(resp.getRight().getResponseContent());
                verifyResult.setHeaderMap(resp.getRight().getHeaderMap());
            }
        }

        Map<String, String> extMap = verifyResult.getExtMap() != null ? verifyResult.getExtMap() : new HashMap<>();
        extMap.put(ExtensionKey.PAGE_URL_FOR_SIGN.key, buildSignUrl(baseOrder));
        verifyResult.setExtMap(extMap);
    }


    private String buildSignUrl(InstBaseOrder baseOrder) {
        CardToken cardToken = cardTokenRepository.queryByOrder(baseOrder);
        if (cardToken == null) {
            return Strings.EMPTY;
        }
        String resultUrl = cardToken.getResultUrl();
        /**
         * 若是外部url，则返回原值
         */
        if (StringUtils.isEmpty(resultUrl) || ChannelUtil.isOuterUrl(cardToken.getExtension())) {
            return resultUrl;
        }
        String paymentOrderNo = baseOrder.getExtension().get(ExtensionKey.PAYMENT_ORDER_NO.getKey());
        if (resultUrl.endsWith("paymentOrderNo=")) {
            return resultUrl + paymentOrderNo;
        }
        return resultUrl + (resultUrl.contains("?") ? "&" : "?") + ((baseOrder instanceof InstOrder) ? "paymentOrderNo=" + paymentOrderNo : "requestNo=" + ((InstControlOrder) baseOrder).getRequestNo());
    }


}
