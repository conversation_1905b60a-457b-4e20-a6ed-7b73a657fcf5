package com.uaepay.cmf.domainservice.main.sender;

import com.alibaba.fastjson.JSON;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.domain.ChannelCommonResult;
import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ControlOrderSendService.java v1.0  2020-09-12 00:53
 */
@Slf4j
@Service
public class ControlOrderSendService extends AbstractChannelSendService<InstControlOrder, ChannelRequest, ChannelResult> {

    @Override
    protected void updateOrderCommunicateStatus2Send(InstControlOrder order) {
        if (order.getOrderId() == null){
            return;
        }
        int modCount = instControlOrderRepository.updateCommunicateStatusByIdAndPreStatus(order,
                CommunicateStatus.IN_PROCESS, order.getCommunicateStatus());
        assertStatusIsUpdated(modCount);
    }

    @Override
    protected ChannelResult send2Bank(ChannelRequest request, InstControlOrder order) {
        // 针对不需要发送银行的情况，特殊处理
        if (notNeedSend2Channel(request.getFundChannelCode(), request.getApiType())) {
            return defaultSuccess(order);
        }
        ChannelCommonResult outerResult = channelSenderFactory.applyManager(request);

        ChannelResult result = null;
        try {
            result = (ChannelResult) JSON.parseObject(outerResult.getResultJason(),
                    Class.forName(outerResult.getResultClass()));
            bankFormService.processControlResult(order, result);
        } catch (ClassNotFoundException e) {
            return buildFailSendResp(request);
        }
        return result;
    }

    private ChannelResult defaultSuccess(InstControlOrder order) {
        ChannelResult result = new ChannelResult();
        result.setInstOrderNo(order.getInstOrderNo());
        result.setApiType(order.getApiType());
        result.setApiResultCode(DEFAULT_SUCCESS_CODE);
        result.setApiResultSubCode(DEFAULT_SUCCESS_SUB_CODE);
        result.setApiResultMessage(SUCCESS_MSG);
        return result;
    }

    @Value("${cmf.notSendChannelCodeList:^FAB2[0-9]{2}$}")
    private String notSendChannelCodeList;

    @Value("${cmf.notSendChannelApiTypeList:VT}")
    private String notSendChannelApiTypeList;

    private boolean notNeedSend2Channel(String channelCode, FundChannelApiType apiType) {
        // 不可以是资金类请求
        if (FundChannelApiType.isFund(apiType)) {
            return false;
        }
        try {
            if (Pattern.matches(notSendChannelCodeList, channelCode) && isNotSendApi(notSendChannelApiTypeList, apiType)) {
                return true;
            }
        } catch (Exception e) {
            log.error("notNeedSend2Bank.error", e);
        }
        return false;
    }

    private static boolean isNotSendApi(String notSendChannelApiTypeList, FundChannelApiType apiType) {
        return StringUtils.isNotEmpty(notSendChannelApiTypeList) && apiType != null && Arrays.asList(notSendChannelApiTypeList.split(CHAR_COMMA)).contains(apiType.getCode());
    }

    @Override
    protected ChannelResult buildResp() {
        return new ChannelResult();
    }

}
