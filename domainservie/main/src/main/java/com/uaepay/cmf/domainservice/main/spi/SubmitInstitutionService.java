package com.uaepay.cmf.domainservice.main.spi;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.institution.*;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;

/**
 * <p>提交机构服务</p>
 * <AUTHOR>
 * @version $Id: InstitutionSubmitService.java, v 0.1 2012-8-2 下午2:41:39 fuyangbiao Exp $
 */
public interface SubmitInstitutionService extends BasicConstant {

    /**
     * 提交资金订单
     * @param cmfOrder
     * @return
     */
    InstOrderResult submit(CmfOrder cmfOrder);

    /**
     * 提交机构订单
     * @param instOrder
     * @param apiType
     * @return
     */
    InstOrderResult submit(InstOrder instOrder, FundChannelApiType apiType);

    /**
     * 提交控制订单
     * @param order
     * @return
     */
    InstControlOrderResult submit(InstControlOrder order);

    /**
     * 提交控制订单
     * @param controlOrder
     * @param apiType
     * @return
     */
    InstControlOrderResult submit(InstControlOrder controlOrder, FundChannelApiType apiType);

    /**
     * verifySign提交订单
     * @param request
     * @param apiType
     * @return
     */
    InstBaseResult submit(VerifySignRequest request, FundChannelApiType apiType);

}
