package com.uaepay.cmf.domainservice.main.process.biz.impl;

import com.uaepay.cmf.common.core.dal.daointerface.FundoutOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.InstOrderDO;
import com.uaepay.cmf.common.core.domain.institution.InstFundoutOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.domainservice.main.convert.InstOrderConverter;
import com.uaepay.schema.cmf.enums.BizType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>出款业务处理器</p>
 *
 * <AUTHOR>
 * @date FundOutBizProcessor.java v1.0  2020-09-06 17:05
 */
@Service
public class FundOutBizProcessor extends AbstractBizProcessor {

    @Resource
    private FundoutOrderDAO fundOutOrderDAO;

    @Override
    public InstOrder convertDO2Dto(InstOrderDO instOrderDO) {
        return InstOrderConverter.convert(instOrderDO,
                fundOutOrderDAO.loadById(instOrderDO.getInstOrderId()));
    }

    @Override
    public void insertSubOrder(InstOrder instOrder) {
        fundOutOrderDAO
                .insert(InstOrderConverter.convert((InstFundoutOrder) instOrder));
    }

    @Override
    public void deleteSubOrder(Long instOrderId) {
        fundOutOrderDAO.delete(instOrderId);
    }

    @Override
    public BizType getBizType() {
        return BizType.FUNDOUT;
    }

    @Override
    public RequestType getRequestType() {
        return RequestType.FUND_OUT;
    }


}
