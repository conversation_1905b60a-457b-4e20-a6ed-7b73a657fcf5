package com.uaepay.cmf.domainservice.main.process.biz.impl;

import com.uaepay.cmf.common.core.dal.daointerface.FundinOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.InstOrderDO;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstFundinOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.domainservice.main.convert.InstOrderConverter;
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>入款业务处理器</p>
 *
 * <AUTHOR>
 * @date FundInBizProcessor.java v1.0  2020-09-06 00:48
 */
@Service
public class FundInBizProcessor extends AbstractBizProcessor {

    @Resource
    private FundinOrderDAO fundinOrderDAO;

    @Resource
    private InstOrderResultRepository instOrderResultRepository;

    @Value("${cmf.instSeq.channels:CS101,CS102,CS103,CS104,CS105,CS106}")
    private String instSeqChannels;

    @Override
    public InstOrder convertDO2Dto(InstOrderDO instOrderDO) {
        InstOrder instOrder = InstOrderConverter.convert(instOrderDO,
                fundinOrderDAO.loadById(instOrderDO.getInstOrderId()));
        // 通过机构结果取银行返回流水号，以后要将流水号放到instOrder表中
        if (StringUtils.isEmpty(instOrder.getInstSeqNo()) && instSeqChannels.contains(instOrder.getFundChannelCode())) {
            if (instOrder.getExtension() != null && instOrder.getExtension().containsKey(ExtensionKey.INST_SEQ_NO.key)) {
                instOrder.setInstSeqNo(instOrder.getExtension().get(ExtensionKey.INST_SEQ_NO.key));
            } else {
                InstOrderResult realResult = instOrderResultRepository.loadRealResultByOrder(instOrder.getInstOrderId());
                if (realResult != null && StringUtils.isNotEmpty(realResult.getInstSeqNo())) {
                    instOrder.setInstSeqNo(realResult.getInstSeqNo());
                }
            }
        }
        return instOrder;
    }

    @Override
    public void insertSubOrder(InstOrder instOrder) {
        fundinOrderDAO.insert(InstOrderConverter.convert((InstFundinOrder) instOrder));
    }

    @Override
    public void deleteSubOrder(Long instOrderId) {
        fundinOrderDAO.delete(instOrderId);
    }

    @Override
    public BizType getBizType() {
        return BizType.FUNDIN;
    }

    @Override
    public RequestType getRequestType() {
        return RequestType.FUND_IN;
    }

}
