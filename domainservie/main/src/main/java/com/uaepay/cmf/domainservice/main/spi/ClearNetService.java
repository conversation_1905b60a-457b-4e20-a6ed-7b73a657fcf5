package com.uaepay.cmf.domainservice.main.spi;

import com.uaepay.cmf.common.core.domain.enums.ClearNetEnum;
import com.uaepay.cmf.service.facade.domain.clear.ClearInfo;
import com.uaepay.router.service.facade.domain.channel.InstCurrencyVO;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ClearNetService.java v1.0
 */
public interface ClearNetService {
    List<ClearInfo> convert(List<InstCurrencyVO> instCurrencies, ClearNetEnum clearNet);
}
