package com.uaepay.cmf.domainservice.main.convert;

import java.util.HashMap;
import java.util.Map;

import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.OrderRiskStatus;
import com.uaepay.cmf.common.core.domain.exception.AppRuntimeException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import com.uaepay.cmf.service.facade.result.CmfResult;
import org.apache.commons.lang3.StringUtils;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;

/**
 * 
 * <p>新PE结果通知转换类</p>
 * <AUTHOR> Liu
 * @version $Id: InstitutionCmfResultConverter.java, v 0.1 2013-3-20 上午10:32:27 liumaoli Exp $
 */
public class InstitutionCmfResultConverter {

    private static final String DEFAULT_SETTLEMENT_ID = "0";

    /**
     * 依据cmf订单创建通知pe请求对象
     * @param cmfOrder
     * @return
     */
    public static CmfResult convert(CmfOrder cmfOrder) {
        CmfResult cmfResult = new CmfResult();
        cmfResult.setAmount(cmfOrder.getAmount());
        Map<String, String> map = new HashMap<>();
        map.put(ExtensionKey.SETTLEMENT_ID.key, DEFAULT_SETTLEMENT_ID.equals(cmfOrder
            .getSettlementId()) ? null : cmfOrder.getSettlementId());
        map.put(ExtensionKey.BIZ_TYPE.key,cmfOrder.getBizType().getCode());
        cmfResult.setExtension(MapUtil.mapToJson(map));
        cmfResult.setFundsChannelCode(cmfOrder.getFundChannelCode());
        cmfResult.setInstPayTime(cmfOrder.getGmtCreate());
        cmfResult.setPaymentSeqNo(cmfOrder.getPaymentSeqNo());
        combileFundReturnInfo(cmfResult, InstOrderStatus.FAILURE, cmfOrder.getBizType(), "内部落地失败");
        return cmfResult;
    }

    /**
     * 依据信息转换PE结果请求对象
     * @param cmfOrder
     * @param instOrder
     * @param result
     * @return
     */
    public static CmfResult convert(CmfOrder cmfOrder, InstOrder instOrder, InstOrderResult result) {
        CmfResult cmfResult = new CmfResult();
        cmfResult.setAmount(cmfOrder.getAmount());

        cmfResult.setFundsChannelCode(instOrder.getFundChannelCode());
        cmfResult.setInstPayNo(instOrder.getInstOrderNo());
        cmfResult.setInstPayTime((result != null && result.getGmtCreate() !=null) ? result.getGmtCreate() : cmfOrder.getGmtCreate());
        cmfResult.setPaymentSeqNo(cmfOrder.getPaymentSeqNo());
        combileFundReturnInfo(cmfResult, instOrder.getStatus(), cmfOrder.getBizType(),
            (result != null && StringUtils.isNotEmpty(result.getMemo())) ? result.getMemo() : "");

        Map<String, String> extension = new HashMap<>();
        extension.put(ExtensionKey.BIZ_TYPE.getKey(), instOrder.getBizType().getCode());
        extension.put(ExtensionKey.TRANSFORM_FLAG.getKey(), instOrder.getExtension().get(ExtensionKey.TRANSFORM_FLAG.getKey()));
        if (result != null) {
            cmfResult.setUnityResultCode(result.getInstResultCode());
            String message = result.getExtension().get(ExtensionKey.UNITY_RESULT_MESSAGE.key);
            cmfResult.setUnityResultMessage(StringUtils.isEmpty(message) ? "" : message);
            if (OrderRiskStatus.IN_PROCESS.equals(instOrder.getRiskStatus())) {
                result.getExtension().put(ExtensionKey.IS_BANK_RISK.key, YesNo.YES.getCode());
            }
            result.getExtension().put(ExtensionKey.SETTLEMENT_ID.key,
                    DEFAULT_SETTLEMENT_ID.equals(cmfOrder.getSettlementId()) ? null : cmfOrder.getSettlementId());
            if(result.getRealAmount()!=null && cmfOrder.getBizType()==BizType.FUNDOUT){
                result.getExtension().put(ExtensionKey.ARRIVAL_AMOUNT.key, result.getRealAmount().getAmount().toString());
                result.getExtension().put(ExtensionKey.ARRIVAL_CURRENCY.key, result.getRealAmount().getCurrency());
            }
            extension.putAll(result.getExtension());
        }

        cmfResult.setExtension(MapUtil.mapToJson(extension));
        return cmfResult;
    }

    /**
     * 组装机构返回结果信息
     *
     * @param status
     * @return
     */
    private static void combileFundReturnInfo(CmfResult cmfResult, InstOrderStatus status,
                                              BizType bizType, String message) {
        if (InstOrderStatus.SUCCESSFUL.equals(status)) {
            cmfResult.setReturnCode(CmfFundResultCode.SUCCESS.getCode());
            cmfResult.setReturnMessage(CmfFundResultCode.SUCCESS.getMessage() + " " + message);
        } else if (InstOrderStatus.FAILURE.equals(status)) {
            cmfResult.setReturnCode(CmfFundResultCode.FAILED.getCode());
            cmfResult.setReturnMessage(CmfFundResultCode.FAILED.getMessage() + " " + message);
            //出款处理中的订单，通知PE出款渠道，及channelPayNo
        } else if (BizType.FUNDOUT == bizType && InstOrderStatus.IN_PROCESS == status) {
            cmfResult.setReturnCode(CmfFundResultCode.SUBMIT_INST.getCode());
            cmfResult.setReturnMessage(CmfFundResultCode.SUBMIT_INST.getMessage() + " " + message);
        } else {
            throw new AppRuntimeException("MQ通知，机构订单状态不对,instStatus:" + status + ", bizType:"
                                          + bizType);
        }
    }
}
