package com.uaepay.cmf.domainservice.channel.alert;

import com.uaepay.cmf.common.core.domain.enums.MonitorType;
import com.uaepay.cmf.common.monitor.MonitorLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
//import com.uaepay.mns.service.notify.enums.NotifyProtocol;

/**
 * <p>监控服务</p>
 *
 * <AUTHOR> won
 * @version $Id: MonitorServiceImpl.java, v 0.1 2011-7-20 下午03:12:47 sean won Exp $
 */
@Service
public class AlertServiceImpl implements AlertService {
    Logger logger = LoggerFactory.getLogger(AlertServiceImpl.class);


//    @Resource
//    private LockService lockService;

    @Override
    public void alertMonitorEvent(MonitorLog monitorLog, MonitorType monitorType) {

        if (monitorType == null || monitorLog == null) {
            return;
        }

        try {
            boolean lockStatus;
//            //1:鉴于可能会有批量报警，会对mns造成压力且大部分报警无效，需要做时间间隔控制
//            Lock lock = new Lock(monitorType.getMonitorKey(), monitorType.getMessage(), LockType.EXCLUSION, monitorType.getMonitorInterval());
//            try {
//                lockStatus = lockService.lock(lock);
//            } catch (Exception e) {
//                logger.error(monitorType.getMonitorKey(), e);
//                return;
//            }
//            if (!lockStatus) {
//                return;
//            }

            //2:发送短信&邮件
//            mnsNotifyClient.sendMsg(monitorLog.getEventMessage(), NotifyProtocol.SNS, "");
//
//            mnsNotifyClient.sendMsg(monitorLog.getEventMessage(), NotifyProtocol.MAIL, "");

        } catch (Exception e) {
            logger.error("监控异常. monitorLog=" + monitorLog, e);
        }


    }
}
