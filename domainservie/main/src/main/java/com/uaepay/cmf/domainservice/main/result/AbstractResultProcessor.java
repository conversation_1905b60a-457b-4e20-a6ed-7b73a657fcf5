package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.exception.ResultProcessException;
import com.uaepay.cmf.common.core.domain.exception.WrongOrderResultException;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.common.core.domain.institution.InstCommonOrder;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.domainservice.channel.limit.LimitService;
import com.uaepay.cmf.domainservice.main.pattern.CardTokenService;
import com.uaepay.cmf.domainservice.main.process.*;
import com.uaepay.cmf.domainservice.main.repository.*;
import com.uaepay.cmf.domainservice.main.validate.ChannelResultValidator;
import com.uaepay.cmf.fss.ext.integration.ues.UesClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>结果处理器， 该步骤为发送银行后，得到的结果处理</p>
 *
 * <AUTHOR>
 * @date AbstractResultProcessor.java v1.0  2020-09-12 10:06
 */
@Slf4j
public abstract class AbstractResultProcessor<Order extends InstCommonOrder, Cs extends ChannelResult, Res extends InstBaseResult> implements BasicConstant {

    @Resource
    protected InstOrderRepository instOrderRepository;
    @Resource
    protected InstControlOrderRepository controlOrderRepository;
    @Resource
    protected InstOrderResultRepository instOrderResultRepository;
    @Resource
    protected InstControlOrderResultRepository controlOrderResultRepository;
    @Resource
    protected MonitorService monitorService;
    @Resource
    protected ChannelResultValidator channelResultValidator;

    @Resource
    protected CmfOrderRepository cmfOrderRepository;

    @Resource
    protected NotifyPaymentService notifyPaymentService;

    @Resource
    protected NotifyCashdeskService notifyCashdeskService;

    @Resource
    protected NotifyEscrowService notifyEscrowService;

    @Resource
    protected NotifyCounterService notifyCounterService;
    @Resource
    protected LimitService limitService;
    @Resource
    protected CardTokenService cardTokenService;

    @Resource
    protected DuplicateResultProcessService resultProcess;

    @Resource
    protected ResultCodeService resultCodeService;

    @Resource
    protected SaveBankReceiveService saveBankReceiveService;

    @Resource
    private UesClient uesClient;

    @Resource(name = "memoryCacheClient")
    private CacheClient cacheClient;



    static Map<CommunicateStatus, List<CommunicateStatus>> statusMapping = new HashMap<>(20);

    static Map<InstOrderStatus, List<InstOrderStatus>> orderStatusMapping = new HashMap<>(20);

    static Map<InstOrderProcessStatus, CommunicateStatus> targetCommStatusMap = new HashMap<>(8);

    static Map<InstOrderResultStatus, InstOrderStatus> instOrderFinalStatusMap = new HashMap<>(4);

    static {
        // A->I ->R
        // I->F I->S I->R
        // FIXME:重发操作F->I->..
        // F->S F->R,F->F
        // S->R, S->S
        // R->R

        statusMapping.put(CommunicateStatus.IN_PROCESS, new ArrayList<>(
                Arrays.asList(CommunicateStatus.AWAITING, CommunicateStatus.SENT, CommunicateStatus.FAILURE)));
        statusMapping.put(CommunicateStatus.FAILURE,
                new ArrayList<>(Arrays.asList(CommunicateStatus.IN_PROCESS, CommunicateStatus.SENT, CommunicateStatus.FAILURE)));
        statusMapping.put(CommunicateStatus.SENT, new ArrayList<>(
                Arrays.asList(CommunicateStatus.IN_PROCESS, CommunicateStatus.FAILURE, CommunicateStatus.SENT)));
        statusMapping.put(CommunicateStatus.RECEIVED,
                new ArrayList<>(Arrays.asList(CommunicateStatus.IN_PROCESS, CommunicateStatus.FAILURE,
                        CommunicateStatus.AWAITING, CommunicateStatus.SENT, CommunicateStatus.RECEIVED)));

        /**
         * I -> C/S/F/R/H/V
         * C -> F
         * V -> I/F/C/S
         */
        orderStatusMapping.put(InstOrderStatus.SUCCESSFUL, Arrays.asList(InstOrderStatus.IN_PROCESS,InstOrderStatus.HALF_SUCCESSFUL, InstOrderStatus.REDIRECT_VERIFY));
        orderStatusMapping.put(InstOrderStatus.HALF_SUCCESSFUL, Collections.singletonList(InstOrderStatus.IN_PROCESS));
        orderStatusMapping.put(InstOrderStatus.FAILURE, Arrays.asList(InstOrderStatus.IN_PROCESS, InstOrderStatus.CANCEL, InstOrderStatus.REDIRECT_VERIFY));
        orderStatusMapping.put(InstOrderStatus.CANCEL, Arrays.asList(InstOrderStatus.IN_PROCESS,InstOrderStatus.HALF_SUCCESSFUL, InstOrderStatus.REDIRECT_VERIFY));
        orderStatusMapping.put(InstOrderStatus.RISK, Collections.singletonList(InstOrderStatus.IN_PROCESS));
        orderStatusMapping.put(InstOrderStatus.REDIRECT_VERIFY, Collections.singletonList(InstOrderStatus.IN_PROCESS));

        // 发送状态映射
        targetCommStatusMap.put(InstOrderProcessStatus.AWAITING, CommunicateStatus.SENT);
        targetCommStatusMap.put(InstOrderProcessStatus.SUCCESS, CommunicateStatus.RECEIVED);
        targetCommStatusMap.put(InstOrderProcessStatus.SUBMIT_INST_FAIL, CommunicateStatus.FAILURE);

        // 订单状态映射
        instOrderFinalStatusMap.put(InstOrderResultStatus.SUCCESSFUL, InstOrderStatus.SUCCESSFUL);
        instOrderFinalStatusMap.put(InstOrderResultStatus.FAILURE, InstOrderStatus.FAILURE);
    }

    /**
     * 处理机构结果
     *
     * @param order
     * @param cs
     * @return
     */
    public Res process(Order order, Cs cs) {

        // 将渠道结果转换为订单结果
        Res res = convert2InstResp(order, cs);
        try {

            // 调用外部系统进行结果码转换并回写信息
            convertResultCode(res);

            // 调用处理
            process(order, res);

            return res;
        } catch (ResultProcessException ex) {
            log.error("ResultProcessor.process.error", ex);
            res.setProcessStatus(InstOrderProcessStatus.UNKNOW_EXCEPTION);
            res.setStatus(InstOrderResultStatus.IN_PROCESS);
            return res;
        }
    }

    /**
     * 处理订单结果
     *
     * @param order
     * @param res
     * @return
     */
    public CommonResponse process(Order order, Res res) {

        try {
            // 结果校验
            validateResult(order, res);

            // 过风控-待补充

            // 返回结果保存，订单状态跃迁
            storeResult(order, res);

            // 后续处理，发送通知
            laterProcess(order, res);

            // 后续通用处理
            commonLaterProcess(order, res);

            return CommonResponse.buildSuccess("处理成功");
        } catch (ResultProcessException | WrongOrderResultException wore) {
            log.warn("ResultProcessor.orderStatus.error:errorCode-{},message-{}", wore.getErrorCode(), wore.getMessage());
            putRespInProcess(res, wore.getErrorCode());
            return CommonResponse.buildFail(ApplyStatusEnum.FAIL, wore.getMessage());
        } catch (IllegalArgumentException iae) {
            log.warn("ResultProcessor.orderStatus.iae:order-{},message-{}", order , res);
            putRespInProcess(res, ErrorCode.WRONG_STATE_EXCEPTION);
            return CommonResponse.buildFail(ApplyStatusEnum.FAIL, iae.getMessage());
        }
    }

    protected void putRespInProcess(Res res, ErrorCode errorCode) {
        res.setProcessStatus(InstOrderProcessStatus.UNKNOW_EXCEPTION);
        res.setStatus(InstOrderResultStatus.IN_PROCESS);
        if (errorCode != null) {
            res.setMemo(errorCode.getErrorMessage());
        }
    }


    protected abstract void validateResult(Order order, Res res);

    private void convertResultCode(Res res) {
        resultCodeService.fillResultStatus(res);
    }

    protected abstract Res convert2InstResp(Order order, Cs cs);

    protected abstract void storeResult(Order order, Res res);

    protected abstract void laterProcess(Order order, Res res);

    private void commonLaterProcess(Order order, Res res){

        // 安全规则： 清除临时cvv
        String cardToken = order.getExtension().get("cardToken");
        String clearCvv = order.getExtension().get("clearCvv");
        String csc;
        if ((res.getStatus().isFinalResult() || "Y".equals(clearCvv))
                && StringUtils.isNotBlank(cardToken)
                && (csc = (String) cacheClient.get(CacheType.CSC, cardToken)) != null) {
            uesClient.deleteTempDataByTicket(csc);
            log.info("cvv加密令牌已删除");
        }
    }


}
