package com.uaepay.cmf.domainservice.main.process.biz.impl;

import com.uaepay.cmf.domainservice.main.factory.BizProcessorFactory;
import com.uaepay.cmf.domainservice.main.process.biz.BizProcessor;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date AbstractBizProcessor.java v1.0  2020-09-06 00:47
 */
public abstract class AbstractBizProcessor implements BizProcessor {

    @Resource
    private BizProcessorFactory bizProcessorFactory;

    @PostConstruct
    void register() {
        bizProcessorFactory.put(this.getBizType(), this);
    }


}
