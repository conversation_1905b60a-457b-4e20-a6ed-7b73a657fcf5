package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;

/**
 * 
 * <p>通知PE支付结果接口</p>
 * <AUTHOR>
 * @version $Id: NotifyPaymentService.java, v 0.1 2012-8-14 下午2:53:24 liumaoli Exp $
 */
public interface NotifyPaymentService extends BasicConstant{

    /**
     * 通知PE支付结果
     * @param cmfSeqNo
     */
    void notifyPE(String cmfSeqNo, Boolean isRetry);

    /**
     * 通知PE支付结果
     * @param cmfOrder
     * @param instOrder
     * @param result
     */
    void notifyPE(CmfOrder cmfOrder, InstOrder instOrder, InstOrderResult result,
                  Boolean isRetry);

    /**
     * 超时通知PE
     * @param cmfOrder
     * @param instResult
     */
    void notifyResult(CmfOrder cmfOrder, InstOrderResult instResult);
}
