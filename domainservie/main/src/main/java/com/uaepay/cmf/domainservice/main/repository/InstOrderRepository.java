package com.uaepay.cmf.domainservice.main.repository;

import com.uaepay.basis.beacon.service.facade.domain.request.PageRequest;
import com.uaepay.biz.common.util.PageList;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.exception.WrongStateException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.service.facade.domain.query.BatchOrderQueryRequest;
import com.uaepay.cmf.service.facade.enums.ArchiveStatusEnum;
import com.uaepay.common.lang.Paginator;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>提交机构订单仓储</p>
 *
 * <AUTHOR>
 * @version $Id: InstOrderRepository.java, v 0.1 2012-8-2 下午2:46:16 fuyangbiao Exp $
 */
public interface InstOrderRepository extends BasicConstant {

    /**
     * 根据CMF订单流水号获取机构订单列表
     *
     * TODO 如果结果中是一条记录会加个空记录，避坑
     *
     * @param cmfSeqNo
     * @return
     */
    List<InstOrder> loadByCmfSeqNo(String cmfSeqNo);

    /**
     * 根据CMF订单流水号获取机构订单列表
     *
     * @param cmfSeqNo
     * @return
     */
    InstOrder loadByCmfSeqNoSingle(String cmfSeqNo);

    /**
     * 根据ID获取机构订单
     *
     * @param id
     * @param isLock 是否加锁
     * @return
     */
    InstOrder loadById(Long id, boolean isLock);

    /**
     * 根据提交机构订单号获取机构订单
     *
     * @param instOrderNo
     * @return
     */
    InstOrder loadByNo(String instOrderNo);

    boolean updateBookingSubmit(InstOrder instOrder, Date bookingSubmit);

    boolean updateInstOrderStatus(InstOrder instOrder, InstOrderStatus targetStatus);

    boolean updateInstOrderStatusNoCheck(InstOrder instOrder, InstOrderStatus targetStatus);

    /**
     * 单笔存储机构订单
     *
     * @param order
     */
    void store(InstOrder order);

    /**
     * 批量存储机构订单
     *
     * @param instOrderList
     */
    void store(List<InstOrder> instOrderList);

    Pair<List<InstOrder>, Paginator> queryChannelOrders(PageRequest pageRequest);

    /**
     * 基于原标记更新标记,补单时使用
     *
     * @param instOrderId
     * @param flag
     * @param preFlag
     * @return
     */
    int updateFlagWithOrderIdAndPreFlag(Long instOrderId, OrderFlag flag, OrderFlag preFlag);

    /**
     * 更新渠道信息
     *
     * @param fundChannelCode
     * @param instOrderId
     * @return
     */
    int updateChannelInfoById(String fundChannelCode, String fundChannelApi, long instOrderId);

    /**
     * 根据入款机构订单号查询充退订单
     *
     * @param fundInOrderNo
     * @param orderStatusList
     * @return
     */
    List<InstOrder> getRefundOrderByFundInOrder(String fundInOrderNo,
                                                List<InstOrderStatus> orderStatusList);

    /**
     * 更新机构订单状态
     *
     * @param instOrderId
     * @param targetStatus
     * @param preStatus
     * @return
     */
    int updateStatusById(long instOrderId, InstOrderStatus targetStatus, InstOrderStatus preStatus);

    /**
     * 更新机构单金额
     * @param instOrderId
     * @param amount
     * @return
     */
    int updateAmountAndExtension(long instOrderId, Money amount, String extension);

    /**
     * 更新通讯状态
     *
     * @param communicateStatus
     * @param instOrderId
     * @return
     */
    int updateCommunicateStatusById(CommunicateStatus communicateStatus, long instOrderId);

    /**
     * 依据初始通讯状态更新通讯状态
     *
     * @param communicateStatus
     * @param instOrder
     * @param preStatus
     * @return
     */

    int updateCommunicateStatusWithPreStatus(InstOrder instOrder,
                                             CommunicateStatus communicateStatus,
                                             CommunicateStatus preStatus);

    /**
     * 依据批次号获取机构订单信息
     *
     * @param archiveBatchId
     * @return
     */
    List<InstOrder> getInstOrderListByAichiveBatchId(Long archiveBatchId);

    /**
     * 根据批次号获取
     *
     * @param archiveBatchId
     * @return
     */
    List<Long> loadInstOrderIdListByBatchId(Long archiveBatchId);

    /**
     * 根据批次号和币种获取订单列表
     *
     * @param archiveBatchId
     * @param currency
     * @return
     */
    List<Long> loadInstOrderIdListByBatchIdAndCurrency(Long archiveBatchId, String currency);

    /**
     * 获取币种列表
     * @param archiveBatchId
     * @return
     */
    List<String> loadOrderCurrencyListByBatchId(Long archiveBatchId);

    /**
     * 依据批次号，订单发送状态，订单状态获取机构订单信息
     *
     * @param archiveBatchId
     * @param communicateStatus
     * @param instOrderStatus
     * @return
     */
    List<InstOrder> getInstOrderListByBatchIdStatus(Long archiveBatchId, Date archiveDate,
                                                    CommunicateStatus communicateStatus,
                                                    InstOrderStatus instOrderStatus);

    /**
     * 获取批次订单
     *
     * @param archiveBatchId
     * @return
     */
    InstBatchOrder loadById(Long archiveBatchId);

    /**
     * 获取批次订单带锁
     *
     * @param archiveBatchId
     * @return
     */
    InstBatchOrder loadByIdForUpdate(Long archiveBatchId);

    /**
     * 更新补单信息
     *
     * @param gmtNextRetry
     * @param instOrderId
     * @return
     */
    int updateRetryInfoById(Integer retryTimes, Date gmtNextRetry, Long instOrderId);

    int updateBatchRetryInfoById(Integer retryTimes, Date gmtNextRetry, Long instOrderId);

    /**
     * 批次信息更新金额和总数
     *
     * @param instBatchOrder
     */
    void updateAmountAndCount(InstBatchOrder instBatchOrder);

    /**
     * 更新batchID，根据老的batchID及instOrderIds (指定更新范围).
     *
     * @param tempBatchId
     * @param archiveBatchId
     * @param instOrderIdList
     * @return
     */
    int updateBatchIdListByTempBatchId(Long tempBatchId, Long archiveBatchId,
                                       List<Long> instOrderIdList);

    Money getArchiveBatchAmt(Long archiveBatchId);

    int getArchiveBatchCnt(Long archiveBatchId);

    /**
     * 保存批次订单
     *
     * @param instBatchOrder
     */
    void insert(InstBatchOrder instBatchOrder);

    /**
     * 重新读取instBatchOrder统计信息
     *
     * @param instBatchOrder
     */
    void reloadInstBatchOrder(InstBatchOrder instBatchOrder);

    /**
     * 统计已退款金额
     *
     * @param fundInInstOrderNo
     * @return
     */
    Money getHasRefundAmount(String fundInInstOrderNo, String currency);

    /**
     * 更新到临时批次
     *
     * @param templateId
     * @param apiCode
     * @param tempBatchId
     * @param hours
     * @return
     */
    int updateTempBatchId(Long templateId, String apiCode, Long tempBatchId,
                          Long hours, Date bookingTime);

    List<Long> loadInstOrderList4ArchivePage(Long archiveTemplateId, String apiCode, Long hours, Date bookingTime, String communicateType);

    int getArchivePages(Long archiveTemplateId, String apiCode, Long hours, Date bookingTime, String communicateType);

    Integer updateBatchByInstOrderId(Long tempBatchId, List<Long> instOrderIdList);

    /**
     * 临时批次恢复
     *
     * @param tempBatchId
     * @return
     */
    int updateBatchId2Default(Long tempBatchId);

    /**
     * 保存扩展信息
     *
     * @param order
     */
    void storeExtension(InstOrder order);

    /**
     * 保存扩展参数
     * @param order
     */
    void storeExtension(InstBatchOrder order);

    /**
     * 校验该订单是否属于拆分订单
     * 1.非拆分,返回true
     * 2.拆分且都成功或都失败,返回true
     * 3.拆分存在处理中的记录,返回false
     * 4.拆分存在状态不一致的,抛异常
     *
     * @param instOrder
     * @return
     */
    boolean isCompleteSuccess(InstOrder instOrder) throws WrongStateException;

    /**
     * 更新备注信息
     *
     * @param memo
     * @param instOrderId
     * @return
     */
    int updateMemoById(String memo, Long instOrderId);

    /**
     * 累计待出款金额
     *
     * @param communicateStatusList
     * @param orderType
     * @param channelCodeList
     * @return
     */
    Money sumAmountForQueryResult(List<String> communicateStatusList, String orderType,
                                  List<String> channelCodeList, Date startDate, Date endDate);

    /**
     * 依据机构订单id删除机构订单信息
     *
     * @param instOrder
     */
    void delete(InstOrder instOrder);

    /**
     * 修改订单推进状态
     *
     * @param instOrderId
     * @param advanceStatus
     * @param preStatus
     * @return
     */
    int updateAdvanceStatusWithPreStatus(IsAdvance advanceStatus, Long instOrderId,
                                         IsAdvance preStatus);

    /**
     * 批次订单是否完成
     *
     * @param instBatchOrder
     * @return
     * @throws WrongStateException
     */
    boolean isBatchComplete(InstBatchOrder instBatchOrder);

    /**
     * 银企直连批量出款
     *
     * @param archiveStatus
     * @param isLocked
     * @param apiCode
     * @param operator
     * @param batchSize
     * @param page
     * @return
     */
    List<Long> loadBatchInstOrders(ArchiveStatusEnum archiveStatus, YesNo isLocked, String apiCode,
                                   String operator, int batchSize, int page);

    int updateIsLockedByOriStatus(List<Long> archiveBatchIdList, YesNo status, YesNo oriStatus);

    int updateIsLockedByOriStatus(Long archiveBatchId, YesNo status, YesNo oriStatus);

    /**
     * 更改批量订单状态
     *
     * @param archiveBatchId
     * @param archiveStatus
     * @param preStatus
     * @return
     */
    int updateBatchOrderStatus(Long archiveBatchId, InstOrderArchiveStatus archiveStatus,
                               InstOrderArchiveStatus preStatus);

    /**
     * 批量更改交互状态
     *
     * @param instOrderList
     * @param communicateStatus
     * @param preStatus
     * @return
     */
    int updateCommunicateStatusByInstOrders(List<InstOrder> instOrderList,
                                            CommunicateStatus communicateStatus,
                                            CommunicateStatus preStatus);

    int updateOrderNoAndStatus(String newInstOrderNo, InstOrder instOrder);

    /**
     * 保存机构订单并回写机构订单id
     *
     * @param instOrderList
     */
    void storeInstOrder(List<InstOrder> instOrderList);

    /**
     * 捞取单笔订单以查询
     *
     * @param startTime
     * @param endTime
     * @param maxSize
     * @param bizType
     * @return
     */
    Set<Long> loadSingleOrder4Query(Date startTime, Date endTime, int maxSize, BizType bizType);

    /**
     * @param channelList
     * @param bizType
     * @param maxSize
     * @return
     */
    Set<Long> loadInstOrder4Send(List<String> channelList, List<String> ignoreChannelList, BizType bizType, Date startDate,
                                 Date endDate, int maxSize);

    int updateCommunicateStatusByArchiveBatchId(long archiveBatchId, CommunicateStatus targetStatus);

    List<String> queryMigrateDateList(String channelCode);

    PageList queryBatchList(BatchOrderQueryRequest request);

    boolean updateBatchRetryById(Date gmtArchive, Long archiveBatchId);

    List<InstOrder> loadInstOrderListByIds(List<Long> instOrderIds);


    int updateRetryDataWithPreStatus(InstOrder sendOrder, CommunicateStatus communicateStatus, CommunicateStatus communicateStatus1);
}
