package com.uaepay.cmf.domainservice.main.process.impl;

import com.alibaba.fastjson.JSON;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.vo.BindCardInfo;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.domainservice.main.process.NotifyBindCardInfoService;
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository;
import com.uaepay.cmf.fss.ext.integration.notify.BindCardResultNotify;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date NotifyBindCardInfoServiceImpl.java v1.0
 */
@Service
public class NotifyBindCardInfoServiceImpl implements NotifyBindCardInfoService {

    @Resource
    private CardTokenRepository cardTokenRepository;
    @Resource
    private BindCardResultNotify bindCardResultNotify;

    @Override
    public void notifyInfo(InstControlOrder controlOrder, InstControlOrderResult controlResult) {
        BindCardInfo info = convert(controlOrder, controlResult);
        if (info != null) {
            bindCardResultNotify.notifyResult(info);
        }
    }

    private BindCardInfo convert(InstControlOrder controlOrder, InstControlOrderResult controlResult) {
        CardToken cardToken = cardTokenRepository.queryByInstOrderId(controlOrder.getOrderId());
        if (controlOrder.getStatus() == InstOrderStatus.IN_PROCESS || cardToken == null) {
            return null;
        }
        BindCardInfo info = new BindCardInfo();
        BeanUtils.copyProperties(cardToken, info);
        info.setChannelCode(controlOrder.getFundChannelCode());
        info.setInstCode(controlOrder.getInstCode());
        info.setInstOrderNo(controlOrder.getInstOrderNo());
        info.setRequestNo(controlOrder.getRequestNo());
        info.setOrderStatus(controlOrder.getStatus().getCode());
        info.setUnityResultCode(controlResult.getInstResultCode());
        info.setMessage(controlResult.getResultMessage());

        // 补充渠道扩展字段
        Map<String, String> extension = new HashMap<>();
        if (StringUtils.isNotBlank(info.getExtension()) && JSON.isValidObject(info.getExtension())){
            extension = MapUtil.jsonToMap(info.getExtension());
        }
        extension.putAll(controlResult.getExtension());
        info.setExtension(MapUtil.mapToJson(extension));

        return info;
    }


}
