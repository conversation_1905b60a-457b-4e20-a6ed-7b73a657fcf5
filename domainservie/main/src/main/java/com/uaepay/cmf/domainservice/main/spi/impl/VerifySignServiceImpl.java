package com.uaepay.cmf.domainservice.main.spi.impl;

import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.exception.DuplicateRequestException;
import com.uaepay.cmf.common.core.domain.institution.*;
import com.uaepay.cmf.common.core.domain.vo.VerifyResponseContent;
import com.uaepay.cmf.common.domain.fundin.ebank.EBankChannelVerifyResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.TokenHolder;
import com.uaepay.cmf.domainservice.channel.holder.VerifySignHolder;
import com.uaepay.cmf.domainservice.channel.router.ConfigurationService;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.domainservice.main.spi.VerifySignService;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date VerifySignService.java v1.0  2020-04-02 17:24
 */
@Service
public class VerifySignServiceImpl implements VerifySignService {


    @Resource
    private SubmitInstitutionService submitInstitutionService;
    @Resource
    private ConfigurationService configurationService;
    @Resource
    private InstOrderRepository instOrderRepository;


    @Async
    @Override
    public void asyncVerify(VerifySignRequest request) throws DuplicateRequestException {
        try {
            VerifySignHolder.set(request);
            InstOrder instOrder = prepareVerify(request);
            submitInstitutionService.submit(instOrder, FundChannelApiType.getByCode(request.getApiType()));
        } finally {
            VerifySignHolder.clear();
            TokenHolder.clear();
        }
    }

    @Override
    public InstOrderResult verify(VerifySignRequest request) throws DuplicateRequestException {
        try {
            VerifySignHolder.set(request);
            InstOrder instOrder = prepareVerify(request);
            return submitInstitutionService.submit(instOrder, FundChannelApiType.getByCode(request.getApiType()));
        } finally {
            VerifySignHolder.clear();
            TokenHolder.clear();
        }
    }

    @Async
    @Override
    public void asyncVerifyReq(VerifySignRequest request, InstBaseOrder baseOrder) {
        verifyReq(request, baseOrder);
    }

    @Override
    public Pair<InstBaseResult, VerifyResponseContent> verifyReq(VerifySignRequest request, InstBaseOrder baseOrder) {


        try {
            VerifySignHolder.set(request);
            FundChannelApiType apiType = FundChannelApiType.getByCode(request.getApiType());
            configurationService.queryCardToken(baseOrder.getPayMode(), baseOrder.getExtension());
            InstBaseResult baseResult = null;
            if (apiType == FundChannelApiType.VERIFY_SIGN) {
                baseResult = submitInstitutionService.submit((InstOrder) baseOrder, apiType);
            } else {
                baseResult = submitInstitutionService.submit((InstControlOrder) baseOrder, apiType);
            }
            return Pair.of(baseResult, VerifySignHolder.getResult());
        } finally {
            VerifySignHolder.clear();
            TokenHolder.clear();
        }
    }

    @Async
    @Override
    public void asyncVerifyReqNonOrder(VerifySignRequest request) {
        verifyReqNonOrder(request);
    }

    @Override
    public InstBaseResult verifyReqNonOrder(VerifySignRequest request) {

        try {
            VerifySignHolder.set(request);
            FundChannelApiType apiType = FundChannelApiType.getByCode(request.getApiType());
            return submitInstitutionService.submit(request, apiType);

        } finally {
            VerifySignHolder.clear();
        }

    }

    private InstOrder prepareVerify(VerifySignRequest request) throws DuplicateRequestException {
        Assert.notNull(request.getInstOrderNo(), "机构订单号不可为空");
        InstOrder instOrder = instOrderRepository.loadByNo(request.getInstOrderNo());
        Assert.notNull(instOrder, "机构订单不存在");
        Assert.isTrue(instOrder.getFundChannelCode().equals(request.getChannelCode()), "通知渠道与订单记录不匹配");
        if (instOrder.getStatus() == InstOrderStatus.SUCCESSFUL || instOrder.getStatus() == InstOrderStatus.FAILURE) {
            throw new DuplicateRequestException(ErrorCode.WRONG_ORDER_DUPLICATE_PROCESS);
        }
        configurationService.queryCardToken(instOrder.getPayMode(), instOrder.getExtension());
        return instOrder;
    }

}
