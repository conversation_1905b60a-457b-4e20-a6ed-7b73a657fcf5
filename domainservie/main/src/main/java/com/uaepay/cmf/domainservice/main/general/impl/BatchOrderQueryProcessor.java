package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.PageResponse;
import com.uaepay.biz.common.util.PageList;
import com.uaepay.cmf.common.core.domain.exception.CmfBizException;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.domainservice.main.convert.BatchOrderConverter;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.service.facade.domain.query.BatchOrderQueryRequest;
import com.uaepay.cmf.service.facade.domain.query.order.BatchOrderVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BatchOrderQueryProcessor.java v1.0
 */
@Service
public class BatchOrderQueryProcessor extends GeneralProcessorTemplate<BatchOrderQueryRequest, PageResponse<BatchOrderVO>> {

    @Resource
    private InstOrderRepository instOrderRepository;

    @Override
    protected String getServiceName() {
        return "BatchOrderQueryProcessor";
    }

    @Override
    protected PageResponse<BatchOrderVO> createResponse() {
        return new PageResponse<>();
    }

    @Override
    protected void process(BatchOrderQueryRequest request, PageResponse<BatchOrderVO> response) throws CmfBizException {
        PageList pageList = instOrderRepository.queryBatchList(request);
        List<BatchOrderVO> batchOrderList = (List<BatchOrderVO>) pageList.stream().map(item -> BatchOrderConverter.convert((InstBatchOrder)item)).collect(Collectors.toList());
        response.setDataList(batchOrderList);
        response.setTotalSize(pageList.getPaginator().getItems());

    }
}
