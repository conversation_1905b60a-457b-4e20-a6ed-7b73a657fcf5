package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.enums.MonitorItem;
import com.uaepay.cmf.common.monitor.MonitorLog;
import com.uaepay.cmf.domainservice.main.convert.CmfResultConverter;
import com.uaepay.cmf.domainservice.main.process.MonitorService;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderResultRepository;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 *
 */
@Slf4j
@Service
public class DuplicateControlOrderResultProcessService {

    private static final Logger logger = LoggerFactory
            .getLogger(DuplicateControlOrderResultProcessService.class);

    @Resource
    private MonitorService monitorService;

    @Resource
    InstControlOrderResultRepository instControlOrderResultRepository;

    @Resource
    InstControlOrderRepository instControlOrderRepository;

    @Resource
    protected CmfOrderRepository cmfOrderRepository;

    /**
     * 重复订单结果查询.
     *
     * @return
     */
    public CmfControlResult queryDuplicateResult(String requestNo) {
        try {

            //查询控制订单
            InstControlOrder instControlOrder = instControlOrderRepository.loadByRequestNo(requestNo);
            if(Objects.isNull(instControlOrder)){
                return null;
            }
            //查询控制单结果记录
            InstControlOrderResult controlOrderResult = instControlOrderResultRepository.loadByInstOrderNo(instControlOrder.getInstOrderNo());
            if (Objects.isNull(controlOrderResult)) {
                return null;
            }
            //发送监控
            logMonitorDuplicateRequestWarning(controlOrderResult);
            //结果转化
            return CmfResultConverter.convert(instControlOrder,controlOrderResult);
        } catch (Exception e) {
            logger.error("[订单查询]处理异常", e);
            return null;
        }
    }

    /**
     * 组装重复请求消息，发送给监控.
     *
     * @param dbResult
     * @return
     */
    private void logMonitorDuplicateRequestWarning(InstControlOrderResult dbResult) {
        StringBuilder msg = new StringBuilder();
        msg.append("重复回调CMF:{instOrderNo:" + dbResult.getInstOrderNo());
        msg.append(",orderType:" + dbResult.getApiType().getMessage());
        msg.append(",amount:" + dbResult.getAmount().getAmount());
        msg.append(",instOrderNo:" + dbResult.getInstOrderNo());
        msg.append(",instOrderResult.status:" + dbResult.getStatus().getMessage());

        //重复请求，发送给监控.
        logger.warn("[重复请求处理]: controlOrderResult:{}" , dbResult);
        monitorService.logMonitorEvent(new MonitorLog(dbResult.getInstOrderNo(),
                MonitorItem.STATUS_NOT_CONSISTENT, msg.toString()));
    }
}
