package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.ManualRefundType;
import com.uaepay.cmf.common.core.domain.institution.InstRefundOrder;
import com.uaepay.cmf.common.domain.refund.RefundRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>人工退款参数构建器</p>
 *
 * <AUTHOR>
 * @date ManualRefundApiRequestBuilder.java v1.0  2020-09-09 16:56
 */
@Service
public class ManualRefundApiRequestBuilder extends RefundApiRequestBuilder {

    @Override
    protected void buildCustomParam(InstRefundOrder order, RefundRequest request) {
        // 构建退款的通用参数
        super.buildCustomParam(order, request);
        //手工退款需要指明转手工原因
        if (StringUtils.isNotEmpty(ManualRefundType.replaceMessage(order.getMemo()))) {
            request.getExtension().put(ExtensionKey.MEMO.key,
                    ManualRefundType.replaceMessage(order.getMemo()));
        } else {
            request.getExtension().put(ExtensionKey.MEMO.key,
                    ManualRefundType.NON_SUPPORT_AUTO_REFUND.getMessage());
        }
    }
}
