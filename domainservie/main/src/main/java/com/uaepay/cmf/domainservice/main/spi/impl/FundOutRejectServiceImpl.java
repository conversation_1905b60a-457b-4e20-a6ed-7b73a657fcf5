package com.uaepay.cmf.domainservice.main.spi.impl;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstFundoutOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.domainservice.main.domain.FundRejectReason;
import com.uaepay.cmf.domainservice.main.domain.H2hRejectAccountEnum;
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor;
import com.uaepay.cmf.domainservice.main.spi.FundOutRejectService;
import com.uaepay.cmf.fss.ext.integration.grc.GrcClient;
import com.uaepay.cmf.fss.ext.integration.ues.UesClient;
import com.uaepay.grc.connect.api.vo.domain.CheckInfo;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date FundOutRejectServiceImpl.java v1.0  2020-11-11 21:56
 */
@Slf4j
@Service
public class FundOutRejectServiceImpl implements FundOutRejectService {

    @Value("${h2h.rejectAccount.banned.channel:FAB211,FAB213}")
    private String rejectAccountNotAllowedChannels;


    @Resource
    private UesClient uesClient;

    @Resource
    private InstResultProcessor instResultProcessor;


    @Value("${biz.product.code.withdraw:230201}")
    private List<String> withdrawProductCodes;

    @Resource
    private GrcClient grcClient;


    @Override
    public H2hRejectAccountEnum getRejectAccountType(List<InstOrder> instOrderList) {
        if (instOrderList.size() > 1) {
            return null;
        }
        InstOrder instOrder = instOrderList.get(0);
        if (instOrder.getBizType() != BizType.FUNDOUT || !(instOrder instanceof InstFundoutOrder) || !rejectAccountNotAllowedChannels.contains(instOrder.getFundChannelCode())) {
            return null;
        }
        try {
            InstFundoutOrder fundoutOrder = (InstFundoutOrder) instOrder;

            String iban = uesClient.getDataByTicket(fundoutOrder.getIbanNo());
            return H2hRejectAccountEnum.getRejectAccountType(iban);
        } catch (Exception e) {
            log.warn("FundOutRejectService.getRejectAccountType.error:{}", e.getMessage());
            return null;
        }
    }

    @Override
    public InstOrderResult processRejectAccountRequest(List<InstOrder> instOrderList, H2hRejectAccountEnum accountType) {
        Assert.isTrue(instOrderList.size() == 1, "列表不为1");

        InstOrder instOrder = instOrderList.get(0);
        // 结果推进
        ChannelFundResult channelFundResult = buildRejectResult(instOrder, accountType);
        return instResultProcessor.process(instOrder, channelFundResult);
    }

    @Override
    public void batchRejectWithdrawOrder(List<InstOrder> instOrders, FundRejectReason reason) {
        if (CollectionUtils.isEmpty(instOrders)) {
            return;
        }
        //只拒绝提现订单
        for (InstOrder instOrder : instOrders) {
            if (isWithdrawOrder(instOrder)) {
                processRejectRequest(instOrder, reason);
            }
        }

    }

    @Override
    public InstOrderResult processRejectRequest(InstOrder instOrder, FundRejectReason reason) {
        // 结果推进
        ChannelFundResult channelFundResult = buildRejectResult(instOrder, reason);
        return instResultProcessor.process(instOrder, channelFundResult);
    }

    @Override
    public List<CheckInfo> grcCheck(List<InstOrder> instOrderList) {

        if (CollectionUtils.isEmpty(instOrderList)) {
            return new ArrayList<>();
        }

        List<CheckInfo> checkInfos = new ArrayList<>();

        for (InstOrder instOrder : instOrderList) {
            //会员id
            String memberId = instOrder.getExtension().get(ExtensionKey.MEMBER_ID.key);
            //支付订单号
            String paymentOrderNo = instOrder.getExtension().get(ExtensionKey.PAYMENT_ORDER_NO.key);

            //如果参数都为空不参与校验
            if (StringUtils.isAllBlank(memberId, paymentOrderNo) || !isWithdrawOrder(instOrder)) {
                continue;
            }

            CheckInfo checkInfo = new CheckInfo();
            checkInfo.setMemberId(memberId);
            checkInfo.setPaymentOrderNo(paymentOrderNo);
            checkInfos.add(checkInfo);
        }


        return grcClient.checkResult(checkInfos);
    }

    /**
     * 是否为提现订单
     *
     * @param instOrder 机构订单
     * @return 是否提现订单
     */
    public boolean isWithdrawOrder(InstOrder instOrder) {
        String bizProductCode = instOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.key);

        if (StringUtils.isBlank(bizProductCode)) {
            return false;
        }
        return withdrawProductCodes.contains(bizProductCode);
    }

    @Override
    public FundRejectReason getRejectReason(InstOrder instOrder) {
        return null;
    }

    private ChannelFundResult buildRejectResult(InstOrder instOrder, FundRejectReason rejectReason) {
        ChannelFundResult fundResult = new ChannelFundResult();
        fundResult.setRealAmount(instOrder.getAmount());
        fundResult.setApiType(instOrder.getApiType());
        fundResult.setFundChannelCode(instOrder.getFundChannelCode());
        fundResult.setInstOrderNo(instOrder.getInstOrderNo());
        fundResult.setApiResultCode("channel.validateFail");
        fundResult.setApiResultSubCode(rejectReason.getResultCode());
        fundResult.setApiResultMessage(rejectReason.getDescription());
        return fundResult;
    }

}
