package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderType;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.domainservice.main.process.AsyncVoidTransactionService;
import com.uaepay.cmf.service.facade.api.ControlRequestFacade;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfControlResult;
import com.uaepay.common.domain.Extension;
import com.uaepay.common.domain.OperationEnvironment;
import com.uaepay.schema.cmf.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.core.ExchangeBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

import static com.uaepay.cmf.common.core.domain.enums.ExtensionKey.*;

/**
 * <p>异步撤销交易</p>
 *
 * <AUTHOR>
 * @date 2022/6/28
 */
@Slf4j
@Service
public class AsyncVoidTransactionServiceImpl implements AsyncVoidTransactionService, BasicConstant {

    @Resource
    private ControlRequestFacade controlRequestFacade;
    @Autowired
    private AmqpAdmin amqpAdmin;
    @Autowired
    private AmqpTemplate amqpTemplate;

    @PostConstruct
    public void init() {
        amqpAdmin.declareExchange(ExchangeBuilder.directExchange(CMF_INNER_EXCHANGE).durable(true).build());
        log.info("已创建exchange: {}", CMF_INNER_EXCHANGE);
    }

    @Override
    public Result processControlVoidTx(InstControlOrder instControlOrder, InstControlOrderResult instControlResult) {

        if(!Objects.equals(InstOrderResultStatus.CANCEL,instControlResult.getStatus())){
            return Result.ofNothing();
        }

        CmfControlRequest request = buildCmfControlRequest(instControlOrder,instControlResult);
        try {
            log.info("AsyncVoidTransactionService-request:{}", request);
            CmfControlResult vtResult = controlRequestFacade.control(request, new OperationEnvironment());
            //String message = JsonUtil.toJsonString(request);
            //amqpTemplate.convertAndSend(CMF_INNER_EXCHANGE,QUEUE_VOID_TRANSACTION_ROUTING_KEY, message);
            log.info("AsyncVoidTransactionService-response-{}", vtResult);
            return Result.ofSuccess();
        } catch (Throwable e) {
            log.error("AsyncVoidTransactionService.message.error", e);
            return Result.ofFail();
        }

    }

    private CmfControlRequest buildCmfControlRequest(InstControlOrder order, InstControlOrderResult instControlResult) {
        CmfControlRequest request = new CmfControlRequest();
        request.setRequestNo(ControlRequestType.VOID_TRANSACTION.getCode() + order.getPreRequestNo());
        request.setPreRequestNo(order.getPreRequestNo());
        request.setRequestType(ControlRequestType.VOID_TRANSACTION);
        request.setPayMode(order.getPayMode());
        request.setInstCode(order.getInstCode());
        request.setAmount(order.getAmount());
        Extension extension = new Extension();
        extension.add(ExtensionKey.SOURCE_ORDER.key, SOURCE_ORDER_INST);
        request.setExtension(extension);
        return request;
    }

}
