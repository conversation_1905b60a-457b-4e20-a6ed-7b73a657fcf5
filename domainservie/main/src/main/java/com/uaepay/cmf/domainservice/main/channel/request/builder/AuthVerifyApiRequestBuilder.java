package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.alibaba.fastjson.JSON;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.domain.ChannelControlRequest;
import com.uaepay.cmf.common.enums.CallBackType;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.VerifySignHolder;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import static com.uaepay.cmf.common.enums.FundChannelApiType.*;

/**
 * <p>通用控制类请求组装器</p>
 *
 * <AUTHOR>
 * @date ControlApiRequestBuilder.java v1.0  2020-09-09 18:05
 */
@Service
public class AuthVerifyApiRequestBuilder extends AbstractApiRequestBuilder<InstControlOrder, ChannelControlRequest> {


    @Override
    protected void buildCustomParam(InstControlOrder order, ChannelControlRequest request) {
        request.setTargetInstCode(order.getInstCode());
        request.setInstOrderSubmitTime(order.getGmtCreate());
        VerifySignRequest vsRequest = VerifySignHolder.get();
        request.getExtension().put(CALLBACK_TYPE, vsRequest.getCallbackType());
        request.getExtension().put(VERIFY_PARAM, JSON.toJSONString(vsRequest.getVerifyParam()));
        request.getExtension().put(QUERY_STRING, vsRequest.getVerifyParamStr());
    }

    @Override
    public ChannelControlRequest buildReq() {
        return new ChannelControlRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(AUTH_VERIFY);
    }
}
