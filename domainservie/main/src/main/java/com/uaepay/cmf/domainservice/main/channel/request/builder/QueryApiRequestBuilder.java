package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.domain.query.QueryRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.uaepay.cmf.common.enums.FundChannelApiType.SINGLE_QUERY;

/**
 * <p>查询请求组装器</p>
 *
 * <AUTHOR>
 * @date QueryApiRequestBuilder.java v1.0  2020-09-09 18:16
 */
@Service
public class QueryApiRequestBuilder extends AbstractApiRequestBuilder<InstOrder, QueryRequest> {

    @Override
    protected void buildCustomParam(InstOrder order, QueryRequest request) {
        request.setQueryTime(new Date());
        request.setInstOrderSubmitTime(order.getGmtBookingSubmit());
        request.setOriginalInstSeqNo(order.getInstSeqNo());
    }

    @Override
    public QueryRequest buildReq() {
        return new QueryRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(SINGLE_QUERY);
    }
}
