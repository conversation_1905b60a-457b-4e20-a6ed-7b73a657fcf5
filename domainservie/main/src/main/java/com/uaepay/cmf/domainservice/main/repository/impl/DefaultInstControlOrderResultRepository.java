package com.uaepay.cmf.domainservice.main.repository.impl;

import com.uaepay.cmf.common.core.dal.daointerface.ControlOrderResultDAO;
import com.uaepay.cmf.common.core.dal.dataobject.ControlOrderResultDO;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator;
import com.uaepay.cmf.common.core.engine.generator.SequenceNameEnum;
import com.uaepay.cmf.domainservice.main.convert.InstControlOrderResultConverter;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderResultRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>机构控制订单结果仓储默认实现</p>
 *
 * <AUTHOR>
 * @version $Id: DefaultInstControlOrderResultRepository.java, v 0.1 2012-8-20 上午10:57:20 fuyangbiao Exp $
 */
@Repository
public class DefaultInstControlOrderResultRepository implements InstControlOrderResultRepository {
    @Resource
    private ControlOrderResultDAO controlOrderResultDAO;

    @Resource
    private PrimaryKeyGenerator primaryKeyGenerator;

    @Override
    public void storeOrUpdate(InstControlOrderResult result) {
        ControlOrderResultDO resultDO = InstControlOrderResultConverter.convert(result);
        resultDO.setResultId(Long.valueOf(primaryKeyGenerator.generateKey(SequenceNameEnum.CONTROL_RESULT)));
        controlOrderResultDAO.insert(resultDO);
        result.setResultId(resultDO.getResultId());
    }

    @Override
    public List<InstControlOrderResult> loadByOrderId(Long orderId) {
        List<ControlOrderResultDO> resultDOList = controlOrderResultDAO
                .loadWithControlOrderId(orderId);
        return InstControlOrderResultConverter.convertList(resultDOList);
    }

    @Override
    public InstControlOrderResult loadByInstOrderNo(String instOrderNo) {
        ControlOrderResultDO controlOrderResultDO = controlOrderResultDAO.loadWithInstOrderNo(instOrderNo);
        return InstControlOrderResultConverter.convert(controlOrderResultDO);
    }

}
