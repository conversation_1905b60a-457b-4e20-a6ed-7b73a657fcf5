package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.exception.CmfBizException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.convert.CmfRequestConverter;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.cmf.service.facade.domain.control.CmfFileRequest;
import com.uaepay.cmf.service.facade.domain.control.CmfFileResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date DownloadFileProcessor.java v1.0  1/7/21 7:23 PM
 */
@Service
public class DownloadFileProcessor extends GeneralProcessorTemplate<CmfFileRequest, CmfFileResponse> {

    @Resource
    protected SubmitInstitutionService submitInstitutionService;

    @Resource
    protected InstControlOrderRepository instControlOrderRepository;

    @Resource
    protected RouterClient routerClient;

    @Override
    protected String getServiceName() {
        return "DownloadFileProcessor";
    }

    @Override
    protected CmfFileResponse createResponse() {
        return new CmfFileResponse();
    }

    @Override
    protected void process(CmfFileRequest request, CmfFileResponse result) throws CmfBizException {
        InstControlOrder order = buildControlOrder(request);
        instControlOrderRepository.store(order);
        InstControlOrderResult controlResult = submitInstitutionService.submit(order, order.getApiType());
        result.setApplyStatus(controlResult.getStatus()== InstOrderResultStatus.SUCCESSFUL? ApplyStatusEnum.SUCCESS : ApplyStatusEnum.FAIL);
        result.setRequestNo(request.getRequestNo());
        if(controlResult.getExtension()!=null && controlResult.getExtension().containsKey(ExtensionKey.FILE_LIST.key)) {
            result.setFileList(Arrays.asList(controlResult.getExtension().get(ExtensionKey.FILE_LIST.key).split(CHAR_COMMA)));
        }
        result.setExtension(controlResult.getExtension());
    }

    private InstControlOrder buildControlOrder(CmfFileRequest request) {

        InstControlOrder order = CmfRequestConverter.convert(request, FundChannelApiType.DOWNLOAD_STATEMENT);
        order.setInstOrderNo(routerClient.genOrderNo(order.getFundChannelCode(), order.getApiType()));
        return order;
    }


}
