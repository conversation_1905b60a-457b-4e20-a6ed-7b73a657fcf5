package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.dal.dataobject.*;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.institution.*;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.common.core.engine.util.PropertyValueUtil;
import com.uaepay.cmf.common.enums.ApiParamScene;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.cmf.service.facade.domain.query.SimpleOrder;
import com.uaepay.common.util.DateUtil;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.router.service.facade.domain.channel.ChannelApiParamVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.router.service.facade.domain.order.OrderInfo;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.CardType;
import com.uaepay.schema.cmf.enums.CompanyOrPersonal;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.Map;
import java.util.Map.Entry;

/**
 * <p>
 * 机构订单转换器
 * </p>
 *
 * <AUTHOR>
 * @version $Id: InstOrderConverter.java, v 0.1 2012-8-6 下午1:27:51 fuyangbiao Exp $
 */
public class InstOrderConverter implements BasicConstant {

    /**
     * DOMAIN转换为DO
     *
     * @param instOrder
     * @return
     */
    public static InstOrderDO convert(InstOrder instOrder) {
        if (instOrder == null) {
            return null;
        }
        InstOrderDO instOrderDO = new InstOrderDO();
        BeanUtils.copyProperties(instOrder, instOrderDO);
        instOrderDO.setOrderType(instOrder.getBizType().getCode());
        instOrderDO.setPayMode(instOrder.getPayMode().getCode());
        instOrderDO.setCommunicateType(instOrder.getCommunicateType().getCode());
        instOrderDO.setCommunicateStatus(instOrder.getCommunicateStatus().getCode());
        instOrderDO.setStatus(instOrder.getStatus().getCode());
        if (instOrder.getRiskStatus() != null) {
            instOrderDO.setRiskStatus(instOrder.getRiskStatus().getCode());
        }
        if (instOrder.getIsSplit() != null) {
            instOrderDO.setIsSplit(instOrder.getIsSplit().getCode());
        }
        if (instOrder.getFlag() != null) {
            instOrderDO.setFlag(instOrder.getFlag().getCode());
        }
        if (instOrder.getExtension() != null) {
            instOrderDO.setExtension(CommonConverter.convertToDb(instOrder.getExtension()));
        }
        // 推进订单状态
        if (instOrder.getIsAdvance() != null) {
            instOrderDO.setIsAdvance(instOrder.getIsAdvance().getCode());
        }
        if (null != instOrder.getSendType()) {
            instOrderDO.setSendType(instOrder.getSendType().getCode());
        }
        return instOrderDO;
    }

    /**
     * 将入款DOMAIN转换为DO
     *
     * @param order
     * @return
     */
    public static FundinOrderDO convert(InstFundinOrder order) {
        if (order == null) {
            return null;
        }
        FundinOrderDO orderDO = new FundinOrderDO();
        BeanUtils.copyProperties(order, orderDO);
        if (order.getCardType() != null) {
            orderDO.setCardType(order.getCardType().getCode());
        }
        return orderDO;
    }

    /**
     * 入款DO转换为DOMAIN
     *
     * @param orderDO
     * @param fundInDO
     * @return
     */
    public static InstFundinOrder convert(InstOrderDO orderDO, FundinOrderDO fundInDO) {

        InstFundinOrder order = new InstFundinOrder();

        // 组装通用信息
        buildCommon(order, orderDO);
        if (fundInDO == null) {
            return order;
        }

        // 组装入款参数
        BeanUtils.copyProperties(fundInDO, order);
        order.setCardType(CardType.getByCode(fundInDO.getCardType()));

        return order;
    }

    /**
     * 出款DOMAIN转换为DO
     *
     * @param order
     * @return
     */
    public static FundoutOrderDO convert(InstFundoutOrder order) {
        FundoutOrderDO orderDO = new FundoutOrderDO();
        BeanUtils.copyProperties(order, orderDO);
        if (order.getCardType() != null) {
            orderDO.setCardType(order.getCardType().getCode());
        }
        if (order.getCompanyOrPersonal() != null) {
            orderDO.setAccountType(order.getCompanyOrPersonal().getCode());
        }

        return orderDO;
    }

    /**
     * 出款DO转换为DOMAIN
     *
     * @param orderDO
     * @param fundoutDO
     * @return
     */
    public static InstFundoutOrder convert(InstOrderDO orderDO, FundoutOrderDO fundoutDO) {
        InstFundoutOrder order = new InstFundoutOrder();

        // 组装通用信息
        buildCommon(order, orderDO);

        if (fundoutDO == null) {
            return order;
        }

        // 组装个性化信息
        BeanUtils.copyProperties(fundoutDO, order);
        order.setCardType(CardType.getByCode(fundoutDO.getCardType()));
        order.setCompanyOrPersonal(CompanyOrPersonal.getByCode(fundoutDO.getAccountType()));
        return order;
    }

    /**
     * 退款DOMAIN转换为DO
     *
     * @param order
     * @return
     */
    public static RefundOrderDO convert(InstRefundOrder order) {
        RefundOrderDO orderDO = new RefundOrderDO();

        orderDO.setInstOrderId(order.getInstOrderId());
        orderDO.setFundinOrderNo(order.getFundinOrderNo());
        orderDO.setFundinRealAmount(order.getFundinRealAmount());
        orderDO.setFundinDate(order.getFundinDate());
        orderDO.setFundinOrderNo(order.getFundinOrderNo());

        return orderDO;
    }

    /**
     * 退款DO转换为DOMAIN
     *
     * @param orderDO
     * @param refundDO
     * @return
     */
    public static InstRefundOrder convert(InstOrderDO orderDO, RefundOrderDO refundDO) {
        InstRefundOrder order = new InstRefundOrder();

        // 组装通用信息
        buildCommon(order, orderDO);
        if (refundDO == null) {
            return order;
        }

        BeanUtils.copyProperties(refundDO, order);

        order.setFundinInstSeqNo(order.getExtension().get(ExtensionKey.ORGI_INST_SEQ_NO.key));

        return order;
    }

    /**
     * 根据CMF订单组装机构订单
     *
     * @param cmfOrder
     * @param preOrder  原机构订单
     * @param channel   渠道
     * @param orderInfo 订单信息
     * @return
     */
    public static InstOrder convert(CmfOrder cmfOrder, InstBaseOrder preOrder, InstBaseResult preResult,
                                    ChannelVO channel, OrderInfo orderInfo) {
        InstOrder instOrder = null;

        // 组装个性化信息
        switch (cmfOrder.getBizType()) {
            case FUNDIN:
                instOrder = new InstFundinOrder();
                for (InstOrderExtensionMapping mapping : InstOrderExtensionMapping
                        .getMappingSet(InstFundinOrder.class)) {
                    PropertyValueUtil.setValue(instOrder, mapping,
                            cmfOrder.getExtension().get(mapping.getExtensionKey()));
                }

                break;
            case FUNDOUT:
                InstFundoutOrder instFundoutOrder = new InstFundoutOrder();
                instFundoutOrder.setPtId(cmfOrder.getMemberId());

                for (InstOrderExtensionMapping mapping : InstOrderExtensionMapping
                        .getMappingSet(InstFundoutOrder.class)) {
                    PropertyValueUtil.setValue(instFundoutOrder, mapping,
                            cmfOrder.getExtension().get(mapping.getExtensionKey()));
                }
                if (preOrder != null) {
                    instFundoutOrder.getExtension().put(ExtensionKey.PRE_INST_ORDER_NO.key, preOrder.getInstOrderNo());
                }
                String payoutAccount = ChannelUtil.getExtVal(channel.getExtList(), ChannelInfoExtKey.PAYOUT_ACCOUNT);
                instFundoutOrder.setPayoutAccount(payoutAccount);
                instOrder = instFundoutOrder;

                break;
            case REFUND:
                InstRefundOrder instRefundOrder = new InstRefundOrder();
                instRefundOrder.setFundinOrderNo(preOrder.getInstOrderNo());
                instRefundOrder.setFundinRealAmount(preOrder.getAmount());
                instRefundOrder.setFundinDate(DateUtil.getLongDateString(preOrder.getGmtCreate()));
                if (preResult != null) {
                    instRefundOrder.setFundinInstSeqNo(((InstOrderResult) preResult).getInstSeqNo());
                    instRefundOrder.getExtension().put(ExtensionKey.ORGI_INST_SEQ_NO.key,
                            instRefundOrder.getFundinInstSeqNo());
                }

                instRefundOrder.getExtension().put(ExtensionKey.IS_CANCEL.key,
                        (equalAmountAndSameDay(preOrder, cmfOrder) ? YesNo.YES : YesNo.NO).getCode());
                instRefundOrder.getExtension().put(ExtensionKey.CARD_BRAND.key, preOrder.getExtension().get(ExtensionKey.CARD_BRAND.key));
                if(StringUtils.isNotBlank(preOrder.getExtension().get(ExtensionKey.MERCHANT_CONFIG_VERSION.key))){
                    instRefundOrder.getExtension().put(ExtensionKey.MERCHANT_CONFIG_VERSION.key, preOrder.getExtension().get(ExtensionKey.MERCHANT_CONFIG_VERSION.key));
                }
                if (StringUtils.isNotBlank(preOrder.getExtension().get(ExtensionKey.TRANSFORM_ACCOUNT_ACCESS.getKey()))){
                    instRefundOrder.getExtension().put(ExtensionKey.TRANSFORM_ACCOUNT_ACCESS.getKey(), preOrder.getExtension().get(ExtensionKey.TRANSFORM_ACCOUNT_ACCESS.getKey()));
                }
                instOrder = instRefundOrder;

                break;
            default:
                throw new RuntimeException("此业务类型不支持");
        }

        // 组装主信息
        buildCommon(instOrder, cmfOrder, channel, orderInfo);

        // 组装原订单参数列表
        buildOriginParam(preOrder, channel, instOrder);

        return instOrder;
    }

    private static boolean equalAmountAndSameDay(InstBaseOrder preOrder, CmfOrder cmfOrder) {
        return preOrder != null && cmfOrder != null && preOrder.getAmount().equals(cmfOrder.getAmount()) && DateUtil.isSameDay(preOrder.getGmtCreate(), cmfOrder.getGmtCreate());
    }

    /**
     * 根据DO组装机构订单通用信息
     *
     * @param instOrder
     * @param orderDO
     */
    public static void buildCommon(InstOrder instOrder, InstOrderDO orderDO) {
        BeanUtils.copyProperties(orderDO, instOrder);
        instOrder.setBizType(BizType.getByCode(orderDO.getOrderType()));
        instOrder.setPayMode(PayMode.getByCode(orderDO.getPayMode()));
        if (orderDO.getFundChannelApi() != null) {
            //设置接口类型
            String[] apiArray = orderDO.getFundChannelApi().split(SPLIT_TAG);
            if (apiArray.length == 2) {
                instOrder.setApiType(FundChannelApiType.getByCode(apiArray[1]));
            }
        }
        instOrder.setCommunicateType(InstOrderCommunicateType.getByCode(orderDO.getCommunicateType()));
        instOrder.setCommunicateStatus(CommunicateStatus.getByCode(orderDO.getCommunicateStatus()));
        instOrder.setStatus(InstOrderStatus.getByCode(orderDO.getStatus()));
        instOrder.setRiskStatus(OrderRiskStatus.getByCode(orderDO.getRiskStatus()));
        instOrder.setIsSplit(YesNo.getByCode(orderDO.getIsSplit()));
        instOrder.setFlag(OrderFlag.getByCode(orderDO.getFlag()));
        instOrder.setSendType(InstOrderSendType.getByCode(orderDO.getSendType()));
        // 订单是否推进
        instOrder.setIsAdvance(IsAdvance.getByCode(orderDO.getIsAdvance()));
        instOrder.setInstOrderType(InstOrderType.FUND);
        if (StringUtils.isNotEmpty(orderDO.getExtension())) {
            instOrder.setExtension(CommonConverter.convertFromDb(orderDO.getExtension()));
        }
    }

    /**
     * 组装原订单参数列表
     *
     * @param preOrder
     * @param channel
     * @param instOrder
     */
    private static void buildOriginParam(InstBaseOrder preOrder, ChannelVO channel, InstOrder instOrder) {
        if (preOrder != null && !CollectionUtils.isEmpty(preOrder.getExtension())) {
            for (ChannelApiParamVO apiParam : channel.getChannelApi().getParamList()) {
                String paramVal = preOrder.getExtension().get(apiParam.getParamName());
                if (ApiParamScene.REQUEST_CHANNEL.getCode().equals(apiParam.getScene())
                        && StringUtils.isNotEmpty(paramVal)) {
                    instOrder.getExtension().put(apiParam.getParamName(), paramVal);
                }
            }
        }
    }

    /**
     * 根据cmf订单组装机构订单通用信息
     *
     * @param instOrder
     * @param cmfOrder
     * @param channel
     */
    private static void buildCommon(InstOrder instOrder, CmfOrder cmfOrder, ChannelVO channel, OrderInfo orderInfo) {
        instOrder.setFundChannelCode(channel.getChannelCode());
        instOrder.setCommunicateType(FundChannelApiType.isBatch(channel.getChannelApi().getApiType())
                ? InstOrderCommunicateType.BATCH : InstOrderCommunicateType.SINGLE);
        instOrder.setFundChannelApi(channel.getChannelApi().getApiCode());
        instOrder.setApiType(FundChannelApiType.getByCode(channel.getChannelApi().getApiType()));

        instOrder.setPayMode(cmfOrder.getPayMode());
        instOrder.setInstCode(cmfOrder.getInstCode());
        instOrder.setCommunicateStatus(CommunicateStatus.AWAITING);
        instOrder.setArchiveStatus(InstOrderArchiveStatus.AWAITING);
        instOrder.setStatus(InstOrderStatus.IN_PROCESS);
        instOrder.setBizType(cmfOrder.getBizType());
        if (orderInfo != null) {
            instOrder.setInstOrderNo(orderInfo.getOrderNo());
        }
        instOrder.setSendType(isValidOrderInfo(orderInfo) ? InstOrderSendType.getByCode(orderInfo.getSendType()) : InstOrderSendType.SYNCHRONIZED);
        instOrder.setGmtBookingSubmit(isValidOrderInfo(orderInfo) ? orderInfo.getGmtBooking() : new Date());
        instOrder.setGmtCreate(new Date());

        instOrder.setProductCode(cmfOrder.getProductCode());
        instOrder.setPaymentCode(cmfOrder.getPaymentCode());
        instOrder.setAmount(cmfOrder.getAmount());
        instOrder.setFlag(OrderFlag.DEFAULT);
        instOrder.setMerchantId(cmfOrder.getMerchantId());
        instOrder.setCmfSeqNo(cmfOrder.getOrderSeqNo());
        instOrder.setMemo(cmfOrder.getMemo());
        instOrder.setGmtNextRetry(new Date());
        instOrder.setRetryTimes(DEFAULT_RETRY_TIMES);

        if (StringUtils.isNotEmpty(cmfOrder.getMemberId())
                && !cmfOrder.getExtension().containsKey(ExtensionKey.MEMBER_ID.key)) {
            instOrder.getExtension().put(ExtensionKey.MEMBER_ID.key, cmfOrder.getMemberId());
        }

        // 拷贝扩展信息
        if (!CollectionUtils.isEmpty(cmfOrder.getExtension())) {
            instOrder.setIsAdvance(IsAdvance.NO);
            for (Entry<String, String> entry : cmfOrder.getExtension().entrySet()) {
                // 值不为空且不作为属性的扩展信息复制
                if (StringUtils.isNotBlank(entry.getValue())
                        && (InstOrderExtensionMapping.PURPOSE.getExtensionKey().equals(entry.getKey())
                        || !InstOrderExtensionMapping.getKeySet(instOrder.getClass()).contains(entry.getKey()))) {
                    instOrder.getExtension().put(entry.getKey(), entry.getValue());
                }

                if (StringUtils.isNotBlank(channel.getParamExt().get(ExtensionKey.MERCHANT_ONBOARDING_ID.key))) {
                    instOrder.getExtension().put(ExtensionKey.MERCHANT_ONBOARDING_ID.key, channel.getParamExt().get(ExtensionKey.MERCHANT_ONBOARDING_ID.key));
                }
            }
        }

    }

    public static SimpleOrder transfer(InstOrder instOrder) {
        SimpleOrder simpleOrder = new SimpleOrder();
        BeanUtils.copyProperties(instOrder, simpleOrder);
        simpleOrder.setGmtSubmit(instOrder.getGmtBookingSubmit());
        simpleOrder.setBizType(instOrder.getBizType().getCode());
        simpleOrder.setStatus(instOrder.getStatus().getCode());

        simpleOrder.setMemberId(instOrder.getExtension().get(ExtensionKey.MEMBER_ID.getKey()));
        simpleOrder.setProductOrderNo(instOrder.getExtension().get(ExtensionKey.PRODUCT_ORDER_NO.key));
        simpleOrder.setPaymentOrderNo(instOrder.getExtension().get(ExtensionKey.PAYMENT_ORDER_NO.key));
        simpleOrder.setBizProductCode(instOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.key));

        return simpleOrder;
    }

    private static boolean isValidOrderInfo(OrderInfo orderInfo) {
        return orderInfo != null && orderInfo.getSendType() != null && orderInfo.getGmtBooking() != null;
    }

    public static UniqueOrderDO convertUnique(InstOrder instOrder) {
        UniqueOrderDO uniqueOrder = new UniqueOrderDO();
        BeanUtils.copyProperties(instOrder, uniqueOrder);
        return uniqueOrder;
    }

}
