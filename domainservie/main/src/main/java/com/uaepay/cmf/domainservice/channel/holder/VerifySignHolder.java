package com.uaepay.cmf.domainservice.channel.holder;

import com.uaepay.cmf.common.core.domain.vo.VerifyResponseContent;
import com.uaepay.cmf.common.domain.fundin.ebank.EBankChannelVerifyResult;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignResult;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date VerifySignHolder.java v1.0  2020-09-13 23:00
 */
public class VerifySignHolder {

    public static final ThreadLocal<VerifySignRequest> threadLocal = new ThreadLocal<>();
    public static final ThreadLocal<VerifyResponseContent> resultThreadLocal = new ThreadLocal<>();


    /**
     * 获取，如果线程中没有，则返回null
     *
     * @return
     */
    public static VerifySignRequest get() {
        return threadLocal.get();
    }

    /**
     * 设置渠道
     *
     * @param request
     */
    public static void set(VerifySignRequest request) {
        threadLocal.set(request);
    }

    /**
     * 结果
     *
     * @return
     */
    public static VerifyResponseContent getResult() {
        return resultThreadLocal.get();
    }

    /**
     *
     * @param content
     */
    public static void set(VerifyResponseContent content) {
        resultThreadLocal.set(content);
    }


    /**
     * 清理
     */
    public static void clear() {
        threadLocal.remove();
        resultThreadLocal.remove();
    }


}
