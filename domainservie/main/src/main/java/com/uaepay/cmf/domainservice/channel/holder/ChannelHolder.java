package com.uaepay.cmf.domainservice.channel.holder;

import com.uaepay.router.service.facade.domain.channel.ChannelVO;

/**
 * <p>获取渠道对象</p>
 *
 * <AUTHOR>
 * @date ChannelHolder.java v1.0  2020-09-08 14:48
 */
public class ChannelHolder {

    public static final ThreadLocal<ChannelVO> threadLocal = new ThreadLocal<>();


    /**
     * 获取，如果线程中没有，则返回null
     * @return
     */
    public static ChannelVO get() {
        return threadLocal.get();
    }

    /**
     * 设置渠道
     * @param channel
     */
    public static void set(ChannelVO channel) {
        threadLocal.set(channel);
    }

    /**
     * 清理
     */
    public static void clear() {
        threadLocal.remove();
    }

}
