package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.domain.file.ChannelFileResult;
import com.uaepay.cmf.fss.ext.common.domain.authenticate.AuthenticateResponse;
import com.uaepay.common.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <p>控制类结果转换</p>
 *
 * <AUTHOR>
 * @version $Id: ChannelControlResultConverter.java, v 0.1 2012-8-20 下午6:29:55 liumaoli Exp $
 */
public class ChannelControlResultConverter implements BasicConstant {

    /**
     * 控制类结果转换
     *
     * @param controlOrder
     * @param channelResult
     * @return
     */
    public static InstControlOrderResult convert(InstControlOrder controlOrder, ChannelResult channelResult) {
        InstControlOrderResult result = new InstControlOrderResult();
        result.setApiType(channelResult.getApiType());
        result.setApiResultCode(channelResult.getApiResultCode());
        result.setApiResultSubCode(channelResult.getApiResultSubCode());
        result.setInstOrderNo(controlOrder.getInstOrderNo());
        result.setResultMessage((StringUtils.isEmpty(channelResult.getApiResultMessage()) ? ""
                : channelResult.getApiResultMessage())
                + (StringUtils.isEmpty(channelResult.getApiResultSubMessage()) ? ""
                : channelResult.getApiResultSubMessage()));
        result.setOrderId(controlOrder.getOrderId());
        result.setFundChannelCode(controlOrder.getFundChannelCode());

        Map<String, String> extMap = MapUtil.jsonToMap(channelResult.getExtension());
        result.setExtension(extMap);
        result.getExtension().put(ExtensionKey.INST_SEQ_NO.key,
                channelResult.getInstReturnOrderNo());
        if (channelResult instanceof ChannelFundResult) {
            result.setAmount(((ChannelFundResult) channelResult).getRealAmount());
        }
        if (channelResult instanceof AuthenticateResponse) {
            completeSignResult(result, (AuthenticateResponse) channelResult);
        }
        if (channelResult instanceof ChannelFileResult) {
            completeFileResult(result, (ChannelFileResult) channelResult);
        }
        return result;
    }

    private static void completeFileResult(InstControlOrderResult result, ChannelFileResult fileResult) {
        if (CollectionUtils.isNotEmpty(fileResult.getFileList())) {
            result.getExtension().put(ExtensionKey.FILE_LIST.key, StringUtils.join(fileResult.getFileList(), CHAR_COMMA));
        }
    }

    private static void completeSignResult(InstControlOrderResult result,
                                           AuthenticateResponse response) {
        result.getExtension().put(ExtensionKey.SIGN_NO.key, response.getSignNo());
        if (response.getSignDate() != null) {
            result.getExtension().put(ExtensionKey.SIGN_DATE.key,
                    DateUtil.getLongDateString(response.getSignDate()));
        }
    }
}
