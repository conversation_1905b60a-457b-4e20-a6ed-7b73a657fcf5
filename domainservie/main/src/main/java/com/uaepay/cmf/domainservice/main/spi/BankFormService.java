package com.uaepay.cmf.domainservice.main.spi;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelResult;

/**
 * <p>银行表单服务.</p>
 *
 * <AUTHOR>
 * @version BankFormService.java 1.0 Created@2017-02-07 11:03 $
 */
public interface BankFormService {

    void processControlResult(InstControlOrder instControlOrder, ChannelResult result);

    void processInstResult(InstOrder instOrder, ChannelFundResult result);
}
