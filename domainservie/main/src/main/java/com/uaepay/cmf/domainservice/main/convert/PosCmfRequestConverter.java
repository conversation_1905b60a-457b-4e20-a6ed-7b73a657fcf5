package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.service.facade.domain.control.CmfControlRequest;

/**
 * <p>PosCmfRequestConverter</p>
 *
 * <AUTHOR>
 * @version PosCmfRequestConverter.java v1.0  2022/10/10 09:47
 */
public class PosCmfRequestConverter {


    /**
     * 转换pos控制订单
     *
     * @param controlRequest 控制请求
     * @return 控制订单
     */
    public static InstControlOrder convert(CmfControlRequest controlRequest) {
        InstControlOrder instControlOrder = CmfRequestConverter.convert(controlRequest);
        ControlRequestType requestType = controlRequest.getRequestType();

        //预授权更新和完成类型需要更新原订单
        if (ControlRequestType.PREAUTH_COMPLETE == requestType
                || ControlRequestType.PREAUTH_UPDATE == requestType) {
            instControlOrder.setUpdatePreOrder(true);
        }
        return instControlOrder;
    }
}
