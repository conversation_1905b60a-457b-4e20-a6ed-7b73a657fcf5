package com.uaepay.cmf.domainservice.main.spi.impl;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.ReSubmitInstitutionService;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>重路由订单服务抽象类</p>
 *
 * <AUTHOR>
 * @version $Id: AbstractReSubmitInstitutionService.java, v 0.1 2014-7-9 下午6:39:46 fuyangbiao Exp $
 */
public abstract class AbstractReSubmitInstitutionService implements ReSubmitInstitutionService,
        BasicConstant {
    protected static final Logger logger = LoggerFactory
            .getLogger(AbstractReSubmitInstitutionService.class);

    /**
     * CMF订单仓储
     */
    @Resource
    protected CmfOrderRepository cmfOrderRepository;
    /**
     * 机构订单仓储
     */
    @Resource
    protected InstOrderRepository instOrderRepository;

    /**
     * 提交cmf订单处理器
     */
    @Resource
    protected SubmitInstitutionService submitInstitutionService;

    /**
     * 重新提交订单
     *
     * @param orders
     * @return
     */
    protected BaseResult reSubmit(Map<CmfOrder, List<InstOrder>> orders) {
        Boolean success = true;
        StringBuilder message = new StringBuilder();

        Set<Map.Entry<CmfOrder, List<InstOrder>>> set = orders.entrySet();
        for (Iterator<Map.Entry<CmfOrder, List<InstOrder>>> it = set.iterator(); it.hasNext(); ) {
            Map.Entry<CmfOrder, List<InstOrder>> entry = it.next();
            CmfOrder cmfOrder = entry.getKey();
            List<InstOrder> instOrders = entry.getValue();
            try {
                for (InstOrder instOrder : instOrders) {
                    cancelOrder(instOrder);
                }
                submitInstitutionService.submit(cmfOrder);
                logger.info("cmfSeqNo{}重路由成功", cmfOrder.getOrderSeqNo());
                message.append("cmfSeqNo[" + cmfOrder.getOrderSeqNo() + "]重路由成功;");
            } catch (Exception e) {
                logger.error("cmfSeqNo[" + cmfOrder.getOrderSeqNo() + "]重路由失败", e);
                message.append("cmfSeqNo[" + cmfOrder.getOrderSeqNo() + "]重路由失败;");
                success = false;
            }
        }
        return new BaseResult(success, message.toString());
    }

    /**
     * 校验订单
     *
     * @param cmfOrder
     * @param orderList
     */
    protected Set<Long> validate(CmfOrder cmfOrder, List<InstOrder> orderList) {
        Assert.isTrue(cmfOrder.getBizType().equals(BizType.FUNDOUT),
                "cmfSeqNo[" + cmfOrder.getOrderSeqNo() + "]不为出款订单,无法进行重路由操作");

        Assert.isTrue(cmfOrder.getStatus().equals(CmfOrderStatus.IN_PROCESS),
                "cmfSeqNo[" + cmfOrder.getOrderSeqNo() + "]cmf状态不为处理中");

        Set<Long> batchIdSet = new HashSet<>();
        for (InstOrder instOrder : orderList) {
            Assert.isTrue(instOrder.getStatus().equals(InstOrderStatus.IN_PROCESS),
                    "cmfSeqNo[" + cmfOrder.getOrderSeqNo() + "]机构订单状态不为处理中");

            Assert.isTrue(instOrder.getCommunicateStatus() == CommunicateStatus.AWAITING,
                    "cmfSeqNo[" + cmfOrder.getOrderSeqNo() + "]机构订单发送状态不为待发送");

            //已经打批的校验批次状态
            batchIdSet.add(instOrder.getArchiveBatchId());
        }

        return batchIdSet;
    }

    /**
     * 取消订单
     * 1. 记录日志
     * 2. 若属于批次,移除批次
     * 3. 删除订单信息
     *
     * @param instOrder
     */
    private void cancelOrder(InstOrder instOrder) {

        instOrderRepository.delete(instOrder);

    }

    /**
     * 过滤掉相同的string
     *
     * @param strs
     */
    protected List<String> filter(List<String> strs) {
        List<String> result = new ArrayList<>();
        for (String str : strs) {
            if (StringUtils.isNotEmpty(str) && !result.contains(str.trim())) {
                result.add(str.trim());
            }
        }
        if (result.size() != strs.size()) {
            logger.warn("重路由过滤前[" + strs.size() + "],过滤后[" + result.size() + "]");
        }
        return result;
    }
}
