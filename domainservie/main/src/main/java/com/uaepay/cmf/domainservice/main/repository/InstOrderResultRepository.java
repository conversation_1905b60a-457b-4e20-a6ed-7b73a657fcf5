package com.uaepay.cmf.domainservice.main.repository;

import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstResultOperateStatus;
import com.uaepay.cmf.common.core.domain.enums.RiskFlag;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;

import java.util.List;

/**
 * <p>机构订单结果仓储</p>
 *
 * <AUTHOR>
 * @version $Id: InstOrderResultRepository.java, v 0.1 2012-8-2 下午2:46:34 fuyangbiao Exp $
 */
public interface InstOrderResultRepository {

    /**
     * 根据ID加载领域对象
     *
     * @param resultId
     * @param isLock
     * @return
     */
    InstOrderResult load(Long resultId, boolean isLock);

    /**
     * 根据机构订单ID加载领域对象列表，不包括 instStatus='Q'的记录
     * 如果存在多条,则按以下顺序返回
     * 1.优先返回实际影响机构订单结果的一条
     * 2.返回有明确结果的
     * 3.随机返回一条
     *
     * @param orderId
     * @return
     */
    InstOrderResult loadRealResultByOrder(Long orderId);

    Long storeOrUpdate(InstOrderResult result);

    /**
     * 保存订单结果
     *
     * @param result
     * @return id
     */
    Long store(InstOrderResult result);

    /**
     * 人工生成结果订单
     *
     * @param instOrder
     * @param memo
     * @param instOrderResultStatus
     * @return
     */
    InstOrderResult convertInstOrder(InstOrder instOrder, String memo, InstOrderResultStatus instOrderResultStatus);

    /**
     * @param to
     * @param ids
     * @param from
     */
    void updateOperateStatusByResultIds(InstResultOperateStatus to, List<Long> ids, InstResultOperateStatus from);

    /**
     * 更新风控标识
     *
     * @param riskFlag
     * @param resultId
     * @return
     */
    int updateRiskFlagById(RiskFlag riskFlag, Long resultId);

    /**
     * 更新操作状态
     *
     * @param operateStatus
     * @param resultId
     * @return
     */
    int updateOperateStatusById(InstResultOperateStatus operateStatus, Long resultId);

    /**
     * 得到最后一次生效的结果
     *
     * @param instOrderId
     * @return
     */
    InstOrderResult getLastResult(Long instOrderId);

    List<InstOrderResult> getAllResult(Long instOrderId);

}
