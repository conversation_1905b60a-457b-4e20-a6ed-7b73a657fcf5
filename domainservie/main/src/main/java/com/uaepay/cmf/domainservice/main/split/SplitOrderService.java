package com.uaepay.cmf.domainservice.main.split;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;

import java.util.List;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version SplitOrderService.java 1.0 Created@2018-03-21 11:14 $
 */
public interface SplitOrderService extends BasicConstant {


    List<InstOrder> orderComplement(CmfOrder cmfOrder, InstOrder instOrder,
                                    InstBaseOrder preOrder, ChannelVO fundChannel);
}
