package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.domain.ChannelControlRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import static com.uaepay.cmf.common.enums.FundChannelApiType.MERCHANT_REGISTER_QUERY;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version: MerchantRegisterQueryApiRequestBuilder.class v1.0
 */
@Service
public class MerchantRegisterQueryApiRequestBuilder  extends AbstractApiRequestBuilder<InstControlOrder, ChannelControlRequest> {

    @Override
    protected void buildCustomParam(InstControlOrder order, ChannelControlRequest request) {
        request.setTargetInstCode(order.getInstCode());
        request.setInstOrderSubmitTime(order.getGmtCreate());
    }

    @Override
    public ChannelControlRequest buildReq() {
        return new ChannelControlRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(MERCHANT_REGISTER_QUERY);
    }
}