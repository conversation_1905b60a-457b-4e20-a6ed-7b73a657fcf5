package com.uaepay.cmf.domainservice.main.validate;

import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.exception.WrongOrderResultException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>渠道返回结果校验</p>
 *
 * <AUTHOR>
 * @version $Id: ChannelResultValidator.java, v 0.1 2012-8-9 下午7:34:57 liumaoli Exp $
 */
@Slf4j
@Service
public class ChannelResultValidator {

    /**
     * 渠道返回结果金额校验
     *
     * @param order
     * @param instResult
     * @return
     */
    public void validateAmount(InstOrder order, InstOrderResult instResult) {
        if (order == null || instResult == null) {
            return;
        }
        if (!order.getAmount().equals(instResult.getRealAmount())) {
            log.warn("机构订单{},金额校验不通过,订单金额-{},结果金额-{}", order.getInstOrderNo(), order.getAmount(), instResult.getRealAmount());
            throw new WrongOrderResultException(ErrorCode.UNMATCHED_ORDER_AMOUNT_EXCEPTION);
        }
    }
}
