package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.schema.cmf.enums.BizType;

/**
 * 
 * <p>
 * 处理结果，包括实时调用的结果，包括实时返回，或者异步回调
 * </p>
 * 
 * 1. 核对返回的结果，金额是否匹配，账户信息是否一致等 2. 调用状态模块更新状态
 * 
 * <AUTHOR> won
 * @version $Id: ResultProcess.java, v 0.1 2010-12-30 上午11:29:39 sean won Exp $
 */
public interface DuplicateResultProcessService {

    void validateNotProcessed(Long instOrderId);

    /**
     * 检查机构订单：通讯状态是否已返回.
     * 
     * @param instOrder
     */
    boolean checkNotProcessed(InstOrder instOrder);

    /**
     * 查询重复结果
     * @param paymentSeqNo
     * @param settlementId
     * @param bizType
     * @return
     */
    CmfFundResult queryDuplicateResult(String paymentSeqNo, String settlementId,
                                       BizType bizType);

    /**
     * 重复请求处理.
     * 
     * @param dbInstOrder
     * @param comingResult
     */
    boolean duplicateRequestProcess(InstOrder dbInstOrder, InstOrderResult comingResult);
}
