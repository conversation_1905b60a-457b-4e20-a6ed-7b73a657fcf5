package com.uaepay.cmf.domainservice.main.sender;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.core.domain.exception.WrongOrderResultException;
import com.uaepay.cmf.common.core.domain.institution.InstCommonOrder;
import com.uaepay.cmf.common.domain.ChannelFundRequest;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelRequest;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.domainservice.main.factory.ApiRequestBuilderFactory;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.retry.RetryTimeManage;
import com.uaepay.cmf.domainservice.main.spi.BankFormService;
import com.uaepay.cmf.fss.ext.common.api.ChannelFundFacade;
import com.uaepay.cmf.fss.ext.integration.factory.ChannelSenderFactory;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date AbstractChannelSender.java v1.0  2020-09-09 15:49
 */
@Slf4j
public abstract class AbstractChannelSendService<Order extends InstCommonOrder, Req extends ChannelRequest, Res extends ChannelResult> implements BasicConstant {

    @Resource
    protected InstOrderRepository instOrderRepository;

    @Resource
    protected InstControlOrderRepository instControlOrderRepository;

    @Resource
    protected ApiRequestBuilderFactory apiRequestBuilderFactory;

    @Resource
    protected ChannelSenderFactory channelSenderFactory;

    @Resource
    protected TransactionTemplate cmfTransactionTemplate;

    @Resource
    protected BankFormService bankFormService;

    @Resource(name = "ebankChannelFundAgent")
    protected ChannelFundFacade ebankChannelFundAgent;

    /**
     * 代理模式匹配对象
     */
    private Map<String, ChannelFundFacade> proxyModeMap = new HashMap<>(10);


    /**
     * 订单发送方法
     * 1）订单状态准备
     * 2）订单转换为请求并发送
     * 3）获取响应结果
     *
     * @param order
     * @return
     */
    @RetryTimeManage
    public Res send(Order order) {
        //1. 乐观锁 - 更新发送状态
        updateOrderCommunicateStatus2Send(order);

        //2.组装请求参数
        Req request = (Req) apiRequestBuilderFactory.get(getApiType()).build(order);

        Res resp;

        //3.发送银行
        try {
            resp = send2Bank(request, order);
        } catch (CommunicateException e) {
            log.error("AbstractChannelSendService.send.error", e);
            resp = buildFailSendResp(request);
        }

        return resp;
    }

    protected FundChannelApiType getApiType() {
        ChannelVO channel = ChannelHolder.get();
        Assert.notNull(channel, "渠道不可为空");
        Assert.notNull(channel.getChannelApi(), "渠道Api不可为空");
        return FundChannelApiType.getByCode(channel.getChannelApi().getApiType());
    }

    /**
     * 状态更新
     *
     * @param order
     */
    protected abstract void updateOrderCommunicateStatus2Send(Order order);

    /**
     * 发送银行
     *
     * @param request
     * @return
     */
    protected abstract Res send2Bank(Req request, Order order);

    /**
     * 组装返回结果
     *
     * @param req
     * @return
     */
    protected Res buildFailSendResp(Req req) {
        Res result = buildResp();
        if (req instanceof ChannelFundRequest && result instanceof ChannelFundResult) {
            ((ChannelFundResult) result).setRealAmount(((ChannelFundRequest) req).getAmount());
        }
        result.setApiType(req.getApiType());
        result.setInstOrderNo(req.getInstOrderNo());
        result.setApiResultCode(INNER_ERROR_CODE);
        result.setApiResultSubCode(ERROR_COMM_CODE);
        result.setApiResultMessage(ERROR_COMM);
        return result;
    }

    protected abstract Res buildResp();


    void assertStatusIsUpdated(int cnt) {
        if (cnt == 0) {
            throw new WrongOrderResultException(ErrorCode.WRONG_ORDER_STATUS_EXCEPTION);
        }
    }

    /**
     * 获取代理类
     *
     * @return
     */
    protected ChannelFundFacade getSpecialAgent(PayMode payMode, FundChannelApiType apiType) {
        ChannelFundFacade agent = proxyModeMap.get(payMode.getCode() + "_" + apiType.getCode());
        if (agent == null) {
            agent = proxyModeMap.get(payMode.getCode());
        }
        return agent;
    }


    /**
     * 判断是否走代理
     *
     * @param payMode 支付模式
     * @param apiType API类型
     * @return
     */
    protected boolean isRouteAgent(PayMode payMode, FundChannelApiType apiType) {
        return !(payMode == null || apiType == null)
                && (proxyModeMap.containsKey(payMode.getCode() + "_" + apiType.getCode())
                || proxyModeMap.containsKey(payMode.getCode()));
    }


    @PostConstruct
    private synchronized void init() {
        proxyModeMap.put("ONLINE_BANK_SG", ebankChannelFundAgent);
    }

}
