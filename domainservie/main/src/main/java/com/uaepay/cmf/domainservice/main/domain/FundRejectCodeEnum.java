package com.uaepay.cmf.domainservice.main.domain;

/**
 * <p>FundRejectCodeEnum</p>
 *
 * <AUTHOR>
 * @version FundRejectCodeEnum.java v1.0  2022/9/6 10:52
 */
public enum FundRejectCodeEnum implements FundRejectReason {

    /**
     * 拒付
     */
    CHARGE_BACK("CHARGE_BACK","CHARGE_BACK");


    private final String resultCode;
    private final String description;

    FundRejectCodeEnum(String resultCode, String description) {
        this.resultCode = resultCode;
        this.description = description;
    }


    @Override
    public String getDescription() {
        return resultCode;
    }

    @Override
    public String getResultCode() {
        return description;
    }
}
