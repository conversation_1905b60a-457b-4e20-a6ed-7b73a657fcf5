package com.uaepay.cmf.domainservice.main.repository.impl;

import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.basis.beacon.service.facade.domain.request.PageRequest;
import com.uaepay.biz.common.util.PageList;
import com.uaepay.cmf.common.core.dal.daointerface.InstBatchOrderDAO;
import com.uaepay.cmf.common.core.dal.daointerface.InstOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.InstBatchOrderDO;
import com.uaepay.cmf.common.core.dal.dataobject.InstOrderDO;
import com.uaepay.cmf.common.core.dal.dataobject.UniqueOrderDO;
import com.uaepay.cmf.common.core.dal.util.DbRouter;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.exception.WrongStateException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.ma.MemberChannelToken;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator;
import com.uaepay.cmf.common.core.engine.generator.SequenceNameEnum;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.common.core.query.InstBatchOrderPageQuery;
import com.uaepay.cmf.common.core.util.log.LogUtil;
import com.uaepay.cmf.domainservice.channel.holder.TokenHolder;
import com.uaepay.cmf.domainservice.main.convert.InstBatchOrderConverter;
import com.uaepay.cmf.domainservice.main.convert.InstOrderConverter;
import com.uaepay.cmf.domainservice.main.factory.BizProcessorFactory;
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.service.facade.domain.query.BatchOrderQueryRequest;
import com.uaepay.cmf.service.facade.enums.ArchiveStatusEnum;
import com.uaepay.common.lang.Paginator;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>机构订单仓储默认实现</p>
 *
 * <AUTHOR>
 * <AUTHOR> Liu
 * @version $Id: DefaultInstOrderRepository.java, v 0.1 2012-8-3 上午10:13:06 fuyangbiao Exp $
 */
@Repository("instOrderRepository")
public class DefaultInstOrderRepository implements InstOrderRepository {
    private Logger logger = LoggerFactory.getLogger(DefaultInstOrderRepository.class);
    @Resource
    private InstOrderDAO instOrderDAO;
    @Resource
    private InstBatchOrderDAO instBatchOrderDAO;
    @Resource
    private PrimaryKeyGenerator primaryKeyGenerator;
    @Resource
    private TransactionTemplate cmfTransactionTemplate;
    @Resource
    private CmfOrderRepository cmfOrderRepository;
    @Resource
    private CardTokenRepository cardTokenRepository;
    @Resource
    private BizProcessorFactory bizProcessorFactory;


    private static final int CNT_ONCE_TIME = 100;

    @Value("${cmf.sharding.closeNewDb:N}")
    private String closeNewDb;

    @Override
    public List<InstOrder> loadByCmfSeqNo(String cmfSeqNo) {
        List<InstOrder> instOrderList = new ArrayList<>();
        List<InstOrderDO> instOrderDOList = instOrderDAO.loadByCmfSeq(cmfSeqNo);
        if (CollectionUtils.isEmpty(instOrderDOList)) {
            return instOrderList;
        }
        instOrderList = buildInstOrderList(instOrderDOList);
        return instOrderList;
    }

    @Override
    public InstOrder loadByCmfSeqNoSingle(String cmfSeqNo) {
        List<InstOrder> instOrderList = loadByCmfSeqNo(cmfSeqNo);
        return CollectionUtils.isEmpty(instOrderList) ? null : instOrderList.get(0);
    }

    @Override
    public InstOrder loadById(Long id, boolean isLock) {
        if (id == null) {
            return null;
        }
        InstOrderDO instOrderDO = isLock ? instOrderDAO.lockedById(id) : instOrderDAO.loadById(id);
        return buildInstOrder(instOrderDO);
    }

    @Override
    public InstOrder loadByNo(String instOrderNo) {
        UniqueOrderDO uniqueOrderDO = instOrderDAO.loadUniqueOrderByNo(instOrderNo);
        if (uniqueOrderDO == null) {
            return null;
        }
        InstOrderDO instOrderDO = instOrderDAO.loadById(uniqueOrderDO.getInstOrderId());
        return buildInstOrder(instOrderDO);
    }

    @Override
    public boolean updateBookingSubmit(final InstOrder instOrder, Date bookingSubmit) {
        int rows = instOrderDAO.updateBookingSubmitById(bookingSubmit, instOrder.getInstOrderId());
        if (rows == 1) {
            instOrder.setGmtBookingSubmit(bookingSubmit);
        }
        return rows == 1;
    }

    @Override
    public boolean updateInstOrderStatus(final InstOrder instOrder, InstOrderStatus targetStatus) {
        // 不是最终状态,不更新
        if (InstOrderStatus.IN_PROCESS.equals(targetStatus)) {
            return false;
        }
        // 原订单是处理中,更新订单状态
        // 如果原订单不为处理中并且与需要更新的状态不一致,则异常
        if (InstOrderStatus.IN_PROCESS.equals(instOrder.getStatus())
                || InstOrderStatus.CANCEL.equals(instOrder.getStatus())
                || InstOrderStatus.HALF_SUCCESSFUL == instOrder.getStatus()
                || InstOrderStatus.REDIRECT_VERIFY == instOrder.getStatus()) {
            int rows = updateStatusById(instOrder.getInstOrderId(), targetStatus, instOrder.getStatus());
            Assert.isTrue(rows == 1, "机构订单状态跃迁异常");
            instOrder.setStatus(targetStatus);
            return true;
        }
        return false;
    }

    @Override
    public boolean updateInstOrderStatusNoCheck(InstOrder instOrder, InstOrderStatus targetStatus) {
        int rows = updateStatusById(instOrder.getInstOrderId(), targetStatus, instOrder.getStatus());
        Assert.isTrue(rows == 1, "机构订单状态跃迁异常");
        instOrder.setStatus(targetStatus);
        return true;
    }

    private List<InstOrder> buildInstOrderList(List<InstOrderDO> instOrderDOList) {
        if (CollectionUtils.isEmpty(instOrderDOList)) {
            return null;
        }
        if (instOrderDOList.size() == 1) {
            return new ArrayList<>(Arrays.asList(buildInstOrder(instOrderDOList.get(0)), new InstOrder()));
        }
        List<InstOrder> instOrderList = new ArrayList<>();
        for (InstOrderDO instOrderDO : instOrderDOList) {
            InstOrder instOrder = null;
            BizType bizType = BizType.getByCode(instOrderDO.getOrderType());
            Assert.notNull(bizType, "业务类型不支持");

            instOrder = bizProcessorFactory.get(bizType).convertDO2Dto(instOrderDO);

            instOrderList.add(instOrder);
        }
        return instOrderList;
    }

    /**
     * 组装机构订单领域对象
     *
     * @param instOrderDO
     * @return
     */
    private InstOrder buildInstOrder(InstOrderDO instOrderDO) {
        if (instOrderDO == null) {
            return null;
        }
        InstOrder instOrder = null;
        BizType bizType = BizType.getByCode(instOrderDO.getOrderType());
        if (bizType == null) {
            throw new IllegalArgumentException("订单类型不支持:" + instOrderDO.getOrderType());
        }

        instOrder = bizProcessorFactory.get(bizType).convertDO2Dto(instOrderDO);

        return instOrder;
    }

    @Override
    public void store(InstOrder instOrder) {
        // 设置ID
        String instOrderId = DbRouter.gen4Sharding(primaryKeyGenerator.generateKey(SequenceNameEnum.INST_ORDER), instOrder.getCmfSeqNo());

        instOrder.setInstOrderId(Long.valueOf(instOrderId));

        // 插入机构订单
        instOrderDAO.insertUnique(InstOrderConverter.convertUnique(instOrder));

        // 插入主表
        instOrderDAO.insert(InstOrderConverter.convert(instOrder));

        bizProcessorFactory.get(instOrder.getBizType()).insertSubOrder(instOrder);

    }

    @Override
    public void store(final List<InstOrder> instOrderList) {
        if (CollectionUtils.isEmpty(instOrderList)) {
            return;
        }

        cmfTransactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                for (InstOrder instOrder : instOrderList) {
                    store(instOrder);
                }
            }
        });
    }

    @Override
    public void storeExtension(InstOrder order) {
        if (order.getExtension() != null) {
            instOrderDAO.updateExtensionById(CommonConverter.convertToDb(order.getExtension()), order.getInstOrderId());
        }
    }

    @Override
    public void storeExtension(InstBatchOrder order) {
        if (order.getExtension() != null) {
            instBatchOrderDAO.updateExtensionById(CommonConverter.convertToDb(order.getExtension()), order.getArchiveBatchId());
        }
    }

    @Override
    public int updateCommunicateStatusWithPreStatus(InstOrder instOrder, CommunicateStatus communicateStatus, CommunicateStatus preStatus) {
        int count = instOrderDAO.updateCommunicateStatusWithPreStatus(communicateStatus.getCode(), instOrder.getInstOrderId(), preStatus.getCode());
        if (count != 0) {
            instOrder.setCommunicateStatus(communicateStatus);
        }
        return count;
    }

    @Override
    public int updateRetryDataWithPreStatus(InstOrder instOrder, CommunicateStatus communicateStatus, CommunicateStatus preStatus) {
        Map<String, String> extMap = instOrder.getExtension();
        String extension = extMap == null ? null : JsonUtil.mapToString(extMap);
        int count = instOrderDAO.updateRetryDataWithPreStatus(communicateStatus.getCode(), instOrder.getInstOrderId(), preStatus.getCode(), instOrder.getInstOrderNo(), extension);
        if (count != 0) {
            instOrder.setCommunicateStatus(communicateStatus);
        }
        return count;
    }

    @Override
    public List<InstOrder> getRefundOrderByFundInOrder(String fundInOrderNo, List<InstOrderStatus> orderStatusList) {
        List<String> orderStatusStrList = new ArrayList<>();
        List<InstOrder> instOrderList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderStatusList)) {
            for (InstOrderStatus orderStatus : orderStatusList) {
                orderStatusStrList.add(orderStatus.getCode());
            }
        }

        List<InstOrderDO> instOrderDOList = instOrderDAO.getRefundOrderByFundInOrder(fundInOrderNo, orderStatusStrList);
        for (InstOrderDO instOrderDO : instOrderDOList) {
            instOrderList.add(buildInstOrder(instOrderDO));
        }
        return instOrderList;
    }

    @Override
    public int updateChannelInfoById(String fundChannelCode, String fundChannelApi, long instOrderId) {
        return instOrderDAO.updateChannelInfoById(fundChannelCode, fundChannelApi, instOrderId);
    }

    @Override
    public List<InstOrder> getInstOrderListByAichiveBatchId(Long archiveBatchId) {
        List<InstOrderDO> instOrderDOList = instOrderDAO.loadByArchiveBatchId(archiveBatchId);
        List<InstOrder> instOrderList = new ArrayList<>();
        for (InstOrderDO instOrderDO : instOrderDOList) {
            instOrderList.add(bizProcessorFactory.get(BizType.FUNDOUT).convertDO2Dto(instOrderDO));
        }
        return instOrderList;

    }

    @Override
    public List<Long> loadInstOrderIdListByBatchId(Long archiveBatchId) {
        return instOrderDAO.loadInstOrderIdListByBatchId(archiveBatchId);
    }

    @Override
    public List<Long> loadInstOrderIdListByBatchIdAndCurrency(Long archiveBatchId, String currency) {
        return instOrderDAO.loadInstOrderIdListByBatchIdAndCurrency(archiveBatchId, currency);
    }

    @Override
    public List<String> loadOrderCurrencyListByBatchId(Long archiveBatchId) {
        return instOrderDAO.loadOrderCurrencyListByBatchId(archiveBatchId);
    }

    @Override
    public List<InstOrder> getInstOrderListByBatchIdStatus(Long archiveBatchId, Date archiveDate, CommunicateStatus communicateStatus, InstOrderStatus instOrderStatus) {
        String communicateStatusStr = communicateStatus == null ? null : communicateStatus.getCode();
        String instOrderStatusStr = instOrderStatus == null ? null : instOrderStatus.getCode();

        List<InstOrderDO> instOrderDOList = instOrderDAO.loadByBatchIdStatus(archiveBatchId, archiveDate, communicateStatusStr, instOrderStatusStr);

        List<InstOrder> instOrderList = new ArrayList<>();
        for (InstOrderDO instOrderDO : instOrderDOList) {
            instOrderList.add(bizProcessorFactory.get(BizType.FUNDOUT).convertDO2Dto(instOrderDO));
        }
        return instOrderList;
    }


    @Override
    public InstBatchOrder loadById(Long archiveBatchId) {
        InstBatchOrderDO instBatchOrderDO = instBatchOrderDAO.loadById(archiveBatchId);
        if (instBatchOrderDO == null) {
            return null;
        }
        return InstBatchOrderConverter.convert(instBatchOrderDO);
    }

    @Override
    public InstBatchOrder loadByIdForUpdate(Long archiveBatchId) {
        InstBatchOrderDO instBatchOrderDO = instBatchOrderDAO.lockedById(archiveBatchId);
        return InstBatchOrderConverter.convert(instBatchOrderDO);
    }

    @Override
    public int updateRetryInfoById(Integer retryTimes, Date gmtNextRetry, Long instOrderId) {
        return instOrderDAO.updateRetryInfoById(retryTimes, gmtNextRetry, instOrderId);
    }

    @Override
    public int updateBatchRetryInfoById(Integer retryTimes, Date gmtNextRetry, Long orderId) {
        return instBatchOrderDAO.updateRetryInfoById(retryTimes, gmtNextRetry, orderId);
    }

    @Override
    public void updateAmountAndCount(InstBatchOrder instBatchOrder) {
        instBatchOrderDAO.updateAmountAndCount(instBatchOrder.getAmount(), instBatchOrder.getTotalCount(), instBatchOrder.getArchiveBatchId());
    }

    @Override
    public Money getArchiveBatchAmt(Long archiveBatchId) {
        return instOrderDAO.getArchiveBatchAmt(archiveBatchId);
    }

    @Override
    public int getArchiveBatchCnt(Long archiveBatchId) {
        return instOrderDAO.getArchiveBatchCnt(archiveBatchId);
    }

    @Override
    public void insert(InstBatchOrder instBatchOrder) {
        instBatchOrderDAO.insert(InstBatchOrderConverter.convert(instBatchOrder));
    }

    @Override
    @SuppressWarnings("unchecked")
    public void reloadInstBatchOrder(InstBatchOrder instBatchOrder) {
        int totalCount = this.getArchiveBatchCnt(instBatchOrder.getArchiveBatchId());
        Money totalAmt = this.getArchiveBatchAmt(instBatchOrder.getArchiveBatchId());
        instBatchOrder.setAmount(totalAmt);
        instBatchOrder.setTotalCount(totalCount);
    }

    @Override
    public Money getHasRefundAmount(String fundInInstOrderNo, String currency) {
        //包含：成功、成功有风险、处理中
        List<InstOrderStatus> instOrderStatus = new ArrayList<>(Arrays.asList(InstOrderStatus.IN_PROCESS, InstOrderStatus.SUCCESSFUL, InstOrderStatus.RISK));
        List<InstOrder> instOrderList = getRefundOrderByFundInOrder(fundInInstOrderNo, instOrderStatus);
        Money hasRefund = new Money(ZERO_MONEY_STRING, currency);
        for (InstOrder instOrder : instOrderList) {
            hasRefund = hasRefund.add(instOrder.getAmount());
        }
        return hasRefund;
    }

    @Override
    public int updateTempBatchId(Long archiveTemplateId, String apiCode, final Long tempBatchId, Long hours, Date bookingTime) {
        String communicateType = InstOrderCommunicateType.BATCH.getCode();
        int rows = 0;
        long startMillis = System.currentTimeMillis();
        int pages = getArchivePages(archiveTemplateId, apiCode, hours, bookingTime, communicateType);

        LogUtil.info("Archive page count," + pages, startMillis, System.currentTimeMillis());

        for (; pages > 0; pages--) {
            final List<Long> instOrderIdList = loadInstOrderList4ArchivePage(archiveTemplateId, apiCode, hours, bookingTime, communicateType);
            if (CollectionUtils.isEmpty(instOrderIdList)) {
                break;
            }

            startMillis = System.currentTimeMillis();
            rows += updateBatchByInstOrderId(tempBatchId, instOrderIdList);
            LogUtil.info("Archive Template Single Update,Pages=" + pages, startMillis, System.currentTimeMillis());
        }
        return rows;
    }

    @Override
    public List<Long> loadInstOrderList4ArchivePage(Long archiveTemplateId, String apiCode, Long hours, Date bookingTime, String communicateType) {
        return instOrderDAO.queryInstOrderList4ArchivePage(archiveTemplateId, hours, apiCode, communicateType, bookingTime, CNT_ONCE_TIME);
    }

    @Override
    public int getArchivePages(Long archiveTemplateId, String apiCode, Long hours, Date bookingTime, String communicateType) {
        return instOrderDAO.countArchivePages(archiveTemplateId, hours, apiCode, communicateType, bookingTime, CNT_ONCE_TIME);
    }

    @Override
    public Integer updateBatchByInstOrderId(Long tempBatchId, List<Long> instOrderIdList) {
        return cmfTransactionTemplate.execute(transactionStatus -> {
            int rows1 = instOrderDAO.updateTempBatchByInstOrderId(tempBatchId, instOrderIdList);
            Assert.isTrue(rows1 == instOrderIdList.size(), "更新数据异常");
            return instOrderIdList.size();
        });
    }


    @Override
    public int updateBatchId2Default(Long tempBatchId) {
        return instOrderDAO.updateBatchId2Default(tempBatchId, 0L);
    }

    @Override
    public boolean isCompleteSuccess(InstOrder instOrder) throws WrongStateException {

        if (!YesNo.YES.equals(instOrder.getIsSplit())) {
            return true;
        }

        if (!isReturnStatus(instOrder.getStatus())) {
            throw new WrongStateException(ErrorCode.WRONG_STATE_EXCEPTION, "当前订单状态不对,不为成功或失败[" + instOrder.getInstOrderNo() + "]");
        }

        boolean result = true;
        List<InstOrder> list = loadByCmfSeqNo(instOrder.getCmfSeqNo());

        Money sumAmount = new Money(instOrder.getAmount().getAmount(), instOrder.getAmount().getCurrency());

        for (InstOrder instOrderTemp : list) {
            if (!instOrder.getInstOrderNo().equals(instOrderTemp.getInstOrderNo()) && !instOrder.getStatus().equals(instOrderTemp.getStatus())) {
                if (isLastStatus(instOrderTemp.getStatus()) && isLastStatus(instOrder.getStatus())) {
                    throw new WrongStateException(ErrorCode.WRONG_STATE_EXCEPTION, "同一批订单状态不一致,当前订单[" + instOrder.getInstOrderNo() + "]");
                } else {
                    result = false;
                }
            }
            if (!instOrder.getInstOrderNo().equals(instOrderTemp.getInstOrderNo())) {
                sumAmount = sumAmount.add(instOrderTemp.getAmount());
            }
        }
        if (result) {
            CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(instOrder.getCmfSeqNo(), false);
            if (!sumAmount.equals(cmfOrder.getAmount())) {
                result = false;
            }
        }

        return result;
    }

    @Override
    public boolean isBatchComplete(InstBatchOrder instBatchOrder) {
        List<InstOrder> instOrderList = getInstOrderListByAichiveBatchId(instBatchOrder.getArchiveBatchId());
        Money sumAmount = new Money("0.00", instOrderList.get(0).getAmount().getCurrency());
        for (InstOrder instOrderTemp : instOrderList) {
            if (!isLastStatus(instOrderTemp.getStatus())) {
                //不为最终状态，返回false
                return false;
            }
            sumAmount = sumAmount.add(instOrderTemp.getAmount());
        }
        //校验金额和笔数
        return sumAmount.equals(instBatchOrder.getAmount()) && instOrderList.size() == instBatchOrder.getTotalCount();
    }

    @Override
    public List<Long> loadBatchInstOrders(ArchiveStatusEnum archiveStatus, YesNo isLocked, String apiCode, String operator, int batchSize, int page) {
        String status = archiveStatus == null ? null : archiveStatus.getCode();
        String isLockedStr = isLocked == null ? null : isLocked.getCode();
        return instBatchOrderDAO.queryForBatchFundout(status, isLockedStr, apiCode, operator, batchSize, page);
    }

    @Override
    public int updateIsLockedByOriStatus(final List<Long> archiveBatchIdList, final YesNo status, final YesNo oriStatus) {
        return cmfTransactionTemplate.execute(transactionStatus -> {

            int innerRows = instBatchOrderDAO.updateIsLockedByOriStatus(archiveBatchIdList, status.getCode(), oriStatus.getCode());
            Assert.isTrue(innerRows == archiveBatchIdList.size(), "更新条数不匹配");
            return innerRows;
        });
    }

    @Override
    public int updateIsLockedByOriStatus(Long archiveBatchId, YesNo status, YesNo oriStatus) {
        return updateIsLockedByOriStatus(Arrays.asList(archiveBatchId), status, oriStatus);
    }

    @Override
    public int updateBatchOrderStatus(Long archiveBatchId, InstOrderArchiveStatus archiveStatus, InstOrderArchiveStatus preStatus) {
        return instBatchOrderDAO.updateStatusByIdAndPreStatus(archiveStatus.getCode(), preStatus.getCode(), archiveBatchId);
    }

    @Override
    public int updateCommunicateStatusByInstOrders(final List<InstOrder> instOrderList, CommunicateStatus targetStatus, CommunicateStatus preStatus) {
        List<Long> instOrderIdList = instOrderList.stream().map(InstOrder::getInstOrderId).collect(Collectors.toList());

        int modCount = instOrderDAO.updateCommunicateStatusBatchByIds(instOrderIdList, targetStatus.getCode(), preStatus.getCode());

        if (modCount == instOrderList.size()) {
            for (InstOrder instOrder : instOrderList) {
                instOrder.setCommunicateStatus(targetStatus);
            }
        }
        return modCount;
    }

    private boolean isReturnStatus(InstOrderStatus status) {
        return (InstOrderStatus.SUCCESSFUL.equals(status) || InstOrderStatus.FAILURE.equals(status) || InstOrderStatus.RISK.equals(status));
    }

    private boolean isLastStatus(InstOrderStatus status) {
        return (InstOrderStatus.SUCCESSFUL.equals(status) || InstOrderStatus.FAILURE.equals(status));
    }

    @Override
    public int updateBatchIdListByTempBatchId(Long tempBatchId, Long archiveBatchId, List<Long> instOrderIdList) {
        return instOrderDAO.updateBatchIdListByTempBatchId(tempBatchId, archiveBatchId, instOrderIdList);
    }

    @Override
    public int updateMemoById(String memo, Long instOrderId) {
        return instOrderDAO.updateMemoById(memo, instOrderId);
    }

    @Override
    public Pair<List<InstOrder>, Paginator> queryChannelOrders(PageRequest pageRequest) {
        PageList list = instOrderDAO.queryInstOrders(pageRequest);
        List<InstOrder> instOrderList = (List<InstOrder>) list.stream().map(item -> buildInstOrder((InstOrderDO) item)).collect(Collectors.toList());
        return Pair.of(instOrderList, list.getPaginator());
    }

    @Override
    public int updateFlagWithOrderIdAndPreFlag(Long instOrderId, OrderFlag flag, OrderFlag preFlag) {
        return instOrderDAO.updateFlagWithOrderIdAndPreFlag(instOrderId, flag.getCode(), preFlag.getCode());
    }

    @Override
    public int updateStatusById(long instOrderId, InstOrderStatus targetStatus, InstOrderStatus preStatus) {
        return instOrderDAO.updateStatusById(instOrderId, targetStatus.getCode(), preStatus.getCode());
    }

    @Override
    public int updateAmountAndExtension(long instOrderId, Money amount, String extension) {
        return instOrderDAO.updateAmountAndExtension(instOrderId, amount, extension);
    }

    @Override
    public int updateCommunicateStatusById(CommunicateStatus communicateStatus, long instOrderId) {
        return instOrderDAO.updateCommunicateStatusById(communicateStatus.getCode(), instOrderId);
    }

    @Override
    public Money sumAmountForQueryResult(List<String> communicateStatusList, String orderType, List<String> channelCodeList, Date startDate, Date endDate) {
        return instOrderDAO.sumAmountForQueryResult(communicateStatusList, orderType, channelCodeList, startDate, endDate);
    }

    @Override
    public void delete(InstOrder instOrder) {

        instOrderDAO.delete(instOrder.getInstOrderId());

        bizProcessorFactory.get(instOrder.getBizType()).deleteSubOrder(instOrder.getInstOrderId());

    }

    @Override
    public int updateAdvanceStatusWithPreStatus(IsAdvance advanceStatus, Long instOrderId, IsAdvance preStatus) {
        return instOrderDAO.updateAdvanceStatusWithPreStatus(advanceStatus.getCode(), instOrderId, preStatus.getCode());
    }

    @Override
    public int updateOrderNoAndStatus(String newInstOrderNo, InstOrder instOrder) {
        int rows = instOrderDAO.updateFoOrderRetryWithOrderId(newInstOrderNo, instOrder.getMemo(), instOrder.getInstCode(), instOrder.getStatus().getCode(), instOrder.getCommunicateStatus().getCode(), instOrder.getInstOrderId(), instOrder.getInstOrderNo());
        if (rows == 1) {
            instOrder.setInstOrderNo(newInstOrderNo);
        }
        return rows;
    }

    /**
     * 保存机构订单并回写机构订单id
     *
     * @param instOrderList
     */
    @Override
    public void storeInstOrder(final List<InstOrder> instOrderList) {

        cmfTransactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                List<InstOrderDO> dbList = instOrderDAO.lockedByCmfSeq(instOrderList.get(0).getCmfSeqNo());
                Assert.isTrue(CollectionUtils.isEmpty(dbList), "订单列表不为空，请检查");

                long startMillis = System.currentTimeMillis();

                // 保存机构订单
                store(instOrderList);
                // 回写机构订单id
                if (instOrderList.size() == 1) {
                    updateCardToken(instOrderList.get(0));
                }
                long endMillis = System.currentTimeMillis();
                LogUtil.info("InstOrder Insert Cost", startMillis, endMillis);
            }
        });
    }

    private void updateCardToken(InstOrder instOrder) {
        Map<String, Object> holderMap = TokenHolder.get();
        if (holderMap == null || !holderMap.containsKey(ExtensionKey.CARD_TOKEN.key)) {
            return;
        }
        CardToken cardToken = (CardToken) holderMap.get(ExtensionKey.CARD_TOKEN.key);
        // 对于撤销交易，发过来的cardToken与支付相同
        // fixme 但是目前无法区分退款还是撤销，暂时将退款交易都不校验
        if (instOrder.getBizType() == BizType.REFUND && cardToken.getInstOrderId() != null) {
            return;
        }
        Assert.isTrue(cardToken.getInstOrderId() == null, "Card token has been used!");
        String instTokenId = null;
        if (holderMap.containsKey(ExtensionKey.MEMBER_TOKENS.key)) {
            List<MemberChannelToken> memberTokens = (List<MemberChannelToken>) holderMap.get(ExtensionKey.MEMBER_TOKENS.key);
            for (MemberChannelToken token : memberTokens) {
                if (instOrder.getFundChannelCode().equals(token.getFundChannelCode())) {
                    instTokenId = token.getToken();
                }
            }
        }
        cardTokenRepository.updateInstInfo(instOrder.getInstOrderId(), instTokenId, cardToken);
        holderMap.put(ExtensionKey.CARD_TOKEN.key, cardToken);
        TokenHolder.set(holderMap);
    }


    @Override
    public Set<Long> loadSingleOrder4Query(Date startTime, Date endTime, int maxSize, BizType bizType) {
        Set<Long> dataSet = new HashSet<>();
        String[] tabSuffixList = DbRouter.getTableSuffixArr();
        if (YesNo.YES.getCode().equals(closeNewDb)) {
            tabSuffixList = new String[]{""};
        }
        for (String tabSuffix : tabSuffixList) {
            List<Long> orderList = instOrderDAO.loadSingleOrder4Query(startTime, endTime, maxSize, bizType.getCode(), tabSuffix);
            if (!CollectionUtils.isEmpty(orderList)) {
                dataSet.addAll(orderList);
            }
        }
        return dataSet;
    }

    @Override
    public Set<Long> loadInstOrder4Send(List<String> channelList, List<String> ignoreChannelList, BizType bizType, Date startDate, Date endDate, int maxSize) {
        Set<Long> dataSet = new HashSet<>();
        String[] tabSuffixList = DbRouter.getTableSuffixArr();
        if (YesNo.YES.getCode().equals(closeNewDb)) {
            tabSuffixList = new String[]{""};
        }
        for (String tabSuffix : tabSuffixList) {
            List<Long> orderList = instOrderDAO.loadSingleOrder4Send(channelList, ignoreChannelList, bizType.getCode(), startDate, endDate, tabSuffix, maxSize);
            if (!CollectionUtils.isEmpty(orderList)) {
                dataSet.addAll(orderList);
            }
        }

        return dataSet;
    }

    @Override
    public int updateCommunicateStatusByArchiveBatchId(long archiveBatchId, CommunicateStatus targetStatus) {
        List<InstOrderDO> instOrderList = instOrderDAO.loadByArchiveBatchId(archiveBatchId);
        List<Long> instOrderIdList = instOrderList.stream().map(InstOrderDO::getInstOrderId).collect(Collectors.toList());
        return instOrderDAO.updateCommunicateStatusBatchByIds(instOrderIdList, targetStatus.getCode(), instOrderList.get(0).getCommunicateStatus());
    }

    @Override
    public List<String> queryMigrateDateList(String channelCode) {
        return instOrderDAO.queryFOFinishedDateList(channelCode);
    }

    @Override
    public PageList queryBatchList(BatchOrderQueryRequest request) {
        InstBatchOrderPageQuery query = new InstBatchOrderPageQuery();
        BeanUtils.copyProperties(request, query);
        PageList batchList = instBatchOrderDAO.loadBatchList(query);
        PageList respList = new PageList();
        respList.setPaginator(batchList.getPaginator());
        List<InstBatchOrder> batchOrderList = (List<InstBatchOrder>) batchList.stream().map(item -> InstBatchOrderConverter.convert((InstBatchOrderDO) item)).collect(Collectors.toList());
        respList.addAll(batchOrderList);
        return respList;
    }

    @Override
    public boolean updateBatchRetryById(Date gmtRetry, Long archiveBatchId) {
        InstBatchOrderDO bo = instBatchOrderDAO.loadById(archiveBatchId);
        return cmfTransactionTemplate.execute(status -> {
            int rows = instBatchOrderDAO.updateStatusAndArchiveTimeByIdAndPreStatus(InstOrderArchiveStatus.AWAITING.getCode(), gmtRetry, InstOrderArchiveStatus.SUBMMITED.getCode(), archiveBatchId);
            Assert.isTrue(rows == 1, "更新失败");
            int instRows = instOrderDAO.updateCommStatusAndBookingByArchiveId(CommunicateStatus.AWAITING.getCode(), gmtRetry, archiveBatchId, CommunicateStatus.SENT.getCode());
            Assert.isTrue(instRows == bo.getTotalCount(), "更新条数与记录数不等:" + instRows);
            return true;
        });
    }

    public void setCloseNewDb(String closeNewDb) {
        this.closeNewDb = closeNewDb;
    }


    @Override
    public List<InstOrder> loadInstOrderListByIds(List<Long> instOrderIds) {
        if (CollectionUtils.isEmpty(instOrderIds)) {
            return new ArrayList<>();
        }
        //查询机构订单列表
        List<InstOrderDO> instOrderDOS = instOrderDAO.loadInstOrderIdListByIds(instOrderIds);
        return buildInstOrderList(instOrderDOS);
    }
}