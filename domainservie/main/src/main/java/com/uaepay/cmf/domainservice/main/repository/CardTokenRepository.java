package com.uaepay.cmf.domainservice.main.repository;

import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.vo.CardToken;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenRepository.java v1.0  2020-03-28 15:48
 */
public interface CardTokenRepository {

    /**
     * 保存cardToken
     *
     * @param cardToken
     * @return
     */
    String store(CardToken cardToken);

    /**
     * 更新 cardToken
     * @param cardToken
     * @return
     */
    int update(CardToken cardToken);

    int updateInstInfo(Long instOrderId, String instTokenId, CardToken cardToken);

    /**
     * 更新 cardToken
     * @param cardToken
     * @return
     */
    int updateSelective(CardToken cardToken);

    /**
     * 根据id查询cardToken
     * @param cardTokenId
     * @return
     */
    CardToken query(String cardTokenId);

    CardToken queryByInstOrderId(Long instOrderId);

    CardToken queryByOrder(InstBaseOrder baseOrder);
}
