package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.YesNoEnum;
import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.PaymentNotifyLog;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.NotifyStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.common.enums.MonitorItem;
import com.uaepay.cmf.common.monitor.MonitorLog;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.domainservice.main.convert.InstitutionCmfResultConverter;
import com.uaepay.cmf.domainservice.main.process.MonitorService;
import com.uaepay.cmf.domainservice.main.process.NotifyPaymentService;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository;
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService;
import com.uaepay.cmf.fss.ext.integration.payment.PaymentClient;
import com.uaepay.cmf.service.facade.result.CmfResult;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.SuccessFailure;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>通知PE支付结果信息实现</p>
 *
 * <AUTHOR> Liu
 * @version $Id: DefaultNotifyPaymentService.java, v 0.1 2012-8-14 下午2:54:49 liumaoli Exp $
 */
@Service
public class DefaultNotifyPaymentService implements NotifyPaymentService {

    private static final Logger logger = LoggerFactory
            .getLogger(DefaultNotifyPaymentService.class);

    @Resource
    private CmfOrderRepository cmfOrderRepository;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private InstOrderResultRepository instOrderResultRepository;

    @Resource
    private MonitorService monitorService;

    @Resource
    private PaymentClient paymentClient;

    @Resource
    private OrderLoaderService orderLoaderService;

    @Resource
    private ChannelCodeMappingService channelCodeMappingService;
    
    @Override
    public void notifyPE(String cmfSeqNo, Boolean isRetry) {
        //银行机构流水号
        CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(cmfSeqNo, false);
        if (cmfOrder == null) {
            logger.info("Cmf订单{}为空", cmfSeqNo);
            return;
        }
        InstOrder instOrder = instOrderRepository.loadByCmfSeqNoSingle(cmfOrder.getOrderSeqNo());
        if (!isNeedNotifyPE(cmfOrder, instOrder, isRetry)) {
            logger.info("PE订单{},状态{}不需要通知PE结果", cmfOrder.getPaymentSeqNo(), cmfOrder.getStatus());
            return;
        }
        //若无机构订单(比如出款审核不通过)
        if (instOrder == null) {
            List<InstOrder> instOrderSplit = instOrderRepository.loadByCmfSeqNo(cmfOrder
                    .getOrderSeqNo());
            if (CollectionUtils.isNotEmpty(instOrderSplit)) {
                InstOrderResult result = instOrderResultRepository
                        .loadRealResultByOrder(instOrderSplit.get(0).getInstOrderId());
                notifyPE(cmfOrder, instOrderSplit.get(0), result, isRetry);
            } else {
                notifyPE(cmfOrder, null, null, isRetry);
            }
        } else {
            InstOrderResult result = instOrderResultRepository.loadRealResultByOrder(instOrder
                    .getInstOrderId());
            notifyPE(cmfOrder, instOrder, result, isRetry);
        }
    }

    /**
     * CMF处理时间过长，并且该笔单子有明确结果；则MQ方式异步通知PE。
     *
     * @param cmfOrder
     * @param instResult
     */
    @Override
    public void notifyResult(CmfOrder cmfOrder, InstOrderResult instResult) {
        try {
            // 若处理超过了15秒，则异步通知PE.
            // 或者instResult中包含了需要通知参数
            if (instResult != null
                    && InstOrderProcessStatus.SUCCESS.equals(instResult.getProcessStatus())
                    && cmfOrder.getGmtCreate() != null) {
                if (!notNeedNotifyResult(instResult) && (timeOut(cmfOrder.getGmtCreate()) || needNotifyResult(instResult) || isAsyncApi())) {
                    logger.info("CMF处理时间过长, MQ通知：{}", instResult);
                    notifyPE(cmfOrder.getOrderSeqNo(), false);
                }
            }
        } catch (Exception e) {
            logger.error("[补发JMS失败](paymentSeqNo=" + cmfOrder.getPaymentSeqNo() + "):", e);
        }
    }

    private boolean isAsyncApi() {
        ChannelVO channel = ChannelHolder.get();
        return channel != null && channel.getChannelApi() != null && FundChannelApiType.isAsync(channel.getChannelApi().getApiType());
    }

    private boolean timeOut(Date gmtCreate) {
        long processTime = System.currentTimeMillis() - gmtCreate.getTime();
        return processTime > TIME_OUT_MILLIS;
    }

    private boolean needNotifyResult(InstOrderResult instResult) {
        return instResult != null && instResult.getExtension() != null && YesNoEnum.YES.getCode().equals(instResult.getExtension().get(NEED_NOTIFY));
    }

    private boolean notNeedNotifyResult(InstOrderResult instResult){
        return instResult != null && instResult.getExtension() != null && YesNoEnum.NO.getCode().equals(instResult.getExtension().get(NEED_NOTIFY));
    }

    @Override
    public void notifyPE(CmfOrder cmfOrder, InstOrder instOrder, InstOrderResult result,
                         Boolean isRetry) {
        if (cmfOrder == null) {
            logger.info("Cmf订单为空");
            return;
        }
        if (!isNeedNotifyPE(cmfOrder, instOrder, isRetry)) {
            logger.info("PE订单{},状态{}不需要通知PE结果", cmfOrder.getPaymentSeqNo(), cmfOrder.getStatus());
            return;
        }
        executeTask(cmfOrder, instOrder, result);
    }

    /**
     * MQ通知PE.
     *
     * @param cmfOrder
     * @param result
     * @param instOrder
     * @return
     */
    private BaseResult executeTask(CmfOrder cmfOrder, InstOrder instOrder, InstOrderResult result) {

        BaseResult notifyResult = null;
        try {

            notifyResult = execute(cmfOrder, instOrder, result);

            if (notifyResult.isSuccess()) {
                String logMessage = "订单(" + cmfOrder.getPaymentSeqNo() + ")通知PE成功";
                //出款，通知PE渠道编号+channelPayNo
                if (cmfOrder.getBizType() == BizType.FUNDOUT
                        && cmfOrder.getStatus() == CmfOrderStatus.IN_PROCESS) {
                    cmfOrder.setPaymentNotifyStatus(NotifyStatus.CHANNEL_CODE_NOTIFY_SUCCESS);
                    logMessage += ";出款渠道及channelPayNo";
                } else {
                    cmfOrder.setPaymentNotifyStatus(NotifyStatus.SUCCESSFUL);
                }
                logger.info(logMessage);
            } else {
                logger.info("订单:{}通知PE失败， 失败原因：", cmfOrder.getPaymentSeqNo(),
                        notifyResult.getResultMessage());
                cmfOrder.setPaymentNotifyStatus(NotifyStatus.FAILURE);
            }

        } catch (Exception e) {
            logger.error("CMF订单" + cmfOrder.getPaymentSeqNo() + "通知PE发送异常", e);
            cmfOrder.setPaymentNotifyStatus(NotifyStatus.FAILURE);

            notifyResult = new BaseResult();
            notifyResult.setSuccess(false);
            notifyResult.setResultMessage("通知PE失败，" + e.getMessage());

            //监控 - PE通讯异常
            monitorService.logMonitorEvent(new MonitorLog(cmfOrder.getPaymentSeqNo(),
                    MonitorItem.JMS_SEND_EXCEPTION, null, e));

        } finally {
            //记录日志，并修改cmfOrder通知状态.
            notifyLog(cmfOrder, notifyResult);
            cmfOrderRepository.updatePaymentNotifyStatusById(cmfOrder.getPaymentNotifyStatus(),
                    cmfOrder.getOrderSeqNo());
        }

        return notifyResult;
    }

    private BaseResult execute(CmfOrder cmfOrder, InstOrder instOrder, InstOrderResult result) {
        CmfResult cmfResult;
        BaseResult notifyResult = null;
        //若无机构订单(比如出款审核不通过)
        if (instOrder == null) {
            cmfResult = InstitutionCmfResultConverter.convert(cmfOrder);
            logger.info("CMF订单({})通知PE MQ参数转换结果{}", cmfOrder.getPaymentSeqNo(), cmfResult);
        } else {
            cmfResult = InstitutionCmfResultConverter.convert(cmfOrder, instOrder, result);

            cmfResult
                    .setChannelPayNo(orderLoaderService.loadReturnOrderNo(instOrder, result));
            logger.info("CMF订单({}),机构订单:({})通知PE MQ参数转换结果{}", cmfOrder.getPaymentSeqNo(),
                    instOrder.getInstOrderNo(), cmfResult);
        }

        String newChannelCode = channelCodeMappingService.getNewChannelCode(
        cmfResult.getFundsChannelCode(), 
        instOrder);
        cmfResult.setFundsChannelCode(newChannelCode);
        //MQ通知PE
        notifyResult = paymentClient.sendInstCmfResult(cmfResult);
        return notifyResult;
    }

    /**
     * 记录通知日志.
     *
     * @param cmfOrder
     * @param notifyResult
     */
    private void notifyLog(CmfOrder cmfOrder, BaseResult notifyResult) {
        PaymentNotifyLog log = new PaymentNotifyLog();
        log.setChannelSeqNo(cmfOrder.getOrderSeqNo());
        log.setNotifyResult(notifyResult.isSuccess() ? SuccessFailure.SUCCESSFUL
                : SuccessFailure.FAILURE);
        log.setMemo(notifyResult.getResultMessage());
        cmfOrderRepository.storeNotifyLog(log);
    }

    private boolean isNeedNotifyPE(CmfOrder cmfOrder, InstOrder instOrder, Boolean isRetry) {
        //机构订单未产生,用cmf订单判断
        if (instOrder == null) {
            return cmfOrder != null
                    && (CmfOrderStatus.CANCEL.equals(cmfOrder.getStatus())
                    || CmfOrderStatus.FAILURE.equals(cmfOrder.getStatus()) || CmfOrderStatus.SUCCESSFUL
                    .equals(cmfOrder.getStatus()));
        }

        // 对于出款订单,需要再文件下载后通知PE渠道编号
        if (instOrder.getBizType() == BizType.FUNDOUT) {
            //退款转出款的失败 不要发通知
            if (BizType.REFUND.equals(cmfOrder.getBizType()) && CmfOrderStatus.FAILURE.equals(cmfOrder.getStatus())) {
                logger.warn("cmf_seq_no-{}, refund-change-to-fundout-fail-warn", cmfOrder.getOrderSeqNo());
                return false;
            }

            if (instOrder.getArchiveBatchId() != null && instOrder.getArchiveBatchId() > 0L
                    && InstOrderStatus.IN_PROCESS.equals(instOrder.getStatus())) {
                return true;
            }
        }

        return (CmfOrderStatus.SUCCESSFUL.equals(cmfOrder.getStatus()) || CmfOrderStatus.FAILURE
                .equals(cmfOrder.getStatus()))
                && (!NotifyStatus.SUCCESSFUL.equals(cmfOrder.getPaymentNotifyStatus()) || isRetry);
    }

}
