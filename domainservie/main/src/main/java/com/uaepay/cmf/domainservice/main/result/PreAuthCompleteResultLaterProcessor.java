package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.schema.cmf.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <p>PreAuthCompleteResultLaterProcessor</p>
 *
 * <AUTHOR>
 * @version PreAuthUpdateResultLaterProcessor.java v1.0  2022/10/10 11:18
 */
@Slf4j
@Component
public class PreAuthCompleteResultLaterProcessor extends PreAuthUpdateResultLaterProcessor {

    @Resource
    private InstResultProcessor instResultProcessor;

    @PostConstruct
    public void init() {
        ResultLaterProcessorEnum.PREAUTH_COMPLETE.register(this);
    }

    @Override
    public Result<?> process(InstControlOrder instControlOrder, InstControlOrderResult instControlResult) {
        if (InstOrderResultStatus.SUCCESSFUL != instControlResult.getStatus()) {
            return Result.ofNothing();
        }
        //先更新金额
        super.process(instControlOrder, instControlResult);

        log.info("预授权订单结果更新处理-控制单:{},控制单结果:{}", instControlOrder, instControlResult);

        InstOrder preInstOrder = instOrderRepository.loadByNo(instControlOrder.getPreInstOrderNo());

        InstOrderResult instResult = new InstOrderResult();

        BeanUtils.copyProperties(instControlResult, instResult);

        // 4. 更新机构订单状态，保存扩展信息
        instResultProcessor.updateOrderStatusAndExt(preInstOrder, instResult);

        // 5. 更新cmf订单状态
        instResultProcessor.updateCmfOrderStatus(preInstOrder);

        return Result.ofSuccess();

    }
}
