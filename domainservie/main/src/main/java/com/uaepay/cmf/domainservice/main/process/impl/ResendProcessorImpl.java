package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.domainservice.channel.holder.TokenHolder;
import com.uaepay.cmf.domainservice.channel.router.ConfigurationService;
import com.uaepay.cmf.domainservice.main.process.ResendProcessor;
import com.uaepay.cmf.domainservice.main.result.ControlResultProcessor;
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor;
import com.uaepay.cmf.domainservice.main.sender.ControlOrderSendService;
import com.uaepay.cmf.domainservice.main.sender.InstOrderSendService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 补单流程实现.
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: ResendProcessorImpl.java, v 0.1 2011-4-12 下午03:55:16 sean won
 * Exp $
 */
@Service
public class ResendProcessorImpl implements ResendProcessor {

    @Resource
    private InstResultProcessor instResultProcessor;

    @Resource
    private ControlResultProcessor controlResultProcessor;

    @Resource
    private InstOrderSendService instOrderSendService;

    @Resource
    private ControlOrderSendService controlOrderSendService;

    @Resource
    protected ConfigurationService configurationService;

    @Override
    public InstOrderResult process(InstOrder instOrder) {
        try {
            configurationService.queryCardToken(instOrder.getPayMode(), instOrder.getExtension());
            ChannelFundResult result = instOrderSendService.send(instOrder);
            return instResultProcessor.process(instOrder, result);
        } finally {
            TokenHolder.clear();
        }
    }

    @Override
    public InstControlOrderResult process(InstControlOrder instControlOrder) {
        ChannelResult result = controlOrderSendService.send(instControlOrder);
        return controlResultProcessor.process(instControlOrder, result);
    }

}
