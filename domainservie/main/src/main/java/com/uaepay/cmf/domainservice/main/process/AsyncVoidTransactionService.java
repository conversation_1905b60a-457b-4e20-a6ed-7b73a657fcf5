package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.schema.cmf.common.Result;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/6/28
 */
public interface AsyncVoidTransactionService {

    /**
     * 控制交易撤销
     * @param order
     * @param instControlResult
     */
    Result<?> processControlVoidTx(InstControlOrder order, InstControlOrderResult instControlResult);

}
