package com.uaepay.cmf.domainservice.channel;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;

import java.util.List;

/**
 * <p><b>资金渠道manager</b></p>
 * 查询路由规则<br>
 * 查询路由规则相应可用的渠道<br>
 * 查询路由规则相关的过滤条件<br>
 * 查询渠道选择要素<br>
 * <p>此Manager主要对渠道路由提供交易相关的服务，资金渠道配置相关的配置管理可以由其他manager实现</p>
 *
 * <AUTHOR>
 * @version $Id: FundChannelManager.java, v 0.1 2011-2-22 下午06:11:27 liumaoli Exp $
 */
public interface FundChannelManager extends BasicConstant {


    /**
     * 根据资金渠道编号查询资金渠道
     *
     * @param fundChannelCode
     * @return
     */
    List<ChannelVO> getFundChannel(String fundChannelCode, FundChannelApiType... apiTypes);

    /**
     * 根据接口类型获取渠道列表
     *
     * @param apiType
     * @return
     */
    List<ChannelVO> loadByApiType(FundChannelApiType apiType);

    /**
     * 根据接口获取渠道列表
     *
     * @param apiTypes
     * @return
     */
    List<ChannelVO> loadByApiTypes(FundChannelApiType... apiTypes);

}
