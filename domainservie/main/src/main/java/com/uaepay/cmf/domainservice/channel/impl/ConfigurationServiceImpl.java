package com.uaepay.cmf.domainservice.channel.impl;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.TokenTypeEnum;
import com.uaepay.cmf.common.core.domain.exception.CmfIllegalArgumentException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.ma.MemberChannelToken;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.domain.vo.TransformInfo;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigureKey;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.domainservice.channel.holder.TokenHolder;
import com.uaepay.cmf.domainservice.channel.router.ConfigurationService;
import com.uaepay.cmf.domainservice.main.domain.InnerFundoutTypeEnum;
import com.uaepay.cmf.domainservice.main.pattern.CardTokenService;
import com.uaepay.cmf.fss.ext.integration.config.RemittanceConfig;
import com.uaepay.cmf.fss.ext.integration.config.TransformConfig;
import com.uaepay.cmf.fss.ext.integration.ma.MemberClient;
import com.uaepay.common.util.money.Money;
import com.uaepay.payment.common.v2.enums.PayMode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("configurationService")
public class ConfigurationServiceImpl implements ConfigurationService, SysConfigureKey {

    protected static final Logger logger = LoggerFactory
            .getLogger(ConfigurationServiceImpl.class);

    @Resource
    private SysConfigurationHolder sysConfigurationHolder;
    @Resource
    private CardTokenService cardTokenService;
    @Resource
    private MemberClient memberClient;
    @Resource
    private TransformConfig transformConfig;
    @Resource
    private RemittanceConfig remittanceConfig;


    @Override
    public void beforeRoute(CmfOrder cmfOrder) {
        if (cmfOrder == null) {
            return;
        }
        // 去MA查询该卡已签约的渠道
        TokenTypeEnum tokenType = queryCardToken(cmfOrder.getPayMode(), cmfOrder);
        if (tokenType != null && !TokenTypeEnum.ANNI.equals(tokenType)) {
            // 此处修改了paymode
            cmfOrder.setPayMode(tokenType.getPayMode());
        }
        // 若为外币卡汇款，则使用外币金额替换aed金额
        foreignRemittanceProcess(cmfOrder);
    }

    private void foreignRemittanceProcess(CmfOrder cmfOrder) {
        String innerFundoutType = cmfOrder.getExtension().get(ExtensionKey.INNER_FUNDOUT_TYPE.getKey());
        if (!InnerFundoutTypeEnum.isRemittance(innerFundoutType)) {
            return;
        }
        String targetAmount = cmfOrder.getExtension().get(ExtensionKey.TARGET_AMOUNT.getKey());
        String targetCurrency = cmfOrder.getExtension().get(ExtensionKey.TARGET_CURRENCY.getKey());
        if (StringUtils.isEmpty(targetAmount) || StringUtils.isEmpty(targetCurrency)) {
            throw new CmfIllegalArgumentException("目标金额或币种不能为空!");
        }
        cmfOrder.getExtension().put(ExtensionKey.ORI_AMOUNT.getKey(), cmfOrder.getAmount().getAmount().toPlainString());
        cmfOrder.getExtension().put(ExtensionKey.ORI_CURRENCY.getKey(), cmfOrder.getAmount().getCurrency());
        cmfOrder.setAmount(new Money(targetAmount, targetCurrency));
    }


    public TokenTypeEnum queryCardToken(PayMode payMode, CmfOrder cmfOrder) {

        Map<String, String> extMap = cmfOrder.getExtension();

        String cardTokenId = extMap.get(ExtensionKey.CARD_TOKEN.key);
        // 把可走渠道放到thread local中
        CardToken cardToken = cardTokenService.queryToken(cardTokenId);
        if (cardToken != null) {
            if (StringUtils.isNotEmpty(cardToken.getInstCode()) && StringUtils.isEmpty(cmfOrder.getInstCode())) {
                cmfOrder.setInstCode(cardToken.getInstCode());
            }
        }
        Map<String, Object> dataMap = prepareTokenMap(cardToken, payMode, cmfOrder.getExtension());

        TokenHolder.set(dataMap);
        logger.info("Current Card Signed Channels:{}", dataMap);
        return cardToken != null ? cardToken.getTokenType() : null;
    }

    @Override
    public void beforeRoute(InstControlOrder controlOrder) {
        if (needNotCheck(controlOrder)) {
            return;
        }
        String cardTokenId = controlOrder.getExtension().get(ExtensionKey.CARD_TOKEN.key);
        CardToken cardToken = cardTokenService.queryToken(cardTokenId);
        if (StringUtils.isEmpty(controlOrder.getInstCode()) && cardToken != null && StringUtils.isNotEmpty(cardToken.getInstCode())) {
            controlOrder.setInstCode(cardToken.getInstCode());
        }

        Map<String, Object> dataMap = prepareTokenMap(cardToken, controlOrder.getPayMode(), controlOrder.getExtension());
        TokenHolder.set(dataMap);
        logger.info("Current Card Signed Channels:{}", dataMap);
    }

    private boolean needNotCheck(InstControlOrder controlOrder) {
        return controlOrder == null || controlOrder.getExtension() == null || controlOrder.getRequestType() == ControlRequestType.ADVANCE;
    }

    @Override
    public TokenTypeEnum queryCardToken(PayMode payMode, Map<String, String> extMap) {

        String cardTokenId = extMap.get(ExtensionKey.CARD_TOKEN.key);
        if (StringUtils.isEmpty(cardTokenId)) {
            return null;
        }
        // 把可走渠道放到thread local中
        CardToken cardToken = cardTokenService.queryToken(cardTokenId);
        Map<String, Object> dataMap = prepareTokenMap(cardToken, payMode, extMap);
        TokenHolder.set(dataMap);
        logger.info("Current Card Signed Channels:{}", dataMap);
        return cardToken != null ? cardToken.getTokenType() : null;
    }

    @Override
    public boolean isSvaTransformOrder(InstOrder instOrder) {
        TransformInfo info = TransformInfo.builder()
                .fundChannelCode(instOrder.getFundChannelCode())
                .bizProductCode(instOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.getKey()))
                .merchantId(instOrder.getExtension().get(ExtensionKey.TOPAY_MERCHANT_ID.getKey()))
                .dbcr(instOrder.getExtension().get(ExtensionKey.DBCR.getKey()))
                .cardType(instOrder.getExtension().get(ExtensionKey.CARD_TYPE_UNDERLINE.getKey()))
                .build();
        return instOrder.getBizType().isFundInRefund() &&
                instOrder.getStatus() == InstOrderStatus.SUCCESSFUL &&
                (transformConfig.isTransform(info)
                        || remittanceConfig.isTransform(info));
    }

    private Map<String, Object> prepareTokenMap(CardToken cardToken, PayMode payMode, Map<String, String> extMap) {
        Map<String, Object> dataMap = new HashMap<>(extMap.size());
        if (cardToken != null) {
            putExt(extMap, cardToken.getCompanyOrPersonal(), ExtensionKey.COMPANY_OR_PERSONAL);
            putExt(extMap, cardToken.getIs3DS(), ExtensionKey.IS_3DS);
            putExt(extMap, cardToken.getDbcr(), ExtensionKey.DBCR);
            putExt(extMap, cardToken.getCompanyOrPersonal(), ExtensionKey.COMPANY_OR_PERSONAL);
            putExt(extMap, cardToken.getNeedCsc(), ExtensionKey.NEED_CSC);
            putExt(extMap, cardToken.getCardBrand(), ExtensionKey.CARD_BRAND);
            putExt(extMap, cardToken.getCardType(), ExtensionKey.CARD_TYPE_UNDERLINE);

            if (payMode == PayMode.QUICKPAY || payMode == PayMode.TOKENPAY) {
                putExt(extMap, cardToken.getCardNo(), ExtensionKey.CARD_NO);
                putExt(extMap, cardToken.getCardHolder(), ExtensionKey.CARD_HOLDER);
                putExt(extMap, cardToken.getCountryCode(), ExtensionKey.COUNTRY_CODE);
                putExt(extMap, cardToken.getCardExpired(), ExtensionKey.EXPIRED_DATE);
                putExt(extMap, cardToken.getIpAddress(), ExtensionKey.IP_ADDRESS);
                putExt(extMap, cardToken.getMemberId(), ExtensionKey.MEMBER_ID);
            }

            if (payMode == PayMode.BALANCE) {
                putExt(extMap, cardToken.getIban(), ExtensionKey.IBAN);
                putExt(extMap, cardToken.getCardHolder(), ExtensionKey.ACCOUNT_NAME);
                putExt(extMap, cardToken.getCardAccountNo(), ExtensionKey.CARD_ACCOUNT_NO);
            }

            dataMap.put(ExtensionKey.CARD_TOKEN.key, cardToken);
            List<MemberChannelToken> tokens = memberClient.queryTokens(cardToken.getCardId());
            if (CollectionUtils.isNotEmpty(tokens)) {
                dataMap.put(ExtensionKey.MEMBER_TOKENS.key, convertToken(tokens));
            }
            // 扩展参数
            if (StringUtils.isNotEmpty(cardToken.getExtension())) {
                Map<String, String> tokenMap = MapUtil.jsonToMap(cardToken.getExtension());
                for (Map.Entry<String, String> entry : tokenMap.entrySet()) {
                    if (StringUtils.isNotEmpty(entry.getValue()) && StringUtils.isEmpty(extMap.get(entry.getKey()))) {
                        extMap.put(entry.getKey(), entry.getValue());
                    }
                }
            }
        }
        return dataMap;
    }


    private void putExt(Map<String, String> extMap, String value, ExtensionKey key) {
        if (StringUtils.isNotEmpty(value) && StringUtils.isEmpty(extMap.get(key.key))) {
            extMap.put(key.key, value);
        }
    }

    private Object convertToken(List<MemberChannelToken> tokens) {
        List<MemberChannelToken> convertedTokens = new ArrayList<>(tokens.size() + 1);
        for (MemberChannelToken token : tokens) {
            convertedTokens.add(token);
            Map<String, String> reflectMap = MapUtil.jsonToMap(sysConfigurationHolder.loadConfigureOrDefault(SysConfigureKey.CHANNEL_TOKEN_REFLECT, "{}"));
            if (reflectMap.containsKey(token.getFundChannelCode())) {
                for (String channel : reflectMap.get(token.getFundChannelCode()).split(CHAR_COMMA)) {
                    MemberChannelToken tt = new MemberChannelToken();
                    tt.setFundChannelCode(channel);
                    tt.setToken(token.getToken());
                    convertedTokens.add(tt);
                }
            }
        }
        return convertedTokens;
    }

}
