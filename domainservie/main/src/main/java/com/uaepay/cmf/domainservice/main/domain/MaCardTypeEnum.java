package com.uaepay.cmf.domainservice.main.domain;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date maCardTypeEnum.java v1.0  11/25/20 8:26 PM
 */
public enum MaCardTypeEnum {

    // ma card type
    // 卡类型(1借记卡 2信用卡,3存折,4其它)
    DC(1, "借记"),
    CC(2, "贷记"),
    PB(3, "存折"),
    OT(4, "其他");

    private int code;
    private String description;

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    MaCardTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static MaCardTypeEnum getByCode(int code) {
        for (MaCardTypeEnum cardType : values()) {
            if (cardType.getCode() == code) {
                return cardType;
            }
        }
        return null;
    }

}
