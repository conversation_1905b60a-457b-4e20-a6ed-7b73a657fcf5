package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.domainservice.main.convert.ChannelResultConverter;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.schema.cmf.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>PreAuthCancelResultLaterProcessor</p>
 *
 * <AUTHOR>
 * @version PreAuthCancelResultLaterProcessor.java v1.0  2022/10/11 17:53
 */
@Slf4j
@Component
public class PreAuthVoidResultLaterProcessor implements ResultLaterProcessor {

    @Resource
    private InstOrderRepository instOrderRepository;
    @Resource
    private InstResultProcessor instResultProcessor;

    @PostConstruct
    public void init() {
        ResultLaterProcessorEnum.PREAUTH_VOID.register(this);
    }

    @Override
    public Result<?> process(InstControlOrder instControlOrder, InstControlOrderResult instControlResult) {
        if (InstOrderResultStatus.SUCCESSFUL != instControlResult.getStatus()) {
            return Result.ofNothing();
        }
        log.info("预授权订单撤销处理:{},控制单结果:{}", instControlOrder, instControlResult);

        InstOrder instOrder = instOrderRepository.loadByNo(instControlOrder.getPreInstOrderNo());

        //更新为撤销
        instOrderRepository.updateInstOrderStatus(instOrder, InstOrderStatus.CANCEL);

        InstOrderResult instResult = ChannelResultConverter.convert(instControlResult, instOrder);

        Assert.isTrue(instResult.getStatus() ==InstOrderResultStatus.FAILURE,"撤销状态有误");


        //更新订单为失败

        // 4. 更新机构订单状态，保存扩展信息
        instResultProcessor.updateOrderStatusAndExt(instOrder, instResult);

        // 5. 更新cmf订单状态
        instResultProcessor.updateCmfOrderStatus(instOrder);

        return Result.ofSuccess();
    }
}
