package com.uaepay.cmf.domainservice.main.repository;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import com.uaepay.cmf.service.facade.domain.grc.Query3dsRequest;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date Notify3dsResultRepository.java v1.0  2020-10-18 00:05
 */
public interface Notify3dsResultRepository extends BasicConstant {


    long store(Notify3dsResult notify3dsResult);

    Notify3dsResult queryBy3dsResultId(Long notify3dsResultId);

    Notify3dsResult queryBy3dsResult(String productOrderNo);

    Notify3dsResult queryBy3dsResult(Query3dsRequest request);
}
