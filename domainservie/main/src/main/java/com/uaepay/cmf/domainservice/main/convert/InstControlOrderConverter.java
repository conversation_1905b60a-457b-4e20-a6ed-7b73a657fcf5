package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.dal.dataobject.ControlOrderDO;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.service.facade.domain.query.order.ControlOrderVO;
import com.uaepay.payment.common.v2.enums.PayMode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

/**
 * <p>机构控制订单转换器</p>
 *
 * <AUTHOR>
 * @version $Id: InstControlOrderConverter.java, v 0.1 2012-8-20 上午10:28:35 fuyangbiao Exp $
 */
public class InstControlOrderConverter {
    /**
     * DOMAIN转换为DO
     *
     * @param order
     * @return
     */
    public static ControlOrderDO convert(InstControlOrder order) {
        if (order == null) {
            return null;
        }
        ControlOrderDO orderDO = new ControlOrderDO();
        BeanUtils.copyProperties(order, orderDO);
        orderDO.setOrderId(order.getOrderId());
        orderDO.setRequestNo(order.getRequestNo());
        orderDO.setPreRequestNo(order.getPreRequestNo());
        orderDO.setApiType(order.getApiType().getCode());
        orderDO.setFundChannelCode(order.getFundChannelCode());
        if (order.getNotifyStatus() != null) {
            orderDO.setNotifyStatus(order.getNotifyStatus().getCode());
        }
        if (order.getSourceCode() != null) {
            orderDO.setSourceCode(order.getSourceCode().getCode());
        }
        orderDO.setCommunicateStatus(order.getCommunicateStatus().getCode());
        orderDO.setFlag(order.getFlag().getCode());
        orderDO.setAmount(order.getAmount());
        orderDO.setInstCode(order.getInstCode());
        orderDO.setInstOrderNo(order.getInstOrderNo());
        if (order.getPayMode() != null) {
            orderDO.setPayMode(order.getPayMode().getCode());
        }
        orderDO.setProductCode(order.getProductCode());
        if(order.getRequestType()!=null) {
            orderDO.setRequestType(order.getRequestType().getCode());
        }
        orderDO.setStatus(order.getStatus().getCode());
        if (order.getExtension() != null) {
            orderDO.setExtension(CommonConverter.convertToDb(order.getExtension()));
        }
        orderDO.setGmtCreate(order.getGmtCreate());
        orderDO.setGmtNextRetry(order.getGmtNextRetry());
        Integer retryTimes = (order.getRetryTimes() == null) ? 0 : order.getRetryTimes();
        orderDO.setRetryTimes(retryTimes);
        orderDO.setGmtModified(order.getGmtModified());
        orderDO.setMemo(order.getMemo());
        orderDO.setMerchantId(order.getMerchantId());

        return orderDO;
    }

    /**
     * 根据DO转换为DOMAIN
     *
     * @param orderDO
     * @return
     */
    public static InstControlOrder convert(ControlOrderDO orderDO) {
        InstControlOrder order = new InstControlOrder();
        order.setOrderId(orderDO.getOrderId());
        order.setRequestNo(orderDO.getRequestNo());
        order.setPreRequestNo(orderDO.getPreRequestNo());
        order.setApiType(FundChannelApiType.getByCode(orderDO.getApiType()));
        order.setFundChannelCode(orderDO.getFundChannelCode());
        order.setAmount(orderDO.getAmount());
        order.setInstCode(orderDO.getInstCode());
        order.setInstOrderNo(orderDO.getInstOrderNo());
        order.setPreInstOrderNo(orderDO.getPreRequestNo());
        order.setPayMode(PayMode.getByCode(orderDO.getPayMode()));
        order.setProductCode(orderDO.getProductCode());
        order.setRequestType(ControlRequestType.getByCode(orderDO.getRequestType()));
        order.setStatus(InstOrderStatus.getByCode(orderDO.getStatus()));
        order.setGmtNextRetry(orderDO.getGmtNextRetry());
        if (StringUtils.isNotEmpty(orderDO.getExtension())) {
            order.setExtension(CommonConverter.convertFromDb(orderDO.getExtension()));
            if (StringUtils
                    .isNotEmpty(order.getExtension().get(ExtensionKey.ORGI_SETTLEMENT_ID.key))
                    && !"null".equals(order.getExtension().get(ExtensionKey.ORGI_SETTLEMENT_ID.key))) {
                order.setPreSettlementId(order.getExtension().get(
                        ExtensionKey.ORGI_SETTLEMENT_ID.key));
            }
        }
        order.setGmtCreate(orderDO.getGmtCreate());
        order.setGmtModified(orderDO.getGmtModified());
        order.setRetryTimes(orderDO.getRetryTimes());
        order.setMemo(orderDO.getMemo());
        order.setMerchantId(orderDO.getMerchantId());
        order.setNotifyStatus(NotifyStatus.getByCode(orderDO.getNotifyStatus()));
        order.setSourceCode(SourceCode.getByCode(orderDO.getSourceCode()));
        order.setCommunicateStatus(CommunicateStatus.getByCode(orderDO.getCommunicateStatus()));
        order.setFlag(OrderFlag.getByCode(orderDO.getFlag()));
        order.setInstOrderType(InstOrderType.CONTROL);


        return order;
    }

    public static ControlOrderVO convert2VO(InstControlOrder controlOrder){
        ControlOrderVO vo = new ControlOrderVO();
        BeanUtils.copyProperties(controlOrder, vo);
        if(controlOrder.getRequestType()!=null){
            vo.setRequestType(controlOrder.getRequestType().getCode());
        }
        if(controlOrder.getPayMode()!=null){
            vo.setPayMode(controlOrder.getPayMode().getCode());
        }
        if(controlOrder.getApiType()!=null){
            vo.setApiType(controlOrder.getApiType().getCode());
        }
        if(controlOrder.getStatus()!=null){
            vo.setStatus(controlOrder.getStatus().getCode());
        }
        if(controlOrder.getNotifyStatus()!=null){
            vo.setNotifyStatus(controlOrder.getNotifyStatus().getCode());
        }
        return vo;
    }

    public static InstControlOrder convert(InstBaseOrder instOrder, ControlRequestType requestType, FundChannelApiType apiType) {
        InstControlOrder controlOrder = new InstControlOrder();
        BeanUtils.copyProperties(instOrder, controlOrder);
        controlOrder.setRequestType(requestType);
        controlOrder.setApiType(apiType);
        controlOrder.setRetryTimes(0);
        controlOrder.setStatus(InstOrderStatus.IN_PROCESS);
        controlOrder.setNotifyStatus(NotifyStatus.AWAITING);
        controlOrder.setPreInstOrderNo(instOrder.getInstOrderNo());
        controlOrder.setPreRequestNo(instOrder.getInstOrderNo());
        controlOrder.getExtension().put(ExtensionKey.WHITE_CHANNEL_CODE.getKey(), instOrder.getFundChannelCode());
        controlOrder.getExtension().put(ExtensionKey.INST_ORDER_TYPE.getKey(), instOrder instanceof InstOrder ? InstOrderType.FUND.name(): InstOrderType.CONTROL.name());
        return controlOrder;
    }
}
