package com.uaepay.cmf.domainservice.main.repository.impl;

import com.uaepay.cmf.common.core.dal.daointerface.Notify3dsResultDAO;
import com.uaepay.cmf.common.core.dal.dataobject.Notify3dsResultDO;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator;
import com.uaepay.cmf.common.core.engine.generator.SequenceNameEnum;
import com.uaepay.cmf.domainservice.main.pattern.CardTokenService;
import com.uaepay.cmf.domainservice.main.repository.CardTokenRepository;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.Notify3dsResultRepository;
import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import com.uaepay.cmf.service.facade.domain.grc.Query3dsRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date Notify3dsResultRepositoryImpl.java v1.0  2020-10-18 00:06
 */
@Slf4j
@Service
public class Notify3dsResultRepositoryImpl implements Notify3dsResultRepository {

    @Resource
    private Notify3dsResultDAO notify3dsResultDAO;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private CmfOrderRepository cmfOrderRepository;

    @Resource
    private CardTokenRepository cardTokenRepository;

    @Resource
    private PrimaryKeyGenerator primaryKeyGenerator;

    @Resource
    private CardTokenService cardTokenService;

    @Override
    @Transactional(rollbackFor = {Throwable.class})
    public long store(Notify3dsResult notify3dsResult) {
        InstOrder instOrder = instOrderRepository.loadByNo(notify3dsResult.getInstOrderNo());
        if (instOrder == null) {
            return 0;
        }
        CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(instOrder.getCmfSeqNo(), false);


        long resultId = notify3dsResultDAO.insert(convert2DO(notify3dsResult, instOrder, cmfOrder));

        cardTokenService.updateCardToken(instOrder, notify3dsResult);
        return resultId;
    }

    @Override
    public Notify3dsResult queryBy3dsResultId(Long notify3dsResultId) {
        Notify3dsResultDO resultDO = notify3dsResultDAO.selectByPrimaryKey(notify3dsResultId);
        return build3dsResultVO(resultDO);
    }


    @Override
    public Notify3dsResult queryBy3dsResult(String instOrderNo) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("instOrderNo", instOrderNo);
        Notify3dsResultDO resultDO = notify3dsResultDAO.selectByCondition(paramMap);
        return build3dsResultVO(resultDO);
    }

    @Override
    public Notify3dsResult queryBy3dsResult(Query3dsRequest request) {
        Map<String, String> paramMap = new HashMap<>(5);
        paramMap.put("productOrderNo", request.getProductOrderNo());
        paramMap.put("paymentOrderNo", request.getPaymentOrderNo());
        paramMap.put("instOrderNo", request.getInstOrderNo());
        Notify3dsResultDO resultDO = notify3dsResultDAO.selectByCondition(paramMap);
        return build3dsResultVO(resultDO);
    }

    private Notify3dsResult build3dsResultVO(Notify3dsResultDO resultDO) {
        if (resultDO == null) {
            return null;
        }
        Notify3dsResult result = new Notify3dsResult();
        BeanUtils.copyProperties(resultDO, result);
        CardToken cardToken = cardTokenRepository.query("" + resultDO.getCardTokenId());
        result.setCardHolder(cardToken.getCardHolder());
        if (StringUtils.isNotEmpty(cardToken.getCardNo()) && (cardToken.getCardNo().contains(STAR)||cardToken.getCardNo().contains(X))) {
            result.setCardNoMask(cardToken.getCardNo());
        } else {
            result.setCardNo(cardToken.getCardNo());
        }
        result.setBankCode(cardToken.getIssueBank());
        result.setBankName(cardToken.getIssueBankName());
        result.setCardType(cardToken.getCardType());
        result.setCardBrand(cardToken.getCardBrand());
        result.setCardExpire(cardToken.getCardExpired());
        return result;
    }


    private Notify3dsResultDO convert2DO(Notify3dsResult notify3dsResult, InstOrder instOrder, CmfOrder cmfOrder) {
        Notify3dsResultDO resultDO = new Notify3dsResultDO();
        BeanUtils.copyProperties(notify3dsResult, resultDO);

        Long resultId = Long.valueOf(primaryKeyGenerator.generateKey(SequenceNameEnum.NOTIFY_3DS_RESULT));
        resultDO.setResultId(resultId);
        String cardTokenId = instOrder.getExtension().get(ExtensionKey.CARD_TOKEN.key);
        if (StringUtils.isNotEmpty(cardTokenId) && StringUtils.isNumeric(cardTokenId)) {
            resultDO.setCardTokenId(Long.valueOf(cardTokenId));
        }
        resultDO.setTradeOrderNo(cmfOrder.getExtension().get(ExtensionKey.TRADE_VOUCHER_NOS.key));
        resultDO.setPaymentOrderNo(cmfOrder.getPaymentVoucherNo());
        resultDO.setGmtCmfRequest(instOrder.getGmtBookingSubmit());
        return resultDO;
    }
}
