package com.uaepay.cmf.domainservice.main.convert;

import com.google.common.collect.*;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.fss.ext.integration.enums.ChannelRefundStatus;
import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.cmf.service.facade.result.CmfFundResult;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import com.uaepay.common.domain.Extension;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * <p>机构订单结果-->CMF处理结果,  转换类</p>
 *
 * <AUTHOR> won
 * @version $Id: CmfFundResultConverterImpl.java, v 0.1 2011-4-2 下午03:33:34 sean won Exp $
 */
public class CmfFundResultConverter {
    private static final Logger logger = LoggerFactory.getLogger(CmfFundResultConverter.class);

    private static final Map<ApplyStatusEnum, List<CmfFundResultCode>> applyStatusMapping = ImmutableMap.of(
            ApplyStatusEnum.SUCCESS, Lists.newArrayList(CmfFundResultCode.SUCCESS),
            ApplyStatusEnum.ERROR, Lists.newArrayList(CmfFundResultCode.REQUEST_SUCCESS,CmfFundResultCode.SUBMIT_INST,CmfFundResultCode.IN_PROCESS,CmfFundResultCode.REFUND_MANUAL,CmfFundResultCode.UNKNOW_EXCEPTION,CmfFundResultCode.ORDER_QUERY_ERROR),
            ApplyStatusEnum.FAIL, Lists.newArrayList(CmfFundResultCode.FAILED,CmfFundResultCode.CHANNEL_NOT_ACCESS,CmfFundResultCode.ORDER_NOT_EXIST,CmfFundResultCode.PARAMETER_INVALID)
    );

    public static CmfFundResult convert(CmfOrder cmfOrder, InstOrder instOrder, InstOrderResult from) {
        return convert(cmfOrder, instOrder, from, null);
    }

    public static CmfFundResult convert(CmfOrder cmfOrder, InstOrder instOrder, InstOrderResult from, CardToken cardToken) {
        CmfFundResult to = new CmfFundResult();
        if (BizType.FUNDIN.equals(from.getBizType())) {
            populateFundinResult(from, to, instOrder, cmfOrder, cardToken);
        }
        boolean needBuildFinalResult = true;
        if (BizType.REFUND.equals(from.getBizType())) {
            //由PE来判断是否0005；CMF需要返回0001 + channelPayNo； 或者0004 + channelPayNo
            populateReFundResult(from, to, instOrder);
            // 需要返回PE：渠道编号，channelPayNo
            buildFinalResult(from, to, instOrder, cmfOrder);
            needBuildFinalResult = false;
        }

        if (BizType.FUNDOUT.equals(from.getBizType())) {
            populateFundoutResult(from, to, instOrder, cmfOrder);
        }

        //明确结果的，须把渠道编号、金额等信息返回给PE
        if (needBuildFinalResult && InstOrderProcessStatus.SUCCESS == from.getProcessStatus()) {
            buildFinalResult(from, to, instOrder, cmfOrder);
        }



        if(to.getExtension() ==null){
            to.setExtension(new Extension());
        }

        String payLoad = from.getExtension().get(ExtensionKey.PAYLOAD.key);
        if(StringUtils.isNotEmpty(payLoad)){
            to.getExtension().add(ExtensionKey.PAYLOAD.key,payLoad);
        }

        if (StringUtils.isNotEmpty(from.getInstResultCode())){
            to.setUnityResultCode(from.getInstResultCode());
        }
        to.getExtension().add(ExtensionKey.PAYMENT_SEQ_NO.key, cmfOrder.getPaymentSeqNo());

        // 通过resultCode设置applyStatus
        repairStatus(to);
        return to;
    }

    /**
     * 出款结果.
     *
     * @param from
     * @param to
     */
    private static void populateFundoutResult(InstOrderResult from, CmfFundResult to,
                                              InstOrder instOrder, CmfOrder cmfOrder) {

        if (InstOrderProcessStatus.SUBMIT_CMF_FAIL.equals(from.getProcessStatus())) {
            //如果保存cmf订单失败,明确失败
            to.setSuccess(false);
            to.setResultCode(CmfFundResultCode.FAILED);
        } else if (InstOrderResultStatus.FAILURE == from.getStatus()
                && InstOrderProcessStatus.SUCCESS == from.getProcessStatus()) {
            //如果机构订单结果是：失败,则明确失败
            to.setSuccess(false);
            to.setResultCode(CmfFundResultCode.FAILED);
        } else if (InstOrderProcessStatus.SUBMIT_INST_SUCCESS == from.getProcessStatus()) {
            //提交机构成功，并且已明确出款渠道，须返回渠道编码及channelPayNo给PE
            to.setSuccess(true);
            to.setResultCode(CmfFundResultCode.SUBMIT_INST);
            buildFinalResult(from, to, instOrder, cmfOrder);
        } else if (InstOrderProcessStatus.SUCCESS == from.getProcessStatus()) {
            //提交机构成功，并且已明确出款渠道，须返回渠道编码及channelPayNo给PE
            to.setSuccess(true);
            if (InstOrderResultStatus.SUCCESSFUL.equals(from.getStatus())
                    && CmfOrderStatus.SUCCESSFUL.equals(cmfOrder.getStatus())) { //防止拆分,保证结果一致
                to.setResultCode(CmfFundResultCode.SUCCESS);
            } else if (InstOrderResultStatus.FAILURE.equals(from.getStatus())
                    && CmfOrderStatus.FAILURE.equals(cmfOrder.getStatus())) { //防止拆分,保证结果一致
                to.setResultCode(CmfFundResultCode.FAILED);
            } else {
                //其它情况都为请求成功
                to.setSuccess(true);
                to.setResultCode(CmfFundResultCode.REQUEST_SUCCESS);
            }
            buildFinalResult(from, to, instOrder, cmfOrder);
        } else {
            //其它情况都为请求成功
            to.setSuccess(true);
            to.setResultCode(CmfFundResultCode.REQUEST_SUCCESS);
            if (instOrder != null) {
                to.setFundsChannel(instOrder.getFundChannelCode());
                to.setInstOrderNo(instOrder.getInstOrderNo());
            }
        }
        to.setResultMessage(from.getMemo());
        if (to.getExtension() == null) {
            to.setExtension(new Extension());
        }
        String bankFormKey = from.getExtension().get(ExtensionKey.BANK_FORM_KEY.key);
        if (!StringUtils.isEmpty(bankFormKey)) {
            to.getExtension().add(ExtensionKey.BANK_FORM_KEY.key, bankFormKey);
        }
    }

    /**
     * 入款结果.
     *
     * @param from
     * @param to
     */
    private static void populateFundinResult(InstOrderResult from, CmfFundResult to,
                                             InstOrder instOrder, CmfOrder cmfOrder, CardToken cardToken) {
        if (InstOrderProcessStatus.SUCCESS.equals(from.getProcessStatus())) { //处理成功
            to.setSuccess(true);

            //明确成功  or 明确失败, 风险订单 则是处理中,其它默认处理中
            CmfFundResultCode resultCode;
            if (InstOrderResultStatus.SUCCESSFUL.equals(from.getStatus())
                    && CmfOrderStatus.SUCCESSFUL.equals(cmfOrder.getStatus())) {
                resultCode = CmfFundResultCode.SUCCESS;
            } else if (InstOrderResultStatus.HALF_SUCCESSFUL.equals(from.getStatus())){
                resultCode = CmfFundResultCode.SUCCESS;
            } else if (InstOrderResultStatus.RISK.equals(from.getStatus())) {
                resultCode = CmfFundResultCode.REQUEST_SUCCESS;
            } else if (InstOrderResultStatus.FAILURE.equals(from.getStatus())
                    && (CmfOrderStatus.FAILURE.equals(cmfOrder.getStatus()) || CmfOrderStatus.CANCEL
                    .equals(cmfOrder.getStatus()))) {
                resultCode = CmfFundResultCode.FAILED;
            } else {
                resultCode = CmfFundResultCode.REQUEST_SUCCESS;
            }
            String paymentNo = from.getExtension().get(ExtensionKey.PAYMENT_SEQ_NO.key);
            if (!StringUtils.isEmpty(paymentNo)) {
                //无磁无密判断风险订单逻辑不同
                if (CmfOrderStatus.IN_PROCESS.equals(cmfOrder.getStatus())) {
                    resultCode = CmfFundResultCode.REQUEST_SUCCESS;
                }
            }
            to.setResultCode(resultCode);
        } else if (InstOrderProcessStatus.AWAITING.equals(from.getProcessStatus())) { //提交成功，待返回(异步)
            to.setSuccess(true);
            to.setResultCode(CmfFundResultCode.REQUEST_SUCCESS);
            if (instOrder != null) {
                to.setFundsChannel(instOrder.getFundChannelCode());
                to.setInstOrderNo(instOrder.getInstOrderNo());
            }
        } else if (InstOrderProcessStatus.FAILURE.equals(from.getProcessStatus())) {
            to.setSuccess(false);

            //明确失败  or 正在处理该订单(包括CMF的补单任务)
            CmfFundResultCode resultCode = (InstOrderResultStatus.FAILURE.equals(from.getStatus())) ? CmfFundResultCode.FAILED
                    : CmfFundResultCode.IN_PROCESS;
            to.setResultCode(resultCode);
        } else if (InstOrderProcessStatus.SUBMIT_CMF_FAIL.equals(from.getProcessStatus())) {
            to.setSuccess(false);
            //如果保存cmf订单失败,明确失败
            CmfFundResultCode resultCode = CmfFundResultCode.FAILED;
            to.setResultCode(resultCode);
        } else {
            to.setSuccess(false);
            //返回给PE的是处理中状态，不能是未知异常(未知异常213会导致PE后续的状态跃迁异常)。
            to.setResultCode(CmfFundResultCode.IN_PROCESS);
        }

        to.setResultMessage(from.getMemo());
        to.setExtension(CommonConverter.convertExtensionWithoutConvertKey(from.getExtension()));
        to.getExtension().add(ExtensionKey.INST_SEQ_NO.key, from.getInstSeqNo());

        //B2C验签Form返回给PE.
        String signForm = from.getExtension().get(ExtensionKey.PAGE_URL_FOR_SIGN.key);
        if (!StringUtils.isEmpty(signForm)) {
            to.getExtension().add(ExtensionKey.PAGE_URL_FOR_SIGN.key, signForm);
        } else if (cardToken != null) {
            String url = cardToken.getResultUrl();
            to.getExtension().add(ExtensionKey.PAGE_URL_FOR_SIGN.key, url);
        }
        String bankFormKey = from.getExtension().get(ExtensionKey.BANK_FORM_KEY.key);
        if (!StringUtils.isEmpty(bankFormKey)) {
            to.getExtension().add(ExtensionKey.BANK_FORM_KEY.key, bankFormKey);
        }

        //风控特殊逻辑处理
        if (instOrder != null && OrderRiskStatus.IN_PROCESS.equals(instOrder.getRiskStatus())) {
            to.getExtension().add(ExtensionKey.IS_BANK_RISK.key, YesNo.YES.getCode());
        }
        if (instOrder != null) {
            to.setInstOrderNo(instOrder.getInstOrderNo());
        }

    }

    private static void repairStatus(CmfFundResult cmfFundResult){

        // -- 0906 修改，针对订单为I 但是仍要返回给前端成功处理
        String unityResultCode = cmfFundResult.getUnityResultCode();
        if(applyStatusMapping.get(ApplyStatusEnum.SUCCESS).contains(cmfFundResult.getResultCode())
                || StringUtils.equals(BasicConstant.SUCCESS_MSG,unityResultCode)
        ){
            cmfFundResult.setApplyStatus(ApplyStatusEnum.SUCCESS);
        }else if(applyStatusMapping.get(ApplyStatusEnum.FAIL).contains(cmfFundResult.getResultCode())){
            cmfFundResult.setApplyStatus(ApplyStatusEnum.FAIL);
        }else {
            cmfFundResult.setApplyStatus(ApplyStatusEnum.ERROR);
        }
    }

    /**
     * 对于有明确结果的交易，须回传PE渠道编号、金额等信息.
     *
     * @param from
     * @param to
     */
    private static void buildFinalResult(InstOrderResult from, CmfFundResult to,
                                         InstOrder instOrder, CmfOrder cmfOrder) {
        //渠道生成的订单号(发送给银行的)
        if (instOrder != null) {
            to.setInstOrderNo(instOrder.getInstOrderNo());
        }
        if (to.getResultCode() != null && StringUtils.isEmpty(to.getResultMessage())) {
            to.setResultMessage(to.getResultCode().getMessage());
        }

        to.setAmount(cmfOrder.getAmount());

        //支付时间
        to.setInstPayTime(Calendar.getInstance().getTime());

        // 获取渠道编码
        String fundChannelCode = populateFundChannelCode(from, to, instOrder, cmfOrder);
        if (!StringUtils.isEmpty(fundChannelCode)) {
            to.setFundsChannel(fundChannelCode);
        }

        if (to.getExtension() == null) {
            to.setExtension(new Extension());
        }
        if (StringUtils.isNotEmpty(from.getInstResultCode())) {
            to.getExtension().add(ExtensionKey.UNITY_RESULT_CODE.key, from.getInstResultCode());
        }
        String message = from.getExtension().get(ExtensionKey.UNITY_RESULT_MESSAGE.key);
        if (StringUtils.isNotEmpty(message)) {
            to.getExtension().add(ExtensionKey.UNITY_RESULT_MESSAGE.key, message);
        }
        if (from.getRealAmount() != null && cmfOrder.getBizType() == BizType.FUNDOUT) {
            to.getExtension().add(ExtensionKey.ARRIVAL_AMOUNT.key, from.getRealAmount().getAmount().toString());
            to.getExtension().add(ExtensionKey.ARRIVAL_CURRENCY.key, from.getRealAmount().getCurrency());
        }
    }

    /**
     * 获取渠道编码
     *
     * @param from
     * @param to
     */
    private static String populateFundChannelCode(InstOrderResult from, CmfFundResult to,
                                                  InstOrder dbInstOrder, CmfOrder cmfOrder) {
        String fundChannelCode = null;

        if (dbInstOrder != null) {
            //cmfOrder里存的是PE请求的渠道编码.
            fundChannelCode = dbInstOrder.getFundChannelCode();
        }

        return fundChannelCode;
    }

    /**
     * 充退结果.
     *
     * @param from
     * @param result
     */
    private static void populateReFundResult(InstOrderResult from, CmfFundResult result,
                                             InstOrder instOrder) {
        logger.debug("返回PE订单{},结果{}", from.getInstOrderId(), from);

        result.setSuccess(true);

        /**
         * 1.PE提交充退请求给CMF时，如果是手工充退，提交资金后台并返回code0004，如果是自动的(直连)并且是同步，直接获得结果，返回0000，否则返回0001(处理中)
         * 2. CMF得到异步的结果后通过MQ返回PE0000(退款成功)或者0003(退款失败)
         * 3. 在未提交机构时发生的异常返回0003直接失败，提交机构失败，处理结果不明的返回处理中
         */
        CmfFundResultCode resultCode = getReFundRerturnCode(from, instOrder);
        result.setResultCode(resultCode);
        result.setResultMessage(resultCode == null ? from.getMemo() : resultCode.getMessage() + ","
                + from.getMemo());
        result.setExtension(CommonConverter.convertExtension(from.getExtension()));
    }

    private static CmfFundResultCode getReFundRerturnCode(InstOrderResult from, InstOrder instOrder) {

        String code = from.getInstResultCode();

        if (InstOrderProcessStatus.SUBMIT_CMF_FAIL.equals(from.getProcessStatus())) {
            //如果保存cmf订单失败,明确失败
            return CmfFundResultCode.FAILED;
        }
        //R_C:使用统一编码
        if (from.isReturnCodeRefacted()) {
            if (from.getStatus() == null) {
                return CmfFundResultCode.IN_PROCESS;
            }
            switch (from.getStatus()) {
                case SUCCESSFUL:
                    //针对风控特殊逻辑处理,结果表成功,不一定成功,目前用不到
                    if (InstOrderStatus.SUCCESSFUL.equals(instOrder.getStatus())) {
                        return CmfFundResultCode.SUCCESS;
                    } else if (InstOrderStatus.FAILURE.equals(instOrder.getStatus())) {
                        return CmfFundResultCode.FAILED;
                    }
                case FAILURE:
                    return CmfFundResultCode.FAILED;
                case IN_PROCESS:
                case UNKNOWN:
                case RISK:
                    return CmfFundResultCode.REQUEST_SUCCESS;
                //TODO:设置手工退款
                default:
                    return CmfFundResultCode.IN_PROCESS;
            }
        } else {
            ChannelRefundStatus refundStatus = ChannelRefundStatus.getByCode(code);
            if (ChannelRefundStatus.RefundAccept.equals(refundStatus)
                    || ChannelRefundStatus.RefundBankAccept.equals(refundStatus)
                    || ChannelRefundStatus.RefundCheckPass.equals(refundStatus)
                    || ChannelRefundStatus.RefundSubmitException.equals(refundStatus)
                    || ChannelRefundStatus.SystemError.equals(refundStatus)) {
                //RefundResultCode.RefundSubmited;0
                return CmfFundResultCode.REQUEST_SUCCESS;

            } else if (ChannelRefundStatus.RefundFailed.equals(refundStatus)
                    || InstOrderResultStatus.FAILURE.equals(from.getStatus())) {
                return CmfFundResultCode.FAILED;

            } else if (ChannelRefundStatus.RefundSuccess.equals(refundStatus)
                    || (InstOrderResultStatus.SUCCESSFUL.equals(from.getStatus()) && InstOrderProcessStatus.SUCCESS
                    .equals(from.getProcessStatus()))) {
                //针对风控特殊逻辑处理,结果表成功,不一定成功,目前用不到
                if (InstOrderStatus.SUCCESSFUL.equals(instOrder.getStatus())) {
                    return CmfFundResultCode.SUCCESS;
                } else if (InstOrderStatus.FAILURE.equals(instOrder.getStatus())) {
                    return CmfFundResultCode.FAILED;
                }
            } else if (ChannelRefundStatus.RefundManualAccept.equals(refundStatus)
                    || ChannelRefundStatus.RefundManualCheckPass.equals(refundStatus)) {
                return CmfFundResultCode.REFUND_MANUAL;
            }
            //明确失败（比如无可用路由），机构订单结果：失败
            if (null == refundStatus && InstOrderResultStatus.FAILURE == from.getStatus()) {
                return CmfFundResultCode.FAILED;
            } else {
                //不能返回未知异常给PE（会导致PE的后续状态跃迁异常），告诉PE在处理中.
                return CmfFundResultCode.IN_PROCESS;
            }
        }
    }

    /**
     * 依据cmf订单组装返回结果
     * 注: 只有当instOrder 和instOrderResult 不存在时方能调用
     *
     * @param cmfOrder
     * @return
     */
    public static CmfFundResult convert(CmfOrder cmfOrder) {
        CmfFundResult cmfFundResult = new CmfFundResult();
        switch (cmfOrder.getStatus()) {
            case SUCCESSFUL:
                cmfFundResult.setResultCode(CmfFundResultCode.SUCCESS);
                cmfFundResult.setSuccess(true);
                break;
            case FAILURE:
            case CANCEL:
                cmfFundResult.setResultCode(CmfFundResultCode.FAILED);
                cmfFundResult.setSuccess(false);
                break;
            default:
                cmfFundResult.setResultCode(CmfFundResultCode.REQUEST_SUCCESS);
                cmfFundResult.setSuccess(false);
                break;
        }

        cmfFundResult.setAmount(cmfOrder.getAmount());

        cmfFundResult.setChannelPayNo(cmfOrder.getOrderSeqNo());
        return cmfFundResult;
    }
}
