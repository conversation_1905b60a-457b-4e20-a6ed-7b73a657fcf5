package com.uaepay.cmf.domainservice.main.general.impl;


import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.constants.MerchantConstant;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.exception.CmfBizException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterQueryRequest;
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterQueryResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version: APlusMerchantRegisterQueryProcessor.class v1.0
 */
@Service
public class APlusMerchantRegisterQueryProcessor extends GeneralProcessorTemplate<APlusMerchantRegisterQueryRequest, APlusMerchantRegisterQueryResponse> implements MerchantConstant {

    @Resource
    private InstControlOrderRepository instControlOrderRepository;
    @Resource
    protected SubmitInstitutionService submitInstitutionService;

    @Override
    protected String getServiceName() {
        return "APlusMerchantRegisterQueryProcessor";
    }

    @Override
    protected APlusMerchantRegisterQueryResponse createResponse() {
        return new APlusMerchantRegisterQueryResponse();
    }

    @Override
    protected void process(APlusMerchantRegisterQueryRequest request, APlusMerchantRegisterQueryResponse response) {
        InstControlOrder controlOrder = instControlOrderRepository.loadLatestByRequestNoAndApiType(request.getPreRequestNo(), FundChannelApiType.MERCHANT_REGISTER.getCode());
        if (controlOrder == null) {
            throw new CmfBizException(ErrorCode.ORDER_NOT_FOUND);
        }
        InstControlOrderResult queryResponse = submitInstitutionService.submit(controlOrder, FundChannelApiType.MERCHANT_REGISTER_QUERY);
        response.setApplyStatus(ApplyStatusEnum.SUCCESS);
        response.setUnityResultCode(queryResponse.getInstResultCode());
        response.setRegisterStatus(queryResponse.getExtension().get(REGISTER_STATUS));
        response.setRegisterMessage(queryResponse.getExtension().get(REGISTER_MESSAGE));
        response.setStoreId(controlOrder.getExtension().get(STORE_ID));
        response.setMerchantId(controlOrder.getMerchantId());
    }

}
