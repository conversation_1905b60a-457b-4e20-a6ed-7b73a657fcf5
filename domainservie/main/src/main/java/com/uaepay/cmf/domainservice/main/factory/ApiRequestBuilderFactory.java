package com.uaepay.cmf.domainservice.main.factory;

import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.channel.request.builder.AbstractApiRequestBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>业务请求构建器工厂</p>
 *
 * <AUTHOR>
 * @date ApiRequestFactory.java v1.0  2020-09-09 16:14
 */
@Service
public class ApiRequestBuilderFactory {

    private Map<FundChannelApiType, AbstractApiRequestBuilder> apiBuilderMap = new ConcurrentHashMap<>(20);

    public void put(List<FundChannelApiType> apiTypeList, AbstractApiRequestBuilder builder) {
        for (FundChannelApiType aipType : apiTypeList) {
            apiBuilderMap.put(aipType, builder);
        }
    }

    public AbstractApiRequestBuilder get(FundChannelApiType apiType) {
        AbstractApiRequestBuilder builder = apiBuilderMap.get(apiType);
        Assert.notNull(builder, "Unsupported api type:" + apiType);
        return builder;
    }
}
