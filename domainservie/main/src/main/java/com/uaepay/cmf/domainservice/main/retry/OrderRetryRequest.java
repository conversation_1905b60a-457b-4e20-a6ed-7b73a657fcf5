package com.uaepay.cmf.domainservice.main.retry;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.uaepay.cmf.common.enums.FundChannelApiType;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version RetryTimes.java 1.0 Created@2017-12-28 15:56 $
 */
@Getter
@Setter
@ToString(callSuper = true)
public class OrderRetryRequest implements Serializable {

    private static final long  serialVersionUID = 8923682997904515139L;
    /** 订单Id */
    private Long               orderId;
    /** 订单类型 */
    private OrderRetryType     retryType;
    /** 重试时间配置 */
    private String             retryTimeConfig;
    /** 接口类型 */
    private FundChannelApiType fundChannelApiType;
    /** 订单重试次数 */
    private Integer            retryTimes;
    /** 订单重试时间 */
    private Date               retryDateTime;
    /** 渠道编号 */
    private String             fundChannelCode;

    public boolean isValid() {
        return orderId != null && retryType != null && retryTimeConfig != null
                && retryTimes != null;
    }

}
