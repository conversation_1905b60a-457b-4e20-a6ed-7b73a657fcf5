package com.uaepay.cmf.domainservice.main.domain;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardCategoryEnum.java v1.0  2020-05-12 21:40
 */
public enum CardCategoryEnum {
    // card type
    COMMON(1, "普通卡"),
    TOKEN(2, "token类型卡"),
    IBAN(3, "提现iban卡"),
    VIRTUAL_DEBIT(4, "虚拟借记卡(FAB)"),
    VIRTUAL_CREDIT(5, "虚拟信用卡(与银行无关)"),
    PAYLATER(6, "PAYLATER"),
    /**
     * 账户卡
     */
    BANK_ACCOUNT(7,"BANK_ACCOUNT"),
    /**
     * fab收款卡，可充值
     */
    FAB_VAM_IBAN(8,"收款卡"),
    MERCHANT_CUSTOMER_CARD(9,"商户卡"),
    ;


    private int code;
    private String description;

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    CardCategoryEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static CardCategoryEnum getByName(String name) {
        for (CardCategoryEnum cardCategory : values()) {
            if (cardCategory.name().equals(name)) {
                return cardCategory;
            }
        }
        return null;
    }

    public boolean isQuickPay() {
        return this == COMMON || this == PAYLATER || this == MERCHANT_CUSTOMER_CARD;
    }

    public boolean isFundOut() {
        return this == IBAN || this == BANK_ACCOUNT;
    }
}
