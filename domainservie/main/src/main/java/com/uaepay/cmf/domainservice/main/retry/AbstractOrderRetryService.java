package com.uaepay.cmf.domainservice.main.retry;

import java.util.Date;

import com.uaepay.cmf.fss.ext.integration.util.ChannelUtil;
import com.uaepay.common.util.DateUtil;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version AbstractOrderRetryService.java 1.0 Created@2017-12-28 16:08 $
 */
abstract class AbstractOrderRetryService implements OrderRetryService {

    @Override
    public int updateRetryTime(OrderRetryRequest request) {
        Date retryDateTime = calcGmtNextRetry(request);
        return updateRetryInfoById(request.getRetryTimes(), retryDateTime, request.getOrderId());
    }

    abstract int updateRetryInfoById(int retryTimes, Date retryDateTime, Long orderId);

    private Date calcGmtNextRetry(OrderRetryRequest request) {
        if (ChannelUtil.isManualRefund(request.getFundChannelApiType())) {
            // 人工退款订单重试时间设置为一天之后，避免查询
            return DateUtil.addMinutes(new Date(), MINUTES_ONE_DAY);
        }

        int minutes = 5;
        try {
            for (String itemConfig : getQueryConfig(request)) {
                String[] currentConfig = itemConfig.split("\\" + CHAR_CARET);
                if (request.getRetryTimes() <= Integer.valueOf(currentConfig[0])) {
                    minutes = Integer.valueOf(currentConfig[1]);
                    break;
                }
            }
        } catch (Exception e) {
            // do nothing
        }
        // 在当前时间上加
        return DateUtil.addMinutes(new Date(), minutes);
    }

    private String[] getQueryConfig(OrderRetryRequest request) {
        return request.getRetryTimeConfig().split("\\" + CHAR_VERTICAL_LINE);
    }



}
