package com.uaepay.cmf.domainservice.channel.limit;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;

/**
 * <p>限额服务</p>
 *
 * <AUTHOR>
 * @date LimitService.java v1.0
 */
public interface LimitService {

    /**
     * 限额校验
     * @param cmfOrder
     * @return
     */
    boolean validateLimit(CmfOrder cmfOrder);

    /**
     * 记录流量
     * @param instOrder
     * @return
     */
    boolean recordFlow(InstOrder instOrder);
}
