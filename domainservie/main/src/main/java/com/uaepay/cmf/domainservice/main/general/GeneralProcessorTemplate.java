package com.uaepay.cmf.domainservice.main.general;

import com.alibaba.fastjson.JSONObject;
import com.uaepay.basis.beacon.common.util.ValidatorUtil;
import com.uaepay.basis.beacon.service.facade.domain.request.AbstractRequest;
import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.exception.CmfBizException;
import com.uaepay.cmf.common.core.domain.exception.CmfIllegalArgumentException;
import com.uaepay.cmf.common.core.domain.exception.DuplicateRequestException;
import com.uaepay.cmf.common.core.domain.exception.ValidateException;
import com.uaepay.cmf.common.core.engine.util.CommonUtil;
import com.uaepay.cmf.common.core.util.validate.Validate;
import com.uaepay.common.lang.diagnostic.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.util.UUID;

/**
 * <p>一般处理器模板</p>
 *
 * <AUTHOR>
 */
public abstract class GeneralProcessorTemplate<Req extends AbstractRequest, Rep extends CommonResponse> implements BasicConstant {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 服务名称，日志用
     */
    protected abstract String getServiceName();

    /**
     * 创建响应对象
     */
    protected abstract Rep createResponse();

    /**
     * 可接受的处理时间，超过则打印分析日志
     */
    protected int acceptableDuration() {
        return 3000;
    }

    /**
     * 校验请求，校验不通过抛异常
     *
     * @throws ValidateException
     */
    protected void validate(Req req) throws ValidateException, DuplicateRequestException {
        Assert.notNull(req, "请求体未提供");
        commonValidate(req);
        businessValidate(req);
    }

    /**
     * 校验请求，校验不通过抛异常
     *
     * @throws ValidateException
     */
    protected void commonValidate(Req req) throws ValidateException {
        Validate.assertNotNull("请求", req);
        // 校验请求参数
        ValidatorUtil.validate(req);
    }

    protected void businessValidate(Req req) throws DuplicateRequestException {

    }

    /**
     * 处理请求
     */
    protected abstract void process(Req req, Rep rep);

    public Rep process(Req request) {
        Profiler.start();
        Rep response = createResponse();
        successResponse(response);
        try {
            String logPrefix = String.format("%s-%s", getServiceName(), UUID.randomUUID().toString().replace("-", ""));
            logger.info("{} - 请求 = {}", logPrefix, buildRequestLog(request));
            validate(request);
            process(request, response);
            logger.info("{} - 返回 = {}", logPrefix, response);
        } catch (DuplicateRequestException dre){
            logger.warn("重复请求异常：{}", dre.getMessage());
            resolveDuplicateResponse(request, response, dre.getMessage());
        } catch (ValidateException|CmfIllegalArgumentException e) {
            logger.error("参数校验异常：{}", e.getMessage());
            fillCode(response, ApplyStatusEnum.FAIL, ErrorCode.INVALID_PARAM, e.getMessage());
        } catch (CmfBizException e) {
            logger.info("CardsBizException异常:" + e.getCode());
            fillFailToi8nCode(response, ApplyStatusEnum.FAIL, e.getCode());
        } catch (Throwable e) {
            logger.error("处理异常", e);
            fillCode(response, ApplyStatusEnum.ERROR, ErrorCode.OPERATION_FAIL);
        } finally {
            Profiler.release();
            logger.info("响应(耗时: {} ms)", Profiler.getDuration());
            if (Profiler.getDuration() > acceptableDuration()) {
                logger.info("时间分析: {}", Profiler.dump());
            }
            Profiler.reset();
        }
        return response;
    }

    protected void resolveDuplicateResponse(Req request, Rep response, String message){
        fillCode(response, ApplyStatusEnum.FAIL, ErrorCode.INVALID_PARAM, message);
    }

    /**
     * 构建请求日志
     */
    protected String buildRequestLog(Req request) {
        return request == null ? null : JSONObject.toJSONString(request);
    }

    /**
     * 构建响应日志
     */
    protected String buildResponseLog(Rep response) {
        return JSONObject.toJSONString(response);
    }


    protected void fillFail(Rep response, ApplyStatusEnum status, ErrorCode errorCode) {
        fillFail(response, status, errorCode, errorCode.getErrorMessage());
    }

    /**
     * 填充国际码
     *`
     * @param response
     * @param responseCode
     */
    protected void fillFailToi8nCode(Rep response, ApplyStatusEnum status, ErrorCode responseCode) {
        fillFail(response, status, responseCode, CommonUtil.i18nCodeBuild(responseCode.getErrorCode()));
    }

    protected void fillFail(Rep response, ApplyStatusEnum status, ErrorCode responseCode, String responseMessage) {
        response.setUnityResultCode(responseMessage);
        fillCode(response, status, responseCode);
    }

    /**
     * 填充code
     *
     * @param response
     * @param errorCode
     */
    protected void fillCode(Rep response, ApplyStatusEnum status, ErrorCode errorCode) {
        fillCode(response, status, errorCode, errorCode.getErrorMessage());
    }

    protected void fillCode(Rep response, ApplyStatusEnum status, ErrorCode errorCode, String responseMessage) {
        response.setApplyStatus(status);
        response.setCode(errorCode.getErrorCode());
        response.setMessage(responseMessage);
    }

    protected void successResponse(Rep response) {
        response.success();
    }

    protected void assertNotNull(Object obj, String message){
        assertIsTrue(obj!=null, message);
    }

    protected void assertIsTrue(boolean condition, String message) {
        if (!condition) {
            throw new CmfIllegalArgumentException(message);
        }
    }


}