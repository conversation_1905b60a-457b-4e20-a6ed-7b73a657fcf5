package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.enums.ChannelRequestExtensionMapping;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstFundoutOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.engine.util.PropertyValueUtil;
import com.uaepay.cmf.common.domain.fundout.BatchFundoutRequest;
import com.uaepay.cmf.common.domain.fundout.FundoutRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BatchFundOutApiRequestBuilder.java v1.0  2020-09-09 17:32
 */
@Service
public class BatchFundOutApiRequestBuilder extends AbstractApiRequestBuilder<InstBatchOrder, BatchFundoutRequest> {
    @Override
    protected void buildCustomParam(InstBatchOrder order, BatchFundoutRequest request) {
        //公用配置
        request.setInstOrderNo(order.getInstBatchNo());
        //资金渠道编码
        request.setInstOrderSubmitTime(order.getGmtArchive());
        request.setAmount(order.getAmount());

        //子订单配置
        List<FundoutRequest> requests = null;

        List<InstOrder> instOrders = order.getInstOrderList();

        if (CollectionUtils.isNotEmpty(instOrders)) {
            request.setTargetInstCode(order.getInstOrderList().get(0).getInstCode());
            requests = instOrders.stream().map(BatchFundOutApiRequestBuilder::convert).collect(Collectors.toList());
        }

        //渠道区分同行与跨行
        if (CollectionUtils.isNotEmpty(requests)) {
            request.setRequests(requests);
            request.setInnerBank(requests.get(0).getInnerBank());
        }

    }

    private static FundoutRequest convert(InstOrder instOrder) {
        InstFundoutOrder order = (InstFundoutOrder) instOrder;
        FundoutRequest request = new FundoutRequest();

        BeanUtils.copyProperties(order, request);
        request.setAccountType(order.getCompanyOrPersonal());
        request.setTargetInstCode(order.getInstCode());
        request.setInstCode(ChannelHolder.get().getInstCode());

        String purpose = StringUtils.defaultIfEmpty(order.getPurpose(),
                order.getExtension().get(ExtensionKey.PURPOSE.key));
        request.setPurpose(purpose);

        request.setInnerBank(request.getTargetInstCode() != null && request.getTargetInstCode().equals(request.getInstCode()));

        //设置机构和请求号信息
        for (ChannelRequestExtensionMapping mapping : ChannelRequestExtensionMapping
                .getMappingSet(FundoutRequest.class)) {
            PropertyValueUtil.setValue(request, mapping,
                    order.getExtension().get(mapping.getExtensionKey()));
        }

        //获取扩展信息
        ChannelApiVO api = ChannelHolder.get().getChannelApi();
        request.setExtension(filterExtKey(order.getExtension(),
                api.getParamList()));

        return request;
    }

    @Override
    public BatchFundoutRequest buildReq() {
        return new BatchFundoutRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(FundChannelApiType.BATCH_FILE_PAY, FundChannelApiType.BATCH_PAY, FundChannelApiType.MANUAL_FILE_PAY);
    }
}
