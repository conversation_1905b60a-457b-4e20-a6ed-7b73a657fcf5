package com.uaepay.cmf.domainservice.main.spi;

import com.uaepay.cmf.common.core.domain.exception.DuplicateRequestException;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.vo.VerifyResponseContent;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.scheduling.annotation.Async;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date VerifySignService.java v1.0  2020-04-02 17:22
 */
public interface VerifySignService {

    void asyncVerify(VerifySignRequest request) throws DuplicateRequestException;

    InstOrderResult verify(VerifySignRequest request) throws DuplicateRequestException;

    @Async
    void asyncVerifyReq(VerifySignRequest request, InstBaseOrder baseOrder);

    Pair<InstBaseResult, VerifyResponseContent> verifyReq(VerifySignRequest request, InstBaseOrder baseOrder);

    @Async
    void asyncVerifyReqNonOrder(VerifySignRequest request);

    InstBaseResult verifyReqNonOrder(VerifySignRequest request);
}
