package com.uaepay.cmf.domainservice.channel.holder;

import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>签约渠道信息.</p>
 *
 * <AUTHOR>
 * @version CardTokenHolder.java 1.0 Created@2017-09-21 10:11 $
 */
@Service
public class TokenHolder {

    public static final ThreadLocal<Map<String, Object>> threadLocal = new ThreadLocal<>();

    /**
     * 获取，如果线程中没有，则返回null
     * @return
     */
    public static Map<String, Object> get() {
        return threadLocal.get();
    }

    /**
     * 设置渠道
     * @param dataMap
     */
    public static void set(Map<String, Object> dataMap) {
        threadLocal.set(dataMap);
    }

    /**
     * 清理
     */
    public static void clear() {
        threadLocal.remove();
    }

}
