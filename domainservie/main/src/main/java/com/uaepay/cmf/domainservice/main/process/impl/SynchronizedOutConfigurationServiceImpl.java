package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.enums.InstOrderCommunicateType;
import com.uaepay.cmf.common.core.domain.enums.InstOrderSendType;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigureKey;
import com.uaepay.cmf.domainservice.main.process.SynchronizedOutConfigurationService;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 同步配置服务
 * </p>
 *
 * <AUTHOR>
 */
@Service("synchronizedOutConfigurationService")
public class SynchronizedOutConfigurationServiceImpl implements SynchronizedOutConfigurationService {

    private static final String FUNDOUT_REALTIME = "2";

    /**
     * 系统配置
     */
    @Resource
    SysConfigurationHolder sysConfigurationHolder;

    /**
     * 渠道异步、订单异步
     *
     * @param fundChannel
     * @param instOrderList
     * @return
     */
    @Override
    public boolean isAsynchronousOrder(final ChannelVO fundChannel, final List<InstOrder> instOrderList) {
        if (CollectionUtils.isEmpty(instOrderList)) {
            return isAsyncChannel(fundChannel);
        }
        return isAsyncChannel(fundChannel) || isAsyncOrder(instOrderList);
    }

    @Override
    public boolean isAsynchronousOrder(final ChannelVO fundChannel, final InstOrder instOrder) {
        List<InstOrder> instOrderList = new ArrayList<>();
        instOrderList.add(instOrder);
        return isAsynchronousOrder(fundChannel, instOrderList);
    }

    /**
     * 异步订单：交互类型-批量，发送类型：异步发送，订单列表>1
     *
     * @param instOrderList
     * @return
     */
    private boolean isAsyncOrder(List<InstOrder> instOrderList) {
        if (CollectionUtils.isEmpty(instOrderList)) {
            return false;
        }
        InstOrder instOrder = instOrderList.get(0);
        return instOrder.getCommunicateType() == InstOrderCommunicateType.BATCH
                || InstOrderSendType.ASYNCHRONOUS == instOrder.getSendType()
                || instOrderList.size() > 1;
    }

    /**
     * 渠道接口配置为异步
     *
     * @param channel
     * @return
     */
    private boolean isAsyncChannel(ChannelVO channel) {
        return channel != null && channel.getChannelApi() != null
//                && channel.getChannelApi().isNotSynchronized()
                || inSufficentBalanceChannel(channel);
    }

    private boolean inSufficentBalanceChannel(ChannelVO channel) {
        String channels =
                sysConfigurationHolder.loadConfigureOrDefault(SysConfigureKey.BALANCE_INSUFFICIENT_CHANNELS, "");
        return channel != null && channels.contains(channel.getChannelCode());
    }

}
