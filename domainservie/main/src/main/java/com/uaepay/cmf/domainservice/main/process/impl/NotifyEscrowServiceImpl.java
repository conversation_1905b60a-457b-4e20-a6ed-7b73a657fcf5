package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstRefundOrder;
import com.uaepay.cmf.domainservice.channel.router.ConfigurationService;
import com.uaepay.cmf.domainservice.main.convert.TransformOrderConverter;
import com.uaepay.cmf.domainservice.main.process.NotifyEscrowService;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.integration.escrow.EscrowClient;
import com.uaepay.cmf.service.facade.domain.query.SimpleOrder;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date NotifyEscrowServiceImpl.java v1.0  2020-10-16 15:36
 */
@Slf4j
@Service
public class NotifyEscrowServiceImpl implements NotifyEscrowService {

    @Resource
    private InstOrderRepository instOrderRepository;
    @Resource
    private EscrowClient escrowClient;
    @Resource
    private ConfigurationService configurationService;
    @Value("${escrow.notify.channels:MC101,MC104,MC105,CS101,CS102,CS103}")
    private String allowNotifyChannels;
    @Value("${escrow.notify.switch:N}")
    private String escrowNotifySwitch;

    @Override
    public void notifyOrder(InstOrder instOrder) {
        if (instOrder == null || instOrder.getStatus() != InstOrderStatus.SUCCESSFUL) {
            return;
        }
        sendSimpleOrder(instOrder);
        sendTransformOrder(instOrder);
    }

    @Override
    public void notifyOrder(String instOrderNo) {
        InstOrder instOrder = instOrderRepository.loadByNo(instOrderNo);
        notifyOrder(instOrder);
    }

    private void sendSimpleOrder(InstOrder instOrder) {
        if (!allowTNotify(instOrder.getFundChannelCode())) {
            return;
        }
        escrowClient.sendOrder(transfer2SimpleOrder(instOrder));
    }

    private void sendTransformOrder(InstOrder instOrder){
        if (!configurationService.isSvaTransformOrder(instOrder)) {
            return;
        }
        escrowClient.sendTransformOrder(TransformOrderConverter.convert(instOrder));
    }

    /**
     * 允许T任务通知
     *
     * @param fundChannelCode
     * @return
     */
    private boolean allowTNotify(String fundChannelCode) {
        return allowNotifyChannels.contains(fundChannelCode) && YesNo.YES.getCode().equals(escrowNotifySwitch);
    }

    private SimpleOrder transfer2SimpleOrder(InstOrder instOrder) {
        SimpleOrder simpleOrder = new SimpleOrder();
        BeanUtils.copyProperties(instOrder, simpleOrder);
        simpleOrder.setGmtSubmit(instOrder.getGmtBookingSubmit());
        simpleOrder.setBizType(instOrder.getBizType().getCode());
        simpleOrder.setStatus(instOrder.getStatus().getCode());
        String bizProductCode = instOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.getKey());
        // 退款订单取原订单业务产品码
        if (StringUtils.isEmpty(bizProductCode) && instOrder.getBizType() == BizType.REFUND) {
            InstRefundOrder refundOrder = (InstRefundOrder) instOrder;
            InstOrder fundInOrder = instOrderRepository.loadByNo(refundOrder.getFundinOrderNo());
            bizProductCode = fundInOrder != null ? fundInOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.getKey()) : bizProductCode;
        }

        simpleOrder.setBizProductCode(bizProductCode);

        simpleOrder.setMemberId(instOrder.getExtension().get(ExtensionKey.MEMBER_ID.getKey()));
        simpleOrder.setProductOrderNo(instOrder.getExtension().get(ExtensionKey.PRODUCT_ORDER_NO.key));
        simpleOrder.setPaymentOrderNo(instOrder.getExtension().get(ExtensionKey.PAYMENT_ORDER_NO.key));

        return simpleOrder;
    }


}
