package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.dal.dataobject.MonitorLogDO;
import com.uaepay.cmf.common.enums.MonitorItem;
import com.uaepay.cmf.common.enums.MonitorLogStatus;
import com.uaepay.cmf.common.monitor.MonitorLog;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>监控转换器</p>
 * <AUTHOR>
 * @version $Id: MonitorConverter.java, v 0.1 2012-8-16 上午11:11:44 fuyangbiao Exp $
 */
public class MonitorLogConverter {

    /**
     * DO转换为DOMAIN
     * @param logDO
     * @return
     */
    public static MonitorLog convert(MonitorLogDO logDO) {
        if (null == logDO) {
            return null;
        }
        MonitorLog log = new MonitorLog(MonitorItem.getByCode(logDO.getActionId()));


        log.setExceptionLog(logDO.getExceptionLog());
        log.setEventMessage(logDO.getEventMessage());
        log.setGmtCreate(logDO.getGmtCreate());
        log.setGmtModified(logDO.getGmtModified());
        log.setIpAddress(logDO.getIpAddress());
        log.setLogId(logDO.getLogId());
        log.setMemo(logDO.getMemo());
        log.setOrderNo(logDO.getOrderNo());
        log.setStatus(MonitorLogStatus.getByCode(logDO.getLogStatus()));

        return log;
    }

    /**
     * DOMAIN转换为DO
     * @param log
     * @return
     */
    public static MonitorLogDO convert(MonitorLog log) {
        if (null == log || null == log.getMonitorItem()) {
            return null;
        }
        MonitorLogDO logDO = new MonitorLogDO();

        logDO.setAlertLevel(log.getMonitorItem().getMonitorLevel());
        logDO.setActionId(log.getMonitorItem().getActionId());
        logDO.setLogStatus(log.getStatus().getCode());
        //事件描述
        if (!StringUtils.isEmpty(log.getEventMessage()) && log.getEventMessage().length() > 200) {
            logDO.setEventMessage(log.getEventMessage().substring(0, 200));
        } else {
            logDO.setEventMessage(log.getEventMessage());
        }

        //异常日志
        if (!StringUtils.isEmpty(log.getExceptionLog()) && log.getExceptionLog().length() > 1000) {
            logDO.setExceptionLog(log.getExceptionLog().substring(0, 1000));
        } else {
            logDO.setExceptionLog(log.getExceptionLog());
        }
        logDO.setGmtCreate(log.getGmtCreate());
        logDO.setGmtModified(log.getGmtModified());
        logDO.setIpAddress(log.getIpAddress());
        logDO.setLogId(log.getLogId());
        logDO.setMemo(log.getMemo());
        logDO.setOrderNo(log.getOrderNo());

        return logDO;
    }
}
