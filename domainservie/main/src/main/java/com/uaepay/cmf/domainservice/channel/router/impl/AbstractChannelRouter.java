package com.uaepay.cmf.domainservice.channel.router.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.exception.RouteChannelException;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.domainservice.channel.router.ConfigurationService;
import com.uaepay.cmf.domainservice.main.domain.ChannelCarrier;
import com.uaepay.router.service.facade.domain.RouteResponse;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;

import javax.annotation.Resource;

/**
 * <p>抽象渠道路由器</p>
 *
 * <AUTHOR>
 * @date AbstractChannelRouter.java v1.0  2020-09-08 15:01
 */
public abstract class AbstractChannelRouter<Request> implements BasicConstant {

    @Resource
    protected ConfigurationService configurationService;

    /**
     * 抽象路由方法
     *
     * @param request
     * @return
     */
    public ChannelCarrier route(Request request) throws RouteChannelException {

        // 路由前准备
        beforeRoute(request);

        // 个性化路由
        RouteResponse<ChannelVO> resp = routeCustom(request);

        // 校验路由结果
        validateRouteResponse(resp);

        // 设置到ThreadLocal中
        ChannelHolder.set(resp.getChannel());

        return ChannelCarrier.builder().channel(resp.getChannel()).orderInfo(resp.getOrderInfo()).build();
    }

    /**
     * 路由前操作
     *
     * @param request
     */
    protected abstract void beforeRoute(Request request);

    /**
     * 个性化路由
     *
     * @param request
     * @return
     */
    protected abstract RouteResponse<ChannelVO> routeCustom(Request request);


    void validateRouteResponse(RouteResponse<ChannelVO> resp) throws RouteChannelException {
        assertRouteSuccess(routeSuccess(resp), ErrorCode.getFromErrorCode(resp.getUnityResultCode()) == null ? ErrorCode.ROUTE_ERROR_NO_VALID_CHANNEL : ErrorCode.getFromErrorCode(resp.getUnityResultCode()));
    }

    boolean routeSuccess(RouteResponse<ChannelVO> resp) {
        return resp != null && resp.getApplyStatus() == ApplyStatusEnum.SUCCESS && resp.getChannel() != null;
    }

    void assertRouteSuccess(boolean status, ErrorCode errorCode) throws RouteChannelException {
        if (!status) {
            throw new RouteChannelException(errorCode);
        }
    }

}
