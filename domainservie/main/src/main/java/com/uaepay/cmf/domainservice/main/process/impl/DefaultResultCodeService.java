package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.domainservice.main.process.ResultCodeService;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.router.service.facade.domain.ResultCodeRequest;
import com.uaepay.router.service.facade.domain.ResultCodeResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>统一结果编码处理实现接口</p>
 *
 * <AUTHOR> Liu
 * @version $Id: DefaultResultCodeService.java, v 0.1 2012-9-25 上午10:12:07 liumaoli Exp $
 */
@Slf4j
@Service
public class DefaultResultCodeService implements ResultCodeService, BasicConstant {

    @Resource
    private RouterClient routerClient;

    /**
     * 根据结果码转换订单状态
     *
     * @param baseResult
     */
    @Override
    public void fillResultStatus(InstBaseResult baseResult) {
        if (baseResult == null) {
            return;
        }
        // 请求转换
        ResultCodeRequest request = convert2ResultRequest(baseResult);
        // 请求router获取转换后结果
        ResultCodeResult result = routerClient.parseResult(request);

        processResult(baseResult, result);
        log.info("获取统一结果编码成功:instOrderNo={},instStatus={},processStatus={},instResultCode={}", baseResult.getInstOrderNo(),
                baseResult.getStatus(), baseResult.getProcessStatus(), baseResult.getInstResultCode());
    }


    private ResultCodeRequest convert2ResultRequest(InstBaseResult baseResult) {
        ResultCodeRequest request = new ResultCodeRequest();
        request.setApiType(baseResult.getApiType().getCode());
        request.setChannelCode(baseResult.getFundChannelCode());
        request.setOrderNo(baseResult.getInstOrderNo());
        request.setResultCode(baseResult.getApiResultCode());
        request.setResultSubCode(StringUtils.isEmpty(baseResult.getApiResultSubCode()) ? Strings.EMPTY : baseResult.getApiResultSubCode());
        request.setClientId(CLIENT_ID);

        return request;
    }

    private void processResult(InstBaseResult baseResult, ResultCodeResult result) {
        if (!isLegalResult(result)) {
            result.setResultStatus(InstOrderResultStatus.UNKNOWN.getCode());
        }
        baseResult.setStatus(InstOrderResultStatus.getByCode(result.getResultStatus()));

        String unityResultCode = result.getUnityResultCode();
        // 结果为成功则不返回统一结果码
        if (baseResult.getStatus() != InstOrderResultStatus.SUCCESSFUL || !ErrorCode.SUCCESS.getErrorCode().equals(unityResultCode)) {
            baseResult.setInstResultCode(unityResultCode);
        }

        // 设置机构订单处理状态
        switch (baseResult.getStatus()) {
            case SUCCESSFUL:
            case FAILURE:
            case HALF_SUCCESSFUL:
                baseResult.setProcessStatus(InstOrderProcessStatus.SUCCESS);
                break;
            case NONEXISTS:
                baseResult.setProcessStatus(InstOrderProcessStatus.SUBMIT_INST_FAIL);
                break;
            default:
                baseResult.setProcessStatus(InstOrderProcessStatus.AWAITING);
                break;
        }
    }

    /**
     * 是否为合法的结果
     *
     * @param result
     * @return
     */
    private boolean isLegalResult(ResultCodeResult result) {
        if (result == null || result.getApplyStatus() != ApplyStatusEnum.SUCCESS) {
            return false;
        }
        return InstOrderResultStatus.getByCode(result.getResultStatus()) != null;
    }

}
