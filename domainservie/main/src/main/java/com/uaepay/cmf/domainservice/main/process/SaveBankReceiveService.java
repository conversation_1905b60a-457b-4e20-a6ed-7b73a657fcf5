package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;

/**
 * <p>保存银行返回信息</p>
 *
 * <AUTHOR>
 * @version $Id: SaveBankReceiveService.java, v 0.1 2014-11-4 下午6:53:06 Administrator Exp $
 */
public interface SaveBankReceiveService {

    /**
     * 保存银行返回信息到订单扩展信息接口
     *
     * @param order  原订单
     * @param result 订单结果信息
     */
    void saveExtension(InstControlOrder order, InstControlOrderResult result,
                       ChannelVO fundChannel);
}
