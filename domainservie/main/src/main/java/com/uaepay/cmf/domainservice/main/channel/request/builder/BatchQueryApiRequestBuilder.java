package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.enums.ChannelRequestExtensionMapping;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstRefundOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.engine.util.PropertyValueUtil;
import com.uaepay.cmf.common.domain.fundout.FundoutRequest;
import com.uaepay.cmf.common.domain.query.BatchQueryItemRequest;
import com.uaepay.cmf.common.domain.query.QueryRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.common.util.DateUtil;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BatchQueryApiRequestBuilder.java v1.0  11/14/20 11:09 PM
 */
@Service
public class BatchQueryApiRequestBuilder extends AbstractApiRequestBuilder<InstBatchOrder, BatchQueryItemRequest>{


    @Override
    protected void buildCustomParam(InstBatchOrder order, BatchQueryItemRequest request) {
        //公用配置
        request.setInstOrderNo(order.getInstBatchNo());
        //资金渠道编码
        request.setInstOrderSubmitTime(order.getGmtArchive());
        request.setAmount(order.getAmount());

        //子订单配置
        List<QueryRequest> requests = null;

        List<InstOrder> instOrders = order.getInstOrderList();

        if (CollectionUtils.isNotEmpty(instOrders)) {
            request.setTargetInstCode(order.getInstOrderList().get(0).getInstCode());
            requests = instOrders.stream().map(BatchQueryApiRequestBuilder::convert).collect(Collectors.toList());
        }

        //渠道区分同行与跨行
        if (CollectionUtils.isNotEmpty(requests)) {
            request.setQueryRequestList(requests);
        }
    }

    private static QueryRequest convert(InstOrder order) {

        QueryRequest request = new QueryRequest();
        BeanUtils.copyProperties(order, request);
        request.setInstCode(order.getInstCode());
        request.setQueryTime(new Date());
        request.setInstOrderSubmitTime(order.getGmtBookingSubmit());
        request.setAmount(order.getAmount());

        if (order instanceof InstRefundOrder) {
            request.setOriginalInstOrderNo(((InstRefundOrder) order).getFundinOrderNo());
            request
                    .setOriginalInstOrderAmount((((InstRefundOrder) order).getFundinRealAmount()));
            request.setOriginalInstSeqNo(((InstRefundOrder) order).getFundinInstSeqNo());
            request.setOriginalInstOrderSettleTime(DateUtil
                    .parseDateLongFormat(((InstRefundOrder) order).getFundinDate()));
            request.setOriginalInstOrderSubmitTime(DateUtil
                    .parseDateLongFormat(((InstRefundOrder) order).getFundinDate()));
        }

        //设置机构和请求号信息
        for (ChannelRequestExtensionMapping mapping : ChannelRequestExtensionMapping
                .getMappingSet(FundoutRequest.class)) {
            PropertyValueUtil.setValue(request, mapping,
                    order.getExtension().get(mapping.getExtensionKey()));
        }

        ChannelApiVO api = ChannelHolder.get().getChannelApi();
        request.getExtension()
                .putAll(filterExtKey(order.getExtension(), api.getParamList()));

        return request;
    }

    @Override
    public BatchQueryItemRequest buildReq() {
        return new BatchQueryItemRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(FundChannelApiType.BATCH_QUERY);
    }
}
