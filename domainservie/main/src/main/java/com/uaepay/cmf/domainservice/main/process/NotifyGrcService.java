package com.uaepay.cmf.domainservice.main.process;

import com.uaepay.cmf.service.facade.domain.grc.Notify3dsResult;
import com.uaepay.cmf.service.facade.domain.grc.Query3dsRequest;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date NotifyGrcService.java v1.0  2020-10-16 15:36
 */
public interface NotifyGrcService {


    void saveResultAndNotify(Notify3dsResult result);

    void notify3dsResult(String orderNo);

    Notify3dsResult query3dsResult(Long resultId);

    Notify3dsResult query3dsResult(Query3dsRequest request);
}
