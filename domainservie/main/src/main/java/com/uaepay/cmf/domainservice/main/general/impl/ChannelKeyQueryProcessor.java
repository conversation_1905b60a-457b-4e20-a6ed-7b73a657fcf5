package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.util.sysconfig.SysConfigurationHolder;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.service.facade.domain.cache.QueryChannelKeyRequest;
import com.uaepay.cmf.service.facade.domain.cache.QueryChannelKeyResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 08/03/2024 09:47
 */
@Component
public class ChannelKeyQueryProcessor extends GeneralProcessorTemplate<QueryChannelKeyRequest, QueryChannelKeyResponse> {

    @Autowired
    private SysConfigurationHolder sysConfigurationHolder;

    @Override
    protected String getServiceName() {
        return "QUERY_CHANNEL_KEY";
    }

    @Override
    protected QueryChannelKeyResponse createResponse() {
        return new QueryChannelKeyResponse();
    }

    @Override
    protected void process(QueryChannelKeyRequest queryChannelKeyRequest, QueryChannelKeyResponse queryChannelKeyResponse) {
        String keyValue = sysConfigurationHolder.loadConfigureOrDefault(queryChannelKeyRequest.getKey(), null);
        queryChannelKeyResponse.setKeyValue(keyValue);
        queryChannelKeyResponse.setApplyStatus(ApplyStatusEnum.SUCCESS);
    }
}
