package com.uaepay.cmf.domainservice.main.result;

import com.uaepay.cmf.common.core.domain.enums.CommunicateStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.domain.ChannelResult;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.domainservice.main.convert.ChannelControlResultConverter;
import com.uaepay.cmf.domainservice.main.process.AsyncVoidTransactionService;
import com.uaepay.cmf.domainservice.main.process.NotifyBindCardInfoService;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.integration.ues.UesClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ControlResultProcessor.java v1.0  2020-09-12 10:24
 */
@Slf4j
@Service
public class ControlResultProcessor extends AbstractResultProcessor<InstControlOrder, ChannelResult, InstControlOrderResult> {

    @Resource
    private NotifyBindCardInfoService notifyBindCardInfoService;

    @Resource
    private AsyncVoidTransactionService asyncVoidTransactionService;

    @Resource
    private InstResultProcessor instResultProcessor;

    @Resource
    InstOrderRepository instOrderRepository;

    @Resource
    private UesClient uesClient;

    @Override
    protected void validateResult(InstControlOrder order, InstControlOrderResult instControlOrderResult) {

    }

    @Override
    public InstControlOrderResult convert2InstResp(InstControlOrder order, ChannelResult channelResult) {
        return ChannelControlResultConverter.convert(order, channelResult);
    }

    @Override
    protected void storeResult(InstControlOrder order, InstControlOrderResult controlResult) {

        log.info("controlOrder:{},controlResult:{}", order, controlResult);
        // 1.更新订单发送状态
        updateOrderCommunicateStatus(order, controlResult);

        // 2. 保存或更新结果
        controlOrderResultRepository.storeOrUpdate(controlResult);

        // 3. 更新机构订单状态，保存扩展信息
        updateOrderStatusAndExt(order, controlResult);
    }

    private void updateOrderStatusAndExt(InstControlOrder order, InstControlOrderResult controlResult) {
        saveBankReceiveService.saveExtension(order, controlResult, ChannelHolder.get());

        InstOrderStatus orderStatus = instOrderFinalStatusMap.get(controlResult.getStatus());
        if (orderStatus == null) {
            return;
        }
        controlOrderRepository.updateInstControlOrderStatus(order, orderStatus);
    }

    private void updateOrderCommunicateStatus(InstControlOrder order, InstControlOrderResult controlResult) {
        CommunicateStatus targetStatus = targetCommStatusMap.get(controlResult.getProcessStatus());
        Assert.notNull(targetStatus, "未找到目标发送状态");
        int modCount = 0;
        if (statusMapping.get(targetStatus).contains(order.getCommunicateStatus())
        ) {
            modCount = controlOrderRepository.updateCommunicateStatusByIdAndPreStatus(order,
                    targetStatus, order.getCommunicateStatus());
        }
        Assert.isTrue(modCount == 1, "订单发送状态更新失败");
    }

    @Override
    protected void laterProcess(InstControlOrder instControlOrder, InstControlOrderResult instControlResult) {

        ResultLaterProcessorEnum.getResultLaterProcessor(instControlOrder.getApiType())
                .ifPresent(process -> process.process(instControlOrder, instControlResult));
        // 控制单异步撤销处理
        asyncVoidTransactionService.processControlVoidTx(instControlOrder, instControlResult);
        // 通知收银台绑卡
        notifyCashdeskService.notifyBindCard(instControlOrder, instControlResult);
        // 通知member front绑卡结果
        notifyBindCardInfoService.notifyInfo(instControlOrder, instControlResult);
    }

}
