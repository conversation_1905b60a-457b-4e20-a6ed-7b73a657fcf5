package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.domainservice.main.convert.CmfRequestConverter;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderResultRepository;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.service.facade.domain.register.APlusMerchantRegisterRequest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version: APlusMerchantRegisterProcessor.class v1.0
 */
@Service
public class APlusMerchantRegisterProcessor extends GeneralProcessorTemplate<APlusMerchantRegisterRequest, CommonResponse> {

    @Resource
    protected SubmitInstitutionService submitInstitutionService;
    @Resource
    protected InstControlOrderRepository instControlOrderRepository;
    @Resource
    protected InstControlOrderResultRepository instControlOrderResultRepository;

    @Override
    protected String getServiceName() {
        return "APlusMerchantRegisterProcessor";
    }

    @Override
    protected CommonResponse createResponse() {
        return new CommonResponse();
    }

    @Override
    protected void process(APlusMerchantRegisterRequest request, CommonResponse response) {
        InstControlOrder order = CmfRequestConverter.convert(request);
        response.setApplyStatus(ApplyStatusEnum.SUCCESS);

        InstControlOrder instControlOrder = instControlOrderRepository.loadLatestByRequestNoAndApiType(request.getRequestNo(), order.getApiType().getCode());
        if (instControlOrder != null) {
            supplementResult(instControlOrder, response);
            return;
        }
        InstControlOrderResult controlResult = submitInstitutionService.submit(order);
        response.setMessage(controlResult.getResultMessage());
        response.setCode(controlResult.getStatus().getCode());
        response.setUnityResultCode(controlResult.getInstResultCode());
    }

    private void supplementResult(InstControlOrder instControlOrder, CommonResponse response) {
        List<InstControlOrderResult> controlResultList = instControlOrderResultRepository.loadByOrderId(instControlOrder.getOrderId());
        if(CollectionUtils.isNotEmpty(controlResultList)){
            response.setCode(controlResultList.get(0).getStatus().getCode());
            response.setMessage(controlResultList.get(0).getResultMessage());
        }else{
            response.setCode(InstOrderResultStatus.UNKNOWN.getCode());
            response.setMessage(InstOrderResultStatus.UNKNOWN.name());
        }
    }

}
