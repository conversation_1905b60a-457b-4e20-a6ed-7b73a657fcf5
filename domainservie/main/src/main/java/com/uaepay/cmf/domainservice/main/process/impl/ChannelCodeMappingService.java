package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.dal.daointerface.ChannelCodeMappingDAO;
import com.uaepay.cmf.common.core.dal.dataobject.ChannelCodeMappingDO;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.uaepay.cmf.common.core.domain.constants.BasicConstant.CACHE_NAMESPACE_CHANNEL_CODE_MAPPING;

@Service
@Slf4j
public class ChannelCodeMappingService {

    @Resource
    private ChannelCodeMappingDAO channelCodeMappingDAO;

    // Cache for compiled scripts
    private final Map<String, Script> scriptCache = new ConcurrentHashMap<>();
    private final GroovyShell groovyShell = new GroovyShell();

    // Pattern to match variable names followed by comparison operators (e.g., ==, !=, <, >, <=, >=)
    private final Pattern comparisonPattern = Pattern.compile("\\b([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*([=!<>]=|[<>])");

    // Pattern to match variable names followed by string methods (e.g., contains, startsWith, endsWith, matches)
    private final Pattern stringMethodPattern = Pattern.compile("\\b([a-zA-Z_$][a-zA-Z0-9_$]*)\\.(contains|startsWith|endsWith|matches)\\s*\\(");

    /**
     * Expression examples:
     * 1. Simple comparison:
     *    orderType == 'I' && payMode == 'BALANCE'
     *
     * 2. String contains:
     *    merchantId.contains('TEST')
     *
     * 3. String starts with:
     *    merchantId.startsWith('TEST_')
     *
     * 4. String ends with:
     *    merchantId.endsWith('_MERCHANT')
     *
     * 5. Multiple conditions:
     *    orderType == 'I' && (merchantId.contains('TEST') || payMode == 'BALANCE')
     * 
     * 6. Null check:
     *    merchantId != null && merchantId.startsWith('TEST')
     * 
     */

    /**
     * Refresh cache every 1 hour
     */

    /**
     * Get channel code mapping rules from all valid rules by oldChannelCode
     * 
     * @param mappings all valid mapping rules
     * @param oldChannelCode the original channel code to look up
     * @return list of mapping rules for the oldChannelCode, sorted by priority
     */
    public List<ChannelCodeMappingDO> getTheMappingDataFromAll(List<ChannelCodeMappingDO> mappings, String oldChannelCode) {
        if (CollectionUtils.isEmpty(mappings)) {
            return null;
        }
        Map<String, List<ChannelCodeMappingDO>> groupedRules = new HashMap<>();
        try {
            // Group by oldChannelCode
            groupedRules = mappings.stream()
                    .collect(Collectors.groupingBy(
                            ChannelCodeMappingDO::getOldChannelCode,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    list -> {
                                        // Ensure rules are sorted by priority
                                        list.sort((a, b) -> b.getPriority().compareTo(a.getPriority()));
                                        return Collections.unmodifiableList(list);
                                    }
                            )
                    ));
        } catch (Exception e) {
            log.error("Failed to get channel code mapping rules cache", e);
        }
        return groupedRules.get(oldChannelCode);
    }

    /**
     * Get new channel code based on mapping rules
     * 
     * @param oldChannelCode the original channel code
     * @param instOrder order information containing context for rule evaluation
     * @return the new channel code if a matching rule is found, otherwise returns the original code
     */
    public String getNewChannelCode(String oldChannelCode, InstOrder instOrder) {
        long startTime = System.nanoTime();
        try {
            if (StringUtils.isBlank(oldChannelCode) || instOrder == null) {
                return oldChannelCode;
            }

            // Get rules from cache
            List<ChannelCodeMappingDO> allValidRules = allValidRules();
            List<ChannelCodeMappingDO> mappings = getTheMappingDataFromAll(allValidRules, oldChannelCode);
            if (CollectionUtils.isEmpty(mappings)) {
                return oldChannelCode;
            }

            // Create binding with variables
            Binding binding = createBinding(instOrder);

            // Evaluate rules until a match is found
            for (ChannelCodeMappingDO mapping : mappings) {
                try {
                    if (evaluateExpression(mapping.getMatchExpression(), binding)) {
                        return mapping.getNewChannelCode();
                    }
                } catch (Exception e) {
                    log.warn("Rule [{}] expression parsing failed: {}", mapping.getRuleName(), e.getMessage(), e);
                }
            }

            return oldChannelCode;
        } catch (Exception e) {
            log.error("getNewChannelCode.error,", e);
            return oldChannelCode;
        } finally {
            if (log.isDebugEnabled()) {
                log.debug("getNewChannelCode took {}ms", (System.nanoTime() - startTime) / 1_000_000.0);
            }
        }
    }

    /**
     * Get all valid channel code mapping rules
     * Results are cached with key determined by method name
     */
    @Cacheable(cacheNames = CACHE_NAMESPACE_CHANNEL_CODE_MAPPING, key = "#root.method")
    public List<ChannelCodeMappingDO> allValidRules() {
        return channelCodeMappingDAO.selectAllValidRules();
    }


    /**
     * Create binding with variables from InstOrder
     * 
     * @param instOrder the order containing data to be bound
     * @return binding object with all variables set
     */
    private Binding createBinding(InstOrder instOrder) {
        Binding binding = new Binding();

        // Set basic variables
        binding.setVariable("instCode", instOrder.getInstCode());
        binding.setVariable("orderType", instOrder.getBizType() != null ? instOrder.getBizType().getCode() : null);
        binding.setVariable("paymentCode", instOrder.getPaymentCode());
        binding.setVariable("payMode", instOrder.getPayMode() != null ? instOrder.getPayMode().getCode() : null);
        binding.setVariable("merchantId", instOrder.getMerchantId());

        // Set extension fields if available
        if (instOrder.getExtension() != null) {
            instOrder.getExtension().forEach(binding::setVariable);
        }

        return binding;
    }

    /**
     * Evaluate Groovy expression with the given binding
     * 
     * @param expressionString the Groovy expression to evaluate
     * @param binding the variable binding to use during evaluation
     * @return true if expression evaluates to true, false otherwise
     */
    private boolean evaluateExpression(String expressionString, Binding binding) {
        try {
            // Extract all variable names from the expression
            Set<String> requiredVariables = extractVariablesFromExpression(expressionString);

            // Check if all required variables exist in the binding
            for (String variable : requiredVariables) {
                if (!binding.hasVariable(variable)) {
                    log.debug("Missing variable in binding: {} for expression: {}", variable, expressionString);
                    binding.setVariable(variable, null); // Set missing variables to null to prevent errors
                }
            }

            // Get or create script from cache
            Script script = scriptCache.computeIfAbsent(expressionString,
                    key -> groovyShell.parse(expressionString));

            // Set new binding and execute
            script.setBinding(binding);

            Object result = script.run();
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.warn("Expression evaluation failed: {}", expressionString, e);
            return false;
        }
    }

    /**
     * Extract variable names from an expression
     * 
     * @param expressionString the expression to analyze
     * @return set of variable names used in the expression
     */
    private Set<String> extractVariablesFromExpression(String expressionString) {
        Set<String> variables = new HashSet<>();
        
        // Thread-local matcher to avoid thread safety issues
        // Instead of using synchronized block, create new matcher instances
        Matcher comparisonMatcher = comparisonPattern.matcher(expressionString);
        while (comparisonMatcher.find()) {
            String varName = comparisonMatcher.group(1);
            if (!isGroovyKeywordOrLiteral(varName)) {
                variables.add(varName);
            }
        }
        
        // Match variables used with string methods (x.contains(), x.startsWith(), etc.)
        Matcher stringMethodMatcher = stringMethodPattern.matcher(expressionString);
        while (stringMethodMatcher.find()) {
            String varName = stringMethodMatcher.group(1);
            if (!isGroovyKeywordOrLiteral(varName)) {
                variables.add(varName);
            }
        }
        
        return variables;
    }
    
    /**
     * Check if a string is a Groovy keyword or literal
     * 
     * @param word the word to check
     * @return true if the word is a keyword or literal, false otherwise
     */
    private boolean isGroovyKeywordOrLiteral(String word) {
        // Common Groovy/Java keywords and operators
        Set<String> keywords = new HashSet<>(Arrays.asList(
            "true", "false", "null", "if", "else", "while", "for", "return", 
            "new", "class", "instanceof", "this", "super", "void", "import", 
            "package", "try", "catch", "finally", "throw", "throws", "switch", 
            "case", "default", "break", "continue", "public", "private", 
            "protected", "static", "final", "abstract", "native", "synchronized", 
            "transient", "volatile", "strictfp", "extends", "implements", 
            "interface", "enum", "assert", "boolean", "byte", "char", "double", 
            "float", "int", "long", "short", "def", "in", "as", "trait"
        ));
        return keywords.contains(word);
    }
}