package com.uaepay.cmf.domainservice.main.retry;

import java.util.Date;

import javax.annotation.Resource;

import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version InstOrderRetryService.java 1.0 Created@2017-12-28 16:03 $
 */
public class InstOrderRetryService extends AbstractOrderRetryService {

    @Resource
    private InstOrderRepository instOrderRepository;

    @Override
    int updateRetryInfoById(int retryTimes, Date retryDateTime, Long orderId) {
        return instOrderRepository.updateRetryInfoById(retryTimes, retryDateTime, orderId);
    }
}
