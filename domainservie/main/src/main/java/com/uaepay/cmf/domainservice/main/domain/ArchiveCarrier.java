package com.uaepay.cmf.domainservice.main.domain;

import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.router.service.facade.domain.channel.ChannelBatchArchiveVO;
import lombok.Builder;
import lombok.Data;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ArchiveCarrier.java v1.0
 */
@Builder
@Data
public class ArchiveCarrier {

    private ChannelBatchArchiveVO batchArchive;

    private ChannelApiVO channelApi;

    private String operator;


}
