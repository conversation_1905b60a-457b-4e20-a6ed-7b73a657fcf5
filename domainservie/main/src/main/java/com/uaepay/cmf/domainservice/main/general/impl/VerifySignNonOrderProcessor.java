package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.exception.DuplicateRequestException;
import com.uaepay.cmf.common.core.domain.institution.InstBaseResult;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.spi.VerifySignService;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date VerifySignProcessor.java v1.0
 */
@Slf4j
@Service
public class VerifySignNonOrderProcessor extends GeneralProcessorTemplate<VerifySignRequest, VerifySignResult> {

    @Resource
    private VerifySignService verifySignService;

    @Override
    protected String getServiceName() {
        return "VerifySignNonOrderProcessor";
    }

    @Override
    protected void businessValidate(VerifySignRequest request) throws DuplicateRequestException {
    }

    @Override
    protected void resolveDuplicateResponse(VerifySignRequest request, VerifySignResult verifyResult, String message) {
        buildVerifySignResult(verifyResult, null);
    }

    @Override
    protected VerifySignResult createResponse() {
        return new VerifySignResult();
    }

    @Override
    protected void process(VerifySignRequest request, VerifySignResult verifyResult) {

        InstBaseResult resp = null;

        if (request.isAsync()) {
            verifySignService.asyncVerifyReqNonOrder(request);
        } else {
            resp = verifySignService.verifyReqNonOrder(request);
        }
        buildVerifySignResult(verifyResult, resp);
    }

    private void buildVerifySignResult(VerifySignResult verifyResult, InstBaseResult instBaseResult) {
        verifyResult.setApplyStatus(ApplyStatusEnum.SUCCESS);

        if (instBaseResult != null) {
            verifyResult.setInstOrderNo(instBaseResult.getInstOrderNo());
            verifyResult.setCode(ApplyStatusEnum.SUCCESS.getCode());
            verifyResult.setMessage(ApplyStatusEnum.SUCCESS.getMessage());
            verifyResult.setUnityResultCode(instBaseResult.getInstResultCode());
            verifyResult.setExtMap(instBaseResult.getExtension());
        }
    }
}
