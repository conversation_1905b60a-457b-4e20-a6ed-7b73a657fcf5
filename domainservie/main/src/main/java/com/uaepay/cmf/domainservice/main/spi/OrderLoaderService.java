package com.uaepay.cmf.domainservice.main.spi;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;

/**
 * <p>获取订单.</p>
 *
 * <AUTHOR>
 * @version OrderLoaderService.java 1.0 Created@2017-12-13 11:18 $
 */
public interface OrderLoaderService extends BasicConstant {

    /**
     *
     * 获取原订单
     * @param requestType
     * @param requestNo
     * @param settlementId
     * @param sourceOrder
     * @return
     */
    InstBaseOrder loadPreOrder(String requestType, String requestNo, String settlementId,
                               String sourceOrder);

    /**
     * 获取返回Payment的订单号
     * @param instOrder
     * @param result
     * @return
     */
    String loadReturnOrderNo(InstOrder instOrder, InstOrderResult result);
}
