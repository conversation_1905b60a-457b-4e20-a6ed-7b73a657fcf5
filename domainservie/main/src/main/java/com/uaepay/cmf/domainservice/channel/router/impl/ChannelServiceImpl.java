package com.uaepay.cmf.domainservice.channel.router.impl;

import com.uaepay.cmf.domainservice.channel.router.ChannelService;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date ChannelServiceImpl.java v1.0
 */
@Service
public class ChannelServiceImpl implements ChannelService {

    @Resource
    private RouterClient routerClient;

    @Override
    @Cacheable(cacheNames = CACHE_NAMESPACE_CHANNEL_CONFIG, key = "#channelCode + '-is3ds'")
    public boolean is3ds2Channel(String channelCode) {
        return routerClient.is3ds2Channel(channelCode);
    }



}
