package com.uaepay.cmf.domainservice.channel.router.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.uaepay.basis.beacon.common.util.JsonUtil;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.exception.CmfBizException;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.util.RouteUtil;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.common.enums.RequestType;
import com.uaepay.cmf.domainservice.channel.limit.LimitService;
import com.uaepay.cmf.domainservice.main.spi.OrderLoaderService;
import com.uaepay.cmf.fss.ext.integration.beneficiary.BeneficiaryInfo;
import com.uaepay.cmf.fss.ext.integration.beneficiary.QueryBeneficiaryInfoClient;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.cmf.service.facade.domain.fundout.QueryFundoutBeneficiaryInfoRequest;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.router.service.facade.domain.RouteRequest;
import com.uaepay.router.service.facade.domain.RouteResponse;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import com.uaepay.schema.cmf.enums.BizType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

/**
 * <p>Cmf订单路由 - 用于资金类订单找新渠道</p>
 *
 * <AUTHOR>
 * @date CmfOrderChannelRouter.java v1.0  2020-09-08 15:04
 */
@Service
public class CmfOrderChannelRouter extends AbstractChannelRouter<CmfOrder> {

    @Resource
    protected RouterClient routerClient;
    @Resource
    protected OrderLoaderService orderLoaderService;
    @Resource
    protected LimitService limitService;

    @Resource
    protected QueryBeneficiaryInfoClient beneficiaryInfoClient;

    @Value("${channel.refund.change.out:LEAN101}")
    private  String NEED_CHANGE_TO_FUNDOUT;

    @Override
    protected void beforeRoute(CmfOrder cmfOrder) {
        configurationService.beforeRoute(cmfOrder);
        // grc消费限额交易
        limitService.validateLimit(cmfOrder);
    }

    @Override
    protected RouteResponse<ChannelVO> routeCustom(CmfOrder cmfOrder) {
        if (cmfOrder.getBizType() == BizType.REFUND) {
            return routeRefund(cmfOrder);
        }
        RouteRequest req = convertRequest(cmfOrder);
        return routerClient.route(req);
    }

    private RouteResponse<ChannelVO> routeRefund(CmfOrder cmfOrder) {
        InstBaseOrder preOrder = orderLoaderService.loadPreOrder(cmfOrder.getRequestType()
                .name(), cmfOrder.getOrgiPaymentSeqNo(), cmfOrder.getOrgiSettlementId(), null);
        Assert.notNull(preOrder, "退款交易原订单不可为空!");

        //退款转出款
        if(NEED_CHANGE_TO_FUNDOUT.contains(preOrder.getFundChannelCode())){
            reFillCmfOrder(cmfOrder, preOrder);
            RouteRequest req = convertRequest(cmfOrder);
            return routerClient.route(req);
        }

        // 退款
        return routerClient.route(RouteUtil.getParam(preOrder.getFundChannelCode(), FundChannelApiType.SINGLE_REFUND, true));
    }

    private void reFillCmfOrder(CmfOrder cmfOrder, InstBaseOrder preOrder) {
        QueryFundoutBeneficiaryInfoRequest request = new QueryFundoutBeneficiaryInfoRequest();
        request.setClientId(CLIENT_ID);
        request.setMemberId(cmfOrder.getMemberId());
        // 后续如果有其他渠道有需求，这块参数需要扩展
        String beneficiaryInfoId = preOrder.getExtension().get(ACCOUNT_ID);
        request.setBeneficiaryInfoId(beneficiaryInfoId);
        BeneficiaryInfo beneficiaryInfo = beneficiaryInfoClient.queryBeneficiaryInfo(preOrder.getFundChannelCode(), request);
        if(beneficiaryInfo == null || StringUtils.isBlank(beneficiaryInfo.getMemberId())){
            throw new CmfBizException(ErrorCode.QUERY_BENEFICIARY_INFO_FAIL);
        }
        Map<String, String> extension = cmfOrder.getExtension();
        String jsonString = null;
        try {
            jsonString = JsonUtil.toJsonString(beneficiaryInfo);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Map<String, String> map = JsonUtil.parseMap(jsonString);
        extension.putAll(map);
        cmfOrder.setExtension(extension);
        cmfOrder.setBizType(BizType.FUNDOUT);
        cmfOrder.setInstCode(beneficiaryInfo.getTargetInst());
        cmfOrder.setRequestType(RequestType.FUND_OUT);
        cmfOrder.setPayMode(PayMode.BALANCE);
    }

    private RouteRequest convertRequest(CmfOrder cmfOrder) {
        RouteRequest request = new RouteRequest();
        BeanUtils.copyProperties(cmfOrder, request);
        request.setClientId(CLIENT_ID);
        request.setBizProductCode(cmfOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.key));
        request.setBizType(cmfOrder.getBizType().getCode());
        request.setCompanyOrPersonal(cmfOrder.getExtension().get(ExtensionKey.COMPANY_OR_PERSONAL.key));
        request.setDbcr(cmfOrder.getExtension().get(ExtensionKey.DBCR.key));
        request.setPayMode(cmfOrder.getPayMode().getCode());
        request.setRequestNo(cmfOrder.getOrderSeqNo());
        if (cmfOrder.getRequestType() != null) {
            request.setRequestType(cmfOrder.getRequestType().getCode());
        }

        request.setWhiteChannel(cmfOrder.getExtension().get(ExtensionKey.WHITE_CHANNEL_CODE.key));
        String blackChannels = cmfOrder.getExtension().get(ExtensionKey.BLACK_CHANNEL_LIST.key);
        if(StringUtils.isNotEmpty(blackChannels)) {
            request.setBlackChannelList(Arrays.asList(blackChannels.split(CHAR_COMMA)));
        }
        // 设置路由失败结果，若路由失败则会返回这个结果
        request.setRouteFailResult(cmfOrder.getExtension().get(ExtensionKey.UNITY_RESULT_CODE.getKey()));
        cmfOrder.getExtension().remove(ExtensionKey.BLACK_CHANNEL_LIST.key);
        cmfOrder.getExtension().remove(ExtensionKey.UNITY_RESULT_CODE.key);
        return request;
    }
}
