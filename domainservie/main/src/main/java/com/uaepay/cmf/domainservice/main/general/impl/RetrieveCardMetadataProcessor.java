package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.domainservice.main.convert.CmfRequestConverter;
import com.uaepay.cmf.domainservice.main.convert.InstControlOrderResultConverter;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.service.facade.domain.card.RetrieveCardMetadataRequest;
import com.uaepay.cmf.service.facade.domain.card.RetrieveCardMetadataResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 21/02/2025 14:26
 */
@Service
public class RetrieveCardMetadataProcessor extends GeneralProcessorTemplate<RetrieveCardMetadataRequest, RetrieveCardMetadataResponse> {

    @Resource
    private SubmitInstitutionService submitInstitutionService;

    @Override
    protected String getServiceName() {
        return "RetrieveCardMetadataProcessor";
    }

    @Override
    protected RetrieveCardMetadataResponse createResponse() {
        return new RetrieveCardMetadataResponse();
    }

    @Override
    protected void process(RetrieveCardMetadataRequest request, RetrieveCardMetadataResponse retrieveCardMetadataResponse) {
        InstControlOrder controlOrder = CmfRequestConverter.createRetrieveCardOrder(request);
        InstControlOrderResult resp = submitInstitutionService.submit(controlOrder);
        InstControlOrderResultConverter.convert(resp, retrieveCardMetadataResponse);
    }
}
