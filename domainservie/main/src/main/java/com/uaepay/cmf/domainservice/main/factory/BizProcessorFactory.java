package com.uaepay.cmf.domainservice.main.factory;

import com.uaepay.cmf.domainservice.main.process.biz.BizProcessor;
import com.uaepay.schema.cmf.enums.BizType;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date BizProcessorFactory.java v1.0  2020-09-06 14:50
 */
@Service
public class BizProcessorFactory {

    private Map<BizType, BizProcessor> bizProcessorMap = new ConcurrentHashMap<>(8);

    public void put(BizType bizType, BizProcessor processor) {
        bizProcessorMap.put(bizType, processor);
    }

    public BizProcessor get(BizType bizType) {
        BizProcessor processor = bizProcessorMap.get(bizType);
        Assert.notNull(processor, "Unsupported processor type!");
        return processor;
    }

}
