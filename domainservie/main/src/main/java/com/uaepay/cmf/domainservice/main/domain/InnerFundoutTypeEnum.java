package com.uaepay.cmf.domainservice.main.domain;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date InnerFundoutTypeEnum.java v1.0
 */
public enum InnerFundoutTypeEnum {
    //
    REMITTANCE("remittance", "外币汇款"),
    FUNDOUT("fundout", "出款");;

    private String code;
    private String message;

    InnerFundoutTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static InnerFundoutTypeEnum getByCode(String code){
        for(InnerFundoutTypeEnum item:values()){
            if(item.getCode().equals(code)){
                return item;
            }
        }
        return null;
    }

    public static boolean isRemittance(String code){
        return getByCode(code) == InnerFundoutTypeEnum.REMITTANCE;
    }
}
