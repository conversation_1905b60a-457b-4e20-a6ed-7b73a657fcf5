package com.uaepay.cmf.domainservice.main.spi.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;

/**
 * <p>
 * 重新提交订单
 * </p>
 *
 * <AUTHOR>
 * @version $Id: DefaultReSubmitInstitutionService.java, v 0.1 2013-8-13 上午10:51:35 liumaoli Exp $
 */
@Service
public class DefaultReSubmitInstitutionService extends AbstractReSubmitInstitutionService {

    @Override
    public BaseResult reSubmit(List<String> cmfSeqNos, String fundChannelCode) {
        Map<CmfOrder, List<InstOrder>> orders = loadInstOrder(filter(cmfSeqNos), fundChannelCode);

        // 校验
        for (Entry<CmfOrder, List<InstOrder>> entry : orders.entrySet()) {
            validate(entry.getKey(), entry.getValue());
        }

        return reSubmit(orders);
    }

    /**
     * 装载订单,没有则返回异常
     *
     * @param cmfSeqNos
     * @return
     */
    private Map<CmfOrder, List<InstOrder>> loadInstOrder(List<String> cmfSeqNos, String whiteFundChannelCode) {
        Map<CmfOrder, List<InstOrder>> map = new HashMap<>();

        for (String cmfSeqNo : cmfSeqNos) {
            // 装载cmf订单
            CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(cmfSeqNo, false);
            Assert.notNull(cmfOrder, "cmfSeqNo[" + cmfSeqNo + "]cmf订单不存在");
            cmfOrder.getExtension().put(ExtensionKey.WHITE_CHANNEL_CODE.key, whiteFundChannelCode);

            // 装载机构订单,判断拆分情况
            List<InstOrder> instOrderlist = new ArrayList<>();
            instOrderlist.addAll(instOrderRepository.loadByCmfSeqNo(cmfSeqNo));

            Assert.isTrue(CollectionUtils.isNotEmpty(instOrderlist), "cmfSeqNo[" + cmfSeqNo + "]机构订单不存在");
            map.put(cmfOrder, instOrderlist);
        }
        return map;
    }
}
