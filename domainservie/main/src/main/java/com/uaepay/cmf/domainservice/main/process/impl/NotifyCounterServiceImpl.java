package com.uaepay.cmf.domainservice.main.process.impl;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstRefundOrder;
import com.uaepay.cmf.domainservice.main.process.NotifyCounterService;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.fss.ext.integration.counter.CounterClient;
import com.uaepay.cmf.service.facade.domain.counter.RefundOrder;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date NotifyCounterServiceImpl.java v1.0  11/22/20 5:39 PM
 */
@Slf4j
@Service
public class NotifyCounterServiceImpl implements NotifyCounterService {

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private CounterClient counterClient;

    @Value("${counter.notifyRefunds.channels:MC101,MC104,MC105,CS101,CS102,CS103,CS104,CS105,CS106}")
    private String allowNotifyChannels;

    @Value("${counter.notify.switch:N}")
    private String counterNotifySwitch;

    private static final String ORDER_REFUND = "B";
    private static final String ORDER_CANCEL = "C";

    @Override
    public void notifyRefundOrder(InstOrder instOrder) {
        if (instOrder == null || instOrder.getStatus() != InstOrderStatus.SUCCESSFUL || instOrder.getBizType() != BizType.REFUND || !allowNotify(instOrder.getFundChannelCode())) {
            log.info("refundOrder.needNotNotify!");
            return;
        }
        counterClient.sendOrder(transfer2RefundOrder(instOrder));
    }

    /**
     * 允许通知
     *
     * @param fundChannelCode
     * @return
     */
    private boolean allowNotify(String fundChannelCode) {
        return allowNotifyChannels.contains(fundChannelCode) && YesNo.YES.getCode().equals(counterNotifySwitch);
    }

    private RefundOrder transfer2RefundOrder(InstOrder instOrder) {
        RefundOrder refundOrder = new RefundOrder();
        BeanUtils.copyProperties(instOrder, refundOrder);
        refundOrder.setGmtSubmit(instOrder.getGmtBookingSubmit());
        refundOrder.setBizType(instOrder.getBizType().getCode());
        refundOrder.setStatus(instOrder.getStatus().getCode());
        String bizProductCode = instOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.getKey());

        InstRefundOrder refundInstOrder = (InstRefundOrder) instOrder;
        InstOrder fundInOrder = instOrderRepository.loadByNo(refundInstOrder.getFundinOrderNo());
        // 退款订单取原订单业务产品码
        if (StringUtils.isEmpty(bizProductCode)) {
            bizProductCode = fundInOrder.getExtension().get(ExtensionKey.BIZ_PRODUCT_CODE.getKey());
        }
        refundOrder.setBizProductCode(bizProductCode);
        refundOrder.setMemberId(instOrder.getExtension().get(ExtensionKey.MEMBER_ID.getKey()));
        refundOrder.setProductOrderNo(instOrder.getExtension().get(ExtensionKey.PRODUCT_ORDER_NO.key));
        refundOrder.setPaymentOrderNo(instOrder.getExtension().get(ExtensionKey.PAYMENT_ORDER_NO.key));

        refundOrder.setGmtOriginal(fundInOrder.getGmtBookingSubmit());
        refundOrder.setOriginalAmount(fundInOrder.getAmount());
        refundOrder.setOriginalOrderNo(fundInOrder.getInstOrderNo());

        String isCancel = refundInstOrder.getExtension().get(ExtensionKey.IS_CANCEL.key);
        refundOrder.setRefundType(YesNo.getByCode(isCancel) == YesNo.YES ? ORDER_CANCEL : ORDER_REFUND);

        return refundOrder;
    }


}
