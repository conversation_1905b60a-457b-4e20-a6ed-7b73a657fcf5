package com.uaepay.cmf.domainservice.main.retry;

import java.util.Date;

import javax.annotation.Resource;

import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import org.springframework.stereotype.Service;

/**
 * <p>.</p>
 *
 * <AUTHOR>
 * @version BatchOrderRetryService.java 1.0 Created@2017-12-28 16:04 $
 */
@Service
public class BatchOrderRetryService extends AbstractOrderRetryService {

    @Resource
    private InstOrderRepository instOrderRepository;

    @Override
    int updateRetryInfoById(int retryTimes, Date retryDateTime, Long orderId) {
        return instOrderRepository.updateBatchRetryInfoById(retryTimes, retryDateTime, orderId);
    }
}
