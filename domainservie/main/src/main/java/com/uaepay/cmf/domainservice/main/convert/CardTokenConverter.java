package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.dal.dataobject.CardTokenDO;
import com.uaepay.cmf.common.core.domain.enums.TokenTypeEnum;
import com.uaepay.cmf.common.core.domain.vo.CardToken;
import com.uaepay.schema.cmf.enums.YesNo;
import org.springframework.beans.BeanUtils;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date CardTokenConverter.java v1.0  2020-03-28 15:51
 */
public class CardTokenConverter {

    private CardTokenConverter() {

    }

    public static CardToken convert(CardTokenDO cardTokenDO) {
        if (cardTokenDO == null) {
            return null;
        }
        CardToken cardToken = new CardToken();
        BeanUtils.copyProperties(cardTokenDO, cardToken);
        cardToken.setTokenType(TokenTypeEnum.getByCode(cardTokenDO.getTokenType()));
        cardToken.setFirstBind(YesNo.getByCode(cardTokenDO.getFirstBind()));
        return cardToken;
    }

    public static CardTokenDO convert(CardToken cardToken) {
        if (cardToken == null) {
            return null;
        }
        CardTokenDO cardTokenDO = new CardTokenDO();
        BeanUtils.copyProperties(cardToken, cardTokenDO);
        if (cardToken.getTokenType() != null) {
            cardTokenDO.setTokenType(cardToken.getTokenType().getCode());
        }
        if (cardToken.getFirstBind() != null) {
            cardTokenDO.setFirstBind(cardToken.getFirstBind().getCode());
        }
        return cardTokenDO;
    }
}
