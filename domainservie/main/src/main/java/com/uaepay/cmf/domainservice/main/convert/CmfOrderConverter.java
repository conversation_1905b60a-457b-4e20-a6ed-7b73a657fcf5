package com.uaepay.cmf.domainservice.main.convert;

import com.uaepay.cmf.common.core.dal.dataobject.CmfOrderDO;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderConfirmStatus;
import com.uaepay.cmf.common.core.domain.enums.CmfOrderStatus;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.NotifyStatus;
import com.uaepay.cmf.common.core.engine.util.CommonConverter;
import com.uaepay.cmf.common.enums.RequestType;
import org.apache.commons.lang3.StringUtils;
import com.uaepay.common.util.DateUtil;
import com.uaepay.payment.common.v2.enums.PayMode;
import com.uaepay.schema.cmf.enums.BizType;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * CMF订单转换器
 * </p>
 *
 * <AUTHOR>
 * @version $Id: CmfOrderConverter.java, v 0.1 2012-8-6 下午8:36:19 fuyangbiao Exp $
 */
public class CmfOrderConverter {

    private CmfOrderConverter() {

    }

    /**
     * DO转换为DOMAIN
     *
     * @param cmfOrderDO
     * @return
     */
    public static CmfOrder convert(CmfOrderDO cmfOrderDO) {

        if (cmfOrderDO == null) {
            return null;
        }
        CmfOrder to = new CmfOrder();
        BeanUtils.copyProperties(cmfOrderDO, to);

        if (!StringUtils.isEmpty(cmfOrderDO.getBizDate())) {
            to.setBizTime(DateUtil.parseDateLongFormat(cmfOrderDO.getBizDate().trim()));
        }

        to.setStatus(CmfOrderStatus.getByCode(cmfOrderDO.getStatus()));
        to.setConfirmStatus(CmfOrderConfirmStatus.getByCode(cmfOrderDO.getConfirmStatus()));
        to.setPaymentNotifyStatus(NotifyStatus.getByCode(cmfOrderDO.getPaymentNotifyStatus()));

        to.setOrderSeqNo(cmfOrderDO.getCmfSeqNo());

        to.setBizType(BizType.getByCode(cmfOrderDO.getOrderType()));
        to.setPayMode(PayMode.getByCode(cmfOrderDO.getPayMode()));
        if (StringUtils.isNotEmpty(cmfOrderDO.getExtension())) {
            to.setExtension(CommonConverter.convertFromDb(cmfOrderDO.getExtension()));
        }
        to.setFundChannelCode(to.getExtension().get(ExtensionKey.FUNDS_CHANNEL.key));
        switch (to.getBizType()) {
            case FUNDIN: {
                to.setRequestType(RequestType.FUND_IN);
                break;
            }
            case REFUND: {
                to.setRequestType(RequestType.REFUND);
                break;
            }
            case FUNDOUT: {
                to.setRequestType(RequestType.FUND_OUT);
                break;
            }
        }
        return to;
    }

    public static CmfOrderDO convert(CmfOrder from) {
        if (from == null) {
            return null;
        }
        from.getExtension().put(ExtensionKey.FUNDS_CHANNEL.key, from.getFundChannelCode());

        CmfOrderDO cmfOrderDO = new CmfOrderDO();
        BeanUtils.copyProperties(from, cmfOrderDO);
        cmfOrderDO.setCmfSeqNo(from.getOrderSeqNo());
        cmfOrderDO.setBizDate(DateUtil.format(from.getBizTime(), DateUtil.longFormat));

        if (from.getStatus() != null) {
            cmfOrderDO.setStatus(from.getStatus().getCode());
        }
        if (from.getConfirmStatus() != null) {
            cmfOrderDO.setConfirmStatus(from.getConfirmStatus().getCode());
        }
        if (from.getPaymentNotifyStatus() != null) {
            cmfOrderDO.setPaymentNotifyStatus(from.getPaymentNotifyStatus().getCode());
        }
        if (from.getBizType() != null) {
            cmfOrderDO.setOrderType(from.getBizType().getCode());
        }
        if (from.getPayMode() != null) {
            cmfOrderDO.setPayMode(from.getPayMode().getCode());
        }
        cmfOrderDO.setExtension(CommonConverter.convertToDb(from.getExtension()));
        return cmfOrderDO;
    }

    public static List<CmfOrder> convert(List<CmfOrderDO> from) {
        if (from == null || from.isEmpty()) {
            return null;
        }

        List<CmfOrder> list = new ArrayList<>();
        for (CmfOrderDO f : from) {
            list.add(convert(f));
        }

        return list;
    }
}
