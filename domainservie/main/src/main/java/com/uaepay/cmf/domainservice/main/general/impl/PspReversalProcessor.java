package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.ErrorCode;
import com.uaepay.cmf.common.core.domain.enums.InstOrderResultStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.exception.DuplicateRequestException;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.main.convert.CmfRequestConverter;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderResultRepository;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.service.facade.domain.control.psp.PspReversalRequest;
import com.uaepay.cmf.service.facade.domain.control.psp.PspReversalResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date PspReversalProcessor.java v1.0
 */
@Service
public class PspReversalProcessor extends GeneralProcessorTemplate<PspReversalRequest, PspReversalResponse> {

    @Resource
    private InstControlOrderRepository instControlOrderRepository;
    @Resource
    private InstControlOrderResultRepository instControlOrderResultRepository;
    @Resource
    private SubmitInstitutionService submitInstitutionService;

    @Override
    protected String getServiceName() {
        return "PspReversalProcessor";
    }

    @Override
    protected PspReversalResponse createResponse() {
        PspReversalResponse response = new PspReversalResponse();
        response.setApplyStatus(ApplyStatusEnum.SUCCESS);
        return response;
    }

    @Override
    protected void businessValidate(PspReversalRequest request) throws DuplicateRequestException {
        // 业务订单校验
        InstControlOrder controlOrder = instControlOrderRepository.loadByRequestNo(request.getRequestNo());
        if (controlOrder != null) {
            Assert.isTrue(request.getRequestType().equals(controlOrder.getRequestType())
                    && request.getInstCode().equals(controlOrder.getInstCode())
                    && request.getPayMode().equals(controlOrder.getPayMode())
                    && request.getAmount().equals(controlOrder.getAmount()), "订单已存在，但请求参数不同");
            if (controlOrder.getStatus() == InstOrderStatus.SUCCESSFUL || controlOrder.getStatus() == InstOrderStatus.FAILURE) {
                throw new DuplicateRequestException(ErrorCode.WRONG_ORDER_DUPLICATE_PROCESS);
            }
        }
    }

    @Override
    protected void resolveDuplicateResponse(PspReversalRequest request, PspReversalResponse response, String message) {
        InstControlOrder controlOrder = instControlOrderRepository.loadByRequestNo(request.getRequestNo());
        response.setMessage(message);
        resolveResponse(controlOrder, response);
    }

    private void resolveResponse(InstControlOrder controlOrder, PspReversalResponse response) {
        response.setFundsChannel(controlOrder.getFundChannelCode());
        response.setInstOrderNo(controlOrder.getInstOrderNo());
        if(controlOrder.getStatus()==InstOrderStatus.FAILURE){
            response.setStatus(InstOrderStatus.SUCCESSFUL.getCode());
        }else {
            response.setStatus(controlOrder.getStatus().getCode());
        }
        List<InstControlOrderResult> controlResultList = instControlOrderResultRepository.loadByOrderId(controlOrder.getOrderId());
        controlResultList = controlResultList.stream().filter(item -> item.getStatus() == InstOrderResultStatus.SUCCESSFUL || item.getStatus() == InstOrderResultStatus.FAILURE).collect(Collectors.toList());
        if (controlResultList.size() > 0) {
            response.setUnityResultCode(controlResultList.get(0).getInstResultCode());
        }
    }

    @Override
    protected void process(PspReversalRequest request, PspReversalResponse response) {
        // 保存订单
        InstControlOrder controlOrder = instControlOrderRepository.loadByRequestNo(request.getRequestNo());
        InstControlOrderResult result = null;
        if (controlOrder == null) {
            InstControlOrder order = CmfRequestConverter.convert(request, FundChannelApiType.CONTROL_VOID_TRANSACTION);
            result = submitInstitutionService.submit(order);
        } else {
            result = submitInstitutionService.submit(controlOrder, controlOrder.getApiType());
        }
        controlOrder = instControlOrderRepository.loadByRequestNo(request.getRequestNo());
        resolveResponse(controlOrder, response);
    }
}
