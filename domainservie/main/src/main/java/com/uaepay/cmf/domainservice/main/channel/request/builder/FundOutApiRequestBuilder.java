package com.uaepay.cmf.domainservice.main.channel.request.builder;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstFundoutOrder;
import com.uaepay.cmf.common.domain.fundout.FundoutRequest;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>出款请求组装器</p>
 *
 * <AUTHOR>
 * @date FundOutApiRequestBuilder.java v1.0  2020-09-09 17:00
 */
@Service
public class FundOutApiRequestBuilder extends AbstractApiRequestBuilder<InstFundoutOrder, FundoutRequest> {

    @Override
    protected void buildCustomParam(InstFundoutOrder order, FundoutRequest request) {

        BeanUtils.copyProperties(order, request);

        Map<String, String> extMap = order.getExtension();
        request.setAccountType(order.getCompanyOrPersonal());
        String purpose = StringUtils.defaultIfEmpty(order.getPurpose(),
                extMap.get(ExtensionKey.PURPOSE.key));
        request.setPurpose(purpose);

        //身份信息设置
        request.setIdentityNo(extMap.get(ExtensionKey.ID_NO.key));
        request.setIdentityType(extMap.get(ExtensionKey.ID_TYPE.key));
        //手机号设置
        request.setMobilePhone(extMap.get(ExtensionKey.MOBILENO.key));

        Boolean isSameBank = order.getInstCode() != null && order.getInstCode().equalsIgnoreCase(
                ChannelHolder.get().getInstCode());
        //根据收单机构和目标机构比较判断是否是同行
        request.setInnerBank(isSameBank);
    }

    @Override
    public FundoutRequest buildReq() {
        return new FundoutRequest();
    }

    @Override
    public List<FundChannelApiType> getApiTypes() {
        return Arrays.asList(FundChannelApiType.SINGLE_PAY);
    }
}
