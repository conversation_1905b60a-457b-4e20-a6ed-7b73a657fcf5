package com.uaepay.cmf.domainservice.main.general.impl;

import com.uaepay.basis.beacon.common.exception.ErrorException;
import com.uaepay.basis.beacon.service.facade.enums.common.ApplyStatusEnum;
import com.uaepay.cmf.common.core.domain.enums.CacheType;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderType;
import com.uaepay.cmf.common.core.domain.institution.InstBaseOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrder;
import com.uaepay.cmf.common.core.domain.institution.InstControlOrderResult;
import com.uaepay.cmf.common.core.engine.cache.CacheClient;
import com.uaepay.cmf.common.core.engine.cache.CacheLockService;
import com.uaepay.cmf.common.enums.ControlRequestType;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.channel.holder.VerifySignHolder;
import com.uaepay.cmf.domainservice.main.convert.InstControlOrderConverter;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstControlOrderResultRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.SubmitInstitutionService;
import com.uaepay.cmf.service.facade.domain.CmfCommonResultCode;
import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceRequest;
import com.uaepay.cmf.service.facade.domain.advance.CmfAdvanceResult;
import com.uaepay.cmf.service.facade.domain.verify.VerifySignRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date Advance3ds2Processor.java v1.0
 */
@Service
public class Advance3ds2Processor extends GeneralProcessorTemplate<CmfAdvanceRequest, CmfAdvanceResult> {

    @Resource(name = "memoryCacheClient")
    private CacheClient cacheClient;
    @Resource
    private InstOrderRepository instOrderRepository;
    @Resource
    private InstControlOrderRepository instControlOrderRepository;
    @Resource
    private InstControlOrderResultRepository instControlOrderResultRepository;
    @Resource
    private SubmitInstitutionService submitInstitutionService;

    @Resource
    private CacheLockService cacheLockService;

    @Value("${fcw.address}/page/")
    private String pageUrl;

    @Override
    protected String getServiceName() {
        return "Advance3ds2Processor";
    }

    @Override
    protected void businessValidate(CmfAdvanceRequest request) {
        InstBaseOrder instOrder = queryPreOrder(request);
        assertNotNull(instOrder, "instOrder不存在");
//        assertIsTrue(instOrder.getStatus() == InstOrderStatus.IN_PROCESS, "机构订单不为处理中");
    }

    @Override
    protected CmfAdvanceResult createResponse() {
        return new CmfAdvanceResult();
    }

    @Override
    protected void process(CmfAdvanceRequest request, CmfAdvanceResult cmfAdvanceResult) {
        execute(request, cmfAdvanceResult, 3);

    }

    public void execute(CmfAdvanceRequest request, CmfAdvanceResult cmfAdvanceResult, int retryTimes) {

        if (controlOrderExists(request, cmfAdvanceResult)) {
            logger.warn("0-Order already exists:{},retry times:{}", request.getInstOrderToken(), 3 - retryTimes);
            return;
        }
        //防止并发
        String lockKey = String.format("%s-%s", this.getServiceName(), request.getInstOrderToken());
        boolean lock = cacheLockService.lock(lockKey, Duration.ofSeconds(30));
        if (!lock) {
            if (retryTimes <= 0) {
                //重复支付
                throw new ErrorException(CmfCommonResultCode.RepeatRequestError.getMessage());
            }
            //未获得锁尝试重新获取一次
            execute(request, cmfAdvanceResult, --retryTimes);
        }

        try {

            //是否需要双重检查
//            if (controlOrderExists(request, cmfAdvanceResult)) {
//                logger.warn("1-Order already exists:{},retry times:{}", request.getInstOrderToken(), retryTimes);
//                return;
//            }

            Pair<InstControlOrder, VerifySignRequest> pair = convert2ControlOrder(request);
            if (pair.getRight() != null) {
                VerifySignHolder.set(pair.getRight());
            }

            // 推进控制订单
            InstControlOrderResult result = submitInstitutionService.submit(pair.getLeft());
            // 组装结果
            buildResponse(pair.getLeft(), result, cmfAdvanceResult);


        } finally {
            VerifySignHolder.clear();
            cacheLockService.unlock(lockKey);
        }
    }

    private boolean controlOrderExists(CmfAdvanceRequest request, CmfAdvanceResult cmfAdvanceResult) {
        InstControlOrder instControlOrder = instControlOrderRepository.loadLatestByRequestNoAndApiType(request.getInstOrderToken(), FundChannelApiType.ADVANCE_3DS2.getCode());
        if (instControlOrder != null) {
            logger.warn("Advance3ds2 Duplicat Request:{}", request.getInstOrderToken());
            //开始进行判断，如果已经产生控制订单结果，幂等返回结果
            List<InstControlOrderResult> instControlOrderResults = instControlOrderResultRepository.loadByOrderId(instControlOrder.getOrderId());
            if (CollectionUtils.isNotEmpty(instControlOrderResults)) {
                //获取日期最新的一条数据
                InstControlOrderResult instControlOrderResult = instControlOrderResults.stream().max(Comparator.comparing(InstControlOrderResult::getGmtCreate)).orElse(instControlOrderResults.get(0));
                buildResponse(instControlOrderResult, cmfAdvanceResult);
            } else {
                buildResponse(instControlOrder, cmfAdvanceResult);
            }
            fillPageUrl(instControlOrder, cmfAdvanceResult);
            return true;
        }
        return false;
    }


    private Pair<InstControlOrder, VerifySignRequest> convert2ControlOrder(CmfAdvanceRequest request) {
        InstBaseOrder instOrder = queryPreOrder(request);
        InstControlOrder controlOrder = InstControlOrderConverter.convert(instOrder, ControlRequestType.ADVANCE, FundChannelApiType.ADVANCE_3DS2);
        controlOrder.setRequestNo(request.getInstOrderToken());
        VerifySignRequest req = null;
        if (request.getExtension() != null) {
            if (StringUtils.isNotEmpty(request.getExtension().get(VERIFY_PARAM))) {
                req = new VerifySignRequest();
                req.setVerifyParamStr(request.getExtension().get(VERIFY_PARAM));
                request.getExtension().remove(VERIFY_PARAM);
            }
            controlOrder.getExtension().putAll(request.getExtension());
        }
        return Pair.of(controlOrder, req);
    }

    private InstBaseOrder queryPreOrder(CmfAdvanceRequest request) {
        Object obj = cacheClient.get(CacheType.INST_ORDER_TOKEN, request.getInstOrderToken());
        assertNotNull(obj, "instOrderToken未找到，可能已过期");
        String instOrderNo = null;
        InstOrderType instOrderType = InstOrderType.FUND;
        if (obj instanceof Map) {
            Map<String, String> hashMap = (Map<String, String>) obj;
            instOrderNo = hashMap.get(ExtensionKey.INST_ORDER_NO.getKey());
            instOrderType = InstOrderType.getByName(hashMap.get(ExtensionKey.INST_ORDER_TYPE.key));
        } else {
            instOrderNo = (String) obj;
        }
        return instOrderType == InstOrderType.CONTROL ? instControlOrderRepository.loadByNo(instOrderNo) : instOrderRepository.loadByNo(instOrderNo);
    }

    private void buildResponse(InstControlOrder controlOrder, InstControlOrderResult result, CmfAdvanceResult cmfAdvanceResult) {
        BeanUtils.copyProperties(result, cmfAdvanceResult);
        cmfAdvanceResult.setApplyStatus(ApplyStatusEnum.SUCCESS);
        cmfAdvanceResult.setUnityResultCode(result.getInstResultCode());
        cmfAdvanceResult.setMessage(result.getResultMessage());
    }

    private void buildResponse(InstControlOrderResult result, CmfAdvanceResult cmfAdvanceResult) {
        BeanUtils.copyProperties(result, cmfAdvanceResult);
        cmfAdvanceResult.setApplyStatus(ApplyStatusEnum.SUCCESS);
        cmfAdvanceResult.setUnityResultCode(result.getInstResultCode());
        cmfAdvanceResult.setMessage(result.getResultMessage());
    }


    private void buildResponse(InstControlOrder instControlOrder, CmfAdvanceResult cmfAdvanceResult) {
        cmfAdvanceResult.setApplyStatus(ApplyStatusEnum.SUCCESS);
        BeanUtils.copyProperties(instControlOrder, cmfAdvanceResult);
    }

    private void fillPageUrl(InstControlOrder instControlOrder, CmfAdvanceResult cmfAdvanceResult) {
        Map<String, String> extension = Optional.ofNullable(cmfAdvanceResult.getExtension()).orElse(new HashMap<>(20));
        if (extension.get(ExtensionKey.PAGE_URL_FOR_SIGN.getKey()) != null) {
            return;
        }

        String apiType = FundChannelApiType.VERIFY_SIGN.getCode();
        InstControlOrder preControlOrder = instControlOrderRepository.loadByNo(instControlOrder.getPreInstOrderNo());
        if (preControlOrder != null && FundChannelApiType.AUTH.equals(preControlOrder.getApiType())) {
            apiType = FundChannelApiType.AUTH_VERIFY.getCode();
        }

        String url = pageUrl + String.format("%s-%s/%s" + POSFIX_HTML, instControlOrder.getFundChannelCode(), apiType, instControlOrder.getPreInstOrderNo());
        extension.put("PAGE_URL", "<form id=\"3ds_form\" method=\"GET\" action=\"" + url + "\"></form><script> document.getElementById(\"3ds_form\").submit(); </script>");
    }

}
