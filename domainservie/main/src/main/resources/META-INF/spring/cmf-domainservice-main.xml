<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	  http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd"
       default-autowire="byName">

    <aop:aspectj-autoproxy/>

    <bean id="abstractOrderRetryService" class="com.uaepay.cmf.domainservice.main.retry.AbstractOrderRetryService"
          abstract="true"/>
    <bean id="instOrderRetryService" class="com.uaepay.cmf.domainservice.main.retry.InstOrderRetryService"
          parent="abstractOrderRetryService"/>
    <bean id="controlOrderRetryService" class="com.uaepay.cmf.domainservice.main.retry.ControlOrderRetryService"
          parent="abstractOrderRetryService"/>
    <bean id="batchOrderRetryService" class="com.uaepay.cmf.domainservice.main.retry.BatchOrderRetryService"
          parent="abstractOrderRetryService"/>

    <bean class="com.uaepay.cmf.domainservice.main.retry.advice.RetryTimeAdvice">
        <property name="orderRetryServiceMap">
            <map>
                <entry key="INST" value-ref="instOrderRetryService"/>
                <entry key="CONTROL" value-ref="controlOrderRetryService"/>
                <entry key="BATCH" value-ref="batchOrderRetryService"/>
            </map>
        </property>
    </bean>


</beans>
