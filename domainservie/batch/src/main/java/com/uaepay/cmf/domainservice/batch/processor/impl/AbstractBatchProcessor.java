package com.uaepay.cmf.domainservice.batch.processor.impl;

import java.util.ArrayList;
import java.util.List;

import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.core.domain.vo.InstSendResult;

/**
 * <p>批量流程抽象实现.</p>
 * <AUTHOR> won
 * @version $Id: AbstractBatchProcessor.java, v 0.1 2011-4-13 下午03:45:16 sean won Exp $
 */
public abstract class AbstractBatchProcessor extends AbstractProcessor {

    /**
     * 结果处理.
     * @param batchResult
     * @return
     */
    protected List<InstSendResult> convert(InstBatchResult batchResult) {
        List<InstOrderResult> resultList = batchResult.getInstOrderResults();

        List<InstSendResult> sendResultList = new ArrayList<>(resultList.size());
        for (InstOrderResult result : resultList) {
            InstSendResult sendResult = new InstSendResult();
            sendResult.setOrder(result);
            sendResultList.add(sendResult);
        }
        return sendResultList;
    }
}
