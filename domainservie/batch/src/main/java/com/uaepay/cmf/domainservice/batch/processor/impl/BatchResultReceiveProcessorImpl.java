package com.uaepay.cmf.domainservice.batch.processor.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.InstResultOperateStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.core.domain.vo.InstSendResult;
import com.uaepay.cmf.domainservice.batch.processor.BatchResultProcessor;
import com.uaepay.cmf.domainservice.batch.processor.BatchResultReceiveProcessor;
import com.uaepay.cmf.domainservice.main.process.NotifyPaymentService;
import com.uaepay.cmf.domainservice.main.repository.CmfOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.repository.InstOrderResultRepository;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 批量返回结果接收流程实现.
 * </p>
 * 
 * <AUTHOR> won
 * @version $Id: BatchResultReceiveProcessorImpl.java, v 0.1 2011-4-13
 *          下午04:07:10 sean won Exp $
 */
@Service
public class BatchResultReceiveProcessorImpl extends AbstractBatchProcessor implements
                                                                           BatchResultReceiveProcessor {
    @Resource
    private BatchResultProcessor batchResultProcessor;

    @Resource
    InstOrderResultRepository    instOrderResultRepository;

    @Resource
    CmfOrderRepository           cmfOrderRepository;

    @Resource
    InstOrderRepository          instOrderRepository;

    @Resource
    private NotifyPaymentService notifyPaymentService;

    /**
     *
     * @see
     * com.uaepay.cmf.domainservice.batch.processor.BatchResultReceiveProcessor#
     * process
     * (com.uaepay.cmf.fss.core.domain.institution.batch.InstBatchResult)
     */
    @Override
    public InstBatchResult process(InstBatchResult batchResult) {
        List<InstSendResult> sendResultList = convert(batchResult);

        List<Long> ids = new ArrayList<>();

        for (InstOrderResult result : batchResult.getInstOrderResults()) {
            ids.add(result.getResultId());
        }
        try {
            /** 状态跃迁 */
            // 处理结果
            batchResultProcessor.process(batchResult, sendResultList);
        } catch (Exception e) {
            logger.info("分批更新结果表操作状态,batchId[" + batchResult.getArchiveBatchId() + "],size["
                        + ids.size() + "]");
            updateOperateStatus(InstResultOperateStatus.FAILURE, ids,
                InstResultOperateStatus.AWAITING);
            logger.error("batchResult[" + batchResult.getBatchResultId() + "] :", e);
            return batchResult;
        }

        logger.info("分批更新结果表操作状态,batchId[" + batchResult.getArchiveBatchId() + "],size["
                    + ids.size() + "]");
        updateOperateStatus(InstResultOperateStatus.SUCCESSFUL, ids,
            InstResultOperateStatus.AWAITING);

        for (InstSendResult instSendResult : sendResultList) {
            InstOrderResult result = instSendResult.getOrder();
            InstOrder instOrder = instOrderRepository.loadById(result.getInstOrderId(), false);
            CmfOrder cmfOrder = cmfOrderRepository.loadByCmfSeqNo(instOrder.getCmfSeqNo(), false);
            //装载数据库最新结果信息
            InstOrderResult instResult = instOrderResultRepository.load(instSendResult.getOrder().getResultId(), false);
            notifyPaymentService.notifyPE(cmfOrder, instOrder, instResult,false);
        }

        return batchResult;
    }

    /**
     * 超过1000 sql执行会有问题,分批执行
     * 
     * @param to
     * @param ids
     * @param from
     */
    private void updateOperateStatus(InstResultOperateStatus to, List<Long> ids,
                                     InstResultOperateStatus from) {
        if (ids.size() > 0) {
            int updateCount = ids.size() / 500 + 1;
            for (int i = 0; i < updateCount; i++) {
                int beginIndex = i * 500;
                int endIndex = beginIndex + 500;
                if (endIndex > ids.size()) {
                    endIndex = ids.size();
                }
                if (beginIndex != endIndex) {
                    instOrderResultRepository.updateOperateStatusByResultIds(to,
                        ids.subList(beginIndex, endIndex), from);
                    logger.info("更新结果表操作状态,beginIndex[" + beginIndex + ",endIndex[" + endIndex
                                + "]");
                }
            }
        }
    }

}
