package com.uaepay.cmf.domainservice.batch.processor.impl;

import com.uaepay.cmf.common.core.domain.exception.CommunicateException;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.domainservice.batch.processor.BatchResendProcessor;
import com.uaepay.cmf.domainservice.batch.processor.BatchResultProcessor;
import com.uaepay.cmf.domainservice.main.sender.BatchOrderSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>批量补发处理器.</p>
 *
 * <AUTHOR>
 * @version BatchResendProcessorImpl.java 1.0 @2015/5/8 19:36 $
 */
@Slf4j
@Service
public class BatchResendProcessorImpl implements BatchResendProcessor {

    @Resource
    private BatchOrderSendService batchOrderSendService;

    @Resource
    private BatchResultProcessor batchResultProcessor;

    /**
     * @param instBatchOrder
     * @return
     * @throws CommunicateException
     */
    @Override
    public InstBatchResult process(InstBatchOrder instBatchOrder) {


        log.info("batchSender.process.orderNo:{}", instBatchOrder.getInstBatchNo());
        //发送银行
        ChannelFundBatchResult channelFundBatchResult = batchOrderSendService.send(instBatchOrder);

        log.info("batchSender.result.batchId:{}", channelFundBatchResult.getArchiveBatchId());

        //结果推进处理
        return batchResultProcessor.process(instBatchOrder, channelFundBatchResult, false);
    }

}
