package com.uaepay.cmf.domainservice.batch.result;

import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.common.util.money.Money;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.schema.cmf.enums.BizType;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 归档明细
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: ArchiveDetail.java, v 0.1 2009-9-28 上午10:44:50 cc Exp $
 */
@Data
public class ArchiveDetail implements Serializable {
    private static final long serialVersionUID = -2011128411092114000L;
    /**
     * 归档批次号
     */
    private Long batchId;
    /**
     * 总笔数
     */
    private long totalRecord;
    /**
     * 总金额
     */
    private Money totalAmount;
    /**
     * 文件相对路径
     */
    private String filePath;
    /**
     * 绝对路径
     */
    private String absFilePath;
    /**
     * 源文件，文件路径相对路径和文件名
     */
    private String sourceFile;

    private boolean success;

    private String message;

    private ChannelApiVO channelApi;

    private InstBatchOrder instBatchOrder;

    private String operator;

    private BizType orderType;

    @Override
    public String toString() {
        return "ArchiveDetail [batchId=" + batchId + ", totalRecord=" + totalRecord
                + ", totalAmount=" + totalAmount + ", filePath=" + filePath + ", sourceFile="
                + sourceFile + ", channelApi=" + channelApi + ", orderType=" + orderType
                + "]";
    }

}
