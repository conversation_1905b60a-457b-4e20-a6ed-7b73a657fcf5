package com.uaepay.cmf.domainservice.batch.repository.impl;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.common.core.dal.daointerface.InstBatchOrderDAO;
import com.uaepay.cmf.common.core.dal.daointerface.InstOrderDAO;
import com.uaepay.cmf.common.core.dal.dataobject.InstBatchOrderDO;
import com.uaepay.cmf.common.core.domain.enums.InstOrderArchiveStatus;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.domainservice.batch.repository.InstBatchOrderRepository;
import com.uaepay.cmf.domainservice.main.convert.InstBatchOrderConverter;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.List;

/**
 * 批量机构订单数据仓库实现
 *
 * <AUTHOR> won
 */
@Repository
public class InstBatchOrderRepositoryImpl implements InstBatchOrderRepository {

    @Resource
    private InstBatchOrderDAO instBatchOrderDAO;

    @Resource
    private InstOrderDAO instOrderDAO;

    @Resource(name = "cmfTransactionTemplate")
    TransactionTemplate cmfTransactionTemplate;

    @Override
    public InstBatchOrder loadById(Long archiveBatchId) {
        InstBatchOrderDO instBatchOrderDO = instBatchOrderDAO.loadById(archiveBatchId);
        return InstBatchOrderConverter.convert(instBatchOrderDO);
    }

    @Override
    public BaseResult transitBatchOrderReceiveStatus(final Long archiveBatchId) {
        return cmfTransactionTemplate.execute(status -> {
            BaseResult result = new BaseResult();

            //1.该批次要存在.
            InstBatchOrderDO dbBatch = instBatchOrderDAO.loadById(archiveBatchId);
            if (null == dbBatch) {
                result.setResultMessage("批次号不存在batchId=" + archiveBatchId);
                return result;
            }

            //2.所有订单都是F. (包括无任何订单的情形)
            long failCount = instOrderDAO.countNotFailureByArchiveBatchId(archiveBatchId);
            if (failCount != 0) {
                result.setResultMessage("部分订单状态不是'失败'，无法清理该批次");
                return result;
            }

            instBatchOrderDAO.updateStatusById(InstOrderArchiveStatus.RECEIVED.getCode(),
                    archiveBatchId);
            instBatchOrderDAO.passStatus(archiveBatchId, YesNo.YES.getCode());

            result.setSuccess(true);
            return result;
        });
    }

    @Override
    public int updateStatusById(Long archiveBatchId, InstOrderArchiveStatus status) {
        return instBatchOrderDAO.updateStatusById(status.getCode(), archiveBatchId);
    }

    @Override
    public int updateStatusByIdWithPreStatus(Long archiveBatchId, InstOrderArchiveStatus status, InstOrderArchiveStatus preStatus) {


        return instBatchOrderDAO.updateStatusByIdAndPreStatus(status.getCode(), preStatus.getCode(), archiveBatchId);
    }

    @Override
    public List<Long> loadBatchOrder4Query(YesNo lockStatus, BizType bizType, int rownum) {
        return instBatchOrderDAO.loadBatchOrder4Query(lockStatus.getCode(), bizType.getCode(), rownum);
    }
}
