package com.uaepay.cmf.domainservice.batch.processor;

import com.uaepay.basis.beacon.service.facade.domain.response.CommonResponse;
import com.uaepay.cmf.common.core.domain.enums.InstOrderArchiveStatus;
import com.uaepay.cmf.common.core.domain.enums.InstOrderStatus;
import com.uaepay.cmf.common.core.domain.exception.CmfBizException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.util.validate.Validate;
import com.uaepay.cmf.domainservice.batch.repository.InstBatchOrderRepository;
import com.uaepay.cmf.domainservice.main.general.GeneralProcessorTemplate;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.service.facade.domain.fundout.FundOutRetryRequest;
import com.uaepay.schema.cmf.enums.BizType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date FundOutRetryProcessor.java v1.0
 */
@Service
public class FundOutRetryProcessor extends GeneralProcessorTemplate<FundOutRetryRequest, CommonResponse> {

    @Resource
    private InstBatchOrderRepository instBatchOrderRepository;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Override
    protected String getServiceName() {
        return "FundOutRetryProcessor";
    }

    @Override
    protected CommonResponse createResponse() {
        return CommonResponse.buildSuccess();
    }

    @Override
    protected void businessValidate(FundOutRetryRequest request) {
        InstBatchOrder instBatchOrder = instBatchOrderRepository.loadById(request.getArchiveBatchId());
        Validate.assertTrue("未找到批次订单", instBatchOrder != null);
        Validate.assertTrue("批次订单状态不为已发送", instBatchOrder.getStatus() == InstOrderArchiveStatus.SUBMMITED);
        Validate.assertTrue("批次订单不为出款订单", instBatchOrder.getBizType() == BizType.FUNDOUT);
        // 校验机构订单状态
        List<InstOrder> instOrderList = instOrderRepository
                .getInstOrderListByAichiveBatchId(instBatchOrder.getArchiveBatchId());
        for (InstOrder instOrder : instOrderList) {
            Validate.assertTrue("机构订单不为处理中", instOrder.getStatus() == InstOrderStatus.IN_PROCESS);
        }

    }

    @Override
    protected void process(FundOutRetryRequest request, CommonResponse response) throws CmfBizException {
        instOrderRepository.updateBatchRetryById(request.getGmtRetry(), request.getArchiveBatchId());

    }
}
