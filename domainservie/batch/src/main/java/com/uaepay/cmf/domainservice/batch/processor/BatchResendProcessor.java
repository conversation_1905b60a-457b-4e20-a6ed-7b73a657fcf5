package com.uaepay.cmf.domainservice.batch.processor;

import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;

/**
 * <p>
 * 批量补发接口.
 * </p>
 *
 * <AUTHOR>
 * @version BatchResendProcessor.java 1.0 @2015/5/8 19:33 $
 */
public interface BatchResendProcessor {

    /**
     * 批量补单.
     * 
     * @param instBatchOrder
     * @return
     */
    InstBatchResult process(InstBatchOrder instBatchOrder);

}
