package com.uaepay.cmf.domainservice.batch.processor;

import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.enums.InstOrderCommunicateType;
import com.uaepay.cmf.common.core.domain.exception.BatchOrderArchiveException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator;
import com.uaepay.cmf.common.core.engine.generator.SequenceNameEnum;
import com.uaepay.cmf.common.core.util.log.LogUtil;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.cmf.domainservice.batch.convert.InstBatchOrderConverter;
import com.uaepay.cmf.domainservice.batch.result.ArchiveDetail;
import com.uaepay.cmf.domainservice.main.domain.ArchiveCarrier;
import com.uaepay.cmf.domainservice.main.domain.FundRejectCodeEnum;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.FundOutRejectService;
import com.uaepay.cmf.fss.ext.integration.router.RouterClient;
import com.uaepay.grc.connect.api.vo.domain.CheckInfo;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.router.service.facade.domain.channel.ChannelBatchArchiveVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>批量订单打批服务</p>
 *
 * <AUTHOR> won
 */
@Service
public class BatchOrderArchiveServiceImpl implements BatchOrderArchiveService {
    private static final Logger logger = LoggerFactory.getLogger(BatchOrderArchiveServiceImpl.class);

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private TransactionTemplate cmfTransactionTemplate;

    @Resource
    private PrimaryKeyGenerator primaryKeyGenerator;

    @Resource
    private RouterClient routerClient;

    @Resource
    private FundOutRejectService fundOutRejectService;


    @Override
    public List<ArchiveDetail> archiveOrder(ChannelApiVO apiVO) {
        ChannelBatchArchiveVO archiveVO = routerClient.queryChannelArchive(apiVO.getApiCode());
        ArchiveCarrier carrier = ArchiveCarrier.builder().batchArchive(archiveVO).channelApi(apiVO).operator(DEFAULT_OPERATOR).build();
        if (carrier.getBatchArchive() == null || carrier    .getChannelApi() == null) {
            logger.info("参数校验为空");
            return new ArrayList<>(0);
        }

        Long archiveId = carrier.getBatchArchive().getArchiveId();
        Long tempBatchId = generateArchiveBatchId();
        try {
            long startMillis = System.currentTimeMillis();
            long count = updateBatch(carrier, archiveId, tempBatchId);


            LogUtil.info("Update Template Batch Id", startMillis, System.currentTimeMillis());
            long maxItem = carrier.getBatchArchive().getMaxItem();

            Assert.isTrue(maxItem > 0, "最大笔数设置不能小于0");

            return archiveWithMaxItem(carrier, maxItem, count, tempBatchId);
        } catch (Exception e) {
            logger.error("archive.error", e);
            throw new BatchOrderArchiveException(e.getMessage());
        } finally {
            instOrderRepository.updateBatchId2Default(tempBatchId);
        }
    }

    private int updateBatch(ArchiveCarrier carrier, Long archiveId, Long tempBatchId) {

        Long hours = 24L;
        Date bookingTime = new Date();
        String communicateType = InstOrderCommunicateType.BATCH.getCode();
        int rows = 0;
        long startMillis = System.currentTimeMillis();
        int pages = instOrderRepository.getArchivePages(archiveId, carrier.getChannelApi().getApiCode(), hours, bookingTime, communicateType);

        LogUtil.info("Archive page count," + pages, startMillis, System.currentTimeMillis());

        for (; pages > 0; pages--) {
            final List<Long> instOrderIdList = instOrderRepository.loadInstOrderList4ArchivePage(archiveId, carrier.getChannelApi().getApiCode(), hours, bookingTime, communicateType);
            if (CollectionUtils.isEmpty(instOrderIdList)) {
                break;
            }

            filterOrder(instOrderIdList);

            startMillis = System.currentTimeMillis();
            rows += instOrderRepository.updateBatchByInstOrderId(tempBatchId, instOrderIdList);
            LogUtil.info("Archive Template Single Update,Pages=" + pages, startMillis, System.currentTimeMillis());
        }
        return rows;
    }

    /**
     * 按笔数进行分批
     *
     * @return
     */
    private List<ArchiveDetail> archiveWithMaxItem(ArchiveCarrier carrier,
                                                   Long maxItem, Long count,
                                                   Long tempBatchId) {

        List<ArchiveDetail> detailList = new ArrayList<>();

        List<String> currencyList = instOrderRepository.loadOrderCurrencyListByBatchId(tempBatchId);

        for (String currency : currencyList) {
            List<Long> instOrderIdList = instOrderRepository.loadInstOrderIdListByBatchIdAndCurrency(tempBatchId, currency);

            Assert.isTrue(instOrderIdList.size() == count, "笔数不一致");

            // 整页归档
            for (int[] arr : convertSubArray(count.intValue(), maxItem.intValue())) {
                ArchiveDetail detail = makeArchiveDetail(carrier,
                        tempBatchId, instOrderIdList.subList(arr[0], arr[1]));
                if (detail != null) {
                    detailList.add(detail);
                }
            }
        }
        return detailList;
    }

    private static List<int[]> convertSubArray(int count, int maxItem) {
        List<int[]> pageList = new ArrayList<>();
        int offset = 0;
        while (offset < count) {
            int endRow = (maxItem + offset > count) ? count : maxItem + offset;
            pageList.add(new int[]{offset, endRow});
            offset = endRow;
        }
        logger.info("整页数={}", pageList.size());

        return pageList;
    }

    private ArchiveDetail makeArchiveDetail(final ArchiveCarrier carrier,
                                            final Long tempBatchId,
                                            final List<Long> instOrderIds) {
        if (CollectionUtils.isEmpty(instOrderIds)) {
            logger.info("makeArchiveDetail.isEmpty");
            return null;
        }
        return cmfTransactionTemplate.execute(new TransactionCallback<ArchiveDetail>() {
            @Override
            public ArchiveDetail doInTransaction(TransactionStatus status) {
                long now = System.currentTimeMillis();
                try {
                    // 得到批量的id号
                    Long instBatchOrderId = generateArchiveBatchId();

                    Integer instOrderCount = updateBatchIdListByTempBatchId(tempBatchId,
                            instBatchOrderId, instOrderIds);

                    Assert.isTrue(instOrderCount == instOrderIds.size(), "数据更新条数失败");

                    List<InstOrder> instOrderList = instOrderRepository
                            .getInstOrderListByAichiveBatchId(instBatchOrderId);
                    String orderNo = routerClient.genOrderNo(carrier.getChannelApi().getChannelCode(), FundChannelApiType.getByCode(carrier.getChannelApi().getApiType()), true);

                    InstBatchOrder instBatchOrder = InstBatchOrderConverter.buildInstBatchOrder(carrier, instBatchOrderId, instOrderList, orderNo);
                    instOrderRepository.insert(instBatchOrder);
                    // 组装归档信息
                    return InstBatchOrderConverter.buildArchiveDetail(instBatchOrderId, instBatchOrder, carrier.getChannelApi());
                } catch (Exception e) {
                    logger.error("创建归档信息异常！", e);
                    status.setRollbackOnly();
                    return null;
                } finally {
                    logger.info("归档的详情:耗时[" + (System.currentTimeMillis() - now) + "]毫秒");
                }
            }

            /**
             * 超过1000 sql执行会有问题,分批执行
             */
            private int updateBatchIdListByTempBatchId(Long tempBatchId, Long archiveBatchId,
                                                       List instOrderIdList) {
                int countUpdated = 0;
                if (instOrderIdList.size() > 0) {
                    int updateCount = instOrderIdList.size() / 500 + 1;
                    for (int i = 0; i < updateCount; i++) {
                        int beginIndex = i * 500;
                        int endIndex = beginIndex + 500;
                        if (endIndex > instOrderIdList.size()) {
                            endIndex = instOrderIdList.size();
                        }
                        if (beginIndex != endIndex) {
                            logger.info("更新一个批次from={},to={},beginIndex={},endIndex:{}",
                                    tempBatchId, archiveBatchId, beginIndex, endIndex);
                            countUpdated += instOrderRepository.updateBatchIdListByTempBatchId(
                                    tempBatchId, archiveBatchId,
                                    instOrderIdList.subList(beginIndex, endIndex));
                        }
                    }
                }
                return countUpdated;
            }
        });
    }

    private Long generateArchiveBatchId() {
        return Long.valueOf(primaryKeyGenerator.generateKey(SequenceNameEnum.BATCH_ORDER));
    }

    public void filterOrder(List<Long> instOrderIdList) {

        List<InstOrder> instOrders = instOrderRepository.loadInstOrderListByIds(instOrderIdList);


        if (CollectionUtils.isEmpty(instOrders)) {
            logger.warn("instOrder list is Empty...");
            return;
        }
        List<CheckInfo> checkInfos = fundOutRejectService.grcCheck(instOrders);

        if (CollectionUtils.isEmpty(checkInfos)) {
            return;
        }

        //收集拒绝的订单
        List<InstOrder> rejectOrders = instOrders.stream().filter(order -> {
            String memberId = order.getExtension().get(ExtensionKey.MEMBER_ID.key);
            String paymentVoucherNo = order.getExtension().get(ExtensionKey.PAYMENT_ORDER_NO.key);
            return checkInfos.stream().anyMatch(ck -> StringUtils.equals(memberId, ck.getMemberId()) && StringUtils.equals(paymentVoucherNo, ck.getPaymentOrderNo()));

        }).collect(Collectors.toList());

        //拒绝的订单id
        List<Long> rejectIds = rejectOrders.stream().map(InstOrder::getInstOrderId).collect(Collectors.toList());

        logger.info("rejectOrderIds:{}", rejectIds);

        //批量拒绝提现订单
        fundOutRejectService.batchRejectWithdrawOrder(rejectOrders, FundRejectCodeEnum.CHARGE_BACK);

        //移除批量id
        instOrderIdList.removeAll(rejectIds);
    }
}
