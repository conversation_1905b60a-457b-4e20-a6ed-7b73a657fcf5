package com.uaepay.cmf.domainservice.batch.processor;

import java.util.List;

import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.core.domain.vo.InstSendResult;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;

/**
 * <p>批量结果处理流程[一级].</p>
 * <AUTHOR> won
 * @version $Id: BatchResultProcessor.java, v 0.1 2011-4-11 下午03:23:20 sean won Exp $
 */
public interface BatchResultProcessor {
    /**
     * 批量结果处理.
     * @param batchResult
     * @param sendResult
     * @return
     */
    InstBatchResult process(InstBatchResult batchResult, List<InstSendResult> sendResult);


    /**
     * 银企出款结果批量处理类
     * @param instBatchOrder
     * @param channelBatchResult
     * @param isSync
     * @return
     */
    InstBatchResult process(InstBatchOrder instBatchOrder, ChannelFundBatchResult channelBatchResult, boolean isSync);
}
