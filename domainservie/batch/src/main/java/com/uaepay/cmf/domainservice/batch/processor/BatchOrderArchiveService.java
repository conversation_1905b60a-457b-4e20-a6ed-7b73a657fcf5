package com.uaepay.cmf.domainservice.batch.processor;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.domainservice.batch.result.ArchiveDetail;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;

import java.util.List;

/**
 *
 * <p>批量订单打批服务</p>
 *
 *
 * <AUTHOR> won
 *
 */
public interface BatchOrderArchiveService extends BasicConstant{

    /**
     * 根据归档模板批量归档
     * @param channelApiVO
     * @return
     */
    List<ArchiveDetail> archiveOrder(ChannelApiVO channelApiVO);

}
