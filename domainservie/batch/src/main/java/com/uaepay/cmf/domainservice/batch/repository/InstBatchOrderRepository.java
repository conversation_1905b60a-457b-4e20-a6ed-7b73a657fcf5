package com.uaepay.cmf.domainservice.batch.repository;

import java.util.List;

import org.springframework.dao.DataAccessException;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.common.core.domain.enums.InstOrderArchiveStatus;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.schema.cmf.enums.BizType;
import com.uaepay.schema.cmf.enums.YesNo;

/**
 * 批量机构订单数据仓库接口
 * <AUTHOR> won
 *
 */
public interface InstBatchOrderRepository {

    /**
     * 根据ID加载对象.
     *
     * @param archiveBatchId
     * @return
     * @throws DataAccessException
     */
    InstBatchOrder loadById(Long archiveBatchId);

    /**
     * 更新批次状态为R, 前提是该批次下的所有订单都为F
     *
     * @param archiveBatchId
     * @return
     */
    BaseResult transitBatchOrderReceiveStatus(Long archiveBatchId);

    /**
     * 修改批次订单状态
     * @param archiveBatchId
     * @param status
     * @return
     */
    int updateStatusById(Long archiveBatchId, InstOrderArchiveStatus status);

    /**
     * 根据之前的状态修改批次订单状态
     * @param archiveBatchId
     * @param status
     * @param preStatus
     * @return
     */
    int updateStatusByIdWithPreStatus(Long archiveBatchId, InstOrderArchiveStatus status,
                                      InstOrderArchiveStatus preStatus);

    List<Long> loadBatchOrder4Query(YesNo lockStatus, BizType bizType, int rownum);
}
