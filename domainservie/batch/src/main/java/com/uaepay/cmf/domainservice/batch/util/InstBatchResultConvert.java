package com.uaepay.cmf.domainservice.batch.util;

import com.uaepay.cmf.common.core.dal.dataobject.InstBatchResultDO;
import com.uaepay.cmf.common.core.domain.enums.InstBatchResultStatus;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.core.util.trans.DOConverter;
import com.uaepay.cmf.common.enums.FundChannelApiType;
import com.uaepay.schema.cmf.enums.BizType;
import org.springframework.beans.BeanUtils;

/**
 * <p>
 * 批量机构VO结果转换
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: InstBatchResultConvert.java, v 0.1 2009-9-28 上午10:44:50 cc Exp $
 */
public class InstBatchResultConvert extends DOConverter<InstBatchResult, InstBatchResultDO> {

	@Override
	public InstBatchResult convert(InstBatchResultDO from) {
		if (from == null) {
			return null;
		}
		InstBatchResult to = new InstBatchResult();
		BeanUtils.copyProperties(from, to);
		if (from.getStatus() != null) {
			to.setBatchStatus(InstBatchResultStatus.getByCode(from.getStatus()));
		}
		to.setBizType(BizType.getByCode(from.getBizType()));
		to.setApiType(FundChannelApiType.getByCode(from.getFundChannelApi()));
		return to;
	}
}
