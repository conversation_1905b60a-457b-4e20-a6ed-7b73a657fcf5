package com.uaepay.cmf.domainservice.batch.spi.impl;

import java.util.*;
import java.util.Map.Entry;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.uaepay.biz.common.util.BaseResult;
import com.uaepay.cmf.common.core.domain.CmfOrder;
import com.uaepay.cmf.common.core.domain.enums.ExtensionKey;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.domainservice.batch.repository.InstBatchOrderRepository;
import com.uaepay.cmf.domainservice.main.spi.impl.AbstractReSubmitInstitutionService;
import org.apache.commons.lang3.StringUtils;
import com.uaepay.common.util.money.Money;
import com.uaepay.schema.cmf.enums.YesNo;

/**
 * <p>批量重新提交订单</p>
 *
 * <AUTHOR>
 * <AUTHOR> fu 完成单笔和批量拆分
 * @version $Id: BatchReSubmitInstitutionService.java, v 0.1 2014-7-9 下午6:20:42 fuyangbiao Exp $
 */
@Service
public class BatchReSubmitInstitutionService extends AbstractReSubmitInstitutionService {
    /**
     * 批次订单仓储
     */
    @Resource
    InstBatchOrderRepository instBatchOrderRepository;

    @Override
    public BaseResult reSubmit(List<String> archiveBatchIds, String fundChannelCode) {
        //过滤相同批次id
        archiveBatchIds = filter(archiveBatchIds);
        //获取所有机构订单
        List<InstBatchOrder> batchOrders = loadBatchOrder(archiveBatchIds);

        Map<String, List<InstOrder>> instOrders = new HashMap<>();
        Map<String, CmfOrder> cmfOrders = new HashMap<>();
        //装载订单信息
        //instOrder 不能用cmfSeqNo装载,确保不会装载拆分多余的机构订单
        for (InstBatchOrder instBatchOrder : batchOrders) {
            for (InstOrder instOrder : instBatchOrder.getInstOrderList()) {
                CmfOrder cmfOrder = cmfOrderRepository
                        .loadByCmfSeqNo(instOrder.getCmfSeqNo(), false);
                if (cmfOrders.get(cmfOrder.getOrderSeqNo()) == null) {
                    cmfOrder.getExtension().put(ExtensionKey.WHITE_CHANNEL_CODE.key,
                            fundChannelCode);
                    List<InstOrder> instOrderList = new ArrayList<>();
                    instOrderList.add(instOrder);
                    cmfOrders.put(cmfOrder.getOrderSeqNo(), cmfOrder);
                    instOrders.put(cmfOrder.getOrderSeqNo(), instOrderList);
                } else {
                    List<InstOrder> instOrderList = instOrders.get(cmfOrder.getOrderSeqNo());
                    instOrderList.add(instOrder);
                    instOrders.put(cmfOrder.getOrderSeqNo(), instOrderList);
                }
            }
        }

        //针对拆分的订单校验,必须在这一批的废除批次中
        Map<CmfOrder, List<InstOrder>> orders = combineOrders(cmfOrders, instOrders);

        // 校验订单
        for (Entry<CmfOrder, List<InstOrder>> entry : orders.entrySet()) {
            CmfOrder cmfOrder = entry.getKey();
            Set<Long> batchIdSet = validate(cmfOrder, entry.getValue());
            // 校验批次
            for (Long batchId : batchIdSet) {
                validateBatch(cmfOrder.getOrderSeqNo(), batchId);
            }
        }

        //重新提交
        BaseResult result = reSubmit(orders);
        //废除批次
        BaseResult cancelBatchResult = cancelBatch(archiveBatchIds);

        return combineResult(result, cancelBatchResult);
    }

    /**
     * 组装订单
     *
     * @param cmfOrders
     * @param instOrders
     * @return
     */
    private Map<CmfOrder, List<InstOrder>> combineOrders(Map<String, CmfOrder> cmfOrders,
                                                         Map<String, List<InstOrder>> instOrders) {
        Map<CmfOrder, List<InstOrder>> orders = new HashMap<>();
        Set<Entry<String, CmfOrder>> set = cmfOrders.entrySet();
        for (Iterator<Entry<String, CmfOrder>> it = set.iterator(); it.hasNext(); ) {
            Entry<String, CmfOrder> entry = it.next();
            String cmfSeqNo = entry.getKey();
            CmfOrder cmfOrder = entry.getValue();
            List<InstOrder> instOrderList = instOrders.get(cmfSeqNo);
            Money countAmount = ZERO_MONEY;
            for (InstOrder instOrder : instOrderList) {
                countAmount = countAmount.add(instOrder.getAmount());
            }
            Assert.isTrue(cmfOrder.getAmount().equals(countAmount),
                    "拆分订单[" + cmfSeqNo + "]属于不同的批次,请确保批次中包含该订单的所有拆分订单;");
            orders.put(cmfOrder, instOrderList);
        }
        return orders;
    }

    /**
     * 组装结果
     *
     * @param result
     * @param otherResult
     * @return
     */
    private BaseResult combineResult(BaseResult result, BaseResult otherResult) {
        String message = StringUtils.isEmpty(result.getResultMessage()) ? "" : result
                .getResultMessage();
        message = message
                + (StringUtils.isEmpty(otherResult.getResultMessage()) ? "" : otherResult
                .getResultMessage());
        return new BaseResult(result.isSuccess() && otherResult.isSuccess(), message);
    }

    /**
     * 装载批次订单信息
     *
     * @param archiveBatchIds
     * @return
     */
    private List<InstBatchOrder> loadBatchOrder(List<String> archiveBatchIds) {
        List<InstBatchOrder> batchOrders = new ArrayList<>();
        for (String archiveBatchId : archiveBatchIds) {
            InstBatchOrder instBatchOrder = instOrderRepository.loadById(Long
                    .valueOf(archiveBatchId));
            Assert.notNull(instBatchOrder, "archiveBatchId[" + archiveBatchId + "]批次不存在");
            instBatchOrder.setInstOrderList(instOrderRepository
                    .getInstOrderListByAichiveBatchId(Long.valueOf(archiveBatchId)));
            Assert.isTrue(CollectionUtils.isNotEmpty(instBatchOrder.getInstOrderList()),
                    "archiveBatchId[" + archiveBatchId + "]批次订单信息不存在");
            validateBatchOrder(instBatchOrder);
            batchOrders.add(instBatchOrder);
        }
        return batchOrders;
    }

    /**
     * 取消批次信息
     *
     * @param archiveBatchIds
     * @return
     */
    private BaseResult cancelBatch(List<String> archiveBatchIds) {
        Boolean success = true;
        StringBuilder message = new StringBuilder();
        for (String archiveBatchId : archiveBatchIds) {
            try {
                InstBatchOrder instBatchOrder = instOrderRepository.loadById(Long
                        .valueOf(archiveBatchId));
                instOrderRepository.reloadInstBatchOrder(instBatchOrder);
                instOrderRepository.updateAmountAndCount(instBatchOrder);
                BaseResult transitResult = instBatchOrderRepository
                        .transitBatchOrderReceiveStatus(Long.valueOf(archiveBatchId));
                Assert.isTrue(transitResult.isSuccess(), transitResult.getResultMessage());
            } catch (Exception e) {
                logger.error("archiveBatchId[" + archiveBatchId + "]清理失败", e);
                message.append("archiveBatchId[").append(archiveBatchId).append("]清理失败;");
                success = false;
            }
        }
        return new BaseResult(success, message.toString());
    }

    private void validateBatch(String cmfSeqNo, Long archiveBatchId) {
        InstBatchOrder instBatchOrder = instOrderRepository.loadById(archiveBatchId);
        validateBatchOrder(cmfSeqNo, instBatchOrder);
    }

    private void validateBatchOrder(String cmfSeqNo, InstBatchOrder instBatchOrder) {
        Assert.notNull(instBatchOrder, "参数不可为空");
        Assert.isTrue(!YesNo.YES.equals(instBatchOrder.getCheckFlag()),
                "cmf订单[" + cmfSeqNo + "],所属批次[" + instBatchOrder.getArchiveBatchId() + "]已提交,不允许重路由");
    }

    private void validateBatchOrder(InstBatchOrder instBatchOrder) {
        Assert.notNull(instBatchOrder, "参数不可为空");
        Assert.isTrue(!YesNo.YES.equals(instBatchOrder.getCheckFlag()),
                "批次[" + instBatchOrder.getArchiveBatchId() + "]已提交,不允许重路由");
    }
}
