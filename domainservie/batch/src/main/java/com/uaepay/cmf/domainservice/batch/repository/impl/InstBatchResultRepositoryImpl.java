package com.uaepay.cmf.domainservice.batch.repository.impl;

import com.uaepay.cmf.common.core.dal.daointerface.InstBatchResultDAO;
import com.uaepay.cmf.common.core.dal.dataobject.InstBatchResultDO;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.core.engine.generator.PrimaryKeyGenerator;
import com.uaepay.cmf.common.core.engine.generator.SequenceNameEnum;
import com.uaepay.cmf.domainservice.batch.repository.InstBatchResultRepository;
import com.uaepay.cmf.domainservice.batch.util.InstBatchResultConvert;
import com.uaepay.cmf.domainservice.batch.util.InstBatchResultDOConvert;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class InstBatchResultRepositoryImpl implements InstBatchResultRepository {
    @Resource
    private InstBatchResultDAO instBatchResultDAO;
    @Resource
    private PrimaryKeyGenerator primaryKeyGenerator;

    @Override
    public long insertInstBatchResult(InstBatchResult instBatchResult) {

        InstBatchResultDO instBatchResultDO = new InstBatchResultDOConvert()
                .convert(instBatchResult);
        instBatchResultDO.setBatchResultId(Long.valueOf(primaryKeyGenerator
                .generateKey(SequenceNameEnum.BATCH_RESULT)));
        instBatchResultDAO.insert(instBatchResultDO);
        instBatchResult.setBatchResultId(instBatchResultDO.getBatchResultId());
        return instBatchResult.getBatchResultId();
    }

    @Override
    public InstBatchResult loadById(Long batchOrderResultId) {
        return new InstBatchResultConvert()
                .convert(instBatchResultDAO.loadById(batchOrderResultId));
    }

}
