package com.uaepay.cmf.domainservice.batch.convert;

import com.uaepay.cmf.common.core.domain.constants.BasicConstant;
import com.uaepay.cmf.common.core.domain.enums.InstOrderArchiveStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.domainservice.batch.result.ArchiveDetail;
import com.uaepay.cmf.domainservice.main.domain.ArchiveCarrier;
import com.uaepay.common.util.money.Money;
import com.uaepay.router.service.facade.domain.channel.ChannelApiVO;
import com.uaepay.schema.cmf.enums.YesNo;

import java.util.Date;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date InstBatchOrderConverter.java v1.0
 */
public class InstBatchOrderConverter implements BasicConstant {

    private InstBatchOrderConverter(){

    }

    public static InstBatchOrder buildInstBatchOrder(ArchiveCarrier carrier,
                                                     Long instBatchOrderId,
                                                     List<InstOrder> instOrderList,
                                                     String orderNo) {
        InstBatchOrder instBatchOrder = new InstBatchOrder();
        InstOrder instOrder = instOrderList.get(0);
        Money totalAmount = new Money(ZERO_MONEY_STRING, instOrder.getAmount().getCurrency());
        for (InstOrder item : instOrderList) {
            totalAmount = totalAmount.add(item.getAmount());
        }
        instBatchOrder.setArchiveBatchId(instBatchOrderId);
        instBatchOrder.setArchiveTemplateId(carrier.getBatchArchive().getArchiveId());
        instBatchOrder.setBizType(instOrder.getBizType());
        instBatchOrder.setFundChannelCode(carrier.getChannelApi().getChannelCode());
        instBatchOrder.setApiCode(carrier.getChannelApi().getApiCode());
        instBatchOrder.setAmount(totalAmount);
        instBatchOrder.setTotalCount(instOrderList.size());
        instBatchOrder.setGmtNextRetry(new Date());
        instBatchOrder.setQueryTimes(DEFAULT_RETRY_TIMES);
        instBatchOrder.setStatus(InstOrderArchiveStatus.AWAITING);
        instBatchOrder.setIsLocked(YesNo.NO);
        instBatchOrder.setBizType(instOrder.getBizType());
        instBatchOrder.setPayMode(instOrder.getPayMode());
        instBatchOrder.setOperator(carrier.getOperator());
        instBatchOrder.setInstBatchNo(orderNo);
        instBatchOrder.setGmtArchive(new Date());
        // 默认状态是NO
        instBatchOrder.setCheckFlag(YesNo.NO);
        instBatchOrder.setInstOrderList(instOrderList);
        return instBatchOrder;
    }

    public static ArchiveDetail buildArchiveDetail(Long instBatchOrderId, InstBatchOrder instBatchOrder,
                                             ChannelApiVO channelApi) {
        ArchiveDetail archiveDetail = new ArchiveDetail();
        archiveDetail.setInstBatchOrder(instBatchOrder);
        archiveDetail.setTotalAmount(instBatchOrder.getAmount());
        archiveDetail.setTotalRecord(instBatchOrder.getTotalCount());
        archiveDetail.setChannelApi(channelApi);
        archiveDetail.setBatchId(instBatchOrderId);
        archiveDetail.setOrderType(instBatchOrder.getBizType());
        return archiveDetail;
    }

}
