package com.uaepay.cmf.domainservice.batch.processor;

import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;

/**
 * <p>批量返回结果接收流程[一级].</p>
 * <AUTHOR> won
 * @version $Id: BatchResultReceiveProcessor.java, v 0.1 2011-4-13 下午04:05:56 sean won Exp $
 */
public interface BatchResultReceiveProcessor {
    /**
     * 批量结果接收.
     * @param batchResult
     * @return
     */
    InstBatchResult process(InstBatchResult batchResult);
}
