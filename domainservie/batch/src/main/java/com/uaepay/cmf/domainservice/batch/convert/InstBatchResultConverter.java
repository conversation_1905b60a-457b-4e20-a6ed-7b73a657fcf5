package com.uaepay.cmf.domainservice.batch.convert;

import com.uaepay.cmf.common.core.domain.enums.InstBatchResultStatus;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.common.util.money.Money;

import java.util.Date;
import java.util.List;

/**
 * <p>批次订单结果转换.</p>
 *
 * <AUTHOR>
 * @version InstBatchResultConverter.java 1.0 @2015/5/8 13:21 $
 */
public class InstBatchResultConverter {

    private InstBatchResultConverter(){
        //Should not  Instantiation
    }

    public static InstBatchResult convert(InstBatchOrder instBatchOrder,ChannelFundBatchResult channelBatchResult,List<InstOrderResult> instOrderResultList ){
        InstBatchResult instBatchResult = new InstBatchResult();

        instBatchResult.setArchiveBatchId(instBatchOrder.getArchiveBatchId());
        instBatchResult.setBizType(instBatchOrder.getBizType());
        instBatchResult.setFundChannelCode(instBatchOrder.getFundChannelCode());

        long failedCount = 0L;
        long successCount = 0L;
        Money failedAmount = new Money("0.00", "AED");
        Money successAmount = new Money("0.00", "AED");

        if(instOrderResultList!=null){
            for(InstOrderResult result : instOrderResultList){
                switch(result.getStatus()){
                    case SUCCESSFUL:
                        successAmount = successAmount.add(result.getRealAmount());
                        successCount++;
                        break;
                    case FAILURE:
                        failedAmount = failedAmount.add(result.getRealAmount());
                        failedCount++;
                        break;
                }
            }
        }
        instBatchResult.setTotalCount(failedCount+successCount);
        instBatchResult.setTotalAmount(successAmount.add(failedAmount));
        instBatchResult.setSuccessCount(successCount);
        instBatchResult.setSuccessAmount(successAmount);
        instBatchResult.setFailedCount(failedCount);
        instBatchResult.setFailedAmount(failedAmount);
        instBatchResult.setBatchStatus(InstBatchResultStatus.FINSH);
        instBatchResult.setGmtCreate(new Date());
        instBatchResult.setGmtModified(new Date());
        instBatchResult.setMemo(channelBatchResult.getApiResultCode() + ":" + channelBatchResult.getResultMessage());
        instBatchResult.setApiType(channelBatchResult.getApiType());

        return instBatchResult;
    }


}
