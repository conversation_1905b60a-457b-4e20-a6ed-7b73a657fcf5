package com.uaepay.cmf.domainservice.batch.util;

import com.uaepay.cmf.common.core.dal.dataobject.InstBatchResultDO;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.core.util.trans.DOConverter;
import org.springframework.beans.BeanUtils;

/**
 * <p>
 * 批量机构DO结果转换
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: InstBatchResultDOConvert.java, v 0.1 2009-9-28 上午10:44:50 cc Exp $
 */
public class InstBatchResultDOConvert extends DOConverter<InstBatchResultDO, InstBatchResult> {

	@Override
	public InstBatchResultDO convert(InstBatchResult from) {
		if (from == null) {
			return null;
		}
		InstBatchResultDO to = new InstBatchResultDO();
		BeanUtils.copyProperties(from, to);
		if (from.getBatchStatus() != null) {
			to.setStatus(from.getBatchStatus().getCode());
		}
		if (from.getBizType() != null) {
			to.setBizType(from.getBizType().getCode());
		}
		if(from.getApiType()!=null) {
			to.setFundChannelApi(from.getApiType().getCode());
		}
		return to;
	}
}
