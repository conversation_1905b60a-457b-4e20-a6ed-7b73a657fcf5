package com.uaepay.cmf.domainservice.batch.processor.impl;

import com.uaepay.cmf.common.core.domain.enums.InstOrderProcessStatus;
import com.uaepay.cmf.common.core.domain.exception.AppRuntimeException;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.schema.cmf.enums.BizType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <p>流程抽象实现.</p>
 * <AUTHOR> won
 * @version $Id: AbstractProcessor.java, v 0.1 2011-4-11 下午03:59:40 sean won Exp $
 */
public abstract class AbstractProcessor {
    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    public InstOrderResult fail(InstOrderProcessStatus failure, BizType bizType,
                                AppRuntimeException e) {
        InstOrderResult result = new InstOrderResult();
        result.setProcessStatus(failure);
        result.setInstResultCode(e == null ? "" : e.getCode());
        result.setMemo(failure.getMessage());
        result.setBizType(bizType);
        return result;
    }

    public InstOrderResult fail(InstOrderProcessStatus failure, BizType bizType, String instOrderNo) {
        InstOrderResult result = new InstOrderResult();
        result.setProcessStatus(failure);
        result.setMemo(failure.getMessage());
        result.setBizType(bizType);
        result.setInstOrderNo(instOrderNo);
        return result;
    }

}
