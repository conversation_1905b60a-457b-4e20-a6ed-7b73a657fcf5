package com.uaepay.cmf.domainservice.batch.processor.impl;

import com.uaepay.cmf.common.core.domain.enums.*;
import com.uaepay.cmf.common.core.domain.exception.WrongOrderResultException;
import com.uaepay.cmf.common.core.domain.exception.WrongStateException;
import com.uaepay.cmf.common.core.domain.institution.InstOrder;
import com.uaepay.cmf.common.core.domain.institution.InstOrderResult;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchOrder;
import com.uaepay.cmf.common.core.domain.institution.batch.InstBatchResult;
import com.uaepay.cmf.common.core.domain.vo.InstSendResult;
import com.uaepay.cmf.common.core.util.biz.MapUtil;
import com.uaepay.cmf.common.domain.ChannelFundBatchResult;
import com.uaepay.cmf.common.domain.ChannelFundResult;
import com.uaepay.cmf.common.enums.ApiParamScene;
import com.uaepay.cmf.common.enums.MonitorItem;
import com.uaepay.cmf.common.monitor.MonitorLog;
import com.uaepay.cmf.domainservice.batch.convert.InstBatchResultConverter;
import com.uaepay.cmf.domainservice.batch.processor.BatchResultProcessor;
import com.uaepay.cmf.domainservice.batch.repository.InstBatchOrderRepository;
import com.uaepay.cmf.domainservice.batch.repository.InstBatchResultRepository;
import com.uaepay.cmf.domainservice.channel.holder.ChannelHolder;
import com.uaepay.cmf.domainservice.main.process.MonitorService;
import com.uaepay.cmf.domainservice.main.repository.InstOrderRepository;
import com.uaepay.cmf.domainservice.main.result.InstResultProcessor;
import com.uaepay.cmf.service.facade.result.CmfFundResultCode;
import com.uaepay.router.service.facade.domain.channel.ChannelApiParamVO;
import com.uaepay.router.service.facade.domain.channel.ChannelVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 批量结果处理流程实现.
 * </p>
 *
 * <AUTHOR> won
 * @version $Id: BatchResultProcessorImpl.java, v 0.1 2011-4-12 下午03:14:50 sean
 * won Exp $
 */
@Service
public class BatchResultProcessorImpl extends AbstractBatchProcessor implements
        BatchResultProcessor {

    @Resource
    private InstBatchOrderRepository instBatchOrderRepository;

    @Resource
    private InstOrderRepository instOrderRepository;

    @Resource
    private MonitorService monitorService;

    @Resource
    private InstResultProcessor instResultProcessor;

    @Resource
    private InstBatchResultRepository instBatchResultRepository;

    /**
     * 事务模板
     */
    @Resource(name = "cmfTransactionTemplate")
    private TransactionTemplate transactionTemplate;

    @Override
    public InstBatchResult process(InstBatchResult batchResult, List<InstSendResult> sendResult) {
        List<InstSendResult> instResult = sendResult;
        if (instResult == null) {
            // 重新装一次
            instResult = convert(batchResult);
            if (instResult == null || instResult.size() == 0) {
                // 无返回
                return batchResult;
            }
        }

        Iterator<InstSendResult> iterator = instResult.iterator();
        while (iterator.hasNext()) {
            InstSendResult instResultTemp = iterator.next();
            InstOrder instOrder = instOrderRepository.loadById(instResultTemp.getOrder()
                    .getInstOrderId(), false);
            try {
                // 临时设置状态用于校验
                if (InstOrderResultStatus.SUCCESSFUL.equals(instResultTemp.getOrder().getStatus())) {
                    instOrder.setStatus(InstOrderStatus.SUCCESSFUL);
                } else if (InstOrderResultStatus.FAILURE.equals(instResultTemp.getOrder()
                        .getStatus())) {
                    instOrder.setStatus(InstOrderStatus.FAILURE);
                }
                if (instOrderRepository.isCompleteSuccess(instOrder)) {
                    instResultTemp.setCompleteSuccess(true);
                } else {
                    instResultTemp.setCompleteSuccess(false);
                }
            } catch (WrongStateException e) {
                String message = "判断机构订单是否完全成功出错[" + instOrder.getInstOrderNo() + "]";
                logger.error(message, e);
                monitorService.logMonitorEvent(new MonitorLog(instOrder.getInstOrderNo(),
                        MonitorItem.STATUS_NOT_CONSISTENT_MULTI, message));
                iterator.remove();
            }
        }

        return batchResult;
    }

    @Override
    public InstBatchResult process(final InstBatchOrder batchOrder,
                                   final ChannelFundBatchResult channelBatchResult,
                                   final boolean isSync) {
        if (batchOrder == null || channelBatchResult == null) {
            return null;
        }

        // 更新extension
        saveExtension(batchOrder, channelBatchResult);

        final List<InstOrderResult> instOrderResultList = new ArrayList<>();
        // 1) 批量订单以及机构订单发送状态跃迁
        // 将UNKNOW_EXCEPTION当作发送失败来处理,其它情况当作发送成功
        final InstOrderArchiveStatus targetArchiveStatus = CmfFundResultCode.UNKNOW_EXCEPTION
                .getCode().equals(channelBatchResult.getResultCode()) ? InstOrderArchiveStatus.FAILURE
                : InstOrderArchiveStatus.SUBMMITED;
        //目标状态和批量订单状态一致 或 批量订单状态已经为最终状态(RECEIVED)时，则不修改
        if (targetArchiveStatus != batchOrder.getStatus() && batchOrder.getStatus() != InstOrderArchiveStatus.RECEIVED) {
            transactionTemplate.execute(status -> {

                // 修改BatchOrder状态
                int modCount = instBatchOrderRepository.updateStatusByIdWithPreStatus(
                        batchOrder.getArchiveBatchId(), targetArchiveStatus,
                        batchOrder.getStatus());

                int batchModCount = 0;
                // 修改机构订单状态（机构订单状态需与批量订单状态保持一致）
                if (CollectionUtils.isNotEmpty(batchOrder.getInstOrderList())) {
                    batchModCount = instOrderRepository.updateCommunicateStatusByInstOrders(batchOrder.getInstOrderList(),
                            convertStatus(targetArchiveStatus), convertStatus(batchOrder.getStatus()));
                } else {
                    batchModCount = instOrderRepository.updateCommunicateStatusByArchiveBatchId(batchOrder.getArchiveBatchId(),
                            convertStatus(targetArchiveStatus));
                }
                return null;
            });
        }

        // 2) 单笔订单状态处理
        if (channelBatchResult.getFundResultList() != null) {
            for (ChannelFundResult channelFundResult : channelBatchResult.getFundResultList()) {
                InstOrder instOrder = instOrderRepository.loadByNo(channelFundResult
                        .getInstOrderNo());
                if (instOrder != null) {
                    // 考虑拆单和向前通知的情况，订单结果处理走单笔结果处理模式
                    try {
                        InstOrderResult instOrderResult = instResultProcessor.process(instOrder,
                                channelFundResult);
                        instOrderResultList.add(instOrderResult);
                    }catch (WrongOrderResultException wre){
                        logger.warn("batchResult.process.wrongOrderStatus:{}",wre.getMessage());
                    }
                }
            }
        }

        InstBatchResult instBatchResult = null;
        // 3) 批量订单完成
        if (batchOrder.getStatus() != InstOrderArchiveStatus.RECEIVED
                && instOrderRepository.isBatchComplete(batchOrder)) {
            // 事务控制
            instBatchResult = transactionTemplate
                    .execute(status -> {
                        // 3.1 修改批次订单为最终状态  TODO:
                        int count = instBatchOrderRepository.updateStatusById(
                                batchOrder.getArchiveBatchId(), InstOrderArchiveStatus.RECEIVED);
                        // 3.2 插入批次订单结果 TODO:仅在订单完成时插入批量订单结果?
                        InstBatchResult tempBatchResult = InstBatchResultConverter.convert(
                                batchOrder, channelBatchResult, instOrderResultList);
                        instBatchResultRepository.insertInstBatchResult(tempBatchResult);
                        return tempBatchResult;
                    });
        }
        return instBatchResult;
    }

    private void saveExtension(InstBatchOrder order, ChannelFundBatchResult result) {

        ChannelVO channel = ChannelHolder.get();
        if (channel == null || channel.getChannelApi() == null || StringUtils.isEmpty(result.getExtension())) {
            return;
        }
        boolean needStore = false;

        Map<String,String> extMap = order.getExtension() == null ? new HashMap<>(10) : order.getExtension();

        if (StringUtils.isNotEmpty(result.getInstReturnOrderNo())) {
            extMap.put(ExtensionKey.INST_SEQ_NO.key,
                    result.getInstReturnOrderNo());
            needStore = true;
        }
        Map<String, String> resultMap = MapUtil.jsonToMap(result.getExtension());

        // 按照接口定义保存数据
        if (CollectionUtils.isNotEmpty(channel.getChannelApi().getParamList())) {
            for (ChannelApiParamVO apiParam : channel.getChannelApi().getParamList()) {
                if (ApiParamScene.CHANNEL_RETURN.getCode().equals(apiParam.getScene())
                        && StringUtils.isNotEmpty(resultMap.get(apiParam.getParamName()))) {
                    extMap.put(apiParam.getParamName(),
                            resultMap.get(apiParam.getParamName()));
                    needStore = true;
                }
            }
        }
        // 如果有需要保存,则更新扩展信息
        if (needStore) {
            order.setExtension(extMap);
            instOrderRepository.storeExtension(order);
        }
    }

    private static CommunicateStatus convertStatus(InstOrderArchiveStatus archiveStatus) {
        CommunicateStatus communicateStatus = null;
        switch (archiveStatus) {
            case AWAITING:
            case GENERATED:
                communicateStatus = CommunicateStatus.AWAITING;
                break;
            case IN_PROCESS:
                communicateStatus = CommunicateStatus.IN_PROCESS;
                break;
            case FAILURE:
                communicateStatus = CommunicateStatus.FAILURE;
                break;
            case SUBMMITED:
                communicateStatus = CommunicateStatus.SENT;
                break;
            case RECEIVED:
                communicateStatus = CommunicateStatus.RECEIVED;
                break;
        }
        return communicateStatus;
    }

}
