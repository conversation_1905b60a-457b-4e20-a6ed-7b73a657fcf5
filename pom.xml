<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.uaepay</groupId>
        <artifactId>uaepay-root</artifactId>
        <version>1.2.1</version>
    </parent>

    <groupId>com.uaepay.fund.cmf</groupId>
    <artifactId>cmf-parent</artifactId>
    <version>1.1.19-SNAPSHOT</version>

    <packaging>pom</packaging>

    <properties>
        <config.root>${config.prefix}/pay/fund/cmf</config.root>
        <packname>cmf</packname>
        <spring.boot.version>2.1.5.RELEASE</spring.boot.version>
        <spring.statemachine.version>2.2.0.RELEASE</spring.statemachine.version>
        <uaepay-spring-dependency.version>1.2.19</uaepay-spring-dependency.version>
        <basic-lang.version>1.3.0</basic-lang.version>
        <velocity.version>1.5</velocity.version>
        <velocity.tools.version>1.4</velocity.tools.version>

        <!-- Inner version -->
        <cobarclient.version>1.1.0</cobarclient.version>

        <uaepay.util.version>1.3.0</uaepay.util.version>
        <uaepay.mq.version>1.0.0</uaepay.mq.version>

        <uaepay.ues.version>2.3.4-SNAPSHOT</uaepay.ues.version>
        <uaepay.common.version>1.0.0</uaepay.common.version>
        <uaepay.payment.version>1.1.2-SNAPSHOT</uaepay.payment.version>
        <uaepay.mns.service.facade>1.0.4</uaepay.mns.service.facade>
        <csa.facade.compensate.version>1.0.0</csa.facade.compensate.version>
        <lombok.version>1.18.20</lombok.version>
        <validation.api.version>2.0.1.Final</validation.api.version>
        <member.facade.version>1.0.49-SNAPSHOT</member.facade.version>
        <beacon.version>1.2.10</beacon.version>
        <router.facade.version>1.1.3-SNAPSHOT</router.facade.version>
        <cards.facade.version>1.0.2-SNAPSHOT</cards.facade.version>
        <csc.version>1.0.0</csc.version>
        <facade-mocker.version>1.0.5-SNAPSHOT</facade-mocker.version>
        <escrow.facade.version>1.0.9-SNAPSHOT</escrow.facade.version>
        <grc.cps.version>1.0.3-SNAPSHOT</grc.cps.version>
        <grc-component-connect-api.version>1.0.15-SNAPSHOT</grc-component-connect-api.version>
        <redisson.version>3.16.6</redisson.version>



    </properties>

    <modules>
        <module>common</module>
        <module>core</module>
        <module>ext</module>
        <module>domainservie</module>
        <module>service</module>
        <module>web</module>
        <module>test</module>
        <module>task</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.uaepay</groupId>
                <artifactId>uaepay-spring-dependencies</artifactId>
                <version>${uaepay-spring-dependency.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.statemachine</groupId>
                <artifactId>spring-statemachine-core</artifactId>
                <version>${spring.statemachine.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>org.redisson</groupId>-->
<!--                <artifactId>redisson</artifactId>-->
<!--                <version>${redisson.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>1.8.6</version>
            </dependency>
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>2.7.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>16.0.1</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty</artifactId>
                <version>3.7.0.Final</version>
            </dependency>
            <dependency>
                <artifactId>commons-lang</artifactId>
                <groupId>commons-lang</groupId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.1</version>
            </dependency>
            <dependency>
                <groupId>velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <dependency>
                <groupId>velocity-tools</groupId>
                <artifactId>velocity-tools</artifactId>
                <version>${velocity.tools.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uaepay.common.basic</groupId>
                <artifactId>basic-util</artifactId>
                <version>${uaepay.util.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jibx-bind</artifactId>
                        <groupId>org.jibx</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.uaepay.common.basic</groupId>
                <artifactId>basic-lang</artifactId>
                <version>${basic-lang.version}</version>
            </dependency>

            <!-- cache -->
            <dependency>
                <groupId>net.sf.smc</groupId>
                <artifactId>smc</artifactId>
                <version>6.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.uaepay.public.csc</groupId>
                <artifactId>csc-compensation-facade</artifactId>
                <version>${csc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.member</groupId>
                <artifactId>member-facade</artifactId>
                <version>${member.facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.escrow</groupId>
                <artifactId>escrow-service-facade</artifactId>
                <version>${escrow.facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.grc</groupId>
                <artifactId>grc-cps-api</artifactId>
                <version>${grc.cps.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uaepay.grc</groupId>
                <artifactId>grc-component-connect-api</artifactId>
                <version>${grc-component-connect-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uaepay.public.payment</groupId>
                <artifactId>payment-common-domain</artifactId>
                <version>${uaepay.payment.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uaepay.public.mns</groupId>
                <artifactId>mns-service-facade</artifactId>
                <version>${uaepay.mns.service.facade}</version>
            </dependency>
            <!-- ues -->
            <dependency>
                <groupId>com.uaepay.basis.ues</groupId>
                <artifactId>ues-client</artifactId>
                <version>${uaepay.ues.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uaepay.public.router</groupId>
                <artifactId>router-service-facade</artifactId>
                <version>${router.facade.version}</version>
            </dependency>

            <!-- SELF -->
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-core-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-core-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-core-util</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-core-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-common-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-domainservice-main</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-domainservice-batch</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-ext-task</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-service-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-ext-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-ext-integration</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-ext-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uaepay.fund.cmf</groupId>
                <artifactId>cmf-web</artifactId>
                <version>${project.version}</version>
            </dependency>


            <!--SELF-->
            <dependency>
                <groupId>com.uaepay.cmf.service</groupId>
                <artifactId>cmf-service-facade</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uaepay.public.csa</groupId>
                <artifactId>csa-facade-compensate</artifactId>
                <version>${csa.facade.compensate.version}</version>
            </dependency>

            <dependency>
                <artifactId>cards-service-facade</artifactId>
                <groupId>com.uaepay.channel.cards</groupId>
                <version>${cards.facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uaepay.basis.beacon</groupId>
                <artifactId>beacon-service-facade</artifactId>
                <version>${beacon.version}</version>
            </dependency>

            <dependency>
                <groupId>com.uaepay.basis.beacon</groupId>
                <artifactId>beacon-common</artifactId>
                <version>${beacon.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.uaepay.unittest</groupId>-->
<!--                <artifactId>facade-mocker</artifactId>-->
<!--                <version>${facade-mocker.version}</version>-->
<!--                <scope>test</scope>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.uaepay.basic.cobarclient</groupId>
                <artifactId>cobarclient</artifactId>
                <version>${cobarclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis-spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-orm</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>http://nexus.test2pay.com/repository/maven-releases</url>
            <uniqueVersion>true</uniqueVersion>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://nexus.test2pay.com/repository/maven-snapshots</url>
            <uniqueVersion>true</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <plugins>
            <!-- try:
            mvn versions:set -DnewVersion=1.0.29-SNAPSHOT
            -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
